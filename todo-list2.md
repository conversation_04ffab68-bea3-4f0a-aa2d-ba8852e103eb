
# mongodb 的 device_status_history 集合
## 添加索引
- device_id
- date
- status_history.timestamp

# 验证 定时任务 和 手动 rebuild 保存 device_status_history 记录
- 是不是以 utc时间的 00:00:00 - 23:59:59 来筛选和保存同一天的数据


curl -X POST http://localhost:9005/api/admin/rebuild-history \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2025-06-14",
    "force": true
  }'

curl -X POST http://localhost:9005/api/admin/rebuild-history \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2025-06-15",
    "force": true
  }'



**
产量:

设备产量详细记录:
序号, 状态, 开始时间, 结束时间, 时长, 加工程序, 计数器(qu), 产量累计, 班别, 班别产量累计


产量累计规则:
产量累计是 计数器跟加工程序相结合进行计算

1. 第一条数据, 产量累计从1开始
2. 计数器与上一次 production 的计数器 加1, 加工程序相同, 产量累计加 1
3. 计数器与上一次 production 的计数器 加1, 加工程序不同, 产量累计从1开始
4. 计数器与上一次 production 的计数器 不是加1, 加工程序相同, 产量累计加1
5. 计数器与上一次 production 的计数器 不是加1, 加工程序不同, 产量累计从1开始
