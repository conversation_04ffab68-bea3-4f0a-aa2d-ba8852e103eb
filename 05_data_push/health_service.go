/**
 * 健康检查服务模块
 *
 * 功能概述：
 * 本模块提供完整的健康检查和监控服务，通过HTTP接口暴露服务状态、统计信息、
 * 系统指标和告警信息，为运维监控、负载均衡和自动化部署提供支持
 *
 * 主要功能：
 * - 健康检查：提供基础和详细的健康检查接口
 * - 统计监控：收集和展示各组件的运行统计信息
 * - 系统指标：监控内存、CPU、goroutine等系统资源
 * - 告警管理：基于阈值的自动告警生成和管理
 * - 运维接口：提供脚本重载、数据清理等运维操作
 * - Prometheus集成：提供Prometheus格式的指标输出
 * - 配置查看：展示当前服务的配置摘要
 *
 * API接口：
 * - GET /health：基础健康检查，返回服务整体状态
 * - GET /health/detailed：详细健康检查，包含所有组件信息
 * - GET /stats：统计信息，包含各组件的运行统计
 * - GET /stats/processors：处理器统计，专门的处理器性能数据
 * - POST /admin/reload-script：重新加载JavaScript清洗脚本
 * - POST /admin/flush-device：清空指定设备的数据队列
 * - GET /metrics：Prometheus格式的指标输出
 *
 * 监控维度：
 * - 组件健康：Redis、SQLite、NATS、InfluxDB、JavaScript引擎
 * - 系统资源：内存使用、goroutine数量、GC统计
 * - 业务指标：处理速率、错误率、队列大小
 * - 连接状态：各种客户端的连接状态和统计
 *
 * 技术特性：
 * - RESTful API：标准的REST接口设计
 * - JSON响应：统一的JSON响应格式
 * - 超时控制：所有检查操作都有超时保护
 * - 并发安全：支持并发的健康检查请求
 * - 实时监控：实时收集和展示最新的状态信息
 *
 * 集成支持：
 * - 负载均衡器：支持负载均衡器的健康检查
 * - Kubernetes：支持K8s的liveness和readiness探针
 * - Prometheus：支持Prometheus监控系统集成
 * - 告警系统：支持基于阈值的自动告警
 *
 * 业务价值：
 * - 运维可视化：提供直观的服务状态和性能指标
 * - 故障诊断：快速定位和诊断系统问题
 * - 性能优化：提供性能分析和优化的数据支持
 * - 自动化运维：支持自动化的监控和告警
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"time"
)

/**
 * 健康检查服务结构体
 *
 * 功能：提供完整的健康检查和监控服务，集成所有组件的状态监控
 *
 * 架构设计：
 * - 中央监控：统一监控所有组件的健康状态
 * - 依赖注入：通过构造函数注入所有需要监控的组件
 * - 状态聚合：收集和聚合各组件的状态信息
 * - HTTP服务：通过HTTP接口暴露监控数据
 *
 * 核心职责：
 * - 健康检查：检查各组件的健康状态
 * - 统计收集：收集各组件的运行统计
 * - 指标监控：监控系统资源和业务指标
 * - 告警管理：基于阈值生成告警信息
 * - 运维支持：提供运维操作接口
 *
 * @struct HealthService
 */
type HealthService struct {
	/** 全局配置对象，包含监控阈值和告警配置 */
	config *Config

	/** Redis客户端，用于检查Redis连接状态 */
	redis *RedisClient

	/** SQLite存储，用于检查本地存储状态 */
	sqlite *SQLiteStorage

	/** 数据推送器，用于检查推送服务状态 */
	dataPusher DataPusher

	/** JavaScript引擎，用于检查脚本执行状态 */
	jsEngine *JSEngine

	/** 设备处理器管理器，用于检查处理器状态 */
	processorManager *DeviceProcessorManager

	/** 设备数据日志器，用于获取日志统计信息 */
	deviceLogger *DeviceLogger

	/** 设备离线监控器，用于获取监控统计信息 */
	deviceMonitor *DeviceMonitor

	/** 服务启动时间，用于计算运行时长 */
	startTime time.Time
}

// NewHealthService 创建健康检查服务
func NewHealthService(config *Config, redis *RedisClient, sqlite *SQLiteStorage, dataPusher DataPusher, jsEngine *JSEngine, processorManager *DeviceProcessorManager, deviceLogger *DeviceLogger, deviceMonitor *DeviceMonitor) *HealthService {
	return &HealthService{
		config:           config,
		redis:            redis,
		sqlite:           sqlite,
		dataPusher:       dataPusher,
		jsEngine:         jsEngine,
		processorManager: processorManager,
		deviceLogger:     deviceLogger,
		deviceMonitor:    deviceMonitor,
		startTime:        time.Now(),
	}
}

// HealthCheckHandler 基础健康检查处理器
func (hs *HealthService) HealthCheckHandler(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
	defer cancel()

	status := "healthy"
	components := make(map[string]ComponentHealth)

	// 检查Redis
	redisHealth := hs.checkRedisHealth(ctx)
	components["redis"] = redisHealth
	if redisHealth.Status != "healthy" {
		status = "degraded"
	}

	// 检查数据推送器
	pusherHealth := hs.checkPusherHealth(ctx)
	components["data_pusher"] = pusherHealth
	if pusherHealth.Status != "healthy" {
		status = "degraded"
	}

	// 检查JavaScript引擎
	jsHealth := hs.checkJSEngineHealth()
	components["javascript_engine"] = jsHealth
	if jsHealth.Status != "healthy" {
		status = "degraded"
	}

	// 检查SQLite
	sqliteHealth := hs.checkSQLiteHealth()
	components["sqlite"] = sqliteHealth
	if sqliteHealth.Status != "healthy" {
		status = "degraded"
	}

	// 获取系统指标
	metrics := hs.getSystemMetrics()

	// 构建健康状态响应
	healthStatus := HealthStatus{
		Service:    ServiceName,
		Status:     status,
		Timestamp:  time.Now(),
		Version:    ServiceVersion,
		Uptime:     time.Since(hs.startTime),
		Components: components,
		Metrics:    metrics,
		Alerts:     hs.getAlerts(components, metrics),
	}

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	if status != "healthy" {
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	// 返回JSON响应
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": status,
		"data":   healthStatus,
	})
}

// DetailedHealthHandler 详细健康检查处理器
func (hs *HealthService) DetailedHealthHandler(w http.ResponseWriter, r *http.Request) {

	// 获取所有组件的详细信息
	details := map[string]interface{}{
		"service": map[string]interface{}{
			"name":       ServiceName,
			"version":    ServiceVersion,
			"start_time": hs.startTime,
			"uptime":     time.Since(hs.startTime),
			"config":     hs.getConfigSummary(),
		},
		"redis":             hs.redis.GetStats(),
		"sqlite":            hs.sqlite.GetStats(),
		"data_pusher":       hs.dataPusher.GetStats(),
		"javascript_engine": hs.jsEngine.GetStats(),
		"processors":        hs.processorManager.GetStats(),
		"system":            hs.getSystemMetrics(),
	}

	// 添加设备日志器统计信息
	if hs.deviceLogger != nil {
		details["device_logger"] = hs.deviceLogger.GetStats()
	}

	// 添加设备监控器统计信息
	if hs.deviceMonitor != nil {
		details["device_monitor"] = hs.deviceMonitor.GetStats()
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "success",
		"data":   details,
	})
}

// StatsHandler 统计信息处理器
func (hs *HealthService) StatsHandler(w http.ResponseWriter, r *http.Request) {
	stats := map[string]interface{}{
		"processors":  hs.processorManager.GetStats(),
		"redis":       hs.redis.GetStats(),
		"sqlite":      hs.sqlite.GetStats(),
		"data_pusher": hs.dataPusher.GetStats(),
		"js_engine":   hs.jsEngine.GetStats(),
		"system":      hs.getSystemMetrics(),
	}

	// 添加设备日志器统计信息
	if hs.deviceLogger != nil {
		stats["device_logger"] = hs.deviceLogger.GetStats()
		fmt.Println("✅ Device logger stats added")
	} else {
		fmt.Println("❌ Device logger is nil")
	}

	// 添加设备监控器统计信息
	if hs.deviceMonitor != nil {
		stats["device_monitor"] = hs.deviceMonitor.GetStats()
		fmt.Println("✅ Device monitor stats added")
	} else {
		fmt.Println("❌ Device monitor is nil")
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "success",
		"data":   stats,
	})
}

// ProcessorStatsHandler 处理器统计处理器
func (hs *HealthService) ProcessorStatsHandler(w http.ResponseWriter, r *http.Request) {
	stats := hs.processorManager.GetStats()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "success",
		"data":   stats,
	})
}

// ReloadScriptHandler 重新加载脚本处理器
func (hs *HealthService) ReloadScriptHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	if err := hs.jsEngine.ReloadScript(); err != nil {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		})
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":  "success",
		"message": "Script reloaded successfully",
	})
}

// FlushDeviceHandler 清空设备数据处理器
func (hs *HealthService) FlushDeviceHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	deviceID := r.URL.Query().Get("device_id")
	if deviceID == "" {
		http.Error(w, "device_id parameter is required", http.StatusBadRequest)
		return
	}

	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	if err := hs.redis.FlushDeviceData(ctx, deviceID); err != nil {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		})
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":  "success",
		"message": fmt.Sprintf("Device %s data flushed successfully", deviceID),
	})
}

// MetricsHandler Prometheus格式指标处理器
func (hs *HealthService) MetricsHandler(w http.ResponseWriter, r *http.Request) {
	metrics := hs.getSystemMetrics()
	processorStats := hs.processorManager.GetStats()
	redisStats := hs.redis.GetStats()
	pusherStats := hs.dataPusher.GetStats()

	w.Header().Set("Content-Type", "text/plain")

	// 输出Prometheus格式的指标
	fmt.Fprintf(w, "# HELP data_push_uptime_seconds Service uptime in seconds\n")
	fmt.Fprintf(w, "# TYPE data_push_uptime_seconds counter\n")
	fmt.Fprintf(w, "data_push_uptime_seconds %.0f\n", time.Since(hs.startTime).Seconds())

	fmt.Fprintf(w, "# HELP data_push_memory_usage_bytes Memory usage in bytes\n")
	fmt.Fprintf(w, "# TYPE data_push_memory_usage_bytes gauge\n")
	fmt.Fprintf(w, "data_push_memory_usage_bytes %.0f\n", metrics.MemoryUsageMB*1024*1024)

	fmt.Fprintf(w, "# HELP data_push_goroutines Number of goroutines\n")
	fmt.Fprintf(w, "# TYPE data_push_goroutines gauge\n")
	fmt.Fprintf(w, "data_push_goroutines %d\n", metrics.GoroutineCount)

	fmt.Fprintf(w, "# HELP data_push_processors_active Number of active processors\n")
	fmt.Fprintf(w, "# TYPE data_push_processors_active gauge\n")
	fmt.Fprintf(w, "data_push_processors_active %d\n", processorStats.ActiveProcessors)

	fmt.Fprintf(w, "# HELP data_push_processed_total Total processed messages\n")
	fmt.Fprintf(w, "# TYPE data_push_processed_total counter\n")
	fmt.Fprintf(w, "data_push_processed_total %d\n", processorStats.TotalProcessed)

	fmt.Fprintf(w, "# HELP data_push_errors_total Total errors\n")
	fmt.Fprintf(w, "# TYPE data_push_errors_total counter\n")
	fmt.Fprintf(w, "data_push_errors_total %d\n", processorStats.TotalErrors)

	fmt.Fprintf(w, "# HELP data_push_redis_operations_total Total Redis operations\n")
	fmt.Fprintf(w, "# TYPE data_push_redis_operations_total counter\n")
	fmt.Fprintf(w, "data_push_redis_operations_total %d\n", redisStats.TotalReads+redisStats.TotalWrites+redisStats.TotalDeletes)

	fmt.Fprintf(w, "# HELP data_push_pusher_published_total Total messages published\n")
	fmt.Fprintf(w, "# TYPE data_push_pusher_published_total counter\n")
	fmt.Fprintf(w, "data_push_pusher_published_total %d\n", pusherStats.GetTotalPushed())
}

// checkRedisHealth 检查Redis健康状态
func (hs *HealthService) checkRedisHealth(ctx context.Context) ComponentHealth {
	start := time.Now()
	err := hs.redis.HealthCheck(ctx)
	responseTime := float64(time.Since(start).Milliseconds())

	if err != nil {
		return ComponentHealth{
			Status:       "unhealthy",
			LastCheck:    time.Now(),
			ErrorCount:   1,
			LastError:    err.Error(),
			ResponseTime: responseTime,
		}
	}

	return ComponentHealth{
		Status:       "healthy",
		LastCheck:    time.Now(),
		ErrorCount:   0,
		LastError:    "",
		ResponseTime: responseTime,
	}
}

// checkPusherHealth 检查数据推送器健康状态
func (hs *HealthService) checkPusherHealth(ctx context.Context) ComponentHealth {
	start := time.Now()
	err := hs.dataPusher.HealthCheck(ctx)
	responseTime := float64(time.Since(start).Milliseconds())

	if err != nil {
		return ComponentHealth{
			Status:       "unhealthy",
			LastCheck:    time.Now(),
			ErrorCount:   1,
			LastError:    err.Error(),
			ResponseTime: responseTime,
		}
	}

	return ComponentHealth{
		Status:       "healthy",
		LastCheck:    time.Now(),
		ErrorCount:   0,
		LastError:    "",
		ResponseTime: responseTime,
	}
}

// checkJSEngineHealth 检查JavaScript引擎健康状态
func (hs *HealthService) checkJSEngineHealth() ComponentHealth {
	start := time.Now()
	err := hs.jsEngine.ValidateScript()
	responseTime := float64(time.Since(start).Milliseconds())

	if err != nil {
		return ComponentHealth{
			Status:       "unhealthy",
			LastCheck:    time.Now(),
			ErrorCount:   1,
			LastError:    err.Error(),
			ResponseTime: responseTime,
		}
	}

	return ComponentHealth{
		Status:       "healthy",
		LastCheck:    time.Now(),
		ErrorCount:   0,
		LastError:    "",
		ResponseTime: responseTime,
	}
}

// checkSQLiteHealth 检查SQLite健康状态
func (hs *HealthService) checkSQLiteHealth() ComponentHealth {
	stats := hs.sqlite.GetStats()

	status := "healthy"
	if stats.ErrorCount > 0 {
		status = "degraded"
	}

	return ComponentHealth{
		Status:       status,
		LastCheck:    time.Now(),
		ErrorCount:   int(stats.ErrorCount),
		LastError:    stats.LastError,
		ResponseTime: float64(stats.AverageLatency.Milliseconds()),
	}
}

// getSystemMetrics 获取系统指标
func (hs *HealthService) getSystemMetrics() SystemMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return SystemMetrics{
		Timestamp:         time.Now(),
		MemoryUsageMB:     float64(m.Alloc) / 1024 / 1024,
		GoroutineCount:    runtime.NumGoroutine(),
		GCCount:           m.NumGC,
		RedisConnections:  hs.redis.GetStats().ConnectionCount,
		SQLiteConnections: hs.sqlite.GetStats().ActiveConnections,
		NATSConnections:   1, // NATS通常是单连接
		ActiveWorkers:     hs.processorManager.GetStats().ActiveProcessors,
		QueuedItems:       hs.getTotalQueueSize(),
		ProcessingRate:    hs.getProcessingRate(),
	}
}

// getTotalQueueSize 获取总队列大小
func (hs *HealthService) getTotalQueueSize() int {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if size, err := hs.redis.GetTotalQueueSize(ctx); err == nil {
		return int(size)
	}
	return 0
}

// getProcessingRate 获取处理速率
func (hs *HealthService) getProcessingRate() float64 {
	stats := hs.processorManager.GetStats()
	if stats.TotalProcessed == 0 {
		return 0
	}

	uptime := time.Since(hs.startTime).Seconds()
	if uptime == 0 {
		return 0
	}

	return float64(stats.TotalProcessed) / uptime
}

// getAlerts 获取告警信息
func (hs *HealthService) getAlerts(components map[string]ComponentHealth, metrics SystemMetrics) []Alert {
	var alerts []Alert

	// 检查组件健康状态
	for name, health := range components {
		if health.Status != "healthy" {
			alerts = append(alerts, Alert{
				Level:     "error",
				Message:   fmt.Sprintf("Component %s is %s: %s", name, health.Status, health.LastError),
				Component: name,
				Timestamp: time.Now(),
				Resolved:  false,
			})
		}
	}

	// 检查内存使用率
	if metrics.MemoryUsageMB > float64(hs.config.Monitoring.AlertThresholds.MemoryUsagePercent) {
		alerts = append(alerts, Alert{
			Level:     "warning",
			Message:   fmt.Sprintf("High memory usage: %.2f MB", metrics.MemoryUsageMB),
			Component: "system",
			Timestamp: time.Now(),
			Resolved:  false,
		})
	}

	// 检查队列大小
	if metrics.QueuedItems > hs.config.Monitoring.AlertThresholds.RedisQueueSize {
		alerts = append(alerts, Alert{
			Level:     "warning",
			Message:   fmt.Sprintf("High queue size: %d items", metrics.QueuedItems),
			Component: "redis",
			Timestamp: time.Now(),
			Resolved:  false,
		})
	}

	return alerts
}

// getConfigSummary 获取配置摘要
func (hs *HealthService) getConfigSummary() map[string]interface{} {
	return map[string]interface{}{
		"service_name":    hs.config.Service.Name,
		"service_port":    hs.config.Service.Port,
		"max_workers":     hs.config.Service.MaxWorkers,
		"batch_size":      hs.config.Service.BatchSize,
		"redis_host":      hs.config.Redis.Host,
		"redis_port":      hs.config.Redis.Port,
		"nats_url":        hs.config.NATS.URL,
		"sqlite_data_dir": hs.config.SQLite.DataDir,
		"script_file":     hs.config.DataCleaning.ScriptFile,
	}
}
