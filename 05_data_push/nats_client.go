/**
 * NATS客户端模块
 *
 * 功能概述：
 * 本模块提供NATS JetStream客户端功能，负责将处理后的设备数据推送到NATS消息队列，
 * 支持单条和批量发布、异步发布、流管理和完善的错误处理机制
 *
 * 主要功能：
 * - 数据发布：将处理后的设备数据发布到NATS JetStream
 * - 批量操作：支持批量发布，提高吞吐量
 * - 异步发布：使用异步发布模式，提高性能
 * - 流管理：自动创建和管理JetStream流
 * - 连接管理：自动重连和连接状态监控
 * - 健康检查：定期检查NATS连接和JetStream状态
 * - 统计监控：详细的发布统计和性能指标
 *
 * 技术特性：
 * - JetStream：使用NATS JetStream确保消息持久化
 * - 异步发布：非阻塞的异步发布模式
 * - 自动重连：连接断开时自动重连
 * - 流持久化：消息持久化存储，确保不丢失
 * - 批量处理：支持批量发布，提高效率
 * - 错误恢复：完善的错误处理和重试机制
 *
 * 消息格式：
 * - 标准化：统一的消息格式和元数据
 * - 可追踪：包含消息ID、设备ID、序列号等追踪信息
 * - 时间戳：包含原始时间戳和处理时间戳
 * - 数据源：标识数据来源（redis、sqlite等）
 *
 * 性能优化：
 * - 异步发布：使用异步模式提高发布性能
 * - 批量操作：减少网络往返次数
 * - 连接复用：复用NATS连接减少开销
 * - 流配置：优化的流配置参数
 *
 * 可靠性保证：
 * - 消息持久化：JetStream确保消息不丢失
 * - 自动重连：网络故障时自动重连
 * - 错误处理：完善的错误处理和统计
 * - 健康检查：定期检查连接和流状态
 *
 * 业务价值：
 * - 数据传输：可靠的数据传输到下游系统
 * - 高性能：异步批量发布的高性能
 * - 可观测性：详细的统计信息和监控
 * - 系统集成：与下游数据处理系统的集成
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/nats-io/nats.go"
)

/**
 * NATS客户端结构体
 *
 * 功能：封装NATS JetStream连接和操作，提供数据发布和流管理功能
 *
 * 架构设计：
 * - 连接管理：封装NATS连接和JetStream上下文
 * - 统计监控：实时统计发布性能和错误信息
 * - 并发安全：使用读写锁保护统计数据
 * - 配置驱动：基于配置文件的连接参数
 *
 * @struct NATSClient
 */
type NATSClient struct {
	/** NATS连接实例，提供底层NATS操作 */
	conn *nats.Conn

	/** JetStream上下文，提供流操作和消息发布 */
	js nats.JetStreamContext

	/** NATS配置信息，包含连接参数和流配置 */
	config NATSConfig

	/** NATS操作统计信息，用于性能监控 */
	stats NATSStats

	/** 读写锁，保护统计数据的并发访问 */
	mutex sync.RWMutex
}

// NATSStats NATS统计信息
type NATSStats struct {
	TotalPublished   int64         `json:"total_published"`   // 总发布次数
	SuccessCount     int64         `json:"success_count"`     // 成功次数
	ErrorCount       int64         `json:"error_count"`       // 错误次数
	LastError        string        `json:"last_error"`        // 最后错误
	AverageLatency   time.Duration `json:"average_latency"`   // 平均延迟
	LastPublishTime  time.Time     `json:"last_publish_time"` // 最后发布时间
	ConnectionStatus string        `json:"connection_status"` // 连接状态
	PendingMessages  int           `json:"pending_messages"`  // 待确认消息数
	StreamInfo       *StreamInfo   `json:"stream_info"`       // 流信息
}

// StreamInfo 流信息
type StreamInfo struct {
	Name      string   `json:"name"`      // 流名称
	Subjects  []string `json:"subjects"`  // 主题列表
	Messages  uint64   `json:"messages"`  // 消息数量
	Bytes     uint64   `json:"bytes"`     // 字节数
	Consumers int      `json:"consumers"` // 消费者数量
}

// NewNATSClient 创建新的NATS客户端
func NewNATSClient(config NATSConfig) (*NATSClient, error) {
	// 连接到NATS服务器
	conn, err := nats.Connect(config.URL,
		nats.Timeout(config.Timeout),
		nats.MaxReconnects(-1), // 无限重连
		nats.ReconnectWait(time.Second),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			fmt.Printf("NATS disconnected: %v\n", err)
		}),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			fmt.Printf("NATS reconnected to %v\n", nc.ConnectedUrl())
		}),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to NATS: %w", err)
	}

	// 创建JetStream上下文
	js, err := conn.JetStream(
		nats.PublishAsyncMaxPending(config.MaxPending),
	)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("failed to create JetStream context: %w", err)
	}

	client := &NATSClient{
		conn:   conn,
		js:     js,
		config: config,
		stats: NATSStats{
			ConnectionStatus: "connected",
		},
	}

	// 确保流存在
	if err := client.ensureStream(); err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to ensure stream: %w", err)
	}

	return client, nil
}

// ensureStream 确保流存在
func (n *NATSClient) ensureStream() error {
	// 检查流是否存在
	_, err := n.js.StreamInfo(n.config.Stream)
	if err == nil {
		return nil // 流已存在
	}

	// 创建流
	streamConfig := &nats.StreamConfig{
		Name:      n.config.Stream,
		Subjects:  []string{n.config.Subject},
		Storage:   nats.FileStorage,
		Retention: nats.WorkQueuePolicy,
		MaxAge:    24 * time.Hour,     // 消息保留24小时
		MaxMsgs:   1000000,            // 最大消息数
		MaxBytes:  1024 * 1024 * 1024, // 最大1GB
		Replicas:  1,
	}

	_, err = n.js.AddStream(streamConfig)
	if err != nil {
		return fmt.Errorf("failed to create stream: %w", err)
	}

	fmt.Printf("Created NATS stream: %s\n", n.config.Stream)
	return nil
}

// PublishData 发布单条数据
func (n *NATSClient) PublishData(ctx context.Context, data *ProcessedData) error {
	startTime := time.Now()
	defer n.updateStats(startTime, nil)

	// 创建NATS消息
	message := &NATSMessage{
		MessageID:   fmt.Sprintf("%s_%s_%d", data.DeviceID, data.Sequence, time.Now().UnixNano()),
		DeviceID:    data.DeviceID,
		DataType:    data.DataType,
		Timestamp:   data.Timestamp,
		ProcessedAt: data.ProcessedAt,
		Data:        *data,
		Sequence:    data.Sequence,
		Source:      "redis",
	}

	// 序列化消息
	messageJSON, err := message.ToJSON()
	if err != nil {
		n.updateStats(startTime, err)
		return fmt.Errorf("failed to serialize message: %w", err)
	}

	// 发布消息
	_, err = n.js.PublishAsync(n.config.Subject, []byte(messageJSON))
	if err != nil {
		n.updateStats(startTime, err)
		return fmt.Errorf("failed to publish message: %w", err)
	}

	n.stats.TotalPublished++
	n.stats.SuccessCount++
	return nil
}

// BatchPublishData 批量发布数据
func (n *NATSClient) BatchPublishData(ctx context.Context, dataList []*ProcessedData, source string) error {
	if len(dataList) == 0 {
		return nil
	}

	startTime := time.Now()
	defer n.updateStats(startTime, nil)

	var publishErrors []error

	// 批量发布消息
	for _, data := range dataList {
		message := &NATSMessage{
			MessageID:   fmt.Sprintf("%s_%s_%d", data.DeviceID, data.Sequence, time.Now().UnixNano()),
			DeviceID:    data.DeviceID,
			DataType:    data.DataType,
			Timestamp:   data.Timestamp,
			ProcessedAt: data.ProcessedAt,
			Data:        *data,
			Sequence:    data.Sequence,
			Source:      source,
		}

		messageJSON, err := message.ToJSON()
		if err != nil {
			publishErrors = append(publishErrors, fmt.Errorf("failed to serialize message for device %s: %w", data.DeviceID, err))
			continue
		}

		_, err = n.js.PublishAsync(n.config.Subject, []byte(messageJSON))
		if err != nil {
			publishErrors = append(publishErrors, fmt.Errorf("failed to publish message for device %s: %w", data.DeviceID, err))
			continue
		}

		n.stats.TotalPublished++
	}

	// 等待所有异步发布完成
	select {
	case <-n.js.PublishAsyncComplete():
		n.stats.SuccessCount += int64(len(dataList) - len(publishErrors))
	case <-time.After(n.config.Timeout):
		publishErrors = append(publishErrors, fmt.Errorf("publish timeout"))
	}

	if len(publishErrors) > 0 {
		n.stats.ErrorCount += int64(len(publishErrors))
		// 返回第一个错误
		n.updateStats(startTime, publishErrors[0])
		return publishErrors[0]
	}

	return nil
}

// PublishFromSQLite 从SQLite记录发布数据
func (n *NATSClient) PublishFromSQLite(ctx context.Context, records []*SQLiteRecord) error {
	if len(records) == 0 {
		return nil
	}

	var dataList []*ProcessedData
	for _, record := range records {
		// 解析处理后的数据
		var processedData ProcessedData
		if record.ProcessedData != "" {
			if err := processedData.FromJSON(record.ProcessedData); err != nil {
				continue // 跳过解析失败的记录
			}
		} else {
			// 如果没有处理后的数据，使用原始数据
			var deviceData DeviceData
			if err := deviceData.FromJSON(record.RawData); err != nil {
				continue
			}

			processedData = ProcessedData{
				DeviceID:      deviceData.DeviceID,
				DataType:      deviceData.DataType,
				Timestamp:     deviceData.Timestamp,
				ProcessedAt:   time.Now(),
				RawData:       deviceData.RawData,
				ProcessedData: deviceData.RawData, // 使用原始数据作为处理后数据
				Sequence:      record.Sequence,
				Location:      deviceData.Location,
			}
		}

		dataList = append(dataList, &processedData)
	}

	return n.BatchPublishData(ctx, dataList, "sqlite")
}

// HealthCheck 健康检查
func (n *NATSClient) HealthCheck(ctx context.Context) error {
	startTime := time.Now()
	defer n.updateStats(startTime, nil)

	// 检查连接状态
	if !n.conn.IsConnected() {
		err := fmt.Errorf("NATS connection is not active")
		n.updateStats(startTime, err)
		n.stats.ConnectionStatus = "disconnected"
		return err
	}

	// 检查JetStream状态
	_, err := n.js.AccountInfo()
	if err != nil {
		n.updateStats(startTime, err)
		n.stats.ConnectionStatus = "jetstream_error"
		return fmt.Errorf("JetStream health check failed: %w", err)
	}

	n.stats.ConnectionStatus = "connected"
	return nil
}

// GetStreamInfo 获取流信息
func (n *NATSClient) GetStreamInfo() (*StreamInfo, error) {
	info, err := n.js.StreamInfo(n.config.Stream)
	if err != nil {
		return nil, fmt.Errorf("failed to get stream info: %w", err)
	}

	streamInfo := &StreamInfo{
		Name:      info.Config.Name,
		Subjects:  info.Config.Subjects,
		Messages:  info.State.Msgs,
		Bytes:     info.State.Bytes,
		Consumers: info.State.Consumers,
	}

	n.mutex.Lock()
	n.stats.StreamInfo = streamInfo
	n.mutex.Unlock()

	return streamInfo, nil
}

// GetPendingCount 获取待确认消息数
func (n *NATSClient) GetPendingCount() int {
	pending := n.js.PublishAsyncPending()

	n.mutex.Lock()
	n.stats.PendingMessages = pending
	n.mutex.Unlock()

	return pending
}

// WaitForPublishComplete 等待所有发布完成
func (n *NATSClient) WaitForPublishComplete(timeout time.Duration) error {
	select {
	case <-n.js.PublishAsyncComplete():
		return nil
	case <-time.After(timeout):
		return fmt.Errorf("timeout waiting for publish complete")
	}
}

// GetStats 获取统计信息
func (n *NATSClient) GetStats() NATSStats {
	n.mutex.RLock()
	defer n.mutex.RUnlock()

	// 更新待确认消息数
	n.stats.PendingMessages = n.js.PublishAsyncPending()

	return n.stats
}

// updateStats 更新统计信息
func (n *NATSClient) updateStats(startTime time.Time, err error) {
	n.mutex.Lock()
	defer n.mutex.Unlock()

	latency := time.Since(startTime)
	n.stats.LastPublishTime = time.Now()

	// 更新平均延迟
	if n.stats.AverageLatency == 0 {
		n.stats.AverageLatency = latency
	} else {
		n.stats.AverageLatency = (n.stats.AverageLatency + latency) / 2
	}

	// 更新错误信息
	if err != nil {
		n.stats.ErrorCount++
		n.stats.LastError = err.Error()
	}
}

// Close 关闭NATS客户端
func (n *NATSClient) Close() error {
	// 等待所有异步发布完成
	select {
	case <-n.js.PublishAsyncComplete():
	case <-time.After(5 * time.Second):
		fmt.Println("Warning: Timeout waiting for publish complete during close")
	}

	if n.conn != nil {
		n.conn.Close()
	}

	n.mutex.Lock()
	n.stats.ConnectionStatus = "closed"
	n.mutex.Unlock()

	return nil
}

// GetConnectionInfo 获取连接信息
func (n *NATSClient) GetConnectionInfo() map[string]interface{} {
	if !n.conn.IsConnected() {
		return map[string]interface{}{
			"connected": false,
			"status":    "disconnected",
		}
	}

	stats := n.conn.Stats()

	return map[string]interface{}{
		"connected":        true,
		"status":           "connected",
		"server_url":       n.conn.ConnectedUrl(),
		"server_name":      n.conn.ConnectedServerName(),
		"in_msgs":          int(stats.InMsgs),
		"out_msgs":         int(stats.OutMsgs),
		"in_bytes":         int(stats.InBytes),
		"out_bytes":        int(stats.OutBytes),
		"reconnects":       int(stats.Reconnects),
		"pending_messages": n.js.PublishAsyncPending(),
	}
}
