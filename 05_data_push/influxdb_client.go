/**
 * InfluxDB客户端模块
 *
 * 功能概述：
 * 本模块提供InfluxDB时序数据库客户端功能，负责将处理后的设备数据写入InfluxDB，
 * 支持单条和批量写入、数据类型转换、连接管理和完善的错误处理机制
 *
 * 主要功能：
 * - 数据写入：将处理后的设备数据写入InfluxDB时序数据库
 * - 批量操作：支持批量写入，提高写入性能
 * - 数据转换：自动转换数据类型，适配InfluxDB数据模型
 * - 连接管理：InfluxDB连接管理和健康检查
 * - 错误处理：完善的错误处理和重试机制
 * - 统计监控：详细的写入统计和性能指标
 * - 配置管理：灵活的配置参数和优化选项
 *
 * 技术特性：
 * - 时序数据库：专为时序数据优化的存储和查询
 * - 批量写入：高效的批量写入模式
 * - 数据压缩：自动数据压缩，节省存储空间
 * - 标签索引：使用标签进行高效索引和查询
 * - 字段类型：智能的字段类型识别和转换
 * - 异步写入：非阻塞的异步写入模式
 *
 * 数据模型：
 * - Measurement：测量名称，类似表名
 * - Tags：标签，用于索引和分组（device_id, data_type, location等）
 * - Fields：字段，实际的数值数据
 * - Timestamp：时间戳，时序数据的核心
 *
 * 性能优化：
 * - 批量写入：减少网络往返次数
 * - 数据压缩：减少网络传输和存储开销
 * - 连接复用：复用HTTP连接减少开销
 * - 异步刷新：定期异步刷新缓冲区
 *
 * 可靠性保证：
 * - 重试机制：写入失败时的自动重试
 * - 错误处理：完善的错误处理和统计
 * - 健康检查：定期检查连接状态
 * - 数据验证：写入前的数据格式验证
 *
 * 业务价值：
 * - 时序存储：专业的时序数据存储和查询
 * - 高性能：优化的批量写入性能
 * - 可扩展性：支持大规模时序数据存储
 * - 数据分析：为时序数据分析提供基础
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"fmt"
	"shared/logger"
	"strconv"
	"sync"
	"time"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	"github.com/influxdata/influxdb-client-go/v2/api/write"
)

/**
 * InfluxDB客户端结构体
 *
 * 功能：封装InfluxDB连接和操作，提供时序数据的写入和查询功能
 *
 * 架构设计：
 * - 客户端管理：封装InfluxDB客户端和API接口
 * - 双API模式：分离写入API和查询API，优化性能
 * - 统计监控：实时统计写入性能和错误信息
 * - 并发安全：使用读写锁保护统计数据
 *
 * @struct InfluxDBClient
 */
type InfluxDBClient struct {
	/** InfluxDB客户端实例，提供底层连接和操作 */
	client influxdb2.Client

	/** 写入API，专门用于数据写入操作 */
	writeAPI api.WriteAPI

	/** 查询API，专门用于数据查询和健康检查 */
	queryAPI api.QueryAPI

	/** InfluxDB配置信息，包含连接参数和写入配置 */
	config InfluxDBConfig

	/** InfluxDB操作统计信息，用于性能监控 */
	stats InfluxDBStats

	/** 读写锁，保护统计数据的并发访问 */
	mutex sync.RWMutex
}

// InfluxDBStats InfluxDB统计信息
type InfluxDBStats struct {
	TotalWrites      int64         `json:"total_writes"`      // 总写入次数
	SuccessCount     int64         `json:"success_count"`     // 成功次数
	ErrorCount       int64         `json:"error_count"`       // 错误次数
	LastError        string        `json:"last_error"`        // 最后错误
	AverageLatency   time.Duration `json:"average_latency"`   // 平均延迟
	LastWriteTime    time.Time     `json:"last_write_time"`   // 最后写入时间
	ConnectionStatus string        `json:"connection_status"` // 连接状态
	BatchSize        int           `json:"batch_size"`        // 批量大小
	QueuedPoints     int           `json:"queued_points"`     // 队列中的点数
}

// InfluxDBConfig InfluxDB配置
type InfluxDBConfig struct {
	URL           string        `yaml:"url"`            // InfluxDB服务器地址
	Token         string        `yaml:"token"`          // 认证令牌
	Organization  string        `yaml:"organization"`   // 组织名称
	Bucket        string        `yaml:"bucket"`         // 存储桶名称
	Measurement   string        `yaml:"measurement"`    // 测量名称
	BatchSize     int           `yaml:"batch_size"`     // 批量大小
	FlushInterval time.Duration `yaml:"flush_interval"` // 刷新间隔
	Timeout       time.Duration `yaml:"timeout"`        // 超时时间
	RetryCount    int           `yaml:"retry_count"`    // 重试次数
	RetryDelay    time.Duration `yaml:"retry_delay"`    // 重试延迟
}

// NewInfluxDBClient 创建新的InfluxDB客户端
func NewInfluxDBClient(config InfluxDBConfig) (*InfluxDBClient, error) {
	// 创建InfluxDB客户端
	client := influxdb2.NewClientWithOptions(
		config.URL,
		config.Token,
		influxdb2.DefaultOptions().
			SetBatchSize(uint(config.BatchSize)).
			SetFlushInterval(uint(config.FlushInterval.Milliseconds())).
			SetRetryInterval(uint(config.RetryDelay.Milliseconds())).
			SetMaxRetries(uint(config.RetryCount)),
	)

	influxClient := &InfluxDBClient{
		client: client,
		config: config,
		stats: InfluxDBStats{
			ConnectionStatus: "connecting",
			BatchSize:        config.BatchSize,
		},
	}

	// 创建写入API
	influxClient.writeAPI = client.WriteAPI(config.Organization, config.Bucket)

	// 创建查询API
	influxClient.queryAPI = client.QueryAPI(config.Organization)

	// 设置错误处理
	influxClient.setupErrorHandling()

	// 测试连接
	if err := influxClient.testConnection(); err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to connect to InfluxDB: %w", err)
	}

	influxClient.stats.ConnectionStatus = "connected"
	return influxClient, nil
}

// setupErrorHandling 设置错误处理
func (i *InfluxDBClient) setupErrorHandling() {
	// 监听写入错误
	go func() {
		for err := range i.writeAPI.Errors() {
			i.mutex.Lock()
			i.stats.ErrorCount++
			i.stats.LastError = err.Error()
			i.mutex.Unlock()

			fmt.Printf("InfluxDB write error: %v\n", err)
		}
	}()
}

// testConnection 测试连接
func (i *InfluxDBClient) testConnection() error {
	ctx, cancel := context.WithTimeout(context.Background(), i.config.Timeout)
	defer cancel()

	// 执行简单的查询来测试连接
	query := fmt.Sprintf(`from(bucket: "%s") |> range(start: -1m) |> limit(n: 1)`, i.config.Bucket)
	_, err := i.queryAPI.Query(ctx, query)

	// 忽略查询结果错误，只关心连接错误
	if err != nil && !isQueryResultError(err) {
		return err
	}

	return nil
}

// isQueryResultError 判断是否是查询结果错误（而非连接错误）
func isQueryResultError(err error) bool {
	// 这里可以根据具体的错误类型来判断
	// 暂时简单处理，认为包含"not found"的错误是查询结果错误
	return err != nil && (fmt.Sprintf("%v", err) == "not found" ||
		fmt.Sprintf("%v", err) == "bucket not found")
}

// WriteData 写入单条数据
func (i *InfluxDBClient) WriteData(ctx context.Context, data *ProcessedData) error {
	startTime := time.Now()
	defer i.updateStats(startTime, nil)

	// 转换为InfluxDB Point
	point, err := i.convertToPoint(data)
	if err != nil {
		i.updateStats(startTime, err)
		return fmt.Errorf("failed to convert data to point: %w", err)
	}

	// 写入数据点
	i.writeAPI.WritePoint(point)

	i.stats.TotalWrites++
	i.stats.SuccessCount++
	return nil
}

// BatchWriteData 批量写入数据
func (i *InfluxDBClient) BatchWriteData(ctx context.Context, dataList []*ProcessedData) error {
	if len(dataList) == 0 {
		return nil
	}

	startTime := time.Now()
	defer i.updateStats(startTime, nil)

	var points []*write.Point
	for _, data := range dataList {
		point, err := i.convertToPoint(data)
		if err != nil {
			i.updateStats(startTime, err)
			continue // 跳过转换失败的数据
		}
		points = append(points, point)
	}

	if len(points) == 0 {
		err := fmt.Errorf("no valid points to write")
		i.updateStats(startTime, err)
		return err
	}

	// 批量写入数据点
	for _, point := range points {
		i.writeAPI.WritePoint(point)
	}

	// 强制刷新
	i.writeAPI.Flush()

	i.stats.TotalWrites += int64(len(points))
	i.stats.SuccessCount += int64(len(points))
	return nil
}

// WriteFromSQLite 从SQLite记录写入数据
func (i *InfluxDBClient) WriteFromSQLite(ctx context.Context, records []*SQLiteRecord) error {
	if len(records) == 0 {
		return nil
	}

	var dataList []*ProcessedData
	for _, record := range records {
		// 解析处理后的数据
		var processedData ProcessedData
		if record.ProcessedData != "" {
			if err := processedData.FromJSON(record.ProcessedData); err != nil {
				continue // 跳过解析失败的记录
			}
		} else {
			// 如果没有处理后的数据，使用原始数据
			var deviceData DeviceData
			if err := deviceData.FromJSON(record.RawData); err != nil {
				continue
			}

			processedData = ProcessedData{
				DeviceID:      deviceData.DeviceID,
				DataType:      deviceData.DataType,
				Timestamp:     deviceData.Timestamp,
				ProcessedAt:   time.Now(),
				RawData:       deviceData.RawData,
				ProcessedData: deviceData.RawData,
				Sequence:      record.Sequence,
				Location:      deviceData.Location,
			}
		}

		dataList = append(dataList, &processedData)
	}

	return i.BatchWriteData(ctx, dataList)
}

// convertToPoint 转换数据为InfluxDB Point
func (i *InfluxDBClient) convertToPoint(data *ProcessedData) (*write.Point, error) {
	// 创建数据点
	point := influxdb2.NewPoint(
		i.config.Measurement,
		map[string]string{
			"device_id": data.DeviceID,
			"data_type": data.DataType,
			"location":  data.Location,
			"sequence":  data.Sequence,
		},
		i.extractFields(data),
		data.Timestamp,
	)

	return point, nil
}

// extractFields 提取字段数据
func (i *InfluxDBClient) extractFields(data *ProcessedData) map[string]interface{} {
	fields := make(map[string]interface{})

	// 添加质量评分
	if data.QualityScore > 0 {
		fields["quality_score"] = data.QualityScore
	}

	// 添加处理后的数据
	if data.ProcessedData != nil {
		for key, value := range data.ProcessedData {
			// 转换数据类型
			switch v := value.(type) {
			case float64:
				fields[key] = v
			case int:
				fields[key] = float64(v)
			case int64:
				fields[key] = float64(v)
			case string:
				// 尝试转换为数字
				if floatVal, err := strconv.ParseFloat(v, 64); err == nil {
					fields[key] = floatVal
				} else {
					fields[key+"_str"] = v // 字符串字段添加后缀
				}
			case bool:
				fields[key] = v
			default:
				// 其他类型转换为字符串
				fields[key+"_str"] = fmt.Sprintf("%v", v)
			}
		}
	}

	// 如果没有处理后的数据，使用原始数据
	if len(fields) == 0 && data.RawData != nil {
		for key, value := range data.RawData {
			switch v := value.(type) {
			case float64:
				fields[key] = v
			case int:
				fields[key] = float64(v)
			case int64:
				fields[key] = float64(v)
			case string:
				if floatVal, err := strconv.ParseFloat(v, 64); err == nil {
					fields[key] = floatVal
				} else {
					fields[key+"_str"] = v
				}
			case bool:
				fields[key] = v
			default:
				fields[key+"_str"] = fmt.Sprintf("%v", v)
			}
		}
	}

	// 添加数据处理元数据
	if data.Metadata.CleaningVersion != "" {
		fields["cleaning_version_str"] = data.Metadata.CleaningVersion
	}
	if data.Metadata.ProcessingTime > 0 {
		fields["processing_time_ms"] = data.Metadata.ProcessingTime
	}

	// 添加原始设备元数据（设备名称、品牌、型号等）
	if data.OriginalMetadata != nil {
		for key, value := range data.OriginalMetadata {
			switch v := value.(type) {
			case string:
				// 设备信息字段直接作为字符串存储
				if key == "device_name" || key == "brand" || key == "model" ||
					key == "ip" || key == "firmware_version" {
					fields[key+"_str"] = v
				}
			case float64:
				fields[key] = v
			case int:
				fields[key] = float64(v)
			case int64:
				fields[key] = float64(v)
			case bool:
				fields[key] = v
			default:
				// 其他类型转换为字符串
				fields[key+"_str"] = fmt.Sprintf("%v", v)
			}
		}
	}

	// 确保至少有一个字段
	if len(fields) == 0 {
		fields["value"] = 1.0 // 默认值
	}

	return fields
}

// HealthCheck 健康检查
func (i *InfluxDBClient) HealthCheck(ctx context.Context) error {
	startTime := time.Now()
	defer i.updateStats(startTime, nil)

	// 执行简单的查询来检查连接
	query := fmt.Sprintf(`from(bucket: "%s") |> range(start: -1m) |> limit(n: 1)`, i.config.Bucket)
	_, err := i.queryAPI.Query(ctx, query)

	if err != nil && !isQueryResultError(err) {
		i.updateStats(startTime, err)
		i.stats.ConnectionStatus = "disconnected"
		return fmt.Errorf("InfluxDB health check failed: %w", err)
	}

	i.stats.ConnectionStatus = "connected"
	return nil
}

// GetStats 获取统计信息
func (i *InfluxDBClient) GetStats() InfluxDBStats {
	i.mutex.RLock()
	defer i.mutex.RUnlock()
	return i.stats
}

// updateStats 更新统计信息
func (i *InfluxDBClient) updateStats(startTime time.Time, err error) {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	latency := time.Since(startTime)
	i.stats.LastWriteTime = time.Now()

	// 更新平均延迟
	if i.stats.AverageLatency == 0 {
		i.stats.AverageLatency = latency
	} else {
		i.stats.AverageLatency = (i.stats.AverageLatency + latency) / 2
	}

	// 更新错误信息
	if err != nil {
		i.stats.ErrorCount++
		i.stats.LastError = err.Error()
	}
}

// Close 关闭InfluxDB客户端
func (i *InfluxDBClient) Close() error {
	// 安全地刷新写入API，避免panic
	if i.writeAPI != nil {
		// 使用defer和recover来捕获可能的panic
		func() {
			defer func() {
				if r := recover(); r != nil {
					// 记录panic但不中断程序
					logger.Warnf("InfluxDB writeAPI flush panic recovered: %v", r)
				}
			}()
			i.writeAPI.Flush()
		}()
	}

	// 关闭客户端连接
	if i.client != nil {
		i.client.Close()
	}

	// 更新连接状态
	i.mutex.Lock()
	i.stats.ConnectionStatus = "closed"
	i.mutex.Unlock()

	return nil
}

// GetConnectionInfo 获取连接信息
func (i *InfluxDBClient) GetConnectionInfo() map[string]interface{} {
	return map[string]interface{}{
		"url":          i.config.URL,
		"organization": i.config.Organization,
		"bucket":       i.config.Bucket,
		"measurement":  i.config.Measurement,
		"batch_size":   i.config.BatchSize,
		"status":       i.stats.ConnectionStatus,
	}
}

// Flush 强制刷新缓冲区
func (i *InfluxDBClient) Flush() {
	if i.writeAPI != nil {
		i.writeAPI.Flush()
	}
}
