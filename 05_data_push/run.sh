#!/bin/bash

# 数据推送服务启动脚本
# 解决Go环境配置问题并启动05_data_push服务

echo "🚀 启动 数据推送服务"
echo "=================================="

# 检查并修复Go环境
echo "🔧 检查Go环境..."

# 设置正确的Go环境变量
export PATH=/opt/homebrew/bin:$PATH
export GOROOT=/opt/homebrew/opt/go/libexec

# 验证Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: Go未安装或不在PATH中"
    echo "请确保Go已正确安装在 /opt/homebrew/opt/go"
    exit 1
fi

# 显示Go版本信息
echo "✅ Go版本: $(go version)"
echo "✅ GOROOT: $(go env GOROOT)"
echo "✅ GOPATH: $(go env GOPATH)"

# 检查是否在正确的目录
if [ ! -f "main.go" ]; then
    echo "❌ 错误: 未找到main.go文件"
    echo "请确保在05_data_push目录中运行此脚本"
    exit 1
fi

# 检查配置文件
echo "🔍 检查配置文件..."
if [ ! -f "config.yml" ]; then
    echo "❌ 错误: 未找到config.yml配置文件"
    echo "请确保配置文件存在"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 检查Redis服务
echo "🔍 检查Redis服务..."
REDIS_HOST="localhost"
REDIS_PORT="6379"

# 检查Redis端口是否可达
if ! nc -z $REDIS_HOST $REDIS_PORT 2>/dev/null; then
    echo "⚠️  警告: Redis服务 ($REDIS_HOST:$REDIS_PORT) 不可达"
    echo ""
    echo "🔧 Redis排查建议:"
    echo "  1. 检查Redis是否运行:"
    echo "     docker ps | grep redis"
    echo "     或 systemctl status redis"
    echo ""
    echo "  2. 启动Redis服务:"
    echo "     Docker: docker-compose up -d redis"
    echo "     系统服务: sudo systemctl start redis"
    echo ""
    echo "  3. 检查端口占用:"
    echo "     lsof -i :$REDIS_PORT"
    echo ""
    echo "  4. 测试Redis连接:"
    echo "     redis-cli -h $REDIS_HOST -p $REDIS_PORT ping"
    echo ""
    echo "⚠️  服务将尝试启动，但可能因Redis连接失败而退出"
    echo "   建议先解决Redis连接问题"
    echo ""
else
    echo "✅ Redis服务检查通过 ($REDIS_HOST:$REDIS_PORT)"

    # 尝试ping Redis
    if command -v redis-cli &> /dev/null; then
        if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping &>/dev/null; then
            echo "✅ Redis连接测试成功"
        else
            echo "⚠️  Redis连接测试失败，可能需要密码认证"
        fi
    fi
fi

# 检查端口是否被占用
PORT=9003
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 $PORT 已被占用"
    echo "正在尝试停止占用端口的进程..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p data
mkdir -p scripts

# 下载Go依赖
echo "📦 下载Go依赖..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "❌ 错误: Go依赖下载失败"
    exit 1
fi

echo "✅ Go依赖下载完成"

echo ""
echo "🌐 启动数据推送服务..."
echo "端口: $PORT"
echo "访问地址: http://localhost:$PORT"
echo "健康检查: http://localhost:$PORT/health"
echo ""
echo "功能特性:"
echo "  - 数据推送到InfluxDB"
echo "  - NATS消息队列处理"
echo "  - Redis数据缓存"
echo "  - SQLite本地存储"
echo "  - JavaScript数据处理"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 启动数据推送服务
go run .
