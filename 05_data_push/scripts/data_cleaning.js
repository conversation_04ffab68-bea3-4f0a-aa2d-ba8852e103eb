// 数据清洗脚本
// 使用JavaScript对设备数据进行清洗、验证和转换

/**
 * 主要清洗函数
 * @param {Object} rawData - 原始数据对象
 * @returns {Object} 清洗后的数据对象
 */
function cleanData(rawData) {
    try {
        // 验证输入数据
        if (!rawData || typeof rawData !== 'object') {
            throw new Error('Invalid input data: must be an object');
        }

        // 必需字段验证
        const requiredFields = ['device_id', 'data_type', 'raw_data'];
        for (let field of requiredFields) {
            if (!rawData[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }

        // 创建清洗后的数据对象
        let cleanedData = {
            device_id: rawData.device_id,
            data_type: rawData.data_type,
            timestamp: rawData.timestamp || new Date().toISOString(),
            processed_at: new Date().toISOString(),
            raw_data: {},
            processed_data: {},
            metadata: {
                cleaning_version: "1.0.0",
                cleaning_rules: []
            }
        };

        // 根据设备类型进行不同的清洗处理
        switch (rawData.data_type) {
            case 'temperature':
                cleanedData = cleanTemperatureData(rawData, cleanedData);
                break;
            case 'pressure':
                cleanedData = cleanPressureData(rawData, cleanedData);
                break;
            case 'motion':
                cleanedData = cleanMotionData(rawData, cleanedData);
                break;
            default:
                cleanedData = cleanGenericData(rawData, cleanedData);
                break;
        }

        // 数据质量评分
        cleanedData.quality_score = calculateQualityScore(cleanedData);

        return cleanedData;

    } catch (error) {
        // 返回错误信息，但不抛出异常
        return {
            device_id: rawData.device_id || 'unknown',
            data_type: rawData.data_type || 'unknown',
            timestamp: new Date().toISOString(),
            error: true,
            error_message: error.message,
            raw_data: rawData
        };
    }
}

/**
 * 清洗温度传感器数据
 */
function cleanTemperatureData(rawData, cleanedData) {
    let rules = [];
    
    // 复制原始数据
    cleanedData.raw_data = JSON.parse(JSON.stringify(rawData.raw_data));
    
    // 温度数据处理
    if (rawData.raw_data.temperature !== undefined) {
        let temp = parseFloat(rawData.raw_data.temperature);
        
        // 温度范围验证 (-50°C to 100°C)
        if (temp < -50 || temp > 100) {
            rules.push('temperature_out_of_range');
            temp = Math.max(-50, Math.min(100, temp)); // 限制在合理范围内
        }
        
        // 温度精度处理（保留1位小数）
        cleanedData.processed_data.temperature = Math.round(temp * 10) / 10;
        cleanedData.processed_data.temperature_celsius = cleanedData.processed_data.temperature;
        cleanedData.processed_data.temperature_fahrenheit = Math.round((temp * 9/5 + 32) * 10) / 10;
        rules.push('temperature_processed');
    }
    
    // 湿度数据处理
    if (rawData.raw_data.humidity !== undefined) {
        let humidity = parseFloat(rawData.raw_data.humidity);
        
        // 湿度范围验证 (0% to 100%)
        if (humidity < 0 || humidity > 100) {
            rules.push('humidity_out_of_range');
            humidity = Math.max(0, Math.min(100, humidity));
        }
        
        cleanedData.processed_data.humidity = Math.round(humidity * 10) / 10;
        rules.push('humidity_processed');
    }
    
    cleanedData.metadata.cleaning_rules = rules;
    return cleanedData;
}

/**
 * 清洗压力传感器数据
 */
function cleanPressureData(rawData, cleanedData) {
    let rules = [];
    
    cleanedData.raw_data = JSON.parse(JSON.stringify(rawData.raw_data));
    
    // 压力数据处理
    if (rawData.raw_data.pressure !== undefined) {
        let pressure = parseFloat(rawData.raw_data.pressure);
        
        // 压力范围验证 (0.5 to 2.0 atm)
        if (pressure < 0.5 || pressure > 2.0) {
            rules.push('pressure_out_of_range');
            pressure = Math.max(0.5, Math.min(2.0, pressure));
        }
        
        cleanedData.processed_data.pressure_atm = Math.round(pressure * 1000) / 1000;
        cleanedData.processed_data.pressure_pa = Math.round(pressure * 101325);
        cleanedData.processed_data.pressure_psi = Math.round(pressure * 14.696 * 100) / 100;
        rules.push('pressure_processed');
    }
    
    // 海拔数据处理
    if (rawData.raw_data.altitude !== undefined) {
        let altitude = parseInt(rawData.raw_data.altitude);
        
        // 海拔范围验证 (-500m to 9000m)
        if (altitude < -500 || altitude > 9000) {
            rules.push('altitude_out_of_range');
            altitude = Math.max(-500, Math.min(9000, altitude));
        }
        
        cleanedData.processed_data.altitude_meters = altitude;
        cleanedData.processed_data.altitude_feet = Math.round(altitude * 3.28084);
        rules.push('altitude_processed');
    }
    
    cleanedData.metadata.cleaning_rules = rules;
    return cleanedData;
}

/**
 * 清洗运动传感器数据
 */
function cleanMotionData(rawData, cleanedData) {
    let rules = [];
    
    cleanedData.raw_data = JSON.parse(JSON.stringify(rawData.raw_data));
    
    // 加速度数据处理
    ['acceleration_x', 'acceleration_y', 'acceleration_z'].forEach(function(axis) {
        if (rawData.raw_data[axis] !== undefined) {
            let acc = parseFloat(rawData.raw_data[axis]);
            
            // 加速度范围验证 (-20g to 20g)
            if (acc < -20 || acc > 20) {
                rules.push(axis + '_out_of_range');
                acc = Math.max(-20, Math.min(20, acc));
            }
            
            cleanedData.processed_data[axis] = Math.round(acc * 100) / 100;
        }
    });
    
    // 计算总加速度
    if (cleanedData.processed_data.acceleration_x !== undefined &&
        cleanedData.processed_data.acceleration_y !== undefined &&
        cleanedData.processed_data.acceleration_z !== undefined) {
        
        let totalAcc = Math.sqrt(
            Math.pow(cleanedData.processed_data.acceleration_x, 2) +
            Math.pow(cleanedData.processed_data.acceleration_y, 2) +
            Math.pow(cleanedData.processed_data.acceleration_z, 2)
        );
        cleanedData.processed_data.total_acceleration = Math.round(totalAcc * 100) / 100;
        rules.push('total_acceleration_calculated');
    }
    
    // 陀螺仪数据处理
    if (rawData.raw_data.gyro_x !== undefined) {
        let gyro = parseFloat(rawData.raw_data.gyro_x);
        if (gyro < -180 || gyro > 180) {
            rules.push('gyro_x_out_of_range');
            gyro = Math.max(-180, Math.min(180, gyro));
        }
        cleanedData.processed_data.gyro_x = Math.round(gyro * 100) / 100;
    }
    
    cleanedData.metadata.cleaning_rules = rules;
    return cleanedData;
}

/**
 * 清洗通用数据
 */
function cleanGenericData(rawData, cleanedData) {
    let rules = [];
    
    cleanedData.raw_data = JSON.parse(JSON.stringify(rawData.raw_data));
    cleanedData.processed_data = JSON.parse(JSON.stringify(rawData.raw_data));
    
    // 通用数据验证和清洗
    for (let key in cleanedData.processed_data) {
        let value = cleanedData.processed_data[key];
        
        // 数值类型处理
        if (typeof value === 'number') {
            // 检查是否为有效数字
            if (isNaN(value) || !isFinite(value)) {
                delete cleanedData.processed_data[key];
                rules.push(key + '_invalid_number');
            } else {
                // 限制精度
                cleanedData.processed_data[key] = Math.round(value * 1000) / 1000;
            }
        }
        
        // 字符串类型处理
        if (typeof value === 'string') {
            // 去除前后空格
            cleanedData.processed_data[key] = value.trim();
            
            // 空字符串处理
            if (cleanedData.processed_data[key] === '') {
                delete cleanedData.processed_data[key];
                rules.push(key + '_empty_string');
            }
        }
    }
    
    rules.push('generic_cleaning_applied');
    cleanedData.metadata.cleaning_rules = rules;
    return cleanedData;
}

/**
 * 计算数据质量评分
 */
function calculateQualityScore(cleanedData) {
    let score = 100;
    
    // 根据清洗规则扣分
    if (cleanedData.metadata && cleanedData.metadata.cleaning_rules) {
        let rules = cleanedData.metadata.cleaning_rules;
        
        // 超出范围扣分
        let outOfRangeRules = rules.filter(rule => rule.includes('out_of_range'));
        score -= outOfRangeRules.length * 10;
        
        // 无效数据扣分
        let invalidRules = rules.filter(rule => rule.includes('invalid') || rule.includes('empty'));
        score -= invalidRules.length * 15;
    }
    
    // 确保分数在0-100范围内
    return Math.max(0, Math.min(100, score));
}

/**
 * 验证清洗后的数据
 */
function validateCleanedData(cleanedData) {
    if (!cleanedData || typeof cleanedData !== 'object') {
        return false;
    }
    
    // 检查必需字段
    const requiredFields = ['device_id', 'data_type', 'timestamp'];
    for (let field of requiredFields) {
        if (!cleanedData[field]) {
            return false;
        }
    }
    
    // 检查是否有处理后的数据或错误信息
    if (!cleanedData.processed_data && !cleanedData.error) {
        return false;
    }
    
    return true;
}

// 导出主要函数（Go程序会调用这些函数）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        cleanData: cleanData,
        validateCleanedData: validateCleanedData
    };
}
