/**
 * 设备离线监控模块 - 05_data_push版本
 *
 * 功能概述：
 * 本模块负责监控设备数据处理状态，自动检测设备离线并管理对应的processor，
 * 通过Redis记录设备最后处理时间，定期检查设备状态，实现设备离线检测和资源优化功能。
 *
 * 主要功能：
 * - 设备状态跟踪：记录每个设备最后处理数据的时间戳
 * - 离线检测：定期检查设备是否超时未处理数据
 * - Processor管理：自动关闭离线设备的processor节省资源
 * - 状态管理：管理设备上线/离线状态转换
 * - 资源清理：定期清理过期的设备状态记录
 *
 * 技术特性：
 * - Redis存储：使用Redis存储设备最后处理时间，支持分布式部署
 * - 定时检查：使用ticker定期检查设备状态
 * - 并发安全：使用sync.RWMutex保护并发访问
 * - 内存控制：限制最大离线设备数量，防止内存泄露
 * - 优雅关闭：支持优雅关闭和资源清理
 *
 * 业务场景：
 * - 资源优化：及时关闭离线设备的processor节省系统资源
 * - 性能监控：监控设备数据处理的连续性
 * - 系统稳定：防止无效processor占用系统资源
 * - 运维管理：为运维人员提供设备处理状态信息
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-08
 */
package main

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"
)

/**
 * 设备监控器结构体
 *
 * 功能：管理设备离线检测的核心逻辑和processor管理
 *
 * 架构设计：
 * - 状态管理：维护设备在线/离线状态
 * - 定时任务：定期检查设备状态和清理过期数据
 * - 并发控制：使用读写锁保护共享状态
 * - 资源管理：管理Redis连接和goroutine生命周期
 *
 * @struct DeviceMonitor
 */
type DeviceMonitor struct {
	/** 服务配置信息，包含监控参数和Redis连接配置 */
	config *Config

	/** Redis客户端实例，用于存储设备最后处理时间 */
	redisClient *RedisClient

	/** 全局上下文对象，用于控制goroutine生命周期 */
	ctx context.Context

	/** 上下文取消函数，用于优雅关闭监控器 */
	cancel context.CancelFunc

	/** 设备处理器管理器实例，用于管理processor */
	processorManager *DeviceProcessorManager

	/** 离线设备集合，记录当前已知的离线设备，防止重复处理 */
	offlineDevices map[string]time.Time

	/** 读写锁，保护offlineDevices的并发访问 */
	mutex sync.RWMutex

	/** 等待组，用于等待所有goroutine完成 */
	wg sync.WaitGroup
}

// NewDeviceMonitor 创建设备监控器实例
// 参数:
//   - cfg: 服务配置
//   - redisClient: Redis客户端
//   - processorManager: 设备处理器管理器实例
//
// 返回:
//   - *DeviceMonitor: 监控器实例
func NewDeviceMonitor(cfg *Config, redisClient *RedisClient, processorManager *DeviceProcessorManager) *DeviceMonitor {
	ctx, cancel := context.WithCancel(context.Background())

	return &DeviceMonitor{
		config:           cfg,
		redisClient:      redisClient,
		ctx:              ctx,
		cancel:           cancel,
		processorManager: processorManager,
		offlineDevices:   make(map[string]time.Time),
		mutex:            sync.RWMutex{},
		wg:               sync.WaitGroup{},
	}
}

// Start 启动设备监控器
// 启动定期检查和清理任务的goroutine
func (dm *DeviceMonitor) Start() {
	if !dm.config.DeviceMonitor.Enabled {
		fmt.Println("Device monitor is disabled")
		return
	}

	fmt.Println("Starting device monitor...")

	// 启动设备状态检查任务
	dm.wg.Add(1)
	go dm.startStatusChecker()

	// 启动清理任务
	dm.wg.Add(1)
	go dm.startCleanupTask()

	fmt.Printf("Device monitor started with check_interval=%ds, offline_timeout=%ds\n",
		dm.config.DeviceMonitor.CheckInterval,
		dm.config.DeviceMonitor.OfflineTimeout)
}

// Stop 停止设备监控器
// 优雅关闭所有goroutine并清理资源
func (dm *DeviceMonitor) Stop() {
	fmt.Println("Stopping device monitor...")

	// 取消上下文，通知所有goroutine停止
	dm.cancel()

	// 等待所有goroutine完成
	dm.wg.Wait()

	fmt.Println("Device monitor stopped")
}

// UpdateDeviceLastProcessed 更新设备最后处理时间
// 当处理设备数据时调用此方法更新设备状态
// 参数:
//   - deviceID: 设备ID
//
// 返回:
//   - error: 更新失败时返回错误
func (dm *DeviceMonitor) UpdateDeviceLastProcessed(deviceID string) error {
	if !dm.config.DeviceMonitor.Enabled {
		return nil
	}

	// 构建Redis key
	key := dm.config.DeviceMonitor.RedisKeyPrefix + deviceID

	// 获取当前时间戳
	timestamp := time.Now().Unix()

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(dm.ctx, 3*time.Second)
	defer cancel()

	// 将时间戳存储到Redis，设置过期时间为离线超时时间的2倍
	expiration := time.Duration(dm.config.DeviceMonitor.OfflineTimeout*2) * time.Second
	err := dm.redisClient.client.Set(ctx, key, timestamp, expiration).Err()
	if err != nil {
		fmt.Printf("Failed to update device last processed for %s: %v\n", deviceID, err)
		return err
	}

	// 如果设备之前是离线状态，现在重新上线，从离线设备列表中移除
	dm.mutex.Lock()
	if _, exists := dm.offlineDevices[deviceID]; exists {
		delete(dm.offlineDevices, deviceID)
		fmt.Printf("Device %s is back online\n", deviceID)
	}
	dm.mutex.Unlock()

	fmt.Printf("Updated last processed time for device %s\n", deviceID)
	return nil
}

// startStatusChecker 启动设备状态检查任务
// 定期检查所有设备的最后处理时间，发现离线设备并关闭对应的processor
func (dm *DeviceMonitor) startStatusChecker() {
	defer dm.wg.Done()

	ticker := time.NewTicker(time.Duration(dm.config.DeviceMonitor.CheckInterval) * time.Second)
	defer ticker.Stop()

	fmt.Printf("Device status checker started, checking every %d seconds\n", dm.config.DeviceMonitor.CheckInterval)

	for {
		select {
		case <-ticker.C:
			dm.checkDeviceStatus()
		case <-dm.ctx.Done():
			fmt.Println("Device status checker stopped")
			return
		}
	}
}

// checkDeviceStatus 检查设备状态
// 扫描Redis中的设备最后处理时间，发现离线设备
func (dm *DeviceMonitor) checkDeviceStatus() {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(dm.ctx, 10*time.Second)
	defer cancel()

	// 扫描所有设备最后处理时间的key
	pattern := dm.config.DeviceMonitor.RedisKeyPrefix + "*"
	keys, err := dm.redisClient.client.Keys(ctx, pattern).Result()
	if err != nil {
		fmt.Printf("Failed to scan device keys: %v\n", err)
		return
	}

	fmt.Printf("Checking status for %d devices\n", len(keys))

	currentTime := time.Now().Unix()
	offlineThreshold := int64(dm.config.DeviceMonitor.OfflineTimeout)

	for _, key := range keys {
		// 提取设备ID
		deviceID := key[len(dm.config.DeviceMonitor.RedisKeyPrefix):]

		// 获取设备最后处理时间
		lastProcessedStr, err := dm.redisClient.client.Get(ctx, key).Result()
		if err != nil {
			// key不存在或其他错误，可能设备已离线
			fmt.Printf("Device %s key not found or error: %v\n", deviceID, err)
			dm.handleDeviceOffline(deviceID)
			continue
		}

		// 解析时间戳
		lastProcessed, err := strconv.ParseInt(lastProcessedStr, 10, 64)
		if err != nil {
			fmt.Printf("Invalid timestamp for device %s: %s\n", deviceID, lastProcessedStr)
			continue
		}

		// 检查是否超时
		if currentTime-lastProcessed > offlineThreshold {
			fmt.Printf("Device %s is offline (last processed: %d seconds ago)\n", deviceID, currentTime-lastProcessed)
			dm.handleDeviceOffline(deviceID)
		}
	}
}

// handleDeviceOffline 处理设备离线
// 关闭设备对应的processor并更新离线设备列表
// 参数:
//   - deviceID: 离线的设备ID
func (dm *DeviceMonitor) handleDeviceOffline(deviceID string) {
	// 检查是否已经处理过离线
	dm.mutex.Lock()
	if offlineTime, exists := dm.offlineDevices[deviceID]; exists {
		// 检查是否需要关闭processor（延迟关闭）
		if time.Since(offlineTime) > time.Duration(dm.config.DeviceMonitor.ProcessorShutdownDelay)*time.Second {
			dm.mutex.Unlock()
			dm.shutdownDeviceProcessor(deviceID)
			return
		}
		dm.mutex.Unlock()
		return // 已经标记为离线，但还在延迟期内
	}

	// 检查离线设备数量限制
	if len(dm.offlineDevices) >= dm.config.DeviceMonitor.MaxOfflineDevices {
		fmt.Printf("Too many offline devices (%d), skipping device %s\n", len(dm.offlineDevices), deviceID)
		dm.mutex.Unlock()
		return
	}

	// 标记设备为离线
	dm.offlineDevices[deviceID] = time.Now()
	dm.mutex.Unlock()

	// 删除Redis中的设备状态记录
	key := dm.config.DeviceMonitor.RedisKeyPrefix + deviceID
	ctx, cancel := context.WithTimeout(dm.ctx, 3*time.Second)
	defer cancel()

	err := dm.redisClient.client.Del(ctx, key).Err()
	if err != nil {
		fmt.Printf("Failed to delete device key %s: %v\n", key, err)
	}

	fmt.Printf("Device %s marked as offline, will shutdown processor in %d seconds\n",
		deviceID, dm.config.DeviceMonitor.ProcessorShutdownDelay)
}

// shutdownDeviceProcessor 关闭设备processor
// 通知processor管理器关闭指定设备的processor
// 参数:
//   - deviceID: 需要关闭processor的设备ID
func (dm *DeviceMonitor) shutdownDeviceProcessor(deviceID string) {
	fmt.Printf("Shutting down processor for offline device: %s\n", deviceID)

	// 通知processor管理器关闭设备processor
	if dm.processorManager != nil {
		dm.processorManager.ShutdownDeviceProcessor(deviceID)
	}

	// 从离线设备列表中移除（processor已关闭）
	dm.mutex.Lock()
	delete(dm.offlineDevices, deviceID)
	dm.mutex.Unlock()

	fmt.Printf("Processor for device %s has been shutdown\n", deviceID)
}

// startCleanupTask 启动清理任务
// 定期清理过期的设备状态记录和离线设备列表
func (dm *DeviceMonitor) startCleanupTask() {
	defer dm.wg.Done()

	ticker := time.NewTicker(time.Duration(dm.config.DeviceMonitor.CleanupInterval) * time.Second)
	defer ticker.Stop()

	fmt.Printf("Device cleanup task started, cleaning every %d seconds\n", dm.config.DeviceMonitor.CleanupInterval)

	for {
		select {
		case <-ticker.C:
			dm.cleanupExpiredDevices()
		case <-dm.ctx.Done():
			fmt.Println("Device cleanup task stopped")
			return
		}
	}
}

// cleanupExpiredDevices 清理过期的设备状态记录
// 清理Redis中过期的设备状态记录和内存中的离线设备列表
func (dm *DeviceMonitor) cleanupExpiredDevices() {
	fmt.Println("Starting device cleanup task")

	// 清理内存中的离线设备列表
	dm.mutex.Lock()
	beforeCount := len(dm.offlineDevices)

	// 检查是否有需要关闭processor的设备
	shutdownDelay := time.Duration(dm.config.DeviceMonitor.ProcessorShutdownDelay) * time.Second
	var devicesToShutdown []string

	for deviceID, offlineTime := range dm.offlineDevices {
		if time.Since(offlineTime) > shutdownDelay {
			devicesToShutdown = append(devicesToShutdown, deviceID)
		}
	}
	dm.mutex.Unlock()

	// 关闭需要关闭的processor
	for _, deviceID := range devicesToShutdown {
		dm.shutdownDeviceProcessor(deviceID)
	}

	// 如果离线设备数量过多，清理一半
	dm.mutex.Lock()
	if len(dm.offlineDevices) > dm.config.DeviceMonitor.MaxOfflineDevices/2 {
		// 创建新的map，只保留一半设备
		newOfflineDevices := make(map[string]time.Time)
		count := 0
		maxKeep := dm.config.DeviceMonitor.MaxOfflineDevices / 4

		for deviceID, offlineTime := range dm.offlineDevices {
			if count < maxKeep {
				newOfflineDevices[deviceID] = offlineTime
				count++
			}
		}

		dm.offlineDevices = newOfflineDevices
		fmt.Printf("Cleaned up offline devices list: %d -> %d\n", beforeCount, len(dm.offlineDevices))
	}
	dm.mutex.Unlock()

	fmt.Printf("Cleanup completed. Offline devices in memory: %d\n", len(dm.offlineDevices))
}

// GetStats 获取设备监控统计信息
// 返回设备监控的运行状态和统计数据
// 返回:
//   - map[string]interface{}: 包含监控统计信息的map
func (dm *DeviceMonitor) GetStats() map[string]interface{} {
	if !dm.config.DeviceMonitor.Enabled {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	dm.mutex.RLock()
	offlineCount := len(dm.offlineDevices)
	dm.mutex.RUnlock()

	// 获取活跃设备数量
	ctx, cancel := context.WithTimeout(dm.ctx, 5*time.Second)
	defer cancel()

	pattern := dm.config.DeviceMonitor.RedisKeyPrefix + "*"
	keys, err := dm.redisClient.client.Keys(ctx, pattern).Result()
	activeCount := 0
	if err == nil {
		activeCount = len(keys)
	}

	return map[string]interface{}{
		"enabled":                  true,
		"check_interval":           dm.config.DeviceMonitor.CheckInterval,
		"offline_timeout":          dm.config.DeviceMonitor.OfflineTimeout,
		"cleanup_interval":         dm.config.DeviceMonitor.CleanupInterval,
		"max_offline_devices":      dm.config.DeviceMonitor.MaxOfflineDevices,
		"processor_shutdown_delay": dm.config.DeviceMonitor.ProcessorShutdownDelay,
		"active_devices":           activeCount,
		"offline_devices":          offlineCount,
		"redis_key_prefix":         dm.config.DeviceMonitor.RedisKeyPrefix,
	}
}
