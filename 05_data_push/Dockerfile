# 构建阶段
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制go mod文件和shared模块
COPY 05_data_push/go.mod 05_data_push/go.sum ./
COPY shared ./shared
# 修改go.mod中的shared路径
RUN sed -i 's|replace shared => ../shared|replace shared => ./shared|' go.mod
RUN go mod download

# 复制源代码
COPY 05_data_push/ .
# 再次确保go.mod中的shared路径正确
RUN sed -i 's|replace shared => ../shared|replace shared => ./shared|' go.mod

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段
FROM alpine:latest

# 安装必要的包
RUN apk --no-cache add ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .

# 创建必要的目录
RUN mkdir -p /data/sqlite
RUN mkdir -p /app/logs
RUN mkdir -p /app/scripts

# 复制配置文件和脚本
COPY 05_data_push/config.yml ./
COPY 05_data_push/scripts/ ./scripts/

# 设置时区
ENV TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8085

# 启动应用
CMD ["./main"]
