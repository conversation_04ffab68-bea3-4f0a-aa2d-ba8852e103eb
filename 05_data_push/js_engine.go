/**
 * JavaScript引擎模块
 *
 * 功能概述：
 * 本模块提供JavaScript数据清洗引擎功能，使用goja引擎执行用户自定义的数据清洗脚本，
 * 支持动态脚本加载、超时控制、错误处理和性能监控，为灵活的数据清洗提供强大支持
 *
 * 主要功能：
 * - 脚本执行：使用goja引擎执行JavaScript数据清洗脚本
 * - 动态加载：支持脚本文件的动态加载和热重载
 * - 超时控制：防止脚本执行时间过长影响系统性能
 * - 错误处理：完善的脚本执行错误处理和恢复机制
 * - 性能监控：详细的执行统计和性能指标
 * - 内存管理：脚本执行的内存使用监控和限制
 * - 全局对象：提供console、JSON等常用全局对象
 *
 * 技术特性：
 * - goja引擎：高性能的JavaScript执行引擎
 * - 并发安全：使用读写锁保护脚本执行
 * - 上下文控制：使用context控制脚本执行超时
 * - 缓存机制：脚本编译结果缓存，提高执行效率
 * - 热重载：支持脚本文件的热重载，无需重启服务
 * - 异常恢复：panic恢复机制，防止脚本错误影响服务
 *
 * 脚本接口：
 * - cleanData函数：主要的数据清洗函数，接收设备数据，返回清洗结果
 * - 输入格式：设备原始数据的JSON对象
 * - 输出格式：包含清洗后数据、质量评分、元数据的JSON对象
 * - 全局对象：console（日志输出）、JSON（数据转换）、Date（时间处理）
 *
 * 数据清洗流程：
 * 1. 脚本加载：加载和编译JavaScript清洗脚本
 * 2. 数据转换：将Go结构体转换为JavaScript对象
 * 3. 脚本执行：调用cleanData函数执行清洗逻辑
 * 4. 结果转换：将JavaScript结果转换为Go结构体
 * 5. 质量评分：计算数据质量评分
 * 6. 元数据生成：生成处理元数据和统计信息
 *
 * 性能优化：
 * - 脚本缓存：编译后的脚本缓存，避免重复编译
 * - 并发执行：支持多个脚本并发执行
 * - 内存控制：监控和限制脚本内存使用
 * - 超时保护：防止脚本执行时间过长
 *
 * 可靠性保证：
 * - 错误隔离：脚本错误不影响其他数据处理
 * - 异常恢复：panic恢复机制保证服务稳定
 * - 超时控制：防止脚本无限执行
 * - 状态监控：实时监控脚本执行状态
 *
 * 业务价值：
 * - 灵活清洗：支持复杂的数据清洗逻辑
 * - 动态配置：无需重启即可更新清洗规则
 * - 高性能：优化的脚本执行性能
 * - 易于维护：JavaScript脚本易于编写和维护
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/dop251/goja"
)

/**
 * JavaScript引擎结构体
 *
 * 功能：封装goja JavaScript引擎，提供数据清洗脚本的执行环境
 *
 * 架构设计：
 * - 引擎封装：封装goja运行时和脚本管理
 * - 缓存机制：缓存编译后的脚本，提高执行效率
 * - 并发安全：使用读写锁保护脚本执行
 * - 状态管理：管理脚本文件状态和执行统计
 *
 * @struct JSEngine
 */
type JSEngine struct {
	/** goja JavaScript运行时实例 */
	vm *goja.Runtime

	/** 编译后的JavaScript程序，用于缓存 */
	script *goja.Program

	/** JavaScript脚本文件的完整路径 */
	scriptFile string

	/** 脚本文件的最后修改时间，用于热重载检测 */
	lastModTime time.Time

	/** 数据清洗配置，包含超时、内存限制等参数 */
	config DataCleaningConfig

	/** 读写锁，保护脚本执行和状态更新的并发安全 */
	mutex sync.RWMutex

	/** JavaScript引擎的运行统计信息 */
	stats JSEngineStats
}

// JSEngineStats JavaScript引擎统计信息
type JSEngineStats struct {
	TotalExecutions   int64         `json:"total_executions"`    // 总执行次数
	SuccessCount      int64         `json:"success_count"`       // 成功次数
	ErrorCount        int64         `json:"error_count"`         // 错误次数
	AverageExecTime   time.Duration `json:"average_exec_time"`   // 平均执行时间
	LastExecutionTime time.Time     `json:"last_execution_time"` // 最后执行时间
	ScriptReloads     int64         `json:"script_reloads"`      // 脚本重载次数
	MemoryUsage       int64         `json:"memory_usage"`        // 内存使用量
}

// NewJSEngine 创建新的JavaScript引擎
func NewJSEngine(config DataCleaningConfig) (*JSEngine, error) {
	engine := &JSEngine{
		config:     config,
		scriptFile: config.ScriptFile,
		stats:      JSEngineStats{},
	}

	// 初始化JavaScript虚拟机
	if err := engine.initVM(); err != nil {
		return nil, fmt.Errorf("failed to initialize JS engine: %w", err)
	}

	// 加载脚本
	if err := engine.loadScript(); err != nil {
		return nil, fmt.Errorf("failed to load script: %w", err)
	}

	return engine, nil
}

// initVM 初始化JavaScript虚拟机
func (js *JSEngine) initVM() error {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	// 创建新的JavaScript运行时
	js.vm = goja.New()

	// 设置内存限制
	if js.config.MaxScriptMemory > 0 {
		// goja目前不直接支持内存限制，但我们可以通过其他方式监控
		// 这里预留接口，未来可以扩展
	}

	// 注册全局函数和对象
	js.registerGlobals()

	return nil
}

// registerGlobals 注册全局函数和对象
func (js *JSEngine) registerGlobals() {
	// 注册console对象用于调试
	console := js.vm.NewObject()
	console.Set("log", func(args ...interface{}) {
		fmt.Printf("[JS] %v\n", args...)
	})
	console.Set("error", func(args ...interface{}) {
		fmt.Printf("[JS ERROR] %v\n", args...)
	})
	js.vm.Set("console", console)

	// 注册JSON对象
	jsonObj := js.vm.NewObject()
	jsonObj.Set("stringify", func(obj interface{}) string {
		data, err := json.Marshal(obj)
		if err != nil {
			return ""
		}
		return string(data)
	})
	jsonObj.Set("parse", func(str string) interface{} {
		var result interface{}
		if err := json.Unmarshal([]byte(str), &result); err != nil {
			return nil
		}
		return result
	})
	js.vm.Set("JSON", jsonObj)

	// 注册Date对象的扩展
	js.vm.RunString(`
		Date.prototype.toISOString = Date.prototype.toISOString || function() {
			return this.getUTCFullYear() + '-' +
				String(this.getUTCMonth() + 1).padStart(2, '0') + '-' +
				String(this.getUTCDate()).padStart(2, '0') + 'T' +
				String(this.getUTCHours()).padStart(2, '0') + ':' +
				String(this.getUTCMinutes()).padStart(2, '0') + ':' +
				String(this.getUTCSeconds()).padStart(2, '0') + '.' +
				String(this.getUTCMilliseconds()).padStart(3, '0') + 'Z';
		};
	`)
}

// loadScript 加载JavaScript脚本
func (js *JSEngine) loadScript() error {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	// 检查脚本文件是否存在
	fileInfo, err := os.Stat(js.scriptFile)
	if err != nil {
		return fmt.Errorf("script file not found: %w", err)
	}

	// 检查是否需要重新加载脚本
	if js.config.EnableCache && js.script != nil && fileInfo.ModTime().Equal(js.lastModTime) {
		return nil // 脚本未修改，使用缓存
	}

	// 读取脚本文件
	scriptContent, err := os.ReadFile(js.scriptFile)
	if err != nil {
		return fmt.Errorf("failed to read script file: %w", err)
	}

	// 编译脚本
	program, err := goja.Compile(js.scriptFile, string(scriptContent), false)
	if err != nil {
		return fmt.Errorf("failed to compile script: %w", err)
	}

	// 执行脚本以加载函数定义
	_, err = js.vm.RunProgram(program)
	if err != nil {
		return fmt.Errorf("failed to execute script: %w", err)
	}

	// 更新缓存信息
	js.script = program
	js.lastModTime = fileInfo.ModTime()
	js.stats.ScriptReloads++

	return nil
}

// CleanData 执行数据清洗
func (js *JSEngine) CleanData(deviceData *DeviceData) (*ProcessedData, error) {
	startTime := time.Now()

	// 更新统计信息
	defer func() {
		js.mutex.Lock()
		js.stats.TotalExecutions++
		js.stats.LastExecutionTime = time.Now()
		execTime := time.Since(startTime)

		// 计算平均执行时间
		if js.stats.TotalExecutions == 1 {
			js.stats.AverageExecTime = execTime
		} else {
			js.stats.AverageExecTime = time.Duration(
				(int64(js.stats.AverageExecTime)*js.stats.TotalExecutions + int64(execTime)) /
					(js.stats.TotalExecutions + 1),
			)
		}
		js.mutex.Unlock()
	}()

	// 检查并重新加载脚本（如果需要）
	if err := js.loadScript(); err != nil {
		js.stats.ErrorCount++
		return nil, fmt.Errorf("failed to reload script: %w", err)
	}

	// 创建执行上下文
	ctx, cancel := context.WithTimeout(context.Background(), js.config.ScriptTimeout)
	defer cancel()

	// 在goroutine中执行脚本，支持超时控制
	resultChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("script panic: %v", r)
			}
		}()

		js.mutex.RLock()
		defer js.mutex.RUnlock()

		// 获取cleanData函数
		cleanDataFunc, ok := goja.AssertFunction(js.vm.Get("cleanData"))
		if !ok {
			errorChan <- fmt.Errorf("cleanData function not found in script")
			return
		}

		// 将Go结构体转换为JavaScript对象
		jsData := js.vm.ToValue(map[string]interface{}{
			"device_id": deviceData.DeviceID,
			"data_type": deviceData.DataType,
			"timestamp": deviceData.Timestamp.Format(time.RFC3339),
			"raw_data":  deviceData.RawData,
			"location":  deviceData.Location,
			"sequence":  deviceData.Sequence,
			"metadata":  deviceData.Metadata, // 传递原始设备元数据
		})

		// 执行清洗函数
		result, err := cleanDataFunc(goja.Undefined(), jsData)
		if err != nil {
			errorChan <- fmt.Errorf("script execution error: %w", err)
			return
		}

		resultChan <- result.Export()
	}()

	// 等待执行完成或超时
	select {
	case result := <-resultChan:
		// 转换结果为ProcessedData
		processedData, err := js.convertResult(result, deviceData)
		if err != nil {
			js.stats.ErrorCount++
			return nil, fmt.Errorf("failed to convert result: %w", err)
		}

		js.stats.SuccessCount++
		return processedData, nil

	case err := <-errorChan:
		js.stats.ErrorCount++
		return nil, err

	case <-ctx.Done():
		js.stats.ErrorCount++
		return nil, fmt.Errorf("script execution timeout")
	}
}

// convertResult 转换JavaScript执行结果为ProcessedData
func (js *JSEngine) convertResult(result interface{}, originalData *DeviceData) (*ProcessedData, error) {
	// 将结果转换为map
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid result type: expected object, got %T", result)
	}

	// 创建ProcessedData对象
	processedData := &ProcessedData{
		DeviceID:    originalData.DeviceID,
		DataType:    originalData.DataType,
		Timestamp:   originalData.Timestamp,
		ProcessedAt: time.Now(),
		Sequence:    originalData.Sequence,
		Location:    originalData.Location,
		// 保留原始设备元数据，用于后续写入InfluxDB
		OriginalMetadata: originalData.Metadata,
	}

	// 解析结果字段
	if deviceID, ok := resultMap["device_id"].(string); ok {
		processedData.DeviceID = deviceID
	}

	if dataType, ok := resultMap["data_type"].(string); ok {
		processedData.DataType = dataType
	}

	if timestampStr, ok := resultMap["timestamp"].(string); ok {
		if ts, err := time.Parse(time.RFC3339, timestampStr); err == nil {
			processedData.Timestamp = ts
		}
	}

	if rawData, ok := resultMap["raw_data"].(map[string]interface{}); ok {
		processedData.RawData = rawData
	} else {
		processedData.RawData = originalData.RawData
	}

	if processedDataMap, ok := resultMap["processed_data"].(map[string]interface{}); ok {
		processedData.ProcessedData = processedDataMap
	}

	if qualityScore, ok := resultMap["quality_score"].(float64); ok {
		processedData.QualityScore = qualityScore
	}

	// 解析元数据
	if metadataMap, ok := resultMap["metadata"].(map[string]interface{}); ok {
		metadata := DataMetadata{}

		if version, ok := metadataMap["cleaning_version"].(string); ok {
			metadata.CleaningVersion = version
		}

		if rules, ok := metadataMap["cleaning_rules"].([]interface{}); ok {
			for _, rule := range rules {
				if ruleStr, ok := rule.(string); ok {
					metadata.CleaningRules = append(metadata.CleaningRules, ruleStr)
				}
			}
		}

		metadata.ProcessingTime = float64(time.Since(processedData.ProcessedAt)) / float64(time.Millisecond)
		metadata.Source = "javascript_engine"

		processedData.Metadata = metadata
	}

	// 检查是否有错误
	if errorFlag, ok := resultMap["error"].(bool); ok && errorFlag {
		processedData.Error = true
		if errorMsg, ok := resultMap["error_message"].(string); ok {
			processedData.ErrorMessage = errorMsg
		}
	}

	return processedData, nil
}

// GetStats 获取引擎统计信息
func (js *JSEngine) GetStats() JSEngineStats {
	js.mutex.RLock()
	defer js.mutex.RUnlock()
	return js.stats
}

// ReloadScript 强制重新加载脚本
func (js *JSEngine) ReloadScript() error {
	js.mutex.Lock()
	js.lastModTime = time.Time{} // 重置修改时间，强制重新加载
	js.mutex.Unlock()

	return js.loadScript()
}

// Close 关闭JavaScript引擎
func (js *JSEngine) Close() error {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	if js.vm != nil {
		// goja运行时会自动清理，这里主要是重置状态
		js.vm = nil
		js.script = nil
	}

	return nil
}

// ValidateScript 验证脚本是否有效
func (js *JSEngine) ValidateScript() error {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	if js.vm == nil {
		return fmt.Errorf("JavaScript engine not initialized")
	}

	// 检查必需的函数是否存在
	cleanDataFunc := js.vm.Get("cleanData")
	if cleanDataFunc == nil {
		return fmt.Errorf("cleanData function not found in script")
	}

	if _, ok := goja.AssertFunction(cleanDataFunc); !ok {
		return fmt.Errorf("cleanData is not a function")
	}

	return nil
}
