/**
 * 设备数据日志模块 - 05_data_push版本
 *
 * 功能概述：
 * 本模块负责记录设备数据处理过程中的原始数据和处理结果到专门的日志文件中，
 * 支持按设备分类记录，并应用日志轮转、压缩等管理功能，为数据分析和故障排查提供详细的数据记录
 *
 * 主要功能：
 * - 设备数据记录：记录每个设备处理的完整数据到日志文件
 * - 处理结果记录：记录数据清洗和推送的结果
 * - 分类存储：按设备ID或类型分类存储日志文件
 * - 日志轮转：应用配置的日志轮转、压缩、清理策略
 * - 性能优化：异步写入，不阻塞主数据处理流程
 * - 格式化输出：支持JSON和文本格式的日志输出
 *
 * 技术特性：
 * - 异步处理：使用goroutine和channel实现异步日志写入
 * - 批量处理：支持批量写入，提高I/O性能
 * - 内存控制：限制内存中的日志缓冲区大小
 * - 文件管理：自动创建目录和文件，支持文件轮转
 * - 错误处理：完善的错误处理和恢复机制
 *
 * 业务场景：
 * - 数据审计：记录所有设备数据处理过程用于审计和合规
 * - 故障排查：提供详细的设备数据处理历史用于故障分析
 * - 数据分析：为数据科学家提供处理后的数据源
 * - 系统监控：监控设备数据的处理质量和频率
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-08
 */
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"gopkg.in/natefinch/lumberjack.v2"
)

/**
 * 设备数据日志器结构体
 *
 * 功能：管理设备数据处理过程的日志记录和文件管理
 *
 * 架构设计：
 * - 异步处理：使用channel和goroutine实现异步日志写入
 * - 文件管理：为每个设备或设备类型创建独立的日志文件
 * - 性能优化：批量写入和缓冲机制
 * - 资源管理：自动清理和文件轮转
 *
 * @struct DeviceLogger
 */
type DeviceLogger struct {
	/** 服务配置信息，包含日志配置和轮转参数 */
	config *Config

	/** 全局上下文对象，用于控制goroutine生命周期 */
	ctx context.Context

	/** 上下文取消函数，用于优雅关闭日志器 */
	cancel context.CancelFunc

	/** 日志数据通道，用于异步传递需要记录的设备数据 */
	logChannel chan *DeviceLogEntry

	/** 日志写入器映射，按设备ID或类型管理不同的日志文件 */
	loggers map[string]*lumberjack.Logger

	/** 读写锁，保护loggers映射的并发访问 */
	mutex sync.RWMutex

	/** 等待组，用于等待所有goroutine完成 */
	wg sync.WaitGroup

	/** 是否启用设备日志记录 */
	enabled bool
}

// DeviceLogEntry 设备日志条目结构体
// 包含设备数据处理过程中的完整信息
type DeviceLogEntry struct {
	DeviceID       string                 `json:"device_id"`       // 设备ID
	DataType       string                 `json:"data_type"`       // 数据类型
	Timestamp      time.Time              `json:"timestamp"`       // 原始数据时间戳
	ProcessedAt    time.Time              `json:"processed_at"`    // 处理时间戳
	RawData        map[string]interface{} `json:"raw_data"`        // 原始数据
	CleanedData    map[string]interface{} `json:"cleaned_data"`    // 清洗后数据
	Metadata       map[string]interface{} `json:"metadata"`        // 设备元数据信息（品牌、型号、位置等）
	PushResult     string                 `json:"push_result"`     // 推送结果：success, failed, skipped
	ErrorMessage   string                 `json:"error_message"`   // 错误信息（如果有）
	ProcessingTime int64                  `json:"processing_time"` // 处理耗时（毫秒）
	LogTime        time.Time              `json:"log_time"`        // 日志记录时间
	LogSource      string                 `json:"log_source"`      // 日志来源
}

// NewDeviceLogger 创建设备数据日志器实例
// 参数:
//   - cfg: 服务配置
//
// 返回:
//   - *DeviceLogger: 日志器实例
func NewDeviceLogger(cfg *Config) *DeviceLogger {
	ctx, cancel := context.WithCancel(context.Background())

	return &DeviceLogger{
		config:     cfg,
		ctx:        ctx,
		cancel:     cancel,
		logChannel: make(chan *DeviceLogEntry, 1000), // 缓冲1000条日志
		loggers:    make(map[string]*lumberjack.Logger),
		mutex:      sync.RWMutex{},
		wg:         sync.WaitGroup{},
		enabled:    cfg.Logging.DeviceLogs,
	}
}

// Start 启动设备数据日志器
// 启动异步日志写入goroutine
func (dl *DeviceLogger) Start() {
	if !dl.enabled {
		fmt.Println("Device data logging is disabled")
		return
	}

	fmt.Println("Starting device data logger...")

	// 创建日志目录
	logDir := filepath.Dir(dl.config.Logging.Output)
	deviceLogDir := filepath.Join(logDir, "devices")
	if err := os.MkdirAll(deviceLogDir, 0755); err != nil {
		fmt.Printf("Failed to create device log directory: %v\n", err)
		return
	}

	// 启动日志写入任务
	dl.wg.Add(1)
	go dl.startLogWriter()

	fmt.Printf("Device data logger started, log directory: %s\n", deviceLogDir)
}

// Stop 停止设备数据日志器
// 优雅关闭所有goroutine并清理资源
func (dl *DeviceLogger) Stop() {
	if !dl.enabled {
		return
	}

	fmt.Println("Stopping device data logger...")

	// 关闭日志通道
	close(dl.logChannel)

	// 取消上下文，通知所有goroutine停止
	dl.cancel()

	// 等待所有goroutine完成
	dl.wg.Wait()

	// 关闭所有日志写入器
	dl.mutex.Lock()
	for _, logWriter := range dl.loggers {
		if logWriter != nil {
			logWriter.Close()
		}
	}
	dl.loggers = make(map[string]*lumberjack.Logger)
	dl.mutex.Unlock()

	fmt.Println("Device data logger stopped")
}

// LogDeviceData 记录设备数据处理过程
// 异步记录设备数据处理的完整过程到日志文件
// 参数:
//   - entry: 需要记录的设备日志条目
func (dl *DeviceLogger) LogDeviceData(entry *DeviceLogEntry) {
	if !dl.enabled || entry == nil {
		return
	}

	// 设置日志时间和来源
	entry.LogTime = time.Now()
	entry.LogSource = "data_push"

	// 非阻塞发送到日志通道
	select {
	case dl.logChannel <- entry:
		// 成功发送到通道
	default:
		// 通道满了，丢弃日志（避免阻塞主流程）
		fmt.Printf("Device log channel is full, dropping log for device %s\n", entry.DeviceID)
	}
}

// startLogWriter 启动日志写入任务
// 从通道中读取设备数据并写入到对应的日志文件
func (dl *DeviceLogger) startLogWriter() {
	defer dl.wg.Done()

	fmt.Println("Device log writer started")

	for {
		select {
		case entry, ok := <-dl.logChannel:
			if !ok {
				// 通道已关闭
				fmt.Println("Device log channel closed, stopping log writer")
				return
			}
			dl.writeDeviceLog(entry)

		case <-dl.ctx.Done():
			// 上下文取消
			fmt.Println("Device log writer stopped by context")
			return
		}
	}
}

// writeDeviceLog 写入设备数据日志
// 将设备数据处理信息写入到对应的日志文件中
// 参数:
//   - entry: 需要写入的设备日志条目
func (dl *DeviceLogger) writeDeviceLog(entry *DeviceLogEntry) {
	// 获取或创建设备对应的日志写入器
	logWriter := dl.getOrCreateLogger(entry.DeviceID)
	if logWriter == nil {
		fmt.Printf("Failed to get log writer for device %s\n", entry.DeviceID)
		return
	}

	// 序列化为JSON
	logData, err := json.Marshal(entry)
	if err != nil {
		fmt.Printf("Failed to marshal device log for %s: %v\n", entry.DeviceID, err)
		return
	}

	// 写入日志文件
	if _, err := logWriter.Write(append(logData, '\n')); err != nil {
		fmt.Printf("Failed to write device log for %s: %v\n", entry.DeviceID, err)
		return
	}

	fmt.Printf("Device log written for %s\n", entry.DeviceID)
}

// getOrCreateLogger 获取或创建设备对应的日志写入器
// 为每个设备创建独立的日志文件，应用轮转配置
// 参数:
//   - deviceID: 设备ID
//
// 返回:
//   - *lumberjack.Logger: 日志写入器实例
func (dl *DeviceLogger) getOrCreateLogger(deviceID string) *lumberjack.Logger {
	dl.mutex.RLock()
	if logWriter, exists := dl.loggers[deviceID]; exists {
		dl.mutex.RUnlock()
		return logWriter
	}
	dl.mutex.RUnlock()

	// 需要创建新的日志写入器
	dl.mutex.Lock()
	defer dl.mutex.Unlock()

	// 双重检查，防止并发创建
	if logWriter, exists := dl.loggers[deviceID]; exists {
		return logWriter
	}

	// 构造日志文件路径
	logDir := filepath.Dir(dl.config.Logging.Output)
	deviceLogDir := filepath.Join(logDir, "devices")
	logFilePath := filepath.Join(deviceLogDir, fmt.Sprintf("device_%s.log", deviceID))

	// 创建lumberjack日志写入器，应用轮转配置
	logWriter := &lumberjack.Logger{
		Filename:   logFilePath,                           // 日志文件路径
		MaxSize:    dl.config.Logging.Rotation.MaxSize,    // 单个文件最大大小(MB)
		MaxAge:     dl.config.Logging.Rotation.MaxAge,     // 文件保留天数
		MaxBackups: dl.config.Logging.Rotation.MaxBackups, // 保留的备份文件数量
		Compress:   dl.config.Logging.Rotation.Compress,   // 是否压缩旧文件
		LocalTime:  true,                                  // 使用本地时间
	}

	// 存储到映射中
	dl.loggers[deviceID] = logWriter

	fmt.Printf("Created device log writer for %s: %s\n", deviceID, logFilePath)
	return logWriter
}

// GetStats 获取设备日志统计信息
// 返回设备日志的运行状态和统计数据
// 返回:
//   - map[string]interface{}: 包含日志统计信息的map
func (dl *DeviceLogger) GetStats() map[string]interface{} {
	if !dl.enabled {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	dl.mutex.RLock()
	loggerCount := len(dl.loggers)
	dl.mutex.RUnlock()

	// 获取通道状态
	channelLen := len(dl.logChannel)
	channelCap := cap(dl.logChannel)

	return map[string]interface{}{
		"enabled":          true,
		"active_loggers":   loggerCount,
		"channel_length":   channelLen,
		"channel_capacity": channelCap,
		"channel_usage":    fmt.Sprintf("%.1f%%", float64(channelLen)/float64(channelCap)*100),
		"log_format":       dl.config.Logging.Format,
		"rotation_config": map[string]interface{}{
			"max_size":    dl.config.Logging.Rotation.MaxSize,
			"max_age":     dl.config.Logging.Rotation.MaxAge,
			"max_backups": dl.config.Logging.Rotation.MaxBackups,
			"compress":    dl.config.Logging.Rotation.Compress,
		},
	}
}

// CleanupOldLoggers 清理长时间未使用的日志写入器
// 定期清理不活跃的设备日志写入器，释放资源
func (dl *DeviceLogger) CleanupOldLoggers() {
	if !dl.enabled {
		return
	}

	dl.mutex.Lock()
	defer dl.mutex.Unlock()

	// 如果日志写入器数量不多，不需要清理
	if len(dl.loggers) <= 100 {
		return
	}

	fmt.Printf("Cleaning up old device loggers, current count: %d\n", len(dl.loggers))

	// 简单策略：保留最近的50个日志写入器，关闭其他的
	if len(dl.loggers) > 50 {
		count := 0
		newLoggers := make(map[string]*lumberjack.Logger)

		for deviceID, logWriter := range dl.loggers {
			if count < 50 {
				newLoggers[deviceID] = logWriter
				count++
			} else {
				// 关闭旧的日志写入器
				if logWriter != nil {
					logWriter.Close()
				}
			}
		}

		dl.loggers = newLoggers
		fmt.Printf("Device logger cleanup completed, remaining count: %d\n", len(dl.loggers))
	}
}
