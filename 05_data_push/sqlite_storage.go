/**
 * SQLite存储模块
 *
 * 功能概述：
 * 本模块提供SQLite本地存储功能，作为数据推送失败时的缓存和重试机制，
 * 支持按日期分文件存储、自动清理过期数据、FIFO顺序处理和批量操作
 *
 * 主要功能：
 * - 本地缓存：推送失败时的数据本地缓存
 * - 分文件存储：按日期分文件，便于管理和清理
 * - FIFO处理：严格按照先进先出顺序处理数据
 * - 批量操作：支持批量插入和删除，提高性能
 * - 自动清理：自动清理过期的数据库文件
 * - 重试机制：记录重试次数和错误信息
 * - 事务支持：使用事务确保数据一致性
 *
 * 技术特性：
 * - 连接池：SQLite连接池管理，支持并发访问
 * - WAL模式：Write-Ahead Logging模式，提高并发性能
 * - 索引优化：合理的索引设计，提高查询性能
 * - 事务处理：批量操作使用事务，确保原子性
 * - 错误恢复：完善的错误处理和重试机制
 *
 * 存储策略：
 * - 按日期分文件：每天一个数据库文件，便于管理
 * - 自动清理：根据配置的保留天数自动清理过期文件
 * - 状态管理：记录数据处理状态（pending, processing, failed）
 * - 重试控制：记录重试次数，避免无限重试
 *
 * 数据结构：
 * - device_data表：存储设备数据和处理状态
 * - 索引设计：device_id+sequence、status、timestamp等索引
 * - 分区策略：按日期分文件，避免单文件过大
 *
 * 性能优化：
 * - 连接复用：数据库连接池减少连接开销
 * - 批量操作：事务批量插入和删除
 * - 索引优化：合理的索引设计提高查询性能
 * - WAL模式：提高并发读写性能
 *
 * 业务价值：
 * - 数据可靠性：确保推送失败的数据不丢失
 * - 故障恢复：系统恢复后自动重试失败数据
 * - 性能保证：本地存储的高性能访问
 * - 运维友好：按日期分文件，便于备份和管理
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

/**
 * SQLite存储结构体
 *
 * 功能：提供SQLite本地存储功能，支持按日期分文件和多连接管理
 *
 * 架构设计：
 * - 多数据库管理：按日期维护多个SQLite数据库文件
 * - 连接池：每个数据库文件维护独立的连接池
 * - 并发安全：使用读写锁保护数据库连接映射
 * - 统计监控：实时统计操作性能和错误信息
 *
 * 核心职责：
 * - 数据缓存：推送失败时的数据本地缓存
 * - 连接管理：多个SQLite数据库的连接管理
 * - 文件管理：按日期分文件和自动清理
 * - 性能监控：统计操作性能和错误
 *
 * @struct SQLiteStorage
 */
type SQLiteStorage struct {
	/** SQLite配置信息，包含文件路径、连接参数等 */
	config SQLiteConfig

	/** 按日期缓存的数据库连接，key为日期字符串 */
	databases map[string]*sql.DB

	/** 读写锁，保护数据库连接映射的并发访问 */
	mutex sync.RWMutex

	/** SQLite操作统计信息，用于性能监控 */
	stats SQLiteStats
}

/**
 * SQLite统计信息结构体
 *
 * 功能：记录SQLite操作的详细统计信息，用于性能监控和问题诊断
 *
 * 统计维度：
 * - 操作计数：插入、查询、删除操作的次数统计
 * - 错误统计：错误次数和最后错误信息
 * - 连接统计：活跃连接数和数据库文件列表
 * - 性能指标：平均延迟和最后操作时间
 *
 * @struct SQLiteStats
 */
type SQLiteStats struct {
	/** 总插入操作次数，包括单次和批量插入 */
	TotalInserts int64 `json:"total_inserts"`

	/** 总查询操作次数，包括数据查询和统计查询 */
	TotalSelects int64 `json:"total_selects"`

	/** 总删除操作次数，包括单次和批量删除 */
	TotalDeletes int64 `json:"total_deletes"`

	/** 错误操作次数，用于计算错误率 */
	ErrorCount int64 `json:"error_count"`

	/** 最后一次错误的详细信息 */
	LastError string `json:"last_error"`

	/** 当前活跃的数据库连接数量 */
	ActiveConnections int `json:"active_connections"`

	/** 平均操作延迟，用于性能监控 */
	AverageLatency time.Duration `json:"average_latency"`

	/** 最后一次操作的时间戳 */
	LastOperationTime time.Time `json:"last_operation_time"`

	/** 当前管理的数据库文件列表 */
	DatabaseFiles []string `json:"database_files"`
}

// NewSQLiteStorage 创建新的SQLite存储
func NewSQLiteStorage(config SQLiteConfig) (*SQLiteStorage, error) {
	storage := &SQLiteStorage{
		config:    config,
		databases: make(map[string]*sql.DB),
		stats:     SQLiteStats{},
	}

	// 确保数据目录存在
	if err := os.MkdirAll(config.DataDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// 清理过期的数据库文件
	if err := storage.cleanupOldFiles(); err != nil {
		return nil, fmt.Errorf("failed to cleanup old files: %w", err)
	}

	return storage, nil
}

// getDatabase 获取指定日期的数据库连接
func (s *SQLiteStorage) getDatabase(date string) (*sql.DB, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查是否已有连接
	if db, exists := s.databases[date]; exists {
		return db, nil
	}

	// 创建新的数据库连接
	filename := fmt.Sprintf(s.config.FilePattern, date)
	dbPath := filepath.Join(s.config.DataDir, filename)

	// 构建连接字符串
	dsn := dbPath + "?"
	if s.config.WALMode {
		dsn += "journal_mode=WAL&"
	}
	dsn += "synchronous=" + s.config.SyncMode + "&"
	dsn += "cache=shared&"
	dsn += "_busy_timeout=30000"

	db, err := sql.Open("sqlite3", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(s.config.MaxConnections)
	db.SetMaxIdleConns(s.config.MaxConnections / 2)
	db.SetConnMaxLifetime(time.Hour)

	// 创建表结构
	if err := s.createTables(db); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to create tables: %w", err)
	}

	// 缓存连接
	s.databases[date] = db
	s.stats.ActiveConnections++

	return db, nil
}

// createTables 创建表结构
func (s *SQLiteStorage) createTables(db *sql.DB) error {
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS device_data (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		device_id TEXT NOT NULL,
		sequence TEXT NOT NULL,
		data_type TEXT NOT NULL,
		raw_data TEXT NOT NULL,
		processed_data TEXT,
		timestamp DATETIME NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		retry_count INTEGER DEFAULT 0,
		last_error TEXT,
		status TEXT DEFAULT 'pending'
	);

	CREATE INDEX IF NOT EXISTS idx_device_sequence ON device_data(device_id, sequence);
	CREATE INDEX IF NOT EXISTS idx_status ON device_data(status);
	CREATE INDEX IF NOT EXISTS idx_timestamp ON device_data(timestamp);
	CREATE INDEX IF NOT EXISTS idx_created_at ON device_data(created_at);
	`

	_, err := db.Exec(createTableSQL)
	return err
}

// SaveData 保存数据到SQLite
func (s *SQLiteStorage) SaveData(ctx context.Context, data *ProcessedData) error {
	startTime := time.Now()
	defer s.updateStats(startTime, nil)

	// 获取今天的数据库
	today := time.Now().Format("2006-01-02")
	db, err := s.getDatabase(today)
	if err != nil {
		s.updateStats(startTime, err)
		return fmt.Errorf("failed to get database: %w", err)
	}

	// 序列化数据
	rawDataJSON, err := data.ToJSON()
	if err != nil {
		s.updateStats(startTime, err)
		return fmt.Errorf("failed to serialize raw data: %w", err)
	}

	processedDataJSON := ""
	if data.ProcessedData != nil {
		if jsonData, err := data.ToJSON(); err == nil {
			processedDataJSON = jsonData
		}
	}

	// 插入数据
	insertSQL := `
	INSERT INTO device_data (device_id, sequence, data_type, raw_data, processed_data, timestamp, status)
	VALUES (?, ?, ?, ?, ?, ?, 'pending')
	`

	_, err = db.ExecContext(ctx, insertSQL,
		data.DeviceID,
		data.Sequence,
		data.DataType,
		rawDataJSON,
		processedDataJSON,
		data.Timestamp,
	)

	if err != nil {
		s.updateStats(startTime, err)
		return fmt.Errorf("failed to insert data: %w", err)
	}

	s.stats.TotalInserts++
	return nil
}

// BatchSaveData 批量保存数据
func (s *SQLiteStorage) BatchSaveData(ctx context.Context, dataList []*ProcessedData) error {
	if len(dataList) == 0 {
		return nil
	}

	startTime := time.Now()
	defer s.updateStats(startTime, nil)

	// 按日期分组数据
	dataByDate := make(map[string][]*ProcessedData)
	for _, data := range dataList {
		date := data.Timestamp.Format("2006-01-02")
		dataByDate[date] = append(dataByDate[date], data)
	}

	// 分别处理每个日期的数据
	for date, dateData := range dataByDate {
		db, err := s.getDatabase(date)
		if err != nil {
			s.updateStats(startTime, err)
			return fmt.Errorf("failed to get database for date %s: %w", date, err)
		}

		// 开始事务
		tx, err := db.BeginTx(ctx, nil)
		if err != nil {
			s.updateStats(startTime, err)
			return fmt.Errorf("failed to begin transaction: %w", err)
		}

		// 准备批量插入语句
		insertSQL := `
		INSERT INTO device_data (device_id, sequence, data_type, raw_data, processed_data, timestamp, status)
		VALUES (?, ?, ?, ?, ?, ?, 'pending')
		`
		stmt, err := tx.PrepareContext(ctx, insertSQL)
		if err != nil {
			tx.Rollback()
			s.updateStats(startTime, err)
			return fmt.Errorf("failed to prepare statement: %w", err)
		}

		// 批量插入
		for _, data := range dateData {
			rawDataJSON, _ := data.ToJSON()
			processedDataJSON := ""
			if data.ProcessedData != nil {
				if jsonData, err := data.ToJSON(); err == nil {
					processedDataJSON = jsonData
				}
			}

			_, err = stmt.ExecContext(ctx,
				data.DeviceID,
				data.Sequence,
				data.DataType,
				rawDataJSON,
				processedDataJSON,
				data.Timestamp,
			)

			if err != nil {
				stmt.Close()
				tx.Rollback()
				s.updateStats(startTime, err)
				return fmt.Errorf("failed to insert batch data: %w", err)
			}
		}

		stmt.Close()

		// 提交事务
		if err = tx.Commit(); err != nil {
			s.updateStats(startTime, err)
			return fmt.Errorf("failed to commit transaction: %w", err)
		}

		s.stats.TotalInserts += int64(len(dateData))
	}

	return nil
}

// GetPendingData 获取待处理的数据（按FIFO顺序）
func (s *SQLiteStorage) GetPendingData(ctx context.Context, deviceID string, limit int) ([]*SQLiteRecord, error) {
	startTime := time.Now()
	defer s.updateStats(startTime, nil)

	var allRecords []*SQLiteRecord

	// 获取所有数据库文件
	files, err := s.getDatabaseFiles()
	if err != nil {
		s.updateStats(startTime, err)
		return nil, fmt.Errorf("failed to get database files: %w", err)
	}

	// 从每个数据库文件中查询数据
	for _, file := range files {
		date := s.extractDateFromFilename(file)
		if date == "" {
			continue
		}

		db, err := s.getDatabase(date)
		if err != nil {
			continue // 跳过无法打开的数据库
		}

		querySQL := `
		SELECT id, device_id, sequence, data_type, raw_data, processed_data, 
		       timestamp, created_at, retry_count, last_error, status
		FROM device_data 
		WHERE device_id = ? AND status = 'pending'
		ORDER BY sequence ASC
		LIMIT ?
		`

		rows, err := db.QueryContext(ctx, querySQL, deviceID, limit)
		if err != nil {
			continue // 跳过查询失败的数据库
		}

		for rows.Next() {
			record := &SQLiteRecord{}
			err := rows.Scan(
				&record.ID,
				&record.DeviceID,
				&record.Sequence,
				&record.DataType,
				&record.RawData,
				&record.ProcessedData,
				&record.Timestamp,
				&record.CreatedAt,
				&record.RetryCount,
				&record.LastError,
				&record.Status,
			)
			if err != nil {
				continue
			}
			allRecords = append(allRecords, record)
		}
		rows.Close()

		// 如果已经获取足够的记录，停止查询
		if len(allRecords) >= limit {
			break
		}
	}

	// 按序列号排序确保FIFO顺序
	sort.Slice(allRecords, func(i, j int) bool {
		return s.compareSequence(allRecords[i].Sequence, allRecords[j].Sequence)
	})

	// 限制返回数量
	if len(allRecords) > limit {
		allRecords = allRecords[:limit]
	}

	s.stats.TotalSelects++
	return allRecords, nil
}

// compareSequence 比较序列号
func (s *SQLiteStorage) compareSequence(seq1, seq2 string) bool {
	// 尝试解析为数字进行比较
	if num1, err1 := strconv.ParseInt(seq1, 10, 64); err1 == nil {
		if num2, err2 := strconv.ParseInt(seq2, 10, 64); err2 == nil {
			return num1 < num2
		}
	}

	// 如果不是数字，按字符串比较
	return seq1 < seq2
}

// DeleteRecord 删除记录
func (s *SQLiteStorage) DeleteRecord(ctx context.Context, record *SQLiteRecord) error {
	startTime := time.Now()
	defer s.updateStats(startTime, nil)

	// 获取记录对应的数据库
	date := record.CreatedAt.Format("2006-01-02")
	db, err := s.getDatabase(date)
	if err != nil {
		s.updateStats(startTime, err)
		return fmt.Errorf("failed to get database: %w", err)
	}

	deleteSQL := "DELETE FROM device_data WHERE id = ?"
	_, err = db.ExecContext(ctx, deleteSQL, record.ID)
	if err != nil {
		s.updateStats(startTime, err)
		return fmt.Errorf("failed to delete record: %w", err)
	}

	s.stats.TotalDeletes++
	return nil
}

// BatchDeleteRecords 批量删除记录
func (s *SQLiteStorage) BatchDeleteRecords(ctx context.Context, records []*SQLiteRecord) error {
	if len(records) == 0 {
		return nil
	}

	startTime := time.Now()
	defer s.updateStats(startTime, nil)

	// 按日期分组记录
	recordsByDate := make(map[string][]*SQLiteRecord)
	for _, record := range records {
		date := record.CreatedAt.Format("2006-01-02")
		recordsByDate[date] = append(recordsByDate[date], record)
	}

	// 分别处理每个日期的记录
	for date, dateRecords := range recordsByDate {
		db, err := s.getDatabase(date)
		if err != nil {
			s.updateStats(startTime, err)
			return fmt.Errorf("failed to get database for date %s: %w", date, err)
		}

		// 构建批量删除SQL
		ids := make([]string, len(dateRecords))
		for i, record := range dateRecords {
			ids[i] = fmt.Sprintf("%d", record.ID)
		}

		deleteSQL := fmt.Sprintf("DELETE FROM device_data WHERE id IN (%s)", strings.Join(ids, ","))
		_, err = db.ExecContext(ctx, deleteSQL)
		if err != nil {
			s.updateStats(startTime, err)
			return fmt.Errorf("failed to batch delete records: %w", err)
		}

		s.stats.TotalDeletes += int64(len(dateRecords))
	}

	return nil
}

// UpdateRetryCount 更新重试次数
func (s *SQLiteStorage) UpdateRetryCount(ctx context.Context, record *SQLiteRecord, errorMsg string) error {
	startTime := time.Now()
	defer s.updateStats(startTime, nil)

	date := record.CreatedAt.Format("2006-01-02")
	db, err := s.getDatabase(date)
	if err != nil {
		s.updateStats(startTime, err)
		return fmt.Errorf("failed to get database: %w", err)
	}

	updateSQL := `
	UPDATE device_data 
	SET retry_count = retry_count + 1, last_error = ?, status = 'failed'
	WHERE id = ?
	`

	_, err = db.ExecContext(ctx, updateSQL, errorMsg, record.ID)
	if err != nil {
		s.updateStats(startTime, err)
		return fmt.Errorf("failed to update retry count: %w", err)
	}

	return nil
}

// getDatabaseFiles 获取所有数据库文件
func (s *SQLiteStorage) getDatabaseFiles() ([]string, error) {
	files, err := filepath.Glob(filepath.Join(s.config.DataDir, "*.db"))
	if err != nil {
		return nil, err
	}

	// 按文件名排序
	sort.Strings(files)
	return files, nil
}

// extractDateFromFilename 从文件名中提取日期
func (s *SQLiteStorage) extractDateFromFilename(filename string) string {
	base := filepath.Base(filename)
	// 假设文件名格式为 data_2006-01-02.db
	if strings.HasPrefix(base, "data_") && strings.HasSuffix(base, ".db") {
		return strings.TrimSuffix(strings.TrimPrefix(base, "data_"), ".db")
	}
	return ""
}

// cleanupOldFiles 清理过期的数据库文件
func (s *SQLiteStorage) cleanupOldFiles() error {
	files, err := s.getDatabaseFiles()
	if err != nil {
		return err
	}

	cutoffDate := time.Now().AddDate(0, 0, -s.config.RetentionDays)

	for _, file := range files {
		date := s.extractDateFromFilename(file)
		if date == "" {
			continue
		}

		fileDate, err := time.Parse("2006-01-02", date)
		if err != nil {
			continue
		}

		// 检查文件是否过期
		if fileDate.Before(cutoffDate) {
			// 检查文件是否为空
			if s.isDatabaseEmpty(file) {
				if err := os.Remove(file); err == nil {
					fmt.Printf("Removed empty database file: %s\n", file)
				}
			}
		}
	}

	return nil
}

// isDatabaseEmpty 检查数据库是否为空
func (s *SQLiteStorage) isDatabaseEmpty(filename string) bool {
	db, err := sql.Open("sqlite3", filename)
	if err != nil {
		return false
	}
	defer db.Close()

	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM device_data").Scan(&count)
	return err == nil && count == 0
}

// GetStats 获取统计信息
func (s *SQLiteStorage) GetStats() SQLiteStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 更新数据库文件列表
	if files, err := s.getDatabaseFiles(); err == nil {
		s.stats.DatabaseFiles = files
	}

	return s.stats
}

// updateStats 更新统计信息
func (s *SQLiteStorage) updateStats(startTime time.Time, err error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	latency := time.Since(startTime)
	s.stats.LastOperationTime = time.Now()

	// 更新平均延迟
	if s.stats.AverageLatency == 0 {
		s.stats.AverageLatency = latency
	} else {
		s.stats.AverageLatency = (s.stats.AverageLatency + latency) / 2
	}

	// 更新错误信息
	if err != nil {
		s.stats.ErrorCount++
		s.stats.LastError = err.Error()
	}
}

// Close 关闭所有数据库连接
func (s *SQLiteStorage) Close() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for date, db := range s.databases {
		if err := db.Close(); err != nil {
			fmt.Printf("Error closing database %s: %v\n", date, err)
		}
	}

	s.databases = make(map[string]*sql.DB)
	s.stats.ActiveConnections = 0
	return nil
}
