/**
 * 数据处理推送服务主程序
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的核心数据处理组件，负责从Redis读取原始设备数据，
 * 通过JavaScript脚本进行数据清洗和转换，然后推送到下游系统（NATS JetStream或InfluxDB），
 * 实现高性能、高可靠的数据处理管道
 *
 * 核心功能：
 * - 数据读取：从Redis按FIFO顺序读取设备数据，保证处理顺序
 * - 数据清洗：使用可配置的JavaScript脚本进行数据清洗和转换
 * - 数据推送：支持多种推送目标（NATS JetStream、InfluxDB）
 * - 故障恢复：推送失败时保存到SQLite本地缓存，支持重试机制
 * - 并行处理：支持多设备并行处理，提高吞吐量
 * - 监控告警：完善的健康检查、统计信息和性能监控
 * - 优雅关闭：支持优雅关闭，确保数据不丢失
 *
 * 技术特性：
 * - 高性能：多goroutine并行处理，支持高并发数据处理
 * - 高可靠：多重故障保护机制，确保数据不丢失
 * - 可扩展：插件化的推送器设计，易于扩展新的推送目标
 * - 可配置：丰富的配置选项，支持不同环境和需求
 * - 可观测：详细的日志记录、指标监控和健康检查
 *
 * 架构设计：
 * - 分层架构：数据读取 -> 数据清洗 -> 数据推送的清晰分层
 * - 插件模式：可插拔的推送器和清洗脚本
 * - 工厂模式：推送器工厂，支持多种推送目标
 * - 管理器模式：设备处理器管理器，统一管理多个处理器
 * - 服务模式：健康检查服务，提供监控和管理接口
 *
 * 数据流程：
 * 1. Redis数据读取：按设备ID从Redis队列读取原始数据
 * 2. JavaScript清洗：使用V8引擎执行清洗脚本
 * 3. 数据验证：验证清洗后数据的格式和完整性
 * 4. 数据推送：推送到配置的目标系统
 * 5. 故障处理：推送失败时保存到SQLite，等待重试
 * 6. 重试机制：优先处理SQLite中的失败数据
 *
 * 性能优化：
 * - 连接池：Redis、SQLite、NATS连接池复用
 * - 批量操作：支持批量读取和推送，提高效率
 * - 内存管理：智能的内存使用和垃圾回收
 * - 异步处理：非阻塞的数据处理流程
 *
 * 业务场景：
 * - 设备数据处理：工业设备数据的实时清洗和转换
 * - 数据质量保证：通过清洗脚本确保数据质量
 * - 系统集成：与MES、ERP等上层系统的数据集成
 * - 数据分析：为数据分析和报表提供清洗后的数据
 *
 * 部署特性：
 * - 容器化：支持Docker容器化部署
 * - 配置外部化：配置文件外挂，支持不同环境
 * - 日志轮转：自动日志轮转，防止磁盘空间耗尽
 * - 健康检查：支持K8s健康检查和负载均衡
 *
 * 使用方法：
 * ```bash
 * # 开发环境运行
 * go run . -config config.yml
 *
 * # 生产环境编译运行
 * go build -o data-push .
 * ./data-push -config config_influxdb_real.yml
 *
 * # Docker容器运行
 * docker run -v /path/to/config:/app/config data-push -config config/config.yml
 * ```
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"shared/logger"
)

/**
 * 服务版本信息常量
 *
 * 用途：定义服务的基本信息，用于日志记录、监控和版本管理
 */
const (
	/** 服务名称，用于日志标识和监控指标 */
	ServiceName = "data-push"

	/** 服务版本号，遵循语义化版本规范 */
	ServiceVersion = "1.0.0"
)

/**
 * 全局变量定义
 *
 * 说明：这些全局变量在服务启动时初始化，在整个服务生命周期中使用
 * 采用全局变量的原因：
 * - 简化组件间的依赖传递
 * - 便于统一的资源管理和清理
 * - 提高代码的可读性和维护性
 */
var (
	/** 全局配置对象，包含所有服务配置信息 */
	config *Config

	/** Redis客户端，用于读取设备数据 */
	redisClient *RedisClient

	/** SQLite存储，用于本地缓存和故障恢复 */
	sqliteStorage *SQLiteStorage

	/** 数据推送器接口，支持多种推送目标 */
	dataPusher DataPusher

	/** 推送器工厂，用于创建不同类型的推送器 */
	pusherFactory *DataPusherFactory

	/** JavaScript引擎，用于执行数据清洗脚本 */
	jsEngine *JSEngine

	/** 设备处理器管理器，管理多个设备的并行处理 */
	processorManager *DeviceProcessorManager

	/** 健康检查服务，提供监控和管理接口 */
	healthService *HealthService

	/** 设备数据日志器，用于记录设备数据处理过程 */
	deviceLogger *DeviceLogger

	/** 设备离线监控器，用于监控设备状态和管理processor */
	deviceMonitor *DeviceMonitor

	/** 服务启动时间，用于计算运行时长 */
	startTime time.Time
)

/**
 * 主函数 - 数据处理推送服务入口点
 *
 * 功能：负责服务的完整生命周期管理，包括初始化、启动、运行和关闭
 *
 * 启动流程：
 * 1. 参数解析：解析命令行参数，获取配置文件路径
 * 2. 配置加载：加载并验证配置文件
 * 3. 日志初始化：初始化日志系统和轮转配置
 * 4. 组件初始化：初始化所有核心组件
 * 5. HTTP服务启动：启动健康检查和管理接口
 * 6. 处理器启动：启动设备数据处理器
 * 7. 信号等待：等待关闭信号并执行优雅关闭
 *
 * 错误处理：
 * - 任何关键组件初始化失败都会导致服务退出
 * - 详细的错误日志记录，便于问题诊断
 * - 优雅的错误处理，避免数据丢失
 *
 * 监控集成：
 * - 启动时间记录，用于计算服务运行时长
 * - 详细的启动日志，便于运维监控
 * - 组件状态跟踪，便于健康检查
 *
 * @example
 * // 使用默认配置启动
 * go run .
 *
 * // 使用指定配置启动
 * go run . -config config.yml
 */
func main() {
	// 记录启动时间
	startTime = time.Now()

	// 解析命令行参数
	configFile := flag.String("config", "config.yml", "配置文件路径")
	flag.Parse()

	fmt.Printf("🚀 %s v%s 启动中...\n", ServiceName, ServiceVersion)
	fmt.Printf("📄 配置文件: %s\n", *configFile)

	// 第一步：加载配置文件
	var err error
	config, err = LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("❌ 配置加载失败: %v", err)
	}
	fmt.Printf("✅ 配置加载成功\n")

	// 第二步：初始化日志系统
	logger.InitLoggerWithRotation(
		config.Logging.Level,
		config.Logging.Format,
		config.Logging.Output,
		logger.LogRotationConfig{
			Enabled:    config.Logging.Rotation.Enabled,
			MaxSize:    config.Logging.Rotation.MaxSize,
			MaxAge:     config.Logging.Rotation.MaxAge,
			MaxBackups: config.Logging.Rotation.MaxBackups,
			Compress:   config.Logging.Rotation.Compress,
		},
	)
	logger.Infof("🎯 服务启动: %s v%s", ServiceName, ServiceVersion)

	// 第三步：初始化各个组件
	if err := initializeComponents(); err != nil {
		logger.Fatalf("❌ 组件初始化失败: %v", err)
	}

	// 第四步：启动HTTP服务器
	httpServer := startHTTPServer()

	// 第五步：启动处理器管理器
	if err := processorManager.Start(); err != nil {
		logger.Fatalf("❌ 处理器管理器启动失败: %v", err)
	}

	logger.Infof("✅ %s 服务启动完成，监听端口: %d", ServiceName, config.Service.Port)

	// 第六步：等待中断信号
	waitForShutdown(httpServer)

	logger.Infof("👋 %s 服务已退出", ServiceName)
}

// initializeComponents 初始化所有组件
func initializeComponents() error {
	logger.Infof("🔧 初始化组件...")

	// 初始化Redis客户端
	var err error
	redisClient, err = NewRedisClient(config.Redis)
	if err != nil {
		// Redis连接失败时提供详细的错误信息
		logger.Errorf("❌ Redis客户端初始化失败")
		logger.Errorf("错误详情: %v", err)
		logger.Errorf("")
		logger.Errorf("💡 快速解决方案:")
		logger.Errorf("   1. 检查Redis服务: docker ps | grep redis")
		logger.Errorf("   2. 启动Redis服务: docker-compose up -d redis")
		logger.Errorf("   3. 测试连接: redis-cli -h %s -p %d ping", config.Redis.Host, config.Redis.Port)
		logger.Errorf("")
		return fmt.Errorf("Redis客户端初始化失败: %w", err)
	}
	logger.Infof("✅ Redis客户端初始化成功 (地址: %s:%d, 数据库: %d)",
		config.Redis.Host, config.Redis.Port, config.Redis.DB)

	// 初始化SQLite存储
	sqliteStorage, err = NewSQLiteStorage(config.SQLite)
	if err != nil {
		return fmt.Errorf("SQLite存储初始化失败: %w", err)
	}
	logger.Infof("✅ SQLite存储初始化成功")

	// 初始化数据推送器工厂
	pusherFactory = NewDataPusherFactory(config)

	// 根据配置创建数据推送器
	pusherType := PusherType(config.DataPush.Type)
	dataPusher, err = pusherFactory.CreatePusher(pusherType)
	if err != nil {
		return fmt.Errorf("数据推送器初始化失败: %w", err)
	}

	// 初始化推送器
	if err = dataPusher.Initialize(); err != nil {
		return fmt.Errorf("数据推送器初始化失败: %w", err)
	}
	logger.Infof("✅ 数据推送器初始化成功 (类型: %s)", pusherType)

	// 初始化JavaScript引擎
	jsEngine, err = NewJSEngine(config.DataCleaning)
	if err != nil {
		return fmt.Errorf("JavaScript引擎初始化失败: %w", err)
	}
	logger.Infof("✅ JavaScript引擎初始化成功")

	// 初始化设备数据日志器
	deviceLogger = NewDeviceLogger(config)
	deviceLogger.Start()
	logger.Infof("✅ 设备数据日志器初始化成功")

	// 初始化处理器管理器
	processorManager = NewDeviceProcessorManager(config, redisClient, sqliteStorage, dataPusher, jsEngine)
	logger.Infof("✅ 处理器管理器初始化成功")

	// 设置设备日志器和设备监控器到处理器管理器
	processorManager.SetDeviceLogger(deviceLogger)
	logger.Infof("✅ 处理器管理器设备日志器设置成功")

	// 初始化设备离线监控器
	deviceMonitor = NewDeviceMonitor(config, redisClient, processorManager)
	deviceMonitor.Start()
	logger.Infof("✅ 设备离线监控器初始化成功")

	// 设置设备监控器到处理器管理器
	processorManager.SetDeviceMonitor(deviceMonitor)
	logger.Infof("✅ 处理器管理器设备监控器设置成功")

	// 初始化健康检查服务
	healthService = NewHealthService(config, redisClient, sqliteStorage, dataPusher, jsEngine, processorManager, deviceLogger, deviceMonitor)
	logger.Infof("✅ 健康检查服务初始化成功")

	return nil
}

// startHTTPServer 启动HTTP服务器
func startHTTPServer() *http.Server {
	mux := http.NewServeMux()

	// 健康检查端点
	mux.HandleFunc("/health", healthService.HealthCheckHandler)
	mux.HandleFunc("/health/detailed", healthService.DetailedHealthHandler)

	// 统计信息端点
	mux.HandleFunc("/stats", healthService.StatsHandler)
	mux.HandleFunc("/stats/processors", healthService.ProcessorStatsHandler)

	// 管理端点
	mux.HandleFunc("/admin/reload-script", healthService.ReloadScriptHandler)
	mux.HandleFunc("/admin/flush-device", healthService.FlushDeviceHandler)

	// 指标端点（如果启用）
	if config.Monitoring.EnableMetrics {
		mux.HandleFunc("/metrics", healthService.MetricsHandler)
	}

	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", config.Service.Host, config.Service.Port),
		Handler:      mux,
		ReadTimeout:  config.Service.Timeout,
		WriteTimeout: config.Service.Timeout,
		IdleTimeout:  config.Service.Timeout * 2,
	}

	go func() {
		logger.Infof("🌐 HTTP服务器启动，地址: %s", server.Addr)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Errorf("HTTP服务器启动失败: %v", err)
		}
	}()

	return server
}

// waitForShutdown 等待关闭信号
func waitForShutdown(httpServer *http.Server) {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	sig := <-sigChan
	logger.Infof("🛑 接收到信号: %v，开始优雅关闭...", sig)

	// 创建关闭上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 停止处理器管理器
	if processorManager != nil {
		logger.Infof("🛑 停止处理器管理器...")
		if err := processorManager.Stop(); err != nil {
			logger.Errorf("处理器管理器停止失败: %v", err)
		} else {
			logger.Infof("✅ 处理器管理器已停止")
		}
	}

	// 关闭HTTP服务器
	if httpServer != nil {
		logger.Infof("🛑 停止HTTP服务器...")
		if err := httpServer.Shutdown(ctx); err != nil {
			logger.Errorf("HTTP服务器关闭失败: %v", err)
		} else {
			logger.Infof("✅ HTTP服务器已关闭")
		}
	}

	// 关闭各个组件
	closeComponents()

	// 输出运行统计
	uptime := time.Since(startTime)
	logger.Infof("📊 服务运行时间: %v", uptime)

	// 输出内存统计
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	logger.Infof("📊 内存使用: %.2f MB", float64(m.Alloc)/1024/1024)
}

// closeComponents 关闭所有组件
func closeComponents() {
	logger.Infof("🛑 关闭组件...")

	// 关闭设备离线监控器
	if deviceMonitor != nil {
		deviceMonitor.Stop()
		logger.Infof("✅ 设备离线监控器已关闭")
	}

	// 关闭设备数据日志器
	if deviceLogger != nil {
		deviceLogger.Stop()
		logger.Infof("✅ 设备数据日志器已关闭")
	}

	// 关闭数据推送器
	if dataPusher != nil {
		if err := dataPusher.Close(); err != nil {
			logger.Errorf("数据推送器关闭失败: %v", err)
		} else {
			logger.Infof("✅ 数据推送器已关闭")
		}
	}

	// 关闭推送器工厂
	if pusherFactory != nil {
		if err := pusherFactory.Close(); err != nil {
			logger.Errorf("推送器工厂关闭失败: %v", err)
		} else {
			logger.Infof("✅ 推送器工厂已关闭")
		}
	}

	// 关闭JavaScript引擎
	if jsEngine != nil {
		if err := jsEngine.Close(); err != nil {
			logger.Errorf("JavaScript引擎关闭失败: %v", err)
		} else {
			logger.Infof("✅ JavaScript引擎已关闭")
		}
	}

	// 关闭SQLite存储
	if sqliteStorage != nil {
		if err := sqliteStorage.Close(); err != nil {
			logger.Errorf("SQLite存储关闭失败: %v", err)
		} else {
			logger.Infof("✅ SQLite存储已关闭")
		}
	}

	// 关闭Redis客户端
	if redisClient != nil {
		if err := redisClient.Close(); err != nil {
			logger.Errorf("Redis客户端关闭失败: %v", err)
		} else {
			logger.Infof("✅ Redis客户端已关闭")
		}
	}

	logger.Infof("✅ 所有组件已关闭")
}
