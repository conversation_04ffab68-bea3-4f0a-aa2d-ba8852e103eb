/**
 * 数据处理推送服务配置模块
 *
 * 功能概述：
 * 本模块定义了数据处理推送服务的完整配置结构体和配置管理功能，
 * 支持YAML格式的配置文件，提供配置验证、默认值设置等功能
 *
 * 主要功能：
 * - 配置结构定义：定义所有组件的配置结构体
 * - 配置文件加载：支持YAML格式配置文件的加载和解析
 * - 配置验证：验证配置参数的合理性和完整性
 * - 默认值设置：为未配置的参数设置合理的默认值
 * - 类型安全：使用强类型确保配置的正确性
 *
 * 配置分类：
 * - 服务配置：基本服务信息和运行参数
 * - 存储配置：Redis、SQLite、InfluxDB等存储系统配置
 * - 推送配置：NATS JetStream等消息推送配置
 * - 性能配置：性能调优和资源限制配置
 * - 监控配置：健康检查和指标监控配置
 * - 日志配置：日志级别、格式和轮转配置
 * - 稳定性配置：内存限制、熔断器等稳定性配置
 *
 * 设计特性：
 * - 模块化：按功能模块组织配置结构
 * - 可扩展：易于添加新的配置项和模块
 * - 向后兼容：支持配置的向后兼容性
 * - 环境适配：支持不同环境的配置差异
 *
 * 业务价值：
 * - 灵活配置：支持不同环境和需求的灵活配置
 * - 运维友好：清晰的配置结构，便于运维管理
 * - 错误预防：配置验证机制，预防配置错误
 * - 性能优化：丰富的性能配置选项
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v2"
)

/**
 * 主配置结构体
 *
 * 功能：作为整个服务的配置根节点，包含所有子模块的配置信息
 *
 * 设计原则：
 * - 模块化：按功能模块组织配置，便于管理和维护
 * - 层次化：清晰的配置层次结构，避免配置冲突
 * - 完整性：覆盖服务运行所需的所有配置项
 * - 可读性：使用有意义的字段名和YAML标签
 *
 * 配置模块说明：
 * - Service：基础服务配置，包括端口、超时等
 * - Redis：Redis连接和性能配置
 * - SQLite：本地存储和缓存配置
 * - NATS：消息队列连接和推送配置
 * - InfluxDB：时序数据库连接和写入配置
 * - DataPush：数据推送策略和目标配置
 * - DataCleaning：数据清洗脚本和执行配置
 * - Performance：性能调优和资源限制配置
 * - Monitoring：监控、告警和健康检查配置
 * - Logging：日志级别、格式和轮转配置
 * - Stability：稳定性保障和熔断器配置
 *
 * @struct Config
 */
type Config struct {
	/** 基础服务配置：端口、超时、工作线程等 */
	Service ServiceConfig `yaml:"service"`

	/** Redis配置：连接信息、连接池、重试策略 */
	Redis RedisConfig `yaml:"redis"`

	/** SQLite配置：本地存储、缓存策略、文件管理 */
	SQLite SQLiteConfig `yaml:"sqlite"`

	/** NATS配置：消息队列连接、主题、流配置 */
	NATS NATSConfig `yaml:"nats"`

	/** InfluxDB配置：时序数据库连接、写入策略 */
	InfluxDB InfluxDBConfig `yaml:"influxdb"`

	/** 数据推送配置：推送策略、目标选择、故障转移 */
	DataPush DataPushConfig `yaml:"data_push"`

	/** 数据清洗配置：脚本文件、执行环境、性能限制 */
	DataCleaning DataCleaningConfig `yaml:"data_cleaning"`

	/** 性能配置：资源限制、批量大小、优化参数 */
	Performance PerformanceConfig `yaml:"performance"`

	/** 监控配置：健康检查、指标收集、告警阈值 */
	Monitoring MonitoringConfig `yaml:"monitoring"`

	/** 日志配置：级别、格式、轮转策略 */
	Logging LoggingConfig `yaml:"logging"`

	/** 稳定性配置：内存限制、熔断器、故障恢复 */
	Stability StabilityConfig `yaml:"stability"`

	/** 设备离线监控配置：设备状态监控、离线检测和processor管理 */
	DeviceMonitor DeviceMonitorConfig `yaml:"device_monitor"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Name            string        `yaml:"name"`             // 服务名称
	Host            string        `yaml:"host"`             // 监听地址
	Port            int           `yaml:"port"`             // 监听端口
	Debug           bool          `yaml:"debug"`            // 调试模式
	Timeout         time.Duration `yaml:"timeout"`          // 超时时间
	MaxWorkers      int           `yaml:"max_workers"`      // 最大工作协程数
	BatchSize       int           `yaml:"batch_size"`       // 批量处理大小
	ProcessInterval time.Duration `yaml:"process_interval"` // 处理间隔
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host       string        `yaml:"host"`        // Redis主机
	Port       int           `yaml:"port"`        // Redis端口
	Password   string        `yaml:"password"`    // Redis密码
	DB         int           `yaml:"db"`          // 数据库编号
	PoolSize   int           `yaml:"pool_size"`   // 连接池大小
	Timeout    time.Duration `yaml:"timeout"`     // 超时时间
	RetryCount int           `yaml:"retry_count"` // 重试次数
	RetryDelay time.Duration `yaml:"retry_delay"` // 重试延迟
}

// SQLiteConfig SQLite配置
type SQLiteConfig struct {
	DataDir        string        `yaml:"data_dir"`        // 数据目录
	FilePattern    string        `yaml:"file_pattern"`    // 文件命名模式
	RetentionDays  int           `yaml:"retention_days"`  // 保留天数
	BatchSize      int           `yaml:"batch_size"`      // 批量大小
	FlushInterval  time.Duration `yaml:"flush_interval"`  // 刷新间隔
	MaxConnections int           `yaml:"max_connections"` // 最大连接数
	WALMode        bool          `yaml:"wal_mode"`        // WAL模式
	SyncMode       string        `yaml:"sync_mode"`       // 同步模式
}

// NATSConfig NATS配置
type NATSConfig struct {
	URL        string        `yaml:"url"`         // NATS服务器地址
	Subject    string        `yaml:"subject"`     // 主题
	Stream     string        `yaml:"stream"`      // 流名称
	Consumer   string        `yaml:"consumer"`    // 消费者名称
	BatchSize  int           `yaml:"batch_size"`  // 批量大小
	Timeout    time.Duration `yaml:"timeout"`     // 超时时间
	RetryCount int           `yaml:"retry_count"` // 重试次数
	RetryDelay time.Duration `yaml:"retry_delay"` // 重试延迟
	MaxPending int           `yaml:"max_pending"` // 最大待确认消息数
}

// DataPushConfig 数据推送配置
type DataPushConfig struct {
	Type     string               `yaml:"type"`     // 推送类型: nats, influxdb, mixed
	NATS     DataPushTargetConfig `yaml:"nats"`     // NATS推送配置
	InfluxDB DataPushTargetConfig `yaml:"influxdb"` // InfluxDB推送配置
}

// DataPushTargetConfig 推送目标配置
type DataPushTargetConfig struct {
	Enabled  bool `yaml:"enabled"`  // 是否启用
	Primary  bool `yaml:"primary"`  // 是否为主要目标
	Fallback bool `yaml:"fallback"` // 是否为备用目标
}

// DataCleaningConfig 数据清洗配置
type DataCleaningConfig struct {
	ScriptFile      string        `yaml:"script_file"`       // 脚本文件路径
	ScriptTimeout   time.Duration `yaml:"script_timeout"`    // 脚本执行超时
	EnableCache     bool          `yaml:"enable_cache"`      // 启用脚本缓存
	MaxScriptMemory int           `yaml:"max_script_memory"` // 脚本最大内存(MB)
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	RedisScanCount      int           `yaml:"redis_scan_count"`      // Redis扫描批次大小
	SQLiteRetryInterval time.Duration `yaml:"sqlite_retry_interval"` // SQLite重试间隔
	NATSRetryInterval   time.Duration `yaml:"nats_retry_interval"`   // NATS重试间隔
	MemoryLimitMB       int           `yaml:"memory_limit_mb"`       // 内存限制
	GCInterval          time.Duration `yaml:"gc_interval"`           // GC间隔
	MetricsInterval     time.Duration `yaml:"metrics_interval"`      // 指标统计间隔
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	EnableMetrics       bool                  `yaml:"enable_metrics"`        // 启用指标收集
	MetricsPort         int                   `yaml:"metrics_port"`          // 指标端口
	HealthCheckInterval time.Duration         `yaml:"health_check_interval"` // 健康检查间隔
	AlertThresholds     AlertThresholdsConfig `yaml:"alert_thresholds"`      // 告警阈值
}

// AlertThresholdsConfig 告警阈值配置
type AlertThresholdsConfig struct {
	RedisQueueSize     int `yaml:"redis_queue_size"`     // Redis队列大小告警阈值
	SQLiteFileSizeMB   int `yaml:"sqlite_file_size_mb"`  // SQLite文件大小告警阈值
	ErrorRatePercent   int `yaml:"error_rate_percent"`   // 错误率告警阈值
	MemoryUsagePercent int `yaml:"memory_usage_percent"` // 内存使用率告警阈值
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string            `yaml:"level"`       // 日志级别
	Format     string            `yaml:"format"`      // 日志格式
	Output     string            `yaml:"output"`      // 输出目标
	Rotation   LogRotationConfig `yaml:"rotation"`    // 日志轮转配置
	DeviceLogs bool              `yaml:"device_logs"` // 是否记录设备数据日志，true时记录每个设备推送的数据到单独的日志文件
}

// LogRotationConfig 日志轮转配置
type LogRotationConfig struct {
	Enabled    bool `yaml:"enabled"`     // 是否启用轮转
	MaxSize    int  `yaml:"max_size"`    // 最大文件大小(MB)
	MaxAge     int  `yaml:"max_age"`     // 保留天数
	MaxBackups int  `yaml:"max_backups"` // 备份文件数量
	Compress   bool `yaml:"compress"`    // 是否压缩
}

// StabilityConfig 稳定性配置
type StabilityConfig struct {
	MaxMemoryMB    uint64               `yaml:"max_memory_mb"`   // 最大内存限制(MB)
	GCThresholdMB  uint64               `yaml:"gc_threshold_mb"` // GC触发阈值(MB)
	CircuitBreaker CircuitBreakerConfig `yaml:"circuit_breaker"` // 熔断器配置
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	Enabled          bool          `yaml:"enabled"`            // 是否启用熔断器
	FailureThreshold int           `yaml:"failure_threshold"`  // 失败阈值
	RecoveryTimeout  time.Duration `yaml:"recovery_timeout"`   // 恢复超时
	HalfOpenRequests int           `yaml:"half_open_requests"` // 半开状态请求数
}

// DeviceMonitorConfig 设备离线监控配置结构体
// 用于配置设备数据处理状态监控、离线检测和processor管理机制
// 支持根据业务需求调整监控策略和超时参数
type DeviceMonitorConfig struct {
	Enabled                bool   `yaml:"enabled"`                  // 是否启用设备离线检测功能，默认true
	CheckInterval          int    `yaml:"check_interval"`           // 检查间隔(秒)，每隔多少秒检查一次设备状态，默认60秒
	OfflineTimeout         int    `yaml:"offline_timeout"`          // 离线超时(秒)，超过多少秒没有数据则判定为离线，默认300秒
	RedisKeyPrefix         string `yaml:"redis_key_prefix"`         // Redis中存储设备最后处理时间的key前缀，默认"device:last_processed:"
	CleanupInterval        int    `yaml:"cleanup_interval"`         // 清理间隔(秒)，每隔多少秒清理过期的设备状态记录，默认3600秒
	MaxOfflineDevices      int    `yaml:"max_offline_devices"`      // 最大离线设备数量，防止内存泄露，默认1000
	ProcessorShutdownDelay int    `yaml:"processor_shutdown_delay"` // 处理器关闭延迟(秒)，设备离线后延迟关闭processor，默认30秒
}

// LoadConfig 加载配置文件
func LoadConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	// 设置默认值
	setDefaultValues(&config)

	return &config, nil
}

// validateConfig 验证配置的合理性
func validateConfig(config *Config) error {
	if config.Service.Name == "" {
		return fmt.Errorf("service name is required")
	}

	if config.Service.Port <= 0 || config.Service.Port > 65535 {
		return fmt.Errorf("invalid service port: %d", config.Service.Port)
	}

	if config.Redis.Host == "" {
		return fmt.Errorf("redis host is required")
	}

	if config.SQLite.DataDir == "" {
		return fmt.Errorf("sqlite data directory is required")
	}

	if config.NATS.URL == "" {
		return fmt.Errorf("nats URL is required")
	}

	if config.DataCleaning.ScriptFile == "" {
		return fmt.Errorf("data cleaning script file is required")
	}

	return nil
}

// setDefaultValues 设置默认值
func setDefaultValues(config *Config) {
	// 服务默认值
	if config.Service.MaxWorkers <= 0 {
		config.Service.MaxWorkers = 10
	}
	if config.Service.BatchSize <= 0 {
		config.Service.BatchSize = 100
	}
	if config.Service.ProcessInterval == 0 {
		config.Service.ProcessInterval = time.Second
	}

	// Redis默认值
	if config.Redis.PoolSize <= 0 {
		config.Redis.PoolSize = 20
	}
	if config.Redis.Timeout == 0 {
		config.Redis.Timeout = 5 * time.Second
	}

	// SQLite默认值
	if config.SQLite.BatchSize <= 0 {
		config.SQLite.BatchSize = 1000
	}
	if config.SQLite.MaxConnections <= 0 {
		config.SQLite.MaxConnections = 10
	}

	// NATS默认值
	if config.NATS.BatchSize <= 0 {
		config.NATS.BatchSize = 50
	}
	if config.NATS.Timeout == 0 {
		config.NATS.Timeout = 10 * time.Second
	}

	// 性能默认值
	if config.Performance.RedisScanCount <= 0 {
		config.Performance.RedisScanCount = 100
	}
	if config.Performance.MemoryLimitMB <= 0 {
		config.Performance.MemoryLimitMB = 512
	}

	// 稳定性默认值
	if config.Stability.MaxMemoryMB == 0 {
		config.Stability.MaxMemoryMB = 512
	}
	if config.Stability.GCThresholdMB == 0 {
		config.Stability.GCThresholdMB = 256
	}
}
