
services:
  mdc_data_push:
    image: mdc_data_push:1.1.2
    container_name: mdc_data_push
    restart: always
    ports:
      - "8085:8085"
    volumes:
      - ./data:/data
      - ./logs:/app/logs
      - ./scripts:/app/scripts
      - ./config.yml:/app/config.yml
    environment:
      - TZ=Asia/Shanghai
    # 网络配置 - 连接到外部网络
    networks:
      - mdc_full_mdc_network

# 网络配置
networks:
  # 使用外部网络，与其他MDC服务共享
  # 这个网络应该在主docker-compose文件中定义
  mdc_full_mdc_network:
    external: true