/**
 * 设备处理器模块
 *
 * 功能概述：
 * 本模块提供设备数据处理器功能，负责管理多个设备的数据处理流水线，
 * 确保每个设备的数据按FIFO顺序处理，实现高效的并行数据处理和资源管理
 *
 * 主要功能：
 * - 处理器管理：动态创建和管理多个设备的数据处理器
 * - 并行处理：每个设备独立的处理流水线，支持并行处理
 * - FIFO保证：严格按照先进先出顺序处理每个设备的数据
 * - 自动发现：自动发现新设备并创建对应的处理器
 * - 资源清理：自动清理不活跃的处理器，释放系统资源
 * - 统计监控：详细的处理统计和性能监控
 * - 故障恢复：优先处理SQLite中的失败数据
 *
 * 架构设计：
 * - 管理器模式：DeviceProcessorManager管理多个设备处理器
 * - 工作者模式：每个DeviceDataProcessor作为独立的工作者
 * - 生产者消费者：Redis作为生产者，处理器作为消费者
 * - 状态机模式：处理器具有明确的状态转换
 *
 * 处理流程：
 * 1. 设备发现：定期扫描Redis中的设备列表
 * 2. 处理器创建：为新设备创建专用处理器
 * 3. 数据处理：按FIFO顺序处理设备数据
 * 4. 数据清洗：使用JavaScript引擎清洗数据
 * 5. 数据推送：推送到目标系统（NATS/InfluxDB）
 * 6. 故障处理：推送失败时保存到SQLite
 * 7. 重试机制：优先处理SQLite中的失败数据
 *
 * 技术特性：
 * - 并发安全：使用读写锁保护共享资源
 * - 上下文控制：使用context控制goroutine生命周期
 * - 优雅关闭：支持优雅关闭和资源清理
 * - 动态扩展：动态创建和销毁处理器
 * - 内存管理：自动清理不活跃的处理器
 *
 * 性能优化：
 * - 批量处理：支持批量读取和处理数据
 * - 独立引擎：每个处理器使用独立的JS引擎
 * - 异步处理：非阻塞的异步数据处理
 * - 资源复用：合理的资源创建和复用策略
 *
 * 可靠性保证：
 * - 数据不丢失：推送失败时自动保存到SQLite
 * - 顺序保证：严格的FIFO数据处理顺序
 * - 故障恢复：优先处理失败数据的重试机制
 * - 错误隔离：单个设备的错误不影响其他设备
 *
 * 业务价值：
 * - 高吞吐量：并行处理多个设备的数据
 * - 数据完整性：确保数据不丢失和顺序正确
 * - 系统稳定性：自动的资源管理和错误恢复
 * - 运维友好：详细的统计信息和监控指标
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"fmt"
	"sync"
	"time"
)

/**
 * 设备处理器管理器结构体
 *
 * 功能：管理多个设备的数据处理器，负责处理器的创建、调度、监控和清理
 *
 * 架构设计：
 * - 中央管理：统一管理所有设备的数据处理器
 * - 动态扩展：根据设备发现动态创建和销毁处理器
 * - 并发控制：使用读写锁保护处理器映射表
 * - 生命周期管理：完整的处理器生命周期管理
 *
 * 核心职责：
 * - 设备发现：定期扫描Redis中的设备列表
 * - 处理器管理：创建、启动、停止、清理处理器
 * - 统计聚合：收集和聚合所有处理器的统计信息
 * - 资源优化：自动清理不活跃的处理器
 *
 * @struct DeviceProcessorManager
 */
type DeviceProcessorManager struct {
	/** 设备处理器映射表，key为设备ID，value为处理器实例 */
	processors map[string]*DeviceDataProcessor

	/** 全局配置对象，包含处理间隔、批量大小等参数 */
	config *Config

	/** Redis客户端，用于设备发现和数据读取 */
	redis *RedisClient

	/** SQLite存储，用于故障数据的本地缓存 */
	sqlite *SQLiteStorage

	/** 数据推送器，用于将处理后的数据推送到目标系统 */
	dataPusher DataPusher

	/** JavaScript引擎，用于数据清洗（共享实例） */
	jsEngine *JSEngine

	/** 设备数据日志器，用于记录设备数据处理过程 */
	deviceLogger *DeviceLogger

	/** 设备离线监控器，用于监控设备状态 */
	deviceMonitor *DeviceMonitor

	/** 读写锁，保护处理器映射表的并发访问 */
	mutex sync.RWMutex

	/** 上下文对象，用于控制所有goroutine的生命周期 */
	ctx context.Context

	/** 取消函数，用于停止所有goroutine */
	cancel context.CancelFunc

	/** 等待组，用于等待所有goroutine结束 */
	wg sync.WaitGroup

	/** 管理器统计信息，包含所有处理器的聚合统计 */
	stats ProcessorManagerStats
}

// ProcessorManagerStats 处理器管理器统计信息
type ProcessorManagerStats struct {
	ActiveProcessors int                         `json:"active_processors"` // 活跃处理器数量
	TotalProcessed   int64                       `json:"total_processed"`   // 总处理数量
	TotalErrors      int64                       `json:"total_errors"`      // 总错误数量
	ProcessorStats   map[string]*ProcessingStats `json:"processor_stats"`   // 各处理器统计
	LastUpdateTime   time.Time                   `json:"last_update_time"`  // 最后更新时间
}

// DeviceDataProcessor 单设备数据处理器
type DeviceDataProcessor struct {
	deviceID      string
	config        *Config
	redis         *RedisClient
	sqlite        *SQLiteStorage
	dataPusher    DataPusher
	jsEngine      *JSEngine
	stats         *ProcessingStats
	ctx           context.Context
	cancel        context.CancelFunc
	mutex         sync.RWMutex
	lastActivity  time.Time
	isRunning     bool
	deviceLogger  *DeviceLogger  // 设备数据日志器，用于记录设备数据处理过程
	deviceMonitor *DeviceMonitor // 设备离线监控器，用于更新设备处理状态
}

// NewDeviceProcessorManager 创建设备处理器管理器
func NewDeviceProcessorManager(config *Config, redis *RedisClient, sqlite *SQLiteStorage, dataPusher DataPusher, jsEngine *JSEngine) *DeviceProcessorManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &DeviceProcessorManager{
		processors: make(map[string]*DeviceDataProcessor),
		config:     config,
		redis:      redis,
		sqlite:     sqlite,
		dataPusher: dataPusher,
		jsEngine:   jsEngine,
		ctx:        ctx,
		cancel:     cancel,
		stats: ProcessorManagerStats{
			ProcessorStats: make(map[string]*ProcessingStats),
		},
	}
}

// SetDeviceLogger 设置设备数据日志器
// 参数:
//   - deviceLogger: 设备数据日志器实例
func (pm *DeviceProcessorManager) SetDeviceLogger(deviceLogger *DeviceLogger) {
	pm.deviceLogger = deviceLogger
}

// SetDeviceMonitor 设置设备离线监控器
// 参数:
//   - deviceMonitor: 设备离线监控器实例
func (pm *DeviceProcessorManager) SetDeviceMonitor(deviceMonitor *DeviceMonitor) {
	pm.deviceMonitor = deviceMonitor
}

// Start 启动处理器管理器
func (pm *DeviceProcessorManager) Start() error {
	// 启动设备发现协程
	pm.wg.Add(1)
	go pm.deviceDiscoveryLoop()

	// 启动统计更新协程
	pm.wg.Add(1)
	go pm.statsUpdateLoop()

	// 启动清理协程
	pm.wg.Add(1)
	go pm.cleanupLoop()

	return nil
}

// Stop 停止处理器管理器
func (pm *DeviceProcessorManager) Stop() error {
	pm.cancel()

	// 停止所有设备处理器
	pm.mutex.Lock()
	for _, processor := range pm.processors {
		processor.Stop()
	}
	pm.mutex.Unlock()

	pm.wg.Wait()
	return nil
}

// deviceDiscoveryLoop 设备发现循环
func (pm *DeviceProcessorManager) deviceDiscoveryLoop() {
	defer pm.wg.Done()

	ticker := time.NewTicker(pm.config.Service.ProcessInterval)
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			return
		case <-ticker.C:
			pm.discoverAndProcessDevices()
		}
	}
}

// discoverAndProcessDevices 发现并处理设备
func (pm *DeviceProcessorManager) discoverAndProcessDevices() {
	// 获取所有设备列表
	devices, err := pm.redis.GetDeviceList(pm.ctx)
	if err != nil {
		fmt.Printf("Failed to get device list: %v\n", err)
		return
	}

	// 为每个设备创建或获取处理器
	for _, deviceID := range devices {
		pm.ensureProcessor(deviceID)
	}
}

// ensureProcessor 确保设备处理器存在
func (pm *DeviceProcessorManager) ensureProcessor(deviceID string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	processor, exists := pm.processors[deviceID]
	if !exists {
		// 创建新的处理器
		processor = NewDeviceDataProcessor(deviceID, pm.config, pm.redis, pm.sqlite, pm.dataPusher, pm.jsEngine, pm.deviceLogger, pm.deviceMonitor)
		pm.processors[deviceID] = processor
		pm.stats.ActiveProcessors++

		// 启动处理器
		go processor.Start(pm.ctx)

		fmt.Printf("Created processor for device: %s\n", deviceID)
	} else {
		// 更新活动时间
		processor.updateActivity()
	}
}

// statsUpdateLoop 统计更新循环
func (pm *DeviceProcessorManager) statsUpdateLoop() {
	defer pm.wg.Done()

	ticker := time.NewTicker(pm.config.Performance.MetricsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			return
		case <-ticker.C:
			pm.updateStats()
		}
	}
}

// updateStats 更新统计信息
func (pm *DeviceProcessorManager) updateStats() {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	var totalProcessed, totalErrors int64

	for deviceID, processor := range pm.processors {
		stats := processor.GetStats()
		pm.stats.ProcessorStats[deviceID] = stats
		totalProcessed += stats.TotalProcessed
		totalErrors += stats.ErrorCount
	}

	pm.stats.TotalProcessed = totalProcessed
	pm.stats.TotalErrors = totalErrors
	pm.stats.LastUpdateTime = time.Now()
}

// cleanupLoop 清理循环
func (pm *DeviceProcessorManager) cleanupLoop() {
	defer pm.wg.Done()

	ticker := time.NewTicker(time.Minute * 5) // 每5分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			return
		case <-ticker.C:
			pm.cleanupInactiveProcessors()
		}
	}
}

// cleanupInactiveProcessors 清理不活跃的处理器
func (pm *DeviceProcessorManager) cleanupInactiveProcessors() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	inactiveThreshold := time.Now().Add(-time.Minute * 10) // 10分钟无活动

	for deviceID, processor := range pm.processors {
		if processor.lastActivity.Before(inactiveThreshold) && !processor.isRunning {
			processor.Stop()
			delete(pm.processors, deviceID)
			delete(pm.stats.ProcessorStats, deviceID)
			pm.stats.ActiveProcessors--

			fmt.Printf("Cleaned up inactive processor for device: %s\n", deviceID)
		}
	}
}

// GetStats 获取管理器统计信息
func (pm *DeviceProcessorManager) GetStats() ProcessorManagerStats {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return pm.stats
}

// ShutdownDeviceProcessor 关闭指定设备的处理器
// 用于设备离线时关闭对应的processor节省资源
// 参数:
//   - deviceID: 需要关闭processor的设备ID
func (pm *DeviceProcessorManager) ShutdownDeviceProcessor(deviceID string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	processor, exists := pm.processors[deviceID]
	if !exists {
		fmt.Printf("Processor for device %s not found\n", deviceID)
		return
	}

	// 停止处理器
	processor.Stop()

	// 从处理器映射中移除
	delete(pm.processors, deviceID)
	delete(pm.stats.ProcessorStats, deviceID)
	pm.stats.ActiveProcessors--

	fmt.Printf("Shutdown processor for device: %s\n", deviceID)
}

// NewDeviceDataProcessor 创建设备数据处理器
func NewDeviceDataProcessor(deviceID string, config *Config, redis *RedisClient, sqlite *SQLiteStorage, dataPusher DataPusher, jsEngine *JSEngine, deviceLogger *DeviceLogger, deviceMonitor *DeviceMonitor) *DeviceDataProcessor {
	ctx, cancel := context.WithCancel(context.Background())

	// 为每个设备处理器创建独立的JavaScript引擎实例
	deviceJSEngine, err := NewJSEngine(config.DataCleaning)
	if err != nil {
		// 如果创建失败，使用共享的引擎（但会有并发问题）
		deviceJSEngine = jsEngine
	}

	return &DeviceDataProcessor{
		deviceID:      deviceID,
		config:        config,
		redis:         redis,
		sqlite:        sqlite,
		dataPusher:    dataPusher,
		jsEngine:      deviceJSEngine,
		ctx:           ctx,
		cancel:        cancel,
		lastActivity:  time.Now(),
		deviceLogger:  deviceLogger,
		deviceMonitor: deviceMonitor,
		stats: &ProcessingStats{
			DeviceID: deviceID,
		},
	}
}

// Start 启动设备处理器
func (dp *DeviceDataProcessor) Start(parentCtx context.Context) {
	dp.mutex.Lock()
	dp.isRunning = true
	dp.mutex.Unlock()

	defer func() {
		dp.mutex.Lock()
		dp.isRunning = false
		dp.mutex.Unlock()
	}()

	ticker := time.NewTicker(dp.config.Service.ProcessInterval)
	defer ticker.Stop()

	for {
		select {
		case <-parentCtx.Done():
			return
		case <-dp.ctx.Done():
			return
		case <-ticker.C:
			dp.processDeviceData()
		}
	}
}

// Stop 停止设备处理器
func (dp *DeviceDataProcessor) Stop() {
	dp.cancel()
}

// processDeviceData 处理设备数据
func (dp *DeviceDataProcessor) processDeviceData() {
	dp.updateActivity()

	// 优先处理SQLite中的数据
	if err := dp.processSQLiteData(); err != nil {
		fmt.Printf("Error processing SQLite data for device %s: %v\n", dp.deviceID, err)
	}

	// 处理Redis中的数据
	if err := dp.processRedisData(); err != nil {
		fmt.Printf("Error processing Redis data for device %s: %v\n", dp.deviceID, err)
	}
}

// processSQLiteData 处理SQLite中的数据
func (dp *DeviceDataProcessor) processSQLiteData() error {
	// 获取待处理的SQLite数据
	records, err := dp.sqlite.GetPendingData(dp.ctx, dp.deviceID, dp.config.Service.BatchSize)
	if err != nil {
		dp.stats.ErrorCount++
		return fmt.Errorf("failed to get pending SQLite data: %w", err)
	}

	if len(records) == 0 {
		return nil // 没有待处理的数据
	}

	// 尝试推送到目标系统
	if err := dp.dataPusher.PushFromSQLite(dp.ctx, records); err != nil {
		// 推送失败，更新重试次数
		for _, record := range records {
			dp.sqlite.UpdateRetryCount(dp.ctx, record, err.Error())
		}
		dp.stats.ErrorCount++
		return fmt.Errorf("failed to push SQLite data: %w", err)
	}

	// 推送成功，删除SQLite记录
	if err := dp.sqlite.BatchDeleteRecords(dp.ctx, records); err != nil {
		dp.stats.ErrorCount++
		return fmt.Errorf("failed to delete SQLite records: %w", err)
	}

	dp.stats.SQLiteProcessed += int64(len(records))
	dp.stats.DataPushed += int64(len(records))
	dp.stats.TotalProcessed += int64(len(records))

	return nil
}

// processRedisData 处理Redis中的数据
func (dp *DeviceDataProcessor) processRedisData() error {
	// 获取Redis中的数据
	dataItems, err := dp.redis.GetDeviceDataKeys(dp.ctx, dp.deviceID, dp.config.Service.BatchSize)
	if err != nil {
		dp.stats.ErrorCount++
		return fmt.Errorf("failed to get Redis data: %w", err)
	}

	if len(dataItems) == 0 {
		return nil // 没有数据
	}

	var processedData []*ProcessedData
	var keysToDelete []string

	// 处理每条数据
	for _, item := range dataItems {
		startTime := time.Now()

		// 解析设备数据
		var deviceData DeviceData
		if err := deviceData.FromJSON(item.Data); err != nil {
			dp.stats.ErrorCount++

			// 记录解析失败的设备日志
			if dp.deviceLogger != nil {
				logEntry := &DeviceLogEntry{
					DeviceID:       dp.deviceID,
					DataType:       "unknown",
					Timestamp:      time.Now(),
					ProcessedAt:    time.Now(),
					RawData:        map[string]interface{}{"raw": item.Data},
					CleanedData:    nil,
					Metadata:       nil, // 解析失败时无法获取metadata
					PushResult:     "failed",
					ErrorMessage:   fmt.Sprintf("JSON parsing failed: %v", err),
					ProcessingTime: time.Since(startTime).Milliseconds(),
				}
				dp.deviceLogger.LogDeviceData(logEntry)
			}
			continue
		}

		// 数据清洗
		cleaned, err := dp.jsEngine.CleanData(&deviceData)
		if err != nil {
			dp.stats.ErrorCount++

			// 记录清洗失败的设备日志
			if dp.deviceLogger != nil {
				logEntry := &DeviceLogEntry{
					DeviceID:       deviceData.DeviceID,
					DataType:       deviceData.DataType,
					Timestamp:      deviceData.Timestamp,
					ProcessedAt:    time.Now(),
					RawData:        deviceData.RawData,
					CleanedData:    nil,
					Metadata:       deviceData.Metadata, // 包含设备元数据信息
					PushResult:     "failed",
					ErrorMessage:   fmt.Sprintf("Data cleaning failed: %v", err),
					ProcessingTime: time.Since(startTime).Milliseconds(),
				}
				dp.deviceLogger.LogDeviceData(logEntry)
			}

			// 清洗失败，保存到SQLite
			errorData := &ProcessedData{
				DeviceID:     deviceData.DeviceID,
				DataType:     deviceData.DataType,
				Timestamp:    deviceData.Timestamp,
				ProcessedAt:  time.Now(),
				RawData:      deviceData.RawData,
				Sequence:     deviceData.Sequence,
				Location:     deviceData.Location,
				Error:        true,
				ErrorMessage: err.Error(),
			}
			dp.sqlite.SaveData(dp.ctx, errorData)
			keysToDelete = append(keysToDelete, item.Key)
			continue
		}

		// 记录成功清洗的设备日志
		if dp.deviceLogger != nil {
			logEntry := &DeviceLogEntry{
				DeviceID:       deviceData.DeviceID,
				DataType:       deviceData.DataType,
				Timestamp:      deviceData.Timestamp,
				ProcessedAt:    time.Now(),
				RawData:        deviceData.RawData,
				CleanedData:    cleaned.ProcessedData,
				Metadata:       deviceData.Metadata, // 包含设备元数据信息
				PushResult:     "pending",
				ErrorMessage:   "",
				ProcessingTime: time.Since(startTime).Milliseconds(),
			}
			dp.deviceLogger.LogDeviceData(logEntry)
		}

		processedData = append(processedData, cleaned)
		keysToDelete = append(keysToDelete, item.Key)
	}

	if len(processedData) == 0 {
		// 删除已处理的键（即使处理失败）
		if len(keysToDelete) > 0 {
			dp.redis.BatchDeleteDeviceData(dp.ctx, dp.deviceID, keysToDelete)
		}
		return nil
	}

	// 尝试推送到目标系统
	if err := dp.dataPusher.BatchPushData(dp.ctx, processedData, "redis"); err != nil {
		// 推送失败，保存到SQLite
		if saveErr := dp.sqlite.BatchSaveData(dp.ctx, processedData); saveErr != nil {
			dp.stats.ErrorCount++
			return fmt.Errorf("failed to save data to SQLite after push failure: %w", saveErr)
		}
		dp.stats.ErrorCount++
	} else {
		// 推送成功
		dp.stats.DataPushed += int64(len(processedData))
	}

	// 删除Redis中的数据
	if err := dp.redis.BatchDeleteDeviceData(dp.ctx, dp.deviceID, keysToDelete); err != nil {
		dp.stats.ErrorCount++
		return fmt.Errorf("failed to delete Redis data: %w", err)
	}

	dp.stats.RedisProcessed += int64(len(processedData))
	dp.stats.TotalProcessed += int64(len(processedData))
	dp.stats.LastProcessedTime = time.Now()

	// 更新设备监控器状态（设备处理了数据，说明设备在线）
	if dp.deviceMonitor != nil {
		if err := dp.deviceMonitor.UpdateDeviceLastProcessed(dp.deviceID); err != nil {
			fmt.Printf("Failed to update device monitor for %s: %v\n", dp.deviceID, err)
		}
	}

	return nil
}

// updateActivity 更新活动时间
func (dp *DeviceDataProcessor) updateActivity() {
	dp.mutex.Lock()
	dp.lastActivity = time.Now()
	dp.mutex.Unlock()
}

// GetStats 获取处理器统计信息
func (dp *DeviceDataProcessor) GetStats() *ProcessingStats {
	dp.mutex.RLock()
	defer dp.mutex.RUnlock()

	// 获取当前队列大小
	if queueSize, err := dp.redis.GetDeviceQueueSize(dp.ctx, dp.deviceID); err == nil {
		dp.stats.QueueSize = int(queueSize)
	}

	// 计算平均处理时间
	if dp.stats.TotalProcessed > 0 {
		totalTime := time.Since(dp.stats.LastProcessedTime)
		dp.stats.AverageProcessTime = float64(totalTime.Milliseconds()) / float64(dp.stats.TotalProcessed)
	}

	return dp.stats
}
