# Redis连接故障排查指南

## 概述

本文档描述了05_data_push服务中Redis连接失败时的详细错误信息和排查建议的实现。

## 问题背景

原始问题：
- 05_data_push服务启动时Redis连接失败
- 错误信息简单：`failed to connect to Redis: context deadline exceeded`
- 缺乏具体的排查建议，用户难以快速解决问题

## 解决方案

### 1. 增强Redis客户端错误信息

#### 修改文件：`redis_client.go`

在`NewRedisClient`函数中增加了详细的错误信息构建：

```go
if err := client.client.Ping(ctx).Err(); err != nil {
    // 构建详细的错误信息和排查建议
    errorMsg := fmt.Sprintf("Redis连接失败: %v", err)
    
    // 添加连接信息
    errorMsg += fmt.Sprintf("\n\n📋 连接信息:")
    errorMsg += fmt.Sprintf("\n  地址: %s", addr)
    errorMsg += fmt.Sprintf("\n  数据库: %d", config.DB)
    errorMsg += fmt.Sprintf("\n  超时: %v", config.Timeout)
    errorMsg += fmt.Sprintf("\n  重试次数: %d", config.RetryCount)
    
    // 添加排查建议
    errorMsg += fmt.Sprintf("\n\n🔍 排查建议:")
    errorMsg += fmt.Sprintf("\n  1. 检查Redis服务是否运行:")
    errorMsg += fmt.Sprintf("\n     docker ps | grep redis")
    errorMsg += fmt.Sprintf("\n     或 systemctl status redis")
    
    // ... 更多建议
    
    return nil, fmt.Errorf(errorMsg)
}
```

#### 特性：

1. **连接信息显示**：显示具体的连接地址、数据库、超时设置等
2. **通用排查建议**：提供6个常见的排查步骤
3. **特定错误建议**：根据错误类型提供针对性建议
4. **格式化输出**：使用emoji和缩进提高可读性

### 2. 增强启动脚本检查

#### 修改文件：`run.sh`

在服务启动前增加Redis服务检查：

```bash
# 检查Redis服务
echo "🔍 检查Redis服务..."
REDIS_HOST="localhost"
REDIS_PORT="6379"

# 检查Redis端口是否可达
if ! nc -z $REDIS_HOST $REDIS_PORT 2>/dev/null; then
    echo "⚠️  警告: Redis服务 ($REDIS_HOST:$REDIS_PORT) 不可达"
    echo ""
    echo "🔧 Redis排查建议:"
    echo "  1. 检查Redis是否运行:"
    echo "     docker ps | grep redis"
    # ... 更多建议
else
    echo "✅ Redis服务检查通过 ($REDIS_HOST:$REDIS_PORT)"
fi
```

#### 特性：

1. **预检查**：在服务启动前检查Redis可达性
2. **早期警告**：提前发现Redis连接问题
3. **具体建议**：提供Docker和系统服务的解决方案
4. **连接测试**：如果redis-cli可用，进行ping测试

### 3. 增强主程序错误处理

#### 修改文件：`main.go`

在Redis初始化失败时提供更好的日志输出：

```go
redisClient, err = NewRedisClient(config.Redis)
if err != nil {
    // Redis连接失败时提供详细的错误信息
    logger.Errorf("❌ Redis客户端初始化失败")
    logger.Errorf("错误详情: %v", err)
    logger.Errorf("")
    logger.Errorf("💡 快速解决方案:")
    logger.Errorf("   1. 检查Redis服务: docker ps | grep redis")
    logger.Errorf("   2. 启动Redis服务: docker-compose up -d redis")
    logger.Errorf("   3. 测试连接: redis-cli -h %s -p %d ping", config.Redis.Host, config.Redis.Port)
    logger.Errorf("")
    return fmt.Errorf("Redis客户端初始化失败: %w", err)
}
```

## 错误信息示例

### 启动脚本检查输出

```
🔍 检查Redis服务...
⚠️  警告: Redis服务 (localhost:6379) 不可达

🔧 Redis排查建议:
  1. 检查Redis是否运行:
     docker ps | grep redis
     或 systemctl status redis

  2. 启动Redis服务:
     Docker: docker-compose up -d redis
     系统服务: sudo systemctl start redis

  3. 检查端口占用:
     lsof -i :6379

  4. 测试Redis连接:
     redis-cli -h localhost -p 6379 ping

⚠️  服务将尝试启动，但可能因Redis连接失败而退出
   建议先解决Redis连接问题
```

### 详细错误日志

```json
{
  "level": "error",
  "msg": "错误详情: Redis连接失败: context deadline exceeded\n\n📋 连接信息:\n  地址: localhost:6379\n  数据库: 2\n  超时: 5s\n  重试次数: 3\n\n🔍 排查建议:\n  1. 检查Redis服务是否运行:\n     docker ps | grep redis\n     或 systemctl status redis\n\n  2. 检查Redis连接:\n     redis-cli -h localhost -p 6379 ping\n\n  3. 检查网络连接:\n     telnet localhost 6379\n     或 nc -zv localhost 6379\n\n  4. 检查防火墙设置:\n     确保端口 6379 未被防火墙阻止\n\n  5. 检查Docker服务:\n     如果使用Docker，确保Redis容器正在运行:\n     docker-compose ps\n     docker-compose up -d redis\n\n  6. 检查配置文件:\n     确认config.yml中的Redis配置正确\n     特别是host、port、password和db设置",
  "time": "2025-06-07 11:26:13"
}
```

## 测试验证

### 1. Redis服务正常时

```bash
$ ./run.sh
🔍 检查Redis服务...
✅ Redis服务检查通过 (localhost:6379)
✅ Redis连接测试成功
...
✅ Redis客户端初始化成功 (地址: localhost:6379, 数据库: 2)
```

### 2. Redis服务异常时

```bash
$ ./run.sh
🔍 检查Redis服务...
⚠️  警告: Redis服务 (localhost:6379) 不可达
[详细排查建议]
...
❌ Redis客户端初始化失败
[详细错误信息和解决方案]
```

## 用户体验改进

1. **问题识别**：用户能够快速识别Redis连接问题
2. **解决指导**：提供具体的命令和步骤
3. **分层信息**：从简单到详细的错误信息
4. **预防性检查**：在问题发生前提供警告
5. **多种场景**：支持Docker和系统服务两种部署方式

## 技术价值

1. **降低运维成本**：减少故障排查时间
2. **提高可用性**：快速恢复服务
3. **改善体验**：友好的错误信息和建议
4. **标准化**：统一的错误处理模式
5. **可扩展性**：可应用到其他服务组件

## 后续扩展

这种详细的错误处理模式可以扩展到：
- InfluxDB连接失败
- MongoDB连接失败
- NATS连接失败
- SQLite数据库问题
- 其他外部依赖服务
