/**
 * 数据推送接口和实现模块
 *
 * 功能概述：
 * 本模块使用策略模式和工厂模式实现统一的数据推送接口，支持多种推送目标，
 * 包括NATS JetStream、InfluxDB和混合推送模式，提供高可用和高可靠的数据推送服务
 *
 * 主要功能：
 * - 统一接口：定义统一的数据推送接口，支持多种推送策略
 * - 多目标支持：支持NATS、InfluxDB等多种推送目标
 * - 混合推送：同时推送到多个目标，提高可靠性
 * - 工厂模式：使用工厂模式创建不同类型的推送器
 * - 策略模式：可插拔的推送策略实现
 * - 统计监控：统一的统计信息接口和实现
 * - 健康检查：完善的健康检查机制
 *
 * 设计模式：
 * - 策略模式：DataPusher接口定义推送策略，不同实现提供不同推送方式
 * - 工厂模式：DataPusherFactory负责创建不同类型的推送器
 * - 适配器模式：将不同客户端适配为统一的推送器接口
 * - 组合模式：MixedPusher组合多个推送器实现混合推送
 *
 * 推送器类型：
 * - NATS推送器：推送到NATS JetStream消息队列
 * - InfluxDB推送器：推送到InfluxDB时序数据库
 * - 混合推送器：同时推送到多个目标，提供冗余保障
 *
 * 技术特性：
 * - 接口统一：所有推送器实现相同接口，便于切换和扩展
 * - 配置驱动：基于配置文件选择推送器类型和参数
 * - 错误隔离：单个推送器失败不影响其他推送器
 * - 统计聚合：统一的统计信息收集和聚合
 * - 资源管理：正确的资源创建、使用和释放
 *
 * 可靠性保证：
 * - 多重保障：混合推送模式提供多重数据保障
 * - 故障转移：主推送器失败时自动使用备用推送器
 * - 错误恢复：完善的错误处理和重试机制
 * - 健康监控：实时监控推送器健康状态
 *
 * 业务价值：
 * - 高可用性：多目标推送确保数据不丢失
 * - 灵活配置：支持不同环境和需求的推送配置
 * - 易于扩展：新增推送目标只需实现接口
 * - 运维友好：统一的监控和管理接口
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"fmt"
	"time"
)

/**
 * 数据推送器接口
 *
 * 功能：定义统一的数据推送接口，支持不同的推送策略和目标
 *
 * 设计原则：
 * - 接口隔离：每个方法职责单一，便于实现和测试
 * - 依赖倒置：依赖抽象接口而非具体实现
 * - 开闭原则：对扩展开放，对修改关闭
 * - 里氏替换：所有实现都可以互相替换
 *
 * 使用场景：
 * - 策略模式：不同推送策略的统一接口
 * - 工厂模式：工厂创建不同类型的推送器
 * - 适配器模式：适配不同的推送客户端
 * - 组合模式：组合多个推送器实现混合推送
 *
 * @interface DataPusher
 */
type DataPusher interface {
	/**
	 * 初始化推送器
	 *
	 * 功能：执行推送器的初始化操作，如连接建立、配置验证等
	 *
	 * @returns {error} 初始化失败时返回错误信息
	 */
	Initialize() error

	/**
	 * 推送单条数据
	 *
	 * 功能：将单条处理后的数据推送到目标系统
	 *
	 * @param {context.Context} ctx - 上下文对象，用于超时控制和取消操作
	 * @param {*ProcessedData} data - 要推送的处理后数据
	 * @returns {error} 推送失败时返回错误信息
	 */
	PushData(ctx context.Context, data *ProcessedData) error

	/**
	 * 批量推送数据
	 *
	 * 功能：批量推送多条处理后的数据，提高推送效率
	 *
	 * @param {context.Context} ctx - 上下文对象
	 * @param {[]*ProcessedData} dataList - 要推送的数据列表
	 * @param {string} source - 数据来源标识（redis、sqlite等）
	 * @returns {error} 推送失败时返回错误信息
	 */
	BatchPushData(ctx context.Context, dataList []*ProcessedData, source string) error

	/**
	 * 从SQLite记录推送数据
	 *
	 * 功能：从SQLite缓存记录中推送数据，用于故障恢复场景
	 *
	 * @param {context.Context} ctx - 上下文对象
	 * @param {[]*SQLiteRecord} records - SQLite记录列表
	 * @returns {error} 推送失败时返回错误信息
	 */
	PushFromSQLite(ctx context.Context, records []*SQLiteRecord) error

	/**
	 * 健康检查
	 *
	 * 功能：检查推送器的健康状态，包括连接状态、服务可用性等
	 *
	 * @param {context.Context} ctx - 上下文对象
	 * @returns {error} 健康检查失败时返回错误信息
	 */
	HealthCheck(ctx context.Context) error

	/**
	 * 获取统计信息
	 *
	 * 功能：获取推送器的运行统计信息，用于监控和分析
	 *
	 * @returns {PusherStats} 推送器统计信息
	 */
	GetStats() PusherStats

	/**
	 * 关闭推送器
	 *
	 * 功能：关闭推送器，释放资源，执行清理操作
	 *
	 * @returns {error} 关闭失败时返回错误信息
	 */
	Close() error

	/**
	 * 获取推送器类型
	 *
	 * 功能：返回推送器的类型标识，用于日志记录和监控
	 *
	 * @returns {string} 推送器类型字符串
	 */
	GetType() string
}

// PusherStats 推送器统计信息接口
type PusherStats interface {
	GetTotalPushed() int64
	GetSuccessCount() int64
	GetErrorCount() int64
	GetLastError() string
	GetConnectionStatus() string
}

// PusherType 推送器类型
type PusherType string

const (
	PusherTypeNATS     PusherType = "nats"
	PusherTypeInfluxDB PusherType = "influxdb"
	PusherTypeMixed    PusherType = "mixed" // 同时推送到多个目标
)

// PusherConfig 推送器配置
type PusherConfig struct {
	Type     PusherType `yaml:"type"`     // 推送器类型
	Primary  string     `yaml:"primary"`  // 主要推送目标
	Fallback string     `yaml:"fallback"` // 备用推送目标
	Enabled  bool       `yaml:"enabled"`  // 是否启用
}

// DataPusherFactory 数据推送器工厂
type DataPusherFactory struct {
	config       *Config
	natsClient   *NATSClient
	influxClient *InfluxDBClient
}

// NewDataPusherFactory 创建数据推送器工厂
func NewDataPusherFactory(config *Config) *DataPusherFactory {
	return &DataPusherFactory{
		config: config,
	}
}

// CreatePusher 创建数据推送器
func (factory *DataPusherFactory) CreatePusher(pusherType PusherType) (DataPusher, error) {
	switch pusherType {
	case PusherTypeNATS:
		if factory.natsClient == nil {
			var err error
			factory.natsClient, err = NewNATSClient(factory.config.NATS)
			if err != nil {
				return nil, fmt.Errorf("failed to create NATS client: %w", err)
			}
		}
		return NewNATSPusher(factory.natsClient), nil

	case PusherTypeInfluxDB:
		if factory.influxClient == nil {
			var err error
			factory.influxClient, err = NewInfluxDBClient(factory.config.InfluxDB)
			if err != nil {
				return nil, fmt.Errorf("failed to create InfluxDB client: %w", err)
			}
		}
		return NewInfluxDBPusher(factory.influxClient), nil

	case PusherTypeMixed:
		// 创建混合推送器，同时推送到多个目标
		pushers := make([]DataPusher, 0)

		// 添加NATS推送器
		if factory.config.DataPush.NATS.Enabled {
			natsPusher, err := factory.CreatePusher(PusherTypeNATS)
			if err != nil {
				return nil, fmt.Errorf("failed to create NATS pusher: %w", err)
			}
			pushers = append(pushers, natsPusher)
		}

		// 添加InfluxDB推送器
		if factory.config.DataPush.InfluxDB.Enabled {
			influxPusher, err := factory.CreatePusher(PusherTypeInfluxDB)
			if err != nil {
				return nil, fmt.Errorf("failed to create InfluxDB pusher: %w", err)
			}
			pushers = append(pushers, influxPusher)
		}

		if len(pushers) == 0 {
			return nil, fmt.Errorf("no pushers enabled for mixed mode")
		}

		return NewMixedPusher(pushers), nil

	default:
		return nil, fmt.Errorf("unsupported pusher type: %s", pusherType)
	}
}

// Close 关闭工厂和所有客户端
func (factory *DataPusherFactory) Close() error {
	var errors []error

	if factory.natsClient != nil {
		if err := factory.natsClient.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close NATS client: %w", err))
		}
	}

	if factory.influxClient != nil {
		if err := factory.influxClient.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close InfluxDB client: %w", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors closing factory: %v", errors)
	}

	return nil
}

// NATSPusher NATS推送器实现
type NATSPusher struct {
	client *NATSClient
}

// NewNATSPusher 创建NATS推送器
func NewNATSPusher(client *NATSClient) *NATSPusher {
	return &NATSPusher{
		client: client,
	}
}

// Initialize 初始化NATS推送器
func (np *NATSPusher) Initialize() error {
	// NATS客户端已在创建时初始化
	return nil
}

// PushData 推送单条数据到NATS
func (np *NATSPusher) PushData(ctx context.Context, data *ProcessedData) error {
	return np.client.PublishData(ctx, data)
}

// BatchPushData 批量推送数据到NATS
func (np *NATSPusher) BatchPushData(ctx context.Context, dataList []*ProcessedData, source string) error {
	return np.client.BatchPublishData(ctx, dataList, source)
}

// PushFromSQLite 从SQLite记录推送数据到NATS
func (np *NATSPusher) PushFromSQLite(ctx context.Context, records []*SQLiteRecord) error {
	return np.client.PublishFromSQLite(ctx, records)
}

// HealthCheck NATS健康检查
func (np *NATSPusher) HealthCheck(ctx context.Context) error {
	return np.client.HealthCheck(ctx)
}

// GetStats 获取NATS统计信息
func (np *NATSPusher) GetStats() PusherStats {
	return &NATSPusherStats{stats: np.client.GetStats()}
}

// Close 关闭NATS推送器
func (np *NATSPusher) Close() error {
	return np.client.Close()
}

// GetType 获取推送器类型
func (np *NATSPusher) GetType() string {
	return string(PusherTypeNATS)
}

// NATSPusherStats NATS推送器统计信息
type NATSPusherStats struct {
	stats NATSStats
}

func (nps *NATSPusherStats) GetTotalPushed() int64       { return nps.stats.TotalPublished }
func (nps *NATSPusherStats) GetSuccessCount() int64      { return nps.stats.SuccessCount }
func (nps *NATSPusherStats) GetErrorCount() int64        { return nps.stats.ErrorCount }
func (nps *NATSPusherStats) GetLastError() string        { return nps.stats.LastError }
func (nps *NATSPusherStats) GetConnectionStatus() string { return nps.stats.ConnectionStatus }

// MixedPusher 混合推送器实现
// 同时推送到多个目标，提供更高的可靠性
type MixedPusher struct {
	pushers []DataPusher
	stats   MixedPusherStats
}

// MixedPusherStats 混合推送器统计信息
type MixedPusherStats struct {
	TotalPushed      int64                  `json:"total_pushed"`
	SuccessCount     int64                  `json:"success_count"`
	ErrorCount       int64                  `json:"error_count"`
	LastError        string                 `json:"last_error"`
	ConnectionStatus string                 `json:"connection_status"`
	PusherStats      map[string]PusherStats `json:"pusher_stats"`
	LastPushTime     time.Time              `json:"last_push_time"`
}

func (mps *MixedPusherStats) GetTotalPushed() int64       { return mps.TotalPushed }
func (mps *MixedPusherStats) GetSuccessCount() int64      { return mps.SuccessCount }
func (mps *MixedPusherStats) GetErrorCount() int64        { return mps.ErrorCount }
func (mps *MixedPusherStats) GetLastError() string        { return mps.LastError }
func (mps *MixedPusherStats) GetConnectionStatus() string { return mps.ConnectionStatus }

// NewMixedPusher 创建混合推送器
func NewMixedPusher(pushers []DataPusher) *MixedPusher {
	return &MixedPusher{
		pushers: pushers,
		stats: MixedPusherStats{
			PusherStats:      make(map[string]PusherStats),
			ConnectionStatus: "unknown",
		},
	}
}

// Initialize 初始化混合推送器
func (mp *MixedPusher) Initialize() error {
	var errors []error

	for _, pusher := range mp.pushers {
		if err := pusher.Initialize(); err != nil {
			errors = append(errors, fmt.Errorf("failed to initialize %s pusher: %w", pusher.GetType(), err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("initialization errors: %v", errors)
	}

	return nil
}

// PushData 推送单条数据到所有目标
func (mp *MixedPusher) PushData(ctx context.Context, data *ProcessedData) error {
	var errors []error
	successCount := 0

	for _, pusher := range mp.pushers {
		if err := pusher.PushData(ctx, data); err != nil {
			errors = append(errors, fmt.Errorf("%s pusher error: %w", pusher.GetType(), err))
			mp.stats.ErrorCount++
		} else {
			successCount++
		}
	}

	mp.stats.TotalPushed++
	mp.stats.LastPushTime = time.Now()

	// 如果至少有一个推送器成功，认为推送成功
	if successCount > 0 {
		mp.stats.SuccessCount++
		mp.updateConnectionStatus()
		return nil
	}

	// 所有推送器都失败
	if len(errors) > 0 {
		mp.stats.LastError = fmt.Sprintf("all pushers failed: %v", errors)
		mp.updateConnectionStatus()
		return fmt.Errorf("all pushers failed: %v", errors)
	}

	return nil
}

// BatchPushData 批量推送数据到所有目标
func (mp *MixedPusher) BatchPushData(ctx context.Context, dataList []*ProcessedData, source string) error {
	var errors []error
	successCount := 0

	for _, pusher := range mp.pushers {
		if err := pusher.BatchPushData(ctx, dataList, source); err != nil {
			errors = append(errors, fmt.Errorf("%s pusher error: %w", pusher.GetType(), err))
			mp.stats.ErrorCount++
		} else {
			successCount++
		}
	}

	mp.stats.TotalPushed += int64(len(dataList))
	mp.stats.LastPushTime = time.Now()

	// 如果至少有一个推送器成功，认为推送成功
	if successCount > 0 {
		mp.stats.SuccessCount += int64(len(dataList))
		mp.updateConnectionStatus()
		return nil
	}

	// 所有推送器都失败
	if len(errors) > 0 {
		mp.stats.LastError = fmt.Sprintf("all pushers failed: %v", errors)
		mp.updateConnectionStatus()
		return fmt.Errorf("all pushers failed: %v", errors)
	}

	return nil
}

// PushFromSQLite 从SQLite记录推送数据到所有目标
func (mp *MixedPusher) PushFromSQLite(ctx context.Context, records []*SQLiteRecord) error {
	var errors []error
	successCount := 0

	for _, pusher := range mp.pushers {
		if err := pusher.PushFromSQLite(ctx, records); err != nil {
			errors = append(errors, fmt.Errorf("%s pusher error: %w", pusher.GetType(), err))
			mp.stats.ErrorCount++
		} else {
			successCount++
		}
	}

	mp.stats.TotalPushed += int64(len(records))
	mp.stats.LastPushTime = time.Now()

	// 如果至少有一个推送器成功，认为推送成功
	if successCount > 0 {
		mp.stats.SuccessCount += int64(len(records))
		mp.updateConnectionStatus()
		return nil
	}

	// 所有推送器都失败
	if len(errors) > 0 {
		mp.stats.LastError = fmt.Sprintf("all pushers failed: %v", errors)
		mp.updateConnectionStatus()
		return fmt.Errorf("all pushers failed: %v", errors)
	}

	return nil
}

// HealthCheck 混合推送器健康检查
func (mp *MixedPusher) HealthCheck(ctx context.Context) error {
	var errors []error
	healthyCount := 0

	for _, pusher := range mp.pushers {
		if err := pusher.HealthCheck(ctx); err != nil {
			errors = append(errors, fmt.Errorf("%s pusher unhealthy: %w", pusher.GetType(), err))
		} else {
			healthyCount++
		}
	}

	mp.updateConnectionStatus()

	// 如果至少有一个推送器健康，认为整体健康
	if healthyCount > 0 {
		return nil
	}

	return fmt.Errorf("all pushers unhealthy: %v", errors)
}

// GetStats 获取混合推送器统计信息
func (mp *MixedPusher) GetStats() PusherStats {
	// 更新各推送器的统计信息
	for _, pusher := range mp.pushers {
		mp.stats.PusherStats[pusher.GetType()] = pusher.GetStats()
	}

	mp.updateConnectionStatus()
	return &mp.stats
}

// Close 关闭混合推送器
func (mp *MixedPusher) Close() error {
	var errors []error

	for _, pusher := range mp.pushers {
		if err := pusher.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close %s pusher: %w", pusher.GetType(), err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors closing mixed pusher: %v", errors)
	}

	return nil
}

// GetType 获取推送器类型
func (mp *MixedPusher) GetType() string {
	return string(PusherTypeMixed)
}

// updateConnectionStatus 更新连接状态
func (mp *MixedPusher) updateConnectionStatus() {
	healthyCount := 0
	totalCount := len(mp.pushers)

	for _, pusher := range mp.pushers {
		stats := pusher.GetStats()
		if stats.GetConnectionStatus() == "connected" || stats.GetConnectionStatus() == "healthy" {
			healthyCount++
		}
	}

	if healthyCount == totalCount {
		mp.stats.ConnectionStatus = "all_healthy"
	} else if healthyCount > 0 {
		mp.stats.ConnectionStatus = "partially_healthy"
	} else {
		mp.stats.ConnectionStatus = "all_unhealthy"
	}
}

// InfluxDBPusher InfluxDB推送器实现
type InfluxDBPusher struct {
	client *InfluxDBClient
}

// NewInfluxDBPusher 创建InfluxDB推送器
func NewInfluxDBPusher(client *InfluxDBClient) *InfluxDBPusher {
	return &InfluxDBPusher{
		client: client,
	}
}

// Initialize 初始化InfluxDB推送器
func (ip *InfluxDBPusher) Initialize() error {
	// InfluxDB客户端已在创建时初始化
	return nil
}

// PushData 推送单条数据到InfluxDB
func (ip *InfluxDBPusher) PushData(ctx context.Context, data *ProcessedData) error {
	return ip.client.WriteData(ctx, data)
}

// BatchPushData 批量推送数据到InfluxDB
func (ip *InfluxDBPusher) BatchPushData(ctx context.Context, dataList []*ProcessedData, source string) error {
	return ip.client.BatchWriteData(ctx, dataList)
}

// PushFromSQLite 从SQLite记录推送数据到InfluxDB
func (ip *InfluxDBPusher) PushFromSQLite(ctx context.Context, records []*SQLiteRecord) error {
	return ip.client.WriteFromSQLite(ctx, records)
}

// HealthCheck InfluxDB健康检查
func (ip *InfluxDBPusher) HealthCheck(ctx context.Context) error {
	return ip.client.HealthCheck(ctx)
}

// GetStats 获取InfluxDB统计信息
func (ip *InfluxDBPusher) GetStats() PusherStats {
	return &InfluxDBPusherStats{stats: ip.client.GetStats()}
}

// Close 关闭InfluxDB推送器
func (ip *InfluxDBPusher) Close() error {
	return ip.client.Close()
}

// GetType 获取推送器类型
func (ip *InfluxDBPusher) GetType() string {
	return string(PusherTypeInfluxDB)
}

// InfluxDBPusherStats InfluxDB推送器统计信息
type InfluxDBPusherStats struct {
	stats InfluxDBStats
}

func (ips *InfluxDBPusherStats) GetTotalPushed() int64       { return ips.stats.TotalWrites }
func (ips *InfluxDBPusherStats) GetSuccessCount() int64      { return ips.stats.SuccessCount }
func (ips *InfluxDBPusherStats) GetErrorCount() int64        { return ips.stats.ErrorCount }
func (ips *InfluxDBPusherStats) GetLastError() string        { return ips.stats.LastError }
func (ips *InfluxDBPusherStats) GetConnectionStatus() string { return ips.stats.ConnectionStatus }
