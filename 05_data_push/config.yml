# 支持InfluxDB推送的配置文件
# 使用docker-compose中配置的真实InfluxDB服务

service:
  name: "data-push"
  host: "0.0.0.0"
  port: 8085
  debug: true
  timeout: 30s
  max_workers: 10              # 最大并发处理设备数
  batch_size: 100              # 批量处理大小
  process_interval: 1s         # 处理间隔

# Redis配置 - 数据源
redis:
  host: "redis"
  port: 6379
  password: "" # mdc_redis_pass123
  db: 2
  pool_size: 20
  timeout: 5s
  retry_count: 3
  retry_delay: 1s

# SQLite配置 - 本地缓存
sqlite:
  data_dir: "./data/sqlite"
  file_pattern: "data_%s.db"    # 按日期命名: data_2025-05-27.db
  retention_days: 30            # 保留30天
  batch_size: 1000              # 批量插入大小
  flush_interval: 5s            # 刷新间隔
  max_connections: 10           # 最大连接数
  wal_mode: true                # 启用WAL模式提高性能
  sync_mode: "NORMAL"           # 同步模式

# NATS JetStream配置（备用）
nats:
  url: "nats://localhost:4222"
  subject: "device.data.processed"
  stream: "PROCESSED_STREAM"
  consumer: "data-push-consumer"
  batch_size: 50                # 批量推送大小
  timeout: 10s
  retry_count: 3
  retry_delay: 2s
  max_pending: 1000             # 最大待确认消息数

# InfluxDB配置（主要推送目标）
influxdb:
  url: "http://influxdb:8086"
  token: "mdc-token-123456789"  # docker-compose中配置的token
  organization: "mdc-org"       # docker-compose中配置的组织    
  bucket: "device-data"         # docker-compose中配置的bucket
  measurement: "sensor_data"
  batch_size: 100               # 批量写入大小
  flush_interval: 5s            # 刷新间隔
  timeout: 10s
  retry_count: 3
  retry_delay: 2s

# 数据推送配置
data_push:
  type: "influxdb"              # 推送类型: nats, influxdb, mixed, 使用InfluxDB
  nats:
    enabled: false              # 不启用NATS推送
    primary: false              # 不作为主要推送目标
    fallback: true              # 作为备用推送目标
  influxdb:
    enabled: true               # 启用InfluxDB推送
    primary: true               # 作为主要推送目标
    fallback: false             # 不作为备用推送目标

# 数据清洗配置
data_cleaning:
  script_file: "scripts/data_cleaning.js"
  script_timeout: 5s            # 脚本执行超时
  enable_cache: true            # 启用脚本缓存
  max_script_memory: 10         # 脚本最大内存(MB)

# 性能优化配置
performance:
  redis_scan_count: 100         # Redis扫描批次大小
  sqlite_retry_interval: 30s    # SQLite重试间隔
  nats_retry_interval: 10s      # NATS重试间隔
  memory_limit_mb: 512          # 内存限制
  gc_interval: 60s              # GC间隔
  metrics_interval: 30s         # 指标统计间隔

# 监控配置
monitoring:
  enable_metrics: true          # 启用指标收集
  metrics_port: 9090            # 指标端口
  health_check_interval: 30s    # 健康检查间隔
  alert_thresholds:
    redis_queue_size: 10000     # Redis队列大小告警阈值
    sqlite_file_size_mb: 1000   # SQLite文件大小告警阈值
    error_rate_percent: 5       # 错误率告警阈值
    memory_usage_percent: 80    # 内存使用率告警阈值

# 日志配置
logging:
  level: "info"
  format: "json"                # 生产环境使用JSON格式
  output: "logs/data-push-influxdb.log"
  rotation:
    enabled: true
    max_size: 100               # 100MB
    max_age: 14                 # 保留14天
    max_backups: 7              # 7个备份
    compress: true              # 启用压缩
  device_logs: true             # 是否记录设备数据日志，true时记录每个设备推送的数据到单独的日志文件

# 稳定性配置
stability:
  max_memory_mb: 512
  gc_threshold_mb: 256
  circuit_breaker:
    enabled: true
    failure_threshold: 10       # 失败阈值
    recovery_timeout: 60s       # 恢复超时
    half_open_requests: 5       # 半开状态请求数

# 设备离线检测配置
# 用于监控设备数据接收状态，自动检测设备离线并关闭对应的processor节省资源
device_monitor:
  enabled: true                    # 是否启用设备离线检测功能
  check_interval: 60               # 检查间隔(秒)，每隔多少秒检查一次设备状态
  offline_timeout: 300             # 离线超时(秒)，超过多少秒没有数据则判定为离线
  redis_key_prefix: "device:last_processed:" # Redis中存储设备最后处理时间的key前缀
  cleanup_interval: 3600           # 清理间隔(秒)，每隔多少秒清理过期的设备状态记录
  max_offline_devices: 1000        # 最大离线设备数量，防止内存泄露
  processor_shutdown_delay: 30     # 处理器关闭延迟(秒)，设备离线后延迟关闭processor
