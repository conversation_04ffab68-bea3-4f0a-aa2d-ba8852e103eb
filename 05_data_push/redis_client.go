/**
 * Redis客户端模块
 *
 * 功能概述：
 * 本模块提供Redis数据访问功能，负责从Redis读取设备数据，是数据处理流程的数据源，
 * 支持FIFO顺序读取、批量处理、连接池管理和性能监控
 *
 * 主要功能：
 * - 数据读取：从Redis按FIFO顺序读取设备数据
 * - 批量操作：支持批量读取和删除，提高性能
 * - 连接管理：Redis连接池管理和健康检查
 * - 统计监控：详细的操作统计和性能指标
 * - 错误处理：完善的错误处理和重试机制
 * - 数据清理：支持数据删除和队列管理
 *
 * 技术特性：
 * - 连接池：高效的Redis连接池管理
 * - Pipeline：使用Redis Pipeline提高批量操作性能
 * - FIFO保证：严格按照先进先出顺序读取数据
 * - 原子操作：使用Redis事务确保操作的原子性
 * - 性能监控：实时的操作统计和延迟监控
 *
 * 数据结构：
 * - 设备列表：device_list:{device_id} 存储设备数据键列表
 * - 设备数据：device:{device_id}:{timestamp}:{sequence} 存储具体数据
 * - FIFO顺序：通过序列号和时间戳确保数据顺序
 *
 * 性能优化：
 * - 连接复用：Redis连接池减少连接开销
 * - 批量操作：Pipeline批量读取和删除
 * - 智能排序：高效的序列号比较算法
 * - 错误恢复：自动重试和错误统计
 *
 * 业务价值：
 * - 数据完整性：确保设备数据按正确顺序处理
 * - 高性能：优化的批量操作和连接管理
 * - 可观测性：详细的统计信息和健康检查
 * - 可靠性：完善的错误处理和恢复机制
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

/**
 * Redis客户端结构体
 *
 * 功能：封装Redis连接和操作，提供设备数据的读取、删除和管理功能
 *
 * 架构设计：
 * - 连接管理：封装Redis客户端连接和配置
 * - 统计监控：实时统计操作性能和错误信息
 * - 并发安全：使用读写锁保护统计数据
 * - 配置驱动：基于配置文件的连接参数
 *
 * @struct RedisClient
 */
type RedisClient struct {
	/** Redis客户端实例，提供底层Redis操作 */
	client *redis.Client

	/** Redis配置信息，包含连接参数和性能设置 */
	config RedisConfig

	/** Redis操作统计信息，用于性能监控 */
	stats RedisStats

	/** 读写锁，保护统计数据的并发访问 */
	mutex sync.RWMutex
}

/**
 * Redis统计信息结构体
 *
 * 功能：记录Redis操作的详细统计信息，用于性能监控和问题诊断
 *
 * @struct RedisStats
 */
type RedisStats struct {
	/** 总读取操作次数，包括单次和批量读取 */
	TotalReads int64 `json:"total_reads"`

	/** 总写入操作次数，包括数据写入和列表操作 */
	TotalWrites int64 `json:"total_writes"`

	/** 总删除操作次数，包括单次和批量删除 */
	TotalDeletes int64 `json:"total_deletes"`

	/** 错误操作次数，用于计算错误率 */
	ErrorCount int64 `json:"error_count"`

	/** 最后一次错误的详细信息 */
	LastError string `json:"last_error"`

	/** 当前连接池中的连接数量 */
	ConnectionCount int `json:"connection_count"`

	/** 平均操作延迟，用于性能监控 */
	AverageLatency time.Duration `json:"average_latency"`

	/** 最后一次操作的时间戳 */
	LastOperationTime time.Time `json:"last_operation_time"`
}

// NewRedisClient 创建新的Redis客户端
func NewRedisClient(config RedisConfig) (*RedisClient, error) {
	// 创建Redis客户端配置
	addr := fmt.Sprintf("%s:%d", config.Host, config.Port)
	rdb := redis.NewClient(&redis.Options{
		Addr:            addr,
		Password:        config.Password,
		DB:              config.DB,
		PoolSize:        config.PoolSize,
		DialTimeout:     config.Timeout,
		ReadTimeout:     config.Timeout,
		WriteTimeout:    config.Timeout,
		MaxRetries:      config.RetryCount,
		MinRetryBackoff: config.RetryDelay,
		MaxRetryBackoff: config.RetryDelay * 3,
	})

	client := &RedisClient{
		client: rdb,
		config: config,
		stats:  RedisStats{},
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), config.Timeout)
	defer cancel()

	if err := client.client.Ping(ctx).Err(); err != nil {
		// 构建详细的错误信息和排查建议
		errorMsg := fmt.Sprintf("Redis连接失败: %v", err)

		// 添加连接信息
		errorMsg += fmt.Sprintf("\n\n📋 连接信息:")
		errorMsg += fmt.Sprintf("\n  地址: %s", addr)
		errorMsg += fmt.Sprintf("\n  数据库: %d", config.DB)
		errorMsg += fmt.Sprintf("\n  超时: %v", config.Timeout)
		errorMsg += fmt.Sprintf("\n  重试次数: %d", config.RetryCount)

		// 添加排查建议
		errorMsg += fmt.Sprintf("\n\n🔍 排查建议:")
		errorMsg += fmt.Sprintf("\n  1. 检查Redis服务是否运行:")
		errorMsg += fmt.Sprintf("\n     docker ps | grep redis")
		errorMsg += fmt.Sprintf("\n     或 systemctl status redis")

		errorMsg += fmt.Sprintf("\n\n  2. 检查Redis连接:")
		errorMsg += fmt.Sprintf("\n     redis-cli -h %s -p %d ping", config.Host, config.Port)
		if config.Password != "" {
			errorMsg += fmt.Sprintf("\n     redis-cli -h %s -p %d -a '密码' ping", config.Host, config.Port)
		}

		errorMsg += fmt.Sprintf("\n\n  3. 检查网络连接:")
		errorMsg += fmt.Sprintf("\n     telnet %s %d", config.Host, config.Port)
		errorMsg += fmt.Sprintf("\n     或 nc -zv %s %d", config.Host, config.Port)

		errorMsg += fmt.Sprintf("\n\n  4. 检查防火墙设置:")
		errorMsg += fmt.Sprintf("\n     确保端口 %d 未被防火墙阻止", config.Port)

		errorMsg += fmt.Sprintf("\n\n  5. 检查Docker服务:")
		errorMsg += fmt.Sprintf("\n     如果使用Docker，确保Redis容器正在运行:")
		errorMsg += fmt.Sprintf("\n     docker-compose ps")
		errorMsg += fmt.Sprintf("\n     docker-compose up -d redis")

		errorMsg += fmt.Sprintf("\n\n  6. 检查配置文件:")
		errorMsg += fmt.Sprintf("\n     确认config.yml中的Redis配置正确")
		errorMsg += fmt.Sprintf("\n     特别是host、port、password和db设置")

		// 根据错误类型提供特定建议
		if strings.Contains(err.Error(), "connection refused") {
			errorMsg += fmt.Sprintf("\n\n❌ 连接被拒绝 - Redis服务可能未启动")
			errorMsg += fmt.Sprintf("\n   解决方案: 启动Redis服务")
			errorMsg += fmt.Sprintf("\n   Docker: docker-compose up -d redis")
			errorMsg += fmt.Sprintf("\n   系统服务: sudo systemctl start redis")
		} else if strings.Contains(err.Error(), "timeout") {
			errorMsg += fmt.Sprintf("\n\n⏱️ 连接超时 - 网络或服务响应慢")
			errorMsg += fmt.Sprintf("\n   解决方案: 检查网络连接或增加超时时间")
		} else if strings.Contains(err.Error(), "no such host") {
			errorMsg += fmt.Sprintf("\n\n🌐 主机不存在 - 主机名解析失败")
			errorMsg += fmt.Sprintf("\n   解决方案: 检查主机名或使用IP地址")
		} else if strings.Contains(err.Error(), "authentication") {
			errorMsg += fmt.Sprintf("\n\n🔐 认证失败 - 密码错误")
			errorMsg += fmt.Sprintf("\n   解决方案: 检查Redis密码配置")
		}

		return nil, fmt.Errorf(errorMsg)
	}

	return client, nil
}

// GetDeviceList 获取所有设备列表
func (r *RedisClient) GetDeviceList(ctx context.Context) ([]string, error) {
	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	// 扫描所有设备列表键
	var devices []string
	iter := r.client.Scan(ctx, 0, "device_list:*", 0).Iterator()

	for iter.Next(ctx) {
		key := iter.Val()
		// 从 "device_list:device_id" 中提取 device_id
		if parts := strings.Split(key, ":"); len(parts) == 2 {
			devices = append(devices, parts[1])
		}
	}

	if err := iter.Err(); err != nil {
		r.updateStats(startTime, err)
		return nil, fmt.Errorf("failed to scan device list: %w", err)
	}

	r.stats.TotalReads++
	return devices, nil
}

// GetDeviceDataKeys 获取指定设备的数据键列表（按FIFO顺序）
func (r *RedisClient) GetDeviceDataKeys(ctx context.Context, deviceID string, limit int) ([]RedisDataItem, error) {
	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	listKey := fmt.Sprintf("device_list:%s", deviceID)

	// 获取设备数据键列表
	keys, err := r.client.LRange(ctx, listKey, 0, int64(limit-1)).Result()
	if err != nil {
		r.updateStats(startTime, err)
		return nil, fmt.Errorf("failed to get device data keys: %w", err)
	}

	if len(keys) == 0 {
		return []RedisDataItem{}, nil
	}

	// 批量获取数据
	pipe := r.client.Pipeline()
	cmds := make([]*redis.StringCmd, len(keys))

	for i, key := range keys {
		cmds[i] = pipe.Get(ctx, key)
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		r.updateStats(startTime, err)
		return nil, fmt.Errorf("failed to execute pipeline: %w", err)
	}

	// 解析结果
	var items []RedisDataItem
	for i, cmd := range cmds {
		data, err := cmd.Result()
		if err == redis.Nil {
			continue // 键不存在，跳过
		}
		if err != nil {
			continue // 其他错误，跳过这个键
		}

		// 解析数据
		var deviceData DeviceData
		if err := json.Unmarshal([]byte(data), &deviceData); err != nil {
			continue // 解析失败，跳过
		}

		item := RedisDataItem{
			Key:       keys[i],
			Data:      data,
			Timestamp: deviceData.Timestamp,
			Sequence:  deviceData.Sequence,
			DeviceID:  deviceData.DeviceID,
		}
		items = append(items, item)
	}

	// 按序列号排序确保FIFO顺序
	sort.Slice(items, func(i, j int) bool {
		return r.compareSequence(items[i].Sequence, items[j].Sequence)
	})

	r.stats.TotalReads++
	return items, nil
}

// compareSequence 比较序列号，用于FIFO排序
func (r *RedisClient) compareSequence(seq1, seq2 string) bool {
	// 尝试解析为数字进行比较
	if num1, err1 := strconv.ParseInt(seq1, 10, 64); err1 == nil {
		if num2, err2 := strconv.ParseInt(seq2, 10, 64); err2 == nil {
			return num1 < num2
		}
	}

	// 如果不是数字，按字符串比较
	return seq1 < seq2
}

// GetDeviceData 获取指定键的设备数据
func (r *RedisClient) GetDeviceData(ctx context.Context, key string) (*DeviceData, error) {
	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	data, err := r.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return nil, nil // 键不存在
	}
	if err != nil {
		r.updateStats(startTime, err)
		return nil, fmt.Errorf("failed to get device data: %w", err)
	}

	var deviceData DeviceData
	if err := json.Unmarshal([]byte(data), &deviceData); err != nil {
		r.updateStats(startTime, err)
		return nil, fmt.Errorf("failed to unmarshal device data: %w", err)
	}

	r.stats.TotalReads++
	return &deviceData, nil
}

// DeleteDeviceData 删除指定的设备数据
func (r *RedisClient) DeleteDeviceData(ctx context.Context, deviceID, key string) error {
	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	pipe := r.client.Pipeline()

	// 从设备列表中移除键
	listKey := fmt.Sprintf("device_list:%s", deviceID)
	pipe.LRem(ctx, listKey, 1, key)

	// 删除数据键
	pipe.Del(ctx, key)

	_, err := pipe.Exec(ctx)
	if err != nil {
		r.updateStats(startTime, err)
		return fmt.Errorf("failed to delete device data: %w", err)
	}

	r.stats.TotalDeletes++
	return nil
}

// BatchDeleteDeviceData 批量删除设备数据
func (r *RedisClient) BatchDeleteDeviceData(ctx context.Context, deviceID string, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	pipe := r.client.Pipeline()
	listKey := fmt.Sprintf("device_list:%s", deviceID)

	// 批量从设备列表中移除键
	for _, key := range keys {
		pipe.LRem(ctx, listKey, 1, key)
	}

	// 批量删除数据键
	pipe.Del(ctx, keys...)

	_, err := pipe.Exec(ctx)
	if err != nil {
		r.updateStats(startTime, err)
		return fmt.Errorf("failed to batch delete device data: %w", err)
	}

	r.stats.TotalDeletes += int64(len(keys))
	return nil
}

// GetDeviceQueueSize 获取设备队列大小
func (r *RedisClient) GetDeviceQueueSize(ctx context.Context, deviceID string) (int64, error) {
	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	listKey := fmt.Sprintf("device_list:%s", deviceID)
	size, err := r.client.LLen(ctx, listKey).Result()
	if err != nil {
		r.updateStats(startTime, err)
		return 0, fmt.Errorf("failed to get device queue size: %w", err)
	}

	r.stats.TotalReads++
	return size, nil
}

// GetTotalQueueSize 获取所有设备的总队列大小
func (r *RedisClient) GetTotalQueueSize(ctx context.Context) (int64, error) {
	devices, err := r.GetDeviceList(ctx)
	if err != nil {
		return 0, err
	}

	var totalSize int64
	for _, deviceID := range devices {
		size, err := r.GetDeviceQueueSize(ctx, deviceID)
		if err != nil {
			continue // 忽略单个设备的错误
		}
		totalSize += size
	}

	return totalSize, nil
}

// HealthCheck 健康检查
func (r *RedisClient) HealthCheck(ctx context.Context) error {
	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	if err := r.client.Ping(ctx).Err(); err != nil {
		r.updateStats(startTime, err)
		return fmt.Errorf("Redis health check failed: %w", err)
	}

	return nil
}

// GetStats 获取统计信息
func (r *RedisClient) GetStats() RedisStats {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// 获取连接池统计信息
	poolStats := r.client.PoolStats()
	r.stats.ConnectionCount = int(poolStats.TotalConns)

	return r.stats
}

// updateStats 更新统计信息
func (r *RedisClient) updateStats(startTime time.Time, err error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	latency := time.Since(startTime)
	r.stats.LastOperationTime = time.Now()

	// 更新平均延迟
	if r.stats.AverageLatency == 0 {
		r.stats.AverageLatency = latency
	} else {
		r.stats.AverageLatency = (r.stats.AverageLatency + latency) / 2
	}

	// 更新错误信息
	if err != nil {
		r.stats.ErrorCount++
		r.stats.LastError = err.Error()
	}
}

// Close 关闭Redis客户端
func (r *RedisClient) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// GetConnectionInfo 获取连接信息
func (r *RedisClient) GetConnectionInfo() map[string]interface{} {
	poolStats := r.client.PoolStats()

	return map[string]interface{}{
		"total_connections": int(poolStats.TotalConns),
		"idle_connections":  int(poolStats.IdleConns),
		"stale_connections": int(poolStats.StaleConns),
		"hits":              int(poolStats.Hits),
		"misses":            int(poolStats.Misses),
		"timeouts":          int(poolStats.Timeouts),
	}
}

// FlushDeviceData 清空指定设备的所有数据（用于测试）
func (r *RedisClient) FlushDeviceData(ctx context.Context, deviceID string) error {
	startTime := time.Now()
	defer r.updateStats(startTime, nil)

	listKey := fmt.Sprintf("device_list:%s", deviceID)

	// 获取所有键
	keys, err := r.client.LRange(ctx, listKey, 0, -1).Result()
	if err != nil {
		r.updateStats(startTime, err)
		return fmt.Errorf("failed to get device keys: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	// 删除所有数据键和列表
	pipe := r.client.Pipeline()
	pipe.Del(ctx, keys...)
	pipe.Del(ctx, listKey)

	_, err = pipe.Exec(ctx)
	if err != nil {
		r.updateStats(startTime, err)
		return fmt.Errorf("failed to flush device data: %w", err)
	}

	r.stats.TotalDeletes += int64(len(keys) + 1)
	return nil
}
