/**
 * 数据模型定义模块
 *
 * 功能概述：
 * 本模块定义了数据处理推送服务中使用的所有数据结构和模型，
 * 包括设备数据、处理结果、系统指标、健康状态等核心数据类型
 *
 * 主要功能：
 * - 数据结构定义：定义完整的数据流转过程中的数据结构
 * - 序列化支持：提供JSON序列化和反序列化方法
 * - 数据验证：提供数据有效性验证方法
 * - 排序支持：提供FIFO排序所需的排序键生成
 * - 类型安全：使用强类型确保数据的正确性
 *
 * 数据流转模型：
 * 1. DeviceData：设备原始数据，从Redis读取
 * 2. ProcessedData：清洗后数据，经过JavaScript处理
 * 3. NATSMessage：推送消息，发送到NATS JetStream
 * 4. SQLiteRecord：本地缓存记录，用于故障恢复
 *
 * 监控模型：
 * - ProcessingStats：处理统计信息
 * - SystemMetrics：系统性能指标
 * - HealthStatus：健康状态信息
 * - ComponentHealth：组件健康状态
 * - Alert：告警信息
 *
 * 设计特性：
 * - 完整性：覆盖数据处理全流程的数据结构
 * - 一致性：统一的字段命名和数据格式
 * - 可扩展：易于添加新的字段和数据类型
 * - 性能优化：合理的数据结构设计，减少内存占用
 *
 * 业务价值：
 * - 数据标准化：统一的数据格式，便于系统集成
 * - 类型安全：编译时类型检查，减少运行时错误
 * - 可维护性：清晰的数据结构，便于代码维护
 * - 可观测性：丰富的监控和统计数据结构
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */
package main

import (
	"encoding/json"
	"time"
)

/**
 * 设备原始数据结构体
 *
 * 功能：表示从Redis读取的设备原始数据，是数据处理流程的起点
 *
 * 数据来源：
 * - Redis队列：从02_device_collect服务写入的设备数据
 * - 数据格式：JSON序列化的设备数据
 * - 读取顺序：按FIFO顺序读取，保证数据处理顺序
 *
 * 使用场景：
 * - 数据读取：从Redis队列读取设备数据
 * - 数据验证：验证数据完整性和有效性
 * - 数据清洗：作为JavaScript清洗脚本的输入
 * - 数据追踪：通过序列号追踪数据处理流程
 *
 * @struct DeviceData
 */
type DeviceData struct {
	/** 设备唯一标识符，如"FANUC_001", "SIEMENS_002" */
	DeviceID string `json:"device_id"`

	/** 数据类型，如"production", "status", "alarm", "maintenance" */
	DataType string `json:"data_type"`

	/** 数据产生的原始时间戳，设备端时间 */
	Timestamp time.Time `json:"timestamp"`

	/** 设备原始数据，包含所有传感器和状态信息 */
	RawData map[string]interface{} `json:"raw_data"`

	/** 设备物理位置，如"车间A-产线1-工位3" */
	Location string `json:"location"`

	/** 设备元数据，包含位置、设备信息等，兼容shared/models格式 */
	Metadata map[string]interface{} `json:"metadata,omitempty"`

	/** 序列号，用于FIFO排序和数据去重，格式：时间戳+递增序号 */
	Sequence string `json:"sequence"`
}

// ProcessedData 处理后的数据结构
type ProcessedData struct {
	DeviceID         string                 `json:"device_id"`         // 设备ID
	DataType         string                 `json:"data_type"`         // 数据类型
	Timestamp        time.Time              `json:"timestamp"`         // 原始时间戳
	ProcessedAt      time.Time              `json:"processed_at"`      // 处理时间戳
	RawData          map[string]interface{} `json:"raw_data"`          // 原始数据
	ProcessedData    map[string]interface{} `json:"processed_data"`    // 处理后数据
	Metadata         DataMetadata           `json:"metadata"`          // 数据处理元数据
	OriginalMetadata map[string]interface{} `json:"original_metadata"` // 原始设备元数据（设备名称、品牌、型号等）
	QualityScore     float64                `json:"quality_score"`     // 数据质量评分
	Sequence         string                 `json:"sequence"`          // 序列号
	Location         string                 `json:"location"`          // 设备位置
	Error            bool                   `json:"error"`             // 是否有错误
	ErrorMessage     string                 `json:"error_message"`     // 错误信息
}

// DataMetadata 数据元数据
type DataMetadata struct {
	CleaningVersion string   `json:"cleaning_version"` // 清洗版本
	CleaningRules   []string `json:"cleaning_rules"`   // 应用的清洗规则
	ProcessingTime  float64  `json:"processing_time"`  // 处理耗时(毫秒)
	Source          string   `json:"source"`           // 数据来源
}

// RedisDataItem Redis中的数据项
type RedisDataItem struct {
	Key       string    `json:"key"`       // Redis键
	Data      string    `json:"data"`      // 数据内容(JSON字符串)
	Timestamp time.Time `json:"timestamp"` // 时间戳
	Sequence  string    `json:"sequence"`  // 序列号
	DeviceID  string    `json:"device_id"` // 设备ID
}

// SQLiteRecord SQLite记录结构
type SQLiteRecord struct {
	ID            int64     `db:"id"`             // 主键ID
	DeviceID      string    `db:"device_id"`      // 设备ID
	Sequence      string    `db:"sequence"`       // 序列号
	DataType      string    `db:"data_type"`      // 数据类型
	RawData       string    `db:"raw_data"`       // 原始数据(JSON)
	ProcessedData string    `db:"processed_data"` // 处理后数据(JSON)
	Timestamp     time.Time `db:"timestamp"`      // 时间戳
	CreatedAt     time.Time `db:"created_at"`     // 创建时间
	RetryCount    int       `db:"retry_count"`    // 重试次数
	LastError     string    `db:"last_error"`     // 最后错误信息
	Status        string    `db:"status"`         // 状态: pending, processing, failed
}

// NATSMessage NATS消息结构
type NATSMessage struct {
	MessageID   string        `json:"message_id"`   // 消息ID
	DeviceID    string        `json:"device_id"`    // 设备ID
	DataType    string        `json:"data_type"`    // 数据类型
	Timestamp   time.Time     `json:"timestamp"`    // 时间戳
	ProcessedAt time.Time     `json:"processed_at"` // 处理时间
	Data        ProcessedData `json:"data"`         // 处理后的数据
	Sequence    string        `json:"sequence"`     // 序列号
	Source      string        `json:"source"`       // 数据来源: redis, sqlite
}

// ProcessingStats 处理统计信息
type ProcessingStats struct {
	DeviceID           string    `json:"device_id"`            // 设备ID
	TotalProcessed     int64     `json:"total_processed"`      // 总处理数量
	SuccessCount       int64     `json:"success_count"`        // 成功数量
	ErrorCount         int64     `json:"error_count"`          // 错误数量
	RedisProcessed     int64     `json:"redis_processed"`      // Redis处理数量
	SQLiteProcessed    int64     `json:"sqlite_processed"`     // SQLite处理数量
	DataPushed         int64     `json:"data_pushed"`          // 数据推送数量
	LastProcessedTime  time.Time `json:"last_processed_time"`  // 最后处理时间
	AverageProcessTime float64   `json:"average_process_time"` // 平均处理时间(毫秒)
	QueueSize          int       `json:"queue_size"`           // 当前队列大小
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	Timestamp         time.Time `json:"timestamp"`          // 时间戳
	MemoryUsageMB     float64   `json:"memory_usage_mb"`    // 内存使用(MB)
	GoroutineCount    int       `json:"goroutine_count"`    // 协程数量
	GCCount           uint32    `json:"gc_count"`           // GC次数
	RedisConnections  int       `json:"redis_connections"`  // Redis连接数
	SQLiteConnections int       `json:"sqlite_connections"` // SQLite连接数
	NATSConnections   int       `json:"nats_connections"`   // NATS连接数
	ActiveWorkers     int       `json:"active_workers"`     // 活跃工作协程数
	QueuedItems       int       `json:"queued_items"`       // 队列中的项目数
	ProcessingRate    float64   `json:"processing_rate"`    // 处理速率(条/秒)
}

// HealthStatus 健康状态
type HealthStatus struct {
	Service    string                     `json:"service"`    // 服务名称
	Status     string                     `json:"status"`     // 状态: healthy, degraded, unhealthy
	Timestamp  time.Time                  `json:"timestamp"`  // 时间戳
	Version    string                     `json:"version"`    // 版本
	Uptime     time.Duration              `json:"uptime"`     // 运行时间
	Components map[string]ComponentHealth `json:"components"` // 组件健康状态
	Metrics    SystemMetrics              `json:"metrics"`    // 系统指标
	Alerts     []Alert                    `json:"alerts"`     // 告警信息
}

// ComponentHealth 组件健康状态
type ComponentHealth struct {
	Status       string    `json:"status"`        // 状态
	LastCheck    time.Time `json:"last_check"`    // 最后检查时间
	ErrorCount   int       `json:"error_count"`   // 错误次数
	LastError    string    `json:"last_error"`    // 最后错误
	ResponseTime float64   `json:"response_time"` // 响应时间(毫秒)
}

// Alert 告警信息
type Alert struct {
	Level     string    `json:"level"`     // 告警级别: info, warning, error, critical
	Message   string    `json:"message"`   // 告警消息
	Component string    `json:"component"` // 相关组件
	Timestamp time.Time `json:"timestamp"` // 告警时间
	Resolved  bool      `json:"resolved"`  // 是否已解决
}

// DeviceProcessor 设备处理器状态
type DeviceProcessor struct {
	DeviceID        string    `json:"device_id"`         // 设备ID
	Status          string    `json:"status"`            // 状态: idle, processing, error
	LastProcessTime time.Time `json:"last_process_time"` // 最后处理时间
	QueueSize       int       `json:"queue_size"`        // 队列大小
	ProcessingCount int64     `json:"processing_count"`  // 处理计数
	ErrorCount      int64     `json:"error_count"`       // 错误计数
	LastError       string    `json:"last_error"`        // 最后错误
}

// ToJSON 将结构体转换为JSON字符串
func (d *DeviceData) ToJSON() (string, error) {
	data, err := json.Marshal(d)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析设备数据
func (d *DeviceData) FromJSON(jsonStr string) error {
	// 先进行标准的JSON解析
	if err := json.Unmarshal([]byte(jsonStr), d); err != nil {
		return err
	}

	// 如果Location为空但Metadata中有location信息，则提取位置信息
	if d.Location == "" && d.Metadata != nil {
		if location, ok := d.Metadata["location"].(string); ok && location != "" {
			d.Location = location
		}
	}

	return nil
}

// ToJSON 将处理后数据转换为JSON字符串
func (p *ProcessedData) ToJSON() (string, error) {
	data, err := json.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析处理后数据
func (p *ProcessedData) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), p)
}

// ToJSON 将NATS消息转换为JSON字符串
func (n *NATSMessage) ToJSON() (string, error) {
	data, err := json.Marshal(n)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析NATS消息
func (n *NATSMessage) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), n)
}

// IsValid 验证设备数据是否有效
func (d *DeviceData) IsValid() bool {
	return d.DeviceID != "" && d.DataType != "" && d.RawData != nil
}

// IsValid 验证处理后数据是否有效
func (p *ProcessedData) IsValid() bool {
	return p.DeviceID != "" && p.DataType != "" && (p.ProcessedData != nil || p.Error)
}

// GetSortKey 获取用于FIFO排序的键
func (d *DeviceData) GetSortKey() string {
	if d.Sequence != "" {
		return d.Sequence
	}
	return d.Timestamp.Format("20060102150405.000000")
}

// GetSortKey 获取用于FIFO排序的键
func (p *ProcessedData) GetSortKey() string {
	if p.Sequence != "" {
		return p.Sequence
	}
	return p.Timestamp.Format("20060102150405.000000")
}

// GetSortKey 获取用于FIFO排序的键
func (r *SQLiteRecord) GetSortKey() string {
	if r.Sequence != "" {
		return r.Sequence
	}
	return r.Timestamp.Format("20060102150405.000000")
}
