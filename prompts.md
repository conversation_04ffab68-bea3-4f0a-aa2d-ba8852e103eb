
├── 03_data_cleaning/        # 数据清洗转换服务
├── 04_data_push/           # 数据推送服务

这两个程序的功能合并成一个程序, 新程序命名为: 05_data_push, 功能完成后将会删除 03_data_cleaning/, 04_data_push/
- 同一台设备 FIFO 读取 02_device_collect/ 保存在 redis 的数据
- 同一台设备 FIFO 使用 goja 脚本(data_cleaning.js) 进行数据清洗,
- 清洗后的数据尝试推送到 nat_jetstream 服务器 (同一台设备 FIFO)
- 推送成功, 删除 redis 对应的记录
- 推送失败, 保存到 本地 sqlite,  (同一台设备 FIFO), 优先将sqlite 的记录尝试重推送到 nat_jetstream 服务器, 推送成功也删除 sqlite 对应的记录, 同时删除 redis 对应的记录
- 本地 sqlite 每天一个文件, 当之前的sqlite 的记录都成功推送到 nat_jetstream 服务器, 数据表已空时, 删除该 sqlite 文件.
- 同一台设备 无论是获取数据, 处理数据, 推送数据 都遵从FIFO规则, 建议使用 redis 的 key (02_device_collect 实现了唯一序号) 进行排序
- 程序要稳定性和高可靠性
- 处理好各种有可能出现的异常情况
- 有没有其它可能性会影响到程序的稳定
- 充分利用资源, 并行处理数据, 提高效率. (保证 FIFO, 数据不丢失的条件)
- 在推送失败时, 有可能会缓存比较大量的数据, 如果有比较 sqlite 更好的方案, 可以替用 sqltie, 以提高性能
- 就算推送失败时, 也要考虑 redis 不停在接收新数据, 怎样平衡性能, 同时要保证数据不丢失. 也就是减少或最快消化掉redis的数据, 寻找最佳方案



# fwlib

- 参考 01_device_api/fanuc/ref 所有代码, 梳理.
- 在 @01_device_api /faunc 目录下创建一个 go 程序
- 使用 fwlib  采集设备数据 (参考 ref/的代码)
- 支持单机/多机采集 (配置文件)
- 提供 restful api 供用户调用
- 高可用设计: 完善的异常处理和恢复机制
- 运行在 docker linux:x86    
- 需要在 docker 环境运行, 开发期间, 代码放到 docker volumn, 用脚本执行 如: go run main.go

# OP2
- 移除模拟数据功能, 用真机运行调试
- - focas 只能在 linux 环境运行, macos 运行不了, 要在 docker运行  调试, 遇到问题就想办法解决
- Focas在Linux 运行条件请看 01_device_api/fanuc/docker/lib/FOCAL2_Linux.pdf
- foacs 按要求放入到 docker, 代码放到 docker volumn, 用脚本执行, 在 docker 环境运行调试,  在本地修改docker volumn 的代码


# fanuc focas v2
- 支持单机/多机采集. 设备信息可以选择从配置文件获取, 也可以从 restful api 获取设备信息
- 每台设备独立数据采集, 独立推送
- 推送失败可以缓存, 连接成功后同步推送
- 推送通道使用设计模式, 支持推送到 restful api服务器, mqtt, nas jetstream, 方便快速切换
- 提供 restful api 供用户调用
* 可以启动所有设备的采集
* 可以停止所有设备的采集
* 可以启动某台设备的采集
* 可以停止某台设备的采集
* 可以重新获取采集设备的清单
  ** 有新加入的设备根据设备配置是自动启动还是手动启动
  ** 清单中不存在的设备自动停止并移除
* 可以设置推送服务的信息
- 高可用设计: 完善的异常处理和恢复机制, stability 机制
