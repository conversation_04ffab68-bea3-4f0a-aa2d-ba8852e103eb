# 设备数据模拟生成器

## 📋 **项目简介**

设备数据模拟生成器是一个用Go语言开发的测试工具，专门用于向设备数据采集服务发送模拟的设备数据。该工具支持多种设备类型、批量发送、实时统计和优雅关闭等功能。

### 🎯 **主要功能**

- **多设备类型支持** - 温度传感器、运动检测器、门磁传感器、空气质量监测器
- **智能数据生成** - 基于配置的随机数据生成，符合真实设备数据特征
- **批量发送** - 支持批量数据发送，提高测试效率
- **实时统计** - 详细的发送统计和性能监控
- **重试机制** - 自动重试失败的请求，确保数据传输可靠性
- **优雅关闭** - 支持信号处理和优雅关闭

### 🏗️ **系统架构**

```
配置文件 → 数据生成器 → HTTP客户端 → 设备数据采集服务
    ↓           ↓            ↓
共享模块     统计管理器   响应处理
(logger/config/models)
```

### 📦 **模块依赖**

- **共享模块 (shared/)** - 使用项目统一的日志、配置和数据模型
- **数据生成器** - 基于gofakeit的智能数据生成
- **HTTP客户端** - 支持重试和统计的HTTP发送客户端
- **统计管理器** - 实时性能监控和报告生成

## 🚀 **快速开始**

### 1. **环境要求**

- Go 1.21 或更高版本
- 网络连接到设备数据采集服务

### 2. **安装依赖**

```bash
cd 02_generate_data
go mod tidy
```

### 3. **配置文件**

编辑 `config.yml` 文件，设置目标服务地址和生成参数：

```yaml
target:
  url: "http://localhost:8081/api/v1/device/data"
  timeout: 10
  retry_count: 3

generator:
  device_count: 10
  interval: 5
  batch_size: 1
  duration: 300  # 0表示无限运行
```

### 4. **启动程序**

#### 方式一：使用启动脚本（推荐）
```bash
./start.sh
```

#### 方式二：直接运行
```bash
go run .
```

#### 方式三：编译后运行
```bash
go build -o data-generator
./data-generator
```
