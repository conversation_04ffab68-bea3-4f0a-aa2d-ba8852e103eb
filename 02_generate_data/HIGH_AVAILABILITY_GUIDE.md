# 02_generate_data 高可用性设计指南

## 概述

本文档描述了02_generate_data服务的高可用性设计和实现，确保在外部依赖服务不可用时也能正常启动和运行。

## 问题背景

原始问题：
- 02_generate_data服务依赖12_server_api提供设备配置
- 当12_server_api服务不可用时，程序启动失败
- 缺乏降级机制，影响系统的可用性和稳定性

## 高可用性解决方案

### 1. 智能降级策略

#### 1.1 多层次检查机制

```bash
# 启动脚本检查（run.sh）
🔍 检查配置源和依赖服务...
📋 配置源类型: api
📡 API地址: http://localhost:9005/api/public/devices/configs

# 网络连通性检查
if ! nc -z $API_HOST $API_PORT 2>/dev/null; then
    echo "⚠️  警告: API服务不可达"
    # 提供详细的排查建议
fi
```

#### 1.2 应用层降级逻辑

```go
// createDevicesFromAPI 高可用版本
func (dm *DeviceManager) createDevicesFromAPI() error {
    // 尝试从API获取设备配置
    apiDevices, err := loadDeviceConfigsFromAPI(dm.config.Devices.ConfigAPI)
    if err != nil {
        // API失败时自动降级到模板配置
        logger.Warnf("🔄 API不可用，启用降级策略...")
        logger.Warnf("   自动切换到模板配置模式")
        
        // 检查模板配置可用性
        if len(dm.config.DeviceTemplates) == 0 {
            return fmt.Errorf("API不可用且无模板配置可降级: %v", err)
        }
        
        // 使用模板配置进行降级初始化
        return dm.createDevicesFromTemplateAsFallback()
    }
    
    // API数据为空时的降级处理
    if len(apiDevices) == 0 {
        logger.Warnf("⚠️  API返回的设备配置为空，启用降级策略...")
        return dm.createDevicesFromTemplateAsFallback()
    }
    
    // 正常API模式处理...
}
```

### 2. 详细错误诊断

#### 2.1 API连接错误分析

```go
// 增强的API错误处理
func loadDeviceConfigsFromAPI(apiURL string) ([]APIDeviceConfig, error) {
    resp, err := client.Get(apiURL)
    if err != nil {
        // 构建详细的错误信息
        errorMsg := fmt.Sprintf("请求设备配置API失败: %v", err)
        
        // 根据错误类型提供具体建议
        if contains(err.Error(), "connection refused") {
            errorMsg += "\n\n❌ 连接被拒绝 - API服务可能未启动"
            errorMsg += "\n  解决方案:"
            errorMsg += "\n    1. 启动12_server_api服务: cd 12_server_api && ./run.sh"
            errorMsg += "\n    2. 检查端口占用: lsof -i :9005"
        } else if contains(err.Error(), "timeout") {
            errorMsg += "\n\n⏱️ 连接超时 - 服务响应慢或网络问题"
            errorMsg += "\n  解决方案:"
            errorMsg += "\n    1. 检查网络连接: ping localhost"
            errorMsg += "\n    2. 检查服务负载: top | grep server_api"
        }
        
        return nil, fmt.Errorf(errorMsg)
    }
    // ...
}
```

#### 2.2 HTTP状态码分析

```go
if resp.StatusCode != http.StatusOK {
    errorMsg := fmt.Sprintf("设备配置API返回错误状态: %d", resp.StatusCode)
    
    switch resp.StatusCode {
    case 404:
        errorMsg += "\n  404 Not Found - API端点不存在"
        errorMsg += "\n  检查API路径是否正确"
    case 500:
        errorMsg += "\n  500 Internal Server Error - 服务器内部错误"
        errorMsg += "\n  检查12_server_api服务日志"
    case 503:
        errorMsg += "\n  503 Service Unavailable - 服务不可用"
        errorMsg += "\n  服务可能正在启动或过载"
    }
    
    return nil, fmt.Errorf(errorMsg)
}
```

### 3. 启动脚本增强

#### 3.1 配置源检测

```bash
# 读取配置源类型
CONFIG_SOURCE=$(grep "config_source:" config.yml | awk '{print $2}' | tr -d '"')
echo "📋 配置源类型: $CONFIG_SOURCE"

if [ "$CONFIG_SOURCE" = "api" ]; then
    # API模式的依赖检查
    API_URL=$(grep "config_api:" config.yml | awk '{print $2}' | tr -d '"')
    API_HOST="localhost"
    API_PORT="9005"
    
    # 检查API服务可达性
    if ! nc -z $API_HOST $API_PORT 2>/dev/null; then
        echo "⚠️  警告: API服务不可达"
        echo "🔧 API服务排查建议:"
        echo "  1. 启动12_server_api服务: cd ../12_server_api && ./run.sh"
        echo "  2. 检查服务状态: curl -s http://$API_HOST:$API_PORT/health"
        echo "  3. 临时解决方案: 修改config.yml中的config_source为\"template\""
        echo "⚠️  程序将尝试启动，如果API不可用会自动降级到模板配置"
    else
        echo "✅ API服务检查通过"
    fi
elif [ "$CONFIG_SOURCE" = "template" ]; then
    echo "✅ 模板配置模式 - 无需外部依赖"
fi
```

### 4. 主程序错误处理

#### 4.1 初始化失败处理

```go
// 增强的初始化错误处理
if err := manager.Initialize(); err != nil {
    fmt.Printf("❌ 设备初始化失败\n")
    fmt.Printf("错误详情: %v\n", err)
    fmt.Printf("\n💡 解决建议:\n")
    
    if config.Devices.ConfigSource == "api" {
        fmt.Printf("   当前配置源: API模式\n")
        fmt.Printf("   1. 检查12_server_api服务: cd 12_server_api && ./run.sh\n")
        fmt.Printf("   2. 测试API连接: curl -s %s\n", config.Devices.ConfigAPI)
        fmt.Printf("   3. 临时解决方案: 修改config.yml中config_source为\"template\"\n")
    } else {
        fmt.Printf("   当前配置源: 模板模式\n")
        fmt.Printf("   1. 检查config.yml中的device_templates配置\n")
        fmt.Printf("   2. 确保至少有一个设备模板配置\n")
    }
    
    log.Fatalf("程序退出")
}
```

## 测试验证

### 1. API服务不可用场景

```bash
# 测试场景：12_server_api服务未启动
$ ./run.sh
⚠️  警告: API服务 (localhost:9005) 不可达
🔧 API服务排查建议:
  1. 启动12_server_api服务: cd ../12_server_api && ./run.sh
  ...
⚠️  程序将尝试启动，如果API不可用会自动降级到模板配置

# 结果：自动降级到模板配置
🔄 API不可用，启用降级策略...
   自动切换到模板配置模式
✅ 降级策略成功：已使用模板配置创建 12 个设备
```

### 2. API服务可用但数据为空场景

```bash
# 测试场景：API服务运行但MongoDB无数据
$ curl -s http://localhost:9005/api/public/devices/configs
{"data":null,"message":"获取设备配置成功","success":true}

# 结果：自动降级到模板配置
⚠️  API返回的设备配置为空，启用降级策略...
✅ 使用模板配置进行降级初始化...
✅ 降级策略成功：已使用模板配置创建 12 个设备
```

### 3. API服务正常场景

```bash
# 测试场景：API服务正常且有数据
$ curl -s http://localhost:9005/api/public/devices/configs
{"data":[{"id":"fanuc_001","name":"FANUC CNC机床#1",...}],"success":true}

# 结果：正常使用API配置
✅ API服务检查通过 (localhost:9005)
✅ API连接测试成功
📱 从API获取到 3 个设备配置
   ✅ 创建设备 1/3: fanuc_001 (FANUC CNC机床#1)
   ✅ 创建设备 2/3: fanuc_002 (FANUC CNC机床#2)
   ✅ 创建设备 3/3: temp_001 (温度传感器#1)
✅ 成功从API初始化 3 个设备
```

## 技术架构

### 1. 降级策略架构

```
API配置模式 (主要)
    ↓
API连接检查
    ↓
API数据获取
    ↓
数据验证
    ↓
[失败] → 降级策略 → 模板配置模式 (备用)
    ↓
设备创建成功
```

### 2. 错误处理层次

```
1. 网络层错误 (连接失败、超时)
    ↓
2. HTTP层错误 (状态码、响应格式)
    ↓
3. 业务层错误 (数据为空、格式错误)
    ↓
4. 降级策略 (模板配置)
```

## 配置管理

### 1. 配置源切换

```yaml
# config.yml
devices:
  config_source: "api"                             # 主要模式：API
  config_api: "http://localhost:9005/api/public/devices/configs"
  refresh_interval: 60

# 降级时自动使用
device_templates:                                  # 备用模式：模板
  - name: "fanuc_cnc"
    count: 2
    # ...
```

### 2. 运行时配置

```go
// 运行时动态切换配置源
func (dm *DeviceManager) createDevicesFromTemplateAsFallback() error {
    // 临时保存原始配置源
    originalConfigSource := dm.config.Devices.ConfigSource
    
    // 临时切换到模板模式
    dm.config.Devices.ConfigSource = "template"
    
    // 调用模板创建函数
    err := dm.createDevicesFromTemplate()
    
    // 恢复原始配置源
    dm.config.Devices.ConfigSource = originalConfigSource
    
    return err
}
```

## 监控和日志

### 1. 结构化日志

```json
{
  "level": "warn",
  "msg": "API不可用，启用降级策略...",
  "config_source": "api",
  "api_url": "http://localhost:9005/api/public/devices/configs",
  "fallback_mode": "template",
  "template_count": 4,
  "time": "2025-06-07T12:00:00Z"
}
```

### 2. 状态指标

```go
type HighAvailabilityMetrics struct {
    APIAttempts      int64  // API尝试次数
    APIFailures      int64  // API失败次数
    FallbackTriggers int64  // 降级触发次数
    CurrentMode      string // 当前运行模式
    LastAPICheck     time.Time // 最后API检查时间
}
```

## 最佳实践

### 1. 部署建议

1. **预检查**：部署前确保模板配置完整
2. **监控**：监控API服务的可用性
3. **告警**：降级事件的及时告警
4. **恢复**：API服务恢复后的自动切换

### 2. 运维建议

1. **日志监控**：关注降级事件的日志
2. **性能对比**：API模式vs模板模式的性能差异
3. **数据一致性**：确保两种模式的数据格式一致
4. **定期测试**：定期测试降级机制的有效性

## 总结

通过实施高可用性设计，02_generate_data服务现在具备：

1. **智能降级**：API不可用时自动切换到模板配置
2. **详细诊断**：提供具体的错误分析和解决建议
3. **预防检查**：启动前检测依赖服务状态
4. **用户友好**：清晰的错误信息和操作指导
5. **零停机**：外部依赖失败不影响服务启动

这确保了系统在各种异常情况下都能保持稳定运行，大大提升了系统的可靠性和可维护性。
