# 构建阶段 - 用于编译Go应用程序
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制go mod文件和shared模块
# 先复制依赖文件以利用Docker缓存层
COPY 02_generate_data/go.mod 02_generate_data/go.sum ./
COPY shared ./shared

# 修改go.mod中的shared路径，适配Docker构建环境
RUN sed -i 's|replace shared => ../shared|replace shared => ./shared|' go.mod

# 下载Go模块依赖
RUN go mod download

# 复制源代码到容器中
COPY 02_generate_data/ .

# 再次确保go.mod中的shared路径正确
RUN sed -i 's|replace shared => ../shared|replace shared => ./shared|' go.mod

# 构建应用程序
# CGO_ENABLED=0: 禁用CGO以生成静态链接的二进制文件
# GOOS=linux: 指定目标操作系统为Linux
# -a: 强制重新构建所有包
# -installsuffix cgo: 为构建的包添加后缀
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段 - 最小化的运行时环境
FROM alpine:latest

# 安装必要的系统包
# ca-certificates: SSL证书，用于HTTPS请求
# tzdata: 时区数据
RUN apk --no-cache add ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 从构建阶段复制编译好的二进制文件
COPY --from=builder /app/main .

# 创建必要的目录
# 数据目录用于存储生成的数据文件
# 日志目录用于存储应用日志
RUN mkdir -p /data /app/logs

# 设置时区为上海时间
ENV TZ=Asia/Shanghai

# 暴露端口（如果应用有HTTP服务端口，根据实际情况调整）
# 注意：02_generate_data主要是数据生成器，可能不需要暴露端口
# EXPOSE 8080

# 运行应用程序
CMD ["./main"]
