# MDC 数据生成器 Docker 部署指南

本文档说明如何使用 Docker 部署和运行 MDC 数据生成器服务。

## 📁 创建的文件

基于 `02_device_collect` 项目模板，为 `02_generate_data` 项目创建了以下文件：

### 1. Dockerfile
- **路径**: `02_generate_data/Dockerfile`
- **功能**: 多阶段构建配置，用于构建 Go 应用的 Docker 镜像
- **特点**: 
  - 使用 `golang:1.21-alpine` 作为构建环境
  - 使用 `alpine:latest` 作为运行环境
  - 支持 shared 模块依赖
  - 镜像大小优化（约 9.43MB）

### 2. Docker 构建脚本
- **路径**: `02_generate_data/docker_build_mdc_generate_data_amd64.sh`
- **功能**: 自动化构建 AMD64 架构的 Docker 镜像
- **特点**:
  - 启用 Docker BuildKit 提升构建性能
  - 自动更新相关配置文件中的版本号
  - 提供详细的构建日志和使用提示

### 3. Docker 推送脚本
- **路径**: `02_generate_data/docker_push_mdc_generate_data.sh`
- **功能**: 将构建的镜像推送到阿里云容器镜像服务
- **配置**: 
  - 镜像仓库: `crpi-miowmruvplz7jr0c.cn-shenzhen.personal.cr.aliyuncs.com/c2studio/mdc_generate_data`
  - 支持版本管理和镜像标签

### 4. Docker Compose 配置
- **路径**: `02_generate_data/docker-compose.yml`
- **功能**: 容器编排配置，简化部署和管理
- **配置特点**:
  - 数据持久化（data、logs 目录挂载）
  - 配置文件外部挂载
  - 网络连接到 `mdc_full_mdc_network`
  - 环境变量配置
  - 自动重启策略

## 🚀 使用方法

### 构建镜像
```bash
# 进入项目目录
cd 02_generate_data

# 执行构建脚本
./docker_build_mdc_generate_data_amd64.sh
```

### 推送镜像到远程仓库
```bash
# 推送到阿里云镜像仓库
./docker_push_mdc_generate_data.sh
```

### 使用 Docker Compose 部署
```bash
# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f mdc_generate_data

# 停止服务
docker-compose down
```

### 直接使用 Docker 运行
```bash
docker run -d \
  --name mdc_generate_data \
  -v $(pwd)/data:/data \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/config.yml:/app/config.yml \
  --network mdc_full_mdc_network \
  mdc_generate_data:1.0.0
```

## 📋 配置说明

### 环境变量
- `TZ=Asia/Shanghai`: 设置时区
- `GO_ENV=production`: Go 应用环境
- `LOG_LEVEL=info`: 日志级别

### 卷挂载
- `./data:/data`: 数据文件存储
- `./logs:/app/logs`: 应用日志存储
- `./config.yml:/app/config.yml`: 配置文件

### 网络配置
- 连接到外部网络 `mdc_full_mdc_network`
- 与其他 MDC 服务共享网络

## 🔧 维护操作

### 更新镜像版本
1. 修改构建脚本中的 `TAG` 变量
2. 重新执行构建脚本
3. 推送新版本到镜像仓库

### 查看容器状态
```bash
# 查看运行中的容器
docker ps | grep mdc_generate_data

# 查看容器日志
docker logs mdc_generate_data

# 进入容器调试
docker exec -it mdc_generate_data sh
```

### 清理资源
```bash
# 停止并删除容器
docker-compose down

# 删除镜像
docker rmi mdc_generate_data:1.0.0

# 清理未使用的镜像
docker image prune
```

## ⚠️ 注意事项

1. **网络依赖**: 确保 `mdc_full_mdc_network` 网络已创建
2. **配置文件**: 确保 `config.yml` 文件存在且配置正确
3. **目录权限**: 确保挂载的目录有正确的读写权限
4. **资源限制**: 根据需要调整容器资源限制
5. **日志轮转**: 注意日志文件大小，避免磁盘空间不足

## 📊 镜像信息

- **镜像名称**: `mdc_generate_data:1.0.0`
- **镜像大小**: 约 9.43MB
- **基础镜像**: `alpine:latest`
- **架构支持**: `linux/amd64`

## 🔗 相关文档

- [02_generate_data 项目文档](./README.md)
- [配置文件说明](./config.yml)
- [高可用部署指南](./HIGH_AVAILABILITY_GUIDE.md)
