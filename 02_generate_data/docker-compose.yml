# Docker Compose 配置文件
# 用于部署 MDC 数据生成器服务

services:
  mdc_generate_data:
    # 使用本地构建的镜像
    image: mdc_generate_data:1.0.0
    
    # 容器名称
    container_name: mdc_generate_data
    
    # 重启策略：总是重启（除非手动停止）
    restart: always
    
    # 端口映射（数据生成器通常不需要暴露端口，如有HTTP服务可取消注释）
    # ports:
    #   - "8080:8080"
    
    # 卷挂载配置
    volumes:
      # 挂载数据目录，用于存储生成的数据文件
      - ./data:/data
      
      # 挂载日志目录，用于持久化应用日志
      - ./logs:/app/logs
      
      # 挂载配置文件，允许在不重建镜像的情况下修改配置
      - ./config.yml:/app/config.yml
    
    # 环境变量配置
    environment:
      # 设置时区为上海时间
      - TZ=Asia/Shanghai
      
      # Go应用程序环境变量
      - GO_ENV=production
      
      # 可选：设置日志级别
      - LOG_LEVEL=info
    
    # 网络配置
    # 连接到外部网络，以便与其他MDC服务通信
    networks:
      - mdc_full_mdc_network
    
    # 健康检查（可选）
    # 如果应用提供健康检查端点，可以启用此配置
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 40s
    
    # 资源限制（可选）
    # 限制容器使用的系统资源
    # deploy:
    #   resources:
    #     limits:
    #       cpus: '1.0'
    #       memory: 512M
    #     reservations:
    #       cpus: '0.5'
    #       memory: 256M

# 网络配置
networks:
  # 使用外部网络，与其他MDC服务共享
  # 这个网络应该在主docker-compose文件中定义
  mdc_full_mdc_network:
    external: true
