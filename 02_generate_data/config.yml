# 多设备数据生成器配置文件
# 用于模拟多台设备同时向数据采集服务推送数据，验证系统稳定性

# 目标服务配置
target:
  url: "http://mdc_device_collect:9001/api/v1/device/data"  # 设备数据采集服务地址
  timeout: 5s                                      # HTTP请求超时时间
  retry_count: 3                                   # 失败重试次数

# 设备配置源
devices:
  config_source: "api"                        # 配置源: "api" | "template"
  config_api: "http://mdc_server_api:9005/api/public/devices/configs"  # API获取设备配置
  refresh_interval: 60                             # API配置刷新间隔(秒)

# 全局配置
global:
  total_devices: 12        # 总设备数量 (config_source=template时有效)
  run_mode: "forever"     # 运行模式: "duration"(持续时间) | "cycles"(循环次数) | "forever"(永久运行)
  run_duration: "5m"     # 运行持续时间 (run_mode=duration时有效)
  run_cycles: 100          # 运行循环次数 (run_mode=cycles时有效)
  concurrent_limit: 50     # 最大并发数限制
  stats_interval: "10s"    # 统计信息输出间隔

# 设备模板配置 (config_source=template时有效)
# 支持多种设备类型，每种类型可以有不同的数据特征
device_templates:
  # FANUC CNC 机床模板
  - name: "fanuc_cnc"
    count: 2                    # 此类型设备数量
    adapter: "fanuc"            # 使用FANUC工厂适配器
    data_type_regex: "^fanuc.*" # 正则表达式匹配fanuc开头的设备类型
    push_interval: "3s"         # 推送间隔
    push_jitter: "1s"           # 推送间隔随机抖动
    # 统一的配置字段
    config:
      # 设备状态配置
      status_config:
        running:                # 运行状态
          duration_min: 30      # 最小持续时间(秒) - 30秒
          duration_max: 60      # 最大持续时间(秒) - 1分钟
          weight: 60            # 权重(百分比)
        standby:                # 待机状态
          duration_min: 15      # 最小持续时间(秒) - 15秒
          duration_max: 30      # 最大持续时间(秒) - 30秒
          weight: 30            # 权重(百分比)
        alarm:                  # 报警状态
          duration_min: 10      # 最小持续时间(秒) - 10秒
          duration_max: 20      # 最大持续时间(秒) - 20秒
          weight: 10            # 权重(百分比)
      # 程序配置
      programs:
        - name: "MAIN_PROG_001"
          sub_programs: ["SUB_001", "SUB_002", "SUB_003"]

  # 温度传感器模板
  - name: "temperature_sensor"
    count: 4                    # 此类型设备数量
    adapter: "sensor"           # 使用传感器工厂适配器
    data_type_regex: "^temperature.*"  # 正则表达式匹配temperature开头的设备类型
    push_interval: "2s"         # 推送间隔
    push_jitter: "500ms"        # 推送间隔随机抖动
    config:
      data_fields:
        temperature:
          type: "float"
          min: 18.0
          max: 35.0
          step: 0.1              # 每次变化步长
          trend: "random"        # 变化趋势: "random" | "increase" | "decrease" | "wave"
        humidity:
          type: "float"
          min: 30.0
          max: 80.0
          step: 0.5
          trend: "wave"

  # 压力传感器模板
  - name: "pressure_sensor"
    count: 3
    adapter: "sensor"           # 使用传感器工厂适配器
    data_type_regex: "^pressure.*"     # 正则表达式匹配pressure开头的设备类型
    push_interval: "3s"
    push_jitter: "1s"
    config:
      data_fields:
        pressure:
          type: "float"
          min: 0.8
          max: 1.2
          step: 0.01
          trend: "random"
        altitude:
          type: "int"
          min: 100
          max: 500
          step: 1
          trend: "increase"

  # 运动传感器模板
  - name: "motion_sensor"
    count: 3
    adapter: "sensor"           # 使用传感器工厂适配器
    data_type_regex: "^motion.*"       # 正则表达式匹配motion开头的设备类型
    push_interval: "1s"
    push_jitter: "200ms"
    config:
      data_fields:
        acceleration_x:
          type: "float"
          min: -10.0
          max: 10.0
          step: 0.2
          trend: "random"
        acceleration_y:
          type: "float"
          min: -10.0
          max: 10.0
          step: 0.2
          trend: "random"
        acceleration_z:
          type: "float"
          min: -10.0
          max: 10.0
          step: 0.2
          trend: "random"

  # Brother 机床模板
  - name: "brother_cnc"
    count: 1
    adapter: "brother"          # 使用Brother工厂适配器
    data_type_regex: "^brother.*"      # 正则表达式匹配brother开头的设备类型
    push_interval: "2s"
    push_jitter: "500ms"
    config:
      # Brother特有的状态配置
      status_config:
        operating:              # 运行状态
          duration_min: 300     # 最小持续时间(秒) - 5分钟
          duration_max: 480     # 最大持续时间(秒) - 8分钟
          weight: 70            # 权重(百分比)
        idle:                   # 空闲状态
          duration_min: 120     # 最小持续时间(秒) - 2分钟
          duration_max: 240     # 最大持续时间(秒) - 4分钟
          weight: 25            # 权重(百分比)
        error:                  # 错误状态
          duration_min: 30      # 最小持续时间(秒) - 30秒
          duration_max: 120     # 最大持续时间(秒) - 2分钟
          weight: 5             # 权重(百分比)
      # Brother程序配置
      programs:
        - name: "PROG_A001"
          sub_programs: ["SUB_A01", "SUB_A02"]
        - name: "PROG_B001"
          sub_programs: ["SUB_B01", "SUB_B02"]

  # Modbus 设备模板
  - name: "modbus_device"
    count: 2
    adapter: "modbus"           # 使用Modbus工厂适配器
    data_type_regex: "^modbus.*"       # 正则表达式匹配modbus开头的设备类型
    push_interval: "5s"
    push_jitter: "1s"
    config:
      # Modbus寄存器配置
      registers:
        holding_registers:
          - address: 40001
            name: "temperature"
            type: "float"
            min: 15.0
            max: 45.0
          - address: 40002
            name: "pressure"
            type: "float"
            min: 0.5
            max: 2.0
        input_registers:
          - address: 30001
            name: "flow_rate"
            type: "float"
            min: 0.0
            max: 100.0
        coils:
          - address: 1
            name: "pump_status"
            type: "bool"
          - address: 2
            name: "valve_status"
            type: "bool"

# 位置配置
locations:
  - "DG-101"

# 日志配置
logging:
  level: "info"              # 日志级别: debug, info, warn, error
  format: "json"            # 日志格式: text, json
  output: "logs/generator.log"  # 输出目标: stdout, stderr, 文件路径
  device_logs: true           # 是否输出每个设备的详细日志
  rotation:
    enabled: true            # 启用日志轮转
    max_size: 20             # 单个文件最大20MB
    max_age: 3               # 保留3天
    max_backups: 3           # 保留3个备份文件
    compress: true           # 压缩旧文件

# 故障模拟配置 (可选)
fault_simulation:
  enabled: false             # 是否启用故障模拟
  network_error_rate: 0.01   # 网络错误率 (0-1)
  timeout_rate: 0.005        # 超时错误率 (0-1)
  device_offline_rate: 0.02  # 设备离线率 (0-1)
