/**
 * 设备管理器模块
 *
 * 功能概述：
 * 本模块实现设备管理器功能，负责创建、管理和监控多个虚拟设备实例，
 * 是整个数据生成器的核心控制组件，统一管理所有设备的生命周期和运行状态
 *
 * 主要功能：
 * - 设备创建：根据配置创建多个虚拟设备实例
 * - 生命周期管理：统一管理所有设备的启动、运行和停止
 * - 批量控制：支持分批启动设备，避免系统冲击
 * - 统计监控：实时收集和展示所有设备的运行统计
 * - 运行模式：支持定时、循环、永久等多种运行模式
 * - 并发控制：控制设备的并发数量，防止系统过载
 * - 配置源支持：支持模板和API两种设备配置来源
 *
 * 架构设计：
 * - 管理器模式：统一管理多个设备实例
 * - 工厂模式：根据配置动态创建设备
 * - 观察者模式：监控所有设备的状态变化
 * - 策略模式：支持不同的运行模式策略
 *
 * 技术特性：
 * - 并发安全：使用读写锁保护共享资源
 * - 上下文控制：使用context控制所有设备的生命周期
 * - 优雅关闭：支持优雅停止所有设备
 * - 实时监控：定时收集和展示统计信息
 * - 分批处理：分批启动设备，避免系统冲击
 * - 内存高效：合理的内存管理和资源释放
 *
 * 运行模式：
 * - duration：按时间运行，到达指定时间后自动停止
 * - cycles：按循环次数运行，完成指定循环后停止
 * - forever：永久运行，直到手动停止
 *
 * 监控维度：
 * - 设备统计：总设备数、活跃设备数
 * - 发送统计：总发送数、成功数、错误数
 * - 性能指标：成功率、吞吐量、运行时间
 * - 类型分析：按设备类型的详细统计
 *
 * 业务价值：
 * - 集中管理：统一管理大量设备，简化操作
 * - 性能监控：实时监控系统性能和设备状态
 * - 压力测试：支持大规模设备的并发测试
 * - 运维友好：清晰的统计信息和状态展示
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"fmt"
	"math/rand"
	"shared/logger"
	"sync"
	"time"
)

/**
 * 设备管理器结构体
 *
 * 功能：统一管理多个虚拟设备实例，控制设备的生命周期和运行状态
 *
 * 架构设计：
 * - 中央控制：作为所有设备的中央控制器
 * - 状态管理：维护所有设备的状态和统计信息
 * - 并发控制：控制设备的并发启动和运行
 * - 资源管理：统一管理设备的创建和销毁
 * - 动态更新：支持设备配置的热更新和实例管理
 *
 * 核心职责：
 * - 设备创建：根据配置创建设备实例
 * - 生命周期：管理设备的启动、运行、停止
 * - 统计监控：收集和展示运行统计信息
 * - 并发控制：控制设备的并发数量
 * - 热更新：动态添加、删除、更新设备实例
 *
 * @struct DeviceManager
 */
type DeviceManager struct {
	/** 管理的所有设备实例列表 */
	devices []*Device

	/** 设备实例映射表，用于快速查找和管理 key: deviceID */
	deviceMap map[string]*Device

	/** 当前API设备配置缓存，用于配置变化检测 */
	currentAPIConfigs map[string]*APIDeviceConfig

	/** 全局配置引用，包含运行参数和设备配置 */
	config *Config

	/** 全局统计信息，聚合所有设备的统计数据 */
	stats *GlobalStats

	/** 上下文对象，用于控制所有设备的生命周期 */
	ctx context.Context

	/** 取消函数，用于停止所有设备 */
	cancel context.CancelFunc

	/** 等待组，用于等待所有goroutine结束 */
	wg sync.WaitGroup

	/** 读写锁，保护设备列表的并发访问 */
	mutex sync.RWMutex

	/** 是否已启动标志，用于控制热更新时机 */
	isStarted bool
}

/**
 * 全局统计信息结构体
 *
 * 功能：记录所有设备的聚合统计信息，用于监控和分析
 *
 * 统计维度：
 * - 设备统计：总设备数、活跃设备数
 * - 发送统计：总发送数、成功数、错误数
 * - 时间信息：开始时间、最后更新时间
 *
 * 计算指标：
 * - 成功率：成功数/总发送数
 * - 吞吐量：总发送数/运行时间
 * - 活跃率：活跃设备数/总设备数
 *
 * @struct GlobalStats
 */
type GlobalStats struct {
	/** 总设备数量 */
	TotalDevices int

	/** 当前活跃的设备数量（最近30秒内有发送数据） */
	ActiveDevices int

	/** 所有设备的总发送次数 */
	TotalSent int64

	/** 所有设备的总成功次数 */
	TotalSuccess int64

	/** 所有设备的总错误次数 */
	TotalErrors int64

	/** 管理器启动时间 */
	StartTime time.Time

	/** 统计信息最后更新时间 */
	LastUpdateTime time.Time

	/** 读写锁，保护统计数据的并发访问 */
	mutex sync.RWMutex
}

// NewDeviceManager 创建设备管理器
func NewDeviceManager(config *Config) *DeviceManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &DeviceManager{
		devices:           make([]*Device, 0),
		deviceMap:         make(map[string]*Device),
		currentAPIConfigs: make(map[string]*APIDeviceConfig),
		config:            config,
		stats: &GlobalStats{
			StartTime:      time.Now(),
			LastUpdateTime: time.Now(),
		},
		ctx:       ctx,
		cancel:    cancel,
		isStarted: false,
	}
}

// Initialize 初始化所有设备
func (dm *DeviceManager) Initialize() error {
	// 根据配置源选择初始化方式
	switch dm.config.Devices.ConfigSource {
	case "api":
		return dm.createDevicesFromAPI()
	case "template":
		return dm.createDevicesFromTemplate()
	default:
		return fmt.Errorf("不支持的设备配置源: %s", dm.config.Devices.ConfigSource)
	}
}

// createDevicesFromTemplate 从模板创建设备
func (dm *DeviceManager) createDevicesFromTemplate() error {
	fmt.Printf("🚀 从模板初始化 %d 个设备...\n", dm.config.Global.TotalDevices)

	deviceIndex := 0

	// 根据设备模板创建设备
	for _, template := range dm.config.DeviceTemplates {
		fmt.Printf("📱 创建 %d 个 %s 类型设备\n", template.Count, template.Name)

		for i := 0; i < template.Count; i++ {
			// 使用适配器类型生成设备ID
			deviceID := fmt.Sprintf("%s_%03d", template.Adapter, i+1)
			location := dm.getRandomLocation()

			device := NewDevice(deviceID, template, location, dm.config)
			dm.devices = append(dm.devices, device)

			deviceIndex++
			if deviceIndex%10 == 0 {
				fmt.Printf("   ✅ 已创建 %d/%d 设备\n", deviceIndex, dm.config.Global.TotalDevices)
			}
		}
	}

	// 更新统计信息
	dm.stats.mutex.Lock()
	dm.stats.TotalDevices = len(dm.devices)
	dm.stats.ActiveDevices = len(dm.devices)
	dm.stats.mutex.Unlock()

	fmt.Printf("✅ 成功从模板初始化 %d 个设备\n", len(dm.devices))
	return nil
}

// Start 启动所有设备
func (dm *DeviceManager) Start() error {
	fmt.Printf("🔥 Starting %d devices...\n", len(dm.devices))

	// 启动统计协程
	dm.wg.Add(1)
	go dm.statsLoop()

	// 分批启动设备，避免同时启动造成冲击
	batchSize := 10
	if dm.config.Global.ConcurrentLimit > 0 && dm.config.Global.ConcurrentLimit < batchSize {
		batchSize = dm.config.Global.ConcurrentLimit
	}

	for i := 0; i < len(dm.devices); i += batchSize {
		end := i + batchSize
		if end > len(dm.devices) {
			end = len(dm.devices)
		}

		// 启动一批设备
		for j := i; j < end; j++ {
			dm.devices[j].Start()
		}

		fmt.Printf("   🚀 Started devices %d-%d\n", i+1, end)

		// 短暂延迟，避免同时启动
		if end < len(dm.devices) {
			time.Sleep(100 * time.Millisecond)
		}
	}

	fmt.Printf("✅ All devices started successfully\n")

	// 设置启动标志
	dm.mutex.Lock()
	dm.isStarted = true
	dm.mutex.Unlock()

	// 根据运行模式控制运行时间
	switch dm.config.Global.RunMode {
	case "duration":
		fmt.Printf("⏱️  Running for %v\n", dm.config.Global.RunDuration)
		time.AfterFunc(dm.config.Global.RunDuration, func() {
			fmt.Printf("⏰ Duration completed, stopping devices...\n")
			dm.Stop()
		})
	case "cycles":
		fmt.Printf("🔄 Running for %d cycles per device\n", dm.config.Global.RunCycles)
		// TODO: 实现循环次数控制
	case "forever":
		fmt.Printf("♾️  Running forever (until interrupted)\n")
	}

	return nil
}

// Stop 停止所有设备
func (dm *DeviceManager) Stop() {
	fmt.Printf("🛑 Stopping all devices...\n")

	dm.cancel()

	// 停止所有设备
	for i, device := range dm.devices {
		device.Stop()
		if (i+1)%10 == 0 {
			fmt.Printf("   🛑 Stopped %d/%d devices\n", i+1, len(dm.devices))
		}
	}

	// 等待统计协程结束
	dm.wg.Wait()

	// 输出最终统计
	dm.printFinalStats()

	fmt.Printf("✅ All devices stopped\n")
}

// Wait 等待所有设备完成
func (dm *DeviceManager) Wait() {
	dm.wg.Wait()
}

// statsLoop 统计信息循环
func (dm *DeviceManager) statsLoop() {
	defer dm.wg.Done()

	ticker := time.NewTicker(dm.config.Global.StatsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dm.updateAndPrintStats()
		case <-dm.ctx.Done():
			return
		}
	}
}

// updateAndPrintStats 更新并打印统计信息
func (dm *DeviceManager) updateAndPrintStats() {
	dm.mutex.RLock()
	devices := make([]*Device, len(dm.devices))
	copy(devices, dm.devices)
	dm.mutex.RUnlock()

	var totalSent, totalSuccess, totalErrors int64
	activeDevices := 0

	// 收集所有设备的统计信息
	for _, device := range devices {
		stats := device.GetStats()
		totalSent += stats.TotalSent
		totalSuccess += stats.SuccessCount
		totalErrors += stats.ErrorCount

		// 检查设备是否活跃（最近30秒内有发送）
		if time.Since(stats.LastSentTime) < 30*time.Second {
			activeDevices++
		}
	}

	// 更新全局统计
	dm.stats.mutex.Lock()
	dm.stats.TotalSent = totalSent
	dm.stats.TotalSuccess = totalSuccess
	dm.stats.TotalErrors = totalErrors
	dm.stats.ActiveDevices = activeDevices
	dm.stats.LastUpdateTime = time.Now()
	dm.stats.mutex.Unlock()

	// 打印统计信息
	dm.printStats()
}

// printStats 打印当前统计信息
func (dm *DeviceManager) printStats() {
	dm.stats.mutex.RLock()
	defer dm.stats.mutex.RUnlock()

	runtime := time.Since(dm.stats.StartTime)
	successRate := float64(0)
	if dm.stats.TotalSent > 0 {
		successRate = float64(dm.stats.TotalSuccess) / float64(dm.stats.TotalSent) * 100
	}

	throughput := float64(0)
	if runtime.Seconds() > 0 {
		throughput = float64(dm.stats.TotalSent) / runtime.Seconds()
	}

	fmt.Printf("\n📊 === Device Statistics ===\n")
	fmt.Printf("   🕐 Runtime: %v\n", runtime.Round(time.Second))
	fmt.Printf("   📱 Devices: %d total, %d active\n", dm.stats.TotalDevices, dm.stats.ActiveDevices)
	fmt.Printf("   📤 Sent: %d total, %d success, %d errors\n", dm.stats.TotalSent, dm.stats.TotalSuccess, dm.stats.TotalErrors)
	fmt.Printf("   📈 Success Rate: %.2f%%\n", successRate)
	fmt.Printf("   🚀 Throughput: %.2f msg/sec\n", throughput)
	fmt.Printf("   🔄 Last Update: %s\n", dm.stats.LastUpdateTime.Format("15:04:05"))
	fmt.Printf("=============================\n\n")
}

// printFinalStats 打印最终统计信息
func (dm *DeviceManager) printFinalStats() {
	dm.updateAndPrintStats()

	fmt.Printf("\n🎯 === Final Statistics ===\n")

	// 按设备类型统计
	typeStats := make(map[string]struct {
		count   int
		sent    int64
		success int64
		errors  int64
	})

	for _, device := range dm.devices {
		stats := device.GetStats()
		typeName := device.Template.Name

		ts := typeStats[typeName]
		ts.count++
		ts.sent += stats.TotalSent
		ts.success += stats.SuccessCount
		ts.errors += stats.ErrorCount
		typeStats[typeName] = ts
	}

	for typeName, stats := range typeStats {
		successRate := float64(0)
		if stats.sent > 0 {
			successRate = float64(stats.success) / float64(stats.sent) * 100
		}
		fmt.Printf("   📱 %s: %d devices, %d sent, %.2f%% success\n",
			typeName, stats.count, stats.sent, successRate)
	}

	fmt.Printf("==========================\n")
}

// getRandomLocation 获取随机位置
func (dm *DeviceManager) getRandomLocation() string {
	if len(dm.config.Locations) == 0 {
		return "Unknown"
	}
	return dm.config.Locations[rand.Intn(len(dm.config.Locations))]
}

// GetGlobalStats 获取全局统计信息
func (dm *DeviceManager) GetGlobalStats() GlobalStats {
	dm.stats.mutex.RLock()
	defer dm.stats.mutex.RUnlock()
	return *dm.stats
}

/**
 * 动态设备管理功能
 *
 * 以下函数实现设备配置的热更新功能，包括：
 * - 配置变化检测：比较新旧配置，识别变化类型
 * - 设备动态添加：为新增设备创建实例并启动
 * - 设备动态删除：停止并销毁已删除的设备实例
 * - 设备参数更新：更新现有设备的配置参数
 * - 状态同步：维护设备映射表和统计信息的一致性
 */

// addDevice 添加新设备实例
func (dm *DeviceManager) addDevice(apiDevice APIDeviceConfig) error {
	logger.Infof("➕ 添加新设备: %s (%s)", apiDevice.ID, apiDevice.Name)

	// 转换为设备模板
	template, err := convertAPIDeviceToTemplate(apiDevice, dm.config)
	if err != nil {
		return fmt.Errorf("转换设备模板失败: %v", err)
	}

	location := dm.getRandomLocation()

	// 创建设备实例
	device := NewDeviceWithAPIConfig(apiDevice.ID, template, location, dm.config, &apiDevice)

	dm.mutex.Lock()
	// 添加到设备列表和映射表
	dm.devices = append(dm.devices, device)
	dm.deviceMap[apiDevice.ID] = device
	// 缓存API配置
	apiConfigCopy := apiDevice
	dm.currentAPIConfigs[apiDevice.ID] = &apiConfigCopy
	dm.mutex.Unlock()

	// 如果管理器已启动，立即启动新设备
	dm.mutex.RLock()
	isStarted := dm.isStarted
	dm.mutex.RUnlock()

	if isStarted {
		device.Start()
		logger.Infof("✅ 新设备 %s 已启动", apiDevice.ID)
	}

	// 更新统计信息
	dm.stats.mutex.Lock()
	dm.stats.TotalDevices = len(dm.devices)
	if isStarted {
		dm.stats.ActiveDevices++
	}
	dm.stats.mutex.Unlock()

	return nil
}

// removeDevice 删除设备实例
func (dm *DeviceManager) removeDevice(deviceID string) error {
	logger.Infof("➖ 删除设备: %s", deviceID)

	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	// 从映射表中查找设备
	device, exists := dm.deviceMap[deviceID]
	if !exists {
		return fmt.Errorf("设备 %s 不存在", deviceID)
	}

	// 停止设备
	device.Stop()

	// 从设备列表中移除
	for i, d := range dm.devices {
		if d.ID == deviceID {
			dm.devices = append(dm.devices[:i], dm.devices[i+1:]...)
			break
		}
	}

	// 从映射表和配置缓存中移除
	delete(dm.deviceMap, deviceID)
	delete(dm.currentAPIConfigs, deviceID)

	// 更新统计信息
	dm.stats.mutex.Lock()
	dm.stats.TotalDevices = len(dm.devices)
	if dm.stats.ActiveDevices > 0 {
		dm.stats.ActiveDevices--
	}
	dm.stats.mutex.Unlock()

	logger.Infof("✅ 设备 %s 已删除", deviceID)
	return nil
}

// updateDevice 更新设备配置
func (dm *DeviceManager) updateDevice(apiDevice APIDeviceConfig) error {
	logger.Infof("🔄 更新设备配置: %s (%s)", apiDevice.ID, apiDevice.Name)

	dm.mutex.RLock()
	device, exists := dm.deviceMap[apiDevice.ID]
	dm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("设备 %s 不存在", apiDevice.ID)
	}

	// 策略选择：重新创建设备实例（更安全、更简单）
	// 这种方式确保所有配置都能正确应用，避免部分更新的复杂性

	// 1. 停止旧设备
	device.Stop()
	logger.Infof("🛑 已停止旧设备实例: %s", apiDevice.ID)

	// 2. 创建新设备实例
	template, err := convertAPIDeviceToTemplate(apiDevice, dm.config)
	if err != nil {
		return fmt.Errorf("转换设备模板失败: %v", err)
	}

	location := dm.getRandomLocation()
	newDevice := NewDeviceWithAPIConfig(apiDevice.ID, template, location, dm.config, &apiDevice)

	// 3. 替换设备实例
	dm.mutex.Lock()
	// 更新设备列表
	for i, d := range dm.devices {
		if d.ID == apiDevice.ID {
			dm.devices[i] = newDevice
			break
		}
	}
	// 更新映射表和配置缓存
	dm.deviceMap[apiDevice.ID] = newDevice
	apiConfigCopy := apiDevice
	dm.currentAPIConfigs[apiDevice.ID] = &apiConfigCopy
	dm.mutex.Unlock()

	// 4. 启动新设备（如果管理器已启动）
	dm.mutex.RLock()
	isStarted := dm.isStarted
	dm.mutex.RUnlock()

	if isStarted {
		newDevice.Start()
		logger.Infof("✅ 新设备实例已启动: %s", apiDevice.ID)
	}

	logger.Infof("✅ 设备 %s 配置更新完成", apiDevice.ID)
	return nil
}

// detectConfigChanges 检测配置变化
func (dm *DeviceManager) detectConfigChanges(newAPIDevices []APIDeviceConfig) (added, removed, updated []APIDeviceConfig) {
	// 创建新配置的映射表
	newConfigMap := make(map[string]APIDeviceConfig)
	for _, device := range newAPIDevices {
		if device.Enabled {
			newConfigMap[device.ID] = device
		}
	}

	dm.mutex.RLock()
	currentConfigMap := make(map[string]*APIDeviceConfig)
	for k, v := range dm.currentAPIConfigs {
		currentConfigMap[k] = v
	}
	dm.mutex.RUnlock()

	// 检测新增的设备
	for deviceID, newDevice := range newConfigMap {
		if _, exists := currentConfigMap[deviceID]; !exists {
			added = append(added, newDevice)
		}
	}

	// 检测删除的设备
	for deviceID, currentDevice := range currentConfigMap {
		if _, exists := newConfigMap[deviceID]; !exists {
			removed = append(removed, *currentDevice)
		}
	}

	// 检测更新的设备
	for deviceID, newDevice := range newConfigMap {
		if currentDevice, exists := currentConfigMap[deviceID]; exists {
			if dm.isDeviceConfigChanged(*currentDevice, newDevice) {
				updated = append(updated, newDevice)
			}
		}
	}

	return added, removed, updated
}

// isDeviceConfigChanged 检查设备配置是否发生变化
func (dm *DeviceManager) isDeviceConfigChanged(current APIDeviceConfig, new APIDeviceConfig) bool {
	// 比较关键配置字段
	return current.Name != new.Name ||
		current.DataType != new.DataType ||
		current.IP != new.IP ||
		current.Port != new.Port ||
		current.CollectInterval != new.CollectInterval ||
		current.Timeout != new.Timeout ||
		current.RetryCount != new.RetryCount ||
		current.RetryDelay != new.RetryDelay ||
		current.AutoStart != new.AutoStart ||
		current.Enabled != new.Enabled ||
		current.Location != new.Location ||
		current.Brand != new.Brand ||
		current.Model != new.Model
}

// processConfigChanges 处理配置变化
func (dm *DeviceManager) processConfigChanges(added, removed, updated []APIDeviceConfig) error {
	var errors []string

	// 处理删除的设备
	for _, device := range removed {
		if err := dm.removeDevice(device.ID); err != nil {
			errorMsg := fmt.Sprintf("删除设备 %s 失败: %v", device.ID, err)
			logger.Errorf(errorMsg)
			errors = append(errors, errorMsg)
		}
	}

	// 处理新增的设备
	for _, device := range added {
		if err := dm.addDevice(device); err != nil {
			errorMsg := fmt.Sprintf("添加设备 %s 失败: %v", device.ID, err)
			logger.Errorf(errorMsg)
			errors = append(errors, errorMsg)
		}
	}

	// 处理更新的设备
	for _, device := range updated {
		if err := dm.updateDevice(device); err != nil {
			errorMsg := fmt.Sprintf("更新设备 %s 失败: %v", device.ID, err)
			logger.Errorf(errorMsg)
			errors = append(errors, errorMsg)
		}
	}

	// 汇总错误信息
	if len(errors) > 0 {
		return fmt.Errorf("配置更新过程中发生 %d 个错误: %s", len(errors), fmt.Sprintf("%v", errors))
	}

	return nil
}
