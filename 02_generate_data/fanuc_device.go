/**
 * FANUC设备数据生成器模块
 *
 * 功能概述：
 * 本模块专门模拟FANUC CNC机床的数据采集，高度还原真实FANUC设备的数据特征，
 * 包括设备状态变化、程序执行、生产周期、产量统计等核心功能
 *
 * 主要功能：
 * - 状态模拟：模拟运行、待机、报警三种主要设备状态
 * - 状态转换：基于权重和概率的智能状态转换逻辑
 * - 程序管理：模拟主程序和子程序的执行和切换
 * - 生产周期：模拟完整的生产周期，包括周期时间和产量统计
 * - 时间管理：精确的时间控制和运行时间统计
 * - 数据生成：生成符合FANUC格式的标准化数据
 *
 * 技术特性：
 * - 状态机：基于状态机模式的设备状态管理
 * - 概率模型：基于权重的状态转换概率模型
 * - 时间驱动：基于时间的事件驱动机制
 * - 配置驱动：完全基于配置的行为控制
 * - 数据标准化：符合FANUC数据格式标准
 *
 * 业务模拟：
 * - 真实场景：高度还原真实FANUC设备的工作场景
 * - 生产流程：完整的生产流程模拟，从程序启动到产品完成
 * - 异常处理：模拟设备报警和异常恢复过程
 * - 效率统计：实时的生产效率和产量统计
 *
 * 数据特征：
 * - 设备状态：运行、待机、报警状态及其持续时间
 * - 程序信息：主程序、当前程序、程序运行时间
 * - 生产数据：总产量、周期时间、周期进度
 * - 时间戳：精确的时间戳和运行时间统计
 *
 * 应用场景：
 * - 系统测试：测试数据采集系统对FANUC设备的支持
 * - 性能验证：验证系统处理FANUC数据的性能
 * - 功能演示：为客户演示FANUC设备监控功能
 * - 开发调试：为开发人员提供标准的FANUC测试数据
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"math/rand"
	"time"
)

/**
 * FANUC设备状态结构体
 *
 * 功能：维护FANUC设备的完整状态信息，包括设备状态、程序信息和生产数据
 *
 * @struct FanucDeviceState
 */
type FanucDeviceState struct {
	/** === 设备状态信息 === */

	/** 当前设备状态："running"(运行), "standby"(待机), "alarm"(报警) */
	CurrentStatus string `json:"status"`

	/** 当前状态的开始时间 */
	StatusStartTime time.Time `json:"status_start_time"`

	/** 当前状态的预计持续时间（秒） */
	StatusDuration int `json:"status_duration"`

	/** === 程序信息 === */

	/** 主程序名称，如"MAIN_PROG_001" */
	MainProgram string `json:"main_program"`

	/** 当前执行的程序名称（可能是主程序或子程序） */
	CurrentProgram string `json:"current_program"`

	/** 程序开始执行的时间 */
	ProgramStartTime time.Time `json:"program_start_time"`

	/** === 生产信息 === */

	/** 累计生产的产品总数 */
	TotalParts int `json:"total_parts"`

	/** 当前生产周期的开始时间 */
	CycleStartTime time.Time `json:"cycle_start_time"`

	/** 当前生产周期的预计时间（秒） */
	CycleTime int `json:"cycle_time"`

	/** === 内部控制状态（不对外暴露） === */

	/** 下次状态变更的预定时间 */
	nextStatusChange time.Time

	/** 下次生产周期结束的预定时间 */
	nextCycleEnd time.Time
}

// FanucDataGenerator FANUC数据生成器
type FanucDataGenerator struct {
	deviceID string
	config   *FanucConfig
	state    *FanucDeviceState
}

// NewFanucDataGenerator 创建FANUC数据生成器
func NewFanucDataGenerator(deviceID string, config *FanucConfig) *FanucDataGenerator {
	generator := &FanucDataGenerator{
		deviceID: deviceID,
		config:   config,
		state: &FanucDeviceState{
			CurrentStatus:   "standby",
			StatusStartTime: time.Now(),
			TotalParts:      0,
			CycleStartTime:  time.Now(),
		},
	}

	// 初始化状态
	generator.initializeState()

	return generator
}

// initializeState 初始化设备状态
func (f *FanucDataGenerator) initializeState() {
	now := time.Now()

	// 随机选择初始状态
	status := f.selectRandomStatus()
	f.state.CurrentStatus = status
	f.state.StatusStartTime = now

	// 设置状态持续时间
	f.state.StatusDuration = f.getStatusDuration(status)
	f.state.nextStatusChange = now.Add(time.Duration(f.state.StatusDuration) * time.Second)

	// 选择程序
	f.selectProgram()

	// 设置生产周期
	if status == "running" {
		f.startProductionCycle()
	}
}

// selectRandomStatus 根据权重随机选择状态
func (f *FanucDataGenerator) selectRandomStatus() string {
	totalWeight := f.config.StatusConfig.Running.Weight +
		f.config.StatusConfig.Standby.Weight +
		f.config.StatusConfig.Alarm.Weight

	random := rand.Intn(totalWeight)

	if random < f.config.StatusConfig.Running.Weight {
		return "running"
	} else if random < f.config.StatusConfig.Running.Weight+f.config.StatusConfig.Standby.Weight {
		return "standby"
	} else {
		return "alarm"
	}
}

// getStatusDuration 获取状态持续时间
func (f *FanucDataGenerator) getStatusDuration(status string) int {
	var config StatusDuration

	switch status {
	case "running":
		config = f.config.StatusConfig.Running
	case "standby":
		config = f.config.StatusConfig.Standby
	case "alarm":
		config = f.config.StatusConfig.Alarm
	default:
		config = f.config.StatusConfig.Standby
	}

	return config.DurationMin + rand.Intn(config.DurationMax-config.DurationMin+1)
}

// selectProgram 选择程序
func (f *FanucDataGenerator) selectProgram() {
	if len(f.config.Programs) == 0 {
		f.state.MainProgram = "MAIN_PROG_001"
		f.state.CurrentProgram = "SUB_001"
		return
	}

	// 随机选择主程序
	program := f.config.Programs[rand.Intn(len(f.config.Programs))]
	f.state.MainProgram = program.Name

	// 随机选择子程序
	if len(program.SubPrograms) > 0 {
		f.state.CurrentProgram = program.SubPrograms[rand.Intn(len(program.SubPrograms))]
	} else {
		f.state.CurrentProgram = program.Name
	}

	f.state.ProgramStartTime = time.Now()
}

// startProductionCycle 开始生产周期
func (f *FanucDataGenerator) startProductionCycle() {
	now := time.Now()
	f.state.CycleStartTime = now

	// 设置周期时间（使用固定范围：5-10分钟）
	minCycle := 300 // 5分钟
	maxCycle := 600 // 10分钟
	f.state.CycleTime = minCycle + rand.Intn(maxCycle-minCycle+1)

	f.state.nextCycleEnd = now.Add(time.Duration(f.state.CycleTime) * time.Second)
}

// Update 更新设备状态
func (f *FanucDataGenerator) Update() {
	now := time.Now()

	// 检查是否需要变更状态
	if now.After(f.state.nextStatusChange) {
		f.changeStatus()
	}

	// 检查生产周期
	if f.state.CurrentStatus == "running" && now.After(f.state.nextCycleEnd) {
		f.completeCycle()
	}
}

// changeStatus 变更设备状态
func (f *FanucDataGenerator) changeStatus() {
	now := time.Now()

	// 状态转换逻辑
	var newStatus string
	switch f.state.CurrentStatus {
	case "running":
		// 运行状态可以转为待机或报警
		if rand.Float64() < 0.1 { // 10%概率转为报警
			newStatus = "alarm"
		} else {
			newStatus = "standby"
		}
	case "standby":
		// 待机状态可以转为运行或报警
		if rand.Float64() < 0.05 { // 5%概率转为报警
			newStatus = "alarm"
		} else {
			newStatus = "running"
		}
	case "alarm":
		// 报警状态只能转为待机
		newStatus = "standby"
	default:
		newStatus = "standby"
	}

	// 更新状态
	f.state.CurrentStatus = newStatus
	f.state.StatusStartTime = now
	f.state.StatusDuration = f.getStatusDuration(newStatus)
	f.state.nextStatusChange = now.Add(time.Duration(f.state.StatusDuration) * time.Second)

	// 如果转为运行状态，开始新的生产周期
	if newStatus == "running" {
		f.selectProgram() // 可能选择新程序
		f.startProductionCycle()
	}
}

// completeCycle 完成生产周期
func (f *FanucDataGenerator) completeCycle() {
	// 增加产量（固定每周期+1）
	f.state.TotalParts += 1

	// 开始新的生产周期
	f.startProductionCycle()
}

// GenerateData 生成设备数据
func (f *FanucDataGenerator) GenerateData() map[string]interface{} {
	// 更新状态
	f.Update()

	now := time.Now()

	// 计算运行时间
	statusRuntime := int(now.Sub(f.state.StatusStartTime).Seconds())
	programRuntime := int(now.Sub(f.state.ProgramStartTime).Seconds())
	cycleRuntime := int(now.Sub(f.state.CycleStartTime).Seconds())

	// 计算周期进度
	cycleProgress := float64(0)
	if f.state.CurrentStatus == "running" && f.state.CycleTime > 0 {
		cycleProgress = float64(cycleRuntime) / float64(f.state.CycleTime) * 100
		if cycleProgress > 100 {
			cycleProgress = 100
		}
	}

	return map[string]interface{}{
		"timestamp":       now.Unix(),
		"status":          f.state.CurrentStatus,
		"status_runtime":  statusRuntime,
		"main_program":    f.state.MainProgram,
		"current_program": f.state.CurrentProgram,
		"program_runtime": programRuntime,
		"total_parts":     f.state.TotalParts,
		"cycle_time":      f.state.CycleTime,
		"cycle_runtime":   cycleRuntime,
		"cycle_progress":  cycleProgress,
	}
}

// GetCurrentState 获取当前状态
func (f *FanucDataGenerator) GetCurrentState() *FanucDeviceState {
	return f.state
}
