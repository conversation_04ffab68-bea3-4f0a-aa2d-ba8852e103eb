#!/bin/bash

# 数据生成服务启动脚本
# 解决Go环境配置问题并启动02_generate_data服务

echo "🚀 启动 数据生成服务"
echo "=================================="

# 检查并修复Go环境
echo "🔧 检查Go环境..."

# 设置正确的Go环境变量
export PATH=/opt/homebrew/bin:$PATH
export GOROOT=/opt/homebrew/opt/go/libexec

# 验证Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: Go未安装或不在PATH中"
    echo "请确保Go已正确安装在 /opt/homebrew/opt/go"
    exit 1
fi

# 显示Go版本信息
echo "✅ Go版本: $(go version)"
echo "✅ GOROOT: $(go env GOROOT)"
echo "✅ GOPATH: $(go env GOPATH)"

# 检查是否在正确的目录
if [ ! -f "main.go" ]; then
    echo "❌ 错误: 未找到main.go文件"
    echo "请确保在02_generate_data目录中运行此脚本"
    exit 1
fi

# 检查配置文件
echo "🔍 检查配置文件..."
if [ ! -f "config.yml" ]; then
    echo "❌ 错误: 未找到config.yml配置文件"
    echo "请确保配置文件存在"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 检查配置源并验证依赖服务
echo "🔍 检查配置源和依赖服务..."

# 读取配置源类型
CONFIG_SOURCE=$(grep "config_source:" config.yml | awk '{print $2}' | tr -d '"')
echo "📋 配置源类型: $CONFIG_SOURCE"

if [ "$CONFIG_SOURCE" = "api" ]; then
    # 检查API配置
    API_URL=$(grep "config_api:" config.yml | awk '{print $2}' | tr -d '"')
    echo "📡 API地址: $API_URL"

    # 提取主机和端口
    API_HOST="localhost"
    API_PORT="9005"

    # 检查API服务是否可达
    if ! nc -z $API_HOST $API_PORT 2>/dev/null; then
        echo "⚠️  警告: API服务 ($API_HOST:$API_PORT) 不可达"
        echo ""
        echo "🔧 API服务排查建议:"
        echo "  1. 启动12_server_api服务:"
        echo "     cd ../12_server_api && ./run.sh"
        echo ""
        echo "  2. 检查服务状态:"
        echo "     curl -s http://$API_HOST:$API_PORT/health"
        echo ""
        echo "  3. 检查端口占用:"
        echo "     lsof -i :$API_PORT"
        echo ""
        echo "  4. 临时解决方案 - 使用模板配置:"
        echo "     修改config.yml中的config_source为\"template\""
        echo ""
        echo "🔄 程序将启动API重连机制，定时尝试连接API服务"
        echo "   如果API服务在启动后恢复，程序会自动连接"
        echo "   建议先解决API服务问题以避免重连等待"
        echo ""
    else
        echo "✅ API服务检查通过 ($API_HOST:$API_PORT)"

        # 尝试测试API连接
        if command -v curl &> /dev/null; then
            if curl -s --connect-timeout 3 "$API_URL" &>/dev/null; then
                echo "✅ API连接测试成功"
            else
                echo "⚠️  API连接测试失败，可能需要认证或API未就绪"
            fi
        fi
    fi
elif [ "$CONFIG_SOURCE" = "template" ]; then
    echo "✅ 模板配置模式 - 无需外部依赖"

    # 检查是否有设备模板配置
    TEMPLATE_COUNT=$(grep -c "name:" config.yml | head -1)
    if [ "$TEMPLATE_COUNT" -gt 0 ]; then
        echo "✅ 发现设备模板配置"
    else
        echo "⚠️  警告: 未发现设备模板配置，请检查config.yml中的device_templates"
    fi
else
    echo "⚠️  未知的配置源类型: $CONFIG_SOURCE"
    echo "   支持的类型: api, template"
fi

# 检查端口是否被占用（如果有HTTP服务）
# 注意：02_generate_data可能不需要HTTP端口，这里预留
# PORT=8080
# if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
#     echo "⚠️  警告: 端口 $PORT 已被占用"
#     echo "正在尝试停止占用端口的进程..."
#     lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
#     sleep 2
# fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p data
mkdir -p build

# 下载Go依赖
echo "📦 下载Go依赖..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "❌ 错误: Go依赖下载失败"
    exit 1
fi

echo "✅ Go依赖下载完成"

echo ""
echo "🌐 启动数据生成服务..."
echo ""
echo "功能特性:"
echo "  - FANUC设备数据模拟"
echo "  - 实时数据生成"
echo "  - 设备状态模拟"
echo "  - 数据推送到采集服务"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 启动数据生成服务
go run .
