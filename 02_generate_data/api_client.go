/**
 * API客户端模块
 *
 * 功能概述：
 * 本模块负责与12_server_api服务进行通信，动态获取设备配置列表，
 * 实现设备配置的动态管理和实时同步，支持大规模设备的集中配置管理
 *
 * 主要功能：
 * - API通信：与12_server_api服务进行HTTP通信
 * - 配置获取：动态获取最新的设备配置列表
 * - 数据转换：将API返回的设备配置转换为内部设备模板
 * - 类型识别：智能识别设备类型（FANUC、温度、压力等）
 * - 配置刷新：支持设备配置的定时刷新和更新
 * - 错误处理：完善的网络错误和数据错误处理
 *
 * 技术特性：
 * - HTTP客户端：标准的HTTP客户端实现
 * - JSON解析：自动解析JSON格式的API响应
 * - 超时控制：配置化的请求超时时间
 * - 错误恢复：网络错误的自动重试和恢复
 * - 数据验证：API响应数据的完整性验证
 * - 类型安全：强类型的数据结构定义
 *
 * 集成架构：
 * - 上游服务：12_server_api设备管理服务
 * - 数据格式：标准化的设备配置JSON格式
 * - 配置映射：API配置到内部模板的智能映射
 * - 动态更新：支持运行时的配置动态更新
 *
 * 设备类型支持：
 * - FANUC：FANUC CNC机床设备
 * - 温度传感器：温度监测设备
 * - 压力传感器：压力监测设备
 * - 通用设备：其他类型的工业设备
 *
 * 业务价值：
 * - 集中管理：统一的设备配置管理
 * - 动态配置：无需重启即可更新设备配置
 * - 规模扩展：支持大规模设备的配置管理
 * - 运维简化：简化设备配置的运维工作
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"shared/logger"
)

// APIResponse API响应结构
type APIResponse struct {
	Success bool              `json:"success"`
	Data    []APIDeviceConfig `json:"data"`
	Message string            `json:"message"`
}

// loadDeviceConfigsFromAPI 从API加载设备配置（增强版）
func loadDeviceConfigsFromAPI(apiURL string) ([]APIDeviceConfig, error) {
	if apiURL == "" {
		return nil, fmt.Errorf("API配置URL为空")
	}

	logger.Infof("📡 从API获取设备配置: %s", apiURL)

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get(apiURL)
	if err != nil {
		// 构建详细的错误信息
		errorMsg := fmt.Sprintf("请求设备配置API失败: %v", err)
		errorMsg += fmt.Sprintf("\n\n🔍 API连接失败排查:")
		errorMsg += fmt.Sprintf("\n  API地址: %s", apiURL)
		errorMsg += fmt.Sprintf("\n  超时时间: 10秒")

		// 根据错误类型提供具体建议
		if contains(err.Error(), "connection refused") {
			errorMsg += fmt.Sprintf("\n\n❌ 连接被拒绝 - API服务可能未启动")
			errorMsg += fmt.Sprintf("\n  解决方案:")
			errorMsg += fmt.Sprintf("\n    1. 启动12_server_api服务: cd 12_server_api && ./run.sh")
			errorMsg += fmt.Sprintf("\n    2. 检查端口占用: lsof -i :9005")
			errorMsg += fmt.Sprintf("\n    3. 检查服务状态: curl -s http://localhost:9005/health")
		} else if contains(err.Error(), "timeout") {
			errorMsg += fmt.Sprintf("\n\n⏱️ 连接超时 - 服务响应慢或网络问题")
			errorMsg += fmt.Sprintf("\n  解决方案:")
			errorMsg += fmt.Sprintf("\n    1. 检查网络连接: ping localhost")
			errorMsg += fmt.Sprintf("\n    2. 检查服务负载: top | grep server_api")
			errorMsg += fmt.Sprintf("\n    3. 增加超时时间或重试")
		} else if contains(err.Error(), "no such host") {
			errorMsg += fmt.Sprintf("\n\n🌐 主机不存在 - 主机名解析失败")
			errorMsg += fmt.Sprintf("\n  解决方案:")
			errorMsg += fmt.Sprintf("\n    1. 检查主机名配置")
			errorMsg += fmt.Sprintf("\n    2. 使用IP地址替代主机名")
		}

		return nil, fmt.Errorf(errorMsg)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		errorMsg := fmt.Sprintf("设备配置API返回错误状态: %d", resp.StatusCode)
		errorMsg += fmt.Sprintf("\n\n🔍 HTTP状态码分析:")

		switch resp.StatusCode {
		case 404:
			errorMsg += fmt.Sprintf("\n  404 Not Found - API端点不存在")
			errorMsg += fmt.Sprintf("\n  检查API路径是否正确: %s", apiURL)
		case 500:
			errorMsg += fmt.Sprintf("\n  500 Internal Server Error - 服务器内部错误")
			errorMsg += fmt.Sprintf("\n  检查12_server_api服务日志")
		case 503:
			errorMsg += fmt.Sprintf("\n  503 Service Unavailable - 服务不可用")
			errorMsg += fmt.Sprintf("\n  服务可能正在启动或过载")
		default:
			errorMsg += fmt.Sprintf("\n  HTTP %d - 请检查API服务状态", resp.StatusCode)
		}

		return nil, fmt.Errorf(errorMsg)
	}

	var apiResponse APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %v\n请检查API返回的数据格式是否正确", err)
	}

	if !apiResponse.Success {
		return nil, fmt.Errorf("API返回错误: %s\n请检查API服务的业务逻辑", apiResponse.Message)
	}

	logger.Infof("✅ 成功从API获取 %d 个设备配置", len(apiResponse.Data))
	return apiResponse.Data, nil
}

// convertAPIDeviceToTemplate 将API设备配置转换为设备模板
func convertAPIDeviceToTemplate(apiDevice APIDeviceConfig, config *Config) (DeviceTemplate, error) {
	// 根据API设备的data_type匹配配置文件中的模板
	matchedTemplate, err := MatchDeviceTemplate(apiDevice.DataType, config.DeviceTemplates)
	if err != nil {
		// 如果没有找到匹配的模板，尝试使用默认配置
		logger.Warn("No matching template found for device type '%s', using default configuration", apiDevice.DataType)

		// 创建默认模板
		template := DeviceTemplate{
			Name:          fmt.Sprintf("%s_template", apiDevice.ID),
			Count:         1,
			Adapter:       "sensor", // 默认使用传感器适配器
			DataTypeRegex: ".*",     // 匹配所有类型
			PushInterval:  time.Duration(apiDevice.CollectInterval) * time.Millisecond,
			PushJitter:    time.Duration(apiDevice.CollectInterval/4) * time.Millisecond,
			Config: &SensorAdapterConfig{
				DataFields: map[string]DataFieldConfig{
					"value": {
						Type:  "float",
						Min:   0.0,
						Max:   100.0,
						Step:  0.1,
						Trend: "random",
					},
				},
			},
		}
		return template, nil
	}

	// 创建基于匹配模板的设备模板
	template := DeviceTemplate{
		Name:          fmt.Sprintf("%s_template", apiDevice.ID),
		Count:         1,
		Adapter:       matchedTemplate.Adapter,
		DataTypeRegex: matchedTemplate.DataTypeRegex,
		PushInterval:  time.Duration(apiDevice.CollectInterval) * time.Millisecond,
		PushJitter:    time.Duration(apiDevice.CollectInterval/4) * time.Millisecond,
		Config:        matchedTemplate.Config,
	}

	return template, nil
}

// getDefaultFanucConfig 获取默认FANUC配置
func getDefaultFanucConfig() *FanucConfig {
	return &FanucConfig{
		StatusConfig: StatusConfig{
			Running: StatusDuration{
				DurationMin: 300, // 5分钟
				DurationMax: 600, // 10分钟
				Weight:      60,  // 60%
			},
			Standby: StatusDuration{
				DurationMin: 120, // 2分钟
				DurationMax: 300, // 5分钟
				Weight:      30,  // 30%
			},
			Alarm: StatusDuration{
				DurationMin: 60,  // 1分钟
				DurationMax: 180, // 3分钟
				Weight:      10,  // 10%
			},
		},
		Programs: []ProgramConfig{
			{
				Name:        "MAIN_PROG_001",
				SubPrograms: []string{"SUB_001", "SUB_002", "SUB_003"},
			},
			{
				Name:        "MAIN_PROG_002",
				SubPrograms: []string{"SUB_004", "SUB_005"},
			},
			{
				Name:        "MAIN_PROG_003",
				SubPrograms: []string{"SUB_006", "SUB_007", "SUB_008", "SUB_009"},
			},
		},
		// 移除ProductionConfig，产量现在基于状态切换计算
	}
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					findSubstring(s, substr)))
}

// findSubstring 在字符串中查找子字符串
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// createDevicesFromAPI 从API创建设备列表（定时重连版本）
func (dm *DeviceManager) createDevicesFromAPI() error {
	logger.Infof("🚀 从API初始化设备...")

	// 启动API重连循环
	return dm.startAPIRetryLoop()
}

// startAPIRetryLoop 启动API重连循环
func (dm *DeviceManager) startAPIRetryLoop() error {
	retryInterval := time.Duration(dm.config.Devices.RefreshInterval) * time.Second
	maxRetries := 10 // 最大重试次数，避免无限重试
	retryCount := 0

	logger.Infof("🔄 启动API重连机制，重试间隔: %v", retryInterval)

	for retryCount < maxRetries {
		retryCount++

		logger.Infof("📡 尝试连接API (第 %d/%d 次)...", retryCount, maxRetries)

		// 尝试从API获取设备配置
		apiDevices, err := loadDeviceConfigsFromAPI(dm.config.Devices.ConfigAPI)
		if err != nil {
			logger.Errorf("❌ API连接失败 (第 %d/%d 次): %v", retryCount, maxRetries, err)
			logger.Errorf("")
			logger.Errorf("🔍 API连接失败排查建议:")
			logger.Errorf("   1. 检查API服务是否运行:")
			logger.Errorf("      curl -s %s", dm.config.Devices.ConfigAPI)
			logger.Errorf("   2. 检查12_server_api服务状态:")
			logger.Errorf("      cd 12_server_api && ./run.sh")
			logger.Errorf("   3. 检查网络连接:")
			logger.Errorf("      ping localhost")
			logger.Errorf("   4. 检查端口占用:")
			logger.Errorf("      lsof -i :9005")
			logger.Errorf("")

			if retryCount < maxRetries {
				logger.Warnf("⏳ 等待 %v 后重试...", retryInterval)
				time.Sleep(retryInterval)
				continue
			} else {
				logger.Errorf("❌ 已达到最大重试次数 (%d)，API连接失败", maxRetries)
				return fmt.Errorf("API连接失败，已重试 %d 次: %v", maxRetries, err)
			}
		}

		// API连接成功，处理设备配置
		if len(apiDevices) == 0 {
			logger.Warnf("⚠️  API返回的设备配置为空 (第 %d/%d 次)", retryCount, maxRetries)

			if retryCount < maxRetries {
				logger.Warnf("⏳ 等待 %v 后重试...", retryInterval)
				time.Sleep(retryInterval)
				continue
			} else {
				return fmt.Errorf("API返回空配置，已重试 %d 次", maxRetries)
			}
		}

		// 成功获取到设备配置
		logger.Infof("✅ API连接成功 (第 %d/%d 次)", retryCount, maxRetries)
		return dm.processAPIDevices(apiDevices)
	}

	return fmt.Errorf("API重连失败，已达到最大重试次数: %d", maxRetries)
}

// processAPIDevices 处理API返回的设备配置
func (dm *DeviceManager) processAPIDevices(apiDevices []APIDeviceConfig) error {
	fmt.Printf("📱 从API获取到 %d 个设备配置\n", len(apiDevices))

	// 为每个API设备创建设备实例
	enabledCount := 0
	for _, apiDevice := range apiDevices {
		if !apiDevice.Enabled {
			logger.Infof("⏭️  跳过未启用的设备: %s", apiDevice.ID)
			continue
		}

		// 转换为设备模板
		template, err := convertAPIDeviceToTemplate(apiDevice, dm.config)
		if err != nil {
			logger.Warnf("Failed to convert API device %s to template: %v", apiDevice.ID, err)
			continue
		}

		location := dm.getRandomLocation()

		// 🔧 修复Go循环变量引用问题：创建API配置的副本
		// 避免所有设备都引用同一个循环变量的地址
		apiConfigCopy := apiDevice

		// 创建设备实例（传递API配置信息的副本）
		device := NewDeviceWithAPIConfig(apiConfigCopy.ID, template, location, dm.config, &apiConfigCopy)
		dm.devices = append(dm.devices, device)

		// 添加到设备映射表和配置缓存
		dm.deviceMap[apiConfigCopy.ID] = device
		dm.currentAPIConfigs[apiConfigCopy.ID] = &apiConfigCopy

		enabledCount++

		fmt.Printf("   ✅ 创建设备 %d/%d: %s (%s)\n", enabledCount, len(apiDevices), apiConfigCopy.ID, apiConfigCopy.Name)
	}

	// 检查是否有启用的设备
	if enabledCount == 0 {
		return fmt.Errorf("API中没有启用的设备")
	}

	// 更新统计信息
	dm.stats.mutex.Lock()
	dm.stats.TotalDevices = len(dm.devices)
	dm.stats.ActiveDevices = len(dm.devices)
	dm.stats.mutex.Unlock()

	fmt.Printf("✅ 成功从API初始化 %d 个设备 (启用: %d, 总数: %d)\n", len(dm.devices), enabledCount, len(apiDevices))

	// 启动后台API监控和设备配置刷新
	go dm.startAPIMonitoring()

	return nil
}

// startAPIMonitoring 启动API监控和定时刷新
func (dm *DeviceManager) startAPIMonitoring() {
	refreshInterval := time.Duration(dm.config.Devices.RefreshInterval) * time.Second
	logger.Infof("🔄 启动API监控，刷新间隔: %v", refreshInterval)

	ticker := time.NewTicker(refreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dm.refreshDeviceConfigsFromAPI()
		case <-dm.ctx.Done():
			logger.Infof("🛑 API监控停止")
			return
		}
	}
}

// createDevicesFromTemplateAsFallback 降级策略：从模板创建设备
func (dm *DeviceManager) createDevicesFromTemplateAsFallback() error {
	logger.Infof("🔄 启用降级策略：从模板创建设备...")
	logger.Infof("   使用config.yml中的device_templates配置")

	// 临时保存原始配置源
	originalConfigSource := dm.config.Devices.ConfigSource

	// 临时切换到模板模式
	dm.config.Devices.ConfigSource = "template"

	// 调用原有的模板创建函数
	err := dm.createDevicesFromTemplate()

	// 恢复原始配置源
	dm.config.Devices.ConfigSource = originalConfigSource

	if err != nil {
		logger.Errorf("❌ 降级策略失败: %v", err)
		return fmt.Errorf("降级到模板配置失败: %v", err)
	}

	logger.Infof("✅ 降级策略成功：已使用模板配置创建 %d 个设备", len(dm.devices))
	logger.Infof("   注意：当前使用的是本地模板配置，不是API配置")
	logger.Infof("   如需使用API配置，请修复API服务后重启程序")

	return nil
}

// refreshDeviceConfigsFromAPI 从API刷新设备配置
func (dm *DeviceManager) refreshDeviceConfigsFromAPI() error {
	logger.Infof("🔄 从API刷新设备配置...")

	// 从API获取最新设备配置
	apiDevices, err := loadDeviceConfigsFromAPI(dm.config.Devices.ConfigAPI)
	if err != nil {
		logger.Warnf("⚠️  API刷新失败，继续使用当前配置: %v", err)
		logger.Warnf("   当前设备将继续运行，下次刷新时间: %v",
			time.Now().Add(time.Duration(dm.config.Devices.RefreshInterval)*time.Second).Format("15:04:05"))
		return nil // 刷新失败不应该导致程序退出
	}

	if len(apiDevices) == 0 {
		logger.Warnf("⚠️  API返回空配置，继续使用当前设备配置")
		return nil
	}

	logger.Infof("✅ 设备配置刷新成功，API返回 %d 个设备", len(apiDevices))

	// 检测配置变化
	added, removed, updated := dm.detectConfigChanges(apiDevices)

	// 输出变化统计
	if len(added) > 0 || len(removed) > 0 || len(updated) > 0 {
		logger.Infof("📊 配置变化检测结果:")
		logger.Infof("   ➕ 新增设备: %d 个", len(added))
		logger.Infof("   ➖ 删除设备: %d 个", len(removed))
		logger.Infof("   🔄 更新设备: %d 个", len(updated))

		// 处理配置变化
		if err := dm.processConfigChanges(added, removed, updated); err != nil {
			logger.Errorf("❌ 配置更新失败: %v", err)
			return err
		}

		logger.Infof("✅ 设备配置动态更新完成")

		// 输出当前设备状态
		dm.mutex.RLock()
		currentDeviceCount := len(dm.devices)
		dm.mutex.RUnlock()

		logger.Infof("📱 当前运行设备数量: %d", currentDeviceCount)
	} else {
		logger.Infof("📊 设备配置无变化，继续运行")
	}

	return nil
}
