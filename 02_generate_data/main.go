/**
 * 多设备数据生成器主程序
 *
 * 功能概述：
 * 本程序是制造业数据采集系统的测试和仿真工具，模拟多台工业设备同时向数据采集服务
 * 推送数据，用于验证系统的稳定性、并发处理能力和性能表现
 *
 * 核心功能：
 * - 设备仿真：根据配置文件创建多个独立的虚拟设备实例
 * - 数据生成：每个设备独立生成和推送数据，模拟真实设备行为
 * - 多类型支持：支持FANUC、西门子等多种设备类型和数据格式
 * - 状态模拟：模拟设备的运行、待机、报警等多种状态
 * - 实时监控：实时统计和监控所有设备的运行状态和性能指标
 * - 压力测试：支持高并发数据推送，验证系统承载能力
 * - 故障模拟：模拟网络故障、设备异常等故障场景
 *
 * 技术特性：
 * - 高并发：支持数百台设备同时运行，每台设备独立的goroutine
 * - 可配置：丰富的配置选项，支持不同测试场景
 * - 实时性：毫秒级的数据生成和推送频率
 * - 可观测：详细的日志记录和统计信息
 * - 优雅关闭：支持信号处理和优雅关闭机制
 *
 * 架构设计：
 * - 管理器模式：DeviceManager统一管理所有设备实例
 * - 工厂模式：根据配置动态创建不同类型的设备
 * - 并发模式：每个设备运行在独立的goroutine中
 * - 观察者模式：统一的状态监控和事件处理
 *
 * 业务场景：
 * - 系统测试：验证数据采集系统的功能和性能
 * - 压力测试：测试系统在高负载下的表现
 * - 故障测试：验证系统的容错能力和恢复机制
 * - 性能调优：为系统优化提供测试数据
 * - 演示展示：为客户演示系统功能
 *
 * 部署特性：
 * - 轻量级：单一可执行文件，无外部依赖
 * - 跨平台：支持Linux、Windows、macOS
 * - 配置驱动：通过配置文件控制所有行为
 * - 日志轮转：自动日志轮转，防止磁盘空间耗尽
 *
 * 使用方法：
 * ```bash
 * # 开发环境运行
 * go run . -config config.yml
 *
 * # 生产环境编译运行
 * go build -o generator .
 * ./generator -config config.yml
 *
 * # 指定不同配置文件
 * ./generator -config test_config.yml
 * ```
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"flag"
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/signal"
	"syscall"
	"time"

	"shared/logger"
)

/**
 * 主函数 - 多设备数据生成器入口点
 *
 * 功能：负责程序的完整生命周期管理，包括初始化、启动、运行和关闭
 *
 * 启动流程：
 * 1. 参数解析：解析命令行参数，获取配置文件路径
 * 2. 随机数初始化：初始化随机数种子，确保数据的随机性
 * 3. 配置加载：加载并验证配置文件
 * 4. 日志初始化：初始化日志系统和轮转配置
 * 5. 设备管理器创建：创建设备管理器实例
 * 6. 设备初始化：初始化所有虚拟设备
 * 7. 信号处理：设置优雅关闭的信号处理
 * 8. 设备启动：启动所有设备开始数据生成
 * 9. 等待关闭：等待中断信号并执行优雅关闭
 *
 * 错误处理：
 * - 任何关键步骤失败都会导致程序退出
 * - 详细的错误日志记录，便于问题诊断
 * - 优雅的错误处理，避免数据丢失
 *
 * 监控集成：
 * - 启动信息输出，便于运维监控
 * - 详细的配置信息记录
 * - 设备数量和运行模式显示
 *
 * @example
 * // 使用默认配置启动
 * go run .
 *
 * // 使用指定配置启动
 * go run . -config test_config.yml
 */
func main() {
	/** 解析命令行参数，获取配置文件路径 */
	configFile := flag.String("config", "config.yml", "配置文件路径")
	flag.Parse()

	/** 初始化随机数种子，确保每次运行生成不同的随机数据 */
	rand.Seed(time.Now().UnixNano())

	/** 输出启动信息，便于运维人员确认程序状态 */
	fmt.Printf("🚀 多设备数据生成器启动中...\n")
	fmt.Printf("📄 配置文件: %s\n", *configFile)

	// 加载配置文件
	config, err := LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("❌ 配置加载失败: %v", err)
	}

	fmt.Printf("✅ 配置加载成功\n")

	// 初始化日志系统
	logger.InitLoggerWithRotation(
		config.Logging.Level,
		config.Logging.Format,
		config.Logging.Output,
		logger.LogRotationConfig{
			Enabled:    config.Logging.Rotation.Enabled,
			MaxSize:    config.Logging.Rotation.MaxSize,
			MaxAge:     config.Logging.Rotation.MaxAge,
			MaxBackups: config.Logging.Rotation.MaxBackups,
			Compress:   config.Logging.Rotation.Compress,
		},
	)

	logger.Infof("🎯 目标服务: %s", config.Target.URL)
	logger.Infof("📱 总设备数: %d", config.Global.TotalDevices)
	logger.Infof("⏱️  运行模式: %s", config.Global.RunMode)

	// 创建设备管理器
	manager := NewDeviceManager(config)

	// 初始化所有设备
	if err := manager.Initialize(); err != nil {
		fmt.Printf("❌ 设备初始化失败\n")
		fmt.Printf("错误详情: %v\n", err)
		fmt.Printf("\n💡 解决建议:\n")

		if config.Devices.ConfigSource == "api" {
			fmt.Printf("   当前配置源: API模式\n")
			fmt.Printf("   1. 检查12_server_api服务: cd 12_server_api && ./run.sh\n")
			fmt.Printf("   2. 测试API连接: curl -s %s\n", config.Devices.ConfigAPI)
			fmt.Printf("   3. 检查MongoDB服务: docker ps | grep mongo\n")
			fmt.Printf("   4. 启动依赖服务: docker-compose up -d mongodb\n")
			fmt.Printf("   5. 临时解决方案: 修改config.yml中config_source为\"template\"\n")
			fmt.Printf("\n   注意: API模式下程序会自动重试连接，最多重试10次")
		} else {
			fmt.Printf("   当前配置源: 模板模式\n")
			fmt.Printf("   1. 检查config.yml中的device_templates配置\n")
			fmt.Printf("   2. 确保至少有一个设备模板配置\n")
		}

		fmt.Printf("   查看详细日志: tail -f logs/generator.log\n")
		fmt.Printf("\n")
		log.Fatalf("程序退出")
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动设备管理器
	if err := manager.Start(); err != nil {
		log.Fatalf("❌ 设备启动失败: %v", err)
	}

	// 等待中断信号
	go func() {
		<-sigChan
		fmt.Printf("\n🛑 接收到中断信号，正在停止所有设备...\n")
		manager.Stop()
	}()

	// 等待所有设备完成
	manager.Wait()

	fmt.Printf("👋 程序已退出\n")
}
