package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"shared/logger"
	"sync"
	"time"
)

/**
 * FANUC工厂实现
 *
 * 负责创建和管理FANUC设备的数据生成器
 * 支持FANUC CNC机床的状态模拟、程序执行、生产计数等功能
 * 支持产量数据持久化和按主程序分别计数
 */

// ProductionData 产量数据结构
type ProductionData struct {
	DeviceID    string `json:"device_id"`    // 设备ID
	MainProgram string `json:"main_program"` // 主程序名称
	Quantity    int    `json:"quantity"`     // 产量值
	UpdateTime  string `json:"update_time"`  // 更新时间
}

// ProductionManager 产量管理器
type ProductionManager struct {
	dataFile string                     // 数据文件路径
	data     map[string]*ProductionData // 设备产量数据 key: deviceID_mainProgram
	mutex    sync.RWMutex               // 读写锁
}

// 全局产量管理器
var productionManager *ProductionManager

// 初始化产量管理器
func init() {
	productionManager = NewProductionManager("data/production_data.json")
}

// NewProductionManager 创建产量管理器
func NewProductionManager(dataFile string) *ProductionManager {
	pm := &ProductionManager{
		dataFile: dataFile,
		data:     make(map[string]*ProductionData),
	}

	// 创建数据目录
	if err := os.MkdirAll(filepath.Dir(dataFile), 0755); err != nil {
		logger.Errorf("创建数据目录失败: %v", err)
	}

	// 加载现有数据
	pm.loadData()
	return pm
}

// loadData 加载产量数据
func (pm *ProductionManager) loadData() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if _, err := os.Stat(pm.dataFile); os.IsNotExist(err) {
		logger.Infof("📊 产量数据文件不存在，将创建新文件: %s", pm.dataFile)
		return
	}

	data, err := ioutil.ReadFile(pm.dataFile)
	if err != nil {
		logger.Errorf("读取产量数据文件失败: %v", err)
		return
	}

	var productionList []*ProductionData
	if err := json.Unmarshal(data, &productionList); err != nil {
		logger.Errorf("解析产量数据失败: %v", err)
		return
	}

	// 转换为map格式
	for _, prod := range productionList {
		key := fmt.Sprintf("%s_%s", prod.DeviceID, prod.MainProgram)
		pm.data[key] = prod
	}

	logger.Infof("📊 成功加载 %d 条产量数据", len(pm.data))
}

// saveData 保存产量数据
func (pm *ProductionManager) saveData() {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 转换为数组格式
	var productionList []*ProductionData
	for _, prod := range pm.data {
		productionList = append(productionList, prod)
	}

	data, err := json.MarshalIndent(productionList, "", "  ")
	if err != nil {
		logger.Errorf("序列化产量数据失败: %v", err)
		return
	}

	if err := ioutil.WriteFile(pm.dataFile, data, 0644); err != nil {
		logger.Errorf("保存产量数据失败: %v", err)
		return
	}

	logger.Debugf("📊 产量数据已保存到: %s", pm.dataFile)
}

// GetQuantity 获取设备指定主程序的产量
func (pm *ProductionManager) GetQuantity(deviceID, mainProgram string) int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	key := fmt.Sprintf("%s_%s", deviceID, mainProgram)
	if prod, exists := pm.data[key]; exists {
		return prod.Quantity
	}
	return 0
}

// UpdateQuantity 更新设备产量
func (pm *ProductionManager) UpdateQuantity(deviceID, mainProgram string, quantity int) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	key := fmt.Sprintf("%s_%s", deviceID, mainProgram)
	now := time.Now().Format("2006-01-02 15:04:05")

	if prod, exists := pm.data[key]; exists {
		// 更新现有记录
		prod.Quantity = quantity
		prod.UpdateTime = now
	} else {
		// 创建新记录
		pm.data[key] = &ProductionData{
			DeviceID:    deviceID,
			MainProgram: mainProgram,
			Quantity:    quantity,
			UpdateTime:  now,
		}
	}

	// 异步保存数据
	go pm.saveData()

	logger.Infof("📊 设备 %s 主程序 %s 产量更新为: %d", deviceID, mainProgram, quantity)
}

// ResetQuantity 重置设备产量（主程序变化时调用）
func (pm *ProductionManager) ResetQuantity(deviceID, oldMainProgram, newMainProgram string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 保存旧程序的最终产量
	oldKey := fmt.Sprintf("%s_%s", deviceID, oldMainProgram)
	if prod, exists := pm.data[oldKey]; exists {
		prod.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	}

	// 为新程序创建或重置产量记录
	newKey := fmt.Sprintf("%s_%s", deviceID, newMainProgram)
	now := time.Now().Format("2006-01-02 15:04:05")

	pm.data[newKey] = &ProductionData{
		DeviceID:    deviceID,
		MainProgram: newMainProgram,
		Quantity:    0, // 重新开始计数
		UpdateTime:  now,
	}

	// 异步保存数据
	go pm.saveData()

	logger.Infof("🔄 设备 %s 主程序从 %s 切换到 %s，产量重新开始计数", deviceID, oldMainProgram, newMainProgram)
}

// FanucFactory FANUC设备工厂
type FanucFactory struct{}

// NewFanucFactory 创建FANUC工厂
func NewFanucFactory() *FanucFactory {
	return &FanucFactory{}
}

// CreateGenerator 创建FANUC数据生成器
func (f *FanucFactory) CreateGenerator(deviceID string, config interface{}) (DataGenerator, error) {
	fanucConfig, ok := config.(*FanucAdapterConfig)
	if !ok {
		return nil, fmt.Errorf("invalid config type for fanuc adapter, expected *FanucAdapterConfig")
	}

	if err := f.ValidateConfig(fanucConfig); err != nil {
		return nil, fmt.Errorf("invalid fanuc config: %w", err)
	}

	return NewFanucDataGeneratorV2(deviceID, fanucConfig), nil
}

// ValidateConfig 验证FANUC配置
func (f *FanucFactory) ValidateConfig(config interface{}) error {
	fanucConfig, ok := config.(*FanucAdapterConfig)
	if !ok {
		return fmt.Errorf("invalid config type for fanuc adapter")
	}

	// 验证状态配置
	statusConfig := fanucConfig.StatusConfig
	totalWeight := statusConfig.Running.Weight + statusConfig.Standby.Weight + statusConfig.Alarm.Weight
	if totalWeight != 100 {
		return fmt.Errorf("status weights must sum to 100, got %d", totalWeight)
	}

	// 验证程序配置
	if len(fanucConfig.Programs) == 0 {
		return fmt.Errorf("at least one program must be configured")
	}

	return nil
}

// GetAdapterType 获取适配器类型
func (f *FanucFactory) GetAdapterType() string {
	return "fanuc"
}

// FanucDataGeneratorV2 FANUC数据生成器V2版本
type FanucDataGeneratorV2 struct {
	deviceID            string
	config              *FanucAdapterConfig
	currentStatus       string
	previousStatus      string // 上一个状态，用于检测状态切换
	statusStartTime     time.Time
	statusDuration      time.Duration
	cycleStartTime      time.Time
	cycleTime           time.Duration
	currentProgram      string
	mainProgram         string
	previousMainProgram string // 上一个主程序，用于检测程序切换
	totalParts          int    // 当前产量计数
	programRuntime      time.Duration
	lastUpdateTime      time.Time
}

// NewFanucDataGeneratorV2 创建FANUC数据生成器V2
func NewFanucDataGeneratorV2(deviceID string, config *FanucAdapterConfig) *FanucDataGeneratorV2 {
	generator := &FanucDataGeneratorV2{
		deviceID:       deviceID,
		config:         config,
		lastUpdateTime: time.Now(),
	}

	// 初始化状态
	generator.initializeStatus()
	generator.initializeProgram()
	generator.initializeCycle()

	// 加载产量数据
	generator.loadProductionData()

	return generator
}

// GenerateData 生成FANUC设备数据
func (g *FanucDataGeneratorV2) GenerateData() map[string]interface{} {
	now := time.Now()

	// 更新状态
	g.UpdateState()

	// 计算运行时间
	statusRuntime := int(now.Sub(g.statusStartTime).Seconds())
	programRuntime := int(g.programRuntime.Seconds())

	// 生成数据ID
	dataID, err := GenerateSnowflakeID()
	if err != nil {
		// 如果生成失败，使用时间戳作为备用方案
		dataID = fmt.Sprintf("%d", time.Now().UnixNano())
	}

	// 生成基础数据
	data := map[string]interface{}{
		"data_id":              dataID,
		"connected":            true,
		"status":               g.mapFanucStatusToStandard(g.currentStatus),
		"quantity":             g.totalParts, // 输出实际产量
		"current_program":      g.currentProgram,
		"main_program":         g.mainProgram,
		"actual_feedrate":      rand.Intn(1000) + 1500, // 1500-2500
		"actual_spindle_speed": rand.Intn(2000) + 3000, // 3000-5000
	}

	// 生成状态数据
	statusData := map[string]interface{}{
		"timestamp":       now.Unix(),
		"status_runtime":  statusRuntime,
		"program_runtime": programRuntime,
		"total_parts":     g.totalParts,
	}

	// 根据状态生成不同的数据
	switch g.currentStatus {
	case "running":
		// 生产状态：计算周期进度
		cycleRuntime := int(now.Sub(g.cycleStartTime).Seconds())
		cycleProgress := float64(cycleRuntime) / g.cycleTime.Seconds()

		statusData["cycle_progress"] = cycleProgress
		statusData["cycle_runtime"] = cycleRuntime
		statusData["cycle_time"] = int(g.cycleTime.Seconds())

		// 移除自动产量增加逻辑，产量现在基于状态切换

	case "standby":
		// 待机状态：周期进度为0
		statusData["cycle_progress"] = 0
		statusData["cycle_runtime"] = int(now.Sub(g.cycleStartTime).Seconds())
		statusData["cycle_time"] = 0

	case "alarm":
		// 故障状态：周期进度为0
		statusData["cycle_progress"] = 0
		statusData["cycle_runtime"] = int(now.Sub(g.cycleStartTime).Seconds())
		statusData["cycle_time"] = 0
	}

	data["status_data"] = statusData

	// 添加额外的数据字段
	for fieldName, fieldConfig := range g.config.DataFields {
		data[fieldName] = g.generateFieldValue(fieldConfig)
	}

	g.lastUpdateTime = now
	return data
}

// UpdateState 更新设备状态
func (g *FanucDataGeneratorV2) UpdateState() {
	now := time.Now()

	// 检查是否需要切换状态
	if now.Sub(g.statusStartTime) >= g.statusDuration {
		g.switchToNextStatus()
	}

	// 更新程序运行时间
	if g.currentStatus == "running" {
		g.programRuntime += now.Sub(g.lastUpdateTime)
	}
}

// GetDeviceID 获取设备ID
func (g *FanucDataGeneratorV2) GetDeviceID() string {
	return g.deviceID
}

// 私有方法

// initializeStatus 初始化设备状态
func (g *FanucDataGeneratorV2) initializeStatus() {
	// 根据权重随机选择初始状态
	g.currentStatus = g.selectRandomStatus()
	g.statusStartTime = time.Now()
	g.statusDuration = g.getStatusDuration(g.currentStatus)
}

// initializeProgram 初始化程序信息
func (g *FanucDataGeneratorV2) initializeProgram() {
	if len(g.config.Programs) > 0 {
		program := g.config.Programs[rand.Intn(len(g.config.Programs))]
		g.mainProgram = program.Name

		if len(program.SubPrograms) > 0 {
			g.currentProgram = program.SubPrograms[rand.Intn(len(program.SubPrograms))]
		} else {
			g.currentProgram = program.Name
		}
	}
}

// loadProductionData 加载产量数据
func (g *FanucDataGeneratorV2) loadProductionData() {
	// 从产量管理器获取当前主程序的产量
	quantity := productionManager.GetQuantity(g.deviceID, g.mainProgram)
	g.totalParts = quantity

	// 设置上一个主程序为当前主程序（初始化时）
	g.previousMainProgram = g.mainProgram

	if quantity > 0 {
		logger.Infof("📊 设备 %s 主程序 %s 加载产量数据: %d", g.deviceID, g.mainProgram, quantity)
	}
}

// initializeCycle 初始化生产周期
func (g *FanucDataGeneratorV2) initializeCycle() {
	g.startNewCycle()
}

// startNewCycle 开始新的生产周期
func (g *FanucDataGeneratorV2) startNewCycle() {
	g.cycleStartTime = time.Now()

	// 使用固定的周期时间范围（5-10分钟）
	minCycle := 300 // 5分钟
	maxCycle := 600 // 10分钟
	cycleSeconds := rand.Intn(maxCycle-minCycle+1) + minCycle
	g.cycleTime = time.Duration(cycleSeconds) * time.Second
}

// selectRandomStatus 根据权重随机选择状态
func (g *FanucDataGeneratorV2) selectRandomStatus() string {
	random := rand.Intn(100) + 1

	statusConfig := g.config.StatusConfig
	if random <= statusConfig.Running.Weight {
		return "running"
	} else if random <= statusConfig.Running.Weight+statusConfig.Standby.Weight {
		return "standby"
	} else {
		return "alarm"
	}
}

// getStatusDuration 获取状态持续时间
func (g *FanucDataGeneratorV2) getStatusDuration(status string) time.Duration {
	var minDuration, maxDuration int

	switch status {
	case "running":
		minDuration = g.config.StatusConfig.Running.DurationMin
		maxDuration = g.config.StatusConfig.Running.DurationMax
	case "standby":
		minDuration = g.config.StatusConfig.Standby.DurationMin
		maxDuration = g.config.StatusConfig.Standby.DurationMax
	case "alarm":
		minDuration = g.config.StatusConfig.Alarm.DurationMin
		maxDuration = g.config.StatusConfig.Alarm.DurationMax
	default:
		minDuration = 60
		maxDuration = 120
	}

	duration := rand.Intn(maxDuration-minDuration+1) + minDuration
	return time.Duration(duration) * time.Second
}

// switchToNextStatus 切换到下一个状态
func (g *FanucDataGeneratorV2) switchToNextStatus() {
	// 保存当前状态作为上一个状态
	g.previousStatus = g.currentStatus

	// 选择新状态
	g.currentStatus = g.selectRandomStatus()
	g.statusStartTime = time.Now()
	g.statusDuration = g.getStatusDuration(g.currentStatus)

	// 检查主程序是否发生变化（模拟程序切换）
	g.checkProgramChange()

	// 实现新的产量计数规则：
	// 当状态从 production 切换到 idle 时，产量 +1
	// 当状态从 production 切换到 fault 或 shutdown 时，产量不变
	if g.previousStatus == "running" && g.currentStatus == "standby" {
		g.totalParts++

		// 更新产量管理器中的数据
		productionManager.UpdateQuantity(g.deviceID, g.mainProgram, g.totalParts)

		logger.Infof("🎯 设备 %s 主程序 %s 完成一个生产周期，产量 +1，当前总产量: %d", g.deviceID, g.mainProgram, g.totalParts)
	}

	// 如果切换到运行状态，开始新的生产周期
	if g.currentStatus == "running" {
		g.startNewCycle()
	}
}

// checkProgramChange 检查主程序变化
func (g *FanucDataGeneratorV2) checkProgramChange() {
	// 有5%的概率切换主程序（模拟换产品）
	if rand.Float64() < 0.05 && len(g.config.Programs) > 1 {
		// 保存当前主程序
		g.previousMainProgram = g.mainProgram

		// 选择不同的主程序
		for {
			program := g.config.Programs[rand.Intn(len(g.config.Programs))]
			if program.Name != g.mainProgram {
				g.mainProgram = program.Name

				// 更新当前程序
				if len(program.SubPrograms) > 0 {
					g.currentProgram = program.SubPrograms[rand.Intn(len(program.SubPrograms))]
				} else {
					g.currentProgram = program.Name
				}
				break
			}
		}

		// 重置产量计数
		productionManager.ResetQuantity(g.deviceID, g.previousMainProgram, g.mainProgram)

		// 加载新主程序的产量数据
		g.totalParts = productionManager.GetQuantity(g.deviceID, g.mainProgram)

		logger.Infof("🔄 设备 %s 主程序切换: %s → %s，产量重新开始计数，当前产量: %d",
			g.deviceID, g.previousMainProgram, g.mainProgram, g.totalParts)
	}
}

// mapFanucStatusToStandard 将FANUC状态映射到标准状态
func (g *FanucDataGeneratorV2) mapFanucStatusToStandard(fanucStatus string) string {
	switch fanucStatus {
	case "running":
		return "production"
	case "standby":
		return "idle"
	case "alarm":
		return "fault"
	default:
		return "unknown"
	}
}

// generateFieldValue 生成数据字段值
func (g *FanucDataGeneratorV2) generateFieldValue(config DataFieldConfig) interface{} {
	switch config.Type {
	case "float":
		min := getFloat64(config.Min, 0.0)
		max := getFloat64(config.Max, 100.0)
		return min + rand.Float64()*(max-min)
	case "int":
		min := getInt64(config.Min, 0)
		max := getInt64(config.Max, 100)
		return min + rand.Int63n(max-min+1)
	case "bool":
		return rand.Float64() > 0.5
	case "string":
		return fmt.Sprintf("value_%d", rand.Intn(1000))
	default:
		return nil
	}
}
