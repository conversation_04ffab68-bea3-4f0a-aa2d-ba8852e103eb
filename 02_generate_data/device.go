/**
 * 设备模拟器模块
 *
 * 功能概述：
 * 本模块实现单个虚拟设备的数据生成和推送逻辑，是数据生成器的核心组件，
 * 负责模拟真实工业设备的数据产生、变化和传输行为
 *
 * 主要功能：
 * - 设备仿真：模拟真实工业设备的数据生成行为
 * - 数据变化：支持多种数据变化趋势（递增、递减、波形、随机）
 * - 定时推送：按配置的间隔定时推送数据到目标服务
 * - 故障模拟：模拟网络故障、超时等异常情况
 * - 统计监控：记录设备的运行统计和错误信息
 * - 多类型支持：支持FANUC、西门子等多种设备类型
 * - 并发安全：支持多设备并发运行
 *
 * 技术特性：
 * - 独立运行：每个设备运行在独立的goroutine中
 * - 上下文控制：使用context控制设备生命周期
 * - 并发安全：使用读写锁保护共享数据
 * - 重试机制：HTTP请求失败时的自动重试
 * - 抖动控制：推送间隔的随机抖动，避免同步效应
 * - 内存高效：合理的数据结构设计，减少内存占用
 *
 * 数据生成策略：
 * - 初始化：根据配置生成初始数据值
 * - 趋势变化：支持线性、波形、随机等变化模式
 * - 边界控制：数据值在配置的最小值和最大值之间变化
 * - 类型支持：支持float、int、bool、string等数据类型
 * - 专业模拟：FANUC设备的专业数据模拟
 *
 * 网络通信：
 * - HTTP客户端：使用标准HTTP客户端推送数据
 * - JSON格式：使用JSON格式传输设备数据
 * - 超时控制：配置化的请求超时时间
 * - 重试策略：指数退避的重试机制
 * - 错误处理：完善的网络错误处理
 *
 * 业务价值：
 * - 真实模拟：高度还原真实设备的数据特征
 * - 压力测试：支持大量设备的并发测试
 * - 故障验证：验证系统的容错和恢复能力
 * - 性能评估：为系统性能优化提供测试数据
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"math/rand"
	mathRand "math/rand"
	"net/http"
	"os"
	"path/filepath"
	"shared/logger"
	"sync"
	"time"

	"gopkg.in/natefinch/lumberjack.v2"
)

/**
 * 设备数据结构体 - 企业级规范格式
 *
 * 功能：定义设备推送到目标服务的标准化数据格式
 *
 * 数据组成：
 * - 设备标识：UUID格式的设备唯一标识符
 * - 时间戳：ISO 8601格式的精确时间戳
 * - 数据类型：标准化的设备类型标识
 * - 原始数据：结构化的设备传感器数据
 * - 元数据：设备配置和环境信息
 *
 * @struct DeviceData
 */
type DeviceData struct {
	/** 设备唯一标识符，从配置文件或API配置信息获取，如"fanuc_001", "temp_001" */
	DeviceID string `json:"device_id"`

	/** 数据生成的时间戳，ISO 8601格式，如"2024-01-01T00:00:00Z" */
	Timestamp time.Time `json:"timestamp"`

	/** 数据类型，标准化类型，如"fanuc_30i", "fanuc_0i", "siemens_840d"等 */
	DataType string `json:"data_type"`

	/** 设备的原始数据，结构化存储传感器和状态信息 */
	RawData RawDataStruct `json:"raw_data"`

	/** 设备元数据，包含配置和环境信息 */
	Metadata MetadataStruct `json:"metadata"`
}

/**
 * 原始数据结构体
 *
 * 功能：存储设备从传感器收集的原始数据
 *
 * @struct RawDataStruct
 */
type RawDataStruct struct {
	/** 数据唯一标识符，使用雪花ID，如"1000000000000000000" */
	DataID string `json:"data_id"`

	/** 设备是否连接，true表示连接，false表示断开 */
	Connected bool `json:"connected"`

	/** 设备状态：production(生产), idle(空闲), fault(报警), shutdown(关机), disconnected(未连接), maintenance(保养), debugging(调试) */
	Status string `json:"status"`

	/** 状态数据，包含具体的状态信息 */
	StatusData map[string]interface{} `json:"status_data"`

	/** 累计产量 */
	Quantity int `json:"quantity"`

	/** 当前加工程序的名称 */
	CurrentProgram string `json:"current_program"`

	/** 主加工程序的名称 */
	MainProgram string `json:"main_program"`

	/** 实际进给速度 (mm/min) */
	ActualFeedrate int `json:"actual_feedrate"`

	/** 实际主轴转速 (rpm) */
	ActualSpindleSpeed int `json:"actual_spindle_speed"`
}

/**
 * 元数据结构体
 *
 * 功能：包含关于设备的附加配置和环境信息
 *
 * @struct MetadataStruct
 */
type MetadataStruct struct {
	/** 设备的地理位置，如"Room 101, Building A" */
	Location string `json:"location"`

	/** 设备的型号或版本号，如"FANUC 30i" */
	DeviceName string `json:"device_name"`

	/** 设备的品牌，如"Fanuc" */
	Brand string `json:"brand"`

	/** 设备的型号，如"Fanuc 30i" */
	Model string `json:"model"`

	/** 设备的IP地址，如"*************" */
	IP string `json:"ip"`

	/** 设备的端口，如8193 */
	Port int `json:"port"`

	/** 设备的固件版本，如"v1.0.0"，可选 */
	FirmwareVersion string `json:"firmware_version,omitempty"`

	/** 设备的ID，可选，采集获取 */
	DeviceIDMeta string `json:"device_id,omitempty"`
}

/**
 * 设备模拟器结构体
 *
 * 功能：模拟单个工业设备的完整行为，包括数据生成、变化和推送
 *
 * 架构设计：
 * - 独立运行：每个设备实例独立运行，互不干扰
 * - 状态管理：维护设备的当前数据状态
 * - 网络通信：负责与目标服务的HTTP通信
 * - 统计监控：记录设备的运行统计信息
 *
 * 生命周期：
 * 1. 创建：根据模板和配置创建设备实例
 * 2. 初始化：初始化数据字段和网络客户端
 * 3. 启动：启动数据生成和推送循环
 * 4. 运行：持续生成和推送数据
 * 5. 停止：优雅停止并清理资源
 *
 * @struct Device
 */
type Device struct {
	/** 设备唯一标识符 */
	ID string

	/** 设备模板配置，定义设备的行为和数据格式 */
	Template DeviceTemplate

	/** 设备数据类型，从API或配置文件获取，如"fanuc_30i"、"siemens_840d"等 */
	DataType string

	/** 设备物理位置信息 */
	Location string

	/** 当前数据值，存储设备的实时状态 */
	CurrentData map[string]interface{}

	/** HTTP客户端，用于向目标服务推送数据 */
	Client *http.Client

	/** 全局配置引用，包含目标服务和运行参数 */
	Config *Config

	/** 设备运行统计信息 */
	Stats *DeviceStats

	/** 数据生成器（工厂模式），根据设备类型动态创建 */
	DataGenerator DataGenerator

	/** FANUC专用数据生成器，用于生成专业的FANUC数据（保持向后兼容） */
	FanucGenerator *FanucDataGenerator

	/** 上下文对象，用于控制设备生命周期 */
	ctx context.Context

	/** 取消函数，用于停止设备运行 */
	cancel context.CancelFunc

	/** API设备配置信息，从配置文件或API获取，包含IP、端口等信息 */
	APIConfig *APIDeviceConfig

	/** 读写锁，保护CurrentData的并发访问 */
	mutex sync.RWMutex

	/** 设备日志写入器，支持同时输出到终端和文件 */
	deviceLogWriter io.Writer
}

/**
 * 设备统计信息结构体
 *
 * 功能：记录单个设备的运行统计信息，用于监控和分析
 *
 * 统计维度：
 * - 发送统计：总发送数、成功数、错误数
 * - 时间信息：最后发送时间
 * - 错误信息：最后错误的详细信息
 *
 * @struct DeviceStats
 */
type DeviceStats struct {
	/** 总数据发送次数 */
	TotalSent int64

	/** 成功发送的次数 */
	SuccessCount int64

	/** 发送失败的次数 */
	ErrorCount int64

	/** 最后一次发送数据的时间 */
	LastSentTime time.Time

	/** 最后一次错误的详细信息 */
	LastError string

	/** 读写锁，保护统计数据的并发访问 */
	mutex sync.RWMutex
}

// NewDevice 创建新的设备实例
func NewDevice(id string, template DeviceTemplate, location string, config *Config) *Device {
	return NewDeviceWithAPIConfig(id, template, location, config, nil)
}

// NewDeviceWithAPIConfig 创建新的设备实例（带API配置）
func NewDeviceWithAPIConfig(id string, template DeviceTemplate, location string, config *Config, apiConfig *APIDeviceConfig) *Device {
	ctx, cancel := context.WithCancel(context.Background())

	// 确定设备数据类型：优先使用API配置，其次根据模板名称推断
	dataType := "fanuc" // 默认数据类型
	if apiConfig != nil && apiConfig.DataType != "" {
		// 从API配置获取数据类型
		dataType = apiConfig.DataType
	} else {
		// 根据模板适配器类型推断数据类型
		switch template.Adapter {
		case "fanuc":
			dataType = "fanuc"
		case "brother":
			dataType = "brother"
		case "modbus":
			dataType = "modbus"
		case "sensor":
			// 根据模板名称进一步推断传感器类型
			switch {
			case contains(template.Name, "temperature"):
				dataType = "temperature"
			case contains(template.Name, "pressure"):
				dataType = "pressure"
			case contains(template.Name, "motion"):
				dataType = "motion"
			default:
				dataType = "sensor"
			}
		default:
			dataType = "fanuc" // 默认为FANUC类型
		}
	}

	device := &Device{
		ID:          id,
		Template:    template,
		DataType:    dataType, // 从API或配置文件获取的数据类型
		Location:    location,
		CurrentData: make(map[string]interface{}),
		Client: &http.Client{
			Timeout: config.Target.Timeout,
		},
		Config: config,
		Stats: &DeviceStats{
			LastSentTime: time.Now(),
		},
		ctx:       ctx,
		cancel:    cancel,
		APIConfig: apiConfig, // 从配置文件或API配置信息获取
	}

	// 使用工厂模式创建数据生成器
	if template.Adapter != "" {
		// 解析适配器配置
		adapterConfig, err := ParseAdapterConfig(template.Adapter, template.Config)
		if err != nil {
			logger.Warnf("Failed to parse adapter config for device %s: %v", id, err)
		} else {
			// 使用工厂创建数据生成器
			generator, err := GlobalFactoryManager.CreateGenerator(template.Adapter, id, adapterConfig)
			if err != nil {
				logger.Warnf("Failed to create data generator for device %s: %v", id, err)
			} else {
				device.DataGenerator = generator
			}
		}
	}

	// 保持向后兼容：如果是FANUC设备且没有使用工厂模式，使用旧的FANUC生成器
	if device.DataGenerator == nil && template.Adapter == "fanuc" {
		// 尝试从Config中获取FANUC配置
		if fanucConfig, ok := template.Config.(*FanucAdapterConfig); ok {
			// 转换为旧的FanucConfig格式（移除ProductionConfig）
			oldFanucConfig := &FanucConfig{
				StatusConfig: fanucConfig.StatusConfig,
				Programs:     fanucConfig.Programs,
			}
			device.FanucGenerator = NewFanucDataGenerator(id, oldFanucConfig)
		}
	}

	// 初始化设备日志写入器
	device.initializeDeviceLogWriter()

	// 初始化数据字段
	device.initializeData()

	return device
}

// initializeDeviceLogWriter 初始化设备日志写入器
// 当device_logs为true时，同时输出到终端和文件，应用日志轮转、压缩等管理功能
func (d *Device) initializeDeviceLogWriter() {
	if d.Config.Logging.DeviceLogs {
		// 创建设备专用日志文件路径
		logDir := filepath.Dir(d.Config.Logging.Output)
		deviceLogFile := filepath.Join(logDir, fmt.Sprintf("device_%s.log", d.ID))

		// 确保日志目录存在
		if err := os.MkdirAll(logDir, 0755); err != nil {
			// 如果创建目录失败，只输出到终端
			d.deviceLogWriter = os.Stdout
			return
		}

		// 创建lumberjack日志写入器，应用轮转配置
		lumberjackLogger := &lumberjack.Logger{
			Filename:   deviceLogFile,                        // 日志文件路径
			MaxSize:    d.Config.Logging.Rotation.MaxSize,    // 单个文件最大大小(MB)
			MaxAge:     d.Config.Logging.Rotation.MaxAge,     // 文件保留天数
			MaxBackups: d.Config.Logging.Rotation.MaxBackups, // 保留的备份文件数量
			Compress:   d.Config.Logging.Rotation.Compress,   // 是否压缩旧文件
			LocalTime:  true,                                 // 使用本地时间
		}

		// 创建多重写入器，同时写入终端和文件（应用轮转管理）
		d.deviceLogWriter = io.MultiWriter(os.Stdout, lumberjackLogger)
	} else {
		// 如果不启用设备日志，使用空写入器
		d.deviceLogWriter = io.Discard
	}
}

// initializeData 初始化设备数据字段
func (d *Device) initializeData() {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// 如果使用工厂模式的数据生成器，不需要初始化数据字段
	if d.DataGenerator != nil {
		return
	}

	// 尝试从Config中获取数据字段配置
	var dataFields map[string]DataFieldConfig

	// 根据适配器类型解析配置
	switch d.Template.Adapter {
	case "fanuc":
		if fanucConfig, ok := d.Template.Config.(*FanucAdapterConfig); ok {
			dataFields = fanucConfig.DataFields
		}
	case "sensor":
		if sensorConfig, ok := d.Template.Config.(*SensorAdapterConfig); ok {
			dataFields = sensorConfig.DataFields
		}
	}

	// 初始化数据字段
	for fieldName, fieldConfig := range dataFields {
		switch fieldConfig.Type {
		case "float":
			min := getFloat64(fieldConfig.Min, 0.0)
			max := getFloat64(fieldConfig.Max, 100.0)
			d.CurrentData[fieldName] = min + mathRand.Float64()*(max-min)
		case "int":
			min := getInt64(fieldConfig.Min, 0)
			max := getInt64(fieldConfig.Max, 100)
			d.CurrentData[fieldName] = min + mathRand.Int63n(max-min+1)
		case "bool":
			d.CurrentData[fieldName] = mathRand.Float64() > 0.5
		case "string":
			d.CurrentData[fieldName] = fmt.Sprintf("value_%d", mathRand.Intn(1000))
		}
	}
}

// Start 启动设备数据推送
func (d *Device) Start() {
	go d.pushLoop()
}

// Stop 停止设备
func (d *Device) Stop() {
	d.cancel()
}

// pushLoop 数据推送循环
func (d *Device) pushLoop() {
	// 计算推送间隔（包含抖动）
	baseInterval := d.Template.PushInterval
	jitter := d.Template.PushJitter

	for {
		select {
		case <-d.ctx.Done():
			return
		default:
			// 更新数据
			d.updateData()

			// 推送数据
			d.pushData()

			// 计算下次推送时间（添加随机抖动）
			interval := baseInterval
			if jitter > 0 {
				jitterMs := int64(jitter / time.Millisecond)
				randomJitter := time.Duration(mathRand.Int63n(jitterMs*2)-jitterMs) * time.Millisecond
				interval += randomJitter
			}

			// 等待下次推送
			select {
			case <-time.After(interval):
			case <-d.ctx.Done():
				return
			}
		}
	}
}

// updateData 更新设备数据
func (d *Device) updateData() {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// 优先使用工厂模式的数据生成器
	if d.DataGenerator != nil {
		// 更新生成器状态
		d.DataGenerator.UpdateState()

		// 生成新数据
		generatedData := d.DataGenerator.GenerateData()

		// 将生成的数据复制到CurrentData
		for k, v := range generatedData {
			d.CurrentData[k] = v
		}
		return
	}

	// 向后兼容：如果没有工厂生成器，使用旧的FANUC生成器
	if d.FanucGenerator != nil {
		fanucData := d.FanucGenerator.GenerateData()
		// 将FANUC数据复制到CurrentData
		for k, v := range fanucData {
			d.CurrentData[k] = v
		}
		return
	}

	// 最后的降级方案：使用原有的数据字段逻辑
	var dataFields map[string]DataFieldConfig

	// 根据适配器类型解析配置
	switch d.Template.Adapter {
	case "fanuc":
		if fanucConfig, ok := d.Template.Config.(*FanucAdapterConfig); ok {
			dataFields = fanucConfig.DataFields
		}
	case "sensor":
		if sensorConfig, ok := d.Template.Config.(*SensorAdapterConfig); ok {
			dataFields = sensorConfig.DataFields
		}
	}

	for fieldName, fieldConfig := range dataFields {
		currentValue := d.CurrentData[fieldName]

		switch fieldConfig.Type {
		case "float":
			d.CurrentData[fieldName] = d.updateFloatValue(currentValue, fieldConfig)
		case "int":
			d.CurrentData[fieldName] = d.updateIntValue(currentValue, fieldConfig)
		case "bool":
			d.CurrentData[fieldName] = d.updateBoolValue(currentValue, fieldConfig)
		}
	}
}

// updateFloatValue 更新浮点数值
func (d *Device) updateFloatValue(current interface{}, config DataFieldConfig) float64 {
	currentVal := getFloat64(current, 0.0)
	min := getFloat64(config.Min, 0.0)
	max := getFloat64(config.Max, 100.0)
	step := getFloat64(config.Step, 1.0)

	switch config.Trend {
	case "increase":
		newVal := currentVal + step
		if newVal > max {
			return max
		}
		return newVal
	case "decrease":
		newVal := currentVal - step
		if newVal < min {
			return min
		}
		return newVal
	case "wave":
		// 正弦波变化
		t := float64(time.Now().Unix()) / 60.0 // 以分钟为周期
		amplitude := (max - min) / 2
		center := (max + min) / 2
		return center + amplitude*math.Sin(t*2*math.Pi/10) // 10分钟周期
	default: // random
		change := (mathRand.Float64() - 0.5) * step * 2
		newVal := currentVal + change
		if newVal < min {
			return min
		}
		if newVal > max {
			return max
		}
		return newVal
	}
}

// updateIntValue 更新整数值
func (d *Device) updateIntValue(current interface{}, config DataFieldConfig) int64 {
	currentVal := getInt64(current, 0)
	min := getInt64(config.Min, 0)
	max := getInt64(config.Max, 100)
	step := getInt64(config.Step, 1)

	switch config.Trend {
	case "increase":
		newVal := currentVal + step
		if newVal > max {
			return max
		}
		return newVal
	case "decrease":
		newVal := currentVal - step
		if newVal < min {
			return min
		}
		return newVal
	default: // random
		change := mathRand.Int63n(step*2+1) - step
		newVal := currentVal + change
		if newVal < min {
			return min
		}
		if newVal > max {
			return max
		}
		return newVal
	}
}

// updateBoolValue 更新布尔值
func (d *Device) updateBoolValue(current interface{}, config DataFieldConfig) bool {
	// 布尔值有10%的概率改变
	if mathRand.Float64() < 0.1 {
		currentVal, _ := current.(bool)
		return !currentVal
	}
	currentVal, _ := current.(bool)
	return currentVal
}

// pushData 推送数据到目标服务
func (d *Device) pushData() {
	d.mutex.RLock()
	data := make(map[string]interface{})
	for k, v := range d.CurrentData {
		data[k] = v
	}
	d.mutex.RUnlock()

	deviceData := DeviceData{
		DeviceID:  d.ID, // 从配置文件或API配置信息获取
		DataType:  d.DataType,
		Timestamp: time.Now().UTC(), // 修复时区问题：使用UTC时间，确保时间戳一致性
		RawData:   d.convertMapToRawData(data),
		Metadata:  d.createMetadata(),
	}

	// 更新统计
	d.Stats.mutex.Lock()
	d.Stats.TotalSent++
	d.Stats.LastSentTime = time.Now()
	d.Stats.mutex.Unlock()

	// 发送数据
	if err := d.sendData(deviceData); err != nil {
		d.Stats.mutex.Lock()
		d.Stats.ErrorCount++
		d.Stats.LastError = err.Error()
		d.Stats.mutex.Unlock()

		if d.Config.Logging.DeviceLogs {
			fmt.Fprintf(d.deviceLogWriter, "[ERROR] Device %s: %v\n", d.ID, err)
		}
	} else {
		d.Stats.mutex.Lock()
		d.Stats.SuccessCount++
		d.Stats.LastError = ""
		d.Stats.mutex.Unlock()

		if d.Config.Logging.DeviceLogs {
			fmt.Fprintf(d.deviceLogWriter, "[INFO] Device %s: Data sent successfully\n", d.ID)
		}
	}
}

// sendData 发送数据到目标服务
func (d *Device) sendData(data DeviceData) error {
	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		d.logPushError("数据序列化失败", d.Config.Target.URL, data, err)
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	// 记录推送开始日志
	d.logPushStart(d.Config.Target.URL, data, jsonData)

	req, err := http.NewRequestWithContext(d.ctx, "POST", d.Config.Target.URL, bytes.NewBuffer(jsonData))
	if err != nil {
		d.logPushError("创建HTTP请求失败", d.Config.Target.URL, data, err)
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// 重试机制
	var lastErr error
	for i := 0; i <= d.Config.Target.RetryCount; i++ {
		// 记录重试日志
		if i > 0 {
			d.logPushRetry(d.Config.Target.URL, data, i, d.Config.Target.RetryCount)
		}

		resp, err := d.Client.Do(req)
		if err != nil {
			lastErr = err
			d.logPushError(fmt.Sprintf("HTTP请求失败 (第%d次尝试)", i+1), d.Config.Target.URL, data, err)
			if i < d.Config.Target.RetryCount {
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			break
		}

		// 读取响应内容
		respBody, readErr := ioutil.ReadAll(resp.Body)
		resp.Body.Close()

		if readErr != nil {
			d.logPushError("读取响应失败", d.Config.Target.URL, data, readErr)
		}

		if resp.StatusCode >= 200 && resp.StatusCode < 300 {
			// 记录推送成功日志
			d.logPushSuccess(d.Config.Target.URL, data, resp.StatusCode, respBody)
			return nil
		}

		lastErr = fmt.Errorf("HTTP %d", resp.StatusCode)
		d.logPushError(fmt.Sprintf("HTTP状态码错误 (第%d次尝试)", i+1), d.Config.Target.URL, data, lastErr, resp.StatusCode, respBody)

		if i < d.Config.Target.RetryCount {
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	// 记录最终失败日志
	d.logPushFinalError(d.Config.Target.URL, data, d.Config.Target.RetryCount, lastErr)
	return fmt.Errorf("failed after %d retries: %w", d.Config.Target.RetryCount, lastErr)
}

// GetStats 获取设备统计信息
func (d *Device) GetStats() DeviceStats {
	d.Stats.mutex.RLock()
	defer d.Stats.mutex.RUnlock()
	return *d.Stats
}

// 辅助函数
func getFloat64(value interface{}, defaultVal float64) float64 {
	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int:
		return float64(v)
	case int64:
		return float64(v)
	default:
		return defaultVal
	}
}

func getInt64(value interface{}, defaultVal int64) int64 {
	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	case float32:
		return int64(v)
	default:
		return defaultVal
	}
}

// logPushStart 记录推送开始日志
func (d *Device) logPushStart(url string, data DeviceData, jsonData []byte) {
	if d.Config.Logging.DeviceLogs {
		// 显示完整的数据内容，不进行截断
		dataStr := string(jsonData)

		fmt.Fprintf(d.deviceLogWriter, "[PUSH-START] 设备 %s 开始推送数据\n", d.ID)
		fmt.Fprintf(d.deviceLogWriter, "  📡 目标URL: %s\n", url)
		fmt.Fprintf(d.deviceLogWriter, "  📊 数据类型: %s\n", data.DataType)
		fmt.Fprintf(d.deviceLogWriter, "  🕐 时间戳: %s\n", data.Timestamp.Format("2006-01-02 15:04:05"))
		fmt.Fprintf(d.deviceLogWriter, "  📍 位置: %s\n", data.Metadata.Location)
		fmt.Fprintf(d.deviceLogWriter, "  📦 数据内容: %s\n", dataStr)
		fmt.Fprintf(d.deviceLogWriter, "  📏 数据大小: %d bytes\n", len(jsonData))
		fmt.Fprintf(d.deviceLogWriter, "  ----------------------------------------\n")
	}
}

// logPushSuccess 记录推送成功日志
func (d *Device) logPushSuccess(url string, data DeviceData, statusCode int, respBody []byte) {
	if d.Config.Logging.DeviceLogs {
		// 显示完整的响应内容，不进行截断
		respStr := string(respBody)

		fmt.Fprintf(d.deviceLogWriter, "[PUSH-SUCCESS] 设备 %s 推送成功 ✅\n", d.ID)
		fmt.Fprintf(d.deviceLogWriter, "  📡 目标URL: %s\n", url)
		fmt.Fprintf(d.deviceLogWriter, "  📊 数据类型: %s\n", data.DataType)
		fmt.Fprintf(d.deviceLogWriter, "  🔢 HTTP状态码: %d\n", statusCode)
		fmt.Fprintf(d.deviceLogWriter, "  📥 服务器响应: %s\n", respStr)
		fmt.Fprintf(d.deviceLogWriter, "  📏 响应大小: %d bytes\n", len(respBody))
		fmt.Fprintf(d.deviceLogWriter, "  ⏱️  推送时间: %s\n", time.Now().Format("15:04:05"))
		fmt.Fprintf(d.deviceLogWriter, "  ----------------------------------------\n")
	}
}

// logPushError 记录推送错误日志
func (d *Device) logPushError(stage string, url string, data DeviceData, err error, args ...interface{}) {
	if d.Config.Logging.DeviceLogs {
		fmt.Fprintf(d.deviceLogWriter, "[PUSH-ERROR] 设备 %s 推送失败 ❌\n", d.ID)
		fmt.Fprintf(d.deviceLogWriter, "  📡 目标URL: %s\n", url)
		fmt.Fprintf(d.deviceLogWriter, "  📊 数据类型: %s\n", data.DataType)
		fmt.Fprintf(d.deviceLogWriter, "  🚨 错误阶段: %s\n", stage)
		fmt.Fprintf(d.deviceLogWriter, "  ❌ 错误信息: %v\n", err)

		// 处理额外参数（HTTP状态码和响应体）
		if len(args) >= 2 {
			if statusCode, ok := args[0].(int); ok {
				fmt.Fprintf(d.deviceLogWriter, "  🔢 HTTP状态码: %d\n", statusCode)
			}
			if respBody, ok := args[1].([]byte); ok {
				// 显示完整的响应内容，不进行截断
				respStr := string(respBody)
				fmt.Fprintf(d.deviceLogWriter, "  📥 服务器响应: %s\n", respStr)
			}
		}

		fmt.Fprintf(d.deviceLogWriter, "  ⏱️  错误时间: %s\n", time.Now().Format("15:04:05"))
		fmt.Fprintf(d.deviceLogWriter, "  ----------------------------------------\n")
	}
}

// logPushRetry 记录推送重试日志
func (d *Device) logPushRetry(url string, data DeviceData, retryCount int, maxRetries int) {
	if d.Config.Logging.DeviceLogs {
		fmt.Fprintf(d.deviceLogWriter, "[PUSH-RETRY] 设备 %s 开始重试 🔄\n", d.ID)
		fmt.Fprintf(d.deviceLogWriter, "  📡 目标URL: %s\n", url)
		fmt.Fprintf(d.deviceLogWriter, "  📊 数据类型: %s\n", data.DataType)
		fmt.Fprintf(d.deviceLogWriter, "  🔄 重试次数: %d/%d\n", retryCount, maxRetries)
		fmt.Fprintf(d.deviceLogWriter, "  ⏱️  重试时间: %s\n", time.Now().Format("15:04:05"))
		fmt.Fprintf(d.deviceLogWriter, "  ----------------------------------------\n")
	}
}

// logPushFinalError 记录最终推送失败日志
func (d *Device) logPushFinalError(url string, data DeviceData, maxRetries int, err error) {
	if d.Config.Logging.DeviceLogs {
		fmt.Fprintf(d.deviceLogWriter, "[PUSH-FINAL-ERROR] 设备 %s 推送最终失败 💥\n", d.ID)
		fmt.Fprintf(d.deviceLogWriter, "  📡 目标URL: %s\n", url)
		fmt.Fprintf(d.deviceLogWriter, "  📊 数据类型: %s\n", data.DataType)
		fmt.Fprintf(d.deviceLogWriter, "  🔄 总重试次数: %d\n", maxRetries)
		fmt.Fprintf(d.deviceLogWriter, "  ❌ 最终错误: %v\n", err)
		fmt.Fprintf(d.deviceLogWriter, "  ⏱️  失败时间: %s\n", time.Now().Format("15:04:05"))
		fmt.Fprintf(d.deviceLogWriter, "  ========================================\n")
	}
}

// generateUUID 生成UUID格式的设备ID
func generateUUID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)

	// 设置版本号 (4) 和变体位
	bytes[6] = (bytes[6] & 0x0f) | 0x40 // 版本4
	bytes[8] = (bytes[8] & 0x3f) | 0x80 // 变体位

	return fmt.Sprintf("%x-%x-%x-%x-%x",
		bytes[0:4], bytes[4:6], bytes[6:8], bytes[8:10], bytes[10:16])
}

// 注意：generateSnowflakeID函数已在snowflake.go中定义，这里不重复定义

// convertMapToRawData 将map数据转换为RawDataStruct
func (d *Device) convertMapToRawData(data map[string]interface{}) RawDataStruct {
	// 默认状态设置为idle，但会被实际数据覆盖
	defaultStatus := "idle"

	// 如果是FANUC设备，从数据中获取状态并进行状态映射
	if status, ok := data["status"].(string); ok {
		// FANUC状态映射到标准设备状态
		switch status {
		case "running":
			defaultStatus = "production" // 运行状态映射为生产状态
		case "standby":
			defaultStatus = "idle" // 待机状态映射为空闲状态
		case "alarm":
			defaultStatus = "fault" // 报警状态映射为故障状态
		default:
			defaultStatus = status // 其他状态保持不变
		}
	}

	// 生成数据ID
	dataID, err := GenerateSnowflakeID()
	if err != nil {
		// 如果生成失败，使用时间戳作为备用方案
		dataID = fmt.Sprintf("%d", time.Now().UnixNano())
	}

	rawData := RawDataStruct{
		DataID:             dataID,
		Connected:          true,
		Status:             defaultStatus, // 使用实际状态或默认状态
		StatusData:         make(map[string]interface{}),
		Quantity:           0,
		CurrentProgram:     "MAIN_PROG_001",
		MainProgram:        "MAIN_PROG_001",
		ActualFeedrate:     1500,
		ActualSpindleSpeed: 3000,
	}

	// 从原始数据中提取特定字段（状态已经在上面映射过了，这里不再重复设置）
	// 状态字段已经通过映射设置，不需要再次覆盖
	if quantity, ok := data["quantity"].(int); ok {
		rawData.Quantity = quantity
	}
	if currentProg, ok := data["current_program"].(string); ok {
		rawData.CurrentProgram = currentProg
	}
	if mainProg, ok := data["main_program"].(string); ok {
		rawData.MainProgram = mainProg
	}
	if feedrate, ok := data["actual_feedrate"].(int); ok {
		rawData.ActualFeedrate = feedrate
	}
	if spindleSpeed, ok := data["actual_spindle_speed"].(int); ok {
		rawData.ActualSpindleSpeed = spindleSpeed
	}

	// 将其他数据放入StatusData
	for k, v := range data {
		if k != "status" && k != "quantity" && k != "current_program" &&
			k != "main_program" && k != "actual_feedrate" && k != "actual_spindle_speed" {
			rawData.StatusData[k] = v
		}
	}

	return rawData
}

// createMetadata 创建设备元数据（从配置文件或API配置信息获取）
func (d *Device) createMetadata() MetadataStruct {
	// 初始化默认元数据
	metadata := MetadataStruct{
		Location:        d.Location,
		DeviceName:      d.Template.Name,
		Brand:           "Generic",
		Model:           "Generic Model",
		IP:              "*************", // 默认值，如果有API配置会被覆盖
		Port:            8193,            // 默认值，如果有API配置会被覆盖
		FirmwareVersion: "v1.0.0",
		DeviceIDMeta:    d.ID,
	}

	// 优先使用API配置信息中的metadata字段
	if d.APIConfig != nil {
		// 基本网络信息
		if d.APIConfig.IP != "" {
			metadata.IP = d.APIConfig.IP
		}
		if d.APIConfig.Port > 0 {
			metadata.Port = d.APIConfig.Port
		}

		// 设备基本信息
		if d.APIConfig.Name != "" {
			metadata.DeviceName = d.APIConfig.Name
		}
		if d.APIConfig.Location != "" {
			metadata.Location = d.APIConfig.Location
		}

		// 设备metadata信息（优先级最高）
		// 注意：使用name字段而不是device_name，因为name字段已经包含了设备名称信息
		if d.APIConfig.Brand != "" {
			metadata.Brand = d.APIConfig.Brand
		}
		if d.APIConfig.Model != "" {
			metadata.Model = d.APIConfig.Model
		}

		logger.Debugf("🔧 Using API metadata for device %s: brand=%s, model=%s, device_name=%s",
			d.ID, metadata.Brand, metadata.Model, metadata.DeviceName)
	}

	// 如果API配置中没有metadata信息，根据设备类型设置默认值
	if metadata.Brand == "Generic" {
		switch d.DataType {
		case "fanuc":
			metadata.Brand = "Fanuc"
			metadata.Model = "Fanuc 30i"
			if metadata.DeviceName == d.Template.Name {
				metadata.DeviceName = "FANUC CNC Machine"
			}
		case "siemens":
			metadata.Brand = "Siemens"
			metadata.Model = "Siemens 840D"
			if metadata.DeviceName == d.Template.Name {
				metadata.DeviceName = "Siemens CNC Machine"
			}
		case "temperature":
			metadata.Brand = "Generic"
			metadata.Model = "Temperature Sensor"
			if metadata.DeviceName == d.Template.Name {
				metadata.DeviceName = "Temperature Sensor"
			}
			if metadata.Port == 8193 {
				metadata.Port = 502
			}
		}
	}

	return metadata
}
