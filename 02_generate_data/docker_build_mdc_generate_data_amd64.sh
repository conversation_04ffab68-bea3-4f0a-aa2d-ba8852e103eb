#!/bin/bash

# 确保脚本在出错时停止执行
set -e

# 镜像名称和标签配置
IMAGE_NAME="mdc_generate_data"
TAG="1.0.0"
PLATFORM="linux/amd64"

echo "构建 ${PLATFORM} 架构的 Docker 镜像..."
echo "镜像名称: ${IMAGE_NAME}"
echo "镜像标签: ${TAG}"
echo "--------------------------------"

# 启用 Docker BuildKit 以获得更好的构建性能和功能
export DOCKER_BUILDKIT=1

# 使用 DOCKER_DEFAULT_PLATFORM 环境变量指定目标平台
export DOCKER_DEFAULT_PLATFORM=${PLATFORM}

# 切换到项目根目录进行构建（因为 Dockerfile 中需要访问 shared 目录）
cd ..

# 构建镜像，指定 02_generate_data 目录的 Dockerfile
# -f: 指定Dockerfile路径
# -t: 指定镜像名称和标签
# .: 构建上下文为当前目录（项目根目录）
docker build -f 02_generate_data/Dockerfile -t ${IMAGE_NAME}:${TAG} .

echo "${PLATFORM} Docker 镜像构建完成: ${IMAGE_NAME}:${TAG}"
echo "可以使用以下命令运行容器:"
echo "  docker run -v \$(pwd)/02_generate_data/data:/data -v \$(pwd)/02_generate_data/logs:/app/logs -v \$(pwd)/02_generate_data/config.yml:/app/config.yml ${IMAGE_NAME}:${TAG}"

# 添加执行权限（回到原目录）
cd 02_generate_data
chmod +x docker_build_mdc_generate_data_amd64.sh docker_push_mdc_generate_data.sh
cd ..

# 更新 ./02_generate_data/docker_push_mdc_generate_data.sh 中的镜像版本号
# 确保 push.sh 文件存在并且路径正确
if [ -f "02_generate_data/docker_push_mdc_generate_data.sh" ]; then
    # 使用sed命令替换版本号
    # 在macOS上使用 -i '' 参数，在Linux上使用 -i 参数
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' "s/IMAGE_VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/IMAGE_VERSION=\"${TAG}\"/" 02_generate_data/docker_push_mdc_generate_data.sh
    else
        sed -i "s/IMAGE_VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/IMAGE_VERSION=\"${TAG}\"/" 02_generate_data/docker_push_mdc_generate_data.sh
    fi
else
    echo "错误: 02_generate_data/docker_push_mdc_generate_data.sh 文件不存在"
fi

# 更新 ./02_generate_data/docker-compose.yml 中的镜像版本号（如果存在）
# 格式示例:
# services:
#   generate_data:
#     image: mdc_generate_data:1.0.0
if [ -f "02_generate_data/docker-compose.yml" ]; then
    # 使用sed命令替换docker-compose.yml中的镜像版本
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' "s/image: ${IMAGE_NAME}:[0-9]*\.[0-9]*\.[0-9]*/image: ${IMAGE_NAME}:${TAG}/" 02_generate_data/docker-compose.yml
    else
        sed -i "s/image: ${IMAGE_NAME}:[0-9]*\.[0-9]*\.[0-9]*/image: ${IMAGE_NAME}:${TAG}/" 02_generate_data/docker-compose.yml
    fi
else
    echo "提示: 02_generate_data/docker-compose.yml 文件不存在，跳过版本更新"
fi

# 获取构建完成的镜像ID
IMAGE_ID=$(docker images -q ${IMAGE_NAME}:${TAG})

echo "镜像ID: ${IMAGE_ID}"
echo "构建完成！"
