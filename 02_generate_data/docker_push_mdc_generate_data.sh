#!/bin/bash

# Docker镜像推送脚本
# 用于将本地构建的镜像推送到阿里云容器镜像服务

# 镜像配置
IMAGE_NAME="mdc_generate_data"
IMAGE_VERSION="1.0.0"

# 通过 docker images 获取本地镜像ID
IMAGE_ID=$(docker images -q ${IMAGE_NAME}:${IMAGE_VERSION})

# 检查镜像是否存在
if [ -z "$IMAGE_ID" ]; then
    echo "错误: 找不到镜像 ${IMAGE_NAME}:${IMAGE_VERSION}"
    echo "请先运行构建脚本: ./docker_build_mdc_generate_data_amd64.sh"
    exit 1
fi

# 阿里云容器镜像服务配置
URL="crpi-miowmruvplz7jr0c.cn-shenzhen.personal.cr.aliyuncs.com"
IMAGE_URL="${URL}/c2studio/${IMAGE_NAME}"

echo "开始推送镜像到阿里云容器镜像服务..."
echo "本地镜像: ${IMAGE_NAME}:${IMAGE_VERSION}"
echo "镜像ID: ${IMAGE_ID}"
echo "目标地址: ${IMAGE_URL}:${IMAGE_VERSION}"
echo "--------------------------------"

# 为镜像打标签，指向阿里云镜像仓库
echo "正在为镜像打标签..."
docker tag ${IMAGE_ID} ${IMAGE_URL}:${IMAGE_VERSION}

# 推送镜像到阿里云
echo "正在推送镜像..."
docker push ${IMAGE_URL}:${IMAGE_VERSION}

# 推送完成提示
echo "================================"
echo "镜像推送完成！"
echo "镜像版本号: ${IMAGE_VERSION}"
echo "镜像ID: ${IMAGE_ID}"
echo ""
echo "使用以下命令拉取镜像:"
echo "  docker pull ${IMAGE_URL}:${IMAGE_VERSION}"
echo ""
echo "使用以下命令重命名镜像:"
echo "  docker tag ${IMAGE_URL}:${IMAGE_VERSION} ${IMAGE_NAME}:${IMAGE_VERSION}"
echo "  # 或者"
echo "  docker tag ${IMAGE_ID} ${IMAGE_NAME}:${IMAGE_VERSION}"
echo ""
echo "推送完成时间: $(date)"
