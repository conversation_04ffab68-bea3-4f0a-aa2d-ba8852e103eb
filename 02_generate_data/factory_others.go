package main

import (
	"fmt"
	"math/rand"
	"time"
)

/**
 * 其他设备工厂实现
 *
 * 包含Brother、Modbus、Sensor等设备类型的工厂实现
 * 每种设备类型都有自己的数据生成逻辑和状态管理
 */

// BrotherFactory Brother设备工厂
type BrotherFactory struct{}

// NewBrotherFactory 创建Brother工厂
func NewBrotherFactory() *BrotherFactory {
	return &BrotherFactory{}
}

// CreateGenerator 创建Brother数据生成器
func (f *BrotherFactory) CreateGenerator(deviceID string, config interface{}) (DataGenerator, error) {
	brotherConfig, ok := config.(*BrotherAdapterConfig)
	if !ok {
		return nil, fmt.Errorf("invalid config type for brother adapter, expected *BrotherAdapterConfig")
	}

	return NewBrotherDataGenerator(deviceID, brotherConfig), nil
}

// ValidateConfig 验证Brother配置
func (f *BrotherFactory) ValidateConfig(config interface{}) error {
	brotherConfig, ok := config.(*BrotherAdapterConfig)
	if !ok {
		return fmt.E<PERSON><PERSON>("invalid config type for brother adapter")
	}

	// 验证状态配置
	statusConfig := brotherConfig.StatusConfig
	totalWeight := statusConfig.Operating.Weight + statusConfig.Idle.Weight + statusConfig.Error.Weight
	if totalWeight != 100 {
		return fmt.Errorf("status weights must sum to 100, got %d", totalWeight)
	}

	return nil
}

// GetAdapterType 获取适配器类型
func (f *BrotherFactory) GetAdapterType() string {
	return "brother"
}

// BrotherDataGenerator Brother数据生成器
type BrotherDataGenerator struct {
	deviceID        string
	config          *BrotherAdapterConfig
	currentStatus   string
	statusStartTime time.Time
	statusDuration  time.Duration
	currentProgram  string
	totalParts      int
}

// NewBrotherDataGenerator 创建Brother数据生成器
func NewBrotherDataGenerator(deviceID string, config *BrotherAdapterConfig) *BrotherDataGenerator {
	generator := &BrotherDataGenerator{
		deviceID: deviceID,
		config:   config,
	}

	generator.initializeStatus()
	generator.initializeProgram()

	return generator
}

// GenerateData 生成Brother设备数据
func (g *BrotherDataGenerator) GenerateData() map[string]interface{} {
	now := time.Now()
	g.UpdateState()

	// 生成数据ID
	dataID, err := GenerateSnowflakeID()
	if err != nil {
		dataID = fmt.Sprintf("%d", time.Now().UnixNano())
	}

	return map[string]interface{}{
		"data_id":         dataID,
		"connected":       true,
		"status":          g.mapBrotherStatusToStandard(g.currentStatus),
		"current_program": g.currentProgram,
		"total_parts":     g.totalParts,
		"timestamp":       now.Unix(),
		"status_runtime":  int(now.Sub(g.statusStartTime).Seconds()),
	}
}

// UpdateState 更新Brother设备状态
func (g *BrotherDataGenerator) UpdateState() {
	now := time.Now()

	if now.Sub(g.statusStartTime) >= g.statusDuration {
		g.switchToNextStatus()
	}
}

// GetDeviceID 获取设备ID
func (g *BrotherDataGenerator) GetDeviceID() string {
	return g.deviceID
}

// 私有方法
func (g *BrotherDataGenerator) initializeStatus() {
	g.currentStatus = g.selectRandomStatus()
	g.statusStartTime = time.Now()
	g.statusDuration = g.getStatusDuration(g.currentStatus)
}

func (g *BrotherDataGenerator) initializeProgram() {
	if len(g.config.Programs) > 0 {
		program := g.config.Programs[rand.Intn(len(g.config.Programs))]
		g.currentProgram = program.Name
	}
}

func (g *BrotherDataGenerator) selectRandomStatus() string {
	random := rand.Intn(100) + 1

	statusConfig := g.config.StatusConfig
	if random <= statusConfig.Operating.Weight {
		return "operating"
	} else if random <= statusConfig.Operating.Weight+statusConfig.Idle.Weight {
		return "idle"
	} else {
		return "error"
	}
}

func (g *BrotherDataGenerator) getStatusDuration(status string) time.Duration {
	var minDuration, maxDuration int

	switch status {
	case "operating":
		minDuration = g.config.StatusConfig.Operating.DurationMin
		maxDuration = g.config.StatusConfig.Operating.DurationMax
	case "idle":
		minDuration = g.config.StatusConfig.Idle.DurationMin
		maxDuration = g.config.StatusConfig.Idle.DurationMax
	case "error":
		minDuration = g.config.StatusConfig.Error.DurationMin
		maxDuration = g.config.StatusConfig.Error.DurationMax
	default:
		minDuration = 60
		maxDuration = 120
	}

	duration := rand.Intn(maxDuration-minDuration+1) + minDuration
	return time.Duration(duration) * time.Second
}

func (g *BrotherDataGenerator) switchToNextStatus() {
	g.currentStatus = g.selectRandomStatus()
	g.statusStartTime = time.Now()
	g.statusDuration = g.getStatusDuration(g.currentStatus)
}

func (g *BrotherDataGenerator) mapBrotherStatusToStandard(brotherStatus string) string {
	switch brotherStatus {
	case "operating":
		return "production"
	case "idle":
		return "idle"
	case "error":
		return "fault"
	default:
		return "unknown"
	}
}

// ModbusFactory Modbus设备工厂
type ModbusFactory struct{}

// NewModbusFactory 创建Modbus工厂
func NewModbusFactory() *ModbusFactory {
	return &ModbusFactory{}
}

// CreateGenerator 创建Modbus数据生成器
func (f *ModbusFactory) CreateGenerator(deviceID string, config interface{}) (DataGenerator, error) {
	modbusConfig, ok := config.(*ModbusAdapterConfig)
	if !ok {
		return nil, fmt.Errorf("invalid config type for modbus adapter, expected *ModbusAdapterConfig")
	}

	return NewModbusDataGenerator(deviceID, modbusConfig), nil
}

// ValidateConfig 验证Modbus配置
func (f *ModbusFactory) ValidateConfig(config interface{}) error {
	_, ok := config.(*ModbusAdapterConfig)
	if !ok {
		return fmt.Errorf("invalid config type for modbus adapter")
	}

	return nil
}

// GetAdapterType 获取适配器类型
func (f *ModbusFactory) GetAdapterType() string {
	return "modbus"
}

// ModbusDataGenerator Modbus数据生成器
type ModbusDataGenerator struct {
	deviceID string
	config   *ModbusAdapterConfig
}

// NewModbusDataGenerator 创建Modbus数据生成器
func NewModbusDataGenerator(deviceID string, config *ModbusAdapterConfig) *ModbusDataGenerator {
	return &ModbusDataGenerator{
		deviceID: deviceID,
		config:   config,
	}
}

// GenerateData 生成Modbus设备数据
func (g *ModbusDataGenerator) GenerateData() map[string]interface{} {
	// 生成数据ID
	dataID, err := GenerateSnowflakeID()
	if err != nil {
		dataID = fmt.Sprintf("%d", time.Now().UnixNano())
	}

	data := map[string]interface{}{
		"data_id":   dataID,
		"connected": true,
		"status":    "idle",
		"timestamp": time.Now().Unix(),
	}

	// 生成保持寄存器数据
	for _, reg := range g.config.Registers.HoldingRegisters {
		data[reg.Name] = g.generateRegisterValue(reg)
	}

	// 生成输入寄存器数据
	for _, reg := range g.config.Registers.InputRegisters {
		data[reg.Name] = g.generateRegisterValue(reg)
	}

	// 生成线圈数据
	for _, coil := range g.config.Registers.Coils {
		data[coil.Name] = rand.Float64() > 0.5
	}

	return data
}

// UpdateState 更新Modbus设备状态
func (g *ModbusDataGenerator) UpdateState() {
	// Modbus设备通常不需要复杂的状态管理
}

// GetDeviceID 获取设备ID
func (g *ModbusDataGenerator) GetDeviceID() string {
	return g.deviceID
}

func (g *ModbusDataGenerator) generateRegisterValue(reg ModbusRegister) interface{} {
	switch reg.Type {
	case "float":
		min := getFloat64(reg.Min, 0.0)
		max := getFloat64(reg.Max, 100.0)
		return min + rand.Float64()*(max-min)
	case "int":
		min := getInt64(reg.Min, 0)
		max := getInt64(reg.Max, 100)
		return min + rand.Int63n(max-min+1)
	default:
		return 0
	}
}

// SensorFactory 传感器设备工厂
type SensorFactory struct{}

// NewSensorFactory 创建传感器工厂
func NewSensorFactory() *SensorFactory {
	return &SensorFactory{}
}

// CreateGenerator 创建传感器数据生成器
func (f *SensorFactory) CreateGenerator(deviceID string, config interface{}) (DataGenerator, error) {
	sensorConfig, ok := config.(*SensorAdapterConfig)
	if !ok {
		return nil, fmt.Errorf("invalid config type for sensor adapter, expected *SensorAdapterConfig")
	}

	return NewSensorDataGenerator(deviceID, sensorConfig), nil
}

// ValidateConfig 验证传感器配置
func (f *SensorFactory) ValidateConfig(config interface{}) error {
	_, ok := config.(*SensorAdapterConfig)
	if !ok {
		return fmt.Errorf("invalid config type for sensor adapter")
	}

	return nil
}

// GetAdapterType 获取适配器类型
func (f *SensorFactory) GetAdapterType() string {
	return "sensor"
}

// SensorDataGenerator 传感器数据生成器
type SensorDataGenerator struct {
	deviceID    string
	config      *SensorAdapterConfig
	fieldValues map[string]interface{}
}

// NewSensorDataGenerator 创建传感器数据生成器
func NewSensorDataGenerator(deviceID string, config *SensorAdapterConfig) *SensorDataGenerator {
	generator := &SensorDataGenerator{
		deviceID:    deviceID,
		config:      config,
		fieldValues: make(map[string]interface{}),
	}

	// 初始化字段值
	for fieldName, fieldConfig := range config.DataFields {
		generator.fieldValues[fieldName] = generator.generateInitialValue(fieldConfig)
	}

	return generator
}

// GenerateData 生成传感器设备数据
func (g *SensorDataGenerator) GenerateData() map[string]interface{} {
	// 生成数据ID
	dataID, err := GenerateSnowflakeID()
	if err != nil {
		dataID = fmt.Sprintf("%d", time.Now().UnixNano())
	}

	data := map[string]interface{}{
		"data_id":   dataID,
		"connected": true,
		"status":    "idle",
		"timestamp": time.Now().Unix(),
	}

	// 更新并生成字段数据
	for fieldName, fieldConfig := range g.config.DataFields {
		g.updateFieldValue(fieldName, fieldConfig)
		data[fieldName] = g.fieldValues[fieldName]
	}

	return data
}

// UpdateState 更新传感器设备状态
func (g *SensorDataGenerator) UpdateState() {
	// 传感器设备的状态更新在GenerateData中处理
}

// GetDeviceID 获取设备ID
func (g *SensorDataGenerator) GetDeviceID() string {
	return g.deviceID
}

func (g *SensorDataGenerator) generateInitialValue(config DataFieldConfig) interface{} {
	switch config.Type {
	case "float":
		min := getFloat64(config.Min, 0.0)
		max := getFloat64(config.Max, 100.0)
		return min + rand.Float64()*(max-min)
	case "int":
		min := getInt64(config.Min, 0)
		max := getInt64(config.Max, 100)
		return min + rand.Int63n(max-min+1)
	case "bool":
		return rand.Float64() > 0.5
	default:
		return 0
	}
}

func (g *SensorDataGenerator) updateFieldValue(fieldName string, config DataFieldConfig) {
	currentValue := g.fieldValues[fieldName]

	switch config.Type {
	case "float":
		min := getFloat64(config.Min, 0.0)
		max := getFloat64(config.Max, 100.0)
		step := getFloat64(config.Step, 0.1)

		current := getFloat64(currentValue, min)

		switch config.Trend {
		case "random":
			g.fieldValues[fieldName] = min + rand.Float64()*(max-min)
		case "increase":
			newValue := current + step
			if newValue > max {
				newValue = min
			}
			g.fieldValues[fieldName] = newValue
		case "decrease":
			newValue := current - step
			if newValue < min {
				newValue = max
			}
			g.fieldValues[fieldName] = newValue
		case "wave":
			// 简单的正弦波模拟
			amplitude := (max - min) / 2
			center := min + amplitude
			// 使用时间作为相位，创建波形效果
			g.fieldValues[fieldName] = center + amplitude*0.8*rand.Float64()
		default:
			g.fieldValues[fieldName] = min + rand.Float64()*(max-min)
		}

	case "int":
		min := getInt64(config.Min, 0)
		max := getInt64(config.Max, 100)
		step := getInt64(config.Step, 1)

		current := getInt64(currentValue, min)

		switch config.Trend {
		case "random":
			g.fieldValues[fieldName] = min + rand.Int63n(max-min+1)
		case "increase":
			newValue := current + step
			if newValue > max {
				newValue = min
			}
			g.fieldValues[fieldName] = newValue
		case "decrease":
			newValue := current - step
			if newValue < min {
				newValue = max
			}
			g.fieldValues[fieldName] = newValue
		default:
			g.fieldValues[fieldName] = min + rand.Int63n(max-min+1)
		}

	case "bool":
		g.fieldValues[fieldName] = rand.Float64() > 0.5
	}
}
