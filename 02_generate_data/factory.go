package main

import (
	"fmt"
	"regexp"
	"gopkg.in/yaml.v3"
)

/**
 * 工厂设计模式实现
 * 
 * 根据设备模板的adapter字段，使用不同的工厂来创建数据生成器
 * 支持的适配器类型：fanuc, brother, modbus, sensor
 * 
 * 工厂模式的优势：
 * 1. 解耦：设备创建逻辑与具体实现分离
 * 2. 扩展性：新增设备类型只需添加新的工厂实现
 * 3. 一致性：所有设备都通过统一的接口创建
 * 4. 配置驱动：通过配置文件控制使用哪种工厂
 */

// DataGeneratorFactory 数据生成器工厂接口
type DataGeneratorFactory interface {
	// CreateGenerator 创建数据生成器
	CreateGenerator(deviceID string, config interface{}) (DataGenerator, error)
	
	// ValidateConfig 验证配置是否有效
	ValidateConfig(config interface{}) error
	
	// GetAdapterType 获取适配器类型
	GetAdapterType() string
}

// DataGenerator 数据生成器接口
type DataGenerator interface {
	// GenerateData 生成设备数据
	GenerateData() map[string]interface{}
	
	// UpdateState 更新设备状态
	UpdateState()
	
	// GetDeviceID 获取设备ID
	GetDeviceID() string
}

// FactoryManager 工厂管理器
type FactoryManager struct {
	factories map[string]DataGeneratorFactory
}

// NewFactoryManager 创建工厂管理器
func NewFactoryManager() *FactoryManager {
	fm := &FactoryManager{
		factories: make(map[string]DataGeneratorFactory),
	}
	
	// 注册所有工厂
	fm.RegisterFactory("fanuc", NewFanucFactory())
	fm.RegisterFactory("brother", NewBrotherFactory())
	fm.RegisterFactory("modbus", NewModbusFactory())
	fm.RegisterFactory("sensor", NewSensorFactory())
	
	return fm
}

// RegisterFactory 注册工厂
func (fm *FactoryManager) RegisterFactory(adapterType string, factory DataGeneratorFactory) {
	fm.factories[adapterType] = factory
}

// CreateGenerator 根据适配器类型创建数据生成器
func (fm *FactoryManager) CreateGenerator(adapterType string, deviceID string, config interface{}) (DataGenerator, error) {
	factory, exists := fm.factories[adapterType]
	if !exists {
		return nil, fmt.Errorf("unsupported adapter type: %s", adapterType)
	}
	
	return factory.CreateGenerator(deviceID, config)
}

// GetSupportedAdapters 获取支持的适配器类型列表
func (fm *FactoryManager) GetSupportedAdapters() []string {
	adapters := make([]string, 0, len(fm.factories))
	for adapterType := range fm.factories {
		adapters = append(adapters, adapterType)
	}
	return adapters
}

// MatchDeviceTemplate 根据设备类型匹配模板
func MatchDeviceTemplate(deviceType string, templates []DeviceTemplate) (*DeviceTemplate, error) {
	for _, template := range templates {
		// 使用正则表达式匹配设备类型
		matched, err := regexp.MatchString(template.DataTypeRegex, deviceType)
		if err != nil {
			return nil, fmt.Errorf("invalid regex pattern '%s': %w", template.DataTypeRegex, err)
		}
		
		if matched {
			return &template, nil
		}
	}
	
	return nil, fmt.Errorf("no template found for device type: %s", deviceType)
}

// ParseAdapterConfig 解析适配器配置
func ParseAdapterConfig(adapterType string, configData interface{}) (interface{}, error) {
	// 将interface{}转换为YAML字节数组，然后解析为具体的配置结构体
	yamlData, err := yaml.Marshal(configData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal config data: %w", err)
	}
	
	switch adapterType {
	case "fanuc":
		var config FanucAdapterConfig
		if err := yaml.Unmarshal(yamlData, &config); err != nil {
			return nil, fmt.Errorf("failed to parse fanuc config: %w", err)
		}
		return &config, nil
		
	case "brother":
		var config BrotherAdapterConfig
		if err := yaml.Unmarshal(yamlData, &config); err != nil {
			return nil, fmt.Errorf("failed to parse brother config: %w", err)
		}
		return &config, nil
		
	case "modbus":
		var config ModbusAdapterConfig
		if err := yaml.Unmarshal(yamlData, &config); err != nil {
			return nil, fmt.Errorf("failed to parse modbus config: %w", err)
		}
		return &config, nil
		
	case "sensor":
		var config SensorAdapterConfig
		if err := yaml.Unmarshal(yamlData, &config); err != nil {
			return nil, fmt.Errorf("failed to parse sensor config: %w", err)
		}
		return &config, nil
		
	default:
		return nil, fmt.Errorf("unsupported adapter type: %s", adapterType)
	}
}

// 全局工厂管理器实例
var GlobalFactoryManager = NewFactoryManager()
