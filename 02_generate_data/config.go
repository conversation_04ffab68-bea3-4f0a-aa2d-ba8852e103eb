/**
 * 多设备数据生成器配置模块
 *
 * 功能概述：
 * 本模块定义了多设备数据生成器的完整配置结构体和配置管理功能，
 * 支持多设备并发模拟、设备类型配置、故障模拟等复杂场景的配置管理
 *
 * 主要功能：
 * - 配置结构定义：定义完整的配置结构体，覆盖所有功能模块
 * - 配置文件加载：支持YAML格式配置文件的加载和解析
 * - 配置验证：验证配置参数的合理性和完整性
 * - 设备模板：支持多种设备类型的模板化配置
 * - 动态配置：支持从API动态获取设备配置
 * - 故障模拟：支持网络故障、设备离线等故障场景配置
 *
 * 配置分类：
 * - 目标配置：数据推送目标服务的连接配置
 * - 设备配置：设备数量、类型、行为等配置
 * - 全局配置：运行模式、并发限制、统计间隔等
 * - 模板配置：不同设备类型的数据生成模板
 * - 日志配置：日志级别、格式、轮转等配置
 * - 故障配置：故障模拟的概率和类型配置
 *
 * 设计特性：
 * - 模块化：按功能模块组织配置结构
 * - 可扩展：易于添加新的配置项和设备类型
 * - 类型安全：使用强类型确保配置的正确性
 * - 向后兼容：支持配置的向后兼容性
 * - 环境适配：支持不同测试环境的配置差异
 *
 * 业务价值：
 * - 灵活测试：支持不同测试场景的灵活配置
 * - 运维友好：清晰的配置结构，便于运维管理
 * - 错误预防：配置验证机制，预防配置错误
 * - 性能调优：丰富的性能配置选项
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v2"
)

/**
 * 主配置结构体
 *
 * 功能：作为整个数据生成器的配置根节点，包含所有子模块的配置信息
 *
 * 设计原则：
 * - 模块化：按功能模块组织配置，便于管理和维护
 * - 层次化：清晰的配置层次结构，避免配置冲突
 * - 完整性：覆盖数据生成器运行所需的所有配置项
 * - 可读性：使用有意义的字段名和YAML标签
 *
 * 配置模块说明：
 * - Target：目标服务配置，包括URL、超时、重试等
 * - Devices：设备配置源，支持API和模板两种方式
 * - Global：全局运行配置，包括设备数量、运行模式等
 * - DeviceTemplates：设备模板配置，定义不同类型设备的行为
 * - Locations：设备位置配置，用于模拟不同车间和产线
 * - Logging：日志配置，包括级别、格式、轮转等
 * - FaultSimulation：故障模拟配置，用于测试系统容错能力
 *
 * @struct Config
 */
type Config struct {
	/** 目标服务配置：数据推送的目标服务连接信息 */
	Target TargetConfig `yaml:"target"`

	/** 设备配置源：设备配置的获取方式和参数 */
	Devices DevicesConfig `yaml:"devices"`

	/** 全局配置：运行模式、设备数量、并发限制等 */
	Global GlobalConfig `yaml:"global"`

	/** 设备模板配置：不同类型设备的数据生成模板 */
	DeviceTemplates []DeviceTemplate `yaml:"device_templates"`

	/** 位置配置：设备所在的车间、产线等位置信息 */
	Locations []string `yaml:"locations"`

	/** 日志配置：日志级别、格式、轮转策略等 */
	Logging LoggingConfig `yaml:"logging"`

	/** 故障模拟配置：网络故障、设备离线等故障场景 */
	FaultSimulation FaultSimulationConfig `yaml:"fault_simulation"`
}

// DevicesConfig 设备配置源
type DevicesConfig struct {
	ConfigSource    string `yaml:"config_source"`    // 配置源: "api" | "template"
	ConfigAPI       string `yaml:"config_api"`       // API获取设备配置URL
	RefreshInterval int    `yaml:"refresh_interval"` // API配置刷新间隔(秒)
}

// TargetConfig 目标服务配置
type TargetConfig struct {
	URL        string        `yaml:"url"`         // 目标服务URL
	Timeout    time.Duration `yaml:"timeout"`     // 请求超时时间
	RetryCount int           `yaml:"retry_count"` // 重试次数
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	TotalDevices    int           `yaml:"total_devices"`    // 总设备数量
	RunMode         string        `yaml:"run_mode"`         // 运行模式
	RunDuration     time.Duration `yaml:"run_duration"`     // 运行持续时间
	RunCycles       int           `yaml:"run_cycles"`       // 运行循环次数
	ConcurrentLimit int           `yaml:"concurrent_limit"` // 并发限制
	StatsInterval   time.Duration `yaml:"stats_interval"`   // 统计间隔
}

// DeviceTemplate 设备模板配置
type DeviceTemplate struct {
	Name          string        `yaml:"name"`            // 模板名称
	Count         int           `yaml:"count"`           // 设备数量
	Adapter       string        `yaml:"adapter"`         // 工厂适配器类型: fanuc, brother, modbus, sensor
	DataTypeRegex string        `yaml:"data_type_regex"` // 正则表达式匹配设备类型
	PushInterval  time.Duration `yaml:"push_interval"`   // 推送间隔
	PushJitter    time.Duration `yaml:"push_jitter"`     // 推送抖动
	Config        interface{}   `yaml:"config"`          // 统一的配置字段，根据adapter类型解析
}

// AdapterConfig 适配器配置接口
type AdapterConfig interface{}

// FanucAdapterConfig FANUC适配器配置
type FanucAdapterConfig struct {
	StatusConfig StatusConfig               `yaml:"status_config"` // 状态配置
	Programs     []ProgramConfig            `yaml:"programs"`      // 程序配置
	DataFields   map[string]DataFieldConfig `yaml:"data_fields"`   // 额外数据字段
}

// BrotherAdapterConfig Brother适配器配置
type BrotherAdapterConfig struct {
	StatusConfig StatusConfigBrother `yaml:"status_config"` // Brother状态配置
	Programs     []ProgramConfig     `yaml:"programs"`      // 程序配置
}

// ModbusAdapterConfig Modbus适配器配置
type ModbusAdapterConfig struct {
	Registers ModbusRegisters `yaml:"registers"` // Modbus寄存器配置
}

// SensorAdapterConfig 传感器适配器配置
type SensorAdapterConfig struct {
	DataFields map[string]DataFieldConfig `yaml:"data_fields"` // 数据字段配置
}

// StatusConfigBrother Brother设备状态配置
type StatusConfigBrother struct {
	Operating StatusDuration `yaml:"operating"` // 运行状态
	Idle      StatusDuration `yaml:"idle"`      // 空闲状态
	Error     StatusDuration `yaml:"error"`     // 错误状态
}

// ModbusRegisters Modbus寄存器配置
type ModbusRegisters struct {
	HoldingRegisters []ModbusRegister `yaml:"holding_registers"` // 保持寄存器
	InputRegisters   []ModbusRegister `yaml:"input_registers"`   // 输入寄存器
	Coils            []ModbusCoil     `yaml:"coils"`             // 线圈
}

// ModbusRegister Modbus寄存器配置
type ModbusRegister struct {
	Address int         `yaml:"address"` // 寄存器地址
	Name    string      `yaml:"name"`    // 寄存器名称
	Type    string      `yaml:"type"`    // 数据类型
	Min     interface{} `yaml:"min"`     // 最小值
	Max     interface{} `yaml:"max"`     // 最大值
}

// ModbusCoil Modbus线圈配置
type ModbusCoil struct {
	Address int    `yaml:"address"` // 线圈地址
	Name    string `yaml:"name"`    // 线圈名称
	Type    string `yaml:"type"`    // 数据类型
}

// FanucConfig FANUC设备配置 (保持向后兼容)
type FanucConfig struct {
	StatusConfig StatusConfig    `yaml:"status_config"` // 状态配置
	Programs     []ProgramConfig `yaml:"programs"`      // 程序配置
}

// StatusConfig 设备状态配置
type StatusConfig struct {
	Running StatusDuration `yaml:"running"` // 运行状态
	Standby StatusDuration `yaml:"standby"` // 待机状态
	Alarm   StatusDuration `yaml:"alarm"`   // 报警状态
}

// StatusDuration 状态持续时间配置
type StatusDuration struct {
	DurationMin int `yaml:"duration_min"` // 最小持续时间(秒)
	DurationMax int `yaml:"duration_max"` // 最大持续时间(秒)
	Weight      int `yaml:"weight"`       // 权重(百分比)
}

// ProgramConfig 程序配置
type ProgramConfig struct {
	Name        string   `yaml:"name"`         // 主程序名称
	SubPrograms []string `yaml:"sub_programs"` // 子程序列表
}

// ProductionConfig 产量配置
type ProductionConfig struct {
	CycleTimeMin  int `yaml:"cycle_time_min"`  // 最小生产周期(秒)
	CycleTimeMax  int `yaml:"cycle_time_max"`  // 最大生产周期(秒)
	PartsPerCycle int `yaml:"parts_per_cycle"` // 每个周期产量
}

// APIDeviceConfig API返回的设备配置
type APIDeviceConfig struct {
	ID              string `json:"id"`
	Name            string `json:"name"`
	DataType        string `json:"data_type"` // 设备数据类型，如"fanuc_30i"、"siemens_840d"等
	IP              string `json:"ip"`
	Port            int    `json:"port"`
	Enabled         bool   `json:"enabled"`
	AutoStart       bool   `json:"auto_start"`
	CollectInterval int    `json:"collect_interval"`
	Timeout         int    `json:"timeout"`
	RetryCount      int    `json:"retry_count"`
	RetryDelay      int    `json:"retry_delay"`
	Location        string `json:"location"`
	Brand           string `json:"brand"`
	Model           string `json:"model"`
}

// DataFieldConfig 数据字段配置
type DataFieldConfig struct {
	Type  string      `yaml:"type"`  // 数据类型: float, int, bool, string
	Min   interface{} `yaml:"min"`   // 最小值
	Max   interface{} `yaml:"max"`   // 最大值
	Step  interface{} `yaml:"step"`  // 变化步长
	Trend string      `yaml:"trend"` // 变化趋势
	Unit  string      `yaml:"unit"`  // 单位
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string            `yaml:"level"`       // 日志级别
	Format     string            `yaml:"format"`      // 日志格式
	Output     string            `yaml:"output"`      // 输出目标
	DeviceLogs bool              `yaml:"device_logs"` // 是否输出设备日志
	Rotation   LogRotationConfig `yaml:"rotation"`    // 日志轮转配置
}

// LogRotationConfig 日志轮转配置
type LogRotationConfig struct {
	Enabled    bool `yaml:"enabled"`     // 是否启用日志轮转
	MaxSize    int  `yaml:"max_size"`    // 单个文件最大大小(MB)
	MaxAge     int  `yaml:"max_age"`     // 文件保留天数
	MaxBackups int  `yaml:"max_backups"` // 保留的备份文件数量
	Compress   bool `yaml:"compress"`    // 是否压缩旧文件
}

// FaultSimulationConfig 故障模拟配置
type FaultSimulationConfig struct {
	Enabled           bool    `yaml:"enabled"`             // 是否启用故障模拟
	NetworkErrorRate  float64 `yaml:"network_error_rate"`  // 网络错误率
	TimeoutRate       float64 `yaml:"timeout_rate"`        // 超时错误率
	DeviceOfflineRate float64 `yaml:"device_offline_rate"` // 设备离线率
}

// LoadConfig 加载配置文件
func LoadConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return &config, nil
}

// validateConfig 验证配置的合理性
func validateConfig(config *Config) error {
	if config.Target.URL == "" {
		return fmt.Errorf("target URL is required")
	}

	if config.Global.RunMode != "duration" && config.Global.RunMode != "cycles" && config.Global.RunMode != "forever" {
		return fmt.Errorf("run_mode must be one of: duration, cycles, forever")
	}

	// 验证设备配置源
	if config.Devices.ConfigSource != "api" && config.Devices.ConfigSource != "template" {
		return fmt.Errorf("devices.config_source must be one of: api, template")
	}

	if config.Devices.ConfigSource == "api" {
		if config.Devices.ConfigAPI == "" {
			return fmt.Errorf("devices.config_api is required when config_source is api")
		}
	} else if config.Devices.ConfigSource == "template" {
		if config.Global.TotalDevices <= 0 {
			return fmt.Errorf("total_devices must be greater than 0 when config_source is template")
		}

		// 验证设备模板
		totalTemplateDevices := 0
		for _, template := range config.DeviceTemplates {
			if template.Count <= 0 {
				return fmt.Errorf("device template %s count must be greater than 0", template.Name)
			}
			totalTemplateDevices += template.Count
		}

		if totalTemplateDevices != config.Global.TotalDevices {
			return fmt.Errorf("sum of device template counts (%d) must equal total_devices (%d)",
				totalTemplateDevices, config.Global.TotalDevices)
		}
	}

	return nil
}
