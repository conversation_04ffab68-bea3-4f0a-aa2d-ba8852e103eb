   Device
      |
      V
   Data Acquisition
      |
      V
   Redis
      |
      V
   Data Cleaning/Transformation
      |
      V
   Local SQLite
      |
      V
   NATS JetStream
      |
      V
   InfluxDB -------> MongoDB


数据流从设备到 NATS JetStream，再到 InfluxDB 和 MongoDB，涉及到多个数据存储和处理层。以下是对该架构中每个部分的详细分析和建议，以实现高效、可扩展的数据处理和存储解决方案。

1. Device (设备)
描述: 设备负责采集原始数据，可能是传感器、IoT设备等。
建议: 使用稳定的通信协议（如MQTT、HTTP或WebSocket）来确保数据安全、可靠地发送到数据采集层。

2. Data Acquisition (数据采集)
描述: 在这一层，负责从各类设备接收数据并传递给下游存储。
建议:
使用数据流处理框架（如 Apache Kafka、Apache NiFi 或 Mosquitto）来接收和处理来自不同设备的数据。
可以编写边缘计算逻辑，进行初步的数据过滤和聚合，减少后续存储的负担。

3.  Redis
描述:  Redis 当作一个临时的时间序列数据库，时间序列存储采集到的数据。
建议:
将数据以适当的时间戳格式存储，以便进行时序分析。
使用适当的保留策略（Retention Policy）来管理存储周期，以避免数据无效膨胀。
优化写入操作，使用批量写入来提高性能。

4. Data Cleaning/Transformation (数据清洗/转化)
描述: 此层负责对 Redis 中的数据进行清洗和转化为统一的数据格式。
建议:
实时读取 Redis 数据, 使用 goja 构建函数来实现脚本语言数据转化。
处理后清洗的数据或聚合结果再存储到 Local SQLite。

5. Local SQLite
描述: SQLite 被用于持久化存储清洗后的数据，确保本地快速访问。
建议:
在 SQLite 中可以继续使用聚合和索引机制以优化读取性能。
针对高并发的读写，确保对数据库的优化配置（如使用 WAL 模式）。
- 每天一个独立的数据库文件
- 数据库文件保留机制
- 定时清理过期数据库文件
- 推送到NATS JetStream服务成功后删除记录


6. NATS JetStream
描述: NATS JetStream 是一个高性能的消息队列，用于在不同服务间传输消息。
建议:
根据需要选择合适的发布/订阅模式。
使用消息持久化功能，以确保数据不会因系统故障而丢失。
实现消费的消息追溯和监控，确保消费端能够正确处理消息。

7. InfluxDB -------> MongoDB
描述: 从 InfluxDB 迁移到 MongoDB，提供灵活的文档存储。
建议:
InfluxDB 数据提供给前端实现显示, 定时进行数据组织转化从 InfluxDB 自动转化到 MongoDB长久保存。
在 MongoDB 中创建合适的索引以优化查询性能。
适时使用 MongoDB 的聚合管道功能进行数据分析。

8. 整体流程优化与监控
在该架构中，实施监控和管理措施，确保性能和可用性：


扩展性:

在每个组件中保留可扩展性，确保随着数据量的增加能够轻松地添加新实例或服务。
使用微服务架构实现各模块的独立部署与维护，简化版本管理和未来的功能扩展。

总结
您的这个架构设计整合了实时数据采集、清洗和长效存储，适用于高并发和大数据量的场景。结合上述详细的建议和优化措施，能够帮助您建立一个灵活、可靠的实时数据处理系统。确保监控和管理各层的数据流动与状态，从而实现数据的有效处理和资源的合理利用。


先构建各个服务的基本的框架, 数据交换的数据格式以json以主, 各个服务先提供基本的功能, 整体流程贯通后再细化细节

使用 golang 实现, 配置文件, docker-compose

## 项目结构

```
mdc_full/
├── 00_device_admin/          # 设备管理界面
├── 01_device_api/           # 设备API接口
├── 02_device_collect/       # 设备数据采集服务
├── 03_data_cleaning/        # 数据清洗转换服务
├── 04_data_push/           # 数据推送服务
├── 11_server_jet/          # NATS JetStream消费服务
├── 12_server_api/          # API服务器
├── shared/                 # 共享组件和数据结构
├── configs/                # 配置文件
├── docker-compose.yml      # Docker编排文件
└── README.md
```

## 数据流格式

```json
{
  "device_id": "string",
  "timestamp": "2024-01-01T00:00:00Z",
  "data_type": "string",
  "raw_data": {},
  "processed_data": {},
  "metadata": {}
}
```

## 快速启动

```bash
# 1. 项目初始化
./scripts/setup.sh

# 2. 启动所有服务
make up

# 3. 发送测试数据
./scripts/test-data.sh

# 4. 查看服务状态
make status

# 5. 查看日志
make logs
```

## 服务说明

### 基础设施服务
- **Redis** (端口 6379): 临时数据存储
- **NATS JetStream** (端口 4222): 消息队列
- **InfluxDB** (端口 8086): 时序数据库
- **MongoDB** (端口 27017): 文档数据库

### 应用服务
- **device-collect** (端口 8081): 设备数据采集服务
- **data-cleaning** (端口 8082): 数据清洗转换服务
- **data-push** (端口 8083): 数据推送服务
- **server-jet** (端口 8084): NATS JetStream消费服务
- **server-api** (端口 9005): API服务器

## API接口

### 设备数据采集 (8081)
```bash
# 发送设备数据
POST /api/v1/device/data

# 获取设备数据
GET /api/v1/device/data/{device_id}

# 获取统计信息
GET /api/v1/stats
```

### API服务器 (9005)
```bash
# 获取设备列表
GET /api/v1/devices

# 获取设备信息
GET /api/v1/devices/{device_id}

# 获取实时数据
GET /api/v1/data/realtime

# 获取历史数据
GET /api/v1/data/historical

# 获取统计概览
GET /api/v1/stats/overview
```

## 数据流程

1. **设备** → **device-collect** (HTTP API)
2. **device-collect** → **Redis** (临时存储)
3. **data-cleaning** ← **Redis** (定时读取)
4. **data-cleaning** → **SQLite** (本地持久化)
5. **data-push** ← **SQLite** (定时读取)
6. **data-push** → **NATS JetStream** (消息队列)
7. **server-jet** ← **NATS JetStream** (消费消息)
8. **server-jet** → **InfluxDB** + **MongoDB** (存储)
9. **server-api** ← **InfluxDB** + **MongoDB** (查询)

## 开发指南

### 添加新的数据处理规则
1. 修改 `03_data_cleaning/main.go` 中的 `cleanAndTransform` 函数
2. 重新构建服务: `make restart-data-cleaning`

### 添加新的API接口
1. 在 `12_server_api/main.go` 中添加新的路由和处理函数
2. 重新构建服务: `make restart-server-api`

### 监控和调试
```bash
# 查看特定服务日志
make logs-device-collect

# 进入服务容器
make shell-redis

# 重启特定服务
make restart-server-jet
```

## 配置说明

每个服务都有独立的配置文件在 `configs/` 目录下，支持环境变量覆盖：

- `SERVICE_HOST`: 服务监听地址
- `SERVICE_PORT`: 服务端口
- `REDIS_HOST`: Redis主机地址
- `NATS_URL`: NATS连接URL
- `INFLUXDB_URL`: InfluxDB连接URL
- `MONGODB_URI`: MongoDB连接URI


# go, node
- augment 无法直接使用 go, node, 请查找到安装路径 export, 再使用.


# 新端口分配方案
服务类型	服务名称	旧端口	新端口	说明

基础服务	
Redis	6379	6379	保持不变
NATS	4222	4222	保持不变
InfluxDB	8086	8086	保持不变
MongoDB	27017	27017	保持不变


数据采集链	
02_device_collect	8081	9001	设备数据采集
03_data_cleaning	8082	9002	数据清洗
04_data_push	8083	9003	数据推送
11_server_jet	8084	9004	NATS消费

01_device_api/fanuc_v2	8080	9010	设备API接口


API服务	
12_server_api	9005	9005	Go API服务
31_machine_backend	9980	9006	Python API服务


前端服务	
30_machine_status	3300	9100	机器状态前端
13_server_admin	3302	9101	管理后台前端
20_viewer	3000	9102	数据查看器前端

新建一个页面, 这个页面计算每天的设备的使用情况统计
当天的数据读取 influxdb sensor_data, 计算
之前的数据读取 mongodb, 如果不存在, 读取 influxdb 计算, 保存到 Mongodb, 返回. 如果 influxdb 也没数据, 返回空
定时任务, 每天 0:30 自动计算前一天的数据, 保存到 mongodb

数据统计
1. 所有当天设备汇集统计, 不同日期的横向对比
2. 每个机台当天统计, 每个机台不同日期的横向对比
3. 每个机台每个班次的统计, 不同日期的横向对比

# 利用率
设备可利用率 = (设备运行时间 ÷ 总时间) × 100%
- OP1: 总时间 = 24小时
- OP2: 总时间 = 24小时 - 休息时间


# 产量达成率
(实际产量 ÷ 计划产量) × 100%


# 设置页面添加新的设置项
1. 每天开始时间
在 http://localhost:9100/device_status_history, http://localhost:9100/production_progress, http://localhost:9100/production_progress_v2 页面都有一个时间段, 起始时间到结束时间, 现在是硬编码从 08:00 - 08:00 (第二天), 但是这个时间应该可以设置, 适合不同工厂的实际上班时间. 有的工厂是 8:00 开始上班, 有些是 9:00, 有些是 9:30. 所以需要有一个设置, 来设置每天的开始时间. 自动计算24小时的结束时间.
这个时间动态应用到以上的页面上, API 数据请求返回的数据同样需要有跨天计算.

2. 班次设置
设置 早中晚(自由管理, 不限于早中晚)班次, 以及每个班次的开始和结束时间.

3. 休息时间
设置休息时间, 以及休息时间的开始和结束时间.

4. 利用率总时间公式 (给利用率计算时提供不同的算法)
- OP1: 总时间 = 24小时
- OP2: 总时间 = 24小时 - 休息时间