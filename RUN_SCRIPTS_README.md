# 项目运行脚本使用指南

## 概述

本项目为每个服务都创建了独立的 `run.sh` 运行脚本，以及全局的启动和停止脚本，方便开发和部署。

## 脚本功能

### 1. 单个服务运行脚本

每个项目目录下都有一个 `run.sh` 脚本，具有以下功能：

- ✅ **环境检查**：自动检查Go/Node.js环境配置
- ✅ **端口管理**：检查端口占用，自动杀死冲突进程
- ✅ **依赖管理**：自动下载和安装依赖
- ✅ **目录创建**：自动创建必要的目录（logs、cache等）
- ✅ **配置验证**：检查配置文件是否存在
- ✅ **服务启动**：启动对应的服务

### 2. 全局管理脚本

- `start_all.sh`：启动所有服务
- `stop_all.sh`：停止所有服务

## 服务列表和端口配置

| 服务名称 | 目录 | 端口 | 类型 | 说明 |
|---------|------|------|------|------|
| FANUC设备采集器 | `01_device_api/fanuc_v2` | 8080 | Go(Docker) | FANUC FOCAS2设备采集(容器运行) |
| 设备数据采集服务 | `02_device_collect` | 9001 | Go | 设备数据采集 |
| 数据生成服务 | `02_generate_data` | - | Go | 模拟数据生成 |
| 数据推送服务 | `05_data_push` | 9003 | Go | 数据推送到InfluxDB |
| NATS JetStream服务 | `11_server_jet` | 9004 | Go | 消息队列处理 |
| Go API服务 | `12_server_api` | 9005 | Go | 统一API接口 |
| 管理后台前端 | `13_server_admin` | 9101 | React | 管理界面 |
| 数据查看器前端 | `20_viewer` | 3000 | React | 数据可视化 |
| 机器状态前端 | `30_machine_status` | 9100 | Next.js | 机器状态监控 |

## 使用方法

### 启动单个服务

```bash
# 进入服务目录
cd 12_server_api

# 运行启动脚本
./run.sh
```

### 启动所有服务

```bash
# 启动所有服务（按正确顺序）
./start_all.sh

# 或者显式指定启动所有服务
./start_all.sh --all
```

### 启动指定服务

```bash
# 只启动API服务
./start_all.sh --service 12_server_api

# 只启动FANUC采集器
./start_all.sh --service 01_device_api/fanuc_v2
```

### 查看可用服务

```bash
# 列出所有可用服务
./start_all.sh --list
```

### 停止所有服务

```bash
# 停止所有服务并清理端口
./stop_all.sh
```

### 查看帮助

```bash
# 查看启动脚本帮助
./start_all.sh --help
```

## 启动顺序

系统按以下顺序启动服务，确保依赖关系正确：

1. **Docker基础服务** (Redis, NATS, InfluxDB, MongoDB)
2. **数据生成服务** (02_generate_data)
3. **设备数据采集服务** (02_device_collect)
4. **数据推送服务** (05_data_push)
5. **NATS JetStream服务** (11_server_jet)
6. **Go API服务** (12_server_api)
7. **FANUC设备采集器** (01_device_api/fanuc_v2)
8. **前端服务** (13_server_admin, 20_viewer, 30_machine_status)

## 环境要求

### Go服务

- Go 1.19+
- CGO支持（FANUC采集器需要）
- FOCAS2库（FANUC采集器需要）

### Node.js服务

- Node.js 16+
- npm 或 yarn

### Docker服务

- Docker
- Docker Compose

## 故障排除

### 端口冲突

如果遇到端口冲突，脚本会自动尝试停止占用端口的进程。如果自动处理失败，可以手动处理：

```bash
# 查看端口占用
lsof -i:9005

# 停止占用端口的进程
lsof -ti:9005 | xargs kill -9
```

### Go环境问题

如果Go环境有问题，脚本会自动设置环境变量：

```bash
export PATH=/opt/homebrew/bin:$PATH
export GOROOT=/opt/homebrew/opt/go/libexec
```

### Node.js依赖问题

如果Node.js依赖有问题，可以手动重新安装：

```bash
# 删除node_modules
rm -rf node_modules

# 重新安装依赖
npm install
```

### FANUC采集器特殊要求

FANUC采集器运行在Docker容器中，需要特殊配置：

#### Docker容器要求：
1. **容器名称**：`fanuc-focas-dev-x86`
2. **容器镜像**：包含Go环境和FOCAS2库的专用镜像
3. **端口映射**：8080:8080
4. **目录挂载**：项目目录挂载到容器的/app目录

#### 容器创建示例：
```bash
docker run -d --name fanuc-focas-dev-x86 \
  -p 8080:8080 \
  -v $(pwd):/app \
  -w /app \
  fanuc-focas-dev:latest \
  tail -f /dev/null
```

#### FOCAS2库要求：
1. 确保容器内 `fwlib` 目录存在
2. 确保包含 `fwlib32.h` 头文件
3. 确保包含库文件（.so/.a）
4. 确保CGO编译环境可用

#### 容器管理命令：
- 查看容器日志：`docker logs -f fanuc-focas-dev-x86`
- 进入容器调试：`docker exec -it fanuc-focas-dev-x86 bash`
- 停止容器：`docker stop fanuc-focas-dev-x86`

## 日志查看

每个服务的日志都会输出到控制台，同时部分服务会将日志写入 `logs` 目录。

## 配置文件

大部分服务需要配置文件：

- Go服务：`config.yml`
- 前端服务：环境变量或配置文件

## 开发建议

1. **开发时**：使用单个服务的 `run.sh` 脚本
2. **测试时**：使用 `start_all.sh` 启动完整系统
3. **部署时**：考虑使用Docker Compose

## 注意事项

1. 首次运行可能需要下载依赖，耗时较长
2. FANUC采集器需要特殊的库文件支持
3. 前端服务首次启动会自动安装npm依赖
4. 确保有足够的系统资源运行所有服务

## 访问地址

启动完成后，可以通过以下地址访问各个服务：

- **API服务**: http://localhost:9005
- **管理后台**: http://localhost:9101
- **数据查看器**: http://localhost:3000
- **机器状态**: http://localhost:9100
- **FANUC采集器**: http://localhost:8080

## 技术支持

如果遇到问题，请检查：

1. 环境要求是否满足
2. 端口是否被占用
3. 配置文件是否正确
4. 依赖是否正确安装
5. 日志输出中的错误信息
