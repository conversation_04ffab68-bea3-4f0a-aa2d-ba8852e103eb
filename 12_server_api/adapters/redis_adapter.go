/**
 * Redis适配器 - 接口兼容层
 *
 * 功能概述：
 * 本适配器用于桥接新的Redis单例服务与现有的services.RedisService接口
 * 确保在重构过程中现有代码能够正常工作，同时逐步迁移到新的架构
 *
 * 设计模式：
 * - 适配器模式：将新接口适配到旧接口
 * - 代理模式：代理底层Redis操作
 * - 装饰器模式：在原有功能基础上添加新特性
 *
 * 使用场景：
 * - 渐进式重构：在不破坏现有代码的前提下引入新架构
 * - 接口兼容：确保handlers和其他模块能够正常工作
 * - 平滑过渡：为完全迁移到新架构提供过渡方案
 *
 * @package adapters
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-14
 */
package adapters

import (
	"server-api/cache"
	"server-api/models"

	"github.com/redis/go-redis/v9"
)

/**
 * Redis适配器结构体
 *
 * 功能：将新的Redis单例服务适配为旧的RedisService接口
 *
 * 设计特点：
 * - 接口兼容：实现所有旧接口的方法
 * - 委托模式：将实际操作委托给新的Redis服务
 * - 透明代理：对调用者透明，无需修改现有代码
 *
 * @struct RedisServiceAdapter
 */
type RedisServiceAdapter struct {
	/** 新的Redis服务实例 */
	redisService *cache.RedisService
}

/**
 * 创建Redis适配器实例
 *
 * 功能：基于新的Redis服务创建适配器实例
 *
 * @param {*cache.RedisService} redisService - 新的Redis服务实例
 * @returns {*RedisServiceAdapter} 适配器实例
 */
func NewRedisServiceAdapter(redisService *cache.RedisService) *RedisServiceAdapter {
	return &RedisServiceAdapter{
		redisService: redisService,
	}
}

/**
 * 连接Redis
 *
 * 功能：代理Redis连接操作（实际上不执行，因为单例管理器负责连接管理）
 *
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) Connect() error {
	// 不执行实际连接操作，因为连接由单例管理器管理
	// 这里只是为了兼容旧接口
	return nil
}

/**
 * 断开Redis连接
 *
 * 功能：代理Redis断开连接操作（实际上不执行，因为单例管理器负责连接管理）
 */
func (adapter *RedisServiceAdapter) Disconnect() {
	// 不执行实际断开操作，因为连接由单例管理器管理
	// 这里只是为了兼容旧接口
}

/**
 * 检查Redis连接状态
 *
 * 功能：代理Redis连接状态检查操作
 *
 * @returns {bool} 连接状态
 */
func (adapter *RedisServiceAdapter) IsConnected() bool {
	return adapter.redisService.IsAvailable()
}

/**
 * 获取Redis客户端
 *
 * 功能：代理获取Redis客户端的操作
 *
 * @returns {*redis.Client} Redis客户端实例
 */
func (adapter *RedisServiceAdapter) GetClient() *redis.Client {
	return adapter.redisService.GetClient()
}

/**
 * 设置设备状态到Redis
 *
 * 功能：代理设备状态设置操作
 *
 * @param {string} deviceID - 设备ID
 * @param {*models.DeviceStatus} status - 设备状态对象
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) SetDeviceStatus(deviceID string, status *models.DeviceStatus) error {
	return adapter.redisService.SetDeviceStatus(deviceID, status)
}

/**
 * 从Redis获取设备状态
 *
 * 功能：代理设备状态获取操作
 *
 * @param {string} deviceID - 设备ID
 * @returns {*models.DeviceStatus} 设备状态对象
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) GetDeviceStatus(deviceID string) (*models.DeviceStatus, error) {
	return adapter.redisService.GetDeviceStatus(deviceID)
}

/**
 * 设置设备产量到Redis
 *
 * 功能：代理设备产量设置操作
 *
 * @param {string} deviceID - 设备ID
 * @param {*models.DeviceProduction} production - 设备产量对象
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) SetDeviceProduction(deviceID string, production *models.DeviceProduction) error {
	return adapter.redisService.SetDeviceProduction(deviceID, production)
}

/**
 * 从Redis获取设备产量
 *
 * 功能：代理设备产量获取操作
 *
 * @param {string} deviceID - 设备ID
 * @returns {*models.DeviceProduction} 设备产量对象
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) GetDeviceProduction(deviceID string) (*models.DeviceProduction, error) {
	return adapter.redisService.GetDeviceProduction(deviceID)
}

/**
 * 设置所有设备状态到Redis
 *
 * 功能：代理所有设备状态设置操作
 *
 * @param {[]models.DeviceStatus} devices - 设备状态列表
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) SetAllDevicesStatus(devices []models.DeviceStatus) error {
	return adapter.redisService.SetAllDevicesStatus(devices)
}

/**
 * 从Redis获取所有设备状态
 *
 * 功能：代理所有设备状态获取操作
 *
 * @returns {[]models.DeviceStatus} 设备状态列表
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) GetAllDevicesStatus() ([]models.DeviceStatus, error) {
	return adapter.redisService.GetAllDevicesStatus()
}

/**
 * 设置状态统计到Redis
 *
 * 功能：代理状态统计设置操作
 *
 * @param {models.StatusCounts} counts - 状态统计对象
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) SetStatusCounts(counts models.StatusCounts) error {
	return adapter.redisService.SetStatusCounts(counts)
}

/**
 * 从Redis获取状态统计
 *
 * 功能：代理状态统计获取操作
 *
 * @returns {*models.StatusCounts} 状态统计对象
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) GetStatusCounts() (*models.StatusCounts, error) {
	return adapter.redisService.GetStatusCounts()
}

/**
 * 设置设备状态历史到Redis
 *
 * 功能：代理设备状态历史设置操作
 *
 * @param {string} deviceID - 设备ID
 * @param {string} date - 日期字符串
 * @param {[]models.DeviceStatusHistory} history - 状态历史列表
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) SetDeviceStatusHistory(deviceID, date string, history []models.DeviceStatusHistory) error {
	return adapter.redisService.SetDeviceStatusHistory(deviceID, date, history)
}

/**
 * 从Redis获取设备状态历史
 *
 * 功能：代理设备状态历史获取操作
 *
 * @param {string} deviceID - 设备ID
 * @param {string} date - 日期字符串
 * @returns {[]models.DeviceStatusHistory} 状态历史列表
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) GetDeviceStatusHistory(deviceID, date string) ([]models.DeviceStatusHistory, error) {
	return adapter.redisService.GetDeviceStatusHistory(deviceID, date)
}

/**
 * 删除过期的缓存键
 *
 * 功能：代理过期缓存清理操作
 *
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) DeleteExpiredKeys() error {
	return adapter.redisService.DeleteExpiredKeys()
}

/**
 * 获取缓存统计信息
 *
 * 功能：代理缓存统计信息获取操作
 *
 * @returns {map[string]interface{}} 缓存统计信息
 * @returns {error} 错误信息
 */
func (adapter *RedisServiceAdapter) GetCacheStats() (map[string]interface{}, error) {
	return adapter.redisService.GetCacheStats()
}
