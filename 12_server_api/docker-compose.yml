services:
  mdc_server_api:
    image: mdc_server_api:1.1.8
    container_name: mdc_server_api
    restart: always
    ports:
      - "9005:9005"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config.yml:/app/config.yml
    environment:
      TZ: Asia/Shanghai
      SITE_NAME: 演示智能制造系统2
      SITE_LOGO: /assets/demo-logo.png
      SITE_SUPPORT: 演示科技公司2
      SITE_SUPPORT_INFO: "技术支持: 400-DEMO-222"
    networks:
      - mdc_full_mdc_network

networks:
  mdc_full_mdc_network:
    external: true
