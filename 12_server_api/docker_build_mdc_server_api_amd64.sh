#!/bin/bash

# 确保脚本在出错时停止执行
set -e

# 镜像名称和标签
IMAGE_NAME="mdc_server_api"
TAG="1.1.8"
PLATFORM="linux/amd64"

echo "构建 ${PLATFORM} 架构的 Docker 镜像..."
echo "镜像名称: ${IMAGE_NAME}"
echo "镜像标签: ${TAG}"
echo "--------------------------------"

# 启用 Docker BuildKit
export DOCKER_BUILDKIT=1

# 使用 DOCKER_DEFAULT_PLATFORM 环境变量指定目标平台
export DOCKER_DEFAULT_PLATFORM=${PLATFORM}

# 切换到项目根目录进行构建（因为 Dockerfile 中需要访问 shared 目录）
cd ..

# 构建镜像，指定 12_server_api 目录的 Dockerfile
docker build -f 12_server_api/Dockerfile -t ${IMAGE_NAME}:${TAG} .

echo "${PLATFORM} Docker 镜像构建完成: ${IMAGE_NAME}:${TAG}"
echo "可以使用以下命令运行容器:"
echo "  docker run -p 9005:9005 -v $(pwd)/data:/app/data ${IMAGE_NAME}:${TAG}"

# 添加执行权限（回到原目录）
cd 12_server_api
chmod +x docker_build_mdc_server_api_amd64.sh docker_push_mdc_server_api.sh
cd ..

# 更新 ./12_server_api/docker_push_mdc_server_api.sh 中的镜像版本号
# 确保 push.sh 文件存在并且路径正确
if [ -f "12_server_api/docker_push_mdc_server_api.sh" ]; then
    sed -i '' "s/IMAGE_VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/IMAGE_VERSION=\"${TAG}\"/" 12_server_api/docker_push_mdc_server_api.sh
else
    echo "错误: 12_server_api/docker_push_mdc_server_api.sh 文件不存在"
fi

# 更新 ./12_server_api/docker-compose.yml 中的镜像版本号
#services:
#  mdc_server_api:
#    image: mdc_server_api:1.0.0
# 确保 docker-compose.yml 文件存在并且路径正确
if [ -f "12_server_api/docker-compose.yml" ]; then
    sed -i '' "s/image: ${IMAGE_NAME}:[0-9]*\.[0-9]*\.[0-9]*/image: ${IMAGE_NAME}:${TAG}/" 12_server_api/docker-compose.yml
else
    echo "提示: 12_server_api/docker-compose.yml 文件不存在，跳过版本更新"
fi

IMAGE_ID=$(docker images -q ${IMAGE_NAME}:${TAG})

echo "镜像ID: ${IMAGE_ID}"
