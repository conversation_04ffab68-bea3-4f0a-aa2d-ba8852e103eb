/**
 * MongoDB数据库服务 - 业务层实现
 *
 * 功能概述：
 * 本服务基于MongoDB管理器单例，提供具体的业务数据库功能
 * 包括设备管理、产品管理、工序管理、订单管理等业务场景的CRUD操作
 *
 * 核心功能：
 * - 设备管理：设备信息的增删改查、状态管理、配置管理
 * - 产品管理：产品信息的维护、规格管理、分类管理
 * - 工序管理：生产工序的定义、流程管理、参数配置
 * - 订单管理：生产订单的全生命周期管理
 * - 用户管理：用户账户、权限、认证信息管理
 * - 统计数据：历史统计数据的持久化存储
 *
 * 技术特性：
 * - 文档数据库：灵活的JSON文档存储，适合复杂业务数据
 * - 事务支持：ACID事务保证数据一致性
 * - 索引优化：基于查询模式的索引设计，提升查询性能
 * - 分页查询：高效的分页和排序机制
 * - 全文搜索：支持模糊搜索和多字段搜索
 * - 聚合查询：复杂的数据统计和分析功能
 *
 * @package db
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-14
 */
package db

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	"server-api/models"
	"shared/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

/**
 * MongoDB数据库服务结构体
 *
 * 功能：作为业务层的MongoDB数据库服务，提供具体的数据库操作接口
 *
 * 设计特点：
 * - 业务导向：针对具体业务场景设计的数据库接口
 * - 类型安全：强类型的数据库操作，避免类型错误
 * - 错误处理：完善的错误处理和异常恢复机制
 * - 性能优化：索引利用和查询优化
 *
 * 数据管理：
 * - 集合管理：设备、产品、工序、订单等业务集合
 * - 索引优化：基于查询模式的索引策略
 * - 数据验证：业务规则的数据完整性检查
 * - 版本控制：数据变更的审计和版本管理
 *
 * @struct MongoDBService
 */
type MongoDBService struct {
	/** MongoDB管理器实例，提供底层连接管理 */
	manager *MongoDBManager
}

/**
 * 创建MongoDB数据库服务实例
 *
 * 功能：基于MongoDB管理器单例创建数据库服务实例
 *
 * 初始化流程：
 * 1. 获取MongoDB管理器单例实例
 * 2. 验证管理器连接状态
 * 3. 返回可用的服务实例
 *
 * 依赖关系：
 * - 依赖MongoDB管理器提供底层连接
 * - 依赖models包提供数据模型
 * - 依赖logger包提供日志功能
 *
 * @returns {*MongoDBService} MongoDB数据库服务实例
 *
 * @example
 * // 创建MongoDB数据库服务
 * service := db.NewMongoDBService()
 *
 * // 查询设备列表
 * params := &models.QueryParams{Page: 1, PageSize: 20}
 * result, err := service.GetDevices(params)
 * if err != nil {
 *     log.Printf("查询设备失败: %v", err)
 * }
 */
func NewMongoDBService() *MongoDBService {
	logger.Info("🔧 创建MongoDB数据库服务实例...")

	return &MongoDBService{
		manager: GetMongoDBManager(),
	}
}

/**
 * 检查MongoDB服务可用性
 *
 * 功能：检查MongoDB连接状态和服务可用性
 *
 * 检查项目：
 * - MongoDB管理器是否已初始化
 * - MongoDB连接是否正常
 * - 数据库实例是否可用
 *
 * @returns {bool} true表示服务可用，false表示服务不可用
 */
func (ms *MongoDBService) IsAvailable() bool {
	if ms.manager == nil {
		return false
	}
	return ms.manager.IsConnected()
}

/**
 * 获取数据库实例
 *
 * 功能：获取底层数据库实例，用于执行原生MongoDB操作
 *
 * 使用场景：
 * - 执行复杂的MongoDB操作
 * - 使用MongoDB的高级功能
 * - 批量操作和事务处理
 *
 * @returns {*mongo.Database} 数据库实例，服务不可用时返回nil
 */
func (ms *MongoDBService) GetDatabase() *mongo.Database {
	if !ms.IsAvailable() {
		return nil
	}
	return ms.manager.GetDatabase()
}

/**
 * 获取MongoDB客户端
 *
 * 功能：获取底层MongoDB客户端实例
 *
 * @returns {*mongo.Client} MongoDB客户端实例，服务不可用时返回nil
 */
func (ms *MongoDBService) GetClient() *mongo.Client {
	if !ms.IsAvailable() {
		return nil
	}
	return ms.manager.GetClient()
}

/**
 * 获取设备列表
 *
 * 功能：分页查询设备信息，支持多字段搜索、状态过滤、排序等功能
 *
 * 查询特性：
 * - 多字段搜索：支持设备ID、名称、型号、位置的模糊搜索
 * - 状态过滤：按设备状态（启用/禁用）进行过滤
 * - 灵活排序：支持任意字段的升序/降序排序
 * - 高效分页：基于skip/limit的分页机制
 *
 * 性能优化：
 * - 索引利用：利用设备ID、状态等字段的索引
 * - 计数优化：使用CountDocuments进行高效计数
 * - 游标管理：正确的游标生命周期管理
 * - 超时控制：10秒查询超时，避免长时间阻塞
 *
 * @param {*models.QueryParams} params - 查询参数对象
 * @returns {*models.ListResponse} 分页查询结果
 * @returns {error} 查询过程中的错误信息
 */
func (ms *MongoDBService) GetDevices(params *models.QueryParams) (*models.ListResponse, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	// 创建带超时的查询上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询过滤条件
	filter := bson.M{}

	// 多字段模糊搜索
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"device_id": bson.M{"$regex": params.Search, "$options": "i"}},
			{"name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"model": bson.M{"$regex": params.Search, "$options": "i"}},
			{"location": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}

	// 状态精确过滤
	if params.Status != "" {
		filter["status"] = params.Status
	}

	// 计算符合条件的总记录数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("统计设备数量失败: %v", err)
	}

	// 计算分页参数
	skip := (params.Page - 1) * params.PageSize

	// 构建排序条件
	var sort bson.D
	sortBy := params.SortBy
	if sortBy == "" {
		// 默认按排序字段升序排序，然后按创建时间排序
		sort = bson.D{
			{Key: "sort_order", Value: 1}, // 排序字段升序（数字小的在前）
			{Key: "created_at", Value: 1}, // 创建时间升序作为次要排序
		}
	} else {
		// 用户指定了排序字段
		sort = bson.D{{Key: sortBy, Value: 1}} // 升序
		if params.SortDesc {
			sort = bson.D{{Key: sortBy, Value: -1}} // 降序
		}
	}

	// 构建查询选项
	opts := options.Find().
		SetSort(sort).                   // 设置排序
		SetSkip(int64(skip)).            // 设置跳过记录数
		SetLimit(int64(params.PageSize)) // 设置返回记录数

	// 执行分页查询
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}
	defer cursor.Close(ctx) // 确保游标正确关闭

	// 解析查询结果
	var devices []models.Device
	if err = cursor.All(ctx, &devices); err != nil {
		return nil, fmt.Errorf("解析设备数据失败: %v", err)
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	// 构建响应结果
	return &models.ListResponse{
		Data:       devices,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

/**
 * 创建设备
 *
 * 功能：在数据库中创建新的设备记录
 *
 * @param {*models.Device} device - 设备对象
 * @returns {error} 创建过程中的错误信息
 */
func (ms *MongoDBService) CreateDevice(device *models.Device) error {
	if !ms.IsAvailable() {
		return fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	device.CreatedAt = time.Now()
	device.UpdatedAt = time.Now()

	_, err := collection.InsertOne(ctx, device)
	if err != nil {
		return fmt.Errorf("创建设备失败: %v", err)
	}

	logger.Infof("✅ 设备创建成功: %s", device.DeviceID)
	return nil
}

/**
 * 更新设备
 *
 * 功能：更新现有设备的信息
 *
 * @param {string} id - MongoDB ObjectID
 * @param {*models.Device} device - 更新的设备对象
 * @returns {error} 更新过程中的错误信息
 */
func (ms *MongoDBService) UpdateDevice(id string, device *models.Device) error {
	if !ms.IsAvailable() {
		return fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的设备ID: %v", err)
	}

	device.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"device_id":        device.DeviceID,
			"name":             device.Name,
			"data_type":        device.DataType,
			"location":         device.Location,
			"status":           device.Status,
			"sort_order":       device.SortOrder,
			"brand":            device.Brand,
			"model":            device.Model,
			"ip":               device.IP,
			"port":             device.Port,
			"enabled":          device.Enabled,
			"auto_start":       device.AutoStart,
			"collect_interval": device.CollectInterval,
			"timeout":          device.Timeout,
			"retry_count":      device.RetryCount,
			"retry_delay":      device.RetryDelay,
			"description":      device.Description,
			"updated_at":       device.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("更新设备失败: %v", err)
	}

	logger.Infof("✅ 设备更新成功: %s", device.DeviceID)
	return nil
}

/**
 * 删除设备
 *
 * 功能：从数据库中删除指定的设备
 *
 * @param {string} id - MongoDB ObjectID
 * @returns {error} 删除过程中的错误信息
 */
func (ms *MongoDBService) DeleteDevice(id string) error {
	if !ms.IsAvailable() {
		return fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的设备ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除设备失败: %v", err)
	}

	logger.Infof("✅ 设备删除成功: %s", id)
	return nil
}

/**
 * 获取单个设备（通过MongoDB ObjectID）
 *
 * 功能：根据MongoDB ObjectID获取设备详情
 *
 * @param {string} id - MongoDB ObjectID
 * @returns {*models.Device} 设备对象，未找到时返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (ms *MongoDBService) GetDevice(id string) (*models.Device, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("无效的设备ID: %v", err)
	}

	var device models.Device
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("设备未找到")
		}
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}

	return &device, nil
}

/**
 * 通过设备ID获取设备配置（精确匹配）
 *
 * 功能：根据业务设备ID（device_id字段）精确查找设备配置
 *
 * @param {string} deviceID - 业务设备ID
 * @returns {*models.Device} 设备配置信息，如果未找到则返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (ms *MongoDBService) GetDeviceByDeviceID(deviceID string) (*models.Device, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用精确匹配查询设备ID
	filter := bson.M{"device_id": deviceID}

	var device models.Device
	err := collection.FindOne(ctx, filter).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("设备未找到")
		}
		return nil, fmt.Errorf("通过设备ID查询设备失败: %v", err)
	}

	return &device, nil
}

/**
 * 获取设备信息映射（用于API响应）
 *
 * 功能：根据设备ID获取设备信息，并转换为API响应格式的映射
 *
 * 设计目的：
 * - 专门为状态历史API提供设备信息查询
 * - 返回标准化的设备信息映射格式
 * - 处理设备未找到的情况，返回默认值避免API失败
 * - 确保所有字段都有合理的默认值
 *
 * 与GetDeviceByDeviceID的区别：
 * - GetDeviceByDeviceID：返回完整的Device模型，用于管理功能
 * - GetDeviceInfoMap：返回API格式的映射，用于前端显示
 *
 * 错误处理策略：
 * - 设备未找到时返回包含默认值的映射，而不是错误
 * - 确保API的稳定性，避免因设备信息缺失导致的API失败
 * - 提供清晰的日志记录，便于问题排查
 *
 * @param {string} deviceID - 业务设备ID
 * @returns {map[string]interface{}} 设备信息映射，包含id、code、name、brand、model、location字段
 * @returns {error} 查询过程中的错误信息（仅在数据库连接等严重问题时返回）
 *
 * @example
 * deviceInfo, err := mongoService.GetDeviceInfoMap("d6b6c35f-2468-429f-831b-c292c821170a")
 * if err != nil {
 *     // 处理数据库连接错误
 * }
 * // deviceInfo["name"] 包含设备名称
 */
func (ms *MongoDBService) GetDeviceInfoMap(deviceID string) (map[string]interface{}, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	// 创建带超时的查询上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用精确匹配查询设备ID
	filter := bson.M{"device_id": deviceID}

	var device models.Device
	err := collection.FindOne(ctx, filter).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			logger.Warnf("⚠️ 设备未找到: %s，返回默认设备信息", deviceID)
			// 返回默认设备信息，避免API失败
			return map[string]interface{}{
				"id":       deviceID,
				"code":     deviceID,
				"name":     "unknown",
				"brand":    "unknown",
				"model":    "unknown",
				"location": "unknown",
			}, nil
		}
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}

	// 构建设备信息映射
	deviceInfo := map[string]interface{}{
		"id":       device.DeviceID,
		"code":     device.DeviceID,
		"name":     device.Name,
		"brand":    device.Brand,
		"model":    device.Model,
		"location": device.Location,
	}

	// 处理空字段，使用默认值确保API响应的一致性
	if deviceInfo["name"] == "" || deviceInfo["name"] == nil {
		deviceInfo["name"] = "unknown"
	}
	if deviceInfo["brand"] == "" || deviceInfo["brand"] == nil {
		deviceInfo["brand"] = "unknown"
	}
	if deviceInfo["model"] == "" || deviceInfo["model"] == nil {
		deviceInfo["model"] = "unknown"
	}
	if deviceInfo["location"] == "" || deviceInfo["location"] == nil {
		deviceInfo["location"] = "unknown"
	}

	logger.Debugf("✅ 从MongoDB获取设备信息成功: %s (名称: %s, 位置: %s)",
		deviceID, deviceInfo["name"], deviceInfo["location"])

	return deviceInfo, nil
}

/**
 * 获取所有设备ID列表（用于定时任务）
 *
 * 功能：获取所有设备的ID列表，用于批量处理
 *
 * @returns {[]string} 设备ID列表
 * @returns {error} 查询过程中的错误信息
 */
func (ms *MongoDBService) GetAllDeviceIDs() ([]string, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("devices")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 只查询device_id字段
	opts := options.Find().SetProjection(bson.M{"device_id": 1, "_id": 0})
	cursor, err := collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("查询设备失败: %v", err)
	}
	defer cursor.Close(ctx)

	var devices []struct {
		DeviceID string `bson:"device_id"`
	}
	if err = cursor.All(ctx, &devices); err != nil {
		return nil, fmt.Errorf("解析设备数据失败: %v", err)
	}

	deviceIDs := make([]string, len(devices))
	for i, device := range devices {
		deviceIDs[i] = device.DeviceID
	}

	logger.Debugf("📋 找到 %d 个设备用于状态历史处理", len(deviceIDs))
	return deviceIDs, nil
}

/**
 * 获取系统设置
 *
 * 功能：根据设置类型获取系统配置
 *
 * @param {string} settingsType - 设置类型
 * @returns {*models.SystemSettings} 系统设置对象，未找到时返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (ms *MongoDBService) GetSettings(settingsType string) (*models.SystemSettings, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("settings")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var settings models.SystemSettings
	err := collection.FindOne(ctx, bson.M{"type": settingsType}).Decode(&settings)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// 如果没有找到设置，返回 nil 而不是错误
			return nil, nil
		}
		return nil, fmt.Errorf("获取系统设置失败: %v", err)
	}

	return &settings, nil
}

/**
 * 保存系统设置
 *
 * 功能：保存或更新系统配置
 *
 * @param {string} settingsType - 设置类型
 * @param {interface{}} settingsData - 设置数据
 * @returns {error} 保存过程中的错误信息
 */
func (ms *MongoDBService) SaveSettings(settingsType string, settingsData interface{}) error {
	if !ms.IsAvailable() {
		return fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("settings")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用 upsert 操作：如果存在则更新，不存在则创建
	filter := bson.M{"type": settingsType}
	update := bson.M{
		"$set": bson.M{
			"settings":   settingsData,
			"updated_at": time.Now(),
		},
		"$setOnInsert": bson.M{
			"type":       settingsType,
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("保存系统设置失败: %v", err)
	}

	logger.Infof("✅ 系统设置保存成功: type=%s", settingsType)
	return nil
}

/**
 * 获取设备状态历史数据
 *
 * 功能：从MongoDB获取设备状态历史数据
 *
 * @param {string} deviceID - 设备ID
 * @param {string} date - 日期字符串
 * @returns {[]models.DeviceStatusHistory} 状态历史列表
 * @returns {error} 查询过程中的错误信息
 */
func (ms *MongoDBService) GetDeviceStatusHistory(deviceID, date string) ([]models.DeviceStatusHistory, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("device_status_history")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 查询条件
	filter := bson.M{
		"device_id": deviceID,
		"date":      date,
	}

	var historyDoc models.DeviceStatusHistoryMongo
	err := collection.FindOne(ctx, filter).Decode(&historyDoc)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 没有找到记录，返回nil
		}
		return nil, fmt.Errorf("查询设备状态历史失败: %v", err)
	}

	logger.Debugf("📊 从MongoDB获取到 %d 条状态历史记录 %s:%s", len(historyDoc.StatusHistory), deviceID, date)
	return historyDoc.StatusHistory, nil
}

/**
 * 按时间范围获取设备状态历史数据
 *
 * 功能：从MongoDB获取指定设备在指定时间范围内的状态历史数据
 *
 * 查询逻辑：
 * 1. 先根据日期范围筛选文档（提高查询效率）
 * 2. 再根据timestamp字段筛选具体的时间范围
 * 3. 按timestamp字段排序返回结果
 *
 * 注意事项：
 * - MongoDB中device_status_history集合按date字段分文档存储
 * - 每个文档的status_history数组包含该日期的所有状态记录
 * - timestamp字段是连续的，需要联合查询以提高效率
 *
 * @param {string} deviceID - 设备ID
 * @param {time.Time} startDateTime - 开始时间（UTC时间）
 * @param {time.Time} endDateTime - 结束时间（UTC时间）
 * @returns {[]models.DeviceStatusHistory} 状态历史列表，按时间排序
 * @returns {error} 查询过程中的错误信息
 */
func (ms *MongoDBService) GetDeviceStatusHistoryByTimeRange(deviceID string, startDateTime, endDateTime time.Time) ([]models.DeviceStatusHistory, error) {
	if !ms.IsAvailable() {
		return nil, fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("device_status_history")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	logger.Debugf("🔍 查询MongoDB设备状态历史: 设备=%s, 时间范围=%s到%s",
		deviceID,
		startDateTime.Format(time.RFC3339),
		endDateTime.Format(time.RFC3339))

	// 计算需要查询的日期范围
	startDate := startDateTime.Format("2006-01-02")
	endDate := endDateTime.Format("2006-01-02")

	// 构建日期范围查询条件
	var dateFilter bson.M
	if startDate == endDate {
		// 同一天
		dateFilter = bson.M{"date": startDate}
	} else {
		// 跨日查询
		dateFilter = bson.M{
			"date": bson.M{
				"$gte": startDate,
				"$lte": endDate,
			},
		}
	}

	// 查询条件：设备ID + 日期范围
	filter := bson.M{
		"device_id": deviceID,
		"$and": []bson.M{
			dateFilter,
		},
	}

	// 执行查询
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("查询设备状态历史失败: %v", err)
	}
	defer cursor.Close(ctx)

	var allStatusHistory []models.DeviceStatusHistory

	// 遍历查询结果
	for cursor.Next(ctx) {
		var historyDoc models.DeviceStatusHistoryMongo
		if err := cursor.Decode(&historyDoc); err != nil {
			logger.Warnf("⚠️ 解码状态历史文档失败: %v", err)
			continue
		}

		// 筛选时间范围内的状态记录
		for _, status := range historyDoc.StatusHistory {
			// status.Timestamp 已经是 time.Time 类型，直接使用
			timestamp := status.Timestamp

			// 检查是否在时间范围内
			if (timestamp.Equal(startDateTime) || timestamp.After(startDateTime)) &&
				(timestamp.Equal(endDateTime) || timestamp.Before(endDateTime)) {
				allStatusHistory = append(allStatusHistory, status)
			}
		}
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("遍历查询结果失败: %v", err)
	}

	// 按时间戳排序（Timestamp 已经是 time.Time 类型，直接比较）
	sort.Slice(allStatusHistory, func(i, j int) bool {
		return allStatusHistory[i].Timestamp.Before(allStatusHistory[j].Timestamp)
	})

	logger.Debugf("📊 从MongoDB获取到 %d 条时间范围内的状态历史记录 %s", len(allStatusHistory), deviceID)
	return allStatusHistory, nil
}

/**
 * 保存设备状态历史数据
 *
 * 功能：保存设备状态历史数据到MongoDB
 *
 * @param {string} deviceID - 设备ID
 * @param {string} date - 日期字符串
 * @param {[]models.DeviceStatusHistory} statusHistory - 状态历史列表
 * @returns {error} 保存过程中的错误信息
 */
func (ms *MongoDBService) SaveDeviceStatusHistory(deviceID, date string, statusHistory []models.DeviceStatusHistory) error {
	if !ms.IsAvailable() {
		return fmt.Errorf("MongoDB服务不可用")
	}

	database := ms.GetDatabase()
	if database == nil {
		return fmt.Errorf("无法获取数据库实例")
	}

	collection := database.Collection("device_status_history")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 创建或更新文档
	historyDoc := models.DeviceStatusHistoryMongo{
		DeviceID:      deviceID,
		Date:          date,
		StatusHistory: statusHistory,
		UpdatedAt:     time.Now(),
	}

	// 使用upsert操作，如果不存在则创建，存在则更新
	filter := bson.M{
		"device_id": deviceID,
		"date":      date,
	}

	update := bson.M{
		"$set": bson.M{
			"device_id":      historyDoc.DeviceID,
			"date":           historyDoc.Date,
			"status_history": historyDoc.StatusHistory,
			"updated_at":     historyDoc.UpdatedAt,
		},
		"$setOnInsert": bson.M{
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	result, err := collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("保存设备状态历史失败: %v", err)
	}

	if result.UpsertedCount > 0 {
		logger.Debugf("✅ 创建新的状态历史文档 %s:%s，包含 %d 条记录", deviceID, date, len(statusHistory))
	} else {
		logger.Debugf("✅ 更新状态历史文档 %s:%s，包含 %d 条记录", deviceID, date, len(statusHistory))
	}

	return nil
}
