/**
 * MongoDB数据库管理器 - 单例模式实现
 *
 * 功能概述：
 * 本模块实现了MongoDB数据库连接的单例管理模式，确保整个应用程序中只有一个MongoDB连接实例
 * 提供线程安全的连接管理、自动重连、健康检查、事务支持等功能
 *
 * 核心特性：
 * - 单例模式：全局唯一的MongoDB连接实例，避免连接资源浪费
 * - 线程安全：使用sync.Once确保初始化的原子性和线程安全
 * - 自动重连：连接断开时自动尝试重新连接
 * - 健康检查：定期检查连接状态，确保服务可用性
 * - 事务支持：提供ACID事务操作的统一接口
 * - 优雅关闭：支持优雅关闭连接，避免数据丢失
 *
 * 设计模式：
 * - 单例模式：确保全局唯一实例
 * - 工厂模式：提供统一的实例创建接口
 * - 代理模式：封装底层MongoDB操作
 *
 * 使用场景：
 * - 业务数据存储：设备信息、产品信息、订单信息等
 * - 用户管理：用户账户、权限、认证信息
 * - 配置管理：系统配置、工作时间设置等
 * - 历史数据：统计结果、审计日志等长期存储
 *
 * @package db
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-14
 */
package db

import (
	"context"
	"fmt"
	"sync"
	"time"

	"shared/config"
	"shared/logger"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

/**
 * MongoDB连接管理器结构体
 *
 * 功能：作为MongoDB连接的单例管理器，提供全局唯一的连接实例
 *
 * 设计特点：
 * - 单例模式：确保全局只有一个MongoDB连接实例
 * - 线程安全：使用互斥锁保护并发访问
 * - 自动重连：连接失败时自动尝试重新连接
 * - 状态管理：实时跟踪连接状态和健康状况
 * - 事务支持：提供事务操作的统一接口
 *
 * 生命周期：
 * 1. 初始化：通过GetInstance()获取单例实例
 * 2. 连接：调用Initialize()建立MongoDB连接
 * 3. 使用：通过GetClient()或GetDatabase()进行数据库操作
 * 4. 监控：自动进行健康检查和状态更新
 * 5. 关闭：调用Close()优雅关闭连接
 *
 * @struct MongoDBManager
 */
type MongoDBManager struct {
	/** MongoDB客户端实例，负责与数据库服务器的通信 */
	client *mongo.Client

	/** 数据库实例，指向具体的业务数据库 */
	database *mongo.Database

	/** MongoDB连接配置，包含URI、数据库名等信息 */
	config *config.MongoDBConfig

	/** 连接状态标志，用于快速判断MongoDB服务可用性 */
	isConnected bool

	/** 互斥锁，保护并发访问时的线程安全 */
	mutex sync.RWMutex

	/** 全局上下文，用于控制所有MongoDB操作的生命周期 */
	ctx context.Context

	/** 取消函数，用于优雅关闭时取消所有正在进行的操作 */
	cancel context.CancelFunc
}

/**
 * 单例实例变量
 *
 * 使用包级别的私有变量存储单例实例，确保全局唯一性
 * 配合sync.Once使用，保证初始化的原子性和线程安全
 */
var (
	/** MongoDB管理器单例实例 */
	instance *MongoDBManager

	/** 单次初始化控制器，确保单例只被初始化一次 */
	once sync.Once
)

/**
 * 获取MongoDB管理器单例实例
 *
 * 功能：实现线程安全的单例模式，确保全局只有一个MongoDB连接管理器实例
 *
 * 实现细节：
 * - 使用sync.Once确保初始化代码只执行一次
 * - 即使在高并发环境下也能保证线程安全
 * - 延迟初始化：只有在第一次调用时才创建实例
 *
 * 单例优势：
 * - 资源节约：避免创建多个MongoDB连接，节省系统资源
 * - 状态一致：全局共享同一个连接状态，避免状态不一致
 * - 配置统一：所有模块使用相同的MongoDB配置
 * - 连接复用：提高连接利用率，减少连接开销
 *
 * 线程安全：
 * - sync.Once保证初始化的原子性
 * - 读写锁保护实例状态的并发访问
 * - 上下文控制保证操作的生命周期管理
 *
 * @returns {*MongoDBManager} MongoDB管理器单例实例
 *
 * @example
 * // 获取MongoDB管理器实例
 * manager := db.GetMongoDBManager()
 *
 * // 配置并连接MongoDB
 * err := manager.Initialize(mongoConfig)
 * if err != nil {
 *     log.Fatal("Failed to initialize MongoDB:", err)
 * }
 *
 * // 获取数据库实例进行操作
 * database := manager.GetDatabase()
 * collection := database.Collection("devices")
 */
func GetMongoDBManager() *MongoDBManager {
	once.Do(func() {
		// 创建带取消功能的上下文，用于优雅关闭
		ctx, cancel := context.WithCancel(context.Background())

		instance = &MongoDBManager{
			ctx:         ctx,
			cancel:      cancel,
			isConnected: false,
		}

		logger.Info("🔧 MongoDB管理器单例实例已创建")
	})
	return instance
}

/**
 * 初始化MongoDB连接
 *
 * 功能：使用提供的配置初始化MongoDB连接，建立与数据库服务器的通信
 *
 * 初始化流程：
 * 1. 保存MongoDB配置信息
 * 2. 创建带超时的连接上下文
 * 3. 建立MongoDB客户端连接
 * 4. 执行Ping测试验证连接可用性
 * 5. 初始化数据库实例
 * 6. 更新连接状态
 *
 * 配置验证：
 * - 检查URI格式的有效性
 * - 验证认证信息的正确性
 * - 确认数据库名称的合法性
 * - 验证连接参数的合理性
 *
 * 错误处理：
 * - 配置无效时返回详细错误信息
 * - 连接失败时提供重试建议
 * - 认证失败时给出明确提示
 * - 网络问题时提供诊断信息
 *
 * @param {*config.MongoDBConfig} cfg - MongoDB连接配置
 * @returns {error} 初始化过程中的错误信息，成功时返回nil
 *
 * @example
 * config := &config.MongoDBConfig{
 *     URI:      "*******************************************************************",
 *     Database: "mdc_admin",
 * }
 *
 * manager := db.GetMongoDBManager()
 * err := manager.Initialize(config)
 * if err != nil {
 *     log.Printf("MongoDB初始化失败: %v", err)
 * }
 */
func (mm *MongoDBManager) Initialize(cfg *config.MongoDBConfig) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	logger.Info("🗄️ 正在初始化MongoDB连接管理器...")
	logger.Infof("📍 MongoDB URI: %s", cfg.URI)
	logger.Infof("📊 MongoDB Database: %s", cfg.Database)

	// 保存配置
	mm.config = cfg

	// 创建带超时的连接上下文，避免长时间等待
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 配置MongoDB连接池参数
	clientOptions := options.Client().ApplyURI(cfg.URI)

	// 连接池配置 - 优化大数据量查询性能
	clientOptions.SetMaxPoolSize(100)                  // 最大连接池大小：100个连接
	clientOptions.SetMinPoolSize(10)                   // 最小连接池大小：10个连接
	clientOptions.SetMaxConnIdleTime(30 * time.Minute) // 连接最大空闲时间：30分钟
	clientOptions.SetMaxConnecting(10)                 // 最大并发连接数：10个

	// 超时配置 - 适应大数据量查询
	clientOptions.SetConnectTimeout(30 * time.Second)         // 连接超时：30秒
	clientOptions.SetSocketTimeout(5 * time.Minute)           // Socket超时：5分钟（适应大查询）
	clientOptions.SetServerSelectionTimeout(30 * time.Second) // 服务器选择超时：30秒

	// 心跳和监控配置
	clientOptions.SetHeartbeatInterval(10 * time.Second)   // 心跳间隔：10秒
	clientOptions.SetLocalThreshold(15 * time.Millisecond) // 本地阈值：15毫秒

	// 重试配置
	clientOptions.SetRetryWrites(true) // 启用写重试
	clientOptions.SetRetryReads(true)  // 启用读重试

	// 压缩配置（可选，减少网络传输）
	clientOptions.SetCompressors([]string{"snappy", "zlib"})

	logger.Info("🔧 MongoDB连接池配置:")
	logger.Infof("   📊 最大连接池大小: %d", 100)
	logger.Infof("   📊 最小连接池大小: %d", 10)
	logger.Infof("   ⏱️ 连接超时: %v", 30*time.Second)
	logger.Infof("   ⏱️ Socket超时: %v", 5*time.Minute)
	logger.Infof("   🔄 启用重试: 写=%v, 读=%v", true, true)

	// 建立MongoDB连接
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		logger.Errorf("❌ MongoDB连接失败: %v", err)
		return fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// 测试连接可用性
	if err := client.Ping(ctx, nil); err != nil {
		// 连接失败时清理资源
		client.Disconnect(ctx)
		logger.Errorf("❌ MongoDB Ping测试失败: %v", err)
		return fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	// 保存客户端和数据库实例
	mm.client = client
	mm.database = client.Database(cfg.Database)
	mm.isConnected = true

	logger.Info("✅ MongoDB连接管理器初始化成功")
	return nil
}

/**
 * 获取MongoDB客户端实例
 *
 * 功能：线程安全地获取MongoDB客户端实例，用于执行数据库操作
 *
 * 安全检查：
 * - 检查管理器是否已初始化
 * - 验证连接状态是否正常
 * - 确保客户端实例可用
 *
 * @returns {*mongo.Client} MongoDB客户端实例，未初始化时返回nil
 */
func (mm *MongoDBManager) GetClient() *mongo.Client {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	if !mm.isConnected || mm.client == nil {
		logger.Warn("⚠️ MongoDB客户端未初始化或连接已断开")
		return nil
	}

	return mm.client
}

/**
 * 获取数据库实例
 *
 * 功能：线程安全地获取数据库实例，用于执行集合操作
 *
 * @returns {*mongo.Database} 数据库实例，未初始化时返回nil
 */
func (mm *MongoDBManager) GetDatabase() *mongo.Database {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	if !mm.isConnected || mm.database == nil {
		logger.Warn("⚠️ MongoDB数据库未初始化或连接已断开")
		return nil
	}

	return mm.database
}

/**
 * 检查MongoDB连接状态
 *
 * 功能：线程安全地检查MongoDB连接的当前状态
 *
 * @returns {bool} true表示已连接，false表示未连接
 */
func (mm *MongoDBManager) IsConnected() bool {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()
	return mm.isConnected
}

/**
 * 关闭MongoDB连接
 *
 * 功能：优雅关闭MongoDB连接，清理相关资源
 *
 * 关闭流程：
 * 1. 取消所有正在进行的操作
 * 2. 关闭MongoDB客户端连接
 * 3. 更新连接状态
 * 4. 清理资源引用
 *
 * @returns {error} 关闭过程中的错误信息
 */
func (mm *MongoDBManager) Close() error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	logger.Info("🔌 正在关闭MongoDB连接...")

	// 取消上下文，停止所有操作
	if mm.cancel != nil {
		mm.cancel()
	}

	// 关闭MongoDB客户端
	if mm.client != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		err := mm.client.Disconnect(ctx)
		if err != nil {
			logger.Errorf("❌ 关闭MongoDB客户端时出错: %v", err)
			return err
		}
	}

	mm.isConnected = false
	mm.client = nil
	mm.database = nil
	logger.Info("✅ MongoDB连接已关闭")
	return nil
}
