package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"server-api/services"
	"shared/logger"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证头"})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证头格式"})
			c.Abort()
			return
		}

		// 验证token
		claims, err := authService.ValidateToken(tokenString)
		if err != nil {
			logger.Errorf("Token validation failed: %v", err)
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "无效的认证token", "details": err.<PERSON>rror()})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("permissions", claims.Permissions)

		c.Next()
	}
}

// RequirePermission 权限检查中间件
func RequirePermission(authService *services.AuthService, resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		role, roleExists := c.Get("role")
		permissions, permExists := c.Get("permissions")

		if !roleExists || !permExists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
			c.Abort()
			return
		}

		userRole := role.(string)
		userPermissions := permissions.([]string)

		// 检查权限
		if !authService.CanAccessResource(userRole, userPermissions, resource, action) {
			logger.Errorf("User %s (role: %s) denied access to %s:%s",
				c.GetString("username"), userRole, resource, action)
			c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole 角色检查中间件
func RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
			c.Abort()
			return
		}

		role := userRole.(string)
		for _, requiredRole := range roles {
			if role == requiredRole {
				c.Next()
				return
			}
		}

		logger.Errorf("User %s (role: %s) denied access, required roles: %v",
			c.GetString("username"), role, roles)
		c.JSON(http.StatusForbidden, gin.H{"error": "角色权限不足"})
		c.Abort()
	}
}

// RequireAdmin 管理员权限中间件
func RequireAdmin(authService *services.AuthService) gin.HandlerFunc {
	return RequireRole("admin")
}

// OptionalAuth 可选认证中间件（不强制要求认证）
func OptionalAuth(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.Next()
			return
		}

		// 尝试验证token
		claims, err := authService.ValidateToken(tokenString)
		if err == nil {
			// token有效，设置用户信息
			c.Set("user_id", claims.UserID)
			c.Set("username", claims.Username)
			c.Set("role", claims.Role)
			c.Set("permissions", claims.Permissions)
		}

		c.Next()
	}
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, Cache-Control, Pragma, Expires, X-Requested-With, Accept, Origin, DNT, User-Agent, If-Modified-Since, If-None-Match")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Type")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
		)
	})
}

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return gin.Recovery()
}
