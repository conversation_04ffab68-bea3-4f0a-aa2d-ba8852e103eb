# 站点信息配置环境变量示例
# Site Information Configuration Environment Variables Example

# 站点名称 - 显示在系统标题和页面中
# Site Name - Displayed in system title and pages
SITE_NAME=abc智能制造系统

# 站点Logo URL - 可以是相对路径或完整URL
# Site Logo URL - Can be relative path or full URL
SITE_LOGO=/assets/logo.png

# 技术支持公司名称
# Technical Support Company Name
SITE_SUPPORT=abc科技

# 技术支持联系信息
# Technical Support Contact Information
SITE_SUPPORT_INFO=技术支持: 13800000000

# 使用说明 / Usage Instructions:
# 1. 复制此文件为 .env
#    Copy this file to .env
# 
# 2. 根据实际需求修改环境变量值
#    Modify environment variable values according to actual needs
#
# 3. 如果不设置某个环境变量，系统将使用默认值
#    If an environment variable is not set, the system will use default values
#
# 4. 在Docker环境中，可以通过docker-compose.yml或Dockerfile设置
#    In Docker environment, can be set via docker-compose.yml or Dockerfile
#
# 5. 在生产环境中，建议通过容器编排工具或配置管理系统设置
#    In production, recommend setting via container orchestration tools or config management systems

# Docker Compose 示例 / Docker Compose Example:
# services:
#   server-api:
#     environment:
#       - SITE_NAME=我的智能制造系统
#       - SITE_LOGO=/assets/my-logo.png
#       - SITE_SUPPORT=我的科技公司
#       - SITE_SUPPORT_INFO=技术支持: 400-123-4567

# Kubernetes ConfigMap 示例 / Kubernetes ConfigMap Example:
# apiVersion: v1
# kind: ConfigMap
# metadata:
#   name: site-config
# data:
#   SITE_NAME: "我的智能制造系统"
#   SITE_LOGO: "/assets/my-logo.png"
#   SITE_SUPPORT: "我的科技公司"
#   SITE_SUPPORT_INFO: "技术支持: 400-123-4567"
