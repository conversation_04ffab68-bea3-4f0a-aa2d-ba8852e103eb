{"api": {"baseURL": "http://localhost:9981/api/admin", "timeout": 10000, "retries": 3}, "endpoints": {"devices": {"list": "/devices", "create": "/devices", "get": "/devices/{id}", "update": "/devices/{id}", "delete": "/devices/{id}"}, "products": {"list": "/products", "create": "/products", "get": "/products/{id}", "update": "/products/{id}", "delete": "/products/{id}"}, "processes": {"list": "/processes", "create": "/processes", "get": "/processes/{id}", "update": "/processes/{id}", "delete": "/processes/{id}"}, "data": {"initialize": "/initialize-data", "clear": "/clear-data", "reset": "/reset-data", "statistics": "/statistics"}}, "pagination": {"defaultPageSize": 20, "pageSizeOptions": [10, 20, 50, 100], "showSizeChanger": true, "showQuickJumper": true}, "search": {"debounceTime": 300, "minLength": 1, "placeholder": "搜索..."}, "table": {"defaultSortBy": "created_at", "defaultSortDesc": true, "showRowSelection": true, "showActions": true}, "form": {"validateOnChange": true, "validateOnBlur": true, "autoSave": false}, "ui": {"theme": "light", "primaryColor": "#1890ff", "language": "zh-CN", "dateFormat": "YYYY-MM-DD HH:mm:ss", "numberFormat": {"decimal": 2, "separator": ","}}, "features": {"enableBatchOperations": true, "enableExport": true, "enableImport": true, "enableStatistics": true, "enableRealTimeUpdates": false}, "validation": {"device": {"device_id": {"required": true, "pattern": "^[A-Z0-9_]+$", "maxLength": 50, "message": "设备编号只能包含大写字母、数字和下划线"}, "name": {"required": true, "maxLength": 100, "message": "设备名称不能超过100个字符"}, "model": {"required": true, "maxLength": 100}, "location": {"required": true, "maxLength": 200}}, "product": {"product_id": {"required": true, "pattern": "^[A-Z0-9_]+$", "maxLength": 50}, "name": {"required": true, "maxLength": 100}, "category": {"required": true, "maxLength": 50}, "unit": {"required": true, "maxLength": 20}}, "process": {"process_id": {"required": true, "pattern": "^[A-Z0-9_]+$", "maxLength": 50}, "name": {"required": true, "maxLength": 100}, "duration": {"required": true, "min": 1, "max": 10080, "message": "工序时长必须在1-10080分钟之间"}, "sequence": {"required": true, "min": 1, "max": 999}}}, "options": {"deviceStatus": [{"value": "active", "label": "运行中", "color": "green"}, {"value": "inactive", "label": "停机", "color": "red"}, {"value": "maintenance", "label": "维护中", "color": "orange"}], "productCategories": ["汽车零部件", "电子产品", "机械零件", "金属制品", "塑料制品", "其他"], "deviceTypes": ["数控机床", "激光切割机", "折弯机", "冲压机", "齿轮加工机", "热处理炉", "工业机器人", "其他"], "units": ["件", "个", "套", "台", "米", "千克", "升", "其他"]}, "messages": {"success": {"create": "创建成功", "update": "更新成功", "delete": "删除成功", "initialize": "数据初始化成功", "clear": "数据清空成功", "reset": "数据重置成功"}, "error": {"network": "网络连接失败，请检查网络设置", "server": "服务器错误，请稍后重试", "validation": "数据验证失败，请检查输入", "notFound": "数据不存在", "duplicate": "数据已存在"}, "confirm": {"delete": "确定要删除这条记录吗？", "clear": "确定要清空所有数据吗？此操作不可恢复！", "reset": "确定要重置数据吗？这将清空现有数据并重新初始化示例数据！"}}, "charts": {"colors": ["#1890ff", "#52c41a", "#faad14", "#f5222d", "#722ed1", "#13c2c2"], "defaultType": "bar", "animation": true, "responsive": true}}