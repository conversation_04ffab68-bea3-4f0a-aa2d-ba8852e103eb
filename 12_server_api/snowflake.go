/**
 * Sony Snowflake ID生成器 - 12_server_api版本
 *
 * 功能概述：
 * 本模块为12_server_api服务提供Sony Snowflake分布式唯一ID生成功能
 * 与02_generate_data保持配置字段同步，确保整个系统的ID生成一致性
 * 
 * 技术特性：
 * - 分布式唯一性：在分布式环境中保证ID的全局唯一性
 * - 时间有序性：生成的ID按时间顺序递增，便于排序和索引
 * - 高性能：支持高并发ID生成，性能优异
 * - 可配置性：支持机器ID、起始时间等参数配置
 * - 容错性：网络分区或时钟回拨时的容错处理
 *
 * Sony Snowflake算法优势：
 * - 相比Twitter Snowflake，Sony Snowflake对时钟回拨更加宽容
 * - 支持更灵活的机器ID分配策略
 * - 更好的性能表现和内存使用效率
 * - 企业级稳定性和可靠性保证
 *
 * ID结构（64位）：
 * - 1位：符号位（固定为0）
 * - 39位：时间戳（毫秒级，可使用约17年）
 * - 8位：机器ID（支持256台机器）
 * - 16位：序列号（每毫秒可生成65536个ID）
 *
 * 使用场景：
 * - 管理系统中的数据记录唯一标识
 * - 订单号、任务号等业务ID生成
 * - 日志记录的追踪ID
 * - 与02_generate_data的ID生成保持一致性
 *
 * @package main
 * <AUTHOR> System
 * @version 1.0.0
 */

package main

import (
	"fmt"
	"net"
	"strconv"
	"sync"
	"time"

	"github.com/sony/sonyflake"
	"shared/logger"
)

/**
 * 雪花ID生成器管理器
 *
 * 功能：管理Sony Snowflake实例的生命周期和配置
 *
 * 设计原则：
 * - 单例模式：全局唯一的Snowflake实例
 * - 线程安全：支持并发访问和ID生成
 * - 配置同步：与02_generate_data保持配置一致性
 * - 错误处理：完善的错误处理和日志记录
 * - 性能优化：高效的ID生成和缓存机制
 *
 * @struct SnowflakeManager
 */
type SnowflakeManager struct {
	/** Sony Snowflake实例，用于生成分布式唯一ID */
	snowflake *sonyflake.Sonyflake

	/** 读写锁，保护Snowflake实例的并发访问 */
	mutex sync.RWMutex

	/** 机器ID，用于区分不同的ID生成节点 */
	machineID uint16

	/** 是否已初始化标志 */
	initialized bool
}

// 全局Snowflake管理器实例
var (
	/** 全局雪花ID管理器实例，采用单例模式 */
	globalSnowflakeManager *SnowflakeManager

	/** 初始化锁，确保单例模式的线程安全 */
	initOnce sync.Once
)

/**
 * 获取全局雪花ID管理器实例
 *
 * 功能：返回全局唯一的SnowflakeManager实例，采用单例模式
 *
 * 实现特性：
 * - 单例模式：使用sync.Once确保只初始化一次
 * - 线程安全：支持并发调用
 * - 延迟初始化：首次调用时才进行初始化
 * - 错误处理：初始化失败时返回nil
 *
 * 返回值：
 * - *SnowflakeManager: 雪花ID管理器实例，失败时返回nil
 *
 * 使用示例：
 *   manager := GetSnowflakeManager()
 *   if manager != nil {
 *       id := manager.GenerateID()
 *   }
 */
func GetSnowflakeManager() *SnowflakeManager {
	initOnce.Do(func() {
		globalSnowflakeManager = &SnowflakeManager{}
		if err := globalSnowflakeManager.Initialize(); err != nil {
			logger.Errorf("❌ Failed to initialize Snowflake manager: %v", err)
			globalSnowflakeManager = nil
		}
	})
	return globalSnowflakeManager
}

/**
 * 初始化雪花ID管理器
 *
 * 功能：配置和初始化Sony Snowflake实例
 *
 * 初始化步骤：
 * 1. 获取机器ID（基于网络接口或配置）
 * 2. 配置Snowflake参数（起始时间、机器ID等）
 * 3. 创建Snowflake实例
 * 4. 验证ID生成功能
 * 5. 记录初始化状态
 *
 * 配置参数：
 * - StartTime: 起始时间，用于计算时间戳偏移
 * - MachineID: 机器标识函数，返回当前节点的唯一ID
 * - CheckMachineID: 机器ID验证函数，确保ID的唯一性
 *
 * 错误处理：
 * - 网络接口获取失败：使用默认机器ID
 * - Snowflake创建失败：返回详细错误信息
 * - ID生成测试失败：返回验证错误
 *
 * 返回值：
 * - error: 初始化成功返回nil，失败返回错误信息
 */
func (sm *SnowflakeManager) Initialize() error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if sm.initialized {
		return nil
	}

	// 获取机器ID（与02_generate_data使用相同的算法）
	machineID, err := sm.getMachineID()
	if err != nil {
		logger.Warnf("⚠️  Failed to get machine ID, using default: %v", err)
		machineID = 2 // 使用不同的默认机器ID以区分服务
	}
	sm.machineID = machineID

	// 配置Sony Snowflake（与02_generate_data保持一致）
	settings := sonyflake.Settings{
		StartTime: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC), // 设置起始时间为2024年1月1日
		MachineID: func() (uint16, error) {
			return sm.machineID, nil
		},
		CheckMachineID: func(machineID uint16) bool {
			// 验证机器ID的有效性（0-255范围内）
			return machineID < 256
		},
	}

	// 创建Snowflake实例
	sm.snowflake = sonyflake.NewSonyflake(settings)
	if sm.snowflake == nil {
		return fmt.Errorf("failed to create Sony Snowflake instance")
	}

	// 测试ID生成功能
	testID, err := sm.snowflake.NextID()
	if err != nil {
		return fmt.Errorf("failed to generate test ID: %w", err)
	}

	sm.initialized = true

	logger.Infof("✅ Sony Snowflake initialized successfully (12_server_api)")
	logger.Infof("   🔧 Machine ID: %d", sm.machineID)
	logger.Infof("   🆔 Test ID: %d", testID)
	logger.Infof("   📅 Start Time: 2024-01-01 00:00:00 UTC")

	return nil
}

/**
 * 生成雪花ID
 *
 * 功能：生成分布式唯一的雪花ID字符串
 *
 * 实现特性：
 * - 线程安全：使用读锁保护并发访问
 * - 错误处理：生成失败时返回错误信息
 * - 格式转换：将uint64 ID转换为字符串格式
 * - 性能优化：使用读锁减少锁竞争
 *
 * ID特性：
 * - 全局唯一：在分布式环境中保证唯一性
 * - 时间有序：按生成时间递增排序
 * - 高性能：支持高并发生成
 * - 可追溯：可从ID中提取时间戳信息
 *
 * 返回值：
 * - string: 雪花ID字符串，如"1234567890123456789"
 * - error: 生成成功返回nil，失败返回错误信息
 *
 * 使用示例：
 *   id, err := manager.GenerateID()
 *   if err != nil {
 *       log.Printf("Failed to generate ID: %v", err)
 *   } else {
 *       log.Printf("Generated ID: %s", id)
 *   }
 */
func (sm *SnowflakeManager) GenerateID() (string, error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	if !sm.initialized || sm.snowflake == nil {
		return "", fmt.Errorf("Snowflake manager not initialized")
	}

	id, err := sm.snowflake.NextID()
	if err != nil {
		return "", fmt.Errorf("failed to generate Snowflake ID: %w", err)
	}

	return strconv.FormatUint(id, 10), nil
}

/**
 * 获取机器ID
 *
 * 功能：基于网络接口MAC地址生成唯一的机器标识
 * 与02_generate_data使用相同的算法，但使用不同的偏移量以区分服务
 *
 * 生成策略：
 * 1. 获取所有网络接口
 * 2. 过滤掉回环接口和虚拟接口
 * 3. 选择第一个有效的物理网络接口
 * 4. 基于MAC地址计算机器ID
 * 5. 确保ID在有效范围内（0-255）
 *
 * 计算方法：
 * - 使用MAC地址的最后两个字节
 * - 通过异或运算生成8位机器ID
 * - 添加服务偏移量以区分不同服务
 * - 确保在0-255范围内的唯一性
 *
 * 容错处理：
 * - 无网络接口：返回默认ID
 * - MAC地址获取失败：使用接口索引
 * - 计算异常：返回错误信息
 *
 * 返回值：
 * - uint16: 机器ID（0-255范围）
 * - error: 获取成功返回nil，失败返回错误信息
 */
func (sm *SnowflakeManager) getMachineID() (uint16, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return 0, fmt.Errorf("failed to get network interfaces: %w", err)
	}

	for _, iface := range interfaces {
		// 跳过回环接口和未启用的接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 跳过虚拟接口（通常MAC地址为空或特殊值）
		if len(iface.HardwareAddr) == 0 {
			continue
		}

		// 基于MAC地址生成机器ID
		mac := iface.HardwareAddr
		if len(mac) >= 6 {
			// 使用MAC地址的最后两个字节生成机器ID
			machineID := uint16(mac[4])<<8 | uint16(mac[5])
			// 添加服务偏移量（12_server_api使用偏移量100）
			machineID = (machineID + 100) % 256

			logger.Debugf("🔧 Generated machine ID %d from MAC address %s (interface: %s, service: 12_server_api)",
				machineID, mac.String(), iface.Name)
			return machineID, nil
		}
	}

	// 如果没有找到合适的网络接口，使用默认值
	return 2, fmt.Errorf("no suitable network interface found")
}

/**
 * 获取机器ID信息
 *
 * 功能：返回当前使用的机器ID，用于调试和监控
 *
 * 返回值：
 * - uint16: 当前机器ID
 * - bool: 是否已初始化
 */
func (sm *SnowflakeManager) GetMachineID() (uint16, bool) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.machineID, sm.initialized
}

/**
 * 生成雪花ID的便捷函数
 *
 * 功能：提供全局访问的雪花ID生成函数
 *
 * 实现：
 * - 获取全局Snowflake管理器实例
 * - 调用实例的GenerateID方法
 * - 处理初始化失败的情况
 *
 * 返回值：
 * - string: 雪花ID字符串
 * - error: 生成失败时的错误信息
 *
 * 使用示例：
 *   id, err := GenerateSnowflakeID()
 *   if err != nil {
 *       log.Printf("Failed to generate ID: %v", err)
 *   }
 */
func GenerateSnowflakeID() (string, error) {
	manager := GetSnowflakeManager()
	if manager == nil {
		return "", fmt.Errorf("Snowflake manager not available")
	}
	return manager.GenerateID()
}
