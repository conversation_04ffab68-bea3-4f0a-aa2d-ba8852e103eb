/**
 * 设备利用率数据模型定义
 *
 * 功能概述：
 * 本模块定义了制造业数据采集系统中所有与设备利用率仪表盘相关的数据结构，
 * 包括利用率概览、设备排名、班次分析、趋势统计等核心数据模型
 *
 * 主要数据模型：
 * - 利用率概览：UtilizationOverview, UtilizationZone
 * - 设备排名：DeviceUtilizationRanking, DeviceUtilization
 * - 班次分析：ShiftUtilizationRanking, ShiftUtilization
 * - 趋势分析：DailyUtilizationTrend, ShiftUtilizationTrend
 * - 状态构成：StatusComposition
 *
 * 设计原则：
 * - 数据完整性：包含所有必要的利用率维度和元数据
 * - 类型安全：使用强类型定义，避免运行时错误
 * - 序列化友好：支持JSON序列化，便于API数据传输
 * - 业务语义：字段命名清晰，符合制造业业务术语
 * - 可视化友好：数据结构适合前端图表组件直接使用
 *
 * 数据标准化：
 * - 时间单位：统一使用秒作为时间统计单位
 * - 百分比：利用率使用0-100的浮点数表示
 * - 日期格式：统一使用YYYY-MM-DD格式
 * - 时间格式：班次时间使用HH:MM格式
 * - 状态枚举：production、idle、fault、adjusting、shutdown、disconnected
 *
 * 业务规则：
 * - 利用率计算：支持OP1(24小时)和OP2(24小时-休息时间)两种模式
 * - 班次划分：基于工作时间设置的班次配置进行数据分组
 * - 状态映射：将原始设备状态映射为标准化的利用率状态
 * - 时间范围：支持跨天查询和多日趋势分析
 *
 * @package models
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-12
 */
package models

import "time"

// UtilizationOverview 设备利用率概览
type UtilizationOverview struct {
	Date                string                `json:"date"`                  // 统计日期
	OverallUtilization  float64               `json:"overall_utilization"`  // 总体利用率百分比
	TotalDevices        int                   `json:"total_devices"`         // 设备总数
	UtilizationZones    []UtilizationZone     `json:"utilization_zones"`     // 利用率区间统计
	DeviceUtilizations  []DeviceUtilization   `json:"device_utilizations"`   // 各设备利用率详情
	WorkTimeSettings    WorkTimeSettings      `json:"work_time_settings"`    // 工作时间设置
	CalculationMode     string                `json:"calculation_mode"`      // 计算模式 OP1/OP2
	WorkingHours        float64               `json:"working_hours"`         // 工作时长（小时）
	LastUpdated         time.Time             `json:"last_updated"`          // 最后更新时间
}

// UtilizationZone 利用率区间统计
type UtilizationZone struct {
	Name        string  `json:"name"`         // 区间名称，如"高利用率"
	MinRate     float64 `json:"min_rate"`     // 最小利用率
	MaxRate     float64 `json:"max_rate"`     // 最大利用率
	DeviceCount int     `json:"device_count"` // 该区间设备数量
	Percentage  float64 `json:"percentage"`   // 占总设备数的百分比
	Color       string  `json:"color"`        // 显示颜色
}

// DeviceUtilizationRanking 各设备利用率排名
type DeviceUtilizationRanking struct {
	Date             string              `json:"date"`               // 统计日期
	Devices          []DeviceUtilization `json:"devices"`            // 设备利用率列表（按利用率排序）
	StatusLegend     []StatusLegendItem  `json:"status_legend"`      // 状态图例
	WorkTimeSettings WorkTimeSettings    `json:"work_time_settings"` // 工作时间设置
	LastUpdated      time.Time           `json:"last_updated"`       // 最后更新时间
}

// DeviceUtilization 设备利用率详情
type DeviceUtilization struct {
	DeviceID          string            `json:"device_id"`           // 设备ID
	DeviceName        string            `json:"device_name"`         // 设备名称
	DeviceCode        string            `json:"device_code"`         // 设备编码
	Location          string            `json:"location"`            // 设备位置
	Brand             string            `json:"brand"`               // 设备品牌
	Model             string            `json:"model"`               // 设备型号
	UtilizationRate   float64           `json:"utilization_rate"`    // 利用率百分比
	StatusComposition StatusComposition `json:"status_composition"`  // 状态构成
	TotalWorkingTime  float64           `json:"total_working_time"`  // 总工作时间（小时）
	ProductiveTime    float64           `json:"productive_time"`     // 生产时间（小时）
	IdleTime          float64           `json:"idle_time"`           // 空闲时间（小时）
	FaultTime         float64           `json:"fault_time"`          // 故障时间（小时）
	Rank              int               `json:"rank"`                // 排名
}

// StatusComposition 状态构成
type StatusComposition struct {
	Production   StatusDetail `json:"production"`   // 生产状态
	Idle         StatusDetail `json:"idle"`         // 空闲状态
	Fault        StatusDetail `json:"fault"`        // 故障状态
	Adjusting    StatusDetail `json:"adjusting"`    // 调机状态
	Shutdown     StatusDetail `json:"shutdown"`     // 停机状态
	Disconnected StatusDetail `json:"disconnected"` // 断线状态
}

// StatusDetail 状态详情
type StatusDetail struct {
	Duration   float64 `json:"duration"`   // 持续时间（小时）
	Percentage float64 `json:"percentage"` // 占比百分比
	Color      string  `json:"color"`      // 显示颜色
}

// StatusLegendItem 状态图例项
type StatusLegendItem struct {
	Status string `json:"status"` // 状态名称
	Label  string `json:"label"`  // 显示标签
	Color  string `json:"color"`  // 颜色
}

// ShiftUtilizationRanking 各设备班次利用率排名
type ShiftUtilizationRanking struct {
	Date             string               `json:"date"`               // 统计日期
	Devices          []ShiftUtilization   `json:"devices"`            // 设备班次利用率列表
	Shifts           []ShiftInfo          `json:"shifts"`             // 班次信息
	StatusLegend     []StatusLegendItem   `json:"status_legend"`      // 状态图例
	WorkTimeSettings WorkTimeSettings     `json:"work_time_settings"` // 工作时间设置
	LastUpdated      time.Time            `json:"last_updated"`       // 最后更新时间
}

// ShiftUtilization 设备班次利用率
type ShiftUtilization struct {
	DeviceID     string                           `json:"device_id"`      // 设备ID
	DeviceName   string                           `json:"device_name"`    // 设备名称
	DeviceCode   string                           `json:"device_code"`    // 设备编码
	Location     string                           `json:"location"`       // 设备位置
	Brand        string                           `json:"brand"`          // 设备品牌
	Model        string                           `json:"model"`          // 设备型号
	ShiftDetails map[string]ShiftUtilizationDetail `json:"shift_details"` // 各班次详情
	OverallRate  float64                          `json:"overall_rate"`   // 总体利用率
	Rank         int                              `json:"rank"`           // 排名
}

// ShiftUtilizationDetail 班次利用率详情
type ShiftUtilizationDetail struct {
	ShiftName         string            `json:"shift_name"`          // 班次名称
	StartTime         string            `json:"start_time"`          // 开始时间
	EndTime           string            `json:"end_time"`            // 结束时间
	UtilizationRate   float64           `json:"utilization_rate"`    // 利用率百分比
	StatusComposition StatusComposition `json:"status_composition"`  // 状态构成
	WorkingHours      float64           `json:"working_hours"`       // 工作时长（小时）
	ProductiveTime    float64           `json:"productive_time"`     // 生产时间（小时）
}

// ShiftInfo 班次信息
type ShiftInfo struct {
	Name      string `json:"name"`       // 班次名称
	StartTime string `json:"start_time"` // 开始时间
	EndTime   string `json:"end_time"`   // 结束时间
	Enabled   bool   `json:"enabled"`    // 是否启用
	Color     string `json:"color"`      // 显示颜色
}

// DailyUtilizationTrend 每日利用率趋势
type DailyUtilizationTrend struct {
	StartDate        string                    `json:"start_date"`         // 开始日期
	EndDate          string                    `json:"end_date"`           // 结束日期
	DailyData        []DailyUtilizationData    `json:"daily_data"`         // 每日数据
	AverageRate      float64                   `json:"average_rate"`       // 平均利用率
	MaxRate          float64                   `json:"max_rate"`           // 最高利用率
	MinRate          float64                   `json:"min_rate"`           // 最低利用率
	TrendDirection   string                    `json:"trend_direction"`    // 趋势方向 up/down/stable
	WorkTimeSettings WorkTimeSettings          `json:"work_time_settings"` // 工作时间设置
	LastUpdated      time.Time                 `json:"last_updated"`       // 最后更新时间
}

// DailyUtilizationData 每日利用率数据
type DailyUtilizationData struct {
	Date            string  `json:"date"`             // 日期
	UtilizationRate float64 `json:"utilization_rate"` // 利用率百分比
	TotalDevices    int     `json:"total_devices"`    // 设备总数
	ActiveDevices   int     `json:"active_devices"`   // 活跃设备数
	WorkingHours    float64 `json:"working_hours"`    // 工作时长（小时）
	ProductiveHours float64 `json:"productive_hours"` // 生产时长（小时）
}

// ShiftUtilizationTrend 每日班次利用率趋势
type ShiftUtilizationTrend struct {
	StartDate        string                        `json:"start_date"`         // 开始日期
	EndDate          string                        `json:"end_date"`           // 结束日期
	DailyShiftData   []DailyShiftUtilizationData   `json:"daily_shift_data"`   // 每日班次数据
	ShiftAverages    map[string]float64            `json:"shift_averages"`     // 各班次平均利用率
	Shifts           []ShiftInfo                   `json:"shifts"`             // 班次信息
	WorkTimeSettings WorkTimeSettings              `json:"work_time_settings"` // 工作时间设置
	LastUpdated      time.Time                     `json:"last_updated"`       // 最后更新时间
}

// DailyShiftUtilizationData 每日班次利用率数据
type DailyShiftUtilizationData struct {
	Date       string                     `json:"date"`        // 日期
	ShiftRates map[string]float64         `json:"shift_rates"` // 各班次利用率
	ShiftData  map[string]ShiftDailyData  `json:"shift_data"`  // 各班次详细数据
}

// ShiftDailyData 班次每日数据
type ShiftDailyData struct {
	UtilizationRate float64 `json:"utilization_rate"` // 利用率百分比
	WorkingHours    float64 `json:"working_hours"`    // 工作时长（小时）
	ProductiveHours float64 `json:"productive_hours"` // 生产时长（小时）
	ActiveDevices   int     `json:"active_devices"`   // 活跃设备数
}
