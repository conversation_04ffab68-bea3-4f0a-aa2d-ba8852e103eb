/**
 * 设备数据模型定义
 *
 * 功能概述：
 * 本模块定义了制造业数据采集系统中所有与设备相关的数据结构，
 * 包括设备状态、产量统计、程序信息、历史记录等核心数据模型
 *
 * 主要数据模型：
 * - 设备状态：实时设备运行状态、产量、程序等信息
 * - 产量数据：设备生产统计、计划完成情况
 * - 程序信息：设备当前运行的加工程序
 * - 状态历史：设备状态变化的历史记录
 * - 生产进度：订单执行进度和预计完成时间
 *
 * 数据存储适配：
 * - InfluxDB：时序数据存储，支持高频数据写入和查询
 * - Redis：缓存数据存储，提供毫秒级数据访问
 * - MongoDB：业务数据存储，支持复杂查询和聚合
 * - JSON：API数据交换格式，前后端数据传输
 *
 * 兼容性设计：
 * - 与31_machine_backend保持100%API兼容
 * - 与30_machine_status前端保持数据格式兼容
 * - 支持fanuc_v2等数据采集服务的配置格式
 * - 标准化的JSON序列化和反序列化
 *
 * 数据标准化：
 * - 统一的时间戳格式：RFC3339
 * - 标准化的状态枚举：production、idle、fault等
 * - 一致的字段命名：snake_case for JSON, camelCase for frontend
 * - 完整的数据验证：必填字段、数据类型、取值范围
 *
 * 性能优化：
 * - 结构体标签：支持多种序列化格式
 * - 缓存友好：支持Redis的JSON序列化
 * - 查询优化：支持InfluxDB的标签和字段分离
 * - 内存效率：合理的数据类型选择和结构设计
 *
 * 业务场景：
 * - 实时监控：设备状态的实时展示和报警
 * - 生产统计：产量数据的统计和分析
 * - 历史分析：设备运行历史的趋势分析
 * - 进度跟踪：生产订单的执行进度监控
 *
 * @package models
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package models

import (
	"encoding/json"
	"time"
)

/**
 * 设备状态数据结构
 *
 * 功能：存储设备的实时状态信息，是系统中最核心的数据模型
 *
 * 数据来源：
 * - InfluxDB：从sensor_data表查询的原始时序数据
 * - Redis：缓存的实时状态数据，提供快速访问
 * - 数据采集：fanuc_v2、02_generate_data等采集服务
 *
 * 状态枚举：
 * - production：生产中，设备正在加工产品
 * - idle：空闲，设备待机状态
 * - fault：故障，设备出现异常需要处理
 * - adjusting：调机，设备正在调试或维护
 * - shutdown：停机，设备已关闭
 * - disconnected：断线，设备通信中断
 *
 * 数据用途：
 * - 实时监控：设备状态大屏显示
 * - 统计分析：设备利用率计算
 * - 报警处理：异常状态的识别和通知
 * - 历史记录：状态变化的历史追踪
 *
 * 兼容性：
 * - 与31_machine_backend的machines接口完全兼容
 * - 支持30_machine_status前端的数据格式要求
 * - 适配InfluxDB的标签和字段结构
 *
 * @struct DeviceStatus
 */
type DeviceStatus struct {
	/** 设备唯一标识符，如"fanuc_001"、"1111"等，用于系统内部识别 */
	DeviceID string `json:"device_id" influx:"device_id"`

	/** 设备业务编码，如"XCY0001"，用于业务层面的设备标识 */
	DeviceCode string `json:"device_code" influx:"device_code"`

	/** 设备显示名称，如"FANUC CNC机床#1"，用于用户界面显示 */
	DeviceName string `json:"device_name" influx:"device_name"`

	/** 设备物理位置，如"A区"、"生产线1"，便于现场定位 */
	Location string `json:"location" influx:"location"`

	/** 设备型号规格，如"FANUC-01i"、"HXM-530"，技术参考信息 */
	Model string `json:"model" influx:"model"`

	/** 设备数据类型，如"fanuc_30i"、"siemens_840d"，用于数据分类和处理 */
	DataType string `json:"data_type" influx:"data_type"`

	/** 设备当前运行状态，枚举值见上述状态说明 */
	Status string `json:"status" influx:"status"`

	/** 当前运行的加工程序名称，如"PART001.NC" */
	Program string `json:"program" influx:"program"`

	/** 当前累计产量，实时更新的生产数量 */
	Production int `json:"production" influx:"production"`

	/** 计划产量目标，用于计算完成率和进度 */
	Plan int `json:"plan" influx:"plan"`

	/** 数据采集时间戳，记录数据的实际产生时间 */
	Timestamp time.Time `json:"timestamp" influx:"timestamp"`

	/** 数据更新时间，记录数据在系统中的最后更新时间 */
	UpdatedAt time.Time `json:"updated_at"`
}

// DeviceProduction 设备产量数据结构
// 用于存储设备的产量统计信息，支持按时间聚合
type DeviceProduction struct {
	DeviceID     string     `json:"device_id" influx:"device_id"`         // 设备ID
	DeviceCode   string     `json:"device_code" influx:"device_code"`     // 设备编码
	DeviceName   string     `json:"device_name" influx:"device_name"`     // 设备名称
	ProductID    string     `json:"product_id" influx:"product_id"`       // 产品ID
	ProductName  string     `json:"product_name" influx:"product_name"`   // 产品名称
	ProcessCode  string     `json:"process_code" influx:"process_code"`   // 工序代码
	ProcessName  string     `json:"process_name" influx:"process_name"`   // 工序名称
	Program      string     `json:"program" influx:"program"`             // 加工程序
	Quantity     int        `json:"quantity" influx:"quantity"`           // 产量数量
	PlanQuantity int        `json:"plan_quantity" influx:"plan_quantity"` // 计划产量
	StartTime    time.Time  `json:"start_time" influx:"start_time"`       // 开始时间
	EndTime      *time.Time `json:"end_time" influx:"end_time"`           // 结束时间（可为空）
	Date         string     `json:"date" influx:"date"`                   // 日期，格式：2025-06-02
	Hour         int        `json:"hour" influx:"hour"`                   // 小时，0-23
	Timestamp    time.Time  `json:"timestamp" influx:"timestamp"`         // 数据时间戳
}

// DeviceProgram 设备程序数据结构
// 用于存储设备当前运行的程序信息
type DeviceProgram struct {
	DeviceID    string     `json:"device_id" influx:"device_id"`       // 设备ID
	DeviceCode  string     `json:"device_code" influx:"device_code"`   // 设备编码
	DeviceName  string     `json:"device_name" influx:"device_name"`   // 设备名称
	ProgramID   string     `json:"program_id" influx:"program_id"`     // 程序ID
	ProgramName string     `json:"program_name" influx:"program_name"` // 程序名称
	ProgramPath string     `json:"program_path" influx:"program_path"` // 程序路径
	ProductID   string     `json:"product_id" influx:"product_id"`     // 关联产品ID
	ProductName string     `json:"product_name" influx:"product_name"` // 关联产品名称
	ProcessCode string     `json:"process_code" influx:"process_code"` // 工序代码
	ProcessName string     `json:"process_name" influx:"process_name"` // 工序名称
	IsActive    bool       `json:"is_active" influx:"is_active"`       // 是否为当前活动程序
	StartTime   time.Time  `json:"start_time" influx:"start_time"`     // 程序开始时间
	EndTime     *time.Time `json:"end_time" influx:"end_time"`         // 程序结束时间（可为空）
	Timestamp   time.Time  `json:"timestamp" influx:"timestamp"`       // 数据时间戳
}

// DeviceStatusHistory 设备状态历史数据结构
// 用于存储设备状态变化的历史记录
type DeviceStatusHistory struct {
	DeviceID      string    `json:"device_id" influx:"device_id"`           // 设备ID
	DeviceCode    string    `json:"device_code" influx:"device_code"`       // 设备编码
	DeviceName    string    `json:"device_name" influx:"device_name"`       // 设备名称
	PrevStatus    string    `json:"prev_status" influx:"prev_status"`       // 前一个状态
	CurrentStatus string    `json:"current_status" influx:"current_status"` // 当前状态
	Duration      int64     `json:"duration" influx:"duration"`             // 状态持续时间（秒）
	StartTime     time.Time `json:"start_time" influx:"start_time"`         // 状态开始时间
	EndTime       time.Time `json:"end_time" influx:"end_time"`             // 状态结束时间
	Date          string    `json:"date" influx:"date"`                     // 日期，格式：2025-06-02
	Hour          int       `json:"hour" influx:"hour"`                     // 小时，0-23
	Timestamp     time.Time `json:"timestamp" influx:"timestamp"`           // 数据时间戳

	// 前一个状态的详细时间信息
	PrevStartTime time.Time `json:"prev_start_time" influx:"prev_start_time"` // 前一个状态的开始时间
	PrevEndTime   time.Time `json:"prev_end_time" influx:"prev_end_time"`     // 前一个状态的结束时间
	//PrevDuration  int64     `json:"prev_duration" influx:"prev_duration"`     // 前一个状态的持续时间（秒）
}

// TimeSlotStatusRecord 时间段内的状态记录（前端渲染用）
// 用于前端直接渲染，包含预计算的位置和样式信息
type TimeSlotStatusRecord struct {
	ID                string  `json:"id"`                 // 唯一标识
	DeviceID          string  `json:"device_id"`          // 设备ID
	Status            string  `json:"status"`             // 当前状态
	StartTime         string  `json:"start_time"`         // 开始时间（北京时间字符串）
	EndTime           string  `json:"end_time"`           // 结束时间（北京时间字符串）
	Duration          int64   `json:"duration"`           // 持续时间（秒）
	Position          float64 `json:"position"`           // 在时间段中的位置（百分比）
	Width             float64 `json:"width"`              // 宽度（百分比）
	Tooltip           string  `json:"tooltip"`            // 预生成的tooltip文本
	OriginalStart     string  `json:"original_start"`     // 原始开始时间（用于跨时间段的记录）
	OriginalEnd       string  `json:"original_end"`       // 原始结束时间（用于跨时间段的记录）
	OriginalTimestamp string  `json:"original_timestamp"` // InfluxDB原始时间戳（UTC格式）
	IsSegmented       bool    `json:"is_segmented"`       // 是否为分割的记录

	// 前一个状态的相关信息
	PrevStatus    string `json:"prev_status"`     // 前一个状态
	PrevStartTime string `json:"prev_start_time"` // 前一个状态的开始时间（北京时间字符串）
	PrevEndTime   string `json:"prev_end_time"`   // 前一个状态的结束时间（北京时间字符串）
	PrevDuration  int64  `json:"prev_duration"`   // 前一个状态的持续时间（秒）
}

// TimeSlotData 时间段数据
// 包含时间段信息和该时间段内的所有状态记录
type TimeSlotData struct {
	Label      string                 `json:"label"`       // 时间段标签，如 "09:00-11:00"
	Start      string                 `json:"start"`       // 开始时间，如 "09:00"（保持向后兼容）
	End        string                 `json:"end"`         // 结束时间，如 "11:00"（保持向后兼容）
	StartLocal string                 `json:"start_local"` // 本地时区开始时间，如 "09:00"
	EndLocal   string                 `json:"end_local"`   // 本地时区结束时间，如 "11:00"
	StartUTC   string                 `json:"start_utc"`   // 开始的UTC时间，RFC3339格式
	EndUTC     string                 `json:"end_utc"`     // 结束的UTC时间，RFC3339格式
	Records    []TimeSlotStatusRecord `json:"records"`     // 该时间段内的状态记录
}

// DeviceStatusHistoryResponse 设备状态历史响应（新版本，支持时间段预处理）
// 用于返回按时间段预处理的设备状态历史数据
type DeviceStatusHistoryResponse struct {
	DeviceInfo map[string]interface{} `json:"device_info"` // 设备信息
	TimeSlots  []TimeSlotData         `json:"time_slots"`  // 按时间段预处理的数据
	Date       string                 `json:"date"`        // 查询日期
	Total      int                    `json:"total"`       // 总记录数
}

// DeviceStatusHistoryMongo MongoDB中存储的设备状态历史数据结构
// 用于存储已合并的历史状态数据，提高查询性能
type DeviceStatusHistoryMongo struct {
	ID            string                `bson:"_id,omitempty" json:"id"`              // MongoDB文档ID
	DeviceID      string                `bson:"device_id" json:"device_id"`           // 设备ID
	Date          string                `bson:"date" json:"date"`                     // 日期，格式：2025-06-02
	StatusHistory []DeviceStatusHistory `bson:"status_history" json:"status_history"` // 状态历史记录列表
	CreatedAt     time.Time             `bson:"created_at" json:"created_at"`         // 创建时间
	UpdatedAt     time.Time             `bson:"updated_at" json:"updated_at"`         // 更新时间
}

// ProductionProgress 生产进度数据结构
// 用于前端显示生产进度信息
type ProductionProgress struct {
	DeviceID     string     `json:"device_id"`     // 设备ID
	DeviceCode   string     `json:"device_code"`   // 设备编码
	DeviceName   string     `json:"device_name"`   // 设备名称
	ProductID    string     `json:"product_id"`    // 产品ID
	ProductName  string     `json:"product_name"`  // 产品名称
	OrderID      string     `json:"order_id"`      // 订单ID
	WorkOrder    string     `json:"work_order"`    // 工单号
	Status       string     `json:"status"`        // 设备状态
	CurrentQty   int        `json:"current_qty"`   // 当前产量
	PlanQty      int        `json:"plan_qty"`      // 计划产量
	Progress     float64    `json:"progress"`      // 完成进度百分比
	StartTime    time.Time  `json:"start_time"`    // 开始时间
	EstimatedEnd time.Time  `json:"estimated_end"` // 预计完成时间
	ActualEnd    *time.Time `json:"actual_end"`    // 实际完成时间
	Date         string     `json:"date"`          // 日期
	LastUpdate   time.Time  `json:"last_update"`   // 最后更新时间
}

// CompanyInfo 公司信息数据结构
// 用于API响应中的公司信息
type CompanyInfo struct {
	Name     string `json:"name"`     // 公司名称
	DateTime string `json:"dateTime"` // 当前时间
}

// StatusCounts 状态统计数据结构
// 用于统计各种设备状态的数量
type StatusCounts struct {
	Production   int `json:"production"`   // 生产中设备数量
	Idle         int `json:"idle"`         // 空闲设备数量
	Fault        int `json:"fault"`        // 故障设备数量
	Adjusting    int `json:"adjusting"`    // 调试中设备数量
	Shutdown     int `json:"shutdown"`     // 停机设备数量
	Disconnected int `json:"disconnected"` // 断线设备数量
}

// MachinesResponse 设备列表API响应结构
// 与31_machine_backend保持100%兼容的响应格式
type MachinesResponse struct {
	Machines     []DeviceStatus `json:"machines"`     // 设备列表
	StatusCounts StatusCounts   `json:"statusCounts"` // 状态统计
	CompanyInfo  CompanyInfo    `json:"companyInfo"`  // 公司信息
}

// ProductionHistoryResponse 生产历史API响应结构
// 与31_machine_backend保持100%兼容的响应格式
type ProductionHistoryResponse struct {
	Machines          []DeviceStatus     `json:"machines"`          // 设备列表
	Products          []ProductInfo      `json:"products"`          // 产品列表
	ProductionRecords []ProductionRecord `json:"productionRecords"` // 生产记录
	CompanyInfo       CompanyInfo        `json:"companyInfo"`       // 公司信息
}

// ProductInfo 产品信息数据结构（用于API响应）
type ProductInfo struct {
	ID          string `json:"id"`          // 产品ID
	Code        string `json:"code"`        // 产品编码
	Name        string `json:"name"`        // 产品名称
	ProcessCode string `json:"processCode"` // 工序代码
	ProcessName string `json:"processName"` // 工序名称
	Program     string `json:"program"`     // 加工程序
}

// ProductionRecord 生产记录数据结构
type ProductionRecord struct {
	ID        string  `json:"id"`        // 记录ID
	DeviceID  string  `json:"deviceId"`  // 设备ID
	ProductID string  `json:"productId"` // 产品ID
	Date      string  `json:"date"`      // 日期
	Quantity  int     `json:"quantity"`  // 产量
	Plan      int     `json:"plan"`      // 计划
	StartTime string  `json:"startTime"` // 开始时间
	EndTime   *string `json:"endTime"`   // 结束时间
}

// RedisKeys Redis缓存键定义
type RedisKeys struct {
	// 实时数据键格式
	DeviceStatusToday     string // device:status:{device_id}:today
	DeviceProductionToday string // device:production:{device_id}:today
	DeviceProgramToday    string // device:program:{device_id}:today
	DeviceStatusHistory   string // device:status_history:{device_id}:{date}

	// 聚合数据键格式
	AllDevicesStatus     string // devices:status:today
	AllDevicesProduction string // devices:production:today
	StatusCounts         string // devices:status_counts:today

	// 缓存过期时间
	TodayDataExpiration   time.Duration // 当天数据过期时间：25小时
	HistoryDataExpiration time.Duration // 历史数据过期时间：7天
}

// GetRedisKeys 获取Redis键配置
func GetRedisKeys() RedisKeys {
	return RedisKeys{
		DeviceStatusToday:     "device:status:%s:today",
		DeviceProductionToday: "device:production:%s:today",
		DeviceProgramToday:    "device:program:%s:today",
		DeviceStatusHistory:   "device:status_history:%s:%s",
		AllDevicesStatus:      "devices:status:today",
		AllDevicesProduction:  "devices:production:today",
		StatusCounts:          "devices:status_counts:today",
		TodayDataExpiration:   25 * time.Hour,     // 25小时，确保跨天时有缓冲
		HistoryDataExpiration: 7 * 24 * time.Hour, // 7天
	}
}

// ToJSON 将结构体转换为JSON字符串
func (ds *DeviceStatus) ToJSON() (string, error) {
	data, err := json.Marshal(ds)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析设备状态
func (ds *DeviceStatus) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), ds)
}

/**
 * 图表数据点结构体
 *
 * 功能：用于20_viewer图表展示的数据点格式
 *
 * 数据来源：
 * - InfluxDB时序数据查询结果
 * - 设备传感器数据的时间序列
 *
 * 使用场景：
 * - 实时图表展示
 * - 历史趋势分析
 * - 数据可视化
 *
 * @struct ChartDataPoint
 */
type ChartDataPoint struct {
	/** 时间戳，RFC3339格式 */
	Timestamp string `json:"timestamp"`

	/** 数值，传感器读数或计算值 */
	Value float64 `json:"value"`

	/** 设备ID，数据来源设备 */
	DeviceID string `json:"device_id"`

	/** 字段名称，如temperature、pressure等 */
	Field string `json:"field"`
}

/**
 * 传感器数据点结构体
 *
 * 功能：InfluxDB查询返回的原始传感器数据格式
 *
 * @struct SensorDataPoint
 */
type SensorDataPoint struct {
	/** 时间戳 */
	Timestamp time.Time `json:"timestamp"`

	/** 数值 */
	Value float64 `json:"value"`

	/** 设备ID */
	DeviceID string `json:"device_id"`

	/** 字段名称 */
	Field string `json:"field"`

	/** 测量名称 */
	Measurement string `json:"measurement"`
}

// ToJSON 将生产数据转换为JSON字符串
func (dp *DeviceProduction) ToJSON() (string, error) {
	data, err := json.Marshal(dp)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析生产数据
func (dp *DeviceProduction) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), dp)
}

// IsToday 判断给定日期是否为今天
func IsToday(date string) bool {
	today := time.Now().Format("2006-01-02")
	return date == today
}

// GetTodayDate 获取今天的日期字符串
func GetTodayDate() string {
	return time.Now().Format("2006-01-02")
}

// CalculateProgress 计算生产进度百分比
func CalculateProgress(current, plan int) float64 {
	if plan <= 0 {
		return 0.0
	}
	progress := float64(current) / float64(plan) * 100
	if progress > 100 {
		progress = 100
	}
	return progress
}

// Machine 机器数据结构（用于前端显示）
// 与30_machine_status前端期望的格式保持一致
type Machine struct {
	ID        string    `json:"id"`        // 机器ID
	Brand     string    `json:"brand"`     // 机器品牌（原code字段）
	Name      string    `json:"name"`      // 机器名称
	Location  string    `json:"location"`  // 机器位置
	Model     string    `json:"model"`     // 机器型号
	Status    string    `json:"status"`    // 机器状态
	Quantity  int       `json:"quantity"`  // 当前产量
	Plan      int       `json:"plan"`      // 计划产量
	Timestamp time.Time `json:"timestamp"` // 状态时间戳，记录状态的最后更新时间
}

// MachineResponse 机器状态API响应结构
// 与30_machine_status前端期望的格式保持一致
type MachineResponse struct {
	Machines     []Machine    `json:"machines"`     // 机器列表
	StatusCounts StatusCounts `json:"statusCounts"` // 状态统计
	CompanyInfo  CompanyInfo  `json:"companyInfo"`  // 公司信息
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 错误消息
	Error   string `json:"error"`   // 错误详情
}

// SuccessResponse 成功响应结构
type SuccessResponse struct {
	Success bool        `json:"success"` // 是否成功
	Message string      `json:"message"` // 成功消息
	Data    interface{} `json:"data"`    // 数据
}

// DeviceMapping 设备映射配置
type DeviceMapping struct {
	DeviceID   string `json:"device_id"`   // 设备ID
	DeviceCode string `json:"device_code"` // 设备编码
	DeviceName string `json:"device_name"` // 设备名称
	Location   string `json:"location"`    // 位置
	Model      string `json:"model"`       // 型号
	Category   string `json:"category"`    // 类别
}

// GetDefaultDeviceMappings 获取默认设备映射
// 注意：这些是硬编码的备用映射，当MongoDB查询失败时使用
// 所有信息都设置为"unknown"，让用户一目了然地知道这些是未配置的设备
func GetDefaultDeviceMappings() []DeviceMapping {
	return []DeviceMapping{}
}

// FindDeviceMapping 查找设备映射
func FindDeviceMapping(deviceID string) *DeviceMapping {
	mappings := GetDefaultDeviceMappings()
	for _, mapping := range mappings {
		if mapping.DeviceID == deviceID {
			return &mapping
		}
	}
	return nil
}

// StatusMapping 状态映射
var StatusMapping = map[string]string{
	"running":      "production", // 运行中 -> 生产中
	"standby":      "idle",       // 待机 -> 空闲
	"alarm":        "fault",      // 报警 -> 故障
	"maintenance":  "adjusting",  // 维护 -> 调机
	"offline":      "shutdown",   // 离线 -> 关机
	"disconnected": "shutdown",   // 未连接 -> 未连接
	"stop":         "shutdown",   // 停止 -> 关机
	"unknown":      "shutdown",   // 未知 -> 关机
	"error":        "fault",      // 错误 -> 故障
}

// MapStatus 映射状态
func MapStatus(deviceStatus string) string {
	if mappedStatus, exists := StatusMapping[deviceStatus]; exists {
		return mappedStatus
	}

	// 如果没有找到映射，检查是否已经是目标状态
	validStatuses := []string{"production", "idle", "fault", "adjusting", "shutdown", "disconnected"}
	for _, status := range validStatuses {
		if deviceStatus == status {
			return status
		}
	}

	// 默认返回未连接状态
	return "shutdown"
}

type UtilizationData struct {
	// 使用工作时间
	UseWorkTime bool `json:"use_work_time,omitempty"`
	// 总工作时间
	WorkTime float64 `json:"work_time,omitempty"`
	// 利用率
	Utilization float64 `json:"utilization,omitempty"`
	// 运行/生产时间
	RunningTime int64 `json:"running_time,omitempty"`
	// 所有状态时间之和
	TotalTime int64 `json:"total_time,omitempty"`
	// 各个状态集合
	StatusSummary map[string]int64 `json:"status_summary,omitempty"`
	// 设备信息
	Device Device `json:"device,omitempty"`
}
