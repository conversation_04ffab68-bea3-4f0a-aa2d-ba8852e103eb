/**
 * 管理系统数据模型
 *
 * 功能概述：
 * 本模块定义了制造业数据采集系统中所有管理相关的数据结构，
 * 包括设备管理、产品管理、工序管理、订单管理、生产计划等核心业务模型
 *
 * 主要数据模型：
 * - 设备管理：设备信息、配置参数、状态管理
 * - 产品管理：产品信息、规格参数、计量单位
 * - 工序管理：工艺流程、标准工时、工序顺序
 * - 订单管理：客户订单、产品需求、交期管理
 * - 生产计划：日计划、任务分配、进度跟踪
 * - 用户管理：用户账户、角色权限、个人资料
 * - 系统设置：刷新配置、显示设置、系统参数
 *
 * 数据存储设计：
 * - MongoDB文档存储：灵活的JSON文档结构
 * - 索引优化：基于查询模式的索引设计
 * - 关联关系：通过ID字段建立数据关联
 * - 时间戳：统一的创建和更新时间记录
 * - 状态管理：标准化的状态枚举和转换
 *
 * 业务规则：
 * - 数据完整性：必填字段验证和约束检查
 * - 唯一性约束：关键字段的唯一性保证
 * - 状态转换：业务状态的合法转换规则
 * - 权限控制：基于角色的数据访问控制
 * - 审计追踪：数据变更的完整记录
 *
 * API兼容性：
 * - JSON序列化：标准的JSON格式支持
 * - 请求验证：完整的参数验证规则
 * - 响应格式：统一的API响应结构
 * - 分页支持：大数据集的分页查询
 * - 搜索过滤：多字段的搜索和过滤
 *
 * 业务场景：
 * - 设备配置管理：为数据采集提供设备配置
 * - 生产计划制定：制定和调整生产计划
 * - 订单执行跟踪：跟踪订单的执行进度
 * - 用户权限管理：管理系统用户和权限
 * - 系统参数配置：配置系统运行参数
 *
 * @package models
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SystemSettings 系统设置数据模型
type SystemSettings struct {
	ID        primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	Type      string                 `bson:"type" json:"type"`         // 设置类型: "refresh" 或 "display"
	Settings  map[string]interface{} `bson:"settings" json:"settings"` // 设置内容（JSON格式）
	CreatedAt time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time              `bson:"updated_at" json:"updated_at"`
}

// RefreshSettings 刷新设置结构体
type RefreshSettings struct {
	AutoRefresh          bool `json:"autoRefresh"`
	RefreshInterval      int  `json:"refreshInterval"`
	ShowRefreshIndicator bool `json:"showRefreshIndicator"`
}

// DisplaySettings 显示设置结构体
type DisplaySettings struct {
	DeviceGridGap      int         `json:"deviceGridGap"`
	DeviceRowGap       int         `json:"deviceRowGap"`
	DeviceColumnGap    int         `json:"deviceColumnGap"`
	StatusDeviceGap    int         `json:"statusDeviceGap"`
	StatusCountDigits  int         `json:"statusCountDigits"`
	StatusCountPadChar string      `json:"statusCountPadChar"`
	CardContent        interface{} `json:"cardContent"`
	CardStyle          interface{} `json:"cardStyle"`
	StatusColors       interface{} `json:"statusColors"`
	StatusHistory      interface{} `json:"statusHistory"`
	ProductionProgress interface{} `json:"productionProgress"`
}

/**
 * 设备管理数据模型
 *
 * 功能：存储设备的基本信息和数据采集配置参数
 *
 * 业务用途：
 * - 设备档案管理：设备的基本信息和分类管理
 * - 数据采集配置：为fanuc_v2等采集服务提供配置
 * - 设备状态管理：设备的启用、禁用、维护状态
 * - 位置管理：设备的物理位置和区域划分
 *
 * 配置分类：
 * - 基本信息：设备编号、名称、类别、位置
 * - 网络配置：IP地址、端口号
 * - 采集参数：采集间隔、超时时间、重试策略
 * - 状态管理：启用状态、自动启动配置
 *
 * 数据关联：
 * - 与DeviceStatus关联：通过device_id字段
 * - 与ProductionTask关联：生产任务的设备分配
 * - 与DailyPlan关联：日计划的设备安排
 * - 与采集服务关联：提供设备列表API
 *
 * @struct Device
 */
type Device struct {
	/** MongoDB文档ID，系统内部唯一标识 */
	ID primitive.ObjectID `bson:"_id,omitempty" json:"id"`

	/** 设备业务编号，如"FANUC_001"，用于业务层面的设备识别 */
	DeviceID string `bson:"device_id" json:"device_id"`

	/** 设备显示名称，如"FANUC CNC机床#1"，用于用户界面显示 */
	Name string `bson:"name" json:"name"`

	/** 设备数据类型，如"fanuc_30i"、"siemens_840d"等，用于数据采集和处理 */
	DataType string `bson:"data_type" json:"data_type"`

	/** 设备物理位置，如"A区"、"生产线1"，便于现场定位 */
	Location string `bson:"location" json:"location"`

	/** 设备管理状态，如"active"、"inactive"、"maintenance" */
	Status string `bson:"status" json:"status"`

	/** 设备显示排序，数字越小越靠前，用于控制30_machine_status主页的显示顺序 */
	SortOrder int `bson:"sort_order" json:"sort_order"`

	// 设备元数据信息，用于数据推送时的metadata字段
	/** 设备品牌，如"Fanuc"，用于metadata字段 */
	Brand string `bson:"brand,omitempty" json:"brand,omitempty"`

	/** 设备型号，如"Fanuc 30i"，用于metadata字段 */
	Model string `bson:"model,omitempty" json:"model,omitempty"`

	// 数据采集相关配置
	/** 设备IP地址，用于网络通信连接 */
	IP string `bson:"ip,omitempty" json:"ip,omitempty"`

	/** 设备通信端口，如8193（FANUC默认端口） */
	Port int `bson:"port,omitempty" json:"port,omitempty"`

	/** 是否启用数据采集，控制采集服务是否连接此设备 */
	Enabled bool `bson:"enabled" json:"enabled"`

	/** 是否自动启动采集，系统启动时自动开始采集 */
	AutoStart bool `bson:"auto_start" json:"auto_start"`

	/** 数据采集间隔，单位毫秒，如1000表示每秒采集一次 */
	CollectInterval int `bson:"collect_interval" json:"collect_interval"`

	/** 通信超时时间，单位秒，超过此时间认为连接失败 */
	Timeout int `bson:"timeout" json:"timeout"`

	/** 连接失败重试次数，如3表示最多重试3次 */
	RetryCount int `bson:"retry_count" json:"retry_count"`

	/** 重试间隔延迟，单位秒，重试之间的等待时间 */
	RetryDelay int `bson:"retry_delay" json:"retry_delay"`

	/** 设备描述信息，备注和说明 */
	Description string `bson:"description" json:"description"`

	/** 记录创建时间 */
	CreatedAt time.Time `bson:"created_at" json:"created_at"`

	/** 记录最后更新时间 */
	UpdatedAt time.Time `bson:"updated_at" json:"updated_at"`
}

// Product 产品管理数据模型
type Product struct {
	ID             primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	ProductID      string                 `bson:"product_id" json:"product_id"`         // 产品编号
	Name           string                 `bson:"name" json:"name"`                     // 产品名称
	Description    string                 `bson:"description" json:"description"`       // 产品描述
	Specifications map[string]interface{} `bson:"specifications" json:"specifications"` // 产品规格
	Unit           string                 `bson:"unit" json:"unit"`                     // 计量单位
	CreatedAt      time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt      time.Time              `bson:"updated_at" json:"updated_at"`
}

// Process 工序管理数据模型
type Process struct {
	ID              primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProcessID       string             `bson:"process_id" json:"process_id"`             // 工序编号
	Name            string             `bson:"name" json:"name"`                         // 工序名称
	Description     string             `bson:"description" json:"description"`           // 工序描述
	Duration        int                `bson:"duration" json:"duration"`                 // 标准工时(秒)
	PreparationTime int                `bson:"preparation_time" json:"preparation_time"` // 准备时间(秒)
	Sequence        int                `bson:"sequence" json:"sequence"`                 // 工序顺序
	ProductID       string             `bson:"product_id" json:"product_id"`             // 关联产品ID
	CreatedAt       time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt       time.Time          `bson:"updated_at" json:"updated_at"`
}

// OrderOld 旧订单管理数据模型 (保留兼容性)
type OrderOld struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderID       string             `bson:"order_id" json:"order_id"`             // 订单编号
	Customer      string             `bson:"customer" json:"customer"`             // 客户名称
	Products      []OrderProduct     `bson:"products" json:"products"`             // 订单产品列表
	TotalQuantity int                `bson:"total_quantity" json:"total_quantity"` // 总数量
	Status        string             `bson:"status" json:"status"`                 // 订单状态: pending, in_progress, completed, cancelled
	Priority      int                `bson:"priority" json:"priority"`             // 优先级 1-5
	Deadline      time.Time          `bson:"deadline" json:"deadline"`             // 交期
	CreatedAt     time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt     time.Time          `bson:"updated_at" json:"updated_at"`
}

// OrderProduct 订单产品项
type OrderProduct struct {
	ProductID   string  `bson:"product_id" json:"product_id"`     // 产品ID
	ProductName string  `bson:"product_name" json:"product_name"` // 产品名称
	Quantity    int     `bson:"quantity" json:"quantity"`         // 数量
	Unit        string  `bson:"unit" json:"unit"`                 // 计量单位
	UnitPrice   float64 `bson:"unit_price" json:"unit_price"`     // 单价
}

// ProductionPlan 生产计划数据模型
type ProductionPlan struct {
	ID              primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	PlanID          string             `bson:"plan_id" json:"plan_id"`                     // 计划编号
	Date            time.Time          `bson:"date" json:"date"`                           // 计划日期
	DeviceID        string             `bson:"device_id" json:"device_id"`                 // 设备ID
	ProductID       string             `bson:"product_id" json:"product_id"`               // 产品ID
	OrderID         string             `bson:"order_id" json:"order_id"`                   // 关联订单ID
	TargetQuantity  int                `bson:"target_quantity" json:"target_quantity"`     // 计划产量
	ActualQuantity  int                `bson:"actual_quantity" json:"actual_quantity"`     // 实际产量
	Status          string             `bson:"status" json:"status"`                       // 计划状态: planned, in_progress, completed, cancelled
	StartTime       time.Time          `bson:"start_time" json:"start_time"`               // 计划开始时间
	EndTime         time.Time          `bson:"end_time" json:"end_time"`                   // 计划结束时间
	ActualStartTime *time.Time         `bson:"actual_start_time" json:"actual_start_time"` // 实际开始时间
	ActualEndTime   *time.Time         `bson:"actual_end_time" json:"actual_end_time"`     // 实际结束时间
	CreatedAt       time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt       time.Time          `bson:"updated_at" json:"updated_at"`
}

// ProductionSummary 生产汇总数据模型
type ProductionSummary struct {
	ID                primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Date              time.Time          `bson:"date" json:"date"`                             // 汇总日期
	DeviceID          string             `bson:"device_id" json:"device_id"`                   // 设备ID
	ProductID         string             `bson:"product_id" json:"product_id"`                 // 产品ID
	TotalQuantity     int                `bson:"total_quantity" json:"total_quantity"`         // 总产量
	QualifiedQuantity int                `bson:"qualified_quantity" json:"qualified_quantity"` // 合格产量
	DefectQuantity    int                `bson:"defect_quantity" json:"defect_quantity"`       // 不良产量
	EfficiencyRate    float64            `bson:"efficiency_rate" json:"efficiency_rate"`       // 效率率
	QualityRate       float64            `bson:"quality_rate" json:"quality_rate"`             // 合格率
	WorkingHours      float64            `bson:"working_hours" json:"working_hours"`           // 工作时长(小时)
	DowntimeHours     float64            `bson:"downtime_hours" json:"downtime_hours"`         // 停机时长(小时)
	CreatedAt         time.Time          `bson:"created_at" json:"created_at"`
}

// DeviceStatus 设备状态枚举
const (
	DeviceStatusActive      = "active"      // 活跃
	DeviceStatusInactive    = "inactive"    // 非活跃
	DeviceStatusMaintenance = "maintenance" // 维护中
	DeviceStatusFault       = "fault"       // 故障
)

// OrderStatusOld 旧订单状态枚举 (保留兼容性)
const (
	OrderStatusInProgress = "in_progress" // 进行中
)

// PlanStatus 生产计划状态枚举
const (
	PlanStatusPlanned    = "planned"     // 已计划
	PlanStatusInProgress = "in_progress" // 进行中
	PlanStatusCompleted  = "completed"   // 已完成
	PlanStatusCancelled  = "cancelled"   // 已取消
)

// DeviceCreateRequest 设备创建请求
type DeviceCreateRequest struct {
	DeviceID        string `json:"device_id" binding:"required"`
	Name            string `json:"name" binding:"required"`
	DataType        string `json:"data_type" binding:"required"`
	Location        string `json:"location" binding:"required"`
	SortOrder       int    `json:"sort_order"`
	Brand           string `json:"brand"`
	Model           string `json:"model"`
	IP              string `json:"ip"`
	Port            int    `json:"port"`
	Enabled         bool   `json:"enabled"`
	AutoStart       bool   `json:"auto_start"`
	CollectInterval int    `json:"collect_interval"`
	Timeout         int    `json:"timeout"`
	RetryCount      int    `json:"retry_count"`
	RetryDelay      int    `json:"retry_delay"`
	Description     string `json:"description"`
}

// DeviceUpdateRequest 设备更新请求
type DeviceUpdateRequest struct {
	Name            string `json:"name"`
	DataType        string `json:"data_type"`
	Location        string `json:"location"`
	Status          string `json:"status"`
	SortOrder       int    `json:"sort_order"`
	Brand           string `json:"brand"`
	Model           string `json:"model"`
	IP              string `json:"ip"`
	Port            int    `json:"port"`
	Enabled         bool   `json:"enabled"`
	AutoStart       bool   `json:"auto_start"`
	CollectInterval int    `json:"collect_interval"`
	Timeout         int    `json:"timeout"`
	RetryCount      int    `json:"retry_count"`
	RetryDelay      int    `json:"retry_delay"`
	Description     string `json:"description"`
}

// ProductCreateRequest 产品创建请求
type ProductCreateRequest struct {
	ProductID      string                 `json:"product_id" binding:"required"`
	Name           string                 `json:"name" binding:"required"`
	Description    string                 `json:"description"`
	Specifications map[string]interface{} `json:"specifications"`
	Unit           string                 `json:"unit"`
}

// ProductUpdateRequest 产品更新请求
type ProductUpdateRequest struct {
	Name           string                 `json:"name"`
	Description    string                 `json:"description"`
	Specifications map[string]interface{} `json:"specifications"`
	Unit           string                 `json:"unit"`
}

// ProcessCreateRequest 工序创建请求
type ProcessCreateRequest struct {
	ProcessID       string `json:"process_id" binding:"required"`
	Name            string `json:"name" binding:"required"`
	Description     string `json:"description"`
	Duration        int    `json:"duration" binding:"required"`         // 标准工时(秒)
	PreparationTime int    `json:"preparation_time" binding:"required"` // 准备时间(秒)
	Sequence        int    `json:"sequence" binding:"required"`
	ProductID       string `json:"product_id" binding:"required"`
}

// ProcessUpdateRequest 工序更新请求
type ProcessUpdateRequest struct {
	Name            string `json:"name"`
	Description     string `json:"description"`
	Duration        int    `json:"duration"`         // 标准工时(秒)
	PreparationTime int    `json:"preparation_time"` // 准备时间(秒)
	Sequence        int    `json:"sequence"`
	ProductID       string `json:"product_id"`
}

// OrderCreateRequestOld 旧订单创建请求 (保留兼容性)
type OrderCreateRequestOld struct {
	OrderID  string         `json:"order_id" binding:"required"`
	Customer string         `json:"customer" binding:"required"`
	Products []OrderProduct `json:"products" binding:"required"`
	Priority int            `json:"priority"`
	Deadline time.Time      `json:"deadline" binding:"required"`
}

// OrderUpdateRequest 订单更新请求
type OrderUpdateRequest struct {
	Customer string         `json:"customer"`
	Products []OrderProduct `json:"products"`
	Status   string         `json:"status"`
	Priority int            `json:"priority"`
	Deadline time.Time      `json:"deadline"`
}

// ProductionPlanCreateRequest 生产计划创建请求
type ProductionPlanCreateRequest struct {
	PlanID         string    `json:"plan_id" binding:"required"`
	Date           time.Time `json:"date" binding:"required"`
	DeviceID       string    `json:"device_id" binding:"required"`
	ProductID      string    `json:"product_id" binding:"required"`
	OrderID        string    `json:"order_id"`
	TargetQuantity int       `json:"target_quantity" binding:"required"`
	StartTime      time.Time `json:"start_time" binding:"required"`
	EndTime        time.Time `json:"end_time" binding:"required"`
}

// ProductionPlanUpdateRequest 生产计划更新请求
type ProductionPlanUpdateRequest struct {
	Date            time.Time  `json:"date"`
	DeviceID        string     `json:"device_id"`
	ProductID       string     `json:"product_id"`
	OrderID         string     `json:"order_id"`
	TargetQuantity  int        `json:"target_quantity"`
	ActualQuantity  int        `json:"actual_quantity"`
	Status          string     `json:"status"`
	StartTime       time.Time  `json:"start_time"`
	EndTime         time.Time  `json:"end_time"`
	ActualStartTime *time.Time `json:"actual_start_time"`
	ActualEndTime   *time.Time `json:"actual_end_time"`
}

// ListResponse 通用列表响应
type ListResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// QueryParams 通用查询参数
type QueryParams struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
	Search   string `form:"search"`
	Status   string `form:"status"`
	SortBy   string `form:"sort_by,default=created_at"`
	SortDesc bool   `form:"sort_desc,default=true"`
}

// 用户角色常量
const (
	RoleAdmin    = "admin"    // 管理员
	RoleOperator = "operator" // 操作员
	RoleViewer   = "viewer"   // 查看者
)

// 用户状态常量
const (
	UserStatusActive   = "active"   // 激活
	UserStatusInactive = "inactive" // 停用
	UserStatusLocked   = "locked"   // 锁定
)

// User 用户模型
type User struct {
	ID          primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Username    string             `json:"username" bson:"username"`
	Email       string             `json:"email" bson:"email"`
	Password    string             `json:"-" bson:"password"` // 不在JSON中返回密码
	Role        string             `json:"role" bson:"role"`
	Status      string             `json:"status" bson:"status"`
	LastLogin   *time.Time         `json:"last_login,omitempty" bson:"last_login,omitempty"`
	LoginCount  int                `json:"login_count" bson:"login_count"`
	Profile     UserProfile        `json:"profile" bson:"profile"`
	Permissions []string           `json:"permissions" bson:"permissions"`
	CreatedAt   time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at" bson:"updated_at"`
}

// UserProfile 用户资料
type UserProfile struct {
	FullName   string `json:"full_name" bson:"full_name"`
	Phone      string `json:"phone" bson:"phone"`
	Department string `json:"department" bson:"department"`
	Position   string `json:"position" bson:"position"`
	Avatar     string `json:"avatar" bson:"avatar"`
}

// UserCreateRequest 创建用户请求
type UserCreateRequest struct {
	Username string      `json:"username" binding:"required"`
	Email    string      `json:"email" binding:"required,email"`
	Password string      `json:"password" binding:"required,min=6"`
	Role     string      `json:"role" binding:"required"`
	Profile  UserProfile `json:"profile"`
}

// UserUpdateRequest 更新用户请求
type UserUpdateRequest struct {
	Email       string      `json:"email" binding:"omitempty,email"`
	Role        string      `json:"role"`
	Status      string      `json:"status"`
	Profile     UserProfile `json:"profile"`
	Permissions []string    `json:"permissions"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserLoginResponse 用户登录响应
type UserLoginResponse struct {
	Token     string    `json:"token"`
	User      User      `json:"user"`
	ExpiresAt time.Time `json:"expires_at"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// 新订单状态常量
const (
	OrderStatusPending    = "pending"    // 待处理
	OrderStatusProcessing = "processing" // 处理中
	OrderStatusCompleted  = "completed"  // 已完成
	OrderStatusCancelled  = "cancelled"  // 已取消
)

// 生产任务状态常量
const (
	TaskStatusPlanned    = "planned"     // 已计划
	TaskStatusInProgress = "in_progress" // 进行中
	TaskStatusCompleted  = "completed"   // 已完成
	TaskStatusPaused     = "paused"      // 暂停
	TaskStatusCancelled  = "cancelled"   // 已取消
)

/**
 * 订单数据模型
 *
 * 功能：存储客户订单信息，是生产计划制定的基础数据
 *
 * 业务用途：
 * - 订单管理：客户订单的全生命周期管理
 * - 生产计划：为生产任务提供订单依据
 * - 交期管理：跟踪订单的交付进度
 * - 优先级控制：根据优先级安排生产顺序
 *
 * 状态流转：
 * - pending：待处理，新创建的订单
 * - processing：处理中，已安排生产
 * - completed：已完成，订单交付完成
 * - cancelled：已取消，订单被取消
 *
 * 数据关联：
 * - 与Product关联：通过product_id字段
 * - 与ProductionTask关联：生产任务的订单来源
 * - 与DailyPlan关联：日计划的订单依据
 *
 * @struct Order
 */
type Order struct {
	/** MongoDB文档ID，系统内部唯一标识 */
	ID primitive.ObjectID `json:"id" bson:"_id,omitempty"`

	/** 订单编号，业务层面的唯一标识，如"ORD20250605001" */
	OrderNumber string `json:"order_number" bson:"order_number"`

	/** 客户名称，订单的客户信息 */
	CustomerName string `json:"customer_name" bson:"customer_name"`

	/** 订单产品列表，支持多产品订单 */
	Products []OrderProduct `json:"products" bson:"products"`

	/** 产品ID，关联到Product表的产品信息（保留兼容性） */
	ProductID string `json:"product_id,omitempty" bson:"product_id,omitempty"`

	/** 产品名称，冗余存储便于查询显示（保留兼容性） */
	ProductName string `json:"product_name,omitempty" bson:"product_name,omitempty"`

	/** 订单数量，需要生产的产品数量（保留兼容性） */
	Quantity int `json:"quantity,omitempty" bson:"quantity,omitempty"`

	/** 产品单价，用于计算订单总金额（保留兼容性） */
	UnitPrice float64 `json:"unit_price,omitempty" bson:"unit_price,omitempty"`

	/** 订单总金额，数量×单价的计算结果 */
	TotalAmount float64 `json:"total_amount" bson:"total_amount"`

	/** 订单状态，见上述状态流转说明 */
	Status string `json:"status" bson:"status"`

	/** 订单优先级，1-5级，5为最高优先级 */
	Priority int `json:"priority" bson:"priority"`

	/** 交付日期，客户要求的交付时间 */
	DeliveryDate time.Time `json:"delivery_date" bson:"delivery_date"`

	/** 订单描述，备注和特殊要求 */
	Description string `json:"description" bson:"description"`

	/** 记录创建时间 */
	CreatedAt time.Time `json:"created_at" bson:"created_at"`

	/** 记录最后更新时间 */
	UpdatedAt time.Time `json:"updated_at" bson:"updated_at"`
}

// ProductionTask 生产任务模型
type ProductionTask struct {
	ID                primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	TaskNumber        string             `json:"task_number" bson:"task_number"`
	OrderID           string             `json:"order_id" bson:"order_id"`
	OrderNumber       string             `json:"order_number" bson:"order_number"`
	ProductID         string             `json:"product_id" bson:"product_id"`
	ProductName       string             `json:"product_name" bson:"product_name"`
	DeviceID          string             `json:"device_id" bson:"device_id"`
	DeviceName        string             `json:"device_name" bson:"device_name"`
	PlannedQuantity   int                `json:"planned_quantity" bson:"planned_quantity"`
	ActualQuantity    int                `json:"actual_quantity" bson:"actual_quantity"`
	QualifiedQuantity int                `json:"qualified_quantity" bson:"qualified_quantity"`
	DefectiveQuantity int                `json:"defective_quantity" bson:"defective_quantity"`
	Status            string             `json:"status" bson:"status"`
	Priority          int                `json:"priority" bson:"priority"`
	PlannedStartTime  time.Time          `json:"planned_start_time" bson:"planned_start_time"`
	PlannedEndTime    time.Time          `json:"planned_end_time" bson:"planned_end_time"`
	ActualStartTime   *time.Time         `json:"actual_start_time,omitempty" bson:"actual_start_time,omitempty"`
	ActualEndTime     *time.Time         `json:"actual_end_time,omitempty" bson:"actual_end_time,omitempty"`
	EstimatedDuration int                `json:"estimated_duration" bson:"estimated_duration"` // 预计时长(分钟)
	ActualDuration    int                `json:"actual_duration" bson:"actual_duration"`       // 实际时长(分钟)
	Progress          float64            `json:"progress" bson:"progress"`                     // 进度百分比
	Notes             string             `json:"notes" bson:"notes"`
	CreatedAt         time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt         time.Time          `json:"updated_at" bson:"updated_at"`
}

// DailyPlan 每日生产计划模型
type DailyPlan struct {
	ID              primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	PlanDate        time.Time          `json:"plan_date" bson:"plan_date"`
	DeviceID        string             `json:"device_id" bson:"device_id"`
	DeviceName      string             `json:"device_name" bson:"device_name"`
	ProductID       string             `json:"product_id" bson:"product_id"`
	ProductName     string             `json:"product_name" bson:"product_name"`
	PlannedQuantity int                `json:"planned_quantity" bson:"planned_quantity"`
	ActualQuantity  int                `json:"actual_quantity" bson:"actual_quantity"`
	CompletionRate  float64            `json:"completion_rate" bson:"completion_rate"` // 完成率
	ShiftType       string             `json:"shift_type" bson:"shift_type"`           // 班次类型
	OperatorName    string             `json:"operator_name" bson:"operator_name"`
	Status          string             `json:"status" bson:"status"`
	Notes           string             `json:"notes" bson:"notes"`
	CreatedAt       time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt       time.Time          `json:"updated_at" bson:"updated_at"`
}

// OrderCreateRequest 创建订单请求
type OrderCreateRequest struct {
	OrderNumber  string         `json:"order_number" binding:"required"`
	CustomerName string         `json:"customer_name" binding:"required"`
	Products     []OrderProduct `json:"products,omitempty"`   // 多产品支持
	ProductID    string         `json:"product_id,omitempty"` // 单产品兼容
	Quantity     int            `json:"quantity,omitempty"`   // 单产品兼容
	UnitPrice    float64        `json:"unit_price,omitempty"` // 单产品兼容
	Priority     int            `json:"priority" binding:"min=1,max=5"`
	DeliveryDate time.Time      `json:"delivery_date" binding:"required"`
	Description  string         `json:"description"`
}

// ProductionTaskCreateRequest 创建生产任务请求
type ProductionTaskCreateRequest struct {
	OrderID           string    `json:"order_id" binding:"required"`
	ProductID         string    `json:"product_id" binding:"required"`
	DeviceID          string    `json:"device_id" binding:"required"`
	PlannedQuantity   int       `json:"planned_quantity" binding:"required,min=1"`
	Priority          int       `json:"priority" binding:"min=1,max=5"`
	PlannedStartTime  time.Time `json:"planned_start_time" binding:"required"`
	PlannedEndTime    time.Time `json:"planned_end_time" binding:"required"`
	EstimatedDuration int       `json:"estimated_duration" binding:"required,min=1"`
	Notes             string    `json:"notes"`
}

// DailyPlanCreateRequest 创建每日计划请求
type DailyPlanCreateRequest struct {
	PlanDate        time.Time `json:"plan_date" binding:"required"`
	DeviceID        string    `json:"device_id" binding:"required"`
	ProductID       string    `json:"product_id" binding:"required"`
	PlannedQuantity int       `json:"planned_quantity" binding:"required,min=1"`
	ShiftType       string    `json:"shift_type" binding:"required"`
	OperatorName    string    `json:"operator_name" binding:"required"`
	Notes           string    `json:"notes"`
}

// ===== 计量单位管理数据模型 =====

/**
 * 计量单位数据模型
 *
 * 功能：存储系统中使用的计量单位信息，为产品和订单提供标准化的计量基础
 *
 * 业务用途：
 * - 产品管理：为产品定义标准计量单位
 * - 订单管理：统一订单中的数量计量标准
 * - 库存管理：标准化库存计量单位
 * - 报表统计：提供一致的数据展示单位
 *
 * 分类管理：
 * - count：计数单位（个、件、套、台等）
 * - weight：重量单位（千克、克、吨等）
 * - length：长度单位（米、厘米、毫米等）
 * - volume：体积单位（升、毫升、立方米等）
 * - area：面积单位（平方米、平方厘米等）
 *
 * 数据关联：
 * - 与Product关联：产品的计量单位
 * - 与Order关联：订单产品的数量单位
 * - 与ProductionPlan关联：生产计划的目标数量单位
 * - 与统计报表关联：数据展示的单位标准化
 *
 * @struct Unit
 */
type Unit struct {
	/** MongoDB文档ID，系统内部唯一标识 */
	ID primitive.ObjectID `bson:"_id,omitempty" json:"id"`

	/** 单位名称，如"个"、"件"、"千克"，用于显示和选择 */
	Name string `bson:"name" json:"name"`

	/** 单位符号，如"个"、"件"、"kg"，用于数据存储和API传输 */
	Symbol string `bson:"symbol" json:"symbol"`

	/** 单位类别，如"count"、"weight"、"length"等，用于分类管理 */
	Category string `bson:"category" json:"category"`

	/** 单位描述，详细说明单位的用途和适用场景 */
	Description string `bson:"description,omitempty" json:"description,omitempty"`

	/** 是否为默认单位，系统默认选择的计量单位 */
	IsDefault bool `bson:"is_default" json:"is_default"`

	/** 排序顺序，数字越小越靠前，控制单位选择列表的显示顺序 */
	SortOrder int `bson:"sort_order" json:"sort_order"`

	/** 记录创建时间 */
	CreatedAt time.Time `bson:"created_at" json:"created_at"`

	/** 记录最后更新时间 */
	UpdatedAt time.Time `bson:"updated_at" json:"updated_at"`
}

// 计量单位类别常量
const (
	UnitCategoryCount  = "count"  // 计数单位
	UnitCategoryWeight = "weight" // 重量单位
	UnitCategoryLength = "length" // 长度单位
	UnitCategoryVolume = "volume" // 体积单位
	UnitCategoryArea   = "area"   // 面积单位
)

// UnitCreateRequest 计量单位创建请求
type UnitCreateRequest struct {
	Name        string `json:"name" binding:"required"`     // 单位名称，必填
	Symbol      string `json:"symbol" binding:"required"`   // 单位符号，必填
	Category    string `json:"category" binding:"required"` // 单位类别，必填
	Description string `json:"description"`                 // 单位描述，可选
	IsDefault   bool   `json:"is_default"`                  // 是否为默认单位，可选
	SortOrder   int    `json:"sort_order"`                  // 排序顺序，可选
}

// UnitUpdateRequest 计量单位更新请求
type UnitUpdateRequest struct {
	Name        string `json:"name"`        // 单位名称
	Symbol      string `json:"symbol"`      // 单位符号
	Category    string `json:"category"`    // 单位类别
	Description string `json:"description"` // 单位描述
	IsDefault   bool   `json:"is_default"`  // 是否为默认单位
	SortOrder   int    `json:"sort_order"`  // 排序顺序
}
