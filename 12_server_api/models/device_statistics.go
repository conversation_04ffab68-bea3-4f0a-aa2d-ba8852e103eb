/**
 * 设备统计数据模型定义
 *
 * 功能概述：
 * 本模块定义了制造业数据采集系统中所有与设备统计分析相关的数据结构，
 * 包括设备利用率、产量统计、班次分析、工作时间配置等核心业务模型
 *
 * 数据模型分类：
 * - 统计结果模型：DeviceStatistics, DailyStatisticsSummary
 * - 配置管理模型：WorkTimeSettings, ShiftSetting, RestPeriod
 * - 请求响应模型：StatisticsRequest, StatisticsResponse
 * - 班次分析模型：ShiftStatistics
 *
 * 设计原则：
 * - 数据完整性：包含所有必要的统计维度和元数据
 * - 类型安全：使用强类型定义，避免运行时错误
 * - 序列化友好：支持JSON和BSON序列化，便于API和数据库操作
 * - 业务语义：字段命名清晰，符合制造业业务术语
 *
 * 存储策略：
 * - MongoDB：业务数据和配置信息的持久化存储
 * - JSON：API接口的数据交换格式
 * - 索引优化：基于查询模式设计数据库索引
 *
 * 业务规则：
 * - 时间单位：统一使用秒作为时间统计单位
 * - 百分比：利用率和达成率使用0-100的浮点数表示
 * - 日期格式：统一使用YYYY-MM-DD格式
 * - 时间格式：班次时间使用HH:MM格式
 *
 * @package models
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

/**
 * 设备统计数据模型
 *
 * 功能：存储单个设备在特定日期的完整统计信息
 *
 * 数据维度：
 * - 基础信息：设备标识、名称、统计日期
 * - 时间统计：各种状态的时间分布（秒为单位）
 * - 效率指标：设备利用率（百分比）
 * - 产量指标：实际产量、计划产量、达成率
 * - 班次分析：各班次的独立统计数据
 * - 元数据：创建和更新时间戳
 *
 * 业务规则：
 * - 利用率 = 运行时间 / 有效工作时间 × 100%
 * - 产量达成率 = 实际产量 / 计划产量 × 100%
 * - 时间分布：运行 + 空闲 + 故障 + 维护 ≤ 总时间
 *
 * 存储策略：
 * - MongoDB集合：device_statistics
 * - 索引：device_id + date（复合唯一索引）
 * - 分区：按月分区，提高查询性能
 *
 * @struct DeviceStatistics
 */
type DeviceStatistics struct {
	/** MongoDB文档ID，自动生成的唯一标识符 */
	ID primitive.ObjectID `bson:"_id,omitempty" json:"id"`

	/** 统计日期，格式：YYYY-MM-DD，用于数据分区和查询 */
	Date string `bson:"date" json:"date"`

	/** 设备唯一标识符，与设备管理系统中的ID对应 */
	DeviceID string `bson:"device_id" json:"device_id"`

	/** 设备显示名称，便于用户识别和报表展示 */
	DeviceName string `bson:"device_name" json:"device_name"`

	// ===== 时间统计数据（单位：秒） =====

	/** 统计周期内的总时间，通常为24小时（86400秒） */
	TotalTime int64 `bson:"total_time" json:"total_time"`

	/** 设备运行时间，计入利用率计算的有效生产时间 */
	RunningTime int64 `bson:"running_time" json:"running_time"`

	/** 设备空闲时间，等待工作或待机状态的时间 */
	IdleTime int64 `bson:"idle_time" json:"idle_time"`

	/** 设备故障时间，因故障停机的时间，影响可用性 */
	FaultTime int64 `bson:"fault_time" json:"fault_time"`

	/** 设备维护时间，计划维护和保养的时间 */
	MaintenanceTime int64 `bson:"maintenance_time" json:"maintenance_time"`

	// ===== 效率指标 =====

	/** 设备利用率，范围0-100，计算公式：运行时间/有效时间×100% */
	UtilizationRate float64 `bson:"utilization_rate" json:"utilization_rate"`

	// ===== 产量统计 =====

	/** 实际产量，设备在统计周期内的实际生产数量 */
	ActualOutput int64 `bson:"actual_output" json:"actual_output"`

	/** 计划产量，设备在统计周期内的计划生产数量 */
	PlannedOutput int64 `bson:"planned_output" json:"planned_output"`

	/** 产量达成率，范围0-100+，计算公式：实际产量/计划产量×100% */
	OutputRate float64 `bson:"output_rate" json:"output_rate"`

	// ===== 班次级别统计 =====

	/** 班次统计列表，包含各个班次的独立统计数据 */
	ShiftStatistics []ShiftStatistics `bson:"shift_statistics" json:"shift_statistics"`

	// ===== 元数据 =====

	/** 记录创建时间，用于数据审计和版本控制 */
	CreatedAt time.Time `bson:"created_at" json:"created_at"`

	/** 记录最后更新时间，用于数据同步和缓存失效 */
	UpdatedAt time.Time `bson:"updated_at" json:"updated_at"`
}

// ShiftStatistics 班次统计
type ShiftStatistics struct {
	ShiftName       string  `bson:"shift_name" json:"shift_name"`             // 班次名称
	StartTime       string  `bson:"start_time" json:"start_time"`             // 开始时间 HH:MM
	EndTime         string  `bson:"end_time" json:"end_time"`                 // 结束时间 HH:MM
	TotalTime       int64   `bson:"total_time" json:"total_time"`             // 班次总时间（秒）
	RunningTime     int64   `bson:"running_time" json:"running_time"`         // 运行时间（秒）
	IdleTime        int64   `bson:"idle_time" json:"idle_time"`               // 空闲时间（秒）
	FaultTime       int64   `bson:"fault_time" json:"fault_time"`             // 故障时间（秒）
	MaintenanceTime int64   `bson:"maintenance_time" json:"maintenance_time"` // 维护时间（秒）
	UtilizationRate float64 `bson:"utilization_rate" json:"utilization_rate"` // 班次利用率
	ActualOutput    int64   `bson:"actual_output" json:"actual_output"`       // 实际产量
	PlannedOutput   int64   `bson:"planned_output" json:"planned_output"`     // 计划产量
	OutputRate      float64 `bson:"output_rate" json:"output_rate"`           // 产量达成率
}

// DailyStatisticsSummary 每日统计汇总
type DailyStatisticsSummary struct {
	Date               string  `json:"date"`                 // 日期
	TotalDevices       int     `json:"total_devices"`        // 设备总数
	TotalTime          int64   `json:"total_time"`           // 总时间
	TotalRunningTime   int64   `json:"total_running_time"`   // 总运行时间
	TotalIdleTime      int64   `json:"total_idle_time"`      // 总空闲时间
	TotalFaultTime     int64   `json:"total_fault_time"`     // 总故障时间
	AvgUtilizationRate float64 `json:"avg_utilization_rate"` // 平均利用率
	TotalActualOutput  int64   `json:"total_actual_output"`  // 总实际产量
	TotalPlannedOutput int64   `json:"total_planned_output"` // 总计划产量
	AvgOutputRate      float64 `json:"avg_output_rate"`      // 平均产量达成率
}

// WorkTimeSettings 工作时间设置
type WorkTimeSettings struct {
	ID              primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	DayStartTime    string             `bson:"day_start_time" json:"day_start_time"`     // 每天开始时间 HH:MM
	Shifts          []ShiftSetting     `bson:"shifts" json:"shifts"`                     // 班次设置
	RestPeriods     []RestPeriod       `bson:"rest_periods" json:"rest_periods"`         // 休息时间
	UtilizationMode string             `bson:"utilization_mode" json:"utilization_mode"` // 利用率计算模式 OP1/OP2
	CreatedAt       time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt       time.Time          `bson:"updated_at" json:"updated_at"`
}

// ShiftSetting 班次设置
type ShiftSetting struct {
	Name      string `bson:"name" json:"name"`             // 班次名称
	StartTime string `bson:"start_time" json:"start_time"` // 开始时间 HH:MM
	EndTime   string `bson:"end_time" json:"end_time"`     // 结束时间 HH:MM
	Enabled   bool   `bson:"enabled" json:"enabled"`       // 是否启用
}

// RestPeriod 休息时间
type RestPeriod struct {
	Name      string `bson:"name" json:"name"`             // 休息时间名称
	StartTime string `bson:"start_time" json:"start_time"` // 开始时间 HH:MM
	EndTime   string `bson:"end_time" json:"end_time"`     // 结束时间 HH:MM
	Enabled   bool   `bson:"enabled" json:"enabled"`       // 是否启用
}

// StatisticsRequest 统计请求参数
type StatisticsRequest struct {
	StartDate string   `json:"start_date"` // 开始日期 YYYY-MM-DD
	EndDate   string   `json:"end_date"`   // 结束日期 YYYY-MM-DD
	DeviceIDs []string `json:"device_ids"` // 设备ID列表，空表示所有设备
	GroupBy   string   `json:"group_by"`   // 分组方式: daily, device, shift
}

// StatisticsResponse 统计响应
type StatisticsResponse struct {
	Summary     DailyStatisticsSummary `json:"summary"`      // 汇总统计
	DeviceStats []DeviceStatistics     `json:"device_stats"` // 设备统计列表
	DateRange   []string               `json:"date_range"`   // 日期范围
}
