# 环境变量配置指南

## 概述

本文档说明如何通过环境变量配置系统的站点信息，实现配置的外部化管理，提高系统的灵活性和可维护性。

## 修改内容

### 1. 代码修改

在 `handlers/v3/setting_handler.go` 中的 `GetSiteInfo` 函数进行了以下修改：

- 添加了 `os` 包导入，用于读取环境变量
- 将硬编码的站点信息改为从环境变量读取
- 为每个配置项提供了默认值作为后备方案

### 2. 支持的环境变量

| 环境变量名 | 描述 | 默认值 | 示例 |
|-----------|------|--------|------|
| `SITE_NAME` | 站点名称 | "abc智能制造系统" | "我的智能制造系统" |
| `SITE_LOGO` | 站点Logo URL | "" (空字符串) | "/assets/logo.png" |
| `SITE_SUPPORT` | 技术支持公司名称 | "abc科技" | "我的科技公司" |
| `SITE_SUPPORT_INFO` | 技术支持联系信息 | "技术支持: 13800000000" | "技术支持: 400-123-4567" |

## 使用方法

### 1. 本地开发环境

#### 方法一：使用 .env 文件
```bash
# 复制示例文件
cp .env.example .env

# 编辑 .env 文件
SITE_NAME=我的智能制造系统
SITE_LOGO=/assets/my-logo.png
SITE_SUPPORT=我的科技公司
SITE_SUPPORT_INFO=技术支持: 400-123-4567
```

#### 方法二：直接设置环境变量
```bash
export SITE_NAME="我的智能制造系统"
export SITE_LOGO="/assets/my-logo.png"
export SITE_SUPPORT="我的科技公司"
export SITE_SUPPORT_INFO="技术支持: 400-123-4567"
```

### 2. Docker 环境

#### Docker Run
```bash
docker run -d \
  -e SITE_NAME="我的智能制造系统" \
  -e SITE_LOGO="/assets/my-logo.png" \
  -e SITE_SUPPORT="我的科技公司" \
  -e SITE_SUPPORT_INFO="技术支持: 400-123-4567" \
  your-image:tag
```

#### Docker Compose
```yaml
services:
  server-api:
    image: your-image:tag
    environment:
      - SITE_NAME=我的智能制造系统
      - SITE_LOGO=/assets/my-logo.png
      - SITE_SUPPORT=我的科技公司
      - SITE_SUPPORT_INFO=技术支持: 400-123-4567
    # 或者使用 env_file
    env_file:
      - .env
```

### 3. Kubernetes 环境

#### 使用 ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: site-config
data:
  SITE_NAME: "我的智能制造系统"
  SITE_LOGO: "/assets/my-logo.png"
  SITE_SUPPORT: "我的科技公司"
  SITE_SUPPORT_INFO: "技术支持: 400-123-4567"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: server-api
spec:
  template:
    spec:
      containers:
      - name: server-api
        image: your-image:tag
        envFrom:
        - configMapRef:
            name: site-config
```

#### 使用 Secret（敏感信息）
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: site-secret
type: Opaque
stringData:
  SITE_SUPPORT_INFO: "技术支持: 400-123-4567"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: server-api
spec:
  template:
    spec:
      containers:
      - name: server-api
        image: your-image:tag
        envFrom:
        - secretRef:
            name: site-secret
```

## 技术优势

### 1. 配置外部化
- 将配置从代码中分离，符合十二要素应用原则
- 支持不同环境使用不同配置，无需修改代码

### 2. 安全性提升
- 敏感信息可以通过环境变量或密钥管理系统管理
- 避免在代码仓库中暴露敏感配置

### 3. 部署灵活性
- 支持多种部署方式和环境
- 便于CI/CD流程中的配置管理

### 4. 向后兼容
- 提供默认值，确保在未设置环境变量时系统正常运行
- 不影响现有的部署和使用方式

## 最佳实践

### 1. 环境变量命名
- 使用大写字母和下划线
- 添加统一的前缀（如 `SITE_`）避免冲突
- 名称要具有描述性

### 2. 默认值设置
- 为所有环境变量提供合理的默认值
- 默认值应该是安全的，不包含敏感信息

### 3. 文档维护
- 及时更新环境变量文档
- 提供清晰的示例和说明

### 4. 验证和错误处理
- 在应用启动时验证关键环境变量
- 提供清晰的错误信息和解决建议

## 测试验证

### 1. 功能测试
```bash
# 设置环境变量
export SITE_NAME="测试系统"

# 启动应用并测试API
curl http://localhost:8080/api/v3/setting/site-info
```

### 2. 默认值测试
```bash
# 清除环境变量
unset SITE_NAME SITE_LOGO SITE_SUPPORT SITE_SUPPORT_INFO

# 启动应用，验证使用默认值
curl http://localhost:8080/api/v3/setting/site-info
```

## 故障排除

### 1. 环境变量未生效
- 检查环境变量名称是否正确
- 确认环境变量在应用启动前已设置
- 验证容器或进程能够访问环境变量

### 2. 特殊字符处理
- 包含空格或特殊字符的值需要用引号包围
- 注意转义字符的处理

### 3. 配置优先级
- 环境变量 > 默认值
- 确保理解配置的优先级顺序

通过这种方式，系统的配置管理变得更加灵活和安全，符合现代应用开发的最佳实践。
