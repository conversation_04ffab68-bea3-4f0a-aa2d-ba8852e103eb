/**
 * 系统设置处理器
 *
 * 功能描述：
 * 提供系统设置相关的API处理，包括刷新设置、显示设置等配置管理功能
 * 支持设置的获取、更新和持久化存储
 *
 * 主要功能：
 * - 获取和更新系统刷新设置
 * - 获取和更新显示设置
 * - 设置数据的验证和默认值处理
 * - 数据库持久化存储
 *
 * API端点：
 * - GET /api/settings - 获取完整系统设置
 * - GET /api/settings/refresh - 获取刷新设置
 * - POST /api/settings/refresh - 更新刷新设置
 * - GET /api/settings/display - 获取显示设置
 * - POST /api/settings/display - 更新显示设置
 *
 * @package settings
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package settings

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/interfaces"
	"server-api/services"
	"shared/logger"
)

/**
 * 系统设置处理器结构体
 *
 * 功能：处理系统设置相关的HTTP请求
 *
 * 依赖服务：
 * - MonitoringService：提供API性能监控
 * - MongoAdminService：提供设置数据的持久化存储
 */
type SettingsHandler struct {
	monitoringService *services.MonitoringService
	mongoAdminService interfaces.MongoAdminServiceInterface
}

/**
 * 刷新设置结构体
 *
 * 功能：定义系统刷新相关的配置参数
 */
type RefreshSettings struct {
	AutoRefresh          bool `json:"autoRefresh"`          // 是否自动刷新
	RefreshInterval      int  `json:"refreshInterval"`      // 刷新间隔（毫秒）
	ShowRefreshIndicator bool `json:"showRefreshIndicator"` // 是否显示刷新指示器
}

/**
 * 显示设置结构体
 *
 * 功能：定义系统显示相关的配置参数
 */
type DisplaySettings struct {
	DeviceGridGap        int    `json:"deviceGridGap"`        // 设备网格间距
	DeviceRowGap         int    `json:"deviceRowGap"`         // 设备行间距
	DeviceColumnGap      int    `json:"deviceColumnGap"`      // 设备列间距
	StatusDeviceGap      int    `json:"statusDeviceGap"`      // 状态设备间距
	StatusCountDigits    int    `json:"statusCountDigits"`    // 状态计数位数
	StatusCountPadChar   string `json:"statusCountPadChar"`   // 状态计数填充字符
	FilterSignalDuration int64  `json:"filterSignalDuration"` // 过滤信号时长（秒），默认1秒
	CardContent          any    `json:"cardContent"`          // 卡片内容配置
	CardStyle            any    `json:"cardStyle"`            // 卡片样式配置
	StatusColors         any    `json:"statusColors"`         // 状态颜色配置
	StatusHistory        any    `json:"statusHistory"`        // 状态历史配置
	ProductionProgress   any    `json:"productionProgress"`   // 生产进度配置
	UtilizationDashboard any    `json:"utilizationDashboard"` // 利用率仪表板配置
}

/**
 * 系统设置结构体
 *
 * 功能：包含所有系统设置的顶级结构
 */
type SystemSettings struct {
	Refresh RefreshSettings `json:"refresh"` // 刷新设置
	Display DisplaySettings `json:"display"` // 显示设置
}

/**
 * 默认刷新设置
 */
var defaultRefreshSettings = RefreshSettings{
	AutoRefresh:          true,
	RefreshInterval:      5000, // 5秒，与缓存时间一致
	ShowRefreshIndicator: true,
}

/**
 * 默认显示设置
 */
var defaultDisplaySettings = DisplaySettings{
	DeviceGridGap:        12,
	DeviceRowGap:         12,
	DeviceColumnGap:      12,
	StatusDeviceGap:      12,
	StatusCountDigits:    2,
	StatusCountPadChar:   "0",
	FilterSignalDuration: 1, // 默认过滤1秒以下的信号
	CardContent:          nil,
	CardStyle:            nil,
	StatusColors:         nil,
	StatusHistory:        nil,
	ProductionProgress:   nil,
	UtilizationDashboard: map[string]interface{}{
		"layoutMode":          "split",
		"rankingDisplayLimit": 20,
	},
}

/**
 * 创建系统设置处理器实例
 *
 * 功能：初始化系统设置处理器，注入必要的服务依赖
 *
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {interfaces.MongoAdminServiceInterface} mongoAdminService - MongoDB管理服务实例
 * @returns {*SettingsHandler} 系统设置处理器实例
 *
 * @example
 * handler := settings.NewSettingsHandler(monitoringService, mongoAdminService)
 */
func NewSettingsHandler(monitoringService *services.MonitoringService, mongoAdminService interfaces.MongoAdminServiceInterface) *SettingsHandler {
	return &SettingsHandler{
		monitoringService: monitoringService,
		mongoAdminService: mongoAdminService,
	}
}

/**
 * 获取完整系统设置
 *
 * 功能：获取包含刷新设置和显示设置的完整系统配置
 *
 * HTTP方法：GET
 * 路径：/api/settings
 *
 * 响应格式：
 * {
 *   "refresh": {
 *     "autoRefresh": true,
 *     "refreshInterval": 5000,
 *     "showRefreshIndicator": true
 *   },
 *   "display": {
 *     "deviceGridGap": 12,
 *     "deviceRowGap": 12,
 *     ...
 *   }
 * }
 *
 * 状态码：
 * - 200: 获取成功
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/settings
 */
func (h *SettingsHandler) GetSettings(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/settings", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debugf("📊 Getting system settings")

	// 构建系统设置响应
	settings := SystemSettings{
		Refresh: defaultRefreshSettings,
		Display: defaultDisplaySettings,
	}

	logger.Debugf("✅ Retrieved system settings")
	c.JSON(http.StatusOK, settings)
}

/**
 * 获取刷新设置
 *
 * 功能：获取系统的刷新设置，包括自动刷新开关、刷新间隔等
 *
 * HTTP方法：GET
 * 路径：/api/settings/refresh
 *
 * 响应格式：
 * {
 *   "autoRefresh": true,
 *   "refreshInterval": 5000,
 *   "showRefreshIndicator": true
 * }
 *
 * 状态码：
 * - 200: 获取成功
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/settings/refresh
 */
func (h *SettingsHandler) GetRefreshSettings(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/settings/refresh", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debugf("📊 Getting refresh settings from database")

	// 从数据库获取设置
	var settings RefreshSettings
	if h.mongoAdminService != nil {
		dbSettings, err := h.mongoAdminService.GetSettings("refresh")
		if err != nil {
			logger.Errorf("❌ Failed to get refresh settings from database: %v", err)
			// 使用默认设置
			settings = defaultRefreshSettings
		} else if dbSettings != nil {
			// 解析数据库中的设置
			settingsData := dbSettings.Settings
			settings.AutoRefresh = getBoolFromMap(settingsData, "autorefresh", defaultRefreshSettings.AutoRefresh)
			settings.RefreshInterval = getIntFromMap(settingsData, "refreshinterval", defaultRefreshSettings.RefreshInterval)
			settings.ShowRefreshIndicator = getBoolFromMap(settingsData, "showrefreshindicator", defaultRefreshSettings.ShowRefreshIndicator)
			logger.Debugf("✅ Retrieved refresh settings from database: %+v", settings)
		} else {
			// 数据库中没有设置，使用默认值
			logger.Debugf("📊 No refresh settings in database, using defaults")
			settings = defaultRefreshSettings
		}
	} else {
		// 没有数据库连接，使用默认设置
		logger.Debugf("📊 No database connection, using default refresh settings")
		settings = defaultRefreshSettings
	}

	c.JSON(http.StatusOK, settings)
}

/**
 * 更新刷新设置
 *
 * 功能：更新系统的刷新设置并保存到数据库
 *
 * HTTP方法：POST
 * 路径：/api/settings/refresh
 *
 * 请求体：
 * {
 *   "autoRefresh": true,
 *   "refreshInterval": 5000,
 *   "showRefreshIndicator": true
 * }
 *
 * 响应格式：
 * {
 *   "autoRefresh": true,
 *   "refreshInterval": 5000,
 *   "showRefreshIndicator": true
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 500: 保存失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/settings/refresh
 * Body: {"autoRefresh": true, "refreshInterval": 3000, "showRefreshIndicator": false}
 */
func (h *SettingsHandler) UpdateRefreshSettings(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/settings/refresh", "POST", c.Writer.Status(), duration)
		}
	}()

	var settings RefreshSettings
	if err := c.ShouldBindJSON(&settings); err != nil {
		logger.Errorf("❌ Invalid refresh settings request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📊 Updating refresh settings: %+v", settings)

	// 验证设置值
	if settings.RefreshInterval < 1000 {
		settings.RefreshInterval = 1000 // 最小1秒
	}
	if settings.RefreshInterval > 60000 {
		settings.RefreshInterval = 60000 // 最大60秒
	}

	// 保存到数据库
	if h.mongoAdminService != nil {
		err := h.mongoAdminService.SaveSettings("refresh", settings)
		if err != nil {
			logger.Errorf("❌ Failed to save refresh settings to database: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "保存设置失败", "details": err.Error()})
			return
		}
		logger.Debugf("✅ Refresh settings saved to database: %+v", settings)
	} else {
		logger.Warnf("⚠️ No database connection, settings not persisted")
	}

	c.JSON(http.StatusOK, settings)
}

/**
 * 获取显示设置
 *
 * 功能：获取系统的显示设置，包括布局、样式等配置
 *
 * HTTP方法：GET
 * 路径：/api/settings/display
 *
 * 响应格式：
 * {
 *   "deviceGridGap": 12,
 *   "deviceRowGap": 12,
 *   "deviceColumnGap": 12,
 *   "statusDeviceGap": 12,
 *   "statusCountDigits": 2,
 *   "statusCountPadChar": "0",
 *   "cardContent": null,
 *   "cardStyle": null,
 *   "statusColors": null,
 *   "statusHistory": null,
 *   "productionProgress": null,
 *   "utilizationDashboard": {
 *     "layoutMode": "split",
 *     "rankingDisplayLimit": 20
 *   }
 * }
 *
 * 状态码：
 * - 200: 获取成功
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/settings/display
 */
func (h *SettingsHandler) GetDisplaySettings(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/settings/display", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debugf("📊 Getting display settings from database")

	// 从数据库获取设置
	var settings DisplaySettings
	if h.mongoAdminService != nil {
		dbSettings, err := h.mongoAdminService.GetSettings("display")
		if err != nil {
			logger.Errorf("❌ Failed to get display settings from database: %v", err)
			// 使用默认设置
			settings = defaultDisplaySettings
		} else if dbSettings != nil {
			// 解析数据库中的设置
			settingsData := dbSettings.Settings
			settings.DeviceGridGap = getIntFromMap(settingsData, "devicegridgap", defaultDisplaySettings.DeviceGridGap)
			settings.DeviceRowGap = getIntFromMap(settingsData, "devicerowgap", defaultDisplaySettings.DeviceRowGap)
			settings.DeviceColumnGap = getIntFromMap(settingsData, "devicecolumngap", defaultDisplaySettings.DeviceColumnGap)
			settings.StatusDeviceGap = getIntFromMap(settingsData, "statusdevicegap", defaultDisplaySettings.StatusDeviceGap)
			settings.StatusCountDigits = getIntFromMap(settingsData, "statuscountdigits", defaultDisplaySettings.StatusCountDigits)
			settings.StatusCountPadChar = getStringFromMap(settingsData, "statuscountpadchar", defaultDisplaySettings.StatusCountPadChar)
			settings.FilterSignalDuration = int64(getIntFromMap(settingsData, "filtersignalduration", int(defaultDisplaySettings.FilterSignalDuration)))
			settings.CardContent = settingsData["cardcontent"]
			settings.CardStyle = settingsData["cardstyle"]
			settings.StatusColors = settingsData["statuscolors"]
			settings.StatusHistory = settingsData["statushistory"]
			settings.ProductionProgress = settingsData["productionprogress"]
			settings.UtilizationDashboard = settingsData["utilizationdashboard"]
			logger.Debugf("✅ Retrieved display settings from database: %+v", settings)
		} else {
			// 数据库中没有设置，使用默认值
			logger.Debugf("📊 No display settings in database, using defaults")
			settings = defaultDisplaySettings
		}
	} else {
		// 没有数据库连接，使用默认设置
		logger.Debugf("📊 No database connection, using default display settings")
		settings = defaultDisplaySettings
	}

	c.JSON(http.StatusOK, settings)
}

/**
 * 更新显示设置
 *
 * 功能：更新系统的显示设置并保存到数据库
 *
 * HTTP方法：POST
 * 路径：/api/settings/display
 *
 * 请求体：DisplaySettings结构体
 *
 * 响应格式：更新后的DisplaySettings
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 500: 保存失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/settings/display
 * Body: {"deviceGridGap": 16, "deviceRowGap": 16, ...}
 */
func (h *SettingsHandler) UpdateDisplaySettings(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/settings/display", "POST", c.Writer.Status(), duration)
		}
	}()

	var settings DisplaySettings
	if err := c.ShouldBindJSON(&settings); err != nil {
		logger.Errorf("❌ Invalid display settings request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📊 Updating display settings: %+v", settings)

	// 验证设置值
	if settings.DeviceGridGap < 0 {
		settings.DeviceGridGap = 0
	}
	if settings.DeviceRowGap < 0 {
		settings.DeviceRowGap = 0
	}
	if settings.DeviceColumnGap < 0 {
		settings.DeviceColumnGap = 0
	}
	if settings.StatusDeviceGap < 0 {
		settings.StatusDeviceGap = 0
	}
	if settings.StatusCountDigits < 1 {
		settings.StatusCountDigits = 1
	}
	if settings.StatusCountDigits > 10 {
		settings.StatusCountDigits = 10
	}

	// 保存到数据库
	if h.mongoAdminService != nil {
		err := h.mongoAdminService.SaveSettings("display", settings)
		if err != nil {
			logger.Errorf("❌ Failed to save display settings to database: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "保存设置失败", "details": err.Error()})
			return
		}
		logger.Debugf("✅ Display settings saved to database: %+v", settings)
	} else {
		logger.Warnf("⚠️ No database connection, settings not persisted")
	}

	c.JSON(http.StatusOK, settings)
}

/**
 * 辅助函数：从 map 中安全获取 bool 值
 *
 * 功能：从map中获取布尔值，如果不存在或类型不匹配则返回默认值
 *
 * @param {map[string]interface{}} data - 数据map
 * @param {string} key - 键名
 * @param {bool} defaultValue - 默认值
 * @returns {bool} 获取到的布尔值或默认值
 */
func getBoolFromMap(data map[string]interface{}, key string, defaultValue bool) bool {
	if value, exists := data[key]; exists {
		if boolValue, ok := value.(bool); ok {
			return boolValue
		}
	}
	return defaultValue
}

/**
 * 辅助函数：从 map 中安全获取 int 值
 *
 * 功能：从map中获取整数值，支持多种数值类型转换，如果不存在或类型不匹配则返回默认值
 *
 * @param {map[string]interface{}} data - 数据map
 * @param {string} key - 键名
 * @param {int} defaultValue - 默认值
 * @returns {int} 获取到的整数值或默认值
 */
func getIntFromMap(data map[string]interface{}, key string, defaultValue int) int {
	if value, exists := data[key]; exists {
		switch v := value.(type) {
		case int:
			return v
		case int32:
			return int(v)
		case int64:
			return int(v)
		case float64:
			return int(v)
		case float32:
			return int(v)
		}
	}
	return defaultValue
}

/**
 * 辅助函数：从 map 中安全获取 string 值
 *
 * 功能：从map中获取字符串值，如果不存在或类型不匹配则返回默认值
 *
 * @param {map[string]interface{}} data - 数据map
 * @param {string} key - 键名
 * @param {string} defaultValue - 默认值
 * @returns {string} 获取到的字符串值或默认值
 */
func getStringFromMap(data map[string]interface{}, key string, defaultValue string) string {
	if value, exists := data[key]; exists {
		if stringValue, ok := value.(string); ok {
			return stringValue
		}
	}
	return defaultValue
}
