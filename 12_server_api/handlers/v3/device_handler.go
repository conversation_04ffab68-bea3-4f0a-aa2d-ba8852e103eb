package handlersV3

import (
	"net/http"
	"server-api/models"
	"server-api/services"
	"server-api/utils"
	"shared/logger"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type DeviceHandlerV3 struct {
	dataService       *services.DataService
	workTimeService   *services.WorkTimeService
	statisticsService *services.StatisticsService
	monitoringService *services.MonitoringService
}

func NewDeviceHandlerV3(
	dataService *services.DataService,
	workTimeService *services.WorkTimeService,
	statisticsService *services.StatisticsService,
	monitoringService *services.MonitoringService,
) *DeviceHandlerV3 {
	return &DeviceHandlerV3{
		dataService:       dataService,
		workTimeService:   workTimeService,
		statisticsService: statisticsService,
		monitoringService: monitoringService,
	}
}

// GetDevices 获取设备列表
// V3版本的设备列表API，使用统一的ResponseModel格式
func (h *DeviceHandlerV3) GetDevices(c *gin.Context) {
	// 实现V3版本的机器页面数据获取逻辑
	response := ResponseModel{
		Success: true,
		Message: "V3版本的机器页面数据获取成功",
		Data: gin.H{
			"devices":       []interface{}{}, // 设备列表
			"status_counts": gin.H{},         // 状态统计
			"company_info":  gin.H{},         // 公司信息
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetDeviceUtilizationByDate 获取设备利用率统计
// V3版本的设备利用率API，使用统一的ResponseModel格式
// 路径: /api/v3/devices/{id}/utilization
func (h *DeviceHandlerV3) GetDeviceUtilizationByDate(c *gin.Context) {
	date := c.Query("date")
	useWorkTime := c.Query("use_work_time") == "true"
	deviceID := c.Param("id")

	response := ResponseModel{
		Success: true,
		Message: "获取设备利用率统计成功",
		Data: gin.H{
			"device_id":      deviceID,
			"date":           date,
			"use_work_time":  useWorkTime,
			"utilization":    0.0,     // 利用率百分比
			"running_time":   0,       // 运行时间（秒）
			"total_time":     0,       // 总时间（秒）
			"status_summary": gin.H{}, // 状态汇总
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *DeviceHandlerV3) getDefaultWorkTimeSettings() *models.WorkTimeSettings {
	return &models.WorkTimeSettings{
		DayStartTime: "00:00", // 根据用户要求，每天开始时间设为 00:00
		Shifts: []models.ShiftSetting{
			{Name: "早班", StartTime: "00:00", EndTime: "08:00", Enabled: true},
			{Name: "中班", StartTime: "08:00", EndTime: "16:00", Enabled: true},
			{Name: "晚班", StartTime: "16:00", EndTime: "00:00", Enabled: true},
		},
		RestPeriods: []models.RestPeriod{
			{Name: "午休", StartTime: "12:00", EndTime: "13:00", Enabled: true},
			{Name: "晚餐", StartTime: "18:00", EndTime: "19:00", Enabled: false},
		},
		UtilizationMode: "OP1", // 24小时制
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

func (h *DeviceHandlerV3) GetDeviceStatusHistoryByDate(c *gin.Context) {
	startTime := time.Now()

	// 🚨 添加非常明显的调试日志
	deviceID := c.Param("id")
	dateParam := c.Query("date")
	useWorkTime := c.DefaultQuery("use_work_time", "true") == "true"

	logger.Infof("🚨🚨🚨 GetDeviceStatusHistoryV2 API CALLED! Device: %s, Date: %s, UseWorkTime: %v 🚨🚨🚨", deviceID, dateParam, useWorkTime)

	// 记录API调用
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/devices/:id/status-history", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debugf("📊 GetDeviceStatusHistoryV2 called for device: %s, date: %s, use_work_time: %v", deviceID, dateParam, useWorkTime)

	// 获取工作时间设置（始终尝试获取，用于确定时间段划分）
	var workTimeSettings *models.WorkTimeSettings
	var err error
	if h.workTimeService != nil {
		workTimeSettings, err = h.workTimeService.GetWorkTimeSettings()
		if err != nil {
			logger.Warnf("⚠️ Failed to get work time settings: %v", err)
			workTimeSettings = h.getDefaultWorkTimeSettings() // 使用默认工作时间设置，每天开始时间 00:00
			logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
		}
	} else {
		logger.Warn("⚠️ WorkTimeService is not initialized, using default work time settings")
		workTimeSettings = h.getDefaultWorkTimeSettings() // 添加默认工作时间设置，每天开始时间 00:00
		logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
	}

	// 确定查询日期
	var date string
	if dateParam != "" {
		date = dateParam
	} else {
		// 如果没有提供日期，使用当前日期
		date = time.Now().Format("2006-01-02")
	}

	logger.Debugf("📅 Query date determined: %s", date)

	deviceInfo, err := h.dataService.GetDeviceByDeviceId(deviceID)

	if err != nil {
		logger.Errorf("❌ Failed to get device info from MongoDB: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备信息失败",
			Error:   err.Error(),
		})
		return
	}

	// 获取设备状态历史数据
	var statusHistory []models.DeviceStatusHistory
	statusHistory, err = h.dataService.GetDeviceStatusHistoryByDate(deviceID, date, workTimeSettings)
	utilizationData := h.dataService.GenerateUtilizationData(date, statusHistory, workTimeSettings)

	utilizationData.Device = *deviceInfo

	// TODO: 检查 redis 是否存在, 存在直接返回 redis 数据
	// TODO: key = "time_slots_" + device_id + date + start_time

	// 生成时间段并预处理数据
	timeSlots := h.dataService.GenerateTimeSlotDataWithPreprocessing(statusHistory, workTimeSettings, date)

	// 🔧 为前端兼容性添加扁平的statusHistory数组
	// 从time_slots中提取所有records作为statusHistory
	var flatStatusHistory []models.TimeSlotStatusRecord
	for _, slot := range timeSlots {
		flatStatusHistory = append(flatStatusHistory, slot.Records...)
	}

	// TODO: 保存到 redis
	// TODO: 过期规则

	// 构建响应（包含前端期望的statusHistory字段）
	response := map[string]interface{}{
		"device_info":     deviceInfo,
		"time_slots":      timeSlots,
		"status_history":  flatStatusHistory, // 前端期望的扁平数组
		"date":            date,
		"utilization":     utilizationData,
		"total":           len(statusHistory),
		"worktime_config": workTimeSettings,
	}

	logger.Debugf("✅ Retrieved status history V2: %d records, %d time slots", len(statusHistory), len(timeSlots))
	c.JSON(http.StatusOK, response)
}

func (h *DeviceHandlerV3) GetAllDevicesUtilizationByDate(c *gin.Context) {

	startTime := time.Now()

	dateParam := c.Query("date")
	useWorkTime := c.DefaultQuery("use_work_time", "true") == "true"
	pageParam := c.DefaultQuery("page", "1")
	pageSizeParam := c.DefaultQuery("page_size", "99999")
	var page, pageSize int
	var err error
	if page, err = strconv.Atoi(pageParam); err != nil || page < 1 {
		page = 1
	}
	if pageSize, err = strconv.Atoi(pageSizeParam); err != nil || pageSize < 1 {
		pageSize = 99999
	}

	// 记录API调用
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/devices/:id/status-history", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取工作时间设置（始终尝试获取，用于确定时间段划分）
	var workTimeSettings *models.WorkTimeSettings

	if h.workTimeService != nil {
		workTimeSettings, err = h.workTimeService.GetWorkTimeSettings()
		if err != nil {
			logger.Warnf("⚠️ Failed to get work time settings: %v", err)
			workTimeSettings = h.getDefaultWorkTimeSettings() // 使用默认工作时间设置，每天开始时间 00:00
			logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
		}
	} else {
		logger.Warn("⚠️ WorkTimeService is not initialized, using default work time settings")
		workTimeSettings = h.getDefaultWorkTimeSettings() // 添加默认工作时间设置，每天开始时间 00:00
		logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
	}

	// 确定查询日期
	var date string
	if dateParam != "" {
		date = dateParam
	} else {
		// 如果没有提供日期，使用当前日期
		date = time.Now().Format("2006-01-02")
	}

	logger.Debugf("📅 Query date determined: %s", date)

	// 获取所有设备列表
	devices, err := h.dataService.GetDeviceList(page, pageSize)

	if err != nil {
		logger.Errorf("❌ Failed to get device list: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备列表失败",
			Error:   err.Error(),
		})
		return
	}

	var utilizationDataList []models.UtilizationData
	var total_running_time int
	var total_status_time int
	var working_time float64
	var total_status_summary map[string]int64

	// 获取每台设备的利用率数据
	for _, device := range devices {
		// 获取设备状态历史数据
		var statusHistory []models.DeviceStatusHistory
		statusHistory, err = h.dataService.GetDeviceStatusHistoryByDate(device.DeviceID, date, workTimeSettings)
		utilizationData := h.dataService.GenerateUtilizationData(date, statusHistory, workTimeSettings)
		utilizationData.Device = device

		// 合计数据
		total_running_time += int(utilizationData.RunningTime)
		total_status_time += int(utilizationData.TotalTime)
		working_time = utilizationData.WorkTime

		for status, duration := range utilizationData.StatusSummary {
			if total_status_summary == nil {
				total_status_summary = make(map[string]int64)
			}
			total_status_summary[status] += duration
		}

		utilizationDataList = append(utilizationDataList, utilizationData)
	}

	// 构建响应
	total_devices := len(devices)

	// TODO: 检查是否当天
	// TODO: 检查 redis 是否存在
	utilizationZone := h.dataService.CalculateUtilizationZones(utilizationDataList)
	// TODO: 保存到 redis
	response := ResponseModel{
		Success: true,
		Message: "获取设备利用率统计成功",
		Data: gin.H{
			"last_updated":         time.Now().Format("2006-01-02 15:04:05"),
			"date":                 date,
			"use_work_time":        useWorkTime,
			"overall_utilization":  utils.FixDecimal(float64(total_running_time) / (working_time * float64(total_devices)) * 100), // 利用率百分比
			"total_running_time":   total_running_time,                                                                            // 运行时间（秒）
			"total_status_time":    total_status_time,                                                                             // 总时间（秒）
			"working_time":         working_time,                                                                                  // 工作时长（秒）
			"total_status_summary": total_status_summary,                                                                          // 状态汇总
			"device_count":         total_devices,                                                                                 // 设备总数
			"device_utilizations":  utilizationDataList,                                                                           // 设备利用率列表

			// 设备利用率区间统计
			/*
				{name: "高利用率", min_rate: 80, max_rate: 100, device_count: 0, percentage: 0, color: "#22c55e"}
				{name: "中利用率", min_rate: 50, max_rate: 80, device_count: 47, percentage: 100, color: "#f59e0b"}
				{name: "低利用率", min_rate: 0, max_rate: 50, device_count: 0, percentage: 0, color: "#ef4444"}
			*/
			"utilization_zones": utilizationZone, // 利用率区间统计

			"work_time_settings": workTimeSettings, // 工作时间设置
		},
	}

	c.JSON(http.StatusOK, response)

}

func (h *DeviceHandlerV3) GetAllDevicesUtilizationByDateRange(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	days := c.DefaultQuery("days", "7")

	// TODO: 从配置获取天数

	if len(days) > 0 {
		d := 7
		if _d, err := strconv.Atoi(days); err == nil && _d > 0 {
			d = _d
		}
		startDate = time.Now().Add(-time.Duration(d) * 24 * time.Hour).Format("2006-01-02") // 默认一周前
		endDate = time.Now().Add(-24 * time.Hour).Format("2006-01-02")                      // 默认昨天
	}

	startTime := time.Now()

	useWorkTime := c.DefaultQuery("use_work_time", "true") == "true"
	pageParam := c.DefaultQuery("page", "1")
	pageSizeParam := c.DefaultQuery("page_size", "99999")
	var page, pageSize int
	var err error
	if page, err = strconv.Atoi(pageParam); err != nil || page < 1 {
		page = 1
	}
	if pageSize, err = strconv.Atoi(pageSizeParam); err != nil || pageSize < 1 {
		pageSize = 99999
	}

	// 记录API调用
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/v3/utilization/daily", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取工作时间设置（始终尝试获取，用于确定时间段划分）
	var workTimeSettings *models.WorkTimeSettings

	if h.workTimeService != nil {
		workTimeSettings, err = h.workTimeService.GetWorkTimeSettings()
		if err != nil {
			logger.Warnf("⚠️ Failed to get work time settings: %v", err)
			workTimeSettings = h.getDefaultWorkTimeSettings() // 使用默认工作时间设置，每天开始时间 00:00
			logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
		}
	} else {
		logger.Warn("⚠️ WorkTimeService is not initialized, using default work time settings")
		workTimeSettings = h.getDefaultWorkTimeSettings() // 添加默认工作时间设置，每天开始时间 00:00
		logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
	}

	var allData []DeviceDateUtilizationEntity

	dateRange := utils.GenerateStringDateRange(startDate, endDate)

	for _, date := range dateRange {
		data, err := h.GetAllDevicesUtilizationByDateInternal(date, page, pageSize, workTimeSettings, useWorkTime)
		if err != nil {
			logger.Errorf("❌ Failed to get device utilization data for date %s: %v", date, err)
			continue
		}

		data.DateRange = dateRange
		allData = append(allData, data)
	}

	response := ResponseModel{
		Success: true,
		Message: "获取设备利用率统计成功",
		Data:    allData,
	}

	c.JSON(http.StatusOK, response)
}

func (h *DeviceHandlerV3) GetAllDevicesUtilizationByRealtime(c *gin.Context) {
	startTime := time.Now()

	dateParam := time.Now().Format("2006-01-02")
	useWorkTime := c.DefaultQuery("use_work_time", "true") == "true"
	pageParam := c.DefaultQuery("page", "1")
	pageSizeParam := c.DefaultQuery("page_size", "99999")
	var page, pageSize int
	var err error
	if page, err = strconv.Atoi(pageParam); err != nil || page < 1 {
		page = 1
	}
	if pageSize, err = strconv.Atoi(pageSizeParam); err != nil || pageSize < 1 {
		pageSize = 99999
	}

	// 记录API调用
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/v3/utilization/daily", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取工作时间设置（始终尝试获取，用于确定时间段划分）
	var workTimeSettings *models.WorkTimeSettings

	if h.workTimeService != nil {
		workTimeSettings, err = h.workTimeService.GetWorkTimeSettings()
		if err != nil {
			logger.Warnf("⚠️ Failed to get work time settings: %v", err)
			workTimeSettings = h.getDefaultWorkTimeSettings() // 使用默认工作时间设置，每天开始时间 00:00
			logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
		}
	} else {
		logger.Warn("⚠️ WorkTimeService is not initialized, using default work time settings")
		workTimeSettings = h.getDefaultWorkTimeSettings() // 添加默认工作时间设置，每天开始时间 00:00
		logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
	}

	// 确定查询日期
	var date string
	if dateParam != "" {
		date = dateParam
	} else {
		// 如果没有提供日期，使用当前日期
		date = time.Now().Format("2006-01-02")
	}

	logger.Debugf("📅 Query date determined: %s", date)

	data, err := h.GetAllDevicesUtilizationByDateInternal(date, page, pageSize, workTimeSettings, useWorkTime)

	if err != nil {
		response := ResponseModel{
			Success: false,
			Message: "获取设备利用率统计失败",
			Error:   err.Error(),
		}

		c.JSON(http.StatusOK, response)

		return
	}

	response := ResponseModel{
		Success: true,
		Message: "获取设备利用率统计成功",
		Data:    data,
	}

	c.JSON(http.StatusOK, response)

}

type DeviceDateUtilizationEntity struct {
	LastUpdated        time.Time                `json:"last_updated", omitempty" :"last_updated"`
	Date               string                   `json:"date"`
	UserWorktime       bool                     `json:"user_worktime"`
	OverallUtilization float64                  `json:"overall_utilization"`
	TotalRunningTime   int                      `json:"total_running_time"`
	TotalStatusTime    int                      `json:"total_status_time"`
	WorkingTime        float64                  `json:"working_time"`
	TotalStatusSummary map[string]int64         `json:"total_status_summary"`
	DeviceCount        int                      `json:"device_count"`
	DeviceUtilizations []models.UtilizationData `json:"device_utilizations"`
	UtilizationZones   []models.UtilizationZone `json:"utilization_zones"`
	WorkTimeSettings   *models.WorkTimeSettings `json:"work_time_settings"`
	DateRange          []string                 `json:"date_range"`
}

func (h *DeviceHandlerV3) GetAllDevicesUtilizationByDateInternal(date string, page, pageSize int, workTimeSettings *models.WorkTimeSettings, useWorkTime bool) (DeviceDateUtilizationEntity, error) {
	// 获取所有设备列表
	devices, err := h.dataService.GetDeviceList(page, pageSize)

	if err != nil {
		return DeviceDateUtilizationEntity{}, err
	}

	var utilizationDataList []models.UtilizationData
	var total_running_time int
	var total_status_time int
	var working_time float64
	var total_status_summary map[string]int64

	// 获取每台设备的利用率数据
	for _, device := range devices {
		// 获取设备状态历史数据
		var statusHistory []models.DeviceStatusHistory
		statusHistory, err = h.dataService.GetDeviceStatusHistoryByDate(device.DeviceID, date, workTimeSettings)
		utilizationData := h.dataService.GenerateUtilizationData(date, statusHistory, workTimeSettings)
		utilizationData.Device = device

		// 合计数据
		total_running_time += int(utilizationData.RunningTime)
		total_status_time += int(utilizationData.TotalTime)
		working_time = utilizationData.WorkTime

		for status, duration := range utilizationData.StatusSummary {
			if total_status_summary == nil {
				total_status_summary = make(map[string]int64)
			}
			total_status_summary[status] += duration
		}

		utilizationDataList = append(utilizationDataList, utilizationData)
	}

	// 构建响应
	total_devices := len(devices)

	// TODO: key = "utilization_zone_"  + date + “worktime settings start_time"
	// TODO: 检查 redis 是否存在, 如果存在返回 redis 数据,
	// TODO: 将数据保存到 redis, 过期时间 48小时 + 随机 10 - 60 分钟

	utilizationZone := h.dataService.CalculateUtilizationZones(utilizationDataList)

	response := DeviceDateUtilizationEntity{

		LastUpdated:        time.Now(),
		Date:               date,
		UserWorktime:       useWorkTime,
		OverallUtilization: utils.FixDecimal(float64(total_running_time) / (working_time * float64(total_devices)) * 100), // 利用率百分比
		TotalRunningTime:   total_running_time,                                                                            // 运行时间（秒）
		TotalStatusTime:    total_status_time,                                                                             // 总时间（秒）
		WorkingTime:        working_time,                                                                                  // 工作时长（秒）
		TotalStatusSummary: total_status_summary,                                                                          // 状态汇总
		DeviceCount:        total_devices,                                                                                 // 设备总数
		DeviceUtilizations: utilizationDataList,                                                                           // 设备利用率列表
		UtilizationZones:   utilizationZone,                                                                               // 利用率区间统计
		WorkTimeSettings:   workTimeSettings,                                                                              // 工作时间设置
	}

	return response, nil
}
