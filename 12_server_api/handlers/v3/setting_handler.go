package handlersV3

import (
	"net/http"
	"os"
	"server-api/services"
	"time"

	"github.com/gin-gonic/gin"
)

type SettingHandlerV3 struct {
	dataService       *services.DataService
	workTimeService   *services.WorkTimeService
	statisticsService *services.StatisticsService
	monitoringService *services.MonitoringService
}

func NewSettingHandlerV3(
	dataService *services.DataService,
	workTimeService *services.WorkTimeService,
	statisticsService *services.StatisticsService,
	monitoringService *services.MonitoringService,
) *SettingHandlerV3 {
	return &SettingHandlerV3{
		dataService:       dataService,
		workTimeService:   workTimeService,
		statisticsService: statisticsService,
		monitoringService: monitoringService,
	}
}

// GetSettings 获取系统设置
// V3版本的系统设置API，使用统一的ResponseModel格式
func (h *SettingHandlerV3) GetSettings(c *gin.Context) {
	response := ResponseModel{
		Success: true,
		Message: "获取系统设置成功",
		Data: gin.H{
			"refresh_settings": gin.H{}, // 刷新设置
			"display_settings": gin.H{}, // 显示设置
			"work_time":        gin.H{}, // 工作时间设置
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetWorktime 获取工作时间设置
// V3版本的工作时间设置API，使用统一的ResponseModel格式
func (h *SettingHandlerV3) GetWorktime(c *gin.Context) {
	/* 返回值格式:
	{
		"success": true,
		"message": "获取工作时间设置成功",
		"data": {
			"id": "6848daafc3b09d834007c5da",
			"day_start_time": "08:00",
			"shifts": [
				{
					"name": "早班",
					"start_time": "08:00",
					"end_time": "16:00",
					"enabled": true
				},
				{
					"name": "中班",
					"start_time": "16:00",
					"end_time": "00:00",
					"enabled": true
				},
				{
					"name": "晚班",
					"start_time": "00:00",
					"end_time": "08:00",
					"enabled": true
				}
			],
			"rest_periods": [
				{
					"name": "午休",
					"start_time": "12:30",
					"end_time": "13:15",
					"enabled": true
				},
				{
					"name": "晚餐",
					"start_time": "18:30",
					"end_time": "19:15",
					"enabled": true
				}
			],
			"utilization_mode": "OP2",
			"created_at": "2025-06-11T01:23:49.929Z",
			"updated_at": "2025-06-21T04:48:12.581Z"
		}
	}
	*/

	settings, err := h.workTimeService.GetWorkTimeSettingsV3()

	if err != nil {

		response := ResponseModel{
			Success: false,
			Message: "获取工作时间设置失败",
			Error:   err.Error(),
		}

		c.JSON(http.StatusInternalServerError, response)
		return
	}

	response := ResponseModel{
		Success: true,
		Message: "获取工作时间设置成功",
		Data:    settings,
	}

	c.JSON(http.StatusOK, response)
}

// GetDisplaySettings 获取显示设置
// V3版本的显示设置API，使用统一的ResponseModel格式
func (h *SettingHandlerV3) GetDisplaySettings(c *gin.Context) {
	response := ResponseModel{
		Success: true,
		Message: "获取显示设置成功",
		Data: gin.H{
			"theme":              "light",         // 主题设置
			"language":           "zh-CN",         // 语言设置
			"timezone":           "Asia/Shanghai", // 时区设置
			"date_format":        "YYYY-MM-DD",    // 日期格式
			"time_format":        "HH:mm:ss",      // 时间格式
			"chart_refresh_rate": 5,               // 图表刷新频率（秒）
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetRefreshSettings 获取刷新设置
// V3版本的刷新设置API，使用统一的ResponseModel格式
func (h *SettingHandlerV3) GetRefreshSettings(c *gin.Context) {
	response := ResponseModel{
		Success: true,
		Message: "获取刷新设置成功",
		Data: gin.H{
			"auto_refresh":     true,  // 是否自动刷新
			"refresh_interval": 30,    // 刷新间隔（秒）
			"page_refresh":     false, // 是否页面刷新
		},
	}

	c.JSON(http.StatusOK, response)
}

type SiteInfo struct {
	Name        string `json:"name"`
	Logo        string `json:"logo"`
	Support     string `json:"support"`
	SupportInfo string `json:"support_info"`
	DateTime    string `json:"datetime"`
}

// GetSiteInfo 获取站点信息
// V3版本的站点信息API，使用统一的ResponseModel格式
// 站点信息从环境变量读取，提供默认值作为后备
func (h *SettingHandlerV3) GetSiteInfo(c *gin.Context) {
	// 从环境变量读取站点信息，如果未设置则使用默认值
	siteName := os.Getenv("SITE_NAME")
	if siteName == "" {
		siteName = "abc智能制造系统" // 默认值
	}

	siteLogo := os.Getenv("SITE_LOGO")
	// Logo可以为空，不设置默认值

	siteSupport := os.Getenv("SITE_SUPPORT")
	if siteSupport == "" {
		siteSupport = "华扬科技" // 默认值
	}

	siteSupportInfo := os.Getenv("SITE_SUPPORT_INFO")
	if siteSupportInfo == "" {
		siteSupportInfo = "技术支持: 13800000000" // 默认值
	}

	siteInfo := SiteInfo{
		Name:        siteName,
		Logo:        siteLogo,
		Support:     siteSupport,
		SupportInfo: siteSupportInfo,
		DateTime:    time.Now().Format("2006-01-02 15:04:05"),
	}

	response := ResponseModel{
		Success: true,
		Message: "获取站点信息成功",
		Data:    siteInfo,
	}

	c.JSON(http.StatusOK, response)
}
