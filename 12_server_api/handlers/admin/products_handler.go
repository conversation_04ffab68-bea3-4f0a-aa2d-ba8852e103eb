/**
 * 管理员产品管理处理器
 *
 * 功能描述：
 * 处理产品管理相关的API请求，包括产品的增删改查、规格管理、分类管理等功能
 * 提供完整的产品信息维护和生产工艺支持
 *
 * 主要功能：
 * - 产品的创建、查询、更新、删除
 * - 产品规格和参数管理
 * - 产品分类和标签管理
 * - 产品与工序的关联管理
 * - 产品统计和报表功能
 *
 * API端点：
 * - POST /api/admin/products - 创建产品
 * - GET /api/admin/products - 获取产品列表
 * - GET /api/admin/products/{id} - 获取单个产品
 * - PUT /api/admin/products/{id} - 更新产品
 * - DELETE /api/admin/products/{id} - 删除产品
 *
 * @package admin
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package admin

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"server-api/interfaces"
	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 管理员产品管理处理器结构体
 *
 * 功能：处理产品管理相关的HTTP请求
 *
 * 依赖服务：
 * - MongoAdminService：提供产品数据的持久化和业务逻辑
 * - MonitoringService：提供API性能监控
 */
type ProductsHandler struct {
	mongoService interfaces.MongoAdminServiceInterface
	monitoring   *services.MonitoringService
}

/**
 * 创建管理员产品管理处理器实例
 *
 * 功能：初始化产品管理处理器，注入必要的服务依赖
 *
 * @param {interfaces.MongoAdminServiceInterface} mongoService - MongoDB管理服务实例
 * @param {*services.MonitoringService} monitoring - 监控服务实例
 * @returns {*ProductsHandler} 产品管理处理器实例
 *
 * @example
 * handler := admin.NewProductsHandler(mongoService, monitoring)
 */
func NewProductsHandler(mongoService interfaces.MongoAdminServiceInterface, monitoring *services.MonitoringService) *ProductsHandler {
	return &ProductsHandler{
		mongoService: mongoService,
		monitoring:   monitoring,
	}
}

/**
 * 创建产品处理器
 *
 * 功能：处理产品创建请求，包含完整的参数验证和业务逻辑处理
 *
 * HTTP方法：POST
 * 路径：/api/admin/products
 *
 * 请求体：
 * {
 *   "product_id": "PROD_001",
 *   "name": "产品名称",
 *   "unit": "件",
 *   "description": "产品描述",
 *   "specifications": {
 *     "material": "不锈钢",
 *     "dimensions": "100x50x20mm",
 *     "weight": "0.5kg"
 *   }
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "产品创建成功"
 * }
 *
 * 状态码：
 * - 201: 创建成功
 * - 400: 请求参数无效
 * - 500: 创建失败
 *
 * 验证规则：
 * - ProductID：产品唯一标识，必填且唯一
 * - Name：产品名称，必填
 * - Unit：计量单位，必填
 * - Description：产品描述，可选
 * - Specifications：产品规格，可选
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/admin/products
 * Body: {"product_id": "PROD_001", "name": "螺栓", "unit": "件"}
 */
func (h *ProductsHandler) CreateProduct(c *gin.Context) {
	var req models.ProductCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid product create request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("🏭 Creating product: %s", req.ProductID)

	// 转换为Product模型
	product := &models.Product{
		ProductID:      req.ProductID,
		Name:           req.Name,
		Unit:           req.Unit,
		Description:    req.Description,
		Specifications: req.Specifications,
	}

	err := h.mongoService.CreateProduct(product)
	if err != nil {
		logger.Errorf("❌ Failed to create product: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建产品失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Product %s created successfully", req.ProductID)
	c.JSON(http.StatusCreated, gin.H{"data": product, "message": "产品创建成功"})
}

/**
 * 获取产品列表处理器
 *
 * 功能：获取产品列表，支持分页查询和条件过滤
 *
 * HTTP方法：GET
 * 路径：/api/admin/products
 *
 * 查询参数：
 * - page：页码，默认1
 * - page_size：每页数量，默认20，最大100
 * - search：搜索关键词，可选
 * - status：状态过滤，可选
 * - sort_by：排序字段，默认created_at
 * - sort_desc：是否降序，默认true
 *
 * 响应格式：
 * {
 *   "data": [...],
 *   "total": 100,
 *   "page": 1,
 *   "page_size": 20,
 *   "pages": 5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/products?page=1&page_size=20&search=螺栓
 */
func (h *ProductsHandler) GetProducts(c *gin.Context) {
	params := h.parseQueryParams(c)

	logger.Debugf("🏭 Getting products with params: %+v", params)

	result, err := h.mongoService.GetProducts(params)
	if err != nil {
		logger.Errorf("❌ Failed to get products: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取产品列表失败", "details": err.Error()})
		return
	}

	// 安全的类型断言和日志记录
	if products, ok := result.Data.([]models.Product); ok {
		logger.Debugf("✅ Retrieved %d products", len(products))
	} else {
		logger.Debugf("✅ Retrieved products (unknown count)")
	}
	c.JSON(http.StatusOK, result)
}

/**
 * 获取单个产品处理器
 *
 * 功能：根据产品ID获取特定产品的详细信息
 *
 * HTTP方法：GET
 * 路径：/api/admin/products/{id}
 *
 * 路径参数：
 * - id：产品ID
 *
 * 响应格式：
 * {
 *   "data": {
 *     "product_id": "PROD_001",
 *     "name": "产品名称",
 *     "unit": "件",
 *     "description": "产品描述",
 *     "specifications": {...},
 *     ...
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 产品ID为空
 * - 404: 产品不存在
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/products/PROD_001
 */
func (h *ProductsHandler) GetProduct(c *gin.Context) {
	productID := c.Param("id")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "产品ID不能为空"})
		return
	}

	logger.Debugf("🏭 Getting product: %s", productID)

	product, err := h.mongoService.GetProduct(productID)
	if err != nil {
		logger.Errorf("❌ Failed to get product %s: %v", productID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "产品不存在", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved product: %s", productID)
	c.JSON(http.StatusOK, gin.H{"data": product})
}

/**
 * 更新产品处理器
 *
 * 功能：更新产品的基本信息和规格参数
 *
 * HTTP方法：PUT
 * 路径：/api/admin/products/{id}
 *
 * 路径参数：
 * - id：产品ID
 *
 * 请求体：ProductUpdateRequest结构体
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "产品更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效或产品ID为空
 * - 404: 产品不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/admin/products/PROD_001
 * Body: {"name": "新产品名称", "unit": "套", "description": "更新的描述"}
 */
func (h *ProductsHandler) UpdateProduct(c *gin.Context) {
	productID := c.Param("id")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "产品ID不能为空"})
		return
	}

	var req models.ProductUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid product update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("🏭 Updating product: %s", productID)

	// 先获取现有产品
	existingProduct, err := h.mongoService.GetProduct(productID)
	if err != nil {
		logger.Errorf("❌ Failed to get product %s: %v", productID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "产品不存在", "details": err.Error()})
		return
	}

	// 更新字段
	if req.Name != "" {
		existingProduct.Name = req.Name
	}
	if req.Unit != "" {
		existingProduct.Unit = req.Unit
	}
	if req.Description != "" {
		existingProduct.Description = req.Description
	}
	if req.Specifications != nil {
		existingProduct.Specifications = req.Specifications
	}

	err = h.mongoService.UpdateProduct(productID, existingProduct)
	if err != nil {
		logger.Errorf("❌ Failed to update product %s: %v", productID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新产品失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Product %s updated successfully", productID)
	c.JSON(http.StatusOK, gin.H{"data": existingProduct, "message": "产品更新成功"})
}

/**
 * 删除产品处理器
 *
 * 功能：删除指定的产品
 *
 * HTTP方法：DELETE
 * 路径：/api/admin/products/{id}
 *
 * 路径参数：
 * - id：产品ID
 *
 * 响应格式：
 * {
 *   "message": "产品删除成功"
 * }
 *
 * 状态码：
 * - 200: 删除成功
 * - 400: 产品ID为空
 * - 500: 删除失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * DELETE /api/admin/products/PROD_001
 */
func (h *ProductsHandler) DeleteProduct(c *gin.Context) {
	productID := c.Param("id")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "产品ID不能为空"})
		return
	}

	logger.Debugf("🏭 Deleting product: %s", productID)

	err := h.mongoService.DeleteProduct(productID)
	if err != nil {
		logger.Errorf("❌ Failed to delete product %s: %v", productID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除产品失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Product %s deleted successfully", productID)
	c.JSON(http.StatusOK, gin.H{"message": "产品删除成功"})
}

/**
 * 解析通用查询参数
 *
 * 功能：解析HTTP请求中的查询参数，设置默认值和验证范围
 *
 * @param {*gin.Context} c - Gin上下文对象
 * @returns {*models.QueryParams} 解析后的查询参数
 */
func (h *ProductsHandler) parseQueryParams(c *gin.Context) *models.QueryParams {
	params := &models.QueryParams{}

	if page, err := strconv.Atoi(c.DefaultQuery("page", "1")); err == nil && page > 0 {
		params.Page = page
	} else {
		params.Page = 1
	}

	if pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "20")); err == nil && pageSize > 0 && pageSize <= 100 {
		params.PageSize = pageSize
	} else {
		params.PageSize = 20
	}

	params.Search = c.Query("search")
	params.Status = c.Query("status")
	params.SortBy = c.DefaultQuery("sort_by", "created_at")
	params.SortDesc = c.DefaultQuery("sort_desc", "true") == "true"

	return params
}
