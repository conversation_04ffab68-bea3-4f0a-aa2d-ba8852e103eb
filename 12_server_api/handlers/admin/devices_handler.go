/**
 * 管理员设备管理处理器
 *
 * 功能描述：
 * 处理设备管理相关的API请求，包括设备的增删改查、配置管理、状态控制等功能
 * 为数据采集服务提供设备配置，支持设备的全生命周期管理
 *
 * 主要功能：
 * - 设备的创建、查询、更新、删除
 * - 设备配置管理和状态控制
 * - 设备列表的分页查询和过滤
 * - 为fanuc_v2等采集服务提供设备配置
 * - 公开设备配置API（无需认证）
 *
 * API端点：
 * - POST /api/admin/devices - 创建设备
 * - GET /api/admin/devices - 获取设备列表
 * - GET /api/admin/devices/configs - 获取设备配置
 * - GET /api/admin/devices/{id} - 获取单个设备
 * - PUT /api/admin/devices/{id} - 更新设备
 * - DELETE /api/admin/devices/{id} - 删除设备
 * - GET /api/public/devices/configs - 获取公开设备配置
 *
 * @package admin
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package admin

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"server-api/interfaces"
	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 管理员设备管理处理器结构体
 *
 * 功能：处理设备管理相关的HTTP请求
 *
 * 依赖服务：
 * - MongoAdminService：提供设备数据的持久化和业务逻辑
 * - MonitoringService：提供API性能监控
 */
type DevicesHandler struct {
	mongoService interfaces.MongoAdminServiceInterface
	monitoring   *services.MonitoringService
}

/**
 * 创建管理员设备管理处理器实例
 *
 * 功能：初始化设备管理处理器，注入必要的服务依赖
 *
 * @param {interfaces.MongoAdminServiceInterface} mongoService - MongoDB管理服务实例
 * @param {*services.MonitoringService} monitoring - 监控服务实例
 * @returns {*DevicesHandler} 设备管理处理器实例
 *
 * @example
 * handler := admin.NewDevicesHandler(mongoService, monitoring)
 */
func NewDevicesHandler(mongoService interfaces.MongoAdminServiceInterface, monitoring *services.MonitoringService) *DevicesHandler {
	return &DevicesHandler{
		mongoService: mongoService,
		monitoring:   monitoring,
	}
}

/**
 * 创建设备处理器
 *
 * 功能：处理设备创建请求，包含完整的参数验证和业务逻辑处理
 *
 * HTTP方法：POST
 * 路径：/api/admin/devices
 *
 * 请求体：
 * {
 *   "device_id": "FANUC_001",
 *   "name": "FANUC CNC机床#1",
 *   "category": "CNC",
 *   "location": "A区",
 *   "ip": "*************",
 *   "port": 8193,
 *   "enabled": true,
 *   "auto_start": true,
 *   "collect_interval": 1000,
 *   "timeout": 5000,
 *   "retry_count": 3,
 *   "retry_delay": 1000,
 *   "description": "主要生产线CNC机床"
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "设备创建成功"
 * }
 *
 * 状态码：
 * - 201: 创建成功
 * - 400: 请求参数无效
 * - 500: 创建失败
 *
 * 验证规则：
 * - DeviceID：设备唯一标识，必填且唯一
 * - Name：设备名称，必填
 * - IP：设备IP地址，必填且格式正确
 * - Port：设备端口，必填且在有效范围内
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/admin/devices
 * Body: {"device_id": "FANUC_001", "name": "CNC机床", "ip": "*************", "port": 8193}
 */
func (h *DevicesHandler) CreateDevice(c *gin.Context) {
	var req models.DeviceCreateRequest

	// 第一步：解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid device create request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数无效",
			"details": err.Error(),
		})
		return
	}

	// 第二步：构建设备模型
	device := &models.Device{
		DeviceID:        req.DeviceID,
		Name:            req.Name,
		DataType:        req.DataType,
		Location:        req.Location,
		Status:          models.DeviceStatusActive, // 新设备默认为活跃状态
		SortOrder:       req.SortOrder,
		Brand:           req.Brand,
		Model:           req.Model,
		IP:              req.IP,
		Port:            req.Port,
		Enabled:         req.Enabled,
		AutoStart:       req.AutoStart,
		CollectInterval: req.CollectInterval,
		Timeout:         req.Timeout,
		RetryCount:      req.RetryCount,
		RetryDelay:      req.RetryDelay,
		Description:     req.Description,
	}

	// 第三步：调用业务服务创建设备
	err := h.mongoService.CreateDevice(device)
	if err != nil {
		logger.Errorf("Failed to create device: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "创建设备失败",
			"details": err.Error(),
		})
		return
	}

	// 第四步：构建成功响应
	c.JSON(http.StatusCreated, gin.H{
		"data":    device,
		"message": "设备创建成功",
	})
}

/**
 * 获取设备列表处理器
 *
 * 功能：获取设备列表，支持分页查询和条件过滤
 *
 * HTTP方法：GET
 * 路径：/api/admin/devices
 *
 * 查询参数：
 * - page：页码，默认1
 * - page_size：每页数量，默认20，最大99999
 * - search：搜索关键词，可选
 * - status：状态过滤，可选
 * - sort_by：排序字段，默认created_at
 * - sort_desc：是否降序，默认true
 *
 * 响应格式：
 * {
 *   "data": [...],
 *   "total": 100,
 *   "page": 1,
 *   "page_size": 20,
 *   "pages": 5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/devices?page=1&page_size=20&search=FANUC&status=active
 */
func (h *DevicesHandler) GetDevices(c *gin.Context) {
	// 解析查询参数
	params := h.parseQueryParams(c)

	// 调用业务服务获取设备列表
	result, err := h.mongoService.GetDevices(params)
	if err != nil {
		logger.Errorf("Failed to get devices: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取设备列表失败", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

/**
 * 获取设备配置列表处理器
 *
 * 功能：获取设备配置列表，专为fanuc_v2等采集服务设计
 *
 * HTTP方法：GET
 * 路径：/api/admin/devices/configs
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "data": [...],
 *   "message": "获取设备配置成功"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * 特点：
 * - 只返回启用的设备
 * - 返回fanuc_v2期望的格式
 * - 包含采集相关的配置参数
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/devices/configs
 */
func (h *DevicesHandler) GetDeviceConfigs(c *gin.Context) {
	// 只获取启用的设备
	params := &models.QueryParams{
		Page:     1,
		PageSize: 1000, // 获取所有设备
	}

	result, err := h.mongoService.GetDevices(params)
	if err != nil {
		logger.Errorf("Failed to get device configs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取设备配置失败", "details": err.Error()})
		return
	}

	// 转换为fanuc_v2需要的格式
	var deviceConfigs []map[string]interface{}

	if devices, ok := result.Data.([]models.Device); ok {
		for _, device := range devices {
			// 只返回启用的设备
			if device.Enabled {
				config := map[string]interface{}{
					"id":               device.DeviceID,
					"name":             device.Name,
					"ip":               device.IP,
					"port":             device.Port,
					"enabled":          device.Enabled,
					"auto_start":       device.AutoStart,
					"collect_interval": device.CollectInterval,
					"timeout":          device.Timeout,
					"retry_count":      device.RetryCount,
					"retry_delay":      device.RetryDelay,
					"description":      device.Description,
					"location":         device.Location,
					"brand":            device.Brand,
					"model":            device.Model,
				}
				deviceConfigs = append(deviceConfigs, config)
			}
		}
	}

	// 返回fanuc_v2期望的格式
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    deviceConfigs,
		"message": "获取设备配置成功",
	})
}

/**
 * 获取公开设备配置列表处理器
 *
 * 功能：获取公开设备配置列表，无需认证，专为fanuc_v2等采集服务设计
 *
 * HTTP方法：GET
 * 路径：/api/public/devices/configs
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "data": [...],
 *   "message": "获取设备配置成功"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * 特点：
 * - 无需认证即可访问
 * - 只返回启用的设备
 * - 包含数据类型字段
 * - 返回fanuc_v2期望的格式
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/public/devices/configs
 */
func (h *DevicesHandler) GetPublicDeviceConfigs(c *gin.Context) {
	// 只获取启用的设备
	params := &models.QueryParams{
		Page:     1,
		PageSize: 1000, // 获取所有设备
	}

	result, err := h.mongoService.GetDevices(params)
	if err != nil {
		logger.Errorf("Failed to get public device configs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取设备配置失败", "details": err.Error()})
		return
	}

	// 转换为fanuc_v2需要的格式
	var deviceConfigs []map[string]interface{}

	if devices, ok := result.Data.([]models.Device); ok {
		for _, device := range devices {
			// 只返回启用的设备
			if device.Enabled {
				config := map[string]interface{}{
					"id":               device.DeviceID,
					"name":             device.Name,
					"data_type":        device.DataType, // 添加数据类型字段
					"ip":               device.IP,
					"port":             device.Port,
					"enabled":          device.Enabled,
					"auto_start":       device.AutoStart,
					"collect_interval": device.CollectInterval,
					"timeout":          device.Timeout,
					"retry_count":      device.RetryCount,
					"retry_delay":      device.RetryDelay,
					"location":         device.Location,
					"brand":            device.Brand,
					"model":            device.Model,
				}
				deviceConfigs = append(deviceConfigs, config)
			}
		}
	}

	// 返回fanuc_v2期望的格式
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    deviceConfigs,
		"message": "获取设备配置成功",
	})
}

/**
 * 获取单个设备处理器
 *
 * 功能：根据设备ID获取特定设备的详细信息
 *
 * HTTP方法：GET
 * 路径：/api/admin/devices/{id}
 *
 * 路径参数：
 * - id：设备ID
 *
 * 响应格式：
 * {
 *   "data": {
 *     "device_id": "FANUC_001",
 *     "name": "FANUC CNC机床#1",
 *     "location": "A区",
 *     "ip": "*************",
 *     "port": 8193,
 *     ...
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 设备ID为空
 * - 404: 设备不存在
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/devices/FANUC_001
 */
func (h *DevicesHandler) GetDevice(c *gin.Context) {
	// 获取设备ID
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	// 调用业务服务获取设备信息
	device, err := h.mongoService.GetDevice(deviceID)
	if err != nil {
		logger.Errorf("Failed to get device %s: %v", deviceID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": device})
}

/**
 * 更新设备处理器
 *
 * 功能：更新设备的基本信息和配置参数
 *
 * HTTP方法：PUT
 * 路径：/api/admin/devices/{id}
 *
 * 路径参数：
 * - id：设备ID
 *
 * 请求体：DeviceUpdateRequest结构体
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "设备更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效或设备ID为空
 * - 404: 设备不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/admin/devices/FANUC_001
 * Body: {"name": "新设备名称", "location": "B区", "enabled": true}
 */
func (h *DevicesHandler) UpdateDevice(c *gin.Context) {
	// 获取设备ID
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	// 解析请求参数
	var req models.DeviceUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid device update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	// 先获取现有设备
	existingDevice, err := h.mongoService.GetDevice(deviceID)
	if err != nil {
		logger.Errorf("Failed to get device %s: %v", deviceID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在", "details": err.Error()})
		return
	}

	// 更新字段
	if req.Name != "" {
		existingDevice.Name = req.Name
	}
	if req.DataType != "" {
		existingDevice.DataType = req.DataType
	}
	if req.Location != "" {
		existingDevice.Location = req.Location
	}
	if req.Status != "" {
		existingDevice.Status = req.Status
	}
	// 排序字段允许设置为0（表示不排序或最低优先级）
	existingDevice.SortOrder = req.SortOrder
	// 对于brand和model字段，允许设置为空字符串（清空字段）
	existingDevice.Brand = req.Brand
	existingDevice.Model = req.Model
	if req.IP != "" {
		existingDevice.IP = req.IP
	}
	if req.Port > 0 {
		existingDevice.Port = req.Port
	}
	// 布尔字段直接赋值
	existingDevice.Enabled = req.Enabled
	existingDevice.AutoStart = req.AutoStart
	if req.CollectInterval > 0 {
		existingDevice.CollectInterval = req.CollectInterval
	}
	if req.Timeout > 0 {
		existingDevice.Timeout = req.Timeout
	}
	if req.RetryCount >= 0 {
		existingDevice.RetryCount = req.RetryCount
	}
	if req.RetryDelay > 0 {
		existingDevice.RetryDelay = req.RetryDelay
	}
	if req.Description != "" {
		existingDevice.Description = req.Description
	}

	// 调用业务服务更新设备
	err = h.mongoService.UpdateDevice(deviceID, existingDevice)
	if err != nil {
		logger.Errorf("Failed to update device %s: %v", deviceID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新设备失败", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": existingDevice, "message": "设备更新成功"})
}

/**
 * 删除设备处理器
 *
 * 功能：删除指定的设备
 *
 * HTTP方法：DELETE
 * 路径：/api/admin/devices/{id}
 *
 * 路径参数：
 * - id：设备ID
 *
 * 响应格式：
 * {
 *   "message": "设备删除成功"
 * }
 *
 * 状态码：
 * - 200: 删除成功
 * - 400: 设备ID为空
 * - 500: 删除失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * DELETE /api/admin/devices/FANUC_001
 */
func (h *DevicesHandler) DeleteDevice(c *gin.Context) {
	// 获取设备ID
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	// 调用业务服务删除设备
	err := h.mongoService.DeleteDevice(deviceID)
	if err != nil {
		logger.Errorf("Failed to delete device %s: %v", deviceID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除设备失败", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "设备删除成功"})
}

/**
 * 解析通用查询参数
 *
 * 功能：解析HTTP请求中的查询参数，设置默认值和验证范围
 *
 * @param {*gin.Context} c - Gin上下文对象
 * @returns {*models.QueryParams} 解析后的查询参数
 */
func (h *DevicesHandler) parseQueryParams(c *gin.Context) *models.QueryParams {
	params := &models.QueryParams{}

	if page, err := strconv.Atoi(c.DefaultQuery("page", "1")); err == nil && page > 0 {
		params.Page = page
	} else {
		params.Page = 1
	}

	if pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "99999")); err == nil && pageSize > 0 && pageSize <= 99999 {
		params.PageSize = pageSize
	} else {
		params.PageSize = 99999
	}

	params.Search = c.Query("search")
	params.Status = c.Query("status")
	params.SortBy = c.DefaultQuery("sort_by", "created_at")
	params.SortDesc = c.DefaultQuery("sort_desc", "true") == "true"

	return params
}
