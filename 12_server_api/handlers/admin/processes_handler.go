/**
 * 管理员工序管理处理器
 *
 * 功能描述：
 * 处理工序管理相关的API请求，包括工序的增删改查、流程管理、参数配置等功能
 * 提供完整的生产工序定义和工艺流程支持
 *
 * 主要功能：
 * - 工序的创建、查询、更新、删除
 * - 工序流程和顺序管理
 * - 工序时间和参数配置
 * - 工序与产品的关联管理
 * - 工序统计和分析功能
 *
 * API端点：
 * - POST /api/admin/processes - 创建工序
 * - GET /api/admin/processes - 获取工序列表
 * - GET /api/admin/processes/{id} - 获取单个工序
 * - PUT /api/admin/processes/{id} - 更新工序
 * - DELETE /api/admin/processes/{id} - 删除工序
 *
 * @package admin
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package admin

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"server-api/interfaces"
	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 管理员工序管理处理器结构体
 *
 * 功能：处理工序管理相关的HTTP请求
 *
 * 依赖服务：
 * - MongoAdminService：提供工序数据的持久化和业务逻辑
 * - MonitoringService：提供API性能监控
 */
type ProcessesHandler struct {
	mongoService interfaces.MongoAdminServiceInterface
	monitoring   *services.MonitoringService
}

/**
 * 创建管理员工序管理处理器实例
 *
 * 功能：初始化工序管理处理器，注入必要的服务依赖
 *
 * @param {interfaces.MongoAdminServiceInterface} mongoService - MongoDB管理服务实例
 * @param {*services.MonitoringService} monitoring - 监控服务实例
 * @returns {*ProcessesHandler} 工序管理处理器实例
 *
 * @example
 * handler := admin.NewProcessesHandler(mongoService, monitoring)
 */
func NewProcessesHandler(mongoService interfaces.MongoAdminServiceInterface, monitoring *services.MonitoringService) *ProcessesHandler {
	return &ProcessesHandler{
		mongoService: mongoService,
		monitoring:   monitoring,
	}
}

/**
 * 创建工序处理器
 *
 * 功能：处理工序创建请求，包含完整的参数验证和业务逻辑处理
 *
 * HTTP方法：POST
 * 路径：/api/admin/processes
 *
 * 请求体：
 * {
 *   "process_id": "PROC_001",
 *   "name": "工序名称",
 *   "product_id": "PROD_001",
 *   "duration": 120,
 *   "preparation_time": 30,
 *   "sequence": 1,
 *   "description": "工序描述"
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "工序创建成功"
 * }
 *
 * 状态码：
 * - 201: 创建成功
 * - 400: 请求参数无效
 * - 500: 创建失败
 *
 * 验证规则：
 * - ProcessID：工序唯一标识，必填且唯一
 * - Name：工序名称，必填
 * - ProductID：关联产品ID，必填
 * - Duration：工序时长（分钟），必填且大于0
 * - PreparationTime：准备时间（分钟），可选
 * - Sequence：工序顺序，必填且大于0
 * - Description：工序描述，可选
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/admin/processes
 * Body: {"process_id": "PROC_001", "name": "切削", "product_id": "PROD_001", "duration": 120, "sequence": 1}
 */
func (h *ProcessesHandler) CreateProcess(c *gin.Context) {
	var req models.ProcessCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid process create request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("⚙️ Creating process: %s", req.ProcessID)

	// 转换为Process模型
	process := &models.Process{
		ProcessID:       req.ProcessID,
		Name:            req.Name,
		ProductID:       req.ProductID,
		Duration:        req.Duration,
		PreparationTime: req.PreparationTime,
		Sequence:        req.Sequence,
		Description:     req.Description,
	}

	err := h.mongoService.CreateProcess(process)
	if err != nil {
		logger.Errorf("❌ Failed to create process: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建工序失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Process %s created successfully", req.ProcessID)
	c.JSON(http.StatusCreated, gin.H{"data": process, "message": "工序创建成功"})
}

/**
 * 获取工序列表处理器
 *
 * 功能：获取工序列表，支持分页查询和条件过滤
 *
 * HTTP方法：GET
 * 路径：/api/admin/processes
 *
 * 查询参数：
 * - page：页码，默认1
 * - page_size：每页数量，默认20，最大100
 * - search：搜索关键词，可选
 * - product_id：产品ID过滤，可选
 * - sort_by：排序字段，默认sequence
 * - sort_desc：是否降序，默认false
 *
 * 响应格式：
 * {
 *   "data": [...],
 *   "total": 100,
 *   "page": 1,
 *   "page_size": 20,
 *   "pages": 5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/processes?page=1&page_size=20&product_id=PROD_001
 */
func (h *ProcessesHandler) GetProcesses(c *gin.Context) {
	params := h.parseQueryParams(c)

	logger.Debugf("⚙️ Getting processes with params: %+v", params)

	result, err := h.mongoService.GetProcesses(params)
	if err != nil {
		logger.Errorf("❌ Failed to get processes: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取工序列表失败", "details": err.Error()})
		return
	}

	// 安全的类型断言和日志记录
	if processes, ok := result.Data.([]models.Process); ok {
		logger.Debugf("✅ Retrieved %d processes", len(processes))
	} else {
		logger.Debugf("✅ Retrieved processes (unknown count)")
	}
	c.JSON(http.StatusOK, result)
}

/**
 * 获取单个工序处理器
 *
 * 功能：根据工序ID获取特定工序的详细信息
 *
 * HTTP方法：GET
 * 路径：/api/admin/processes/{id}
 *
 * 路径参数：
 * - id：工序ID
 *
 * 响应格式：
 * {
 *   "data": {
 *     "process_id": "PROC_001",
 *     "name": "工序名称",
 *     "product_id": "PROD_001",
 *     "duration": 120,
 *     "preparation_time": 30,
 *     "sequence": 1,
 *     "description": "工序描述",
 *     ...
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 工序ID为空
 * - 404: 工序不存在
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/processes/PROC_001
 */
func (h *ProcessesHandler) GetProcess(c *gin.Context) {
	processID := c.Param("id")
	if processID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "工序ID不能为空"})
		return
	}

	logger.Debugf("⚙️ Getting process: %s", processID)

	process, err := h.mongoService.GetProcess(processID)
	if err != nil {
		logger.Errorf("❌ Failed to get process %s: %v", processID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "工序不存在", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved process: %s", processID)
	c.JSON(http.StatusOK, gin.H{"data": process})
}

/**
 * 更新工序处理器
 *
 * 功能：更新工序的基本信息和参数配置
 *
 * HTTP方法：PUT
 * 路径：/api/admin/processes/{id}
 *
 * 路径参数：
 * - id：工序ID
 *
 * 请求体：ProcessUpdateRequest结构体
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "工序更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效或工序ID为空
 * - 404: 工序不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/admin/processes/PROC_001
 * Body: {"name": "新工序名称", "duration": 150, "sequence": 2}
 */
func (h *ProcessesHandler) UpdateProcess(c *gin.Context) {
	processID := c.Param("id")
	if processID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "工序ID不能为空"})
		return
	}

	var req models.ProcessUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid process update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("⚙️ Updating process: %s", processID)

	// 先获取现有工序
	existingProcess, err := h.mongoService.GetProcess(processID)
	if err != nil {
		logger.Errorf("❌ Failed to get process %s: %v", processID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "工序不存在", "details": err.Error()})
		return
	}

	// 更新字段
	if req.Name != "" {
		existingProcess.Name = req.Name
	}
	if req.ProductID != "" {
		existingProcess.ProductID = req.ProductID
	}
	if req.Duration > 0 {
		existingProcess.Duration = req.Duration
	}
	if req.PreparationTime >= 0 { // 允许设置为0
		existingProcess.PreparationTime = req.PreparationTime
	}
	if req.Sequence > 0 {
		existingProcess.Sequence = req.Sequence
	}
	if req.Description != "" {
		existingProcess.Description = req.Description
	}

	err = h.mongoService.UpdateProcess(processID, existingProcess)
	if err != nil {
		logger.Errorf("❌ Failed to update process %s: %v", processID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新工序失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Process %s updated successfully", processID)
	c.JSON(http.StatusOK, gin.H{"data": existingProcess, "message": "工序更新成功"})
}

/**
 * 删除工序处理器
 *
 * 功能：删除指定的工序
 *
 * HTTP方法：DELETE
 * 路径：/api/admin/processes/{id}
 *
 * 路径参数：
 * - id：工序ID
 *
 * 响应格式：
 * {
 *   "message": "工序删除成功"
 * }
 *
 * 状态码：
 * - 200: 删除成功
 * - 400: 工序ID为空
 * - 500: 删除失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * DELETE /api/admin/processes/PROC_001
 */
func (h *ProcessesHandler) DeleteProcess(c *gin.Context) {
	processID := c.Param("id")
	if processID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "工序ID不能为空"})
		return
	}

	logger.Debugf("⚙️ Deleting process: %s", processID)

	err := h.mongoService.DeleteProcess(processID)
	if err != nil {
		logger.Errorf("❌ Failed to delete process %s: %v", processID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除工序失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Process %s deleted successfully", processID)
	c.JSON(http.StatusOK, gin.H{"message": "工序删除成功"})
}

/**
 * 解析通用查询参数
 *
 * 功能：解析HTTP请求中的查询参数，设置默认值和验证范围
 *
 * @param {*gin.Context} c - Gin上下文对象
 * @returns {*models.QueryParams} 解析后的查询参数
 */
func (h *ProcessesHandler) parseQueryParams(c *gin.Context) *models.QueryParams {
	params := &models.QueryParams{}

	if page, err := strconv.Atoi(c.DefaultQuery("page", "1")); err == nil && page > 0 {
		params.Page = page
	} else {
		params.Page = 1
	}

	if pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "20")); err == nil && pageSize > 0 && pageSize <= 100 {
		params.PageSize = pageSize
	} else {
		params.PageSize = 20
	}

	params.Search = c.Query("search")
	params.Status = c.Query("status")
	params.SortBy = c.DefaultQuery("sort_by", "sequence")            // 工序默认按顺序排序
	params.SortDesc = c.DefaultQuery("sort_desc", "false") == "true" // 工序默认升序

	return params
}
