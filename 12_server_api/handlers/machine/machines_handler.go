/**
 * 设备状态查询处理器
 *
 * 功能描述：
 * 处理设备状态查询相关的API请求，包括获取所有设备状态和单个设备详情
 * 提供实时设备状态信息，支持多维度查询和状态统计
 *
 * 主要功能：
 * - 获取所有设备状态列表
 * - 获取单个设备详细信息
 * - 设备状态统计和分析
 * - 数据格式转换和映射
 *
 * API端点：
 * - GET /api/machines - 获取所有设备状态
 * - GET /api/machines/{id} - 获取单个设备详情
 *
 * @package machine
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package machine

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 设备状态处理器结构体
 *
 * 功能：处理设备状态查询相关的HTTP请求
 *
 * 依赖服务：
 * - DataService：提供设备数据访问接口
 * - MonitoringService：提供API性能监控
 * - WorkTimeService：提供工作时间配置
 */
type MachinesHandler struct {
	dataService       *services.DataService
	monitoringService *services.MonitoringService
	workTimeService   *services.WorkTimeService
}

/**
 * 创建设备状态处理器实例
 *
 * 功能：初始化设备状态处理器，注入必要的服务依赖
 *
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 * @returns {*MachinesHandler} 设备状态处理器实例
 *
 * @example
 * handler := machine.NewMachinesHandler(dataService, monitoringService, workTimeService)
 */
func NewMachinesHandler(dataService *services.DataService, monitoringService *services.MonitoringService, workTimeService *services.WorkTimeService) *MachinesHandler {
	return &MachinesHandler{
		dataService:       dataService,
		monitoringService: monitoringService,
		workTimeService:   workTimeService,
	}
}

/**
 * 获取所有设备状态数据
 *
 * 功能：提供所有设备的实时状态信息，支持日期和设备ID过滤
 *
 * HTTP方法：GET
 * 路径：/api/machines
 *
 * 查询参数：
 * - date：查询日期，格式YYYY-MM-DD，默认为当前日期
 * - devices：设备ID列表，逗号分隔，为空时查询所有设备
 *
 * 响应格式：
 * {
 *   "machines": [...],      // 设备列表
 *   "statusCounts": {...},  // 状态统计
 *   "companyInfo": {...}    // 公司信息
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/machines?date=2025-06-15&devices=device1,device2
 */
func (h *MachinesHandler) GetMachines(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/machines", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析查询参数
	date := c.Query("date")
	deviceIDsParam := c.Query("devices")

	var deviceIDs []string
	if deviceIDsParam != "" {
		deviceIDs = strings.Split(deviceIDsParam, ",")
	}

	// 智能日期选择：如果没有指定日期，自动选择有数据的最近日期
	if date == "" {
		date = h.findLatestDataDate(deviceIDs)
	}

	logger.Debugf("📊 Getting machines data for date: %s, devices: %v", date, deviceIDs)

	// 从数据服务获取设备状态
	devices, err := h.dataService.GetDevicesStatus(deviceIDs, date)
	if err != nil {
		logger.Errorf("❌ Failed to get devices status: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备状态失败",
			Error:   err.Error(),
		})
		return
	}

	// 转换为机器数据格式
	machines := h.convertDevicesToMachines(devices)

	// 计算状态统计
	statusCounts := h.calculateStatusCounts(machines)

	// 获取公司信息
	companyInfo := h.getCompanyInfo()

	// 构建响应
	response := models.MachineResponse{
		Machines:     machines,
		StatusCounts: statusCounts,
		CompanyInfo:  companyInfo,
	}

	logger.Debugf("✅ Retrieved %d machines with status counts", len(machines))
	c.JSON(http.StatusOK, response)
}

/**
 * 获取单个设备状态
 *
 * 功能：获取指定设备的详细状态信息
 *
 * HTTP方法：GET
 * 路径：/api/machines/{id}
 *
 * 路径参数：
 * - id：设备ID
 *
 * 查询参数：
 * - date：查询日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "id": "device1",
 *   "brand": "FANUC",
 *   "name": "设备名称",
 *   "location": "车间位置",
 *   "model": "设备型号",
 *   "status": "production",
 *   "quantity": 100,
 *   "plan": 120,
 *   "timestamp": "2025-06-15T10:30:00Z"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 设备不存在
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/machines/device1?date=2025-06-15
 */
func (h *MachinesHandler) GetMachine(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/machines/:id", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析路径参数和查询参数
	machineID := c.Param("id")
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))

	logger.Debugf("📊 Getting machine data for ID: %s, date: %s", machineID, date)

	// 从数据服务获取单个设备状态
	devices, err := h.dataService.GetDevicesStatus([]string{machineID}, date)
	if err != nil {
		logger.Errorf("❌ Failed to get device status: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备状态失败",
			Error:   err.Error(),
		})
		return
	}

	// 检查设备是否存在
	if len(devices) == 0 {
		logger.Warnf("⚠️ Machine not found: %s", machineID)
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Success: false,
			Message: "机器不存在",
			Error:   fmt.Sprintf("Machine ID %s not found", machineID),
		})
		return
	}

	// 转换为机器数据格式
	machine := h.convertDeviceToMachine(devices[0])

	logger.Debugf("✅ Retrieved machine: %s", machineID)
	c.JSON(http.StatusOK, machine)
}

/**
 * 从MongoDB获取单台设备记录
 *
 * 功能：直接从MongoDB数据库获取设备的详细配置信息
 * 这个API专门用于获取设备的基本信息，如名称、型号、位置等
 *
 * 请求参数：
 * - id (路径参数): 设备ID
 *
 * 响应格式：
 * {
 *   "id": "设备ID",
 *   "device_id": "设备业务ID",
 *   "name": "设备名称",
 *   "brand": "设备品牌",
 *   "model": "设备型号",
 *   "location": "设备位置",
 *   "data_type": "数据类型",
 *   "sort_order": 排序顺序,
 *   "created_at": "创建时间",
 *   "updated_at": "更新时间"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 设备不存在
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/machine/test-37
 */
func (h *MachinesHandler) GetMachineFromMongoDB(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/machine/:id", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析路径参数
	deviceID := c.Param("id")

	logger.Debugf("📊 Getting machine from MongoDB for ID: %s", deviceID)

	// 检查数据服务是否可用
	if h.dataService == nil {
		logger.Error("❌ DataService not available")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "数据服务不可用",
			Error:   "DataService is not available",
		})
		return
	}

	// 从MongoDB获取设备配置信息
	device, err := h.dataService.GetDeviceConfig(deviceID)
	if err != nil {
		logger.Errorf("❌ Failed to get device from MongoDB: %v", err)

		// 检查是否是设备不存在的错误
		if strings.Contains(err.Error(), "设备未找到") || strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Success: false,
				Message: "设备不存在",
				Error:   fmt.Sprintf("Device ID %s not found in MongoDB", deviceID),
			})
			return
		}

		// 其他错误
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备信息失败",
			Error:   err.Error(),
		})
		return
	}

	// 检查设备是否为空
	if device == nil {
		logger.Warnf("⚠️ Device not found in MongoDB: %s", deviceID)
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Success: false,
			Message: "设备不存在",
			Error:   fmt.Sprintf("Device ID %s not found in MongoDB", deviceID),
		})
		return
	}

	logger.Debugf("✅ Retrieved device from MongoDB: %s (name: %s, location: %s)",
		deviceID, device.Name, device.Location)

	// 直接返回MongoDB中的设备记录
	c.JSON(http.StatusOK, device)
}

/**
 * 智能日期选择：查找最近有数据的日期
 *
 * 功能：当用户没有指定查询日期时，自动查找最近有数据的日期
 *
 * 查找策略：
 * 1. 优先查找今天的数据
 * 2. 如果今天没有数据，查找昨天的数据
 * 3. 如果昨天也没有数据，查找前天的数据
 * 4. 如果都没有数据，使用今天的日期（返回空数据）
 *
 * @param {[]string} deviceIDs - 设备ID列表
 * @returns {string} 有数据的日期，格式YYYY-MM-DD
 */
func (h *MachinesHandler) findLatestDataDate(deviceIDs []string) string {
	// 尝试今天的数据
	today := time.Now().Format("2006-01-02")
	if devices, err := h.dataService.GetDevicesStatus(deviceIDs, today); err == nil && len(devices) > 0 {
		logger.Debugf("📊 Using today's data: %s, found %d devices", today, len(devices))
		return today
	}

	// 尝试昨天的数据
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	if devices, err := h.dataService.GetDevicesStatus(deviceIDs, yesterday); err == nil && len(devices) > 0 {
		logger.Debugf("📊 Using yesterday's data: %s, found %d devices", yesterday, len(devices))
		return yesterday
	}

	// 尝试前天的数据
	dayBeforeYesterday := time.Now().AddDate(0, 0, -2).Format("2006-01-02")
	if devices, err := h.dataService.GetDevicesStatus(deviceIDs, dayBeforeYesterday); err == nil && len(devices) > 0 {
		logger.Debugf("📊 Using day before yesterday's data: %s, found %d devices", dayBeforeYesterday, len(devices))
		return dayBeforeYesterday
	}

	// 如果都没有数据，使用今天的日期
	logger.Warnf("⚠️ No data found for recent days, using today's date: %s", today)
	return today
}

/**
 * 批量转换设备状态为前端机器数据格式
 *
 * 功能：将从InfluxDB获取的设备状态数据批量转换为前端期望的机器数据格式
 *
 * @param {[]models.DeviceStatus} devices - InfluxDB设备状态数据列表
 * @returns {[]models.Machine} 转换后的前端机器数据列表
 */
func (h *MachinesHandler) convertDevicesToMachines(devices []models.DeviceStatus) []models.Machine {
	machines := make([]models.Machine, len(devices))
	for i, device := range devices {
		machines[i] = h.convertDeviceToMachine(device)
	}
	return machines
}

/**
 * 转换单个设备状态为机器数据格式
 *
 * 功能：将单个设备状态数据转换为前端期望的机器数据格式
 *
 * @param {models.DeviceStatus} device - 设备状态数据
 * @returns {models.Machine} 转换后的机器数据
 */
func (h *MachinesHandler) convertDeviceToMachine(device models.DeviceStatus) models.Machine {
	// 状态映射
	status := h.mapDeviceStatus(device.Status)

	// 获取设备配置信息
	brand := device.DeviceCode
	name := device.DeviceName
	model := device.Model
	location := device.Location

	// 尝试从MongoDB获取更详细的设备配置
	if deviceConfig := h.getDeviceConfigFromMongo(device.DeviceID); deviceConfig != nil {
		if deviceConfig.Brand != "" {
			brand = deviceConfig.Brand
		}
		if deviceConfig.Name != "" {
			name = deviceConfig.Name
		}
		if deviceConfig.Model != "" {
			model = deviceConfig.Model
		}
		if deviceConfig.Location != "" {
			location = deviceConfig.Location
		}
	}

	return models.Machine{
		ID:        device.DeviceID,
		Brand:     brand,
		Name:      name,
		Location:  location,
		Model:     model,
		Status:    status,
		Quantity:  device.Production,
		Plan:      device.Plan,
		Timestamp: device.Timestamp,
	}
}

/**
 * 从MongoDB获取设备配置信息
 *
 * 功能：根据设备ID从MongoDB中获取设备的详细配置信息
 *
 * @param {string} deviceID - 设备ID
 * @returns {*models.Device} 设备配置信息，如果未找到则返回nil
 */
func (h *MachinesHandler) getDeviceConfigFromMongo(deviceID string) *models.Device {
	if h.dataService == nil {
		return nil
	}

	device, err := h.dataService.GetDeviceConfig(deviceID)
	if err != nil {
		logger.Debugf("Failed to get device config for %s: %v", deviceID, err)
		return nil
	}

	return device
}

/**
 * 设备状态映射转换
 *
 * 功能：将InfluxDB中存储的设备状态转换为前端期望的标准状态值
 *
 * 映射规则：
 * - running → production：设备运行中，正在生产
 * - standby → idle：设备待机，空闲等待
 * - alarm → fault：设备报警，故障状态
 * - maintenance → adjusting：设备维护，调机状态
 * - offline → shutdown：设备离线，关机状态
 * - disconnected → disconnected：设备未连接
 * - stop → shutdown：设备停止，关机状态
 * - error → fault：设备错误，故障状态
 *
 * @param {string} deviceStatus - InfluxDB中的原始设备状态
 * @returns {string} 前端标准化的设备状态
 */
func (h *MachinesHandler) mapDeviceStatus(deviceStatus string) string {
	statusMap := map[string]string{
		"running":      "production",
		"standby":      "idle",
		"alarm":        "fault",
		"maintenance":  "adjusting",
		"offline":      "shutdown",
		"disconnected": "disconnected",
		"stop":         "shutdown",
		"error":        "fault",
	}

	// 大小写不敏感的状态映射
	if mappedStatus, exists := statusMap[strings.ToLower(deviceStatus)]; exists {
		return mappedStatus
	}

	// 向后兼容：检查是否已经是前端标准状态
	switch strings.ToLower(deviceStatus) {
	case "production", "idle", "fault", "adjusting", "shutdown", "disconnected":
		return strings.ToLower(deviceStatus)
	default:
		return "disconnected"
	}
}

/**
 * 计算设备状态统计
 *
 * 功能：统计各种状态的设备数量，为前端仪表板提供概览数据
 *
 * @param {[]models.Machine} machines - 机器数据列表
 * @returns {models.StatusCounts} 状态统计结果
 */
func (h *MachinesHandler) calculateStatusCounts(machines []models.Machine) models.StatusCounts {
	counts := models.StatusCounts{}

	for _, machine := range machines {
		switch machine.Status {
		case "production":
			counts.Production++
		case "idle":
			counts.Idle++
		case "fault":
			counts.Fault++
		case "adjusting":
			counts.Adjusting++
		case "shutdown":
			counts.Shutdown++
		case "disconnected":
			counts.Disconnected++
		}
	}

	return counts
}

/**
 * 获取公司信息
 *
 * 功能：返回公司基本信息和当前时间
 *
 * @returns {models.CompanyInfo} 公司信息
 */
func (h *MachinesHandler) getCompanyInfo() models.CompanyInfo {
	return models.CompanyInfo{
		Name:     "制造业数据采集系统",
		DateTime: time.Now().Format("2006-01-02 15:04:05"),
	}
}
