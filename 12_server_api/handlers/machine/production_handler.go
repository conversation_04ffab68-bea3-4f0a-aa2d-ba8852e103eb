/**
 * 生产数据处理器
 *
 * 功能描述：
 * 处理生产历史数据和生产进度查询相关的API请求
 * 提供生产数据的统计分析和历史记录查询功能
 *
 * 主要功能：
 * - 获取生产历史数据
 * - 获取生产进度信息
 * - 生产数据统计分析
 * - 历史数据处理和重构
 *
 * API端点：
 * - GET /api/production-history - 获取生产历史数据
 * - GET /api/production/progress - 获取生产进度信息
 * - POST /api/admin/process-history - 手动处理历史数据
 *
 * @package machine
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package machine

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 生产数据处理器结构体
 *
 * 功能：处理生产历史和进度查询相关的HTTP请求
 *
 * 依赖服务：
 * - DataService：提供生产数据访问接口
 * - MonitoringService：提供API性能监控
 * - WorkTimeService：提供工作时间配置
 */
type ProductionHandler struct {
	dataService       *services.DataService
	monitoringService *services.MonitoringService
	workTimeService   *services.WorkTimeService
}

/**
 * 创建生产数据处理器实例
 *
 * 功能：初始化生产数据处理器，注入必要的服务依赖
 *
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 * @returns {*ProductionHandler} 生产数据处理器实例
 *
 * @example
 * handler := machine.NewProductionHandler(dataService, monitoringService, workTimeService)
 */
func NewProductionHandler(dataService *services.DataService, monitoringService *services.MonitoringService, workTimeService *services.WorkTimeService) *ProductionHandler {
	return &ProductionHandler{
		dataService:       dataService,
		monitoringService: monitoringService,
		workTimeService:   workTimeService,
	}
}

/**
 * 获取生产历史数据
 *
 * 功能：获取指定日期的生产历史数据，包括设备状态、产品信息和生产记录
 *
 * HTTP方法：GET
 * 路径：/api/production-history
 *
 * 查询参数：
 * - date：查询日期，格式YYYY-MM-DD，默认为当前日期
 * - devices：设备ID列表，逗号分隔，为空时查询所有设备
 *
 * 响应格式：
 * {
 *   "machines": [...],          // 设备列表
 *   "products": [...],          // 产品列表
 *   "productionRecords": [...], // 生产记录
 *   "companyInfo": {...}        // 公司信息
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production-history?date=2025-06-15&devices=device1,device2
 */
func (h *ProductionHandler) GetProductionHistory(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/production-history", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析查询参数
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))
	deviceIDsParam := c.Query("devices")

	var deviceIDs []string
	if deviceIDsParam != "" {
		deviceIDs = strings.Split(deviceIDsParam, ",")
	}

	logger.Debugf("📊 Getting production history for date: %s, devices: %v", date, deviceIDs)

	// 从数据服务获取生产历史数据
	productionRecords, err := h.dataService.GetDeviceProduction(deviceIDs, date)
	if err != nil {
		logger.Errorf("❌ Failed to get production history: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取生产历史失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建响应数据
	response := map[string]interface{}{
		"date":      date,
		"records":   productionRecords,
		"total":     len(productionRecords),
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	logger.Debugf("✅ Retrieved production history with %d records", len(productionRecords))
	c.JSON(http.StatusOK, response)
}

/**
 * 获取生产进度数据
 *
 * 功能：获取指定日期的生产进度信息，包括计划完成情况和实时进度
 *
 * HTTP方法：GET
 * 路径：/api/production/progress
 *
 * 查询参数：
 * - date：查询日期，格式YYYY-MM-DD，默认为当前日期
 * - devices：设备ID列表，逗号分隔，为空时查询所有设备
 *
 * 响应格式：
 * {
 *   "date": "2025-06-15",
 *   "devices": [...],           // 设备进度列表
 *   "summary": {...},           // 进度汇总
 *   "timestamp": "..."          // 查询时间戳
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/progress?date=2025-06-15&devices=device1,device2
 */
func (h *ProductionHandler) GetProductionProgress(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/production/progress", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析查询参数
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))
	deviceIDsParam := c.Query("devices")

	var deviceIDs []string
	if deviceIDsParam != "" {
		deviceIDs = strings.Split(deviceIDsParam, ",")
	}

	logger.Debugf("📊 Getting production progress for date: %s, devices: %v", date, deviceIDs)

	// 从数据服务获取生产进度数据
	progressData, err := h.dataService.GetDeviceProduction(deviceIDs, date)
	if err != nil {
		logger.Errorf("❌ Failed to get production progress: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取生产进度失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建响应数据
	response := map[string]interface{}{
		"date":      date,
		"devices":   progressData,
		"summary":   h.calculateProgressSummary(progressData),
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	logger.Debugf("✅ Retrieved production progress for %d devices", len(progressData))
	c.JSON(http.StatusOK, response)
}

/**
 * 计算生产进度汇总信息
 *
 * 功能：根据设备进度数据计算整体的生产进度汇总
 *
 * @param {interface{}} progressData - 生产进度数据
 * @returns {map[string]interface{}} 进度汇总信息
 */
func (h *ProductionHandler) calculateProgressSummary(progressData interface{}) map[string]interface{} {
	// 这里可以根据实际的进度数据结构来计算汇总信息
	// 目前返回基本的汇总结构
	return map[string]interface{}{
		"total_devices":    0,
		"active_devices":   0,
		"total_production": 0,
		"total_plan":       0,
		"completion_rate":  0.0,
	}
}
