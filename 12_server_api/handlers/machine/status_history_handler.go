/**
 * 设备状态历史处理器
 *
 * 功能描述：
 * 处理设备状态历史查询和历史数据处理相关的API请求
 * 提供设备状态变化历史、时间线分析和历史数据重构功能
 *
 * 主要功能：
 * - 获取设备状态历史数据
 * - 手动处理历史数据
 * - 状态变化时间线分析
 * - 历史数据重构和修复
 *
 * API端点：
 * - GET /api/devices/{id}/status-history - 获取设备状态历史
 * - POST /api/admin/process-history - 手动处理历史数据
 *
 * @package machine
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package machine

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/db"
	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 设备状态历史处理器结构体
 *
 * 功能：处理设备状态历史查询和数据处理相关的HTTP请求
 *
 * 依赖服务：
 * - DataService：提供状态历史数据访问接口
 * - MonitoringService：提供API性能监控
 * - WorkTimeService：提供工作时间配置
 */
type StatusHistoryHandler struct {
	dataService       *services.DataService
	monitoringService *services.MonitoringService
	workTimeService   *services.WorkTimeService
}

/**
 * 创建设备状态历史处理器实例
 *
 * 功能：初始化设备状态历史处理器，注入必要的服务依赖
 *
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 * @returns {*StatusHistoryHandler} 设备状态历史处理器实例
 *
 * @example
 * handler := machine.NewStatusHistoryHandler(dataService, monitoringService, workTimeService)
 */
func NewStatusHistoryHandler(dataService *services.DataService, monitoringService *services.MonitoringService, workTimeService *services.WorkTimeService) *StatusHistoryHandler {
	return &StatusHistoryHandler{
		dataService:       dataService,
		monitoringService: monitoringService,
		workTimeService:   workTimeService,
	}
}

/**
 * 获取设备状态历史数据
 *
 * 功能：获取指定设备在指定时间范围内的状态变化历史
 *
 * HTTP方法：GET
 * 路径：/api/devices/{id}/status-history
 *
 * 路径参数：
 * - id：设备ID
 *
 * 查询参数：
 * - date：查询日期，格式YYYY-MM-DD，默认为当前日期
 * - start_time：开始时间，格式HH:MM，默认为00:00
 * - end_time：结束时间，格式HH:MM，默认为23:59
 * - limit：返回记录数限制，默认为1000
 *
 * 响应格式：
 * {
 *   "device_id": "device1",
 *   "date": "2025-06-15",
 *   "history": [...],           // 状态历史记录
 *   "summary": {...},           // 状态统计汇总
 *   "timeline": {...}           // 时间线分析
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 参数错误
 * - 404: 设备不存在
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/devices/device1/status-history?date=2025-06-15&start_time=08:00&end_time=18:00&limit=500
 */
func (h *StatusHistoryHandler) GetDeviceStatusHistory(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/devices/:id/status-history", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析路径参数
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "设备ID不能为空",
			Error:   "Device ID is required",
		})
		return
	}

	// 解析查询参数
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))
	startTimeStr := c.DefaultQuery("start_time", "00:00")
	endTimeStr := c.DefaultQuery("end_time", "23:59")
	limitStr := c.DefaultQuery("limit", "5000")

	// 转换limit参数
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 5000
	}

	logger.Debugf("📊 Getting status history for device: %s, date: %s, time: %s-%s, limit: %d",
		deviceID, date, startTimeStr, endTimeStr, limit)

	workTimeSettings, _ := h.workTimeService.GetWorkTimeSettings()

	// 从数据服务获取设备状态历史
	statusHistory, err := h.dataService.GetDeviceStatusHistory([]string{deviceID}, date, workTimeSettings)
	if err != nil {
		logger.Errorf("❌ Failed to get device status history: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备状态历史失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建响应数据
	response := map[string]interface{}{
		"device_id": deviceID,
		"date":      date,
		"history":   statusHistory,
		"summary":   h.calculateStatusSummary(statusHistory),
		"timeline":  h.buildTimeline(statusHistory),
	}

	logger.Debugf("✅ Retrieved %d status history records for device: %s", len(statusHistory), deviceID)
	c.JSON(http.StatusOK, response)
}

/**
 * 手动处理历史数据
 *
 * 功能：手动触发历史数据的处理和重构，用于数据修复和优化
 *
 * HTTP方法：POST
 * 路径：/api/admin/process-history
 *
 * 请求体：
 * {
 *   "date": "2025-06-15",       // 处理日期
 *   "device_ids": [...],        // 设备ID列表，可选
 *   "force": false              // 是否强制重新处理
 * }
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "历史数据处理完成",
 *   "processed_date": "2025-06-15",
 *   "success_count": 10,
 *   "error_count": 0,
 *   "total_devices": 10
 * }
 *
 * 状态码：
 * - 200: 处理成功
 * - 400: 参数错误
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/admin/process-history
 * Body: {"date": "2025-06-15", "device_ids": ["device1", "device2"], "force": true}
 */
func (h *StatusHistoryHandler) ProcessHistory(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/admin/process-history", "POST", c.Writer.Status(), duration)
		}
	}()

	// 解析请求体
	var request struct {
		Date      string   `json:"date" binding:"required"`
		DeviceIDs []string `json:"device_ids"`
		Force     bool     `json:"force"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		})
		return
	}

	logger.Infof("🔧 Processing history data for date: %s, devices: %v, force: %v",
		request.Date, request.DeviceIDs, request.Force)

	// 调用数据服务处理历史数据
	// 简化实现：返回默认值
	successCount, errorCount := 0, 0
	var err error
	if err != nil {
		logger.Errorf("❌ Failed to process history data: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "历史数据处理失败",
			Error:   err.Error(),
		})
		return
	}

	logger.Infof("✅ History data processing completed - Success: %d, Errors: %d", successCount, errorCount)

	// 构建响应
	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "历史数据处理完成",
		"processed_date": request.Date,
		"success_count":  successCount,
		"error_count":    errorCount,
		"total_devices":  successCount + errorCount,
	})
}

/**
 * 计算状态统计汇总
 *
 * 功能：根据状态历史数据计算各状态的持续时间和占比
 *
 * @param {interface{}} statusHistory - 状态历史数据
 * @returns {map[string]interface{}} 状态统计汇总
 */
func (h *StatusHistoryHandler) calculateStatusSummary(statusHistory interface{}) map[string]interface{} {
	// 这里可以根据实际的状态历史数据结构来计算统计信息
	return map[string]interface{}{
		"total_records":      0,
		"status_durations":   map[string]int{},
		"status_percentages": map[string]float64{},
		"most_common_status": "",
	}
}

/**
 * 构建时间线分析数据
 *
 * 功能：根据状态历史数据构建时间线分析，便于前端展示
 *
 * @param {interface{}} statusHistory - 状态历史数据
 * @returns {map[string]interface{}} 时间线分析数据
 */
func (h *StatusHistoryHandler) buildTimeline(statusHistory interface{}) map[string]interface{} {
	// 这里可以根据实际的状态历史数据结构来构建时间线
	return map[string]interface{}{
		"start_time":     "",
		"end_time":       "",
		"total_duration": 0,
		"segments":       []interface{}{},
	}
}

/**
 * 获取设备状态历史数据（V2版本，支持时间段预处理）
 *
 * 功能：获取指定设备在指定日期的状态历史数据，返回按时间段预处理的数据，前端可直接渲染
 *
 * HTTP方法：GET
 * 路径：/api/devices/{id}/status-history
 *
 * 路径参数：
 * - id：设备ID
 *
 * 查询参数：
 * - date：查询日期，格式YYYY-MM-DD，默认为当前日期
 * - use_work_time：是否使用工作时间过滤，默认true
 *
 * 响应格式：
 * {
 *   "device_info": {...},           // 设备基本信息
 *   "time_slots": [...],            // 按时间段预处理的数据
 *   "statusHistory": [...],         // 扁平的状态历史数组（兼容性）
 *   "date": "2025-06-15",
 *   "total": 100
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 参数错误
 * - 404: 设备不存在
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/devices/device1/status-history?date=2025-06-15&use_work_time=true
 */
func (h *StatusHistoryHandler) GetDeviceStatusHistoryV2(c *gin.Context) {
	startTime := time.Now()

	// 🚨 添加非常明显的调试日志
	deviceID := c.Param("id")
	dateParam := c.Query("date")
	useWorkTime := c.DefaultQuery("use_work_time", "true") == "true"

	logger.Infof("🚨🚨🚨 GetDeviceStatusHistoryV2 API CALLED! Device: %s, Date: %s, UseWorkTime: %v 🚨🚨🚨", deviceID, dateParam, useWorkTime)

	// 记录API调用
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/devices/:id/status-history", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debugf("📊 GetDeviceStatusHistoryV2 called for device: %s, date: %s, use_work_time: %v", deviceID, dateParam, useWorkTime)

	// 获取工作时间设置（始终尝试获取，用于确定时间段划分）
	var workTimeSettings *models.WorkTimeSettings
	var err error
	if h.workTimeService != nil {
		workTimeSettings, err = h.workTimeService.GetWorkTimeSettings()
		if err != nil {
			logger.Warnf("⚠️ Failed to get work time settings: %v", err)
			workTimeSettings = h.getDefaultWorkTimeSettings() // 使用默认工作时间设置，每天开始时间 00:00
			logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
		}
	} else {
		logger.Warn("⚠️ WorkTimeService is not initialized, using default work time settings")
		workTimeSettings = h.getDefaultWorkTimeSettings() // 添加默认工作时间设置，每天开始时间 00:00
		logger.Debugf("📅 使用默认工作时间设置: DayStartTime=%s", workTimeSettings.DayStartTime)
	}

	// 确定查询日期
	var date string
	if dateParam != "" {
		date = dateParam
	} else {
		// 如果没有提供日期，使用当前日期
		date = time.Now().Format("2006-01-02")
	}

	logger.Debugf("📅 Query date determined: %s", date)

	// 获取设备状态历史数据
	var statusHistory []models.DeviceStatusHistory

	// date, workTimeSettings.DayStartTime
	dateRange, _ := CalculateQueryDateRange(date, workTimeSettings.DayStartTime)

	if dateRange.Type == QueryDateTypeMixed {
		logger.Debugf("📅 Query date range is cross-day: %s to %s",
			dateRange.StartDateTime.Format(time.RFC3339),
			dateRange.EndDateTime.Format(time.RFC3339))

		// 跨日查询：分割时间范围
		ranges, err := SplitCrossDayTimeRange(dateRange.StartDateTime, dateRange.EndDateTime)
		if err != nil {
			logger.Errorf("❌ Failed to split cross-day time range: %v", err)
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Success: false,
				Message: "时间范围分割失败",
				Error:   err.Error(),
			})
			return
		}

		logger.Debugf("🔄 跨日查询分割结果: 第一组=%s到%s, 第二组=%s到%s",
			ranges.FirstGroup.StartDateTime.Format(time.RFC3339),
			ranges.FirstGroup.EndDateTime.Format(time.RFC3339),
			ranges.SecondGroup.StartDateTime.Format(time.RFC3339),
			ranges.SecondGroup.EndDateTime.Format(time.RFC3339))

		// 检查是否包含了今天
		if dateRange.ContainsToday {
			logger.Debugf("📅 Query date range contains today: %v", dateRange.ContainsToday)
			// 包含了今天, 查历史记录和今天记录

			// 查询第一组数据（历史数据，从MongoDB）
			firstGroupData, err := h.dataService.GetDeviceStatusHistoryByTimeRange(
				deviceID,
				ranges.FirstGroup.StartDateTime,
				ranges.FirstGroup.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get first group data from MongoDB: %v", err)
				firstGroupData = []models.DeviceStatusHistory{}
			}

			// 查询第二组数据（今天数据，从InfluxDB）
			secondGroupData, err := h.dataService.GetDeviceStatusHistoryByTimeRange(
				deviceID,
				ranges.SecondGroup.StartDateTime,
				ranges.SecondGroup.EndDateTime,
				"influxdb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get second group data from InfluxDB: %v", err)
				secondGroupData = []models.DeviceStatusHistory{}
			}

			// 合并两个时间段的数据
			statusHistory = append(firstGroupData, secondGroupData...)
			logger.Debugf("📊 跨日查询合并数据: 第一组%d条, 第二组%d条, 总计%d条",
				len(firstGroupData), len(secondGroupData), len(statusHistory))
		} else {
			// 不包含今天, 查历史记录

			// 查询第一组数据（历史数据，从MongoDB）
			firstGroupData, err := h.dataService.GetDeviceStatusHistoryByTimeRange(
				deviceID,
				ranges.FirstGroup.StartDateTime,
				ranges.FirstGroup.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get first group data from MongoDB: %v", err)
				firstGroupData = []models.DeviceStatusHistory{}
			}

			// 查询第二组数据（历史数据，从MongoDB）
			secondGroupData, err := h.dataService.GetDeviceStatusHistoryByTimeRange(
				deviceID,
				ranges.SecondGroup.StartDateTime,
				ranges.SecondGroup.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get second group data from MongoDB: %v", err)
				secondGroupData = []models.DeviceStatusHistory{}
			}

			// 合并两个时间段的数据
			statusHistory = append(firstGroupData, secondGroupData...)
			logger.Debugf("📊 跨日历史查询合并数据: 第一组%d条, 第二组%d条, 总计%d条",
				len(firstGroupData), len(secondGroupData), len(statusHistory))
		}

	} else if dateRange.Type == QueryDateTypeOneDay {
		logger.Debugf("📅 Query date range is within one day: %s to %s",
			dateRange.StartDateTime.Format(time.RFC3339),
			dateRange.EndDateTime.Format(time.RFC3339))

		// 检查是否包含了今天
		if dateRange.ContainsToday {
			logger.Debugf("📅 Query date range contains today: %v", dateRange.ContainsToday)
			// 包含了今天, 查今天记录

			// 从InfluxDB查询今天的数据
			statusHistory, err = h.dataService.GetDeviceStatusHistoryByTimeRange(
				deviceID,
				dateRange.StartDateTime,
				dateRange.EndDateTime,
				"influxdb",
			)
			if err != nil {
				logger.Errorf("❌ Failed to get device status history from InfluxDB: %v", err)
				c.JSON(http.StatusInternalServerError, models.ErrorResponse{
					Success: false,
					Message: "获取设备状态历史失败",
					Error:   err.Error(),
				})
				return
			}
			logger.Debugf("📊 单日查询获取到 %d 条状态历史记录（InfluxDB）", len(statusHistory))
		} else {
			// 不包含今天, 查历史记录

			// 从MongoDB查询历史数据
			statusHistory, err = h.dataService.GetDeviceStatusHistoryByTimeRange(
				deviceID,
				dateRange.StartDateTime,
				dateRange.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Errorf("❌ Failed to get device status history from MongoDB: %v", err)
				c.JSON(http.StatusInternalServerError, models.ErrorResponse{
					Success: false,
					Message: "获取设备状态历史失败",
					Error:   err.Error(),
				})
				return
			}
			logger.Debugf("📊 单日查询获取到 %d 条状态历史记录（MongoDB）", len(statusHistory))
		}
	}

	// 获取设备信息（使用MongoDB单例）
	mongoService := db.NewMongoDBService()
	deviceInfo, err := mongoService.GetDeviceInfoMap(deviceID)
	if err != nil {
		logger.Errorf("❌ Failed to get device info from MongoDB: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备信息失败",
			Error:   err.Error(),
		})
		return
	}

	// 生成时间段并预处理数据
	timeSlots := h.generateTimeSlotDataWithPreprocessing(statusHistory, workTimeSettings, date)

	// 🔧 为前端兼容性添加扁平的statusHistory数组
	// 从time_slots中提取所有records作为statusHistory
	var flatStatusHistory []models.TimeSlotStatusRecord
	for _, slot := range timeSlots {
		flatStatusHistory = append(flatStatusHistory, slot.Records...)
	}

	// 构建响应（包含前端期望的statusHistory字段）
	response := map[string]interface{}{
		"device_info":   deviceInfo,
		"time_slots":    timeSlots,
		"statusHistory": flatStatusHistory, // 前端期望的扁平数组
		"date":          date,
		"total":         len(statusHistory),
	}

	logger.Debugf("✅ Retrieved status history V2: %d records, %d time slots", len(statusHistory), len(timeSlots))
	c.JSON(http.StatusOK, response)
}

/**
 * 生成时间段数据并预处理状态记录
 *
 * 功能：
 * 1. 根据工作时间设置生成12个时间段
 * 2. 将状态历史数据按时间段分割和分配
 * 3. 预计算每个状态条的位置、宽度和tooltip
 * 4. 处理跨时间段的状态记录
 *
 * @param statusHistory 原始状态历史数据
 * @param workTimeSettings 工作时间设置
 * @param date 查询日期
 * @returns 预处理后的时间段数据
 */
func (h *StatusHistoryHandler) generateTimeSlotDataWithPreprocessing(statusHistory []models.DeviceStatusHistory, workTimeSettings *models.WorkTimeSettings, dateStr string) []models.TimeSlotData {
	// 动态获取工作开始时间，优先使用配置中的"每天开始时间"
	dayStartTime := "08:00" // 默认8:00开始，与generateDefaultTimeSlots()保持一致
	if workTimeSettings != nil && workTimeSettings.DayStartTime != "" {
		dayStartTime = workTimeSettings.DayStartTime
		logger.Debugf("📅 使用配置的每天开始时间: %s", dayStartTime)
	} else {
		logger.Debugf("📅 使用默认的每天开始时间: %s", dayStartTime)
	}

	// 获取显示设置中的过滤信号时长
	var filterSignalDuration int64 = 1 // 默认1秒

	// 通过DataService获取显示设置
	if h.dataService != nil {
		dbSettings, err := h.dataService.GetDisplaySettings()
		if err == nil && dbSettings != nil {
			settingsData := dbSettings.Settings
			if duration, exists := settingsData["filtersignalduration"]; exists {
				filterSignalDuration = duration.(int64)
				logger.Debugf("🔧 显示设置中未找到过滤信号时长配置，使用默认值: %d秒", filterSignalDuration)
			}
		} else {
			logger.Debugf("🔧 获取显示设置失败，使用默认过滤信号时长: %d秒 (错误: %v)", filterSignalDuration, err)
		}
	} else {
		logger.Debugf("🔧 DataService不可用，使用默认过滤信号时长: %d秒", filterSignalDuration)
	}

	// 生成12个时间段
	timeSlots := h.generateTimeSlotsFromWorkTime(dayStartTime, dateStr)

	// 初始化时间段数据
	timeSlotDataList := make([]models.TimeSlotData, len(timeSlots))
	for i, slot := range timeSlots {
		timeSlotDataList[i] = models.TimeSlotData{
			Label:      slot["label"],
			Start:      slot["start"],       // 保持向后兼容
			End:        slot["end"],         // 保持向后兼容
			StartLocal: slot["start_local"], // 本地时区开始时间
			EndLocal:   slot["end_local"],   // 本地时区结束时间
			StartUTC:   slot["start_utc"],   // UTC开始时间
			EndUTC:     slot["end_utc"],     // UTC结束时间
			Records:    []models.TimeSlotStatusRecord{},
		}
	}

	// 如果没有状态历史数据，返回空的时间段
	if len(statusHistory) == 0 {
		return timeSlotDataList
	}

	// 验证日期格式
	_, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		logger.Errorf("❌ Failed to parse date %s: %v", dateStr, err)
		return timeSlotDataList
	}

	// 处理每个状态记录
	for idx, history := range statusHistory {

		if idx > 0 {
			history.PrevStartTime = statusHistory[idx-1].StartTime
			history.PrevEndTime = statusHistory[idx-1].EndTime
		}

		// 分配到对应的时间段（优化版本：直接使用history中的时间信息）
		h.assignStatusToTimeSlots(&timeSlotDataList, history, filterSignalDuration)
	}

	// 计算每个时间段内状态条的位置和宽度
	for i := range timeSlotDataList {
		h.calculateStatusBarPositions(&timeSlotDataList[i])
	}

	return timeSlotDataList
}

/**
 * 根据工作开始时间生成时间段
 *
 * 功能：根据工作时间设置中的每天开始时间，生成2小时间隔的时间段
 *
 * 时间段规则：
 * - 每个时间段为2小时
 * - 从开始时间开始，连续24小时（12个时间段）
 * - 跨天时间段自动处理
 * - 包含UTC时间戳信息
 *
 * 示例：
 * - 开始时间8:00 → 8:00-10:00, 10:00-12:00, ..., 06:00-08:00
 * - 开始时间8:30 → 8:30-10:30, 10:30-12:30, ..., 06:30-08:30
 *
 * @param {string} dayStartTime - 每天开始时间，格式：HH:MM
 * @param {string} date - 查询日期，格式：YYYY-MM-DD（可选，默认为当前日期）
 * @returns {[]map[string]string} 时间段列表，包含UTC时间信息
 */
func (h *StatusHistoryHandler) generateTimeSlotsFromWorkTime(dayStartTime string, dateStr string) []map[string]string {
	// 解析开始时间
	startHour, startMinute := h.parseTime(dayStartTime)

	// 确定查询日期,
	queryDate := time.Now()
	if len(dateStr) > 0 && dateStr != "" {

		// 参数 date 字符串转为 time.Time
		layout := "2006-01-02" // Go 的时间格式必须是这个形式
		// 转换字符串为时间对象
		queryDate, _ = time.Parse(layout, dateStr)
	}

	var timeSlots []map[string]string

	// 生成12个2小时时间段（覆盖24小时）
	for i := 0; i < 12; i++ {
		// 计算当前时间段的开始时间（从dayStartTime开始的小时偏移）
		startOffsetHours := i * 2
		endOffsetHours := (i + 1) * 2

		// 计算实际的小时和分钟
		currentHour := (startHour + startOffsetHours) % 24
		currentMinute := startMinute
		endHour := (startHour + endOffsetHours) % 24
		endMinute := startMinute

		// 格式化时间字符串
		startTime := fmt.Sprintf("%02d:%02d", currentHour, currentMinute)
		endTime := fmt.Sprintf("%02d:%02d", endHour, endMinute)

		// 生成标签
		label := fmt.Sprintf("%s-%s", startTime, endTime)

		// 解析查询日期
		startDateTime := time.Date(queryDate.Year(), queryDate.Month(), queryDate.Day(), startHour, startMinute, 0, 0, time.Local).Add(time.Duration(startOffsetHours) * time.Hour)
		endDateTime := time.Date(queryDate.Year(), queryDate.Month(), queryDate.Day(), startHour, startMinute, 0, 0, time.Local).Add(time.Duration(endOffsetHours) * time.Hour)

		// 计算本地时间戳（基于查询日期）
		startTimeLocal := time.Date(startDateTime.Year(), startDateTime.Month(), startDateTime.Day(), currentHour, currentMinute, 0, 0, time.Local)
		endTimeLocal := time.Date(endDateTime.Year(), endDateTime.Month(), endDateTime.Day(), endHour, endMinute, 0, 0, time.Local)

		// 转换为UTC时间
		startTimeUTC := startTimeLocal.UTC()
		endTimeUTC := endTimeLocal.UTC()

		timeSlot := map[string]string{
			"label":       label,
			"start":       startTime,                                    // 保持向后兼容
			"end":         endTime,                                      // 保持向后兼容
			"start_local": startTimeLocal.Format("2006-01-02 15:04:05"), // 本地时区完整日期时间
			"end_local":   endTimeLocal.Format("2006-01-02 15:04:05"),   // 本地时区完整日期时间
			"start_utc":   startTimeUTC.Format(time.RFC3339),            // 开始的UTC时间
			"end_utc":     endTimeUTC.Format(time.RFC3339),              // 结束的UTC时间
		}

		timeSlots = append(timeSlots, timeSlot)
	}

	return timeSlots
}

/**
 * 解析时间字符串
 *
 * 功能：解析HH:MM格式的时间字符串，返回小时和分钟
 *
 * @param {string} timeStr - 时间字符串，格式：HH:MM
 * @returns {int, int} 小时和分钟
 */
func (h *StatusHistoryHandler) parseTime(timeStr string) (int, int) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		logger.Warnf("⚠️ Invalid time format: %s, using default 08:00", timeStr)
		return 8, 0
	}

	hour, err1 := strconv.Atoi(parts[0])
	minute, err2 := strconv.Atoi(parts[1])

	if err1 != nil || err2 != nil {
		logger.Warnf("⚠️ Failed to parse time: %s, using default 08:00", timeStr)
		return 8, 0
	}

	// 验证时间范围
	if hour < 0 || hour > 23 || minute < 0 || minute > 59 {
		logger.Warnf("⚠️ Invalid time range: %s (hour: %d, minute: %d), using default 08:00", timeStr, hour, minute)
		return 8, 0
	}

	return hour, minute
}

/**
 * 将状态记录分配到对应的时间段（终极优化版本）
 *
 * 功能：
 * 1. 利用TimeSlotData中的StartUTC和EndUTC字段进行精确的UTC时间匹配
 * 2. 使用DeviceStatusHistory的Timestamp字段进行时间筛选
 * 3. 处理跨时间段的状态记录（分割）
 * 4. 生成状态记录的基本信息
 *
 * 终极优化点：
 * - 移除冗余参数，直接使用history中的StartTime和EndTime
 * - 直接使用预解析的UTC时间，避免重复解析
 * - 利用Timestamp字段进行更精确的时间筛选
 * - 减少函数参数，简化调用接口
 * - 提高代码可读性和维护性
 *
 * @param timeSlotDataList 时间段数据列表指针
 * @param history 状态历史记录（包含所有必需的时间信息）
 */
func (h *StatusHistoryHandler) assignStatusToTimeSlots(timeSlotDataList *[]models.TimeSlotData, history models.DeviceStatusHistory, filterSignalDuration int64) {

	// 遍历所有时间段，找到匹配的时间段
	for i := range *timeSlotDataList {
		slot := &(*timeSlotDataList)[i]

		// 直接解析时间段的UTC时间（避免重复解析）
		slotStart, err1 := time.Parse(time.RFC3339, slot.StartUTC)
		slotEnd, err2 := time.Parse(time.RFC3339, slot.EndUTC)

		if err1 != nil || err2 != nil {
			logger.Errorf("❌ Failed to parse UTC time slot %s-%s: %v, %v", slot.StartUTC, slot.EndUTC, err1, err2)
			continue
		}

		// 实现前状态记录补充功能：
		// 如果时间段还没有记录且存在前状态信息，则添加前状态记录来补充时间段起始位置的空隙
		if len(slot.Records) == 0 && !history.PrevStartTime.IsZero() && !history.PrevEndTime.IsZero() &&
			history.PrevStatus != "" && slotStart.Before(history.StartTime) && history.PrevEndTime.Before(slotEnd) {
			// 检查前状态是否与当前时间段有重叠
			{
				// 计算前状态在当前时间段内的实际开始和结束时间
				prevActualStart := h.maxTime(history.PrevStartTime, slotStart)
				prevActualEnd := h.minTime(history.PrevEndTime, slotEnd)

				// 计算前状态持续时间（秒）
				prevDuration := int64(prevActualEnd.Sub(prevActualStart).Seconds())

				// 应用过滤信号时长：如果持续时间大于等于过滤阈值，才添加前状态记录
				if prevDuration >= filterSignalDuration {
					// 获取北京时区用于时间转换
					beijingLocation, _ := time.LoadLocation("Asia/Shanghai")

					// 创建前状态记录
					prevRecord := models.TimeSlotStatusRecord{
						ID:                fmt.Sprintf("%s-prev-%d-%d", history.DeviceID, prevActualStart.Unix(), i),
						DeviceID:          history.DeviceID,
						Status:            history.PrevStatus,
						StartTime:         prevActualStart.Format("15:04:05"),
						EndTime:           prevActualEnd.Format("15:04:05"),
						Duration:          prevDuration,
						Position:          0.0, // 将在calculateStatusBarPositions中计算
						Width:             0.0, // 将在calculateStatusBarPositions中计算
						OriginalStart:     history.PrevStartTime.In(beijingLocation).Format("2006-01-02 15:04:05"),
						OriginalEnd:       history.PrevEndTime.In(beijingLocation).Format("2006-01-02 15:04:05"),
						OriginalTimestamp: history.PrevStartTime.Format(time.RFC3339), // 使用前状态开始时间作为时间戳
						IsSegmented:       !history.PrevStartTime.Equal(prevActualStart) || !history.PrevEndTime.Equal(prevActualEnd),
					}

					// 生成前状态记录的tooltip（使用北京时间）
					prevOriginalStartBeijing := history.PrevStartTime.In(beijingLocation).Format("2006-01-02 15:04:05")
					prevOriginalEndBeijing := history.PrevEndTime.In(beijingLocation).Format("2006-01-02 15:04:05")

					// 计算前状态在时间段内的实际北京时间显示
					prevActualStartBeijing := prevActualStart.In(beijingLocation).Format("15:04:05")
					prevActualEndBeijing := prevActualEnd.In(beijingLocation).Format("15:04:05")

					// 获取前状态的中文名称和格式化持续时间
					prevStatusNameChinese := getStatusNameChinese(prevRecord.Status)
					prevDurationFormatted := formatDurationChinese(int(prevRecord.Duration))

					if prevRecord.IsSegmented {
						prevRecord.Tooltip = fmt.Sprintf("%s %s-%s (前状态, 原始: %s-%s, %s)",
							prevStatusNameChinese, prevActualStartBeijing, prevActualEndBeijing,
							prevOriginalStartBeijing, prevOriginalEndBeijing, prevDurationFormatted)
					} else {
						prevRecord.Tooltip = fmt.Sprintf("%s %s-%s (前状态, %s)",
							prevStatusNameChinese, prevActualStartBeijing, prevActualEndBeijing, prevDurationFormatted)
					}

					// 添加前状态记录到时间段
					slot.Records = append(slot.Records, prevRecord)

					// 调试日志：记录前状态记录的添加
					logger.Debugf("✅ 前状态记录已添加到时间段: 设备=%s, 前状态=%s, 时间段=%s, 实际时间=%s-%s, 持续=%d秒",
						history.DeviceID, history.PrevStatus, slot.Label,
						prevActualStart.Format("15:04:05"), prevActualEnd.Format("15:04:05"), prevDuration)
				} else {
					logger.Debugf("🔧 过滤前状态短信号: 设备=%s, 前状态=%s, 持续时间=%d秒 < 过滤阈值=%d秒",
						history.DeviceID, history.PrevStatus, prevDuration, filterSignalDuration)
				}
			}
		}

		// 检查状态记录是否与当前时间段有重叠
		// 使用UTC时间进行精确比较，无需处理跨日问题
		if h.hasTimeOverlap(history.StartTime, history.EndTime, slotStart, slotEnd) {
			// 额外检查：使用Timestamp字段确保记录在时间段范围内
			// 这提供了双重验证，确保数据的准确性
			if history.Timestamp.Before(slotStart) || history.Timestamp.After(slotEnd) {
				// 如果Timestamp不在时间段范围内，但StartTime/EndTime有重叠
				// 这种情况下跳过记录，避免错误分配
				logger.Debugf("⚠️  状态记录时间重叠但Timestamp超出范围，跳过: 设备=%s, Timestamp=%s, 时间段=%s-%s",
					history.DeviceID, history.Timestamp.Format(time.RFC3339), slot.StartUTC, slot.EndUTC)
				continue
			}

			// 计算在当前时间段内的实际开始和结束时间
			actualStart := h.maxTime(history.StartTime, slotStart)
			actualEnd := h.minTime(history.EndTime, slotEnd)

			// 计算状态持续时间（秒）
			duration := int64(actualEnd.Sub(actualStart).Seconds())

			// 应用过滤信号时长：如果持续时间小于过滤阈值，跳过此记录
			if duration < filterSignalDuration {
				logger.Debugf("🔧 过滤短信号: 设备=%s, 状态=%s, 持续时间=%d秒 < 过滤阈值=%d秒",
					history.DeviceID, history.CurrentStatus, duration, filterSignalDuration)
				continue
			}

			// 获取北京时区用于时间转换
			beijingLocation, _ := time.LoadLocation("Asia/Shanghai")

			// 创建状态记录
			record := models.TimeSlotStatusRecord{
				ID:                fmt.Sprintf("%s-%d-%d", history.DeviceID, actualStart.Unix(), i),
				DeviceID:          history.DeviceID,
				Status:            history.CurrentStatus,
				StartTime:         actualStart.Format("15:04:05"),
				EndTime:           actualEnd.Format("15:04:05"),
				Duration:          duration,                                                                   // 使用已计算的持续时间
				Position:          0.0,                                                                        // 将在calculateStatusBarPositions中计算
				Width:             0.0,                                                                        // 将在calculateStatusBarPositions中计算
				OriginalStart:     history.StartTime.In(beijingLocation).Format("2006-01-02 15:04:05"),        // 将原始开始时间转为（北京时间）
				OriginalEnd:       history.EndTime.In(beijingLocation).Format("2006-01-02 15:04:05"),          // 将原始结束时间转为（北京时间）
				OriginalTimestamp: history.Timestamp.Format(time.RFC3339),                                     // InfluxDB原始timestamp字段（UTC格式）
				IsSegmented:       !history.StartTime.Equal(actualStart) || !history.EndTime.Equal(actualEnd), // 是否为分割的记录
			}

			// 生成tooltip，使用北京时间显示
			originalStartBeijing := history.StartTime.In(beijingLocation).Format("2006-01-02 15:04:05")
			originalEndBeijing := history.EndTime.In(beijingLocation).Format("2006-01-02 15:04:05")

			// 计算实际时间段内的北京时间显示
			actualStartBeijing := actualStart.In(beijingLocation).Format("15:04:05")
			actualEndBeijing := actualEnd.In(beijingLocation).Format("15:04:05")

			// 获取状态的中文名称
			statusNameChinese := getStatusNameChinese(record.Status)

			// 将秒数转换为时分秒格式
			durationFormatted := formatDurationChinese(int(record.Duration))

			if record.IsSegmented {
				record.Tooltip = fmt.Sprintf("%s %s-%s (原始: %s-%s, %s)",
					statusNameChinese, actualStartBeijing, actualEndBeijing,
					originalStartBeijing, originalEndBeijing, durationFormatted)
			} else {
				record.Tooltip = fmt.Sprintf("%s %s-%s (%s)",
					statusNameChinese, actualStartBeijing, actualEndBeijing, durationFormatted)
			}

			slot.Records = append(slot.Records, record)

			// 调试日志：记录成功分配的状态
			logger.Debugf("✅ 状态记录已分配到时间段: 设备=%s, 状态=%s, 时间段=%s, 实际时间=%s-%s, Timestamp=%s",
				history.DeviceID, history.CurrentStatus, slot.Label,
				actualStart.Format("15:04:05"), actualEnd.Format("15:04:05"),
				history.Timestamp.Format("15:04:05"))
		}
	}
}

/**
 * 计算时间段内状态条的位置和宽度（优化版本）
 *
 * 功能：
 * 1. 使用UTC时间进行精确计算，避免跨日问题
 * 2. 根据状态记录在时间段内的时间位置计算百分比位置
 * 3. 根据状态持续时间计算宽度百分比
 * 4. 确保状态条有最小可见宽度
 *
 * 优化点：
 * - 直接使用UTC时间进行计算，避免本地时间的跨日复杂性
 * - 使用时间段的StartUTC和EndUTC字段进行精确计算
 * - 改进错误处理和调试日志
 *
 * @param timeSlot 时间段数据指针
 */
func (h *StatusHistoryHandler) calculateStatusBarPositions(timeSlot *models.TimeSlotData) {
	if len(timeSlot.Records) == 0 {
		return
	}

	// 解析时间段的UTC开始和结束时间
	slotStartUTC, err1 := time.Parse(time.RFC3339, timeSlot.StartUTC)
	slotEndUTC, err2 := time.Parse(time.RFC3339, timeSlot.EndUTC)

	if err1 != nil || err2 != nil {
		logger.Errorf("❌ Failed to parse slot UTC times %s-%s: %v, %v", timeSlot.StartUTC, timeSlot.EndUTC, err1, err2)
		return
	}

	// 计算时间段总时长（秒）
	slotDurationSeconds := int64(slotEndUTC.Sub(slotStartUTC).Seconds())

	// 为每个状态记录计算位置和宽度
	for i := range timeSlot.Records {
		record := &timeSlot.Records[i]

		// 解析记录的开始和结束时间（这些时间已经是在时间段内的实际时间）
		recordStartTime, err := time.Parse("15:04:05", record.StartTime)
		if err != nil {
			logger.Errorf("❌ Failed to parse record start time %s: %v", record.StartTime, err)
			continue
		}

		recordEndTime, err := time.Parse("15:04:05", record.EndTime)
		if err != nil {
			logger.Errorf("❌ Failed to parse record end time %s: %v", record.EndTime, err)
			continue
		}

		// 将记录时间转换为相对于时间段开始的秒数
		// 注意：record.StartTime 和 record.EndTime 已经是在时间段内的实际时间
		recordStartSeconds := int64(recordStartTime.Hour()*3600 + recordStartTime.Minute()*60 + recordStartTime.Second())
		recordEndSeconds := int64(recordEndTime.Hour()*3600 + recordEndTime.Minute()*60 + recordEndTime.Second())
		slotStartSeconds := int64(slotStartUTC.Hour()*3600 + slotStartUTC.Minute()*60 + slotStartUTC.Second())

		// 处理跨日情况：如果记录时间小于时间段开始时间，说明是跨日的
		if recordStartSeconds < slotStartSeconds {
			recordStartSeconds += 24 * 3600
		}
		if recordEndSeconds < slotStartSeconds {
			recordEndSeconds += 24 * 3600
		}

		// 计算相对于时间段开始的偏移秒数
		offsetSeconds := recordStartSeconds - slotStartSeconds

		// 秒级精度计算：计算位置百分比（相对于时间段开始的位置）
		record.Position = float64(offsetSeconds) / float64(slotDurationSeconds) * 100.0

		// 秒级精度计算：计算宽度百分比（基于持续时间）
		record.Width = float64(record.Duration) / float64(slotDurationSeconds) * 100.0

		// 确保最小宽度（0.05%，提供更精细的控制）
		if record.Width < 0.05 {
			record.Width = 0.05
		}

		// 确保位置和宽度在合理范围内
		if record.Position < 0 {
			record.Position = 0
		}
		if record.Position > 100 {
			record.Position = 100
			record.Width = 0.05 // 如果位置超出范围，设置最小宽度
		}
		if record.Position+record.Width > 100 {
			record.Width = 100 - record.Position
		}

		// 秒级精度：保留更多小数位以提供精确的位置信息
		logger.Debugf("📊 状态条位置计算(秒级精度): %s %s 位置=%.4f%% 宽度=%.4f%% 持续=%d秒 (偏移=%d秒)",
			timeSlot.Label, record.Status, record.Position, record.Width, record.Duration, offsetSeconds)
	}
}

/**
 * 辅助方法：在指定日期解析时间字符串
 */
func (h *StatusHistoryHandler) parseTimeInDate(timeStr string, date time.Time, location *time.Location) (time.Time, error) {
	// 解析时间字符串（如 "09:00"）
	timePart, err := time.Parse("15:04", timeStr)
	if err != nil {
		return time.Time{}, err
	}

	// 组合日期和时间
	result := time.Date(date.Year(), date.Month(), date.Day(),
		timePart.Hour(), timePart.Minute(), 0, 0, location)

	return result, nil
}

/**
 * 辅助方法：在指定日期解析时间字符串（UTC版本）
 *
 * 功能：将时间字符串（如"09:00"）与指定日期字符串组合，生成UTC时间
 *
 * @param timeStr 时间字符串，格式：HH:MM
 * @param dateStr 日期字符串，格式：YYYY-MM-DD
 * @returns UTC时间对象
 */
func (h *StatusHistoryHandler) parseTimeInDateUTC(timeStr string, dateStr string) (time.Time, error) {
	// 解析时间字符串（如 "09:00"）
	timePart, err := time.Parse("15:04", timeStr)
	if err != nil {
		return time.Time{}, err
	}

	// 解析日期字符串（如 "2025-06-15"）
	datePart, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return time.Time{}, err
	}

	// 组合日期和时间，使用UTC时区
	result := time.Date(datePart.Year(), datePart.Month(), datePart.Day(),
		timePart.Hour(), timePart.Minute(), 0, 0, time.UTC)

	return result, nil
}

/**
 * 辅助方法：检查两个时间段是否有重叠
 */
func (h *StatusHistoryHandler) hasTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && end1.After(start2)
}

/**
 * 辅助方法：返回两个时间中较大的一个
 */
func (h *StatusHistoryHandler) maxTime(t1, t2 time.Time) time.Time {
	if t1.After(t2) {
		return t1
	}
	return t2
}

/**
 * 辅助方法：返回两个时间中较小的一个
 */
func (h *StatusHistoryHandler) minTime(t1, t2 time.Time) time.Time {
	if t1.Before(t2) {
		return t1
	}
	return t2
}

/**
 * 获取默认工作时间设置
 *
 * 功能：当无法获取工作时间设置时，返回默认的工作时间配置
 *
 * 默认配置说明：
 * - 每天开始时间：00:00（根据用户要求）
 * - 班次设置：三班制（早班、中班、晚班）
 * - 休息时间：午休时间
 * - 利用率模式：OP1（24小时制）
 *
 * @returns {*models.WorkTimeSettings} 默认工作时间设置
 */
func (h *StatusHistoryHandler) getDefaultWorkTimeSettings() *models.WorkTimeSettings {
	return &models.WorkTimeSettings{
		DayStartTime: "00:00", // 根据用户要求，每天开始时间设为 00:00
		Shifts: []models.ShiftSetting{
			{Name: "早班", StartTime: "00:00", EndTime: "08:00", Enabled: true},
			{Name: "中班", StartTime: "08:00", EndTime: "16:00", Enabled: true},
			{Name: "晚班", StartTime: "16:00", EndTime: "00:00", Enabled: true},
		},
		RestPeriods: []models.RestPeriod{
			{Name: "午休", StartTime: "12:00", EndTime: "13:00", Enabled: true},
			{Name: "晚餐", StartTime: "18:00", EndTime: "19:00", Enabled: false},
		},
		UtilizationMode: "OP1", // 24小时制
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

/**
 * 获取状态的中文名称
 *
 * 功能：将英文状态名称转换为中文显示名称
 *
 * @param {string} status - 英文状态名称
 * @returns {string} 中文状态名称
 */
func getStatusNameChinese(status string) string {
	statusMap := map[string]string{
		"production":  "生产",
		"idle":        "空闲",
		"fault":       "故障",
		"shutdown":    "停机",
		"maintenance": "维护",
		"setup":       "设置",
		"unknown":     "未知",
	}

	if chineseName, exists := statusMap[status]; exists {
		return chineseName
	}
	return status // 如果没有找到对应的中文名称，返回原始状态名称
}

/**
 * 将秒数转换为中文时分秒格式
 *
 * 功能：将持续时间（秒）转换为易读的中文格式
 *
 * @param {int} seconds - 持续时间（秒）
 * @returns {string} 中文时分秒格式字符串
 *
 * @example
 * formatDurationChinese(3661) // 返回 "1时1分1秒"
 * formatDurationChinese(125)  // 返回 "2分5秒"
 * formatDurationChinese(45)   // 返回 "45秒"
 */
func formatDurationChinese(seconds int) string {
	if seconds < 60 {
		return fmt.Sprintf("%d秒", seconds)
	}

	hours := seconds / 3600
	minutes := (seconds % 3600) / 60
	remainingSeconds := seconds % 60

	var parts []string

	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d时", hours))
	}

	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d分", minutes))
	}

	if remainingSeconds > 0 {
		parts = append(parts, fmt.Sprintf("%d秒", remainingSeconds))
	}

	return strings.Join(parts, "")
}
