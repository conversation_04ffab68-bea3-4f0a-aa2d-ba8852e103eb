/**
 * 设备统计分析处理器
 *
 * 功能描述：
 * 处理设备统计分析相关的API请求，包括设备利用率、产量统计、班次分析等功能
 * 提供完整的制造业数据采集系统统计分析核心功能
 *
 * 主要功能：
 * - 单设备和多设备的利用率、产量统计
 * - 时间范围分析：支持日、周、月等不同时间维度
 * - 班次级别统计：白班、夜班等班次的独立统计
 * - 工作时间管理：班次配置、休息时间设置
 * - 统计任务调度：手动触发和自动化统计计算
 * - 汇总数据查询：多设备、多时间段汇总统计
 *
 * API端点：
 * - GET /api/statistics/devices/{deviceId} - 单设备统计
 * - POST /api/statistics/devices/multiple - 多设备统计
 * - GET /api/statistics/devices/{deviceId}/range - 设备日期范围统计
 * - GET /api/statistics/devices/{deviceId}/shifts - 班次统计
 * - GET /api/statistics/summary - 汇总统计
 * - POST /api/statistics/scheduler/run - 手动执行统计任务
 * - GET /api/statistics/scheduler/status - 调度器状态
 * - GET/POST /api/statistics/worktime - 工作时间配置管理
 *
 * @package statistics
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package statistics

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 统计分析处理器结构体
 *
 * 功能：处理统计分析相关的HTTP请求
 *
 * 依赖服务：
 * - StatisticsService：核心统计计算服务
 * - SchedulerService：统计任务调度服务
 * - WorkTimeService：工作时间配置服务
 * - Logger：日志记录和调试信息
 */
type StatisticsHandler struct {
	statisticsService *services.StatisticsService
	schedulerService  *services.SchedulerService
	workTimeService   *services.WorkTimeService
	logger            *log.Logger
}

/**
 * 创建统计分析处理器实例
 *
 * 功能：初始化统计分析处理器，注入必要的服务依赖
 *
 * @param {*services.StatisticsService} statisticsService - 统计分析服务
 * @param {*services.SchedulerService} schedulerService - 任务调度服务
 * @param {*services.WorkTimeService} workTimeService - 工作时间管理服务
 * @param {*log.Logger} logger - 日志记录器
 * @returns {*StatisticsHandler} 统计处理器实例
 *
 * @example
 * handler := statistics.NewStatisticsHandler(statsService, schedulerService, workTimeService, logger)
 */
func NewStatisticsHandler(
	statisticsService *services.StatisticsService,
	schedulerService *services.SchedulerService,
	workTimeService *services.WorkTimeService,
	logger *log.Logger,
) *StatisticsHandler {
	return &StatisticsHandler{
		statisticsService: statisticsService,
		schedulerService:  schedulerService,
		workTimeService:   workTimeService,
		logger:            logger,
	}
}

/**
 * 获取单个设备统计数据
 *
 * 功能：获取指定设备在指定日期的完整统计信息
 *
 * HTTP方法：GET
 * 路径：/api/statistics/devices/{deviceId}
 *
 * 路径参数：
 * - deviceId：设备唯一标识符
 *
 * 查询参数：
 * - date：统计日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "device_id": "device_001",
 *   "device_name": "设备名称",
 *   "date": "2025-06-15",
 *   "utilization_rate": 85.5,
 *   "production_count": 120,
 *   "plan_count": 150,
 *   "completion_rate": 80.0,
 *   "shift_statistics": [...],
 *   "status_durations": {...}
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 设备不存在或无统计数据
 * - 500: 统计计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/statistics/devices/device_001?date=2025-06-15
 */
func (h *StatisticsHandler) GetDeviceStatistics(c *gin.Context) {
	// 获取路径参数和查询参数
	deviceID := c.Param("deviceId")
	date := c.Query("date")

	// 设置默认日期为当前日期
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	logger.Debugf("📊 Getting device statistics: device=%s, date=%s", deviceID, date)

	// 调用统计服务获取设备统计数据
	stats, err := h.statisticsService.GetDeviceStatistics(date, deviceID)
	if err != nil {
		logger.Errorf("❌ Failed to get device statistics: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("获取设备统计失败: %v", err),
		})
		return
	}

	// 处理数据不存在的情况
	if stats == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "没有找到统计数据",
		})
		return
	}

	logger.Debugf("✅ Retrieved device statistics for: %s", deviceID)
	c.JSON(http.StatusOK, stats)
}

/**
 * 获取多设备统计数据
 *
 * 功能：获取多个设备在指定时间范围内的统计信息
 *
 * HTTP方法：POST
 * 路径：/api/statistics/devices/multiple
 *
 * 请求体：
 * {
 *   "device_ids": ["device_001", "device_002"],
 *   "start_date": "2025-06-01",
 *   "end_date": "2025-06-15",
 *   "group_by": "device"
 * }
 *
 * 响应格式：
 * {
 *   "devices": [...],
 *   "summary": {...},
 *   "total_count": 2
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 请求参数无效
 * - 500: 统计计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/statistics/devices/multiple
 * Body: {"device_ids": ["device_001"], "start_date": "2025-06-15", "end_date": "2025-06-15"}
 */
func (h *StatisticsHandler) GetMultipleDeviceStatistics(c *gin.Context) {
	var request models.StatisticsRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("请求参数解析失败: %v", err)})
		return
	}

	// 设置默认值
	if request.StartDate == "" {
		request.StartDate = time.Now().Format("2006-01-02")
	}
	if request.EndDate == "" {
		request.EndDate = request.StartDate
	}

	logger.Debugf("📊 Getting multiple device statistics: start=%s, end=%s, devices=%d",
		request.StartDate, request.EndDate, len(request.DeviceIDs))

	response, err := h.statisticsService.GetMultipleDeviceStatistics(&request)
	if err != nil {
		logger.Errorf("❌ Failed to get multiple device statistics: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("获取多设备统计失败: %v", err)})
		return
	}

	logger.Debugf("✅ Retrieved statistics for %d devices", len(request.DeviceIDs))
	c.JSON(http.StatusOK, response)
}

/**
 * 获取每日统计汇总
 *
 * 功能：获取指定日期范围内的每日统计汇总信息
 *
 * HTTP方法：GET
 * 路径：/api/statistics/summary
 *
 * 查询参数：
 * - start_date：开始日期，格式YYYY-MM-DD，默认为当前日期
 * - end_date：结束日期，格式YYYY-MM-DD，默认为开始日期
 *
 * 响应格式：
 * {
 *   "total_devices": 10,
 *   "average_utilization": 82.5,
 *   "total_production": 1200,
 *   "total_plan": 1500,
 *   "overall_completion_rate": 80.0
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 统计计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/statistics/summary?start_date=2025-06-01&end_date=2025-06-15
 */
func (h *StatisticsHandler) GetDailyStatisticsSummary(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" {
		startDate = time.Now().Format("2006-01-02")
	}
	if endDate == "" {
		endDate = startDate
	}

	request := &models.StatisticsRequest{
		StartDate: startDate,
		EndDate:   endDate,
		DeviceIDs: []string{}, // 空表示所有设备
		GroupBy:   "daily",
	}

	logger.Debugf("📊 Getting daily statistics summary: start=%s, end=%s", startDate, endDate)

	response, err := h.statisticsService.GetMultipleDeviceStatistics(request)
	if err != nil {
		logger.Errorf("❌ Failed to get daily statistics summary: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("获取每日统计汇总失败: %v", err)})
		return
	}

	logger.Debugf("✅ Retrieved daily statistics summary")
	c.JSON(http.StatusOK, response.Summary)
}

/**
 * 按日期范围获取设备统计
 *
 * 功能：获取指定设备在指定日期范围内的统计数据
 *
 * HTTP方法：GET
 * 路径：/api/statistics/devices/{deviceId}/range
 *
 * 路径参数：
 * - deviceId：设备唯一标识符
 *
 * 查询参数：
 * - start_date：开始日期，格式YYYY-MM-DD，默认为一周前
 * - end_date：结束日期，格式YYYY-MM-DD，默认为今天
 *
 * 响应格式：
 * {
 *   "device_id": "device_001",
 *   "date_range": {
 *     "start_date": "2025-06-08",
 *     "end_date": "2025-06-15"
 *   },
 *   "daily_statistics": [...],
 *   "summary": {...}
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 统计计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/statistics/devices/device_001/range?start_date=2025-06-01&end_date=2025-06-15
 */
func (h *StatisticsHandler) GetDeviceStatisticsByDateRange(c *gin.Context) {
	deviceID := c.Param("deviceId")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" {
		startDate = time.Now().Add(-7 * 24 * time.Hour).Format("2006-01-02") // 默认一周前
	}
	if endDate == "" {
		endDate = time.Now().Format("2006-01-02") // 默认今天
	}

	request := &models.StatisticsRequest{
		StartDate: startDate,
		EndDate:   endDate,
		DeviceIDs: []string{deviceID},
		GroupBy:   "device",
	}

	logger.Debugf("📊 Getting device statistics by date range: device=%s, start=%s, end=%s",
		deviceID, startDate, endDate)

	response, err := h.statisticsService.GetMultipleDeviceStatistics(request)
	if err != nil {
		logger.Errorf("❌ Failed to get device statistics by date range: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("按日期范围获取设备统计失败: %v", err)})
		return
	}

	logger.Debugf("✅ Retrieved device statistics by date range for: %s", deviceID)
	c.JSON(http.StatusOK, response)
}

/**
 * 获取班次统计
 *
 * 功能：获取指定设备在指定日期的班次统计信息
 *
 * HTTP方法：GET
 * 路径：/api/statistics/devices/{deviceId}/shifts
 *
 * 路径参数：
 * - deviceId：设备唯一标识符
 *
 * 查询参数：
 * - date：统计日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "device_id": "device_001",
 *   "device_name": "设备名称",
 *   "date": "2025-06-15",
 *   "shift_statistics": [
 *     {
 *       "shift_name": "白班",
 *       "start_time": "08:00",
 *       "end_time": "20:00",
 *       "utilization_rate": 85.5,
 *       "production_count": 60
 *     }
 *   ]
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 设备不存在或无统计数据
 * - 500: 统计计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/statistics/devices/device_001/shifts?date=2025-06-15
 */
func (h *StatisticsHandler) GetShiftStatistics(c *gin.Context) {
	deviceID := c.Param("deviceId")
	date := c.Query("date")

	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	logger.Debugf("📊 Getting shift statistics: device=%s, date=%s", deviceID, date)

	stats, err := h.statisticsService.GetDeviceStatistics(date, deviceID)
	if err != nil {
		logger.Errorf("❌ Failed to get shift statistics: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("获取班次统计失败: %v", err)})
		return
	}

	if stats == nil {
		c.JSON(http.StatusNotFound, gin.H{"message": "没有找到统计数据"})
		return
	}

	// 只返回班次统计部分
	response := gin.H{
		"device_id":        stats.DeviceID,
		"device_name":      stats.DeviceName,
		"date":             stats.Date,
		"shift_statistics": stats.ShiftStatistics,
	}

	logger.Debugf("✅ Retrieved shift statistics for: %s", deviceID)
	c.JSON(http.StatusOK, response)
}

/**
 * 手动执行统计任务
 *
 * 功能：手动触发统计计算任务
 *
 * HTTP方法：POST
 * 路径：/api/statistics/scheduler/run
 *
 * 查询参数：
 * - date：统计日期，格式YYYY-MM-DD，默认为昨天
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "统计任务执行完成: 2025-06-14",
 *   "date": "2025-06-14"
 * }
 *
 * 状态码：
 * - 200: 执行成功
 * - 500: 执行失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/statistics/scheduler/run?date=2025-06-14
 */
func (h *StatisticsHandler) RunManualStatistics(c *gin.Context) {
	date := c.Query("date")
	if date == "" {
		date = time.Now().Add(-24 * time.Hour).Format("2006-01-02") // 默认昨天
	}

	logger.Infof("🔧 Manual statistics execution requested for date: %s", date)

	err := h.schedulerService.RunDailyStatisticsManually(date)
	if err != nil {
		logger.Errorf("❌ Manual statistics execution failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("手动执行统计任务失败: %v", err)})
		return
	}

	logger.Infof("✅ Manual statistics execution completed for: %s", date)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("统计任务执行完成: %s", date),
		"date":    date,
	})
}

/**
 * 获取调度器状态
 *
 * 功能：获取统计任务调度器的当前状态
 *
 * HTTP方法：GET
 * 路径：/api/statistics/scheduler/status
 *
 * 响应格式：
 * {
 *   "status": "running",
 *   "last_run": "2025-06-15T02:00:00Z",
 *   "next_run": "2025-06-16T02:00:00Z",
 *   "tasks_count": 5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/statistics/scheduler/status
 */
func (h *StatisticsHandler) GetSchedulerStatus(c *gin.Context) {
	logger.Debug("📊 Getting scheduler status")

	status := h.schedulerService.GetSchedulerStatus()

	logger.Debug("✅ Retrieved scheduler status")
	c.JSON(http.StatusOK, status)
}

/**
 * 获取工作时间设置
 *
 * 功能：获取系统的工作时间配置
 *
 * HTTP方法：GET
 * 路径：/api/statistics/worktime
 *
 * 响应格式：
 * {
 *   "day_start_time": "08:00",
 *   "day_end_time": "20:00",
 *   "break_periods": [
 *     {
 *       "start_time": "12:00",
 *       "end_time": "13:00",
 *       "name": "午休"
 *     }
 *   ],
 *   "work_mode": "OP1"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/statistics/worktime
 */
func (h *StatisticsHandler) GetWorkTimeSettings(c *gin.Context) {
	logger.Debug("📊 Getting work time settings")

	settings, err := h.workTimeService.GetWorkTimeSettings()
	if err != nil {
		logger.Errorf("❌ Failed to get work time settings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("获取工作时间设置失败: %v", err)})
		return
	}

	logger.Debug("✅ Retrieved work time settings")
	c.JSON(http.StatusOK, settings)
}

/**
 * 保存工作时间设置
 *
 * 功能：保存系统的工作时间配置
 *
 * HTTP方法：POST
 * 路径：/api/statistics/worktime
 *
 * 请求体：
 * {
 *   "day_start_time": "08:00",
 *   "day_end_time": "20:00",
 *   "break_periods": [
 *     {
 *       "start_time": "12:00",
 *       "end_time": "13:00",
 *       "name": "午休"
 *     }
 *   ],
 *   "work_mode": "OP1"
 * }
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "工作时间设置已保存"
 * }
 *
 * 状态码：
 * - 200: 保存成功
 * - 400: 请求参数无效
 * - 500: 保存失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/statistics/worktime
 * Body: {"day_start_time": "08:00", "day_end_time": "20:00", "work_mode": "OP1"}
 */
func (h *StatisticsHandler) SaveWorkTimeSettings(c *gin.Context) {
	var settings models.WorkTimeSettings
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("请求参数解析失败: %v", err)})
		return
	}

	logger.Debugf("📊 Saving work time settings: %+v", settings)

	// 验证设置
	if err := h.workTimeService.ValidateWorkTimeSettings(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("工作时间设置验证失败: %v", err)})
		return
	}

	// 保存设置
	if err := h.workTimeService.SaveWorkTimeSettings(&settings); err != nil {
		logger.Errorf("❌ Failed to save work time settings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("保存工作时间设置失败: %v", err)})
		return
	}

	logger.Info("✅ Work time settings saved successfully")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "工作时间设置已保存",
	})
}
