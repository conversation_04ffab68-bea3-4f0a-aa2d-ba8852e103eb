/**
 * 健康检查处理器
 *
 * 功能描述：
 * 提供系统健康检查和基础状态查询的处理器
 * 用于负载均衡器、监控系统等检查服务运行状态
 *
 * 主要功能：
 * - 系统健康状态检查
 * - 服务基本信息展示
 * - 运行状态确认
 *
 * @package health
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package health

import (
	"github.com/gin-gonic/gin"
)

/**
 * 健康检查处理器结构体
 *
 * 功能：封装健康检查相关的处理逻辑
 *
 * 特点：
 * - 无状态设计，不依赖外部服务
 * - 轻量级响应，适合高频调用
 * - 标准化的健康检查格式
 */
type HealthHandler struct {
	// 可以在这里添加依赖的服务，如监控服务等
	// monitoringService *services.MonitoringService
}

/**
 * 创建新的健康检查处理器实例
 *
 * 功能：初始化健康检查处理器
 *
 * 返回值：
 * - *HealthHandler: 健康检查处理器实例
 *
 * @example
 * healthHandler := health.NewHealthHandler()
 */
func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

/**
 * 健康检查接口处理器
 *
 * 功能：返回服务健康状态信息
 *
 * HTTP方法：GET
 * 路径：/health
 *
 * 响应格式：
 * {
 *   "status": "ok",
 *   "service": "12_server_api"
 * }
 *
 * 状态码：
 * - 200: 服务正常运行
 *
 * 使用场景：
 * - 负载均衡器健康检查
 * - 监控系统状态探测
 * - 容器编排健康检查
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /health
 * Response: {"status": "ok", "service": "12_server_api"}
 */
func (h *HealthHandler) GetHealth(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"service": "server_api",
	})
}

/**
 * 根路径欢迎信息处理器
 *
 * 功能：返回系统欢迎信息和基本状态
 *
 * HTTP方法：GET
 * 路径：/
 *
 * 响应格式：
 * {
 *   "message": "欢迎使用设备状态监控系统 API",
 *   "version": "Go版本 - 具备MongoDB管理功能",
 *   "status": "running"
 * }
 *
 * 状态码：
 * - 200: 服务正常运行
 *
 * 使用场景：
 * - 服务基本信息查询
 * - API可用性确认
 * - 版本信息获取
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /
 * Response: {
 *   "message": "欢迎使用设备状态监控系统 API",
 *   "version": "Go版本 - 具备MongoDB管理功能",
 *   "status": "running"
 * }
 */
func (h *HealthHandler) GetWelcome(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "欢迎使用智能制造系统 API",
		"version": "v1.2.2",
		"status":  "running",
	})
}
