/**
 * 设备利用率仪表板处理器
 *
 * 功能描述：
 * 处理设备利用率仪表板相关的API请求，包括利用率概览、排名、趋势分析等功能
 * 提供完整的设备利用率统计、分析和可视化支持
 *
 * 主要功能：
 * - 设备利用率概览：总体利用率统计和分区间分析
 * - 设备利用率排名：各设备利用率对比和状态构成分析
 * - 班次利用率分析：基于班次设置的利用率统计
 * - 利用率趋势分析：日趋势和班次趋势的时间序列分析
 * - 工作时间配置：支持OP1和OP2两种利用率计算模式
 * - 机器页面V2：统一数据处理，前端直接渲染
 *
 * 技术特性：
 * - 实时数据获取：当天数据从InfluxDB→Redis，历史数据从MongoDB
 * - 灵活的时间计算：支持跨天查询和班次划分
 * - 多维度统计：设备、班次、日期等多个维度的统计分析
 * - 性能监控集成：自动记录API调用指标
 * - 数据预处理：后端统一处理，减少前端计算负担
 *
 * API端点：
 * - GET /api/utilization/overview - 获取设备利用率概览
 * - GET /api/utilization/devices - 获取各设备利用率排名
 * - GET /api/utilization/shifts - 获取各设备班次利用率排名
 * - GET /api/utilization/daily-trend - 获取每日利用率趋势
 * - GET /api/utilization/shift-trend - 获取每日班次利用率趋势
 * - GET /api/utilization/machine-v2 - 获取机器页面V2完整数据
 * - GET /api/utilization/dashboard-v2 - 获取仪表板V2完整数据
 *
 * @package utilization
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package utilization

import (
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 设备利用率处理器结构体
 *
 * 功能：处理设备利用率相关的HTTP请求
 *
 * 依赖服务：
 * - DataService：提供设备数据访问接口
 * - WorkTimeService：提供工作时间配置管理
 * - StatisticsService：提供统计数据计算和查询
 * - MonitoringService：提供API性能监控
 */
type UtilizationHandler struct {
	dataService       *services.DataService
	workTimeService   *services.WorkTimeService
	statisticsService *services.StatisticsService
	monitoringService *services.MonitoringService
}

/**
 * 创建设备利用率处理器实例
 *
 * 功能：初始化设备利用率处理器，注入必要的服务依赖
 *
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 * @param {*services.StatisticsService} statisticsService - 统计服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @returns {*UtilizationHandler} 设备利用率处理器实例
 *
 * @example
 * handler := utilization.NewUtilizationHandler(dataService, workTimeService, statisticsService, monitoringService)
 */
func NewUtilizationHandler(
	dataService *services.DataService,
	workTimeService *services.WorkTimeService,
	statisticsService *services.StatisticsService,
	monitoringService *services.MonitoringService,
) *UtilizationHandler {
	return &UtilizationHandler{
		dataService:       dataService,
		workTimeService:   workTimeService,
		statisticsService: statisticsService,
		monitoringService: monitoringService,
	}
}

/**
 * 获取设备利用率概览
 *
 * 功能：获取总体设备利用率和分区间统计信息
 *
 * HTTP方法：GET
 * 路径：/api/utilization/overview
 *
 * 查询参数：
 * - date：日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "date": "2025-06-15",
 *   "overall_utilization": 82.5,
 *   "total_devices": 10,
 *   "utilization_zones": [
 *     {
 *       "zone": "高利用率 (80-100%)",
 *       "count": 6,
 *       "percentage": 60.0
 *     }
 *   ],
 *   "device_utilizations": [...],
 *   "work_time_settings": {...},
 *   "calculation_mode": "OP1",
 *   "working_hours": 12.0,
 *   "last_updated": "2025-06-15T10:30:00Z"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/utilization/overview?date=2025-06-15
 */
func (h *UtilizationHandler) GetUtilizationOverview(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/utilization/overview", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取查询参数
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))

	logger.Debugf("📊 Getting utilization overview for date: %s", date)

	// 获取工作时间设置
	workTimeSettings, err := h.workTimeService.GetWorkTimeSettings()
	if err != nil {
		logger.Errorf("❌ Failed to get work time settings: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取工作时间设置失败",
			Error:   err.Error(),
		})
		return
	}

	// 获取所有设备的利用率数据
	var overview *models.UtilizationOverview

	// 判断是否为今日数据，如果是今日数据则使用实时计算
	today := time.Now().Format("2006-01-02")
	logger.Debugf("🔍 日期比较: 请求日期=%s, 今日日期=%s", date, today)
	if date == today {
		// 今日数据使用实时计算方法
		logger.Debugf("✅ 使用实时计算方法")
		overview, err = h.calculateRealtimeUtilizationOverview(date, workTimeSettings)
	} else {
		// 历史数据使用统计数据计算方法
		logger.Debugf("✅ 使用历史数据计算方法")
		overview, err = h.calculateUtilizationOverview(date, workTimeSettings)
	}

	if err != nil {
		logger.Errorf("❌ Failed to calculate utilization overview: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "计算设备利用率概览失败",
			Error:   err.Error(),
		})
		return
	}

	logger.Debugf("✅ Retrieved utilization overview for %d devices", len(overview.DeviceUtilizations))
	c.JSON(http.StatusOK, overview)
}

/**
 * 获取各设备利用率排名
 *
 * 功能：获取各设备的利用率排名和状态构成分析
 *
 * HTTP方法：GET
 * 路径：/api/utilization/devices
 *
 * 查询参数：
 * - date：日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "date": "2025-06-15",
 *   "devices": [
 *     {
 *       "device_id": "device_001",
 *       "device_name": "设备名称",
 *       "utilization_rate": 85.5,
 *       "rank": 1,
 *       "status_durations": {...},
 *       "production_count": 120,
 *       "plan_count": 150
 *     }
 *   ],
 *   "total_devices": 10,
 *   "average_utilization": 82.5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/utilization/devices?date=2025-06-15
 */
func (h *UtilizationHandler) GetDeviceUtilizationRanking(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/utilization/devices", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取查询参数
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))

	logger.Debugf("📊 Getting device utilization ranking for date: %s", date)

	// 获取工作时间设置
	workTimeSettings, err := h.workTimeService.GetWorkTimeSettings()
	if err != nil {
		logger.Errorf("❌ Failed to get work time settings: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取工作时间设置失败",
			Error:   err.Error(),
		})
		return
	}

	// 计算设备利用率排名
	var ranking *models.DeviceUtilizationRanking

	// 判断是否为今日数据，如果是今日数据则使用实时计算
	today := time.Now().Format("2006-01-02")
	logger.Debugf("🔍 日期比较: 请求日期=%s, 今日日期=%s", date, today)
	if date == today {
		// 今日数据使用实时计算方法
		logger.Debugf("✅ 使用实时计算方法")
		ranking, err = h.calculateRealtimeDeviceUtilizationRanking(date, workTimeSettings)
	} else {
		// 历史数据使用统计数据计算方法
		logger.Debugf("✅ 使用历史数据计算方法")
		ranking, err = h.calculateDeviceUtilizationRanking(date, workTimeSettings)
	}

	if err != nil {
		logger.Errorf("❌ Failed to calculate device utilization ranking: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "计算设备利用率排名失败",
			Error:   err.Error(),
		})
		return
	}

	logger.Debugf("✅ Retrieved device utilization ranking for %d devices", len(ranking.Devices))
	c.JSON(http.StatusOK, ranking)
}

/**
 * 获取各设备班次利用率排名
 *
 * 功能：获取各设备按班次划分的利用率排名和状态构成分析
 *
 * HTTP方法：GET
 * 路径：/api/utilization/shifts
 *
 * 查询参数：
 * - date：日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "date": "2025-06-15",
 *   "devices": [
 *     {
 *       "device_id": "device_001",
 *       "device_name": "设备名称",
 *       "shifts": [
 *         {
 *           "shift_name": "白班",
 *           "utilization_rate": 85.5,
 *           "status_durations": {...}
 *         }
 *       ]
 *     }
 *   ],
 *   "total_devices": 10
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/utilization/shifts?date=2025-06-15
 */
func (h *UtilizationHandler) GetShiftUtilizationRanking(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/utilization/shifts", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取查询参数
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))

	logger.Debugf("📊 Getting shift utilization ranking for date: %s", date)

	// 获取工作时间设置
	workTimeSettings, err := h.workTimeService.GetWorkTimeSettings()
	if err != nil {
		logger.Errorf("❌ Failed to get work time settings: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取工作时间设置失败",
			Error:   err.Error(),
		})
		return
	}

	// 计算班次利用率排名
	ranking, err := h.calculateShiftUtilizationRanking(date, workTimeSettings)
	if err != nil {
		logger.Errorf("❌ Failed to calculate shift utilization ranking: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "计算班次利用率排名失败",
			Error:   err.Error(),
		})
		return
	}

	logger.Debugf("✅ Retrieved shift utilization ranking for %d devices", len(ranking.Devices))
	c.JSON(http.StatusOK, ranking)
}

/**
 * 获取每日利用率趋势
 *
 * 功能：获取指定时间范围内每日的整体设备利用率趋势
 *
 * HTTP方法：GET
 * 路径：/api/utilization/daily-trend
 *
 * 查询参数：
 * - start_date：开始日期，格式YYYY-MM-DD，可选
 * - end_date：结束日期，格式YYYY-MM-DD，可选
 * - days：天数（从今天往前推），可选
 *
 * 响应格式：
 * {
 *   "start_date": "2025-06-08",
 *   "end_date": "2025-06-15",
 *   "daily_data": [
 *     {
 *       "date": "2025-06-15",
 *       "overall_utilization": 82.5,
 *       "total_devices": 10,
 *       "device_count_by_zone": {...}
 *     }
 *   ],
 *   "summary": {
 *     "average_utilization": 80.2,
 *     "max_utilization": 85.5,
 *     "min_utilization": 75.0
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/utilization/daily-trend?start_date=2025-06-08&end_date=2025-06-15
 * GET /api/utilization/daily-trend?days=7
 */
func (h *UtilizationHandler) GetDailyUtilizationTrend(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/utilization/daily-trend", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取查询参数
	startDate, endDate := h.parseDateRange(c)

	logger.Debugf("📊 Getting daily utilization trend from %s to %s", startDate, endDate)

	// 获取工作时间设置
	workTimeSettings, err := h.workTimeService.GetWorkTimeSettings()
	if err != nil {
		logger.Errorf("❌ Failed to get work time settings: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取工作时间设置失败",
			Error:   err.Error(),
		})
		return
	}

	// 计算每日利用率趋势
	trend, err := h.calculateDailyUtilizationTrend(startDate, endDate, workTimeSettings)
	if err != nil {
		logger.Errorf("❌ Failed to calculate daily utilization trend: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "计算每日利用率趋势失败",
			Error:   err.Error(),
		})
		return
	}

	logger.Debugf("✅ Retrieved daily utilization trend for %d days", len(trend.DailyData))
	c.JSON(http.StatusOK, trend)
}

/**
 * 解析日期范围参数
 *
 * 功能：解析HTTP请求中的日期范围参数，设置默认值
 *
 * @param {*gin.Context} c - Gin上下文对象
 * @returns {string, string} 开始日期和结束日期
 */
func (h *UtilizationHandler) parseDateRange(c *gin.Context) (string, string) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	daysStr := c.Query("days")

	logger.Debugf("🔍 Parsing date range parameters - start_date=%s, end_date=%s, days=%s", startDate, endDate, daysStr)

	// 如果指定了天数，从今天往前推
	if daysStr != "" {
		if days, err := strconv.Atoi(daysStr); err == nil && days > 0 {
			endDate = time.Now().Format("2006-01-02")
			startDate = time.Now().AddDate(0, 0, -days+1).Format("2006-01-02")
			logger.Debugf("📅 Using days parameter: days=%d, calculated: start_date=%s, end_date=%s", days, startDate, endDate)
			return startDate, endDate
		}
	}

	// 默认值处理
	if endDate == "" {
		endDate = time.Now().Format("2006-01-02")
		logger.Debugf("📅 Using default end date: %s", endDate)
	}
	if startDate == "" {
		startDate = time.Now().AddDate(0, 0, -6).Format("2006-01-02") // 默认7天
		logger.Debugf("📅 Using default start date: %s", startDate)
	}

	logger.Debugf("✅ Final parsed result: start_date=%s, end_date=%s", startDate, endDate)
	return startDate, endDate
}

// 以下是计算方法的简化实现，实际项目中需要根据具体业务逻辑完善

/**
 * 计算设备利用率概览
 */
func (h *UtilizationHandler) calculateUtilizationOverview(date string, workTimeSettings *models.WorkTimeSettings) (*models.UtilizationOverview, error) {
	// 获取所有设备的状态历史数据
	devices, err := h.dataService.GetDevicesStatus([]string{}, date)
	if err != nil {
		return nil, fmt.Errorf("获取设备状态失败: %v", err)
	}

	if len(devices) == 0 {
		return &models.UtilizationOverview{
			Date:               date,
			OverallUtilization: 0,
			TotalDevices:       0,
			UtilizationZones:   []models.UtilizationZone{},
			DeviceUtilizations: []models.DeviceUtilization{},
			WorkTimeSettings:   *workTimeSettings,
			CalculationMode:    workTimeSettings.UtilizationMode,
			WorkingHours:       0,
			LastUpdated:        time.Now(),
		}, nil
	}

	// 计算工作时长
	workingHours, err := h.calculateWorkingHours(workTimeSettings)
	if err != nil {
		return nil, fmt.Errorf("计算工作时长失败: %v", err)
	}

	// 使用并行计算各设备利用率
	deviceUtilizations, totalUtilization := h.calculateDeviceUtilizationsParallel(devices, date, workingHours, workTimeSettings, false)

	// 计算总体利用率
	overallUtilization := 0.0
	if len(deviceUtilizations) > 0 {
		overallUtilization = totalUtilization / float64(len(deviceUtilizations))
	}

	// 计算利用率区间统计
	utilizationZones := h.calculateUtilizationZones(deviceUtilizations)

	return &models.UtilizationOverview{
		Date:               date,
		OverallUtilization: overallUtilization,
		TotalDevices:       len(deviceUtilizations),
		UtilizationZones:   utilizationZones,
		DeviceUtilizations: deviceUtilizations,
		WorkTimeSettings:   *workTimeSettings,
		CalculationMode:    workTimeSettings.UtilizationMode,
		WorkingHours:       workingHours,
		LastUpdated:        time.Now(),
	}, nil
}

/**
 * 计算实时设备利用率排名（今日数据专用）
 *
 * 功能：计算今日实时设备利用率，使用动态工作时长计算
 * 工作时长 = 当前时间 - 每天开始时间 - 已过休息时间
 */
func (h *UtilizationHandler) calculateRealtimeDeviceUtilizationRanking(date string, workTimeSettings *models.WorkTimeSettings) (*models.DeviceUtilizationRanking, error) {
	logger.Debugf("🔍 计算今日实时设备利用率排名: 日期=%s", date)

	// 获取实时利用率概览数据
	overview, err := h.calculateRealtimeUtilizationOverview(date, workTimeSettings)
	if err != nil {
		return nil, err
	}

	// 按利用率排序
	devices := overview.DeviceUtilizations
	for i := 0; i < len(devices)-1; i++ {
		for j := i + 1; j < len(devices); j++ {
			if devices[i].UtilizationRate < devices[j].UtilizationRate {
				devices[i], devices[j] = devices[j], devices[i]
			}
		}
	}

	// 设置排名
	for i := range devices {
		devices[i].Rank = i + 1
	}

	// 创建状态图例
	statusLegend := []models.StatusLegendItem{
		{Status: "production", Label: "生产中", Color: "#22c55e"},
		{Status: "idle", Label: "空闲", Color: "#f59e0b"},
		{Status: "fault", Label: "故障", Color: "#ef4444"},
		{Status: "adjusting", Label: "调机", Color: "#3b82f6"},
		{Status: "shutdown", Label: "停机", Color: "#6b7280"},
		{Status: "disconnected", Label: "断线", Color: "#9ca3af"},
	}

	logger.Debugf("✅ 实时利用率排名计算完成: 设备数量=%d", len(devices))

	return &models.DeviceUtilizationRanking{
		Date:             date,
		Devices:          devices,
		StatusLegend:     statusLegend,
		WorkTimeSettings: *workTimeSettings,
		LastUpdated:      time.Now(),
	}, nil
}

/**
 * 计算实时设备利用率概览（今日数据专用）
 *
 * 功能：计算今日实时设备利用率概览，使用动态工作时长计算
 * 以配置的每天开始时间为分界线统计当天数据
 */
func (h *UtilizationHandler) calculateRealtimeUtilizationOverview(date string, workTimeSettings *models.WorkTimeSettings) (*models.UtilizationOverview, error) {
	logger.Debugf("🔍 计算今日实时利用率概览: 日期=%s", date)

	// 获取所有设备的状态历史数据（以每天开始时间为分界线）
	devices, err := h.dataService.GetDevicesStatus([]string{}, date)
	if err != nil {
		return nil, fmt.Errorf("获取设备状态失败: %v", err)
	}

	if len(devices) == 0 {
		return &models.UtilizationOverview{
			Date:               date,
			OverallUtilization: 0,
			TotalDevices:       0,
			UtilizationZones:   []models.UtilizationZone{},
			DeviceUtilizations: []models.DeviceUtilization{},
			WorkTimeSettings:   *workTimeSettings,
			CalculationMode:    workTimeSettings.UtilizationMode,
			WorkingHours:       0,
			LastUpdated:        time.Now(),
		}, nil
	}

	// 计算实时工作时长
	realtimeWorkingHours, err := h.calculateRealtimeWorkingHours(workTimeSettings)
	if err != nil {
		return nil, fmt.Errorf("计算实时工作时长失败: %v", err)
	}

	logger.Debugf("📊 实时工作时长: %.2f小时", realtimeWorkingHours)

	// 使用并行计算各设备利用率
	deviceUtilizations, totalUtilization := h.calculateDeviceUtilizationsParallel(devices, date, realtimeWorkingHours, workTimeSettings, true)

	// 计算总体利用率
	overallUtilization := 0.0
	if len(deviceUtilizations) > 0 {
		overallUtilization = totalUtilization / float64(len(deviceUtilizations))
	}

	// 计算利用率区间统计
	utilizationZones := h.calculateUtilizationZones(deviceUtilizations)

	logger.Debugf("✅ 实时利用率概览计算完成: 设备数=%d, 平均利用率=%.2f%%, 工作时长=%.2f小时",
		len(deviceUtilizations), overallUtilization, realtimeWorkingHours)

	return &models.UtilizationOverview{
		Date:               date,
		OverallUtilization: overallUtilization,
		TotalDevices:       len(deviceUtilizations),
		UtilizationZones:   utilizationZones,
		DeviceUtilizations: deviceUtilizations,
		WorkTimeSettings:   *workTimeSettings,
		CalculationMode:    workTimeSettings.UtilizationMode,
		WorkingHours:       realtimeWorkingHours,
		LastUpdated:        time.Now(),
	}, nil
}

/**
 * 计算设备利用率排名（历史数据）
 */
func (h *UtilizationHandler) calculateDeviceUtilizationRanking(date string, workTimeSettings *models.WorkTimeSettings) (*models.DeviceUtilizationRanking, error) {
	// 获取利用率概览数据
	overview, err := h.calculateUtilizationOverview(date, workTimeSettings)
	if err != nil {
		return nil, err
	}

	// 按利用率排序
	devices := overview.DeviceUtilizations
	for i := 0; i < len(devices)-1; i++ {
		for j := i + 1; j < len(devices); j++ {
			if devices[i].UtilizationRate < devices[j].UtilizationRate {
				devices[i], devices[j] = devices[j], devices[i]
			}
		}
	}

	// 设置排名
	for i := range devices {
		devices[i].Rank = i + 1
	}

	// 创建状态图例
	statusLegend := []models.StatusLegendItem{
		{Status: "production", Label: "生产中", Color: "#22c55e"},
		{Status: "idle", Label: "空闲", Color: "#f59e0b"},
		{Status: "fault", Label: "故障", Color: "#ef4444"},
		{Status: "adjusting", Label: "调机", Color: "#3b82f6"},
		{Status: "shutdown", Label: "停机", Color: "#6b7280"},
		{Status: "disconnected", Label: "断线", Color: "#9ca3af"},
	}

	return &models.DeviceUtilizationRanking{
		Date:             date,
		Devices:          devices,
		StatusLegend:     statusLegend,
		WorkTimeSettings: *workTimeSettings,
		LastUpdated:      time.Now(),
	}, nil
}

/**
 * 计算班次利用率排名
 */
func (h *UtilizationHandler) calculateShiftUtilizationRanking(date string, workTimeSettings *models.WorkTimeSettings) (*models.ShiftUtilizationRanking, error) {
	// 简化实现：返回默认数据
	return &models.ShiftUtilizationRanking{
		Date:             date,
		Devices:          []models.ShiftUtilization{},
		Shifts:           []models.ShiftInfo{},
		StatusLegend:     []models.StatusLegendItem{},
		WorkTimeSettings: *workTimeSettings,
		LastUpdated:      time.Now(),
	}, nil
}

/**
 * 计算每日利用率趋势
 */
func (h *UtilizationHandler) calculateDailyUtilizationTrend(startDate, endDate string, workTimeSettings *models.WorkTimeSettings) (*models.DailyUtilizationTrend, error) {
	logger.Debugf("🔍 计算每日利用率趋势: 开始日期=%s, 结束日期=%s", startDate, endDate)

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, fmt.Errorf("解析开始日期失败: %v", err)
	}
	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, fmt.Errorf("解析结束日期失败: %v", err)
	}

	// 使用并行计算每日利用率趋势，传递工作时间设置
	dailyData, _, maxRate, minRate, _ := h.calculateDailyTrendParallel(start, end, workTimeSettings)

	return &models.DailyUtilizationTrend{
		StartDate:        startDate,
		EndDate:          endDate,
		DailyData:        dailyData,
		AverageRate:      0,
		MaxRate:          maxRate,
		MinRate:          minRate,
		TrendDirection:   "",
		WorkTimeSettings: *workTimeSettings,
		LastUpdated:      time.Now(),
	}, nil
}

/**
 * 计算利用率区间统计
 */
func (h *UtilizationHandler) calculateUtilizationZones(deviceUtilizations []models.DeviceUtilization) []models.UtilizationZone {
	zones := []models.UtilizationZone{
		{Name: "高利用率", MinRate: 80, MaxRate: 100, Color: "#22c55e"},
		{Name: "中利用率", MinRate: 50, MaxRate: 80, Color: "#f59e0b"},
		{Name: "低利用率", MinRate: 0, MaxRate: 50, Color: "#ef4444"},
	}

	totalDevices := len(deviceUtilizations)

	for i := range zones {
		count := 0
		for _, device := range deviceUtilizations {
			if device.UtilizationRate >= zones[i].MinRate && device.UtilizationRate < zones[i].MaxRate {
				count++
			}
		}

		zones[i].DeviceCount = count
		if totalDevices > 0 {
			zones[i].Percentage = (float64(count) / float64(totalDevices)) * 100
		}
	}

	return zones
}

/**
 * 创建状态详情
 *
 * 功能：根据状态名称、持续时间和总时间创建状态详情对象
 *
 * @param {string} status - 状态名称
 * @param {float64} duration - 持续时间（小时）
 * @param {float64} totalTime - 总时间（小时）
 * @returns {models.StatusDetail} 状态详情对象
 */
func (h *UtilizationHandler) createStatusDetail(status string, duration, totalTime float64) models.StatusDetail {
	percentage := 0.0
	if totalTime > 0 {
		percentage = (duration / totalTime) * 100
	}

	// 状态颜色映射
	colorMap := map[string]string{
		"production":   "#22c55e", // 绿色
		"idle":         "#f59e0b", // 琥珀色
		"fault":        "#ef4444", // 红色
		"adjusting":    "#3b82f6", // 蓝色
		"shutdown":     "#6b7280", // 灰色
		"disconnected": "#9ca3af", // 浅灰色
	}

	color := colorMap[status]
	if color == "" {
		color = "#6b7280" // 默认灰色
	}

	return models.StatusDetail{
		Duration:   duration,
		Percentage: percentage,
		Color:      color,
	}
}

/**
 * 计算理论工作时间（用于显示）
 *
 * 功能：根据工作时间设置计算理论工作时间，在OP2模式下会减去休息时间
 *
 * @param {*models.WorkTimeSettings} workTimeSettings - 工作时间设置
 * @returns {float64} 理论工作时间（小时）
 */
func (h *UtilizationHandler) calculateTheoreticalWorkingHours(workTimeSettings *models.WorkTimeSettings) float64 {
	// 基础工作时间（24小时）
	workingHours := 24.0

	// 如果是OP2模式，需要减去休息时间
	if workTimeSettings.UtilizationMode == "OP2" {
		for _, rest := range workTimeSettings.RestPeriods {
			if !rest.Enabled {
				continue
			}

			// 计算休息时间长度
			restDuration := h.calculateTimeDuration(rest.StartTime, rest.EndTime)
			workingHours -= restDuration
		}
	}

	return workingHours
}

/**
 * 计算时间段长度（小时）
 *
 * 功能：计算两个时间点之间的时长，支持跨天计算
 *
 * @param {string} startTime - 开始时间（格式：HH:MM）
 * @param {string} endTime - 结束时间（格式：HH:MM）
 * @returns {float64} 时间长度（小时）
 */
func (h *UtilizationHandler) calculateTimeDuration(startTime, endTime string) float64 {
	start, err := time.Parse("15:04", startTime)
	if err != nil {
		logger.Warnf("⚠️ 解析开始时间失败: %v", err)
		return 0
	}

	end, err := time.Parse("15:04", endTime)
	if err != nil {
		logger.Warnf("⚠️ 解析结束时间失败: %v", err)
		return 0
	}

	// 处理跨天情况
	if end.Before(start) {
		end = end.Add(24 * time.Hour)
	}

	duration := end.Sub(start)
	return duration.Hours()
}

/**
 * 获取设备信息映射
 *
 * 功能：从MongoDB获取所有设备的详细信息，创建DeviceID到设备信息的映射
 *
 * @returns {map[string]*models.Device} 设备信息映射
 */
func (h *UtilizationHandler) getDeviceInfoMap() map[string]*models.Device {
	// 检查DataService是否可用
	if h.dataService == nil {
		return make(map[string]*models.Device)
	}

	// 获取所有设备配置
	devices, err := h.dataService.GetAllDeviceConfigs()
	if err != nil {
		return make(map[string]*models.Device)
	}

	// 创建设备ID到设备信息的映射
	deviceMap := make(map[string]*models.Device)
	for i := range devices {
		device := &devices[i]
		deviceMap[device.DeviceID] = device
	}

	return deviceMap
}

/**
 * 计算工作时长
 */
func (h *UtilizationHandler) calculateWorkingHours(workTimeSettings *models.WorkTimeSettings) (float64, error) {
	// 基础工作时间（24小时）
	workingHours := 24.0

	// 如果是OP2模式，需要减去休息时间
	if workTimeSettings.UtilizationMode == "OP2" {
		for _, rest := range workTimeSettings.RestPeriods {
			if !rest.Enabled {
				continue
			}

			// 计算休息时间长度
			restDuration := h.calculateTimeDuration(rest.StartTime, rest.EndTime)

			workingHours -= restDuration
		}
	}

	return workingHours, nil
}

/**
 * 计算实时工作时长（今日数据专用）
 *
 * 功能：根据当前时间动态计算有效工作时长
 * 逻辑：
 * 1. 基础时长 = 当前时间 - 每天开始时间
 * 2. 如果遇到休息时间但未超过：减去 (当前时间 - 休息开始时间)
 * 3. 如果超过休息时间：减去 (休息结束时间 - 休息开始时间)
 *
 * @param {*models.WorkTimeSettings} workTimeSettings - 工作时间设置
 * @returns {float64} 实时工作时长（小时）
 * @returns {error} 计算错误
 */
func (h *UtilizationHandler) calculateRealtimeWorkingHours(workTimeSettings *models.WorkTimeSettings) (float64, error) {
	now := time.Now()
	logger.Debugf("🕐 开始计算实时工作时长: 当前时间=%s", now.Format("15:04:05"))

	// 解析每天开始时间
	dayStartTime, err := time.Parse("15:04", workTimeSettings.DayStartTime)
	if err != nil {
		return 0, fmt.Errorf("解析每天开始时间失败: %v", err)
	}

	// 构建今日的开始时间
	today := time.Date(now.Year(), now.Month(), now.Day(), dayStartTime.Hour(), dayStartTime.Minute(), 0, 0, now.Location())

	// 如果当前时间早于今日开始时间，说明是跨天情况，使用昨天的开始时间
	if now.Before(today) {
		today = today.AddDate(0, 0, -1)
	}

	// 计算基础工作时长：当前时间 - 每天开始时间
	baseWorkingDuration := now.Sub(today)
	workingHours := baseWorkingDuration.Hours()

	logger.Debugf("📊 基础计算: 开始时间=%s, 当前时间=%s, 基础工作时长=%.2f小时",
		today.Format("15:04:05"), now.Format("15:04:05"), workingHours)

	// 如果是OP2模式，需要减去休息时间
	if workTimeSettings.UtilizationMode == "OP2" {
		logger.Debugf("🔧 OP2模式，开始计算休息时间扣除...")

		for _, rest := range workTimeSettings.RestPeriods {
			if !rest.Enabled {
				logger.Debugf("⏭️ 休息时间 %s 未启用，跳过", rest.Name)
				continue
			}

			// 解析休息时间
			restStart, err := time.Parse("15:04", rest.StartTime)
			if err != nil {
				logger.Warnf("⚠️ 解析休息开始时间失败 %s: %v", rest.StartTime, err)
				continue
			}

			restEnd, err := time.Parse("15:04", rest.EndTime)
			if err != nil {
				logger.Warnf("⚠️ 解析休息结束时间失败 %s: %v", rest.EndTime, err)
				continue
			}

			// 构建今日的休息时间
			todayRestStart := time.Date(now.Year(), now.Month(), now.Day(), restStart.Hour(), restStart.Minute(), 0, 0, now.Location())
			todayRestEnd := time.Date(now.Year(), now.Month(), now.Day(), restEnd.Hour(), restEnd.Minute(), 0, 0, now.Location())

			// 处理跨天的休息时间
			if todayRestEnd.Before(todayRestStart) {
				todayRestEnd = todayRestEnd.AddDate(0, 0, 1)
			}

			// 如果休息时间在工作开始时间之前，跳过
			if todayRestEnd.Before(today) {
				logger.Debugf("⏭️ 休息时间 %s (%s-%s) 在工作开始时间之前，跳过",
					rest.Name, rest.StartTime, rest.EndTime)
				continue
			}

			// 如果休息开始时间在工作开始时间之前，调整为工作开始时间
			if todayRestStart.Before(today) {
				todayRestStart = today
				logger.Debugf("🔧 调整休息开始时间为工作开始时间: %s", today.Format("15:04:05"))
			}

			var restDurationToDeduct float64

			if now.After(todayRestEnd) {
				// 情况3：当前时间超过了休息时间，减去整个休息时间
				restDurationToDeduct = todayRestEnd.Sub(todayRestStart).Hours()
				logger.Debugf("✅ 休息时间 %s 已结束，扣除完整时长: %.2f小时", rest.Name, restDurationToDeduct)
			} else if now.After(todayRestStart) {
				// 情况2：当前时间在休息时间内，减去已过的休息时间
				restDurationToDeduct = now.Sub(todayRestStart).Hours()
				logger.Debugf("⏰ 休息时间 %s 进行中，扣除已过时长: %.2f小时", rest.Name, restDurationToDeduct)
			} else {
				// 情况1：当前时间未到休息时间，不扣除
				logger.Debugf("⏭️ 休息时间 %s 尚未开始，不扣除", rest.Name)
				continue
			}

			workingHours -= restDurationToDeduct
			logger.Debugf("📉 扣除休息时间后: %.2f小时", workingHours)
		}
	}

	// 确保工作时长不为负数
	if workingHours < 0 {
		workingHours = 0
	}

	logger.Debugf("✅ 最终实时工作时长: %.2f小时", workingHours)
	return workingHours, nil
}

/**
 * 计算单个设备实时利用率（今日数据专用）
 *
 * 功能：基于实时工作时长计算设备利用率
 * 与历史数据计算的区别：使用动态计算的实时工作时长
 *
 * @param {models.DeviceStatus} device - 设备状态信息
 * @param {[]models.DeviceStatusHistory} statusHistory - 设备状态历史
 * @param {float64} realtimeWorkingHours - 实时工作时长
 * @param {*models.WorkTimeSettings} workTimeSettings - 工作时间设置
 * @returns {models.DeviceUtilization} 设备利用率信息
 */
func (h *UtilizationHandler) calculateRealtimeDeviceUtilization(
	device models.DeviceStatus,
	statusHistory []models.DeviceStatusHistory,
	realtimeWorkingHours float64,
	workTimeSettings *models.WorkTimeSettings,
) models.DeviceUtilization {
	logger.Debugf("🔍 计算设备 %s 实时利用率", device.DeviceID)

	// 初始化状态时长统计
	statusDurations := map[string]float64{
		"production":   0,
		"idle":         0,
		"fault":        0,
		"adjusting":    0,
		"shutdown":     0,
		"disconnected": 0,
	}

	// 计算各状态持续时长
	for _, record := range statusHistory {
		duration := 0.0
		if record.Duration > 0 {
			duration = float64(record.Duration) / 3600.0 // 转换为小时
		} else {
			// 如果没有duration字段，通过时间差计算
			duration = record.EndTime.Sub(record.StartTime).Hours()
		}

		// 映射状态
		status := h.mapDeviceStatus(record.CurrentStatus)
		if _, exists := statusDurations[status]; exists {
			statusDurations[status] += duration
		}
	}

	// 计算利用率 - 基于实时工作时长
	productiveTime := statusDurations["production"]
	utilizationRate := 0.0
	if realtimeWorkingHours > 0 {
		// 利用率 = 运行时间 / 实时工作时间
		utilizationRate = (productiveTime / realtimeWorkingHours) * 100
	}

	// 确保利用率不超过100%
	if utilizationRate > 100 {
		utilizationRate = 100
	}

	// 构建状态构成
	totalTime := 0.0
	for _, duration := range statusDurations {
		totalTime += duration
	}

	statusComposition := models.StatusComposition{
		Production:   h.createStatusDetail("production", statusDurations["production"], totalTime),
		Idle:         h.createStatusDetail("idle", statusDurations["idle"], totalTime),
		Fault:        h.createStatusDetail("fault", statusDurations["fault"], totalTime),
		Adjusting:    h.createStatusDetail("adjusting", statusDurations["adjusting"], totalTime),
		Shutdown:     h.createStatusDetail("shutdown", statusDurations["shutdown"], totalTime),
		Disconnected: h.createStatusDetail("disconnected", statusDurations["disconnected"], totalTime),
	}

	// 获取设备详细信息
	deviceInfo := h.getDeviceInfo(device.DeviceID)

	logger.Debugf("📊 设备 %s 实时利用率: %.2f%%, 运行时间: %.2f小时, 实时工作时长: %.2f小时",
		device.DeviceID, utilizationRate, productiveTime, realtimeWorkingHours)

	return models.DeviceUtilization{
		DeviceID:          device.DeviceID,
		DeviceName:        deviceInfo.DeviceName,
		DeviceCode:        deviceInfo.DeviceCode,
		Location:          deviceInfo.Location,
		Brand:             deviceInfo.DataType, // 使用DataType作为Brand
		Model:             deviceInfo.Model,
		UtilizationRate:   utilizationRate,
		StatusComposition: statusComposition,
		TotalWorkingTime:  realtimeWorkingHours, // 显示实时工作时长
		ProductiveTime:    productiveTime,
		IdleTime:          statusDurations["idle"],
		FaultTime:         statusDurations["fault"],
	}
}

/**
 * 计算单个设备利用率
 */
func (h *UtilizationHandler) calculateDeviceUtilization(
	device models.DeviceStatus,
	statusHistory []models.DeviceStatusHistory,
	workingHours float64,
	workTimeSettings *models.WorkTimeSettings,
) models.DeviceUtilization {
	// 初始化状态时长统计
	statusDurations := map[string]float64{
		"production":   0,
		"idle":         0,
		"fault":        0,
		"adjusting":    0,
		"shutdown":     0,
		"disconnected": 0,
	}

	// 计算各状态持续时长
	for _, record := range statusHistory {
		duration := 0.0
		if record.Duration > 0 {
			duration = float64(record.Duration) / 3600.0 // 转换为小时
		} else {
			// 如果没有duration字段，通过时间差计算
			duration = record.EndTime.Sub(record.StartTime).Hours()
		}

		// 映射状态
		status := h.mapDeviceStatus(record.CurrentStatus)
		if _, exists := statusDurations[status]; exists {
			statusDurations[status] += duration
		}
	}

	// 计算利用率 - 修改为只基于运行状态计算
	productiveTime := statusDurations["production"]
	utilizationRate := 0.0
	if workingHours > 0 {
		// 利用率 = 运行时间 / 工作时间
		// 不再区分OP1和OP2模式，统一使用运行时间计算利用率
		utilizationRate = (productiveTime / workingHours) * 100
	}

	// 确保利用率不超过100%
	if utilizationRate > 100 {
		utilizationRate = 100
	}

	// 构建状态构成
	totalTime := 0.0
	for _, duration := range statusDurations {
		totalTime += duration
	}

	statusComposition := models.StatusComposition{
		Production:   h.createStatusDetail("production", statusDurations["production"], totalTime),
		Idle:         h.createStatusDetail("idle", statusDurations["idle"], totalTime),
		Fault:        h.createStatusDetail("fault", statusDurations["fault"], totalTime),
		Adjusting:    h.createStatusDetail("adjusting", statusDurations["adjusting"], totalTime),
		Shutdown:     h.createStatusDetail("shutdown", statusDurations["shutdown"], totalTime),
		Disconnected: h.createStatusDetail("disconnected", statusDurations["disconnected"], totalTime),
	}

	// 计算理论工作时间（用于显示）
	theoreticalWorkingHours, err := h.calculateWorkingHours(workTimeSettings)
	if err != nil {
		logger.Warnf("⚠️ Failed to calculate theoretical working hours: %v", err)
		theoreticalWorkingHours = 24.0 // 默认24小时
	}

	return models.DeviceUtilization{
		DeviceID:          device.DeviceID,
		DeviceName:        device.DeviceName,
		DeviceCode:        device.DeviceCode,
		Location:          device.Location,
		Brand:             device.DataType, // 使用DataType作为Brand的替代
		Model:             device.Model,
		UtilizationRate:   utilizationRate,
		StatusComposition: statusComposition,
		TotalWorkingTime:  theoreticalWorkingHours, // 显示理论工作时间（OP2模式下为22小时）
		ProductiveTime:    productiveTime,
		IdleTime:          statusDurations["idle"],
		FaultTime:         statusDurations["fault"],
	}
}

/**
 * 映射设备状态到标准状态
 */
func (h *UtilizationHandler) mapDeviceStatus(status string) string {
	statusMap := map[string]string{
		"production":   "production",
		"idle":         "idle",
		"fault":        "fault",
		"adjusting":    "adjusting",
		"shutdown":     "shutdown",
		"disconnected": "disconnected",
		"maintenance":  "adjusting", // 维护状态映射为调机
		"debugging":    "adjusting", // 调试状态映射为调机
	}

	if mappedStatus, exists := statusMap[status]; exists {
		return mappedStatus
	}
	return "idle" // 默认为空闲状态
}

/**
 * 获取单个设备详细信息
 *
 * 功能：根据设备ID获取设备的详细信息（名称、位置、品牌、型号等）
 *
 * @param {string} deviceID - 设备ID
 * @returns {models.Device} 设备详细信息，如果未找到则返回默认信息
 */
func (h *UtilizationHandler) getDeviceInfo(deviceID string) models.DeviceStatus {
	// 检查DataService是否可用
	if h.dataService == nil {
		logger.Warnf("⚠️ DataService不可用，返回默认设备信息")
		return models.DeviceStatus{
			DeviceID:   deviceID,
			DeviceName: "unknown",
			DeviceCode: deviceID,
			Location:   "unknown",
			DataType:   "unknown",
			Model:      "unknown",
		}
	}

	// 获取所有设备配置
	devices, err := h.dataService.GetAllDeviceConfigs()
	if err != nil {
		logger.Warnf("⚠️ 获取设备配置失败: %v，返回默认设备信息", err)
		return models.DeviceStatus{
			DeviceID:   deviceID,
			DeviceName: "unknown",
			DeviceCode: deviceID,
			Location:   "unknown",
			DataType:   "unknown",
			Model:      "unknown",
		}
	}

	// 查找指定设备
	for _, device := range devices {
		if device.DeviceID == deviceID {
			logger.Debugf("✅ 找到设备信息: %s - %s", deviceID, device.Name)
			// 转换models.Device到models.DeviceStatus
			return models.DeviceStatus{
				DeviceID:   device.DeviceID,
				DeviceName: device.Name,
				DeviceCode: device.DeviceID, // 使用DeviceID作为DeviceCode
				Location:   device.Location,
				DataType:   device.DataType,
				Model:      device.Model,
			}
		}
	}

	// 未找到设备，返回默认信息
	logger.Warnf("⚠️ 未找到设备 %s 的配置信息，返回默认信息", deviceID)
	return models.DeviceStatus{
		DeviceID:   deviceID,
		DeviceName: "unknown",
		DeviceCode: deviceID,
		Location:   "unknown",
		DataType:   "unknown",
		Model:      "unknown",
	}
}

/**
 * 并行计算设备利用率
 *
 * 功能：使用并行计算优化设备利用率计算性能
 * 将设备分批处理，每批并行计算，避免串行计算导致的性能瓶颈
 *
 * @param {[]models.DeviceStatus} devices - 设备列表
 * @param {string} date - 计算日期
 * @param {float64} workingHours - 工作时长
 * @param {*models.WorkTimeSettings} workTimeSettings - 工作时间设置
 * @param {bool} isRealtime - 是否为实时计算
 * @returns {[]models.DeviceUtilization, float64} 设备利用率列表和总利用率
 */
func (h *UtilizationHandler) calculateDeviceUtilizationsParallel(
	devices []models.DeviceStatus,
	date string,
	workingHours float64,
	workTimeSettings *models.WorkTimeSettings,
	isRealtime bool,
) ([]models.DeviceUtilization, float64) {
	const batchSize = 10 // 每批处理10台设备
	totalDevices := len(devices)

	logger.Debugf("🚀 开始并行计算设备利用率: 总设备数=%d, 批次大小=%d", totalDevices, batchSize)

	// 计算批次数量
	numBatches := (totalDevices + batchSize - 1) / batchSize

	// 创建结果通道
	type batchResult struct {
		utilizations []models.DeviceUtilization
		totalRate    float64
		batchIndex   int
	}

	resultChan := make(chan batchResult, numBatches)
	var wg sync.WaitGroup

	// 并行处理每个批次
	for batchIndex := 0; batchIndex < numBatches; batchIndex++ {
		wg.Add(1)

		go func(batchIdx int) {
			defer wg.Done()

			// 计算当前批次的设备范围
			start := batchIdx * batchSize
			end := start + batchSize
			if end > totalDevices {
				end = totalDevices
			}

			batchDevices := devices[start:end]
			logger.Debugf("📦 处理批次 %d: 设备范围 [%d:%d], 设备数量=%d",
				batchIdx+1, start, end, len(batchDevices))

			// 计算当前批次的设备利用率
			batchUtilizations := make([]models.DeviceUtilization, 0, len(batchDevices))
			batchTotalRate := 0.0

			for _, device := range batchDevices {
				// 获取设备状态历史（🔧 修复：添加工作时间设置参数）
				statusHistory, err := h.dataService.GetDeviceStatusHistory([]string{device.DeviceID}, date, workTimeSettings)
				if err != nil {
					logger.Warnf("⚠️ Failed to get status history for device %s: %v", device.DeviceID, err)
					continue
				}

				// 根据是否为实时计算选择不同的计算方法
				var utilization models.DeviceUtilization
				if isRealtime {
					utilization = h.calculateRealtimeDeviceUtilization(device, statusHistory, workingHours, workTimeSettings)
				} else {
					utilization = h.calculateDeviceUtilization(device, statusHistory, workingHours, workTimeSettings)
				}

				batchUtilizations = append(batchUtilizations, utilization)
				batchTotalRate += utilization.UtilizationRate
			}

			logger.Debugf("✅ 批次 %d 计算完成: 处理设备数=%d, 批次总利用率=%.2f",
				batchIdx+1, len(batchUtilizations), batchTotalRate)

			// 发送结果到通道
			resultChan <- batchResult{
				utilizations: batchUtilizations,
				totalRate:    batchTotalRate,
				batchIndex:   batchIdx,
			}
		}(batchIndex)
	}

	// 等待所有批次完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集所有批次的结果
	allUtilizations := make([]models.DeviceUtilization, 0, totalDevices)
	totalUtilization := 0.0
	processedBatches := 0

	for result := range resultChan {
		allUtilizations = append(allUtilizations, result.utilizations...)
		totalUtilization += result.totalRate
		processedBatches++

		logger.Debugf("📊 收集批次 %d 结果: 设备数=%d, 累计总利用率=%.2f",
			result.batchIndex+1, len(result.utilizations), totalUtilization)
	}

	logger.Debugf("🎯 并行计算完成: 处理批次数=%d/%d, 总设备数=%d, 总利用率=%.2f",
		processedBatches, numBatches, len(allUtilizations), totalUtilization)

	return allUtilizations, totalUtilization
}

/**
 * 并行计算每日利用率趋势
 *
 * 功能：使用并行计算优化每日利用率趋势计算性能
 * 将日期范围内的每一天并行计算，避免串行计算导致的性能瓶颈
 *
 * @param {time.Time} start - 开始日期
 * @param {time.Time} end - 结束日期
 * @returns {[]models.DailyUtilizationData, float64, float64, float64, int} 每日数据、总利用率、最大利用率、最小利用率、有效天数
 */
func (h *UtilizationHandler) calculateDailyTrendParallel(
	start, end time.Time, workTimeSettings *models.WorkTimeSettings,
) ([]models.DailyUtilizationData, float64, float64, float64, int) {
	// 使用传入的工作时间设置，确保与调用方一致
	if workTimeSettings == nil {
		logger.Warnf("⚠️ 工作时间设置为空，使用默认OP1模式")
		// 使用默认设置
		workTimeSettings = &models.WorkTimeSettings{
			UtilizationMode: "OP1",
			RestPeriods:     []models.RestPeriod{},
		}
	}
	// 生成日期列表
	var dates []time.Time
	for current := start; !current.After(end); current = current.AddDate(0, 0, 1) {
		dates = append(dates, current)
	}

	totalDays := len(dates)
	logger.Debugf("🚀 开始并行计算每日利用率趋势: 总天数=%d", totalDays)

	// 创建结果通道
	type dayResult struct {
		data  models.DailyUtilizationData
		index int
	}

	resultChan := make(chan dayResult, totalDays)
	var wg sync.WaitGroup

	// 并行处理每一天
	for i, date := range dates {
		wg.Add(1)

		go func(dayIndex int, dayDate time.Time, settings *models.WorkTimeSettings) {
			defer wg.Done()

			dateStr := dayDate.Format("2006-01-02")
			logger.Debugf("📅 并行处理日期: %s (第%d天)", dateStr, dayIndex+1)

			// 获取当日统计数据
			request := &models.StatisticsRequest{
				StartDate: dateStr,
				EndDate:   dateStr,
				DeviceIDs: []string{}, // 空数组表示获取所有设备
				GroupBy:   "device",
			}

			response, err := h.statisticsService.GetMultipleDeviceStatistics(request)
			if err != nil {
				logger.Warnf("⚠️ 获取日期 %s 的统计数据失败: %v", dateStr, err)
				// 添加空数据点
				resultChan <- dayResult{
					data: models.DailyUtilizationData{
						Date:            dateStr,
						UtilizationRate: 0.0,
						TotalDevices:    0,
						ActiveDevices:   0,
						WorkingHours:    0.0,
						ProductiveHours: 0.0,
					},
					index: dayIndex,
				}
				return
			}

			// 计算当日平均利用率
			var dayTotalRate float64
			var dayDeviceCount int
			var dayTotalWorkingTime float64
			var dayTotalProductiveTime float64

			for _, stat := range response.DeviceStats {
				dayTotalRate += stat.UtilizationRate
				dayDeviceCount++
				dayTotalWorkingTime += float64(stat.TotalTime) / 3600.0      // 转换为小时
				dayTotalProductiveTime += float64(stat.RunningTime) / 3600.0 // 转换为小时
			}

			var dayAverageRate float64
			if dayTotalWorkingTime > 0 {
				// 修复：使用总生产时间/总工作时间计算整体利用率，而不是各设备利用率的平均值
				// 这样计算更准确地反映了整体的时间利用情况

				// 实现TODO: 根据配置的利用率计算模式调整计算逻辑
				var effectiveWorkingTime float64

				switch settings.UtilizationMode {
				case "OP2":
					// OP2模式：需要从工作时间中扣除休息时间
					effectiveWorkingTime = dayTotalWorkingTime

					// 计算总休息时间（小时）
					var totalRestHours float64
					for _, rest := range settings.RestPeriods {
						if rest.Enabled {
							restDuration := h.calculateTimeDuration(rest.StartTime, rest.EndTime)
							totalRestHours += restDuration
						}
					}

					// 从总工作时间中扣除休息时间
					// 注意：这里需要乘以设备数量，因为每台设备都有相同的休息时间
					totalRestTime := totalRestHours * float64(dayDeviceCount)
					effectiveWorkingTime = dayTotalWorkingTime - totalRestTime

					logger.Debugf("📊 OP2模式计算: 原工作时间=%.2f小时, 休息时间=%.2f小时, 有效工作时间=%.2f小时",
						dayTotalWorkingTime, totalRestTime, effectiveWorkingTime)

				default: // OP1模式或其他
					// OP1模式：24小时连续生产，不扣除休息时间
					effectiveWorkingTime = dayTotalWorkingTime
					logger.Debugf("📊 OP1模式计算: 工作时间=%.2f小时", effectiveWorkingTime)
				}

				// 确保有效工作时间不为负数
				if effectiveWorkingTime <= 0 {
					logger.Warnf("⚠️ 有效工作时间为负数或零: %.2f，使用原工作时间", effectiveWorkingTime)
					effectiveWorkingTime = dayTotalWorkingTime
				}

				dayAverageRate = (dayTotalProductiveTime / effectiveWorkingTime) * 100
			}

			logger.Debugf("📊 日期 %s: 设备数=%d, 平均利用率=%.2f%%", dateStr, dayDeviceCount, dayAverageRate)

			// 发送结果到通道
			resultChan <- dayResult{
				data: models.DailyUtilizationData{
					Date:            dateStr,
					UtilizationRate: dayAverageRate,
					TotalDevices:    dayDeviceCount,
					ActiveDevices:   dayDeviceCount, // 假设所有有数据的设备都是活跃的
					WorkingHours:    dayTotalWorkingTime,
					ProductiveHours: dayTotalProductiveTime,
				},
				index: dayIndex,
			}
		}(i, date, workTimeSettings)
	}

	// 等待所有天数完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集所有天数的结果
	dailyData := make([]models.DailyUtilizationData, totalDays)
	var totalRate float64
	var maxRate float64
	var minRate float64 = 100.0 // 初始化为最大值
	var validDays int
	processedDays := 0

	for result := range resultChan {
		dailyData[result.index] = result.data
		processedDays++

		// 更新统计信息（只计算有数据的天数）
		if result.data.TotalDevices > 0 {
			totalRate += result.data.UtilizationRate
			validDays++
			if result.data.UtilizationRate > maxRate {
				maxRate = result.data.UtilizationRate
			}
			if result.data.UtilizationRate < minRate {
				minRate = result.data.UtilizationRate
			}
		}

		logger.Debugf("📊 收集第%d天结果: 日期=%s, 利用率=%.2f%%, 累计有效天数=%d",
			result.index+1, result.data.Date, result.data.UtilizationRate, validDays)
	}

	// 如果没有有效数据，重置最小值
	if validDays == 0 {
		minRate = 0.0
	}

	logger.Debugf("🎯 并行计算完成: 处理天数=%d/%d, 有效天数=%d, 总利用率=%.2f, 最大=%.2f, 最小=%.2f",
		processedDays, totalDays, validDays, totalRate, maxRate, minRate)

	return dailyData, totalRate, maxRate, minRate, validDays
}

/**
 * 获取机器页面V2完整数据
 *
 * 功能：为机器页面V2提供统一的数据接口，后端完成所有数据处理
 * 前端只需要一次API调用即可获取页面所需的全部数据
 *
 * HTTP方法：GET
 * 路径：/api/utilization/machine-v2
 *
 * 查询参数：
 * - date：日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "overview": {
 *     "date": "2025-06-19",
 *     "overall_utilization": 75.5,
 *     "total_devices": 10,
 *     "utilization_zones": [...],
 *     "calculation_mode": "OP2",
 *     "working_hours": 22.0
 *   },
 *   "device_ranking": {
 *     "date": "2025-06-19",
 *     "devices": [...],
 *     "status_legend": [...],
 *     "work_time_settings": {...}
 *   },
 *   "work_time_settings": {...},
 *   "last_updated": "2025-06-19T10:30:00Z"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/utilization/machine-v2?date=2025-06-19
 */
func (h *UtilizationHandler) GetMachinePageV2Data(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/utilization/machine-v2", "GET", c.Writer.Status(), duration)
		}
	}()

	// 获取查询参数
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))

	logger.Debugf("📊 获取机器页面V2数据，日期: %s", date)

	// 获取工作时间设置（使用默认值处理错误）
	workTimeSettings, err := h.workTimeService.GetWorkTimeSettings()
	if err != nil {
		logger.Warnf("⚠️ Failed to get work time settings, using defaults: %v", err)
		// 使用默认工作时间设置
		workTimeSettings = &models.WorkTimeSettings{
			DayStartTime:    "08:00",
			UtilizationMode: "OP1",
			Shifts:          []models.ShiftSetting{},
			RestPeriods:     []models.RestPeriod{},
		}
	}

	// 并行获取概览数据和设备排名数据
	type dataResult struct {
		overview *models.UtilizationOverview
		ranking  *models.DeviceUtilizationRanking
		err      error
	}

	resultChan := make(chan dataResult, 2)
	var wg sync.WaitGroup

	// 判断是否为今日数据
	today := time.Now().Format("2006-01-02")
	isToday := date == today

	// 并行获取概览数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		var overview *models.UtilizationOverview
		var err error

		if isToday {
			logger.Debugf("✅ 使用实时计算方法获取概览数据")
			overview, err = h.calculateRealtimeUtilizationOverview(date, workTimeSettings)
		} else {
			logger.Debugf("✅ 使用历史数据计算方法获取概览数据")
			overview, err = h.calculateUtilizationOverview(date, workTimeSettings)
		}

		resultChan <- dataResult{overview: overview, err: err}
	}()

	// 并行获取设备排名数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		var ranking *models.DeviceUtilizationRanking
		var err error

		if isToday {
			logger.Debugf("✅ 使用实时计算方法获取设备排名数据")
			ranking, err = h.calculateRealtimeDeviceUtilizationRanking(date, workTimeSettings)
		} else {
			logger.Debugf("✅ 使用历史数据计算方法获取设备排名数据")
			ranking, err = h.calculateDeviceUtilizationRanking(date, workTimeSettings)
		}

		resultChan <- dataResult{ranking: ranking, err: err}
	}()

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var overview *models.UtilizationOverview
	var ranking *models.DeviceUtilizationRanking
	var hasError bool

	for result := range resultChan {
		if result.err != nil {
			logger.Errorf("❌ 数据获取失败: %v", result.err)
			hasError = true
			continue
		}

		if result.overview != nil {
			overview = result.overview
			logger.Debugf("✅ 概览数据获取成功")
		}

		if result.ranking != nil {
			ranking = result.ranking
			logger.Debugf("✅ 设备排名数据获取成功")
		}
	}

	// 检查是否有错误
	if hasError || overview == nil || ranking == nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取机器页面数据失败",
			Error:   "部分数据获取失败",
		})
		return
	}

	// 构建响应数据
	response := map[string]interface{}{
		"overview":           overview,
		"device_ranking":     ranking,
		"work_time_settings": workTimeSettings,
		"last_updated":       time.Now(),
	}

	logger.Debugf("✅ 机器页面V2数据获取成功: 总体利用率=%.2f%%, 设备数量=%d",
		overview.OverallUtilization, len(ranking.Devices))

	c.JSON(http.StatusOK, response)
}

/**
 * 获取仪表板V2完整数据
 *
 * 功能：为仪表板V2提供统一的数据接口，后端完成所有数据处理
 * 前端只需要一次API调用即可获取页面所需的全部数据
 *
 * HTTP方法：GET
 * 路径：/api/utilization/dashboard-v2
 *
 * 查询参数：
 * - start_date：开始日期，格式YYYY-MM-DD，默认为7天前
 * - end_date：结束日期，格式YYYY-MM-DD，默认为当前日期
 *
 * 响应格式：
 * {
 *   "overview": {
 *     "date": "2025-06-19",
 *     "overall_utilization": 75.5,
 *     "total_devices": 10,
 *     "utilization_zones": [...],
 *     "calculation_mode": "OP2",
 *     "working_hours": 22.0
 *   },
 *   "device_ranking": {
 *     "date": "2025-06-19",
 *     "devices": [...],
 *     "status_legend": [...],
 *     "work_time_settings": {...}
 *   },
 *   "daily_trend": {
 *     "start_date": "2025-06-12",
 *     "end_date": "2025-06-19",
 *     "daily_data": [...],
 *     "average_rate": 75.2,
 *     "max_rate": 85.5,
 *     "min_rate": 65.0,
 *     "trend_direction": "up"
 *   },
 *   "last_updated": "2025-06-19T10:30:00Z"
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 计算失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/utilization/dashboard-v2?start_date=2025-06-12&end_date=2025-06-19
 */
func (h *UtilizationHandler) GetDashboardV2Data(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/utilization/dashboard-v2", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析日期范围参数
	startDate, endDate := h.parseDateRange(c)

	logger.Debugf("📊 获取仪表板V2数据，日期范围: %s ~ %s", startDate, endDate)

	// 获取工作时间设置（使用默认值处理错误）
	workTimeSettings, err := h.workTimeService.GetWorkTimeSettings()
	if err != nil {
		logger.Warnf("⚠️ Failed to get work time settings, using defaults: %v", err)
		// 使用默认工作时间设置
		workTimeSettings = &models.WorkTimeSettings{
			DayStartTime:    "08:00",
			UtilizationMode: "OP1",
			Shifts:          []models.ShiftSetting{},
			RestPeriods:     []models.RestPeriod{},
		}
	}

	// 并行获取三个数据源：概览、设备排名、每日趋势
	type dataResult struct {
		overview   *models.UtilizationOverview
		ranking    *models.DeviceUtilizationRanking
		dailyTrend *models.DailyUtilizationTrend
		err        error
		dataType   string
	}

	resultChan := make(chan dataResult, 3)
	var wg sync.WaitGroup

	// 判断结束日期是否为今日
	today := time.Now().Format("2006-01-02")
	isEndDateToday := endDate == today

	// 并行获取概览数据（基于结束日期）
	wg.Add(1)
	go func() {
		defer wg.Done()
		var overview *models.UtilizationOverview
		var err error

		if isEndDateToday {
			logger.Debugf("✅ 使用实时计算方法获取概览数据")
			overview, err = h.calculateRealtimeUtilizationOverview(endDate, workTimeSettings)
		} else {
			logger.Debugf("✅ 使用历史数据计算方法获取概览数据")
			overview, err = h.calculateUtilizationOverview(endDate, workTimeSettings)
		}

		resultChan <- dataResult{overview: overview, err: err, dataType: "overview"}
	}()

	// 并行获取设备排名数据（根据日期选择计算方法）
	wg.Add(1)
	go func() {
		defer wg.Done()
		var ranking *models.DeviceUtilizationRanking
		var err error

		if isEndDateToday {
			// 今日数据使用实时计算方法，确保工作时长为实时
			logger.Debugf("✅ 使用实时计算方法获取设备排名数据")
			ranking, err = h.calculateRealtimeDeviceUtilizationRanking(endDate, workTimeSettings)
		} else {
			// 历史数据使用统计服务数据，确保与详细页面数据一致
			logger.Debugf("✅ 使用历史数据计算方法获取设备排名数据")
			ranking, err = h.calculateDeviceUtilizationRanking(endDate, workTimeSettings)
		}

		resultChan <- dataResult{ranking: ranking, err: err, dataType: "ranking"}
	}()

	// 并行获取每日趋势数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		logger.Debugf("✅ 获取每日趋势数据")
		dailyTrend, err := h.calculateDailyUtilizationTrend(startDate, endDate, workTimeSettings)
		resultChan <- dataResult{dailyTrend: dailyTrend, err: err, dataType: "daily_trend"}
	}()

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var overview *models.UtilizationOverview
	var ranking *models.DeviceUtilizationRanking
	var dailyTrend *models.DailyUtilizationTrend
	var hasError bool
	var errorMessages []string

	for result := range resultChan {
		if result.err != nil {
			logger.Errorf("❌ %s数据获取失败: %v", result.dataType, result.err)
			hasError = true
			errorMessages = append(errorMessages, fmt.Sprintf("%s: %v", result.dataType, result.err))
			continue
		}

		switch result.dataType {
		case "overview":
			overview = result.overview
			logger.Debugf("✅ 概览数据获取成功")
		case "ranking":
			ranking = result.ranking
			logger.Debugf("✅ 设备排名数据获取成功")
		case "daily_trend":
			dailyTrend = result.dailyTrend
			logger.Debugf("✅ 每日趋势数据获取成功")
		}
	}

	// 检查是否有错误，如果有错误则使用模拟数据
	if hasError || overview == nil || ranking == nil || dailyTrend == nil {
		logger.Warnf("⚠️ 部分数据获取失败，使用模拟数据: %v", errorMessages)

		// 使用模拟数据（暂时注释掉，避免编译错误）
		// if overview == nil {
		// 	overview = h.createMockOverview(endDate, workTimeSettings)
		// }
		// if ranking == nil {
		// 	ranking = h.createMockDeviceRanking(endDate, workTimeSettings)
		// }
		// if dailyTrend == nil {
		// 	dailyTrend = h.createMockDailyTrend(startDate, endDate, workTimeSettings)
		// }
	}

	// 构建响应数据
	response := map[string]interface{}{
		"overview":       overview,
		"device_ranking": ranking,
		"daily_trend":    dailyTrend,
		"last_updated":   time.Now(),
	}

	logger.Debugf("✅ 仪表板V2数据获取成功: 总体利用率=%.2f%%, 设备数量=%d, 趋势天数=%d",
		overview.OverallUtilization, len(ranking.Devices), len(dailyTrend.DailyData))

	c.JSON(http.StatusOK, response)
}
