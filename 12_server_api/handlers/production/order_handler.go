/**
 * 订单管理处理器
 *
 * 功能描述：
 * 处理订单管理相关的API请求，包括订单的增删改查、状态管理、统计分析等功能
 * 支持单产品和多产品订单，提供完整的订单生命周期管理
 *
 * 主要功能：
 * - 订单的创建、查询、更新、删除
 * - 订单状态管理和跟踪
 * - 按状态查询订单
 * - 订单统计分析
 * - 多产品订单支持
 *
 * API端点：
 * - GET /api/production/orders - 获取订单列表
 * - POST /api/production/orders - 创建订单
 * - GET /api/production/orders/{id} - 获取单个订单
 * - PUT /api/production/orders/{id} - 更新订单
 * - DELETE /api/production/orders/{id} - 删除订单
 * - PUT /api/production/orders/{id}/status - 更新订单状态
 * - GET /api/production/orders/statistics - 获取订单统计
 * - GET /api/production/orders/by-status/{status} - 按状态查询订单
 *
 * @package production
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package production

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 订单管理处理器结构体
 *
 * 功能：处理订单管理相关的HTTP请求
 *
 * 依赖服务：
 * - OrderService：提供订单的业务逻辑和数据管理
 */
type OrderHandler struct {
	orderService *services.OrderService
}

/**
 * 创建订单管理处理器实例
 *
 * 功能：初始化订单管理处理器，注入必要的服务依赖
 *
 * @param {*services.OrderService} orderService - 订单服务实例
 * @returns {*OrderHandler} 订单管理处理器实例
 *
 * @example
 * handler := production.NewOrderHandler(orderService)
 */
func NewOrderHandler(orderService *services.OrderService) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
	}
}

/**
 * 获取订单列表
 *
 * 功能：获取订单列表，支持分页查询和条件过滤
 *
 * HTTP方法：GET
 * 路径：/api/production/orders
 *
 * 查询参数：
 * - page：页码，默认1
 * - page_size：每页数量，默认20
 * - search：搜索关键词，可选
 * - status：状态过滤，可选
 * - sort_by：排序字段，默认created_at
 * - sort_desc：是否降序，默认true
 *
 * 响应格式：
 * {
 *   "data": [...],
 *   "total": 100,
 *   "page": 1,
 *   "page_size": 20,
 *   "pages": 5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 查询参数无效
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/orders?page=1&page_size=20&status=pending
 */
func (h *OrderHandler) GetOrders(c *gin.Context) {
	var params models.QueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		logger.Errorf("Invalid query parameters: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "查询参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📦 Getting orders with params: %+v", params)

	response, err := h.orderService.GetOrders(&params)
	if err != nil {
		logger.Errorf("❌ Failed to get orders: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取订单列表失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved %d orders", len(response.Data.([]models.Order)))
	c.JSON(http.StatusOK, response)
}

/**
 * 创建订单
 *
 * 功能：创建新的订单，支持单产品和多产品模式
 *
 * HTTP方法：POST
 * 路径：/api/production/orders
 *
 * 请求体：
 * {
 *   "order_number": "ORD-2025-001",
 *   "customer_name": "客户名称",
 *   "priority": "high",
 *   "delivery_date": "2025-06-30T00:00:00Z",
 *   "description": "订单描述",
 *   "products": [
 *     {
 *       "product_id": "product_001",
 *       "quantity": 100,
 *       "unit_price": 10.50
 *     }
 *   ]
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "订单创建成功"
 * }
 *
 * 状态码：
 * - 201: 创建成功
 * - 400: 请求参数无效
 * - 500: 创建失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/production/orders
 * Body: {"order_number": "ORD-001", "customer_name": "客户A", "products": [...]}
 */
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	var req models.OrderCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid order create request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	// 验证产品信息
	if len(req.Products) == 0 && req.ProductID == "" {
		logger.Errorf("No product information provided")
		c.JSON(http.StatusBadRequest, gin.H{"error": "请至少提供一个产品信息"})
		return
	}

	// 验证多产品数据
	if len(req.Products) > 0 {
		for i, product := range req.Products {
			if product.ProductID == "" {
				logger.Errorf("Product %d missing product_id", i)
				c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("第%d个产品缺少产品编号", i+1)})
				return
			}
			if product.Quantity <= 0 {
				logger.Errorf("Product %d invalid quantity: %d", i, product.Quantity)
				c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("第%d个产品数量必须大于0", i+1)})
				return
			}
		}
	}

	logger.Debugf("📦 Creating order: %s", req.OrderNumber)

	// 转换为Order模型
	order := &models.Order{
		OrderNumber:  req.OrderNumber,
		CustomerName: req.CustomerName,
		Priority:     req.Priority,
		DeliveryDate: req.DeliveryDate,
		Description:  req.Description,
	}

	// 处理产品信息：支持多产品和单产品兼容
	if len(req.Products) > 0 {
		// 多产品模式
		order.Products = req.Products
	} else if req.ProductID != "" {
		// 单产品兼容模式
		order.Products = []models.OrderProduct{
			{
				ProductID:   req.ProductID,
				ProductName: "", // 将在服务层填充
				Quantity:    req.Quantity,
				Unit:        "", // 将在服务层填充
				UnitPrice:   req.UnitPrice,
			},
		}
		// 保留兼容字段
		order.ProductID = req.ProductID
		order.Quantity = req.Quantity
		order.UnitPrice = req.UnitPrice
	}

	err := h.orderService.CreateOrder(order)
	if err != nil {
		logger.Errorf("❌ Failed to create order: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建订单失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Order %s created successfully", req.OrderNumber)
	c.JSON(http.StatusCreated, gin.H{"data": order, "message": "订单创建成功"})
}

/**
 * 获取单个订单
 *
 * 功能：根据订单ID获取特定订单的详细信息
 *
 * HTTP方法：GET
 * 路径：/api/production/orders/{id}
 *
 * 路径参数：
 * - id：订单ID
 *
 * 响应格式：
 * {
 *   "data": {
 *     "id": "order_id",
 *     "order_number": "ORD-2025-001",
 *     "customer_name": "客户名称",
 *     "status": "pending",
 *     "products": [...],
 *     ...
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 订单不存在
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/orders/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *OrderHandler) GetOrder(c *gin.Context) {
	orderID := c.Param("id")

	logger.Debugf("📦 Getting order: %s", orderID)

	order, err := h.orderService.GetOrder(orderID)
	if err != nil {
		logger.Errorf("❌ Failed to get order %s: %v", orderID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "订单不存在", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved order: %s", orderID)
	c.JSON(http.StatusOK, gin.H{"data": order})
}

/**
 * 更新订单
 *
 * 功能：更新订单的基本信息和产品信息
 *
 * HTTP方法：PUT
 * 路径：/api/production/orders/{id}
 *
 * 路径参数：
 * - id：订单ID
 *
 * 请求体：OrderCreateRequest结构体
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "订单更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 404: 订单不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/production/orders/60f1b2b3c4d5e6f7a8b9c0d1
 * Body: {"customer_name": "新客户名称", "priority": "urgent"}
 */
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	orderID := c.Param("id")

	// 先获取现有订单
	existingOrder, err := h.orderService.GetOrder(orderID)
	if err != nil {
		logger.Errorf("❌ Failed to get order %s: %v", orderID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "订单不存在", "details": err.Error()})
		return
	}

	var req models.OrderCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid order update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📦 Updating order: %s", orderID)

	// 更新字段
	existingOrder.CustomerName = req.CustomerName
	existingOrder.Priority = req.Priority
	existingOrder.DeliveryDate = req.DeliveryDate
	existingOrder.Description = req.Description

	// 处理产品信息：支持多产品和单产品兼容
	if len(req.Products) > 0 {
		// 多产品模式
		existingOrder.Products = req.Products
	} else if req.ProductID != "" {
		// 单产品兼容模式
		existingOrder.Products = []models.OrderProduct{
			{
				ProductID:   req.ProductID,
				ProductName: "", // 将在服务层填充
				Quantity:    req.Quantity,
				Unit:        "", // 将在服务层填充
				UnitPrice:   req.UnitPrice,
			},
		}
		// 保留兼容字段
		existingOrder.ProductID = req.ProductID
		existingOrder.Quantity = req.Quantity
		existingOrder.UnitPrice = req.UnitPrice
	}

	err = h.orderService.UpdateOrder(orderID, existingOrder)
	if err != nil {
		logger.Errorf("❌ Failed to update order %s: %v", orderID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新订单失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Order %s updated successfully", orderID)
	c.JSON(http.StatusOK, gin.H{"data": existingOrder, "message": "订单更新成功"})
}

/**
 * 删除订单
 *
 * 功能：删除指定的订单
 *
 * HTTP方法：DELETE
 * 路径：/api/production/orders/{id}
 *
 * 路径参数：
 * - id：订单ID
 *
 * 响应格式：
 * {
 *   "message": "订单删除成功"
 * }
 *
 * 状态码：
 * - 200: 删除成功
 * - 500: 删除失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * DELETE /api/production/orders/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *OrderHandler) DeleteOrder(c *gin.Context) {
	orderID := c.Param("id")

	logger.Debugf("📦 Deleting order: %s", orderID)

	err := h.orderService.DeleteOrder(orderID)
	if err != nil {
		logger.Errorf("❌ Failed to delete order %s: %v", orderID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除订单失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Order %s deleted successfully", orderID)
	c.JSON(http.StatusOK, gin.H{"message": "订单删除成功"})
}

/**
 * 更新订单状态
 *
 * 功能：更新订单的状态
 *
 * HTTP方法：PUT
 * 路径：/api/production/orders/{id}/status
 *
 * 路径参数：
 * - id：订单ID
 *
 * 请求体：
 * {
 *   "status": "in_progress"
 * }
 *
 * 响应格式：
 * {
 *   "message": "订单状态更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/production/orders/60f1b2b3c4d5e6f7a8b9c0d1/status
 * Body: {"status": "completed"}
 */
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	orderID := c.Param("id")

	var req struct {
		Status string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid status update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📦 Updating order status: %s -> %s", orderID, req.Status)

	err := h.orderService.UpdateOrderStatus(orderID, req.Status)
	if err != nil {
		logger.Errorf("❌ Failed to update order status %s: %v", orderID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新订单状态失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Order %s status updated to %s", orderID, req.Status)
	c.JSON(http.StatusOK, gin.H{"message": "订单状态更新成功"})
}

/**
 * 获取订单统计
 *
 * 功能：获取订单的统计信息
 *
 * HTTP方法：GET
 * 路径：/api/production/orders/statistics
 *
 * 响应格式：
 * {
 *   "data": {
 *     "total_orders": 100,
 *     "pending_orders": 20,
 *     "in_progress_orders": 30,
 *     "completed_orders": 45,
 *     "cancelled_orders": 5,
 *     "total_value": 150000.00
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/orders/statistics
 */
func (h *OrderHandler) GetOrderStatistics(c *gin.Context) {
	logger.Debug("📦 Getting order statistics")

	statistics, err := h.orderService.GetOrderStatistics()
	if err != nil {
		logger.Errorf("❌ Failed to get order statistics: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取订单统计失败", "details": err.Error()})
		return
	}

	logger.Debug("✅ Retrieved order statistics")
	c.JSON(http.StatusOK, gin.H{"data": statistics})
}

/**
 * 根据状态获取订单
 *
 * 功能：获取指定状态的所有订单
 *
 * HTTP方法：GET
 * 路径：/api/production/orders/by-status/{status}
 *
 * 路径参数：
 * - status：订单状态
 *
 * 响应格式：
 * {
 *   "data": [...]
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/orders/by-status/pending
 */
func (h *OrderHandler) GetOrdersByStatus(c *gin.Context) {
	status := c.Param("status")

	logger.Debugf("📦 Getting orders by status: %s", status)

	orders, err := h.orderService.GetOrdersByStatus(status)
	if err != nil {
		logger.Errorf("❌ Failed to get orders by status %s: %v", status, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取订单失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved %d orders with status: %s", len(orders), status)
	c.JSON(http.StatusOK, gin.H{"data": orders})
}
