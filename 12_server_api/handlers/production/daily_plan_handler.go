/**
 * 每日计划管理处理器
 *
 * 功能描述：
 * 处理每日生产计划相关的API请求，包括计划的增删改查、进度跟踪、统计分析等功能
 * 提供完整的生产计划管理和执行监控支持
 *
 * 主要功能：
 * - 每日计划的创建、查询、更新、删除
 * - 按日期、设备、产品等维度查询计划
 * - 计划进度跟踪和状态更新
 * - 周计划和计划统计分析
 * - 计划执行情况监控
 *
 * API端点：
 * - GET /api/production/daily-plans - 获取每日计划列表
 * - POST /api/production/daily-plans - 创建每日计划
 * - GET /api/production/daily-plans/{id} - 获取单个计划
 * - PUT /api/production/daily-plans/{id} - 更新计划
 * - DELETE /api/production/daily-plans/{id} - 删除计划
 * - GET /api/production/daily-plans/by-date - 按日期查询计划
 * - GET /api/production/daily-plans/by-device/{device_id} - 按设备查询计划
 * - GET /api/production/daily-plans/weekly - 获取周计划
 * - GET /api/production/daily-plans/statistics - 获取计划统计
 * - PUT /api/production/daily-plans/{id}/progress - 更新计划进度
 *
 * @package production
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package production

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 每日计划管理处理器结构体
 *
 * 功能：处理每日计划管理相关的HTTP请求
 *
 * 依赖服务：
 * - DailyPlanService：提供每日计划的业务逻辑和数据管理
 */
type DailyPlanHandler struct {
	planService *services.DailyPlanService
}

/**
 * 创建每日计划管理处理器实例
 *
 * 功能：初始化每日计划管理处理器，注入必要的服务依赖
 *
 * @param {*services.DailyPlanService} planService - 每日计划服务实例
 * @returns {*DailyPlanHandler} 每日计划管理处理器实例
 *
 * @example
 * handler := production.NewDailyPlanHandler(planService)
 */
func NewDailyPlanHandler(planService *services.DailyPlanService) *DailyPlanHandler {
	return &DailyPlanHandler{
		planService: planService,
	}
}

/**
 * 获取每日计划列表
 *
 * 功能：获取每日计划列表，支持分页查询和条件过滤
 *
 * HTTP方法：GET
 * 路径：/api/production/daily-plans
 *
 * 查询参数：
 * - page：页码，默认1
 * - page_size：每页数量，默认20
 * - search：搜索关键词，可选
 * - status：状态过滤，可选
 * - sort_by：排序字段，默认created_at
 * - sort_desc：是否降序，默认true
 *
 * 响应格式：
 * {
 *   "data": [...],
 *   "total": 100,
 *   "page": 1,
 *   "page_size": 20,
 *   "pages": 5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 查询参数无效
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/daily-plans?page=1&page_size=20&status=pending
 */
func (h *DailyPlanHandler) GetDailyPlans(c *gin.Context) {
	var params models.QueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		logger.Errorf("Invalid query parameters: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "查询参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📋 Getting daily plans with params: %+v", params)

	response, err := h.planService.GetDailyPlans(&params)
	if err != nil {
		logger.Errorf("❌ Failed to get daily plans: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取每日计划列表失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved %d daily plans", len(response.Data.([]models.DailyPlan)))
	c.JSON(http.StatusOK, response)
}

/**
 * 创建每日计划
 *
 * 功能：创建新的每日生产计划
 *
 * HTTP方法：POST
 * 路径：/api/production/daily-plans
 *
 * 请求体：
 * {
 *   "plan_date": "2025-06-15",
 *   "device_id": "device_001",
 *   "product_id": "product_001",
 *   "planned_quantity": 100,
 *   "shift_type": "day",
 *   "operator_name": "张三",
 *   "notes": "备注信息"
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "每日计划创建成功"
 * }
 *
 * 状态码：
 * - 201: 创建成功
 * - 400: 请求参数无效
 * - 500: 创建失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/production/daily-plans
 * Body: {"plan_date": "2025-06-15", "device_id": "device_001", "product_id": "product_001", "planned_quantity": 100}
 */
func (h *DailyPlanHandler) CreateDailyPlan(c *gin.Context) {
	var req models.DailyPlanCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid daily plan create request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📋 Creating daily plan for date: %s", req.PlanDate.Format("2006-01-02"))

	// 转换为DailyPlan模型
	plan := &models.DailyPlan{
		PlanDate:        req.PlanDate,
		DeviceID:        req.DeviceID,
		ProductID:       req.ProductID,
		PlannedQuantity: req.PlannedQuantity,
		ShiftType:       req.ShiftType,
		OperatorName:    req.OperatorName,
		Notes:           req.Notes,
	}

	err := h.planService.CreateDailyPlan(plan)
	if err != nil {
		logger.Errorf("❌ Failed to create daily plan: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建每日计划失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Daily plan created successfully for date %s", req.PlanDate.Format("2006-01-02"))
	c.JSON(http.StatusCreated, gin.H{"data": plan, "message": "每日计划创建成功"})
}

/**
 * 获取单个每日计划
 *
 * 功能：根据计划ID获取特定每日计划的详细信息
 *
 * HTTP方法：GET
 * 路径：/api/production/daily-plans/{id}
 *
 * 路径参数：
 * - id：计划ID
 *
 * 响应格式：
 * {
 *   "data": {
 *     "id": "plan_id",
 *     "plan_date": "2025-06-15",
 *     "device_id": "device_001",
 *     "product_id": "product_001",
 *     "planned_quantity": 100,
 *     "actual_quantity": 80,
 *     "status": "in_progress",
 *     ...
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 计划不存在
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/daily-plans/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *DailyPlanHandler) GetDailyPlan(c *gin.Context) {
	planID := c.Param("id")

	logger.Debugf("📋 Getting daily plan: %s", planID)

	plan, err := h.planService.GetDailyPlan(planID)
	if err != nil {
		logger.Errorf("❌ Failed to get daily plan %s: %v", planID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "每日计划不存在", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved daily plan: %s", planID)
	c.JSON(http.StatusOK, gin.H{"data": plan})
}

/**
 * 更新每日计划
 *
 * 功能：更新每日计划的基本信息
 *
 * HTTP方法：PUT
 * 路径：/api/production/daily-plans/{id}
 *
 * 路径参数：
 * - id：计划ID
 *
 * 请求体：DailyPlanCreateRequest结构体
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "每日计划更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 404: 计划不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/production/daily-plans/60f1b2b3c4d5e6f7a8b9c0d1
 * Body: {"planned_quantity": 120, "operator_name": "李四"}
 */
func (h *DailyPlanHandler) UpdateDailyPlan(c *gin.Context) {
	planID := c.Param("id")

	// 先获取现有计划
	existingPlan, err := h.planService.GetDailyPlan(planID)
	if err != nil {
		logger.Errorf("❌ Failed to get daily plan %s: %v", planID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "每日计划不存在", "details": err.Error()})
		return
	}

	var req models.DailyPlanCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid daily plan update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📋 Updating daily plan: %s", planID)

	// 更新字段
	existingPlan.PlanDate = req.PlanDate
	existingPlan.DeviceID = req.DeviceID
	existingPlan.ProductID = req.ProductID
	existingPlan.PlannedQuantity = req.PlannedQuantity
	existingPlan.ShiftType = req.ShiftType
	existingPlan.OperatorName = req.OperatorName
	existingPlan.Notes = req.Notes

	err = h.planService.UpdateDailyPlan(planID, existingPlan)
	if err != nil {
		logger.Errorf("❌ Failed to update daily plan %s: %v", planID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新每日计划失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Daily plan %s updated successfully", planID)
	c.JSON(http.StatusOK, gin.H{"data": existingPlan, "message": "每日计划更新成功"})
}

/**
 * 删除每日计划
 *
 * 功能：删除指定的每日计划
 *
 * HTTP方法：DELETE
 * 路径：/api/production/daily-plans/{id}
 *
 * 路径参数：
 * - id：计划ID
 *
 * 响应格式：
 * {
 *   "message": "每日计划删除成功"
 * }
 *
 * 状态码：
 * - 200: 删除成功
 * - 500: 删除失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * DELETE /api/production/daily-plans/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *DailyPlanHandler) DeleteDailyPlan(c *gin.Context) {
	planID := c.Param("id")

	logger.Debugf("📋 Deleting daily plan: %s", planID)

	err := h.planService.DeleteDailyPlan(planID)
	if err != nil {
		logger.Errorf("❌ Failed to delete daily plan %s: %v", planID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除每日计划失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Daily plan %s deleted successfully", planID)
	c.JSON(http.StatusOK, gin.H{"message": "每日计划删除成功"})
}

/**
 * 根据日期获取计划
 *
 * 功能：获取指定日期的所有生产计划
 *
 * HTTP方法：GET
 * 路径：/api/production/daily-plans/by-date
 *
 * 查询参数：
 * - date：日期，格式YYYY-MM-DD，必填
 *
 * 响应格式：
 * {
 *   "data": [...]
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 日期参数无效
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/daily-plans/by-date?date=2025-06-15
 */
func (h *DailyPlanHandler) GetPlansByDate(c *gin.Context) {
	dateStr := c.Query("date")
	if dateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "日期参数不能为空"})
		return
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "日期格式无效", "details": err.Error()})
		return
	}

	logger.Debugf("📋 Getting plans by date: %s", dateStr)

	plans, err := h.planService.GetPlansByDate(date)
	if err != nil {
		logger.Errorf("❌ Failed to get plans by date %s: %v", dateStr, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取计划失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved %d plans for date: %s", len(plans), dateStr)
	c.JSON(http.StatusOK, gin.H{"data": plans})
}

/**
 * 根据设备获取计划
 *
 * 功能：获取指定设备的所有生产计划
 *
 * HTTP方法：GET
 * 路径：/api/production/daily-plans/by-device/{device_id}
 *
 * 路径参数：
 * - device_id：设备ID
 *
 * 响应格式：
 * {
 *   "data": [...]
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/daily-plans/by-device/device_001
 */
func (h *DailyPlanHandler) GetPlansByDevice(c *gin.Context) {
	deviceID := c.Param("device_id")

	logger.Debugf("📋 Getting plans by device: %s", deviceID)

	plans, err := h.planService.GetPlansByDevice(deviceID)
	if err != nil {
		logger.Errorf("❌ Failed to get plans by device %s: %v", deviceID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取设备计划失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved %d plans for device: %s", len(plans), deviceID)
	c.JSON(http.StatusOK, gin.H{"data": plans})
}

/**
 * 获取周计划
 *
 * 功能：获取指定周的生产计划
 *
 * HTTP方法：GET
 * 路径：/api/production/daily-plans/weekly
 *
 * 查询参数：
 * - start_date：开始日期，格式YYYY-MM-DD，必填
 *
 * 响应格式：
 * {
 *   "data": [...]
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 日期参数无效
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/daily-plans/weekly?start_date=2025-06-15
 */
func (h *DailyPlanHandler) GetWeeklyPlans(c *gin.Context) {
	startDateStr := c.Query("start_date")
	if startDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "开始日期参数不能为空"})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "日期格式无效", "details": err.Error()})
		return
	}

	logger.Debugf("📋 Getting weekly plans from: %s", startDateStr)

	plans, err := h.planService.GetWeeklyPlans(startDate)
	if err != nil {
		logger.Errorf("❌ Failed to get weekly plans from %s: %v", startDateStr, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取周计划失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved weekly plans from: %s", startDateStr)
	c.JSON(http.StatusOK, gin.H{"data": plans})
}

/**
 * 获取计划统计
 *
 * 功能：获取指定时间范围内的计划统计信息
 *
 * HTTP方法：GET
 * 路径：/api/production/daily-plans/statistics
 *
 * 查询参数：
 * - start_date：开始日期，格式YYYY-MM-DD，必填
 * - end_date：结束日期，格式YYYY-MM-DD，必填
 *
 * 响应格式：
 * {
 *   "data": {
 *     "total_plans": 100,
 *     "completed_plans": 80,
 *     "completion_rate": 80.0,
 *     "total_planned_quantity": 5000,
 *     "total_actual_quantity": 4200
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 日期参数无效
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/daily-plans/statistics?start_date=2025-06-01&end_date=2025-06-15
 */
func (h *DailyPlanHandler) GetPlanStatistics(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "开始日期和结束日期参数不能为空"})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "开始日期格式无效", "details": err.Error()})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "结束日期格式无效", "details": err.Error()})
		return
	}

	logger.Debugf("📋 Getting plan statistics from %s to %s", startDateStr, endDateStr)

	statistics, err := h.planService.GetPlanStatistics(startDate, endDate)
	if err != nil {
		logger.Errorf("❌ Failed to get plan statistics: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取计划统计失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved plan statistics")
	c.JSON(http.StatusOK, gin.H{"data": statistics})
}

/**
 * 更新计划进度
 *
 * 功能：更新计划的执行进度和状态
 *
 * HTTP方法：PUT
 * 路径：/api/production/daily-plans/{id}/progress
 *
 * 路径参数：
 * - id：计划ID
 *
 * 请求体：
 * {
 *   "actual_quantity": 80,
 *   "status": "in_progress",
 *   "notes": "进度更新备注"
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "计划进度更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 404: 计划不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/production/daily-plans/60f1b2b3c4d5e6f7a8b9c0d1/progress
 * Body: {"actual_quantity": 80, "status": "in_progress"}
 */
func (h *DailyPlanHandler) UpdatePlanProgress(c *gin.Context) {
	planID := c.Param("id")

	var req struct {
		ActualQuantity int    `json:"actual_quantity" binding:"required"`
		Status         string `json:"status"`
		Notes          string `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid progress update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("📋 Updating plan progress: %s", planID)

	// 获取现有计划
	existingPlan, err := h.planService.GetDailyPlan(planID)
	if err != nil {
		logger.Errorf("❌ Failed to get daily plan %s: %v", planID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "每日计划不存在", "details": err.Error()})
		return
	}

	// 更新进度
	existingPlan.ActualQuantity = req.ActualQuantity
	if req.Status != "" {
		existingPlan.Status = req.Status
	}
	if req.Notes != "" {
		existingPlan.Notes = req.Notes
	}

	err = h.planService.UpdateDailyPlan(planID, existingPlan)
	if err != nil {
		logger.Errorf("❌ Failed to update plan progress %s: %v", planID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新计划进度失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Plan %s progress updated successfully", planID)
	c.JSON(http.StatusOK, gin.H{"data": existingPlan, "message": "计划进度更新成功"})
}
