/**
 * 生产任务管理处理器
 *
 * 功能描述：
 * 处理生产任务管理相关的API请求，包括任务的增删改查、状态管理、执行控制等功能
 * 提供完整的生产任务生命周期管理和执行监控支持
 *
 * 主要功能：
 * - 生产任务的创建、查询、更新、删除
 * - 任务状态管理和执行控制
 * - 任务开始和完成操作
 * - 任务进度跟踪
 * - 任务统计分析
 *
 * API端点：
 * - GET /api/production/tasks - 获取生产任务列表
 * - POST /api/production/tasks - 创建生产任务
 * - GET /api/production/tasks/{id} - 获取单个任务
 * - PUT /api/production/tasks/{id} - 更新任务
 * - DELETE /api/production/tasks/{id} - 删除任务
 * - POST /api/production/tasks/{id}/start - 开始任务
 * - POST /api/production/tasks/{id}/complete - 完成任务
 *
 * @package production
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package production

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 生产任务管理处理器结构体
 *
 * 功能：处理生产任务管理相关的HTTP请求
 *
 * 依赖服务：
 * - ProductionTaskService：提供生产任务的业务逻辑和数据管理
 */
type ProductionTaskHandler struct {
	taskService *services.ProductionTaskService
}

/**
 * 创建生产任务管理处理器实例
 *
 * 功能：初始化生产任务管理处理器，注入必要的服务依赖
 *
 * @param {*services.ProductionTaskService} taskService - 生产任务服务实例
 * @returns {*ProductionTaskHandler} 生产任务管理处理器实例
 *
 * @example
 * handler := production.NewProductionTaskHandler(taskService)
 */
func NewProductionTaskHandler(taskService *services.ProductionTaskService) *ProductionTaskHandler {
	return &ProductionTaskHandler{
		taskService: taskService,
	}
}

/**
 * 获取生产任务列表
 *
 * 功能：获取生产任务列表，支持分页查询和条件过滤
 *
 * HTTP方法：GET
 * 路径：/api/production/tasks
 *
 * 查询参数：
 * - page：页码，默认1
 * - page_size：每页数量，默认20
 * - search：搜索关键词，可选
 * - status：状态过滤，可选
 * - sort_by：排序字段，默认created_at
 * - sort_desc：是否降序，默认true
 *
 * 响应格式：
 * {
 *   "data": [...],
 *   "total": 100,
 *   "page": 1,
 *   "page_size": 20,
 *   "pages": 5
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 查询参数无效
 * - 500: 查询失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/tasks?page=1&page_size=20&status=pending
 */
func (h *ProductionTaskHandler) GetProductionTasks(c *gin.Context) {
	var params models.QueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		logger.Errorf("Invalid query parameters: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "查询参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("🔧 Getting production tasks with params: %+v", params)

	response, err := h.taskService.GetProductionTasks(&params)
	if err != nil {
		logger.Errorf("❌ Failed to get production tasks: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取生产任务列表失败", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved %d production tasks", len(response.Data.([]models.ProductionTask)))
	c.JSON(http.StatusOK, response)
}

/**
 * 创建生产任务
 *
 * 功能：创建新的生产任务
 *
 * HTTP方法：POST
 * 路径：/api/production/tasks
 *
 * 请求体：
 * {
 *   "order_id": "order_001",
 *   "product_id": "product_001",
 *   "device_id": "device_001",
 *   "planned_quantity": 100,
 *   "priority": "high",
 *   "planned_start_time": "2025-06-15T08:00:00Z",
 *   "planned_end_time": "2025-06-15T16:00:00Z",
 *   "estimated_duration": 480,
 *   "notes": "任务备注"
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "生产任务创建成功"
 * }
 *
 * 状态码：
 * - 201: 创建成功
 * - 400: 请求参数无效
 * - 500: 创建失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/production/tasks
 * Body: {"order_id": "order_001", "product_id": "product_001", "device_id": "device_001", "planned_quantity": 100}
 */
func (h *ProductionTaskHandler) CreateProductionTask(c *gin.Context) {
	var req models.ProductionTaskCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid production task create request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("🔧 Creating production task for order: %s", req.OrderID)

	// 转换为ProductionTask模型
	task := &models.ProductionTask{
		OrderID:           req.OrderID,
		ProductID:         req.ProductID,
		DeviceID:          req.DeviceID,
		PlannedQuantity:   req.PlannedQuantity,
		Priority:          req.Priority,
		PlannedStartTime:  req.PlannedStartTime,
		PlannedEndTime:    req.PlannedEndTime,
		EstimatedDuration: req.EstimatedDuration,
		Notes:             req.Notes,
	}

	err := h.taskService.CreateProductionTask(task)
	if err != nil {
		logger.Errorf("❌ Failed to create production task: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建生产任务失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Production task %s created successfully", task.TaskNumber)
	c.JSON(http.StatusCreated, gin.H{"data": task, "message": "生产任务创建成功"})
}

/**
 * 获取单个生产任务
 *
 * 功能：根据任务ID获取特定生产任务的详细信息
 *
 * HTTP方法：GET
 * 路径：/api/production/tasks/{id}
 *
 * 路径参数：
 * - id：任务ID
 *
 * 响应格式：
 * {
 *   "data": {
 *     "id": "task_id",
 *     "task_number": "TASK-2025-001",
 *     "order_id": "order_001",
 *     "product_id": "product_001",
 *     "device_id": "device_001",
 *     "status": "pending",
 *     ...
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 任务不存在
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/production/tasks/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *ProductionTaskHandler) GetProductionTask(c *gin.Context) {
	taskID := c.Param("id")

	logger.Debugf("🔧 Getting production task: %s", taskID)

	task, err := h.taskService.GetProductionTask(taskID)
	if err != nil {
		logger.Errorf("❌ Failed to get production task %s: %v", taskID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "生产任务不存在", "details": err.Error()})
		return
	}

	logger.Debugf("✅ Retrieved production task: %s", taskID)
	c.JSON(http.StatusOK, gin.H{"data": task})
}

/**
 * 更新生产任务
 *
 * 功能：更新生产任务的基本信息
 *
 * HTTP方法：PUT
 * 路径：/api/production/tasks/{id}
 *
 * 路径参数：
 * - id：任务ID
 *
 * 请求体：ProductionTaskCreateRequest结构体
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "生产任务更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 404: 任务不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/production/tasks/60f1b2b3c4d5e6f7a8b9c0d1
 * Body: {"planned_quantity": 120, "priority": "urgent"}
 */
func (h *ProductionTaskHandler) UpdateProductionTask(c *gin.Context) {
	taskID := c.Param("id")

	// 先获取现有任务
	existingTask, err := h.taskService.GetProductionTask(taskID)
	if err != nil {
		logger.Errorf("❌ Failed to get production task %s: %v", taskID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "生产任务不存在", "details": err.Error()})
		return
	}

	var req models.ProductionTaskCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid production task update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	logger.Debugf("🔧 Updating production task: %s", taskID)

	// 更新字段
	existingTask.OrderID = req.OrderID
	existingTask.ProductID = req.ProductID
	existingTask.DeviceID = req.DeviceID
	existingTask.PlannedQuantity = req.PlannedQuantity
	existingTask.Priority = req.Priority
	existingTask.PlannedStartTime = req.PlannedStartTime
	existingTask.PlannedEndTime = req.PlannedEndTime
	existingTask.EstimatedDuration = req.EstimatedDuration
	existingTask.Notes = req.Notes

	err = h.taskService.UpdateProductionTask(taskID, existingTask)
	if err != nil {
		logger.Errorf("❌ Failed to update production task %s: %v", taskID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新生产任务失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Production task %s updated successfully", taskID)
	c.JSON(http.StatusOK, gin.H{"data": existingTask, "message": "生产任务更新成功"})
}

/**
 * 删除生产任务
 *
 * 功能：删除指定的生产任务
 *
 * HTTP方法：DELETE
 * 路径：/api/production/tasks/{id}
 *
 * 路径参数：
 * - id：任务ID
 *
 * 响应格式：
 * {
 *   "message": "生产任务删除成功"
 * }
 *
 * 状态码：
 * - 200: 删除成功
 * - 500: 删除失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * DELETE /api/production/tasks/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *ProductionTaskHandler) DeleteProductionTask(c *gin.Context) {
	taskID := c.Param("id")

	logger.Debugf("🔧 Deleting production task: %s", taskID)

	err := h.taskService.DeleteProductionTask(taskID)
	if err != nil {
		logger.Errorf("❌ Failed to delete production task %s: %v", taskID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除生产任务失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Production task %s deleted successfully", taskID)
	c.JSON(http.StatusOK, gin.H{"message": "生产任务删除成功"})
}

/**
 * 开始任务
 *
 * 功能：开始执行指定的生产任务
 *
 * HTTP方法：POST
 * 路径：/api/production/tasks/{id}/start
 *
 * 路径参数：
 * - id：任务ID
 *
 * 响应格式：
 * {
 *   "message": "任务已开始"
 * }
 *
 * 状态码：
 * - 200: 开始成功
 * - 500: 开始失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/production/tasks/60f1b2b3c4d5e6f7a8b9c0d1/start
 */
func (h *ProductionTaskHandler) StartTask(c *gin.Context) {
	taskID := c.Param("id")

	logger.Debugf("🔧 Starting production task: %s", taskID)

	err := h.taskService.StartTask(taskID)
	if err != nil {
		logger.Errorf("❌ Failed to start task %s: %v", taskID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "开始任务失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Task %s started successfully", taskID)
	c.JSON(http.StatusOK, gin.H{"message": "任务已开始"})
}

/**
 * 完成任务
 *
 * 功能：完成指定的生产任务
 *
 * HTTP方法：POST
 * 路径：/api/production/tasks/{id}/complete
 *
 * 路径参数：
 * - id：任务ID
 *
 * 响应格式：
 * {
 *   "message": "任务已完成"
 * }
 *
 * 状态码：
 * - 200: 完成成功
 * - 500: 完成失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/production/tasks/60f1b2b3c4d5e6f7a8b9c0d1/complete
 */
func (h *ProductionTaskHandler) CompleteTask(c *gin.Context) {
	taskID := c.Param("id")

	logger.Debugf("🔧 Completing production task: %s", taskID)

	err := h.taskService.CompleteTask(taskID)
	if err != nil {
		logger.Errorf("❌ Failed to complete task %s: %v", taskID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "完成任务失败", "details": err.Error()})
		return
	}

	logger.Infof("✅ Task %s completed successfully", taskID)
	c.JSON(http.StatusOK, gin.H{"message": "任务已完成"})
}
