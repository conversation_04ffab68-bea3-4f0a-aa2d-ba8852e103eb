/**
 * 用户管理处理器
 *
 * 功能描述：
 * 处理用户管理相关的API请求，包括用户的增删改查、权限管理等功能
 * 提供完整的用户生命周期管理和基于角色的权限控制
 *
 * 主要功能：
 * - 用户列表查询和分页
 * - 用户创建和信息管理
 * - 用户信息更新和状态管理
 * - 用户删除和权限控制
 * - 角色权限分配和管理
 *
 * API端点：
 * - GET /api/admin/users - 获取用户列表
 * - POST /api/admin/users - 创建用户
 * - GET /api/admin/users/{id} - 获取单个用户
 * - PUT /api/admin/users/{id} - 更新用户
 * - DELETE /api/admin/users/{id} - 删除用户
 *
 * @package auth
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 用户管理处理器结构体
 *
 * 功能：处理用户管理相关的HTTP请求
 *
 * 依赖服务：
 * - UserService：提供用户数据管理和业务逻辑
 * - AuthService：提供认证和授权功能
 */
type UserHandler struct {
	userService *services.UserService
	authService *services.AuthService
}

/**
 * 创建用户管理处理器实例
 *
 * 功能：初始化用户管理处理器，注入必要的服务依赖
 *
 * @param {*services.UserService} userService - 用户服务实例
 * @param {*services.AuthService} authService - 认证服务实例
 * @returns {*UserHandler} 用户管理处理器实例
 *
 * @example
 * handler := auth.NewUserHandler(userService, authService)
 */
func NewUserHandler(userService *services.UserService, authService *services.AuthService) *UserHandler {
	return &UserHandler{
		userService: userService,
		authService: authService,
	}
}

/**
 * 获取用户列表处理器
 *
 * 功能：获取系统中所有用户的列表，支持分页和查询过滤
 *
 * HTTP方法：GET
 * 路径：/api/admin/users
 *
 * 查询参数：
 * - page：页码，默认1
 * - limit：每页数量，默认10
 * - search：搜索关键词，可选
 * - role：角色过滤，可选
 * - status：状态过滤，可选
 *
 * 响应格式：
 * {
 *   "data": [...],
 *   "total": 100,
 *   "page": 1,
 *   "limit": 10,
 *   "pages": 10
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 查询参数无效
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/users?page=1&limit=20&role=admin&status=active
 */
func (h *UserHandler) GetUsers(c *gin.Context) {
	// 解析查询参数
	var params models.QueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		logger.Errorf("Invalid query parameters: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "查询参数无效", "details": err.Error()})
		return
	}

	// 调用用户服务获取用户列表
	response, err := h.userService.GetUsers(&params)
	if err != nil {
		logger.Errorf("Failed to get users: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户列表失败", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

/**
 * 创建用户处理器
 *
 * 功能：创建新的用户账户，包括用户名、邮箱、角色等信息设置
 *
 * HTTP方法：POST
 * 路径：/api/admin/users
 *
 * 请求体：
 * {
 *   "username": "newuser",
 *   "email": "<EMAIL>",
 *   "password": "password123",
 *   "role": "operator",
 *   "profile": {
 *     "full_name": "新用户",
 *     "phone": "13800138000",
 *     "department": "生产部",
 *     "position": "操作员"
 *   }
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "用户创建成功"
 * }
 *
 * 状态码：
 * - 201: 创建成功
 * - 400: 请求参数无效
 * - 500: 创建失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/admin/users
 * Body: {"username": "operator1", "email": "<EMAIL>", "password": "pass123", "role": "operator"}
 */
func (h *UserHandler) CreateUser(c *gin.Context) {
	// 解析请求参数
	var req models.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid user create request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	// 转换为User模型
	user := &models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password,
		Role:     req.Role,
		Status:   models.UserStatusActive, // 默认状态
		Profile:  req.Profile,
	}

	// 调用用户服务创建用户
	err := h.userService.CreateUser(user)
	if err != nil {
		logger.Errorf("Failed to create user: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建用户失败", "details": err.Error()})
		return
	}

	logger.Infof("User %s created successfully", req.Username)
	c.JSON(http.StatusCreated, gin.H{"data": user, "message": "用户创建成功"})
}

/**
 * 获取单个用户处理器
 *
 * 功能：根据用户ID获取特定用户的详细信息
 *
 * HTTP方法：GET
 * 路径：/api/admin/users/{id}
 *
 * 路径参数：
 * - id：用户ID
 *
 * 响应格式：
 * {
 *   "data": {
 *     "id": "user_id",
 *     "username": "admin",
 *     "email": "<EMAIL>",
 *     "role": "admin",
 *     "status": "active",
 *     "profile": {...},
 *     "permissions": [...],
 *     "created_at": "2025-06-15T10:30:00Z",
 *     "updated_at": "2025-06-15T10:30:00Z"
 *   }
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 404: 用户不存在
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/admin/users/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *UserHandler) GetUser(c *gin.Context) {
	// 获取用户ID
	userID := c.Param("id")

	// 调用用户服务获取用户信息
	user, err := h.userService.GetUser(userID)
	if err != nil {
		logger.Errorf("Failed to get user %s: %v", userID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": user})
}

/**
 * 更新用户处理器
 *
 * 功能：更新用户的基本信息、角色、权限等
 *
 * HTTP方法：PUT
 * 路径：/api/admin/users/{id}
 *
 * 路径参数：
 * - id：用户ID
 *
 * 请求体：
 * {
 *   "email": "<EMAIL>",
 *   "role": "admin",
 *   "status": "active",
 *   "permissions": ["users:read", "users:write"],
 *   "profile": {
 *     "full_name": "更新的姓名",
 *     "phone": "13900139000",
 *     "department": "管理部",
 *     "position": "管理员"
 *   }
 * }
 *
 * 响应格式：
 * {
 *   "data": {...},
 *   "message": "用户更新成功"
 * }
 *
 * 状态码：
 * - 200: 更新成功
 * - 400: 请求参数无效
 * - 404: 用户不存在
 * - 500: 更新失败
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * PUT /api/admin/users/60f1b2b3c4d5e6f7a8b9c0d1
 * Body: {"email": "<EMAIL>", "role": "admin", "status": "active"}
 */
func (h *UserHandler) UpdateUser(c *gin.Context) {
	// 获取用户ID
	userID := c.Param("id")

	// 解析请求参数
	var req models.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid user update request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	// 先获取现有用户
	existingUser, err := h.userService.GetUser(userID)
	if err != nil {
		logger.Errorf("Failed to get user %s: %v", userID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在", "details": err.Error()})
		return
	}

	// 更新字段
	if req.Email != "" {
		existingUser.Email = req.Email
	}
	if req.Role != "" {
		existingUser.Role = req.Role
		// 更新角色时重新设置默认权限
		if req.Permissions == nil {
			existingUser.Permissions = h.getDefaultPermissions(req.Role)
		}
	}
	if req.Status != "" {
		existingUser.Status = req.Status
	}
	if req.Permissions != nil {
		existingUser.Permissions = req.Permissions
	}

	// 更新用户资料
	if req.Profile.FullName != "" {
		existingUser.Profile.FullName = req.Profile.FullName
	}
	if req.Profile.Phone != "" {
		existingUser.Profile.Phone = req.Profile.Phone
	}
	if req.Profile.Department != "" {
		existingUser.Profile.Department = req.Profile.Department
	}
	if req.Profile.Position != "" {
		existingUser.Profile.Position = req.Profile.Position
	}
	if req.Profile.Avatar != "" {
		existingUser.Profile.Avatar = req.Profile.Avatar
	}

	// 调用用户服务更新用户
	err = h.userService.UpdateUser(userID, existingUser)
	if err != nil {
		logger.Errorf("Failed to update user %s: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新用户失败", "details": err.Error()})
		return
	}

	logger.Infof("User %s updated successfully", userID)
	c.JSON(http.StatusOK, gin.H{"data": existingUser, "message": "用户更新成功"})
}

/**
 * 删除用户处理器
 *
 * 功能：删除指定的用户账户，包含安全检查
 *
 * HTTP方法：DELETE
 * 路径：/api/admin/users/{id}
 *
 * 路径参数：
 * - id：用户ID
 *
 * 响应格式：
 * {
 *   "message": "用户删除成功"
 * }
 *
 * 状态码：
 * - 200: 删除成功
 * - 400: 不能删除当前用户
 * - 404: 用户不存在
 * - 500: 删除失败
 *
 * 安全检查：
 * - 不允许删除当前登录的用户
 * - 记录删除操作日志
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * DELETE /api/admin/users/60f1b2b3c4d5e6f7a8b9c0d1
 */
func (h *UserHandler) DeleteUser(c *gin.Context) {
	// 获取用户ID
	userID := c.Param("id")

	// 检查是否是当前用户
	currentUserID, exists := c.Get("user_id")
	if exists && currentUserID.(string) == userID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不能删除当前登录用户"})
		return
	}

	// 调用用户服务删除用户
	err := h.userService.DeleteUser(userID)
	if err != nil {
		logger.Errorf("Failed to delete user %s: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除用户失败", "details": err.Error()})
		return
	}

	logger.Infof("User %s deleted successfully", userID)
	c.JSON(http.StatusOK, gin.H{"message": "用户删除成功"})
}

/**
 * 获取角色默认权限
 *
 * 功能：根据用户角色返回对应的默认权限列表
 *
 * 权限体系：
 * - admin：拥有所有系统权限
 * - operator：拥有生产相关的操作权限
 * - viewer：只有数据查看权限
 *
 * @param {string} role - 用户角色
 * @returns {[]string} 权限列表
 */
func (h *UserHandler) getDefaultPermissions(role string) []string {
	switch role {
	case models.RoleAdmin:
		return []string{
			"users:read", "users:write", "users:delete",
			"devices:read", "devices:write", "devices:delete",
			"products:read", "products:write", "products:delete",
			"processes:read", "processes:write", "processes:delete",
			"data:manage", "statistics:read",
		}
	case models.RoleOperator:
		return []string{
			"devices:read", "devices:write",
			"products:read", "products:write",
			"processes:read", "processes:write",
			"statistics:read",
		}
	case models.RoleViewer:
		return []string{
			"devices:read",
			"products:read",
			"processes:read",
			"statistics:read",
		}
	default:
		return []string{"statistics:read"}
	}
}
