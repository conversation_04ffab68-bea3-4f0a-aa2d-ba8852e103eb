/**
 * 用户认证处理器
 *
 * 功能描述：
 * 处理用户认证相关的API请求，包括登录、令牌刷新、密码修改等核心安全功能
 * 提供基于JWT的无状态认证机制和完整的安全控制
 *
 * 主要功能：
 * - 用户登录认证和令牌生成
 * - JWT令牌刷新和验证
 * - 用户个人资料查看和修改
 * - 密码安全修改
 * - 认证状态管理
 *
 * API端点：
 * - POST /api/auth/login - 用户登录
 * - POST /api/auth/refresh - 刷新访问令牌
 * - GET /api/user/profile - 获取用户资料
 * - POST /api/user/change-password - 修改密码
 *
 * @package auth
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package auth

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 用户认证处理器结构体
 *
 * 功能：处理用户认证相关的HTTP请求
 *
 * 依赖服务：
 * - UserService：提供用户数据管理和业务逻辑
 * - AuthService：提供认证和授权功能
 */
type AuthHandler struct {
	userService *services.UserService
	authService *services.AuthService
}

/**
 * 创建用户认证处理器实例
 *
 * 功能：初始化用户认证处理器，注入必要的服务依赖
 *
 * @param {*services.UserService} userService - 用户服务实例
 * @param {*services.AuthService} authService - 认证服务实例
 * @returns {*AuthHandler} 用户认证处理器实例
 *
 * @example
 * handler := auth.NewAuthHandler(userService, authService)
 */
func NewAuthHandler(userService *services.UserService, authService *services.AuthService) *AuthHandler {
	return &AuthHandler{
		userService: userService,
		authService: authService,
	}
}

/**
 * 用户登录处理器
 *
 * 功能：处理用户登录请求，验证身份并返回访问令牌
 *
 * HTTP方法：POST
 * 路径：/api/auth/login
 *
 * 请求体：
 * {
 *   "username": "admin",
 *   "password": "admin123"
 * }
 *
 * 响应格式：
 * {
 *   "data": {
 *     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *     "user": {...},
 *     "expires_at": "2025-06-15T10:30:00Z"
 *   },
 *   "message": "登录成功"
 * }
 *
 * 状态码：
 * - 200: 登录成功
 * - 400: 请求参数无效
 * - 401: 认证失败
 *
 * 安全措施：
 * - 参数验证：防止恶意输入和注入攻击
 * - 错误处理：统一的错误响应，不泄露敏感信息
 * - 日志记录：记录登录尝试，便于安全审计
 * - 令牌安全：JWT令牌的安全生成和传输
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/auth/login
 * Body: {"username": "admin", "password": "admin123"}
 */
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.UserLoginRequest

	// 第一步：解析登录请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid login request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数无效",
			"details": err.Error(),
		})
		return
	}

	// 第二步：调用认证服务进行身份验证
	response, err := h.authService.Login(&req)
	if err != nil {
		logger.Errorf("Login failed: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "登录失败",
			"details": err.Error(),
		})
		return
	}

	// 第三步：记录成功登录并返回响应
	logger.Infof("User %s logged in successfully", req.Username)
	c.JSON(http.StatusOK, gin.H{
		"data":    response,
		"message": "登录成功",
	})
}

/**
 * 刷新访问令牌处理器
 *
 * 功能：刷新用户的JWT访问令牌，延长会话有效期
 *
 * HTTP方法：POST
 * 路径：/api/auth/refresh
 *
 * 请求头：
 * Authorization: Bearer <current_token>
 *
 * 响应格式：
 * {
 *   "data": {
 *     "token": "new_jwt_token",
 *     "expires_at": "2025-06-15T11:30:00Z"
 *   },
 *   "message": "Token刷新成功"
 * }
 *
 * 状态码：
 * - 200: 刷新成功
 * - 401: 认证失败或令牌无效
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/auth/refresh
 * Headers: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// 获取认证头
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证头"})
		return
	}

	// 提取令牌
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString == authHeader {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证头格式"})
		return
	}

	// 调用认证服务刷新令牌
	response, err := h.authService.RefreshToken(tokenString)
	if err != nil {
		logger.Errorf("Token refresh failed: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "刷新token失败", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": response, "message": "Token刷新成功"})
}

/**
 * 获取用户个人资料处理器
 *
 * 功能：获取当前登录用户的个人资料信息
 *
 * HTTP方法：GET
 * 路径：/api/user/profile
 *
 * 请求头：
 * Authorization: Bearer <access_token>
 *
 * 响应格式：
 * {
 *   "data": {
 *     "id": "user_id",
 *     "username": "admin",
 *     "email": "<EMAIL>",
 *     "role": "admin",
 *     "profile": {...}
 *   }
 * }
 *
 * 状态码：
 * - 200: 获取成功
 * - 401: 用户未认证
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/user/profile
 * Headers: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
func (h *AuthHandler) GetProfile(c *gin.Context) {
	// 从中间件获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 获取用户信息
	user, err := h.userService.GetUser(userID.(string))
	if err != nil {
		logger.Errorf("Failed to get user profile: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户信息失败", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": user})
}

/**
 * 修改密码处理器
 *
 * 功能：允许用户修改自己的登录密码
 *
 * HTTP方法：POST
 * 路径：/api/user/change-password
 *
 * 请求头：
 * Authorization: Bearer <access_token>
 *
 * 请求体：
 * {
 *   "old_password": "current_password",
 *   "new_password": "new_password"
 * }
 *
 * 响应格式：
 * {
 *   "message": "密码修改成功"
 * }
 *
 * 状态码：
 * - 200: 修改成功
 * - 400: 请求参数无效或旧密码错误
 * - 401: 用户未认证
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * POST /api/user/change-password
 * Headers: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Body: {"old_password": "old123", "new_password": "new456"}
 */
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	// 从中间件获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 解析请求参数
	var req models.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Invalid change password request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效", "details": err.Error()})
		return
	}

	// 调用用户服务修改密码
	err := h.userService.ChangePassword(userID.(string), req.OldPassword, req.NewPassword)
	if err != nil {
		logger.Errorf("Failed to change password: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "修改密码失败", "details": err.Error()})
		return
	}

	logger.Infof("User %s changed password successfully", userID)
	c.JSON(http.StatusOK, gin.H{"message": "密码修改成功"})
}
