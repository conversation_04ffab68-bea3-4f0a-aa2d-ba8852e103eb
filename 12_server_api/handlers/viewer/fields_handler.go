/**
 * 查看器字段处理器
 *
 * 功能描述：
 * 专门为 20_viewer 前端应用提供字段和数据类型相关的API服务
 * 处理字段数据查询、可用字段获取和数据类型管理功能
 *
 * 主要功能：
 * - 获取字段的时序数据
 * - 获取可用字段列表
 * - 获取数据类型选项
 * - 字段数据格式转换
 *
 * API端点：
 * - GET /api/viewer/fields/data - 获取字段数据
 * - GET /api/viewer/fields/available - 获取可用字段
 * - GET /api/viewer/data-types - 获取数据类型
 *
 * @package viewer
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package viewer

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 查看器字段处理器结构体
 *
 * 功能：处理20_viewer字段和数据类型相关的HTTP请求
 *
 * 依赖服务：
 * - DataService：提供字段数据访问接口
 * - InfluxDBService：提供原始InfluxDB数据访问
 * - MonitoringService：提供API性能监控
 */
type FieldsHandler struct {
	dataService       *services.DataService
	influxService     *services.InfluxDBService
	monitoringService *services.MonitoringService
}

/**
 * 创建查看器字段处理器实例
 *
 * 功能：初始化查看器字段处理器，注入必要的服务依赖
 *
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.InfluxDBService} influxService - InfluxDB服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @returns {*FieldsHandler} 查看器字段处理器实例
 *
 * @example
 * handler := viewer.NewFieldsHandler(dataService, influxService, monitoringService)
 */
func NewFieldsHandler(dataService *services.DataService, influxService *services.InfluxDBService, monitoringService *services.MonitoringService) *FieldsHandler {
	return &FieldsHandler{
		dataService:       dataService,
		influxService:     influxService,
		monitoringService: monitoringService,
	}
}

/**
 * 获取字段数据
 *
 * 功能：获取特定字段的时序数据，支持多设备过滤
 *
 * HTTP方法：GET
 * 路径：/api/viewer/fields/data
 *
 * 查询参数：
 * - field：字段名称（必需）
 * - timeRange：时间范围，如"-1h"、"-24h"等，默认"-1h"
 * - devices：设备ID列表，逗号分隔，可选
 *
 * 响应格式：
 * [
 *   {
 *     "timestamp": "2025-06-15T10:30:00Z",
 *     "value": 25.6,
 *     "device_id": "fanuc_001",
 *     "field": "temperature"
 *   }
 * ]
 *
 * 状态码：
 * - 200: 查询成功
 * - 400: 参数错误
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/viewer/fields/data?field=temperature&timeRange=-24h&devices=fanuc_001,fanuc_002
 */
func (h *FieldsHandler) GetFieldData(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/viewer/fields/data", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析查询参数
	field := c.Query("field")
	timeRange := c.DefaultQuery("timeRange", "-1h")
	devicesParam := c.Query("devices")

	// 验证必需参数
	if field == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "字段名称不能为空",
			Error:   "field parameter is required",
		})
		return
	}

	// 解析设备列表
	var devices []string
	if devicesParam != "" {
		devices = strings.Split(devicesParam, ",")
	}

	logger.Debugf("📊 Getting field data for field: %s, timeRange: %s, devices: %v", field, timeRange, devices)

	// 检查InfluxDB服务可用性
	if h.influxService == nil {
		logger.Error("❌ InfluxDB service not available")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "InfluxDB服务不可用",
			Error:   "InfluxDB service not initialized",
		})
		return
	}

	// 获取字段数据
	data, err := h.influxService.GetFieldData(field, timeRange, devices)
	if err != nil {
		logger.Errorf("❌ Failed to get field data: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取字段数据失败",
			Error:   err.Error(),
		})
		return
	}

	// 转换为20_viewer期望的格式
	chartData := h.convertToChartData(data)

	logger.Debugf("✅ Retrieved %d data points for field: %s", len(chartData), field)
	c.JSON(http.StatusOK, chartData)
}

/**
 * 获取可用字段列表
 *
 * 功能：获取系统中所有可用的数据字段
 *
 * HTTP方法：GET
 * 路径：/api/viewer/fields/available
 *
 * 查询参数：
 * - data_type：数据类型过滤，可选
 *
 * 响应格式：
 * [
 *   {
 *     "field": "temperature",
 *     "display_name": "温度",
 *     "unit": "°C",
 *     "data_type": "fanuc_30i",
 *     "description": "设备温度传感器数据"
 *   }
 * ]
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/viewer/fields/available?data_type=fanuc_30i
 */
func (h *FieldsHandler) GetAvailableFields(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/viewer/fields/available", "GET", c.Writer.Status(), duration)
		}
	}()

	// 解析查询参数
	dataType := c.Query("data_type")

	logger.Debugf("📊 Getting available fields for data_type: %s", dataType)

	// 获取可用字段列表
	fields := h.getAvailableFieldsList(dataType)

	logger.Debugf("✅ Retrieved %d available fields", len(fields))
	c.JSON(http.StatusOK, fields)
}

/**
 * 获取数据类型列表
 *
 * 功能：获取系统中所有可用的数据类型
 *
 * HTTP方法：GET
 * 路径：/api/viewer/data-types
 *
 * 响应格式：
 * [
 *   {
 *     "data_type": "fanuc_30i",
 *     "display_name": "FANUC 30i系列",
 *     "description": "FANUC 30i系列数控机床",
 *     "fields": ["temperature", "spindle_speed", "feed_rate"]
 *   }
 * ]
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/viewer/data-types
 */
func (h *FieldsHandler) GetDataTypes(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/viewer/data-types", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debug("📊 Getting data types list")

	// 获取数据类型列表
	dataTypes := h.getDataTypesList()

	logger.Debugf("✅ Retrieved %d data types", len(dataTypes))
	c.JSON(http.StatusOK, dataTypes)
}

/**
 * 转换为图表数据格式
 *
 * 功能：将传感器数据点转换为20_viewer期望的图表数据格式
 *
 * @param {[]models.SensorDataPoint} data - 传感器数据点列表
 * @returns {[]map[string]interface{}} 图表数据格式
 */
func (h *FieldsHandler) convertToChartData(data []models.SensorDataPoint) []map[string]interface{} {
	chartData := make([]map[string]interface{}, len(data))

	for i, point := range data {
		chartData[i] = map[string]interface{}{
			"timestamp": point.Timestamp.Format(time.RFC3339),
			"value":     point.Value,
			"device_id": point.DeviceID,
			"field":     point.Field,
		}
	}

	return chartData
}

/**
 * 获取可用字段列表
 *
 * 功能：根据数据类型过滤返回可用的字段列表
 *
 * @param {string} dataType - 数据类型过滤条件
 * @returns {[]map[string]interface{}} 可用字段列表
 */
func (h *FieldsHandler) getAvailableFieldsList(dataType string) []map[string]interface{} {
	// 定义常用字段
	allFields := []map[string]interface{}{
		{
			"field":        "temperature",
			"display_name": "温度",
			"unit":         "°C",
			"data_type":    "fanuc_30i",
			"description":  "设备温度传感器数据",
		},
		{
			"field":        "spindle_speed",
			"display_name": "主轴转速",
			"unit":         "RPM",
			"data_type":    "fanuc_30i",
			"description":  "主轴转速数据",
		},
		{
			"field":        "feed_rate",
			"display_name": "进给速度",
			"unit":         "mm/min",
			"data_type":    "fanuc_30i",
			"description":  "进给速度数据",
		},
		{
			"field":        "pressure",
			"display_name": "压力",
			"unit":         "bar",
			"data_type":    "pressure",
			"description":  "压力传感器数据",
		},
		{
			"field":        "vibration",
			"display_name": "振动",
			"unit":         "mm/s",
			"data_type":    "motion",
			"description":  "振动传感器数据",
		},
	}

	// 如果指定了数据类型，进行过滤
	if dataType != "" {
		var filteredFields []map[string]interface{}
		for _, field := range allFields {
			if field["data_type"] == dataType {
				filteredFields = append(filteredFields, field)
			}
		}
		return filteredFields
	}

	return allFields
}

/**
 * 获取数据类型列表
 *
 * 功能：返回系统支持的所有数据类型
 *
 * @returns {[]map[string]interface{}} 数据类型列表
 */
func (h *FieldsHandler) getDataTypesList() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"data_type":    "fanuc_30i",
			"display_name": "FANUC 30i系列",
			"description":  "FANUC 30i系列数控机床",
			"fields":       []string{"temperature", "spindle_speed", "feed_rate"},
		},
		{
			"data_type":    "siemens_840d",
			"display_name": "SIEMENS 840D系列",
			"description":  "SIEMENS 840D系列数控机床",
			"fields":       []string{"temperature", "spindle_speed", "axis_position"},
		},
		{
			"data_type":    "temperature",
			"display_name": "温度传感器",
			"description":  "各类温度传感器数据",
			"fields":       []string{"temperature"},
		},
		{
			"data_type":    "pressure",
			"display_name": "压力传感器",
			"description":  "各类压力传感器数据",
			"fields":       []string{"pressure"},
		},
		{
			"data_type":    "motion",
			"display_name": "运动传感器",
			"description":  "振动、位移等运动传感器数据",
			"fields":       []string{"vibration", "displacement"},
		},
		{
			"data_type":    "unknown",
			"display_name": "未知类型",
			"description":  "未分类的设备数据",
			"fields":       []string{},
		},
	}
}
