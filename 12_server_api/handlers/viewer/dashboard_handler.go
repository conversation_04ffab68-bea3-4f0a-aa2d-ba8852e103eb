/**
 * 查看器仪表板处理器
 *
 * 功能描述：
 * 专门为 20_viewer 前端应用提供仪表板统计和连接测试相关的API服务
 * 提供系统概览、统计信息和服务连接状态检查功能
 *
 * 主要功能：
 * - 获取仪表板统计信息
 * - 测试服务连接状态
 * - 系统概览数据汇总
 * - 实时状态监控
 *
 * API端点：
 * - GET /api/viewer/dashboard/stats - 获取仪表板统计
 * - GET /api/viewer/connection/test - 测试连接状态
 *
 * @package viewer
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package viewer

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/models"
	"server-api/services"
	"shared/logger"
)

/**
 * 查看器仪表板处理器结构体
 *
 * 功能：处理20_viewer仪表板和连接测试相关的HTTP请求
 *
 * 依赖服务：
 * - DataService：提供统计数据访问接口
 * - InfluxDBService：提供原始InfluxDB数据访问
 * - MonitoringService：提供API性能监控
 */
type DashboardHandler struct {
	dataService       *services.DataService
	influxService     *services.InfluxDBService
	monitoringService *services.MonitoringService
}

/**
 * 创建查看器仪表板处理器实例
 *
 * 功能：初始化查看器仪表板处理器，注入必要的服务依赖
 *
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.InfluxDBService} influxService - InfluxDB服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @returns {*DashboardHandler} 查看器仪表板处理器实例
 *
 * @example
 * handler := viewer.NewDashboardHandler(dataService, influxService, monitoringService)
 */
func NewDashboardHandler(dataService *services.DataService, influxService *services.InfluxDBService, monitoringService *services.MonitoringService) *DashboardHandler {
	return &DashboardHandler{
		dataService:       dataService,
		influxService:     influxService,
		monitoringService: monitoringService,
	}
}

/**
 * 获取仪表板统计信息
 *
 * 功能：提供系统概览统计信息，包括设备总数、在线设备数、记录总数等
 *
 * HTTP方法：GET
 * 路径：/api/viewer/dashboard/stats
 *
 * 响应格式：
 * {
 *   "total_devices": 10,
 *   "online_devices": 8,
 *   "offline_devices": 2,
 *   "total_records": 15000,
 *   "latest_update": "2025-06-15T10:30:00Z",
 *   "status_distribution": {
 *     "online": 8,
 *     "warning": 1,
 *     "offline": 1
 *   },
 *   "data_types": [
 *     {"type": "fanuc_30i", "count": 5},
 *     {"type": "temperature", "count": 3}
 *   ]
 * }
 *
 * 状态码：
 * - 200: 查询成功
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/viewer/dashboard/stats
 */
func (h *DashboardHandler) GetDashboardStats(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/viewer/dashboard/stats", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debug("📊 Getting dashboard stats for viewer")

	// 智能获取设备数据：优先当天，然后尝试最近7天
	devices, err := h.getRecentDevicesData()
	if err != nil {
		logger.Errorf("❌ Failed to get devices status for dashboard stats: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取设备状态失败",
			Error:   "No device data found for dashboard stats",
		})
		return
	}

	// 计算统计信息
	stats := h.calculateDashboardStats(devices)

	logger.Debugf("✅ Retrieved dashboard stats for %d devices", len(devices))
	c.JSON(http.StatusOK, stats)
}

/**
 * 测试连接状态
 *
 * 功能：测试各个服务的连接状态，用于系统健康检查
 *
 * HTTP方法：GET
 * 路径：/api/viewer/connection/test
 *
 * 响应格式：
 * {
 *   "influxdb": {
 *     "status": "connected",
 *     "response_time": 25,
 *     "last_check": "2025-06-15T10:30:00Z"
 *   },
 *   "mongodb": {
 *     "status": "connected",
 *     "response_time": 15,
 *     "last_check": "2025-06-15T10:30:00Z"
 *   },
 *   "overall_status": "healthy"
 * }
 *
 * 状态码：
 * - 200: 测试完成
 * - 500: 服务器内部错误
 *
 * @param {*gin.Context} c - Gin上下文对象
 *
 * @example
 * GET /api/viewer/connection/test
 */
func (h *DashboardHandler) TestConnection(c *gin.Context) {
	startTime := time.Now()

	// 记录API调用性能指标
	defer func() {
		duration := time.Since(startTime)
		if h.monitoringService != nil {
			h.monitoringService.RecordAPIMetric("/api/viewer/connection/test", "GET", c.Writer.Status(), duration)
		}
	}()

	logger.Debug("🔧 Testing connections for viewer")

	// 测试各个服务的连接状态
	connectionStatus := h.testAllConnections()

	logger.Debug("✅ Connection test completed")
	c.JSON(http.StatusOK, connectionStatus)
}

/**
 * 智能获取最近的设备数据
 *
 * 功能：优先获取当天数据，如果没有则尝试最近7天的数据
 *
 * @returns {[]models.DeviceStatus, error} 设备状态列表和错误信息
 */
func (h *DashboardHandler) getRecentDevicesData() ([]models.DeviceStatus, error) {
	// 尝试获取当天的设备状态
	today := time.Now().Format("2006-01-02")
	devices, err := h.dataService.GetDevicesStatus([]string{}, today)

	// 如果当天有数据，直接返回
	if err == nil && len(devices) > 0 {
		logger.Debugf("✅ Found %d devices from today (%s) for dashboard stats", len(devices), today)
		return devices, nil
	}

	logger.Debugf("⚠️ No data for today (%s), trying recent data for dashboard stats", today)

	// 尝试最近7天的数据
	for i := 1; i <= 7; i++ {
		pastDate := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		devices, err = h.dataService.GetDevicesStatus([]string{}, pastDate)
		if err == nil && len(devices) > 0 {
			logger.Debugf("✅ Found %d devices from %s for dashboard stats", len(devices), pastDate)
			return devices, nil
		}
	}

	// 如果都没有数据，返回错误
	return nil, err
}

/**
 * 计算仪表板统计信息
 *
 * 功能：根据设备状态数据计算各种统计指标
 *
 * @param {[]models.DeviceStatus} devices - 设备状态数据列表
 * @returns {map[string]interface{}} 统计信息
 */
func (h *DashboardHandler) calculateDashboardStats(devices []models.DeviceStatus) map[string]interface{} {
	totalDevices := len(devices)
	onlineDevices := 0
	offlineDevices := 0
	totalRecords := 0
	var latestUpdate time.Time

	// 状态分布统计
	statusDistribution := map[string]int{
		"online":  0,
		"warning": 0,
		"offline": 0,
	}

	// 数据类型统计
	dataTypeCount := make(map[string]int)

	// 遍历设备计算统计信息
	for _, device := range devices {
		// 计算在线/离线设备数
		viewerStatus := h.mapToViewerStatus(device.Status)
		statusDistribution[viewerStatus]++

		if viewerStatus == "online" {
			onlineDevices++
		} else {
			offlineDevices++
		}

		// 累计记录数（基于设备状态估算）
		totalRecords += h.calculateDeviceRecords(device)

		// 更新最新时间
		if device.Timestamp.After(latestUpdate) {
			latestUpdate = device.Timestamp
		}

		// 统计数据类型
		dataType := device.DataType
		if dataType == "" {
			dataType = h.getDataTypeFromDevice(device)
		}
		dataTypeCount[dataType]++
	}

	// 转换数据类型统计为数组格式
	var dataTypes []map[string]interface{}
	for dataType, count := range dataTypeCount {
		dataTypes = append(dataTypes, map[string]interface{}{
			"type":  dataType,
			"count": count,
		})
	}

	// 构建统计结果
	return map[string]interface{}{
		"total_devices":       totalDevices,
		"online_devices":      onlineDevices,
		"offline_devices":     offlineDevices,
		"total_records":       totalRecords,
		"latest_update":       latestUpdate.Format(time.RFC3339),
		"status_distribution": statusDistribution,
		"data_types":          dataTypes,
	}
}

/**
 * 测试所有服务连接状态
 *
 * 功能：测试InfluxDB、MongoDB等服务的连接状态
 *
 * @returns {map[string]interface{}} 连接状态信息
 */
func (h *DashboardHandler) testAllConnections() map[string]interface{} {
	now := time.Now()
	overallStatus := "healthy"

	// 测试InfluxDB连接
	influxStatus := h.testInfluxDBConnection()
	if influxStatus["status"] != "connected" {
		overallStatus = "degraded"
	}

	// 测试MongoDB连接（通过DataService）
	mongoStatus := h.testMongoDBConnection()
	if mongoStatus["status"] != "connected" {
		overallStatus = "degraded"
	}

	return map[string]interface{}{
		"influxdb":       influxStatus,
		"mongodb":        mongoStatus,
		"overall_status": overallStatus,
		"test_time":      now.Format(time.RFC3339),
	}
}

/**
 * 测试InfluxDB连接状态
 *
 * @returns {map[string]interface{}} InfluxDB连接状态
 */
func (h *DashboardHandler) testInfluxDBConnection() map[string]interface{} {
	startTime := time.Now()

	if h.influxService == nil {
		return map[string]interface{}{
			"status":        "disconnected",
			"response_time": 0,
			"last_check":    startTime.Format(time.RFC3339),
			"error":         "InfluxDB service not initialized",
		}
	}

	// 尝试执行简单查询测试连接
	_, err := h.influxService.GetDeviceData("test", "-1m")
	responseTime := time.Since(startTime).Milliseconds()

	if err != nil {
		return map[string]interface{}{
			"status":        "disconnected",
			"response_time": responseTime,
			"last_check":    startTime.Format(time.RFC3339),
			"error":         err.Error(),
		}
	}

	return map[string]interface{}{
		"status":        "connected",
		"response_time": responseTime,
		"last_check":    startTime.Format(time.RFC3339),
	}
}

/**
 * 测试MongoDB连接状态
 *
 * @returns {map[string]interface{}} MongoDB连接状态
 */
func (h *DashboardHandler) testMongoDBConnection() map[string]interface{} {
	startTime := time.Now()

	if h.dataService == nil {
		return map[string]interface{}{
			"status":        "disconnected",
			"response_time": 0,
			"last_check":    startTime.Format(time.RFC3339),
			"error":         "Data service not initialized",
		}
	}

	// 尝试获取设备列表测试连接
	_, err := h.dataService.GetDevicesStatus([]string{}, time.Now().Format("2006-01-02"))
	responseTime := time.Since(startTime).Milliseconds()

	if err != nil {
		return map[string]interface{}{
			"status":        "disconnected",
			"response_time": responseTime,
			"last_check":    startTime.Format(time.RFC3339),
			"error":         err.Error(),
		}
	}

	return map[string]interface{}{
		"status":        "connected",
		"response_time": responseTime,
		"last_check":    startTime.Format(time.RFC3339),
	}
}

/**
 * 状态映射：将内部状态转换为viewer期望的状态
 *
 * @param {string} deviceStatus - 内部设备状态
 * @returns {string} viewer期望的状态
 */
func (h *DashboardHandler) mapToViewerStatus(deviceStatus string) string {
	switch strings.ToLower(deviceStatus) {
	case "production", "running", "idle", "standby":
		return "online"
	case "fault", "alarm", "error":
		return "warning"
	case "shutdown", "offline", "disconnected", "stop":
		return "offline"
	default:
		return "offline"
	}
}

/**
 * 计算设备记录数
 *
 * @param {models.DeviceStatus} device - 设备状态数据
 * @returns {int} 估算的记录数
 */
func (h *DashboardHandler) calculateDeviceRecords(device models.DeviceStatus) int {
	// 基于设备运行时间和状态估算记录数
	baseRecords := 1000
	if device.Status == "production" || device.Status == "running" {
		baseRecords += 500
	}
	return baseRecords
}

/**
 * 从设备信息中推断数据类型
 *
 * @param {models.DeviceStatus} device - 设备状态数据
 * @returns {string} 推断的数据类型
 */
func (h *DashboardHandler) getDataTypeFromDevice(device models.DeviceStatus) string {
	// 如果设备有明确的数据类型，直接返回
	if device.DeviceCode != "" {
		// 根据设备编码推断数据类型
		if strings.Contains(strings.ToLower(device.DeviceCode), "fanuc") {
			return "fanuc_30i"
		}
		if strings.Contains(strings.ToLower(device.DeviceCode), "siemens") {
			return "siemens_840d"
		}
	}

	// 根据设备ID推断数据类型
	deviceID := strings.ToLower(device.DeviceID)
	if strings.Contains(deviceID, "fanuc") {
		return "fanuc_30i"
	}
	if strings.Contains(deviceID, "temp") {
		return "temperature"
	}
	if strings.Contains(deviceID, "pressure") {
		return "pressure"
	}
	if strings.Contains(deviceID, "motion") {
		return "motion"
	}

	return "unknown"
}
