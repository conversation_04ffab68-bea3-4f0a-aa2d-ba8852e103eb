/**
 * MongoDB管理服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的业务数据管理核心，负责管理MongoDB文档数据库
 * 的连接和提供完整的CRUD操作，支持设备管理、产品管理、工序管理等业务功能
 *
 * 主要功能：
 * - 连接管理：MongoDB连接的建立、健康检查、优雅关闭
 * - 设备管理：设备信息的增删改查、状态管理、配置管理
 * - 产品管理：产品信息的维护、规格管理、分类管理
 * - 工序管理：生产工序的定义、流程管理、参数配置
 * - 订单管理：生产订单的全生命周期管理
 * - 用户管理：用户账户、权限、认证信息管理
 * - 统计数据：历史统计数据的持久化存储
 *
 * 技术特性：
 * - 文档数据库：灵活的JSON文档存储，适合复杂业务数据
 * - 事务支持：ACID事务保证数据一致性
 * - 索引优化：基于查询模式的索引设计，提升查询性能
 * - 分页查询：高效的分页和排序机制
 * - 全文搜索：支持模糊搜索和多字段搜索
 * - 聚合查询：复杂的数据统计和分析功能
 *
 * 数据模型：
 * - devices：设备基础信息和配置参数
 * - products：产品信息和规格参数
 * - processes：工序定义和工艺参数
 * - orders：生产订单和计划信息
 * - users：用户账户和权限信息
 * - device_statistics：设备统计历史数据
 * - production_tasks：生产任务和执行记录
 *
 * 性能优化：
 * - 连接池：高效的连接复用和资源管理
 * - 索引策略：基于业务查询模式的索引设计
 * - 批量操作：减少网络往返，提高吞吐量
 * - 查询优化：使用投影和过滤减少数据传输
 * - 缓存集成：与Redis缓存层的协同工作
 *
 * 业务场景：
 * - 设备配置管理：设备参数配置和状态管理
 * - 生产计划管理：订单、产品、工序的关联管理
 * - 用户权限管理：系统用户和权限的集中管理
 * - 历史数据存储：统计结果和业务数据的长期保存
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"server-api/db"
	"server-api/models"
	"shared/logger"
)

/**
 * MongoDB管理服务结构体
 *
 * 功能：作为系统的业务数据持久化层，提供完整的MongoDB数据库操作接口
 *
 * 架构设计：
 * - 统一连接管理：使用MongoDB管理器单例，避免多连接问题
 * - 数据库抽象：封装底层数据库操作，提供业务友好的接口
 * - 事务支持：复杂业务操作的事务一致性保证
 * - 错误处理：统一的错误处理和异常恢复机制
 *
 * 数据管理：
 * - 集合管理：设备、产品、工序、订单等业务集合
 * - 索引优化：基于查询模式的索引策略
 * - 数据验证：业务规则的数据完整性检查
 * - 版本控制：数据变更的审计和版本管理
 *
 * 性能特性：
 * - 连接池：通过MongoDB管理器统一管理连接池
 * - 批量操作：减少网络开销，提高吞吐量
 * - 查询优化：投影、过滤、索引的综合优化
 * - 分页机制：大数据集的高效分页查询
 *
 * @struct MongoAdminService
 */
type MongoAdminService struct {
	/** MongoDB管理器实例，提供统一的连接管理 */
	mongoManager *db.MongoDBManager

	/** 全局上下文，用于控制所有数据库操作的生命周期 */
	ctx context.Context
}

/**
 * 创建MongoDB管理服务实例（使用统一的MongoDB管理器）
 *
 * 功能：基于MongoDB管理器创建管理服务实例，实现连接池的统一管理
 *
 * 优化特性：
 * 1. 使用单例MongoDB管理器，避免多连接问题
 * 2. 统一的连接池配置，提高性能和稳定性
 * 3. 自动重连机制，提高服务可靠性
 * 4. 连接状态监控，便于问题诊断
 *
 * 参数说明：
 * - mongoManager：MongoDB管理器实例，提供统一的连接管理
 *
 * 错误处理：
 * - 管理器未初始化：返回错误信息
 * - 连接不可用：返回连接状态错误
 *
 * 使用示例：
 * ```go
 * // 获取MongoDB管理器单例
 * mongoManager := db.GetMongoDBManager()
 *
 * // 创建管理服务
 * service, err := NewMongoAdminService(mongoManager)
 * if err != nil {
 *     log.Fatal("Failed to create MongoDB service:", err)
 * }
 * defer service.Close()
 * ```
 *
 * @param {*db.MongoDBManager} mongoManager - MongoDB管理器实例
 * @returns {*MongoAdminService} 初始化完成的MongoDB管理服务实例
 * @returns {error} 初始化过程中的错误信息
 */
func NewMongoAdminService(mongoManager *db.MongoDBManager) (*MongoAdminService, error) {
	logger.Info("🗄️ Creating MongoDB Admin Service with unified manager...")

	// 检查MongoDB管理器是否可用
	if mongoManager == nil {
		return nil, fmt.Errorf("MongoDB manager is nil")
	}

	// 检查连接状态
	if !mongoManager.IsConnected() {
		return nil, fmt.Errorf("MongoDB manager is not connected")
	}

	logger.Info("✅ MongoDB Admin Service created successfully with unified manager")

	return &MongoAdminService{
		mongoManager: mongoManager,         // 使用统一的MongoDB管理器
		ctx:          context.Background(), // 创建全局上下文
	}, nil
}

// Close 关闭MongoDB连接（通过管理器）
func (s *MongoAdminService) Close() error {
	// MongoDB管理器是单例，不需要在这里关闭
	// 连接的关闭由MongoDB管理器统一管理
	logger.Info("🔌 MongoDB Admin Service closed (connection managed by MongoDB manager)")
	return nil
}

// GetDatabase 获取数据库实例
func (s *MongoAdminService) GetDatabase() *mongo.Database {
	if s.mongoManager == nil {
		logger.Error("❌ MongoDB manager is nil")
		return nil
	}
	return s.mongoManager.GetDatabase()
}

// GetClient 获取MongoDB客户端实例
func (s *MongoAdminService) GetClient() *mongo.Client {
	if s.mongoManager == nil {
		logger.Error("❌ MongoDB manager is nil")
		return nil
	}
	return s.mongoManager.GetClient()
}

// getCollection 获取指定名称的集合（内部辅助方法）
func (s *MongoAdminService) getCollection(collectionName string) (*mongo.Collection, error) {
	database := s.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("database not available")
	}
	return database.Collection(collectionName), nil
}

/**
 * 获取设备列表
 *
 * 功能：分页查询设备信息，支持多字段搜索、状态过滤、排序等功能
 *
 * 查询特性：
 * - 多字段搜索：支持设备ID、名称、型号、位置的模糊搜索
 * - 状态过滤：按设备状态（启用/禁用）进行过滤
 * - 灵活排序：支持任意字段的升序/降序排序
 * - 高效分页：基于skip/limit的分页机制
 *
 * 性能优化：
 * - 索引利用：利用设备ID、状态等字段的索引
 * - 计数优化：使用CountDocuments进行高效计数
 * - 游标管理：正确的游标生命周期管理
 * - 超时控制：10秒查询超时，避免长时间阻塞
 *
 * 搜索逻辑：
 * - 大小写不敏感：使用$regex和$options进行模糊匹配
 * - 多字段OR查询：在多个字段中搜索关键词
 * - 精确状态匹配：状态字段的精确匹配
 *
 * 分页计算：
 * - skip = (page - 1) × pageSize
 * - totalPages = ceil(total / pageSize)
 * - 支持大数据集的高效分页
 *
 * @param {*models.QueryParams} params - 查询参数对象
 *   - Search: 搜索关键词（可选）
 *   - Status: 状态过滤（可选）
 *   - Page: 页码（必需）
 *   - PageSize: 每页大小（必需）
 *   - SortBy: 排序字段（可选，默认created_at）
 *   - SortDesc: 是否降序（可选，默认false）
 * @returns {*models.ListResponse} 分页查询结果
 *   - Data: 设备列表
 *   - Total: 总记录数
 *   - Page: 当前页码
 *   - PageSize: 每页大小
 *   - TotalPages: 总页数
 * @returns {error} 查询过程中的错误信息
 *
 * @example
 * params := &models.QueryParams{
 *     Search: "FANUC",
 *     Status: "enabled",
 *     Page: 1,
 *     PageSize: 20,
 *     SortBy: "name",
 *     SortDesc: false,
 * }
 * result, err := service.GetDevices(params)
 */
func (s *MongoAdminService) GetDevices(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("devices")
	if err != nil {
		return nil, fmt.Errorf("failed to get devices collection: %v", err)
	}

	// 创建带超时的查询上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询过滤条件
	filter := bson.M{}

	// 多字段模糊搜索
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"device_id": bson.M{"$regex": params.Search, "$options": "i"}},
			{"name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"model": bson.M{"$regex": params.Search, "$options": "i"}},
			{"location": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}

	// 状态精确过滤
	if params.Status != "" {
		filter["status"] = params.Status
	}

	// 计算符合条件的总记录数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count devices: %v", err)
	}

	// 计算分页参数
	skip := (params.Page - 1) * params.PageSize

	// 构建排序条件
	var sort bson.D
	sortBy := params.SortBy
	if sortBy == "" {
		// 默认按排序字段升序排序，然后按创建时间排序
		sort = bson.D{
			{Key: "sort_order", Value: 1}, // 排序字段升序（数字小的在前）
			{Key: "created_at", Value: 1}, // 创建时间升序作为次要排序
		}
	} else {
		// 用户指定了排序字段
		sort = bson.D{{Key: sortBy, Value: 1}} // 升序
		if params.SortDesc {
			sort = bson.D{{Key: sortBy, Value: -1}} // 降序
		}
	}

	// 构建查询选项
	opts := options.Find().
		SetSort(sort).                   // 设置排序
		SetSkip(int64(skip)).            // 设置跳过记录数
		SetLimit(int64(params.PageSize)) // 设置返回记录数

	// 执行分页查询
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find devices: %v", err)
	}
	defer cursor.Close(ctx) // 确保游标正确关闭

	// 解析查询结果
	var devices []models.Device
	if err = cursor.All(ctx, &devices); err != nil {
		return nil, fmt.Errorf("failed to decode devices: %v", err)
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	// 构建响应结果
	return &models.ListResponse{
		Data:       devices,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateDevice 创建设备
func (s *MongoAdminService) CreateDevice(device *models.Device) error {
	collection, err := s.getCollection("devices")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	device.CreatedAt = time.Now()
	device.UpdatedAt = time.Now()

	_, err = collection.InsertOne(ctx, device)
	if err != nil {
		return fmt.Errorf("failed to create device: %v", err)
	}

	return nil
}

// UpdateDevice 更新设备
func (s *MongoAdminService) UpdateDevice(id string, device *models.Device) error {
	collection, err := s.getCollection("devices")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid device ID: %v", err)
	}

	device.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"device_id":        device.DeviceID,
			"name":             device.Name,
			"data_type":        device.DataType,
			"location":         device.Location,
			"status":           device.Status,
			"sort_order":       device.SortOrder,
			"brand":            device.Brand,
			"model":            device.Model,
			"ip":               device.IP,
			"port":             device.Port,
			"enabled":          device.Enabled,
			"auto_start":       device.AutoStart,
			"collect_interval": device.CollectInterval,
			"timeout":          device.Timeout,
			"retry_count":      device.RetryCount,
			"retry_delay":      device.RetryDelay,
			"description":      device.Description,
			"updated_at":       device.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update device: %v", err)
	}

	return nil
}

// DeleteDevice 删除设备
func (s *MongoAdminService) DeleteDevice(id string) error {
	collection, err := s.getCollection("devices")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid device ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete device: %v", err)
	}

	return nil
}

// GetDevice 获取单个设备（通过MongoDB ObjectID）
func (s *MongoAdminService) GetDevice(id string) (*models.Device, error) {
	collection, err := s.getCollection("devices")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid device ID: %v", err)
	}

	var device models.Device
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("device not found")
		}
		return nil, fmt.Errorf("failed to find device: %v", err)
	}

	return &device, nil
}

/**
 * 通过设备ID获取设备配置（精确匹配）
 *
 * 功能：根据业务设备ID（device_id字段）精确查找设备配置
 *
 * 与GetDevice的区别：
 * - GetDevice：通过MongoDB的ObjectID查找（用于管理界面）
 * - GetDeviceByDeviceID：通过业务设备ID查找（用于数据处理）
 *
 * 查询特点：
 * - 精确匹配：不使用正则表达式，避免模糊匹配
 * - 高效查询：直接通过索引字段查找
 * - 业务导向：适用于数据采集和API调用场景
 *
 * @param {string} deviceID - 业务设备ID，如"8e0aab17-4168-4a71-82fa-426ad2d77d83"
 * @returns {*models.Device} 设备配置信息，如果未找到则返回nil
 * @returns {error} 查询过程中的错误信息
 *
 * @example
 * device, err := service.GetDeviceByDeviceID("8e0aab17-4168-4a71-82fa-426ad2d77d83")
 * if err != nil {
 *     // 处理查询错误
 * }
 * if device == nil {
 *     // 设备不存在
 * }
 */
func (s *MongoAdminService) GetDeviceByDeviceID(deviceID string) (*models.Device, error) {
	collection, err := s.getCollection("devices")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用精确匹配查询设备ID
	filter := bson.M{"device_id": deviceID}

	var device models.Device
	err = collection.FindOne(ctx, filter).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("device not found")
		}
		return nil, fmt.Errorf("failed to find device by device_id: %v", err)
	}

	return &device, nil
}

// GetProducts 获取产品列表
func (s *MongoAdminService) GetProducts(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("products")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"product_id": bson.M{"$regex": params.Search, "$options": "i"}},
			{"name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"category": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}

	// 计算总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count products: %v", err)
	}

	// 构建排序
	sort := bson.D{{Key: params.SortBy, Value: 1}}
	if params.SortDesc {
		sort = bson.D{{Key: params.SortBy, Value: -1}}
	}

	// 分页查询
	skip := (params.Page - 1) * params.PageSize
	opts := options.Find().
		SetSort(sort).
		SetSkip(int64(skip)).
		SetLimit(int64(params.PageSize))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find products: %v", err)
	}
	defer cursor.Close(ctx)

	var products []models.Product
	if err = cursor.All(ctx, &products); err != nil {
		return nil, fmt.Errorf("failed to decode products: %v", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	return &models.ListResponse{
		Data:       products,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateProduct 创建产品
func (s *MongoAdminService) CreateProduct(product *models.Product) error {
	collection, err := s.getCollection("products")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	product.CreatedAt = time.Now()
	product.UpdatedAt = time.Now()

	_, err = collection.InsertOne(ctx, product)
	if err != nil {
		return fmt.Errorf("failed to create product: %v", err)
	}

	return nil
}

// GetProduct 获取单个产品
func (s *MongoAdminService) GetProduct(id string) (*models.Product, error) {
	collection, err := s.getCollection("products")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid product ID: %v", err)
	}

	var product models.Product
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&product)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("product not found")
		}
		return nil, fmt.Errorf("failed to find product: %v", err)
	}

	return &product, nil
}

// UpdateProduct 更新产品
func (s *MongoAdminService) UpdateProduct(id string, product *models.Product) error {
	collection, err := s.getCollection("products")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid product ID: %v", err)
	}

	product.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"product_id":     product.ProductID,
			"name":           product.Name,
			"unit":           product.Unit,
			"description":    product.Description,
			"specifications": product.Specifications,
			"updated_at":     product.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update product: %v", err)
	}

	return nil
}

// DeleteProduct 删除产品
func (s *MongoAdminService) DeleteProduct(id string) error {
	collection, err := s.getCollection("products")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid product ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete product: %v", err)
	}

	return nil
}

// GetProcesses 获取工序列表
func (s *MongoAdminService) GetProcesses(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("processes")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"process_id": bson.M{"$regex": params.Search, "$options": "i"}},
			{"name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"product_id": bson.M{"$regex": params.Search, "$options": "i"}},
			{"device_type": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}

	// 计算总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count processes: %v", err)
	}

	// 构建排序
	sort := bson.D{{Key: params.SortBy, Value: 1}}
	if params.SortDesc {
		sort = bson.D{{Key: params.SortBy, Value: -1}}
	}

	// 分页查询
	skip := (params.Page - 1) * params.PageSize
	opts := options.Find().
		SetSort(sort).
		SetSkip(int64(skip)).
		SetLimit(int64(params.PageSize))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find processes: %v", err)
	}
	defer cursor.Close(ctx)

	var processes []models.Process
	if err = cursor.All(ctx, &processes); err != nil {
		return nil, fmt.Errorf("failed to decode processes: %v", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	return &models.ListResponse{
		Data:       processes,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateProcess 创建工序
func (s *MongoAdminService) CreateProcess(process *models.Process) error {
	collection, err := s.getCollection("processes")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	process.CreatedAt = time.Now()
	process.UpdatedAt = time.Now()

	_, err = collection.InsertOne(ctx, process)
	if err != nil {
		return fmt.Errorf("failed to create process: %v", err)
	}

	return nil
}

// GetProcess 获取单个工序
func (s *MongoAdminService) GetProcess(id string) (*models.Process, error) {
	collection, err := s.getCollection("processes")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid process ID: %v", err)
	}

	var process models.Process
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&process)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("process not found")
		}
		return nil, fmt.Errorf("failed to find process: %v", err)
	}

	return &process, nil
}

// UpdateProcess 更新工序
func (s *MongoAdminService) UpdateProcess(id string, process *models.Process) error {
	collection, err := s.getCollection("processes")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid process ID: %v", err)
	}

	process.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"process_id":       process.ProcessID,
			"name":             process.Name,
			"product_id":       process.ProductID,
			"duration":         process.Duration,
			"preparation_time": process.PreparationTime, // 添加准备时间字段
			"sequence":         process.Sequence,
			"description":      process.Description,
			"updated_at":       process.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update process: %v", err)
	}

	return nil
}

// DeleteProcess 删除工序
func (s *MongoAdminService) DeleteProcess(id string) error {
	collection, err := s.getCollection("processes")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid process ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete process: %v", err)
	}

	return nil
}

// ===== Settings 管理方法 =====

// GetSettings 获取系统设置
func (s *MongoAdminService) GetSettings(settingsType string) (*models.SystemSettings, error) {
	collection, err := s.getCollection("settings")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var settings models.SystemSettings
	err = collection.FindOne(ctx, bson.M{"type": settingsType}).Decode(&settings)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// 如果没有找到设置，返回 nil 而不是错误
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get settings: %v", err)
	}

	return &settings, nil
}

// SaveSettings 保存系统设置
func (s *MongoAdminService) SaveSettings(settingsType string, settingsData interface{}) error {
	collection, err := s.getCollection("settings")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用 upsert 操作：如果存在则更新，不存在则创建
	filter := bson.M{"type": settingsType}
	update := bson.M{
		"$set": bson.M{
			"settings":   settingsData,
			"updated_at": time.Now(),
		},
		"$setOnInsert": bson.M{
			"type":       settingsType,
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err = collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to save settings: %v", err)
	}

	logger.Infof("✅ Settings saved successfully: type=%s", settingsType)
	return nil
}

// InitializeData 初始化示例数据
func (s *MongoAdminService) InitializeData() error {
	logger.Info("🔄 Initializing sample data...")

	// 清空现有数据（可选）
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 初始化设备数据
	devices := []models.Device{
		{
			DeviceID:        "FANUC_001",
			Name:            "FANUC机器人#1",
			DataType:        "fanuc_30i",
			Location:        "生产线A-工位1",
			Status:          models.DeviceStatusActive,
			Brand:           "FANUC",
			Model:           "R-2000iC/165F",
			IP:              "**************",
			Port:            8193,
			Enabled:         true,
			AutoStart:       true,
			CollectInterval: 3000,
			Timeout:         10,
			RetryCount:      3,
			RetryDelay:      15,
			Description:     "六轴工业机器人，用于焊接作业",
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			DeviceID:        "FANUC_002",
			Name:            "FANUC机器人#2",
			DataType:        "fanuc_30i",
			Location:        "生产线A-工位2",
			Status:          models.DeviceStatusActive,
			Brand:           "FANUC",
			Model:           "R-2000iC/165F",
			IP:              "**************",
			Port:            8194,
			Enabled:         true,
			AutoStart:       false,
			CollectInterval: 2500,
			Timeout:         8,
			RetryCount:      2,
			RetryDelay:      10,
			Description:     "六轴工业机器人，用于装配作业",
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			DeviceID:        "CNC_001",
			Name:            "数控机床#1",
			DataType:        "siemens_840d",
			Location:        "加工车间B-01",
			Status:          models.DeviceStatusActive,
			Brand:           "Siemens",
			Model:           "SINUMERIK 840D sl",
			IP:              "**************",
			Port:            8195,
			Enabled:         true,
			AutoStart:       true,
			CollectInterval: 5000,
			Timeout:         15,
			RetryCount:      5,
			RetryDelay:      20,
			Description:     "高精度数控车床，用于精密加工",
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			DeviceID:        "LASER_001",
			Name:            "激光切割机#1",
			DataType:        "plc_modbus",
			Location:        "切割车间C-01",
			Status:          models.DeviceStatusMaintenance,
			Brand:           "Trumpf",
			Model:           "TruLaser 3030",
			IP:              "**************",
			Port:            8196,
			Enabled:         false,
			AutoStart:       false,
			CollectInterval: 4000,
			Timeout:         12,
			RetryCount:      3,
			RetryDelay:      25,
			Description:     "高功率激光切割设备，用于金属板材切割",
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			DeviceID:        "PRESS_001",
			Name:            "冲压机#1",
			DataType:        "sensor_tcp",
			Location:        "冲压车间D-01",
			Status:          models.DeviceStatusInactive,
			Brand:           "Komatsu",
			Model:           "H1F-200",
			IP:              "**************",
			Port:            8197,
			Enabled:         false,
			AutoStart:       false,
			CollectInterval: 1000,
			Timeout:         5,
			RetryCount:      1,
			RetryDelay:      5,
			Description:     "高速冲压机，用于金属成型",
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
	}

	// 插入设备数据
	deviceCollection, err := s.getCollection("devices")
	if err != nil {
		return fmt.Errorf("failed to get devices collection: %v", err)
	}
	for _, device := range devices {
		_, err = deviceCollection.InsertOne(ctx, device)
		if err != nil {
			logger.Errorf("Failed to insert device %s: %v", device.DeviceID, err)
		}
	}

	// 初始化产品数据
	products := []models.Product{
		{
			ProductID:   "PROD_AUTO_001",
			Name:        "汽车发动机缸体",
			Unit:        "件",
			Description: "高精度铝合金发动机缸体，适用于2.0L涡轮增压发动机",
			Specifications: map[string]interface{}{
				"材质":   "铝合金A356",
				"重量":   "25.5kg",
				"尺寸":   "450×350×280mm",
				"精度":   "±0.05mm",
				"表面处理": "阳极氧化",
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ProductID:   "PROD_ELEC_001",
			Name:        "电子控制器外壳",
			Unit:        "件",
			Description: "工业级电子控制器铝合金外壳，具有良好的散热性能",
			Specifications: map[string]interface{}{
				"材质":   "铝合金6061",
				"重量":   "1.2kg",
				"尺寸":   "200×150×80mm",
				"防护等级": "IP65",
				"颜色":   "银色",
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ProductID:   "PROD_MECH_001",
			Name:        "精密齿轮",
			Unit:        "件",
			Description: "高精度传动齿轮，用于工业减速器",
			Specifications: map[string]interface{}{
				"材质":   "合金钢40Cr",
				"重量":   "0.8kg",
				"模数":   "2.5",
				"齿数":   "48",
				"精度等级": "DIN 6级",
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	// 插入产品数据
	productCollection, err := s.getCollection("products")
	if err != nil {
		return fmt.Errorf("failed to get products collection: %v", err)
	}
	for _, product := range products {
		_, err = productCollection.InsertOne(ctx, product)
		if err != nil {
			logger.Errorf("Failed to insert product %s: %v", product.ProductID, err)
		}
	}

	// 初始化工序数据
	processes := []models.Process{
		{
			ProcessID:   "PROC_001",
			Name:        "粗加工",
			ProductID:   "PROD_AUTO_001",
			Duration:    45,
			Sequence:    1,
			Description: "发动机缸体粗加工，去除多余材料",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ProcessID:   "PROC_002",
			Name:        "精加工",
			ProductID:   "PROD_AUTO_001",
			Duration:    60,
			Sequence:    2,
			Description: "发动机缸体精加工，达到设计精度要求",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ProcessID:   "PROC_003",
			Name:        "激光切割",
			ProductID:   "PROD_ELEC_001",
			Duration:    15,
			Sequence:    1,
			Description: "控制器外壳激光切割成型",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ProcessID:   "PROC_004",
			Name:        "折弯成型",
			ProductID:   "PROD_ELEC_001",
			Duration:    10,
			Sequence:    2,
			Description: "控制器外壳折弯成型",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ProcessID:   "PROC_005",
			Name:        "齿轮切削",
			ProductID:   "PROD_MECH_001",
			Duration:    30,
			Sequence:    1,
			Description: "精密齿轮切削加工",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ProcessID:   "PROC_006",
			Name:        "热处理",
			ProductID:   "PROD_MECH_001",
			Duration:    120,
			Sequence:    2,
			Description: "齿轮热处理，提高硬度和耐磨性",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// 插入工序数据
	processCollection, err := s.getCollection("processes")
	if err != nil {
		return fmt.Errorf("failed to get processes collection: %v", err)
	}
	for _, process := range processes {
		_, err = processCollection.InsertOne(ctx, process)
		if err != nil {
			logger.Errorf("Failed to insert process %s: %v", process.ProcessID, err)
		}
	}

	logger.Info("✅ Sample data initialization completed")
	return nil
}

// ClearAllData 清空所有数据
func (s *MongoAdminService) ClearAllData() error {
	logger.Info("🗑️ Clearing all data...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 清空设备数据
	deviceCollection, err := s.getCollection("devices")
	if err != nil {
		return fmt.Errorf("failed to get devices collection: %v", err)
	}
	_, err = deviceCollection.DeleteMany(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to clear devices: %v", err)
	}

	// 清空产品数据
	productCollection, err := s.getCollection("products")
	if err != nil {
		return fmt.Errorf("failed to get products collection: %v", err)
	}
	_, err = productCollection.DeleteMany(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to clear products: %v", err)
	}

	// 清空工序数据
	processCollection, err := s.getCollection("processes")
	if err != nil {
		return fmt.Errorf("failed to get processes collection: %v", err)
	}
	_, err = processCollection.DeleteMany(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to clear processes: %v", err)
	}

	logger.Info("✅ All data cleared successfully")
	return nil
}

// ResetData 重置数据（清空后重新初始化）
func (s *MongoAdminService) ResetData() error {
	logger.Info("🔄 Resetting data...")

	// 先清空所有数据
	err := s.ClearAllData()
	if err != nil {
		return fmt.Errorf("failed to clear data: %v", err)
	}

	// 重新初始化示例数据
	err = s.InitializeData()
	if err != nil {
		return fmt.Errorf("failed to initialize data: %v", err)
	}

	logger.Info("✅ Data reset completed")
	return nil
}

// GetStatistics 获取数据统计信息
func (s *MongoAdminService) GetStatistics() (map[string]interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stats := make(map[string]interface{})

	// 设备统计
	deviceCollection, err := s.getCollection("devices")
	if err != nil {
		return nil, fmt.Errorf("failed to get devices collection: %v", err)
	}
	deviceTotal, err := deviceCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to count devices: %v", err)
	}

	// 按状态统计设备
	deviceStatusStats := make(map[string]int64)
	statusPipeline := []bson.M{
		{"$group": bson.M{
			"_id":   "$status",
			"count": bson.M{"$sum": 1},
		}},
	}
	cursor, err := deviceCollection.Aggregate(ctx, statusPipeline)
	if err == nil {
		defer cursor.Close(ctx)
		for cursor.Next(ctx) {
			var result struct {
				ID    string `bson:"_id"`
				Count int64  `bson:"count"`
			}
			if err := cursor.Decode(&result); err == nil {
				deviceStatusStats[result.ID] = result.Count
			}
		}
	}

	// 产品统计
	productCollection, err := s.getCollection("products")
	if err != nil {
		return nil, fmt.Errorf("failed to get products collection: %v", err)
	}
	productTotal, err := productCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to count products: %v", err)
	}

	// 工序统计
	processCollection, err := s.getCollection("processes")
	if err != nil {
		return nil, fmt.Errorf("failed to get processes collection: %v", err)
	}
	processTotal, err := processCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to count processes: %v", err)
	}

	// 工序平均时长
	avgDurationPipeline := []bson.M{
		{"$group": bson.M{
			"_id":           nil,
			"avgDuration":   bson.M{"$avg": "$duration"},
			"totalDuration": bson.M{"$sum": "$duration"},
		}},
	}
	cursor, err = processCollection.Aggregate(ctx, avgDurationPipeline)
	var avgDuration, totalDuration float64
	if err == nil {
		defer cursor.Close(ctx)
		if cursor.Next(ctx) {
			var result struct {
				AvgDuration   float64 `bson:"avgDuration"`
				TotalDuration float64 `bson:"totalDuration"`
			}
			if err := cursor.Decode(&result); err == nil {
				avgDuration = result.AvgDuration
				totalDuration = result.TotalDuration
			}
		}
	}

	// 组装统计结果
	stats["devices"] = map[string]interface{}{
		"total":    deviceTotal,
		"byStatus": deviceStatusStats,
	}
	stats["products"] = map[string]interface{}{
		"total": productTotal,
	}
	stats["processes"] = map[string]interface{}{
		"total":         processTotal,
		"avgDuration":   avgDuration,
		"totalDuration": totalDuration,
	}
	stats["summary"] = map[string]interface{}{
		"totalDevices":   deviceTotal,
		"totalProducts":  productTotal,
		"totalProcesses": processTotal,
		"lastUpdated":    time.Now(),
	}

	return stats, nil
}

// ==================== 设备状态历史数据管理 ====================

// GetDeviceStatusHistory 从MongoDB获取设备状态历史数据
func (s *MongoAdminService) GetDeviceStatusHistory(deviceID, date string) ([]models.DeviceStatusHistory, error) {
	collection, err := s.getCollection("device_status_history")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 查询条件
	filter := bson.M{
		"device_id": deviceID,
		"date":      date,
	}

	var historyDoc models.DeviceStatusHistoryMongo
	err = collection.FindOne(ctx, filter).Decode(&historyDoc)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 没有找到记录，返回nil
		}
		return nil, fmt.Errorf("failed to find device status history: %v", err)
	}

	logger.Debugf("📊 Retrieved %d status history records from MongoDB for %s:%s", len(historyDoc.StatusHistory), deviceID, date)
	return historyDoc.StatusHistory, nil
}

// GetDeviceStatusHistoryByTimeRange 按时间范围获取设备状态历史数据
func (s *MongoAdminService) GetDeviceStatusHistoryByTimeRange(deviceID string, startDateTime, endDateTime time.Time) ([]models.DeviceStatusHistory, error) {
	collection, err := s.getCollection("device_status_history")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	logger.Debugf("🔍 查询MongoDB设备状态历史: 设备=%s, 时间范围=%s到%s",
		deviceID,
		startDateTime.Format(time.RFC3339),
		endDateTime.Format(time.RFC3339))

	// 计算需要查询的日期范围
	startDate := startDateTime.Format("2006-01-02")
	endDate := endDateTime.Format("2006-01-02")

	// 构建日期范围查询条件
	var dateFilter bson.M
	if startDate == endDate {
		// 同一天
		dateFilter = bson.M{"date": startDate}
	} else {
		// 跨日查询
		dateFilter = bson.M{
			"date": bson.M{
				"$gte": startDate,
				"$lte": endDate,
			},
		}
	}

	// 查询条件：设备ID + 日期范围
	filter := bson.M{
		"device_id": deviceID,
		"$and": []bson.M{
			dateFilter,
		},
	}

	// 执行查询
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("查询设备状态历史失败: %v", err)
	}
	defer cursor.Close(ctx)

	var allStatusHistory []models.DeviceStatusHistory

	// 遍历查询结果
	for cursor.Next(ctx) {
		var historyDoc models.DeviceStatusHistoryMongo
		if err := cursor.Decode(&historyDoc); err != nil {
			logger.Warnf("⚠️ 解码状态历史文档失败: %v", err)
			continue
		}

		// 筛选时间范围内的状态记录
		for _, status := range historyDoc.StatusHistory {
			// status.Timestamp 已经是 time.Time 类型，直接使用
			timestamp := status.Timestamp

			// 检查是否在时间范围内
			if (timestamp.Equal(startDateTime) || timestamp.After(startDateTime)) &&
				(timestamp.Equal(endDateTime) || timestamp.Before(endDateTime)) {
				allStatusHistory = append(allStatusHistory, status)
			}
		}
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("遍历查询结果失败: %v", err)
	}

	// 按时间戳排序（Timestamp 已经是 time.Time 类型，直接比较）
	sort.Slice(allStatusHistory, func(i, j int) bool {
		return allStatusHistory[i].Timestamp.Before(allStatusHistory[j].Timestamp)
	})

	logger.Debugf("📊 从MongoDB获取到 %d 条时间范围内的状态历史记录 %s", len(allStatusHistory), deviceID)
	return allStatusHistory, nil
}

// SaveDeviceStatusHistory 保存设备状态历史数据到MongoDB
func (s *MongoAdminService) SaveDeviceStatusHistory(deviceID, date string, statusHistory []models.DeviceStatusHistory) error {
	collection, err := s.getCollection("device_status_history")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 创建或更新文档
	historyDoc := models.DeviceStatusHistoryMongo{
		DeviceID:      deviceID,
		Date:          date,
		StatusHistory: statusHistory,
		UpdatedAt:     time.Now(),
	}

	// 使用upsert操作，如果不存在则创建，存在则更新
	filter := bson.M{
		"device_id": deviceID,
		"date":      date,
	}

	update := bson.M{
		"$set": bson.M{
			"device_id":      historyDoc.DeviceID,
			"date":           historyDoc.Date,
			"status_history": historyDoc.StatusHistory,
			"updated_at":     historyDoc.UpdatedAt,
		},
		"$setOnInsert": bson.M{
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	result, err := collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to save device status history: %v", err)
	}

	if result.UpsertedCount > 0 {
		logger.Debugf("✅ Created new status history document for %s:%s with %d records", deviceID, date, len(statusHistory))
	} else {
		logger.Debugf("✅ Updated status history document for %s:%s with %d records", deviceID, date, len(statusHistory))
	}

	return nil
}

// DeleteDeviceStatusHistory 删除设备状态历史数据
// 🔧 新增：支持手动重构历史数据时删除旧数据
func (s *MongoAdminService) DeleteDeviceStatusHistory(deviceID, date string) error {
	collection, err := s.getCollection("device_status_history")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 删除条件
	filter := bson.M{
		"device_id": deviceID,
		"date":      date,
	}

	result, err := collection.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete device status history: %v", err)
	}

	if result.DeletedCount > 0 {
		logger.Debugf("🗑️ Deleted status history document for %s:%s", deviceID, date)
	} else {
		logger.Debugf("📊 No status history document found to delete for %s:%s", deviceID, date)
	}

	return nil
}

// GetAllDeviceIDs 获取所有设备ID列表（用于定时任务）
func (s *MongoAdminService) GetAllDeviceIDs() ([]string, error) {
	collection, err := s.getCollection("devices")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 只查询device_id字段
	opts := options.Find().SetProjection(bson.M{"device_id": 1, "_id": 0})
	cursor, err := collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find devices: %v", err)
	}
	defer cursor.Close(ctx)

	var devices []struct {
		DeviceID string `bson:"device_id"`
	}
	if err = cursor.All(ctx, &devices); err != nil {
		return nil, fmt.Errorf("failed to decode devices: %v", err)
	}

	deviceIDs := make([]string, len(devices))
	for i, device := range devices {
		deviceIDs[i] = device.DeviceID
	}

	logger.Debugf("📋 Found %d devices for status history processing", len(deviceIDs))
	return deviceIDs, nil
}

// ===== 计量单位管理方法 =====

/**
 * 获取计量单位列表
 *
 * 功能：分页查询计量单位信息，支持多字段搜索、类别过滤、排序等功能
 *
 * 查询特性：
 * - 多字段搜索：支持单位名称、符号、描述的模糊搜索
 * - 类别过滤：按单位类别（count、weight、length等）进行过滤
 * - 灵活排序：支持任意字段的升序/降序排序
 * - 高效分页：基于skip/limit的分页机制
 *
 * 性能优化：
 * - 索引利用：利用单位名称、类别等字段的索引
 * - 计数优化：使用CountDocuments进行高效计数
 * - 游标管理：正确的游标生命周期管理
 * - 超时控制：10秒查询超时，避免长时间阻塞
 *
 * @param {*models.QueryParams} params - 查询参数对象
 * @returns {*models.ListResponse} 分页查询结果
 * @returns {error} 查询过程中的错误信息
 */
func (s *MongoAdminService) GetUnits(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("units")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	// 创建带超时的查询上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询过滤条件
	filter := bson.M{}

	// 多字段模糊搜索
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"symbol": bson.M{"$regex": params.Search, "$options": "i"}},
			{"description": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}

	// 类别精确过滤（使用Status字段作为Category的别名）
	if params.Status != "" {
		filter["category"] = params.Status
	}

	// 计算符合条件的总记录数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count units: %v", err)
	}

	// 计算分页参数
	skip := (params.Page - 1) * params.PageSize

	// 构建排序条件
	var sort bson.D
	sortBy := params.SortBy
	if sortBy == "" {
		// 默认按排序字段升序排序，然后按创建时间排序
		sort = bson.D{
			{Key: "sort_order", Value: 1}, // 排序字段升序（数字小的在前）
			{Key: "created_at", Value: 1}, // 创建时间升序作为次要排序
		}
	} else {
		// 用户指定了排序字段
		sort = bson.D{{Key: sortBy, Value: 1}} // 升序
		if params.SortDesc {
			sort = bson.D{{Key: sortBy, Value: -1}} // 降序
		}
	}

	// 构建查询选项
	opts := options.Find().
		SetSort(sort).                   // 设置排序
		SetSkip(int64(skip)).            // 设置跳过记录数
		SetLimit(int64(params.PageSize)) // 设置返回记录数

	// 执行分页查询
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find units: %v", err)
	}
	defer cursor.Close(ctx) // 确保游标正确关闭

	// 解析查询结果
	var units []models.Unit
	if err = cursor.All(ctx, &units); err != nil {
		return nil, fmt.Errorf("failed to decode units: %v", err)
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	// 构建响应结果
	return &models.ListResponse{
		Data:       units,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

/**
 * 创建计量单位
 *
 * 功能：在数据库中创建新的计量单位记录
 *
 * 业务逻辑：
 * - 自动设置创建时间和更新时间
 * - 如果设置为默认单位，需要取消其他默认单位
 * - 验证单位名称和符号的唯一性
 *
 * @param {*models.Unit} unit - 计量单位对象
 * @returns {error} 创建过程中的错误信息
 */
func (s *MongoAdminService) CreateUnit(unit *models.Unit) error {
	collection, err := s.getCollection("units")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 设置时间戳
	unit.CreatedAt = time.Now()
	unit.UpdatedAt = time.Now()

	// 如果设置为默认单位，先取消其他默认单位
	if unit.IsDefault {
		err := s.clearDefaultUnits(ctx)
		if err != nil {
			return fmt.Errorf("failed to clear existing default units: %v", err)
		}
	}

	_, err = collection.InsertOne(ctx, unit)
	if err != nil {
		return fmt.Errorf("failed to create unit: %v", err)
	}

	return nil
}

/**
 * 获取单个计量单位
 *
 * 功能：根据MongoDB ObjectID获取计量单位详情
 *
 * @param {string} id - MongoDB ObjectID
 * @returns {*models.Unit} 计量单位对象
 * @returns {error} 查询过程中的错误信息
 */
func (s *MongoAdminService) GetUnit(id string) (*models.Unit, error) {
	collection, err := s.getCollection("units")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid unit ID: %v", err)
	}

	var unit models.Unit
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&unit)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("unit not found")
		}
		return nil, fmt.Errorf("failed to find unit: %v", err)
	}

	return &unit, nil
}

/**
 * 更新计量单位
 *
 * 功能：更新现有计量单位的信息
 *
 * @param {string} id - MongoDB ObjectID
 * @param {*models.Unit} unit - 更新的计量单位对象
 * @returns {error} 更新过程中的错误信息
 */
func (s *MongoAdminService) UpdateUnit(id string, unit *models.Unit) error {
	collection, err := s.getCollection("units")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid unit ID: %v", err)
	}

	// 设置更新时间
	unit.UpdatedAt = time.Now()

	// 如果设置为默认单位，先取消其他默认单位
	if unit.IsDefault {
		err := s.clearDefaultUnits(ctx)
		if err != nil {
			return fmt.Errorf("failed to clear existing default units: %v", err)
		}
	}

	update := bson.M{
		"$set": bson.M{
			"name":        unit.Name,
			"symbol":      unit.Symbol,
			"category":    unit.Category,
			"description": unit.Description,
			"is_default":  unit.IsDefault,
			"sort_order":  unit.SortOrder,
			"updated_at":  unit.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update unit: %v", err)
	}

	return nil
}

/**
 * 删除计量单位
 *
 * 功能：从数据库中删除指定的计量单位
 *
 * @param {string} id - MongoDB ObjectID
 * @returns {error} 删除过程中的错误信息
 */
func (s *MongoAdminService) DeleteUnit(id string) error {
	collection, err := s.getCollection("units")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid unit ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete unit: %v", err)
	}

	return nil
}

/**
 * 批量删除计量单位
 *
 * 功能：批量删除多个计量单位
 *
 * @param {[]string} ids - MongoDB ObjectID数组
 * @returns {error} 删除过程中的错误信息
 */
func (s *MongoAdminService) BatchDeleteUnits(ids []string) error {
	collection, err := s.getCollection("units")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 转换字符串ID为ObjectID
	objectIDs := make([]primitive.ObjectID, len(ids))
	for i, id := range ids {
		objectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return fmt.Errorf("invalid unit ID %s: %v", id, err)
		}
		objectIDs[i] = objectID
	}

	// 批量删除
	filter := bson.M{"_id": bson.M{"$in": objectIDs}}
	_, err = collection.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to batch delete units: %v", err)
	}

	return nil
}

/**
 * 设置默认计量单位
 *
 * 功能：将指定计量单位设置为默认单位，同时取消其他默认单位
 *
 * @param {string} id - MongoDB ObjectID
 * @returns {error} 设置过程中的错误信息
 */
func (s *MongoAdminService) SetDefaultUnit(id string) error {
	collection, err := s.getCollection("units")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid unit ID: %v", err)
	}

	// 先取消所有默认单位
	err = s.clearDefaultUnits(ctx)
	if err != nil {
		return fmt.Errorf("failed to clear existing default units: %v", err)
	}

	// 设置指定单位为默认
	update := bson.M{
		"$set": bson.M{
			"is_default": true,
			"updated_at": time.Now(),
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to set default unit: %v", err)
	}

	return nil
}

/**
 * 清除所有默认计量单位
 *
 * 功能：将所有计量单位的is_default字段设置为false
 * 这是一个内部辅助方法，用于确保只有一个默认单位
 *
 * @param {context.Context} ctx - 上下文对象
 * @returns {error} 操作过程中的错误信息
 */
func (s *MongoAdminService) clearDefaultUnits(ctx context.Context) error {
	collection, err := s.getCollection("units")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	// 将所有单位的is_default设置为false
	update := bson.M{
		"$set": bson.M{
			"is_default": false,
			"updated_at": time.Now(),
		},
	}

	_, err = collection.UpdateMany(ctx, bson.M{}, update)
	if err != nil {
		return fmt.Errorf("failed to clear default units: %v", err)
	}

	return nil
}
