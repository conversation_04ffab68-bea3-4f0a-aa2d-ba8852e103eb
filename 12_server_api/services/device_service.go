package services

import (
	"fmt"
	"server-api/models"
	"server-api/utils"
	"shared/logger"
	"time"
)

// GetDeviceStatusHistoryByDate 获取设备状态历史数据（按日期查询）
func (ds *DataService) GetDeviceStatusHistoryByDate(deviceId, date string, workTimeSettings *models.WorkTimeSettings) ([]models.DeviceStatusHistory, error) {

	// TODO: 检查 redis 是否存在, 如果存在直接返回 redis 的数据
	// TODO: key = "status_history_" + device_id + date + start_time

	// 获取设备状态历史数据
	var statusHistory []models.DeviceStatusHistory

	// date, workTimeSettings.DayStartTime
	dateRange, _ := utils.CalculateQueryDateRange(date, workTimeSettings.DayStartTime)

	if dateRange.Type == utils.QueryDateTypeMixed {
		logger.Debugf("📅 Query date range is cross-day: %s to %s",
			dateRange.StartDateTime.Format(time.RFC3339),
			dateRange.EndDateTime.Format(time.RFC3339))

		// 跨日查询：分割时间范围
		ranges, err := utils.SplitCrossDayTimeRange(dateRange.StartDateTime, dateRange.EndDateTime)
		if err != nil {
			return []models.DeviceStatusHistory{}, err
		}

		logger.Debugf("🔄 跨日查询分割结果: 第一组=%s到%s, 第二组=%s到%s",
			ranges.FirstGroup.StartDateTime.Format(time.RFC3339),
			ranges.FirstGroup.EndDateTime.Format(time.RFC3339),
			ranges.SecondGroup.StartDateTime.Format(time.RFC3339),
			ranges.SecondGroup.EndDateTime.Format(time.RFC3339))

		// 检查是否包含了今天
		if dateRange.ContainsToday {
			logger.Debugf("📅 Query date range contains today: %v", dateRange.ContainsToday)
			// 包含了今天, 查历史记录和今天记录

			// 查询第一组数据（历史数据，从MongoDB）
			firstGroupData, err := ds.GetDeviceStatusHistoryByTimeRange(
				deviceId,
				ranges.FirstGroup.StartDateTime,
				ranges.FirstGroup.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get first group data from MongoDB: %v", err)
				firstGroupData = []models.DeviceStatusHistory{}
			}

			// 查询第二组数据（今天数据，从InfluxDB）
			secondGroupData, err := ds.GetDeviceStatusHistoryByTimeRange(
				deviceId,
				ranges.SecondGroup.StartDateTime,
				ranges.SecondGroup.EndDateTime,
				"influxdb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get second group data from InfluxDB: %v", err)
				secondGroupData = []models.DeviceStatusHistory{}
			}

			// 合并两个时间段的数据
			statusHistory = append(firstGroupData, secondGroupData...)
			logger.Debugf("📊 跨日查询合并数据: 第一组%d条, 第二组%d条, 总计%d条",
				len(firstGroupData), len(secondGroupData), len(statusHistory))
		} else {
			// 不包含今天, 查历史记录

			// 查询第一组数据（历史数据，从MongoDB）
			firstGroupData, err := ds.GetDeviceStatusHistoryByTimeRange(
				deviceId,
				ranges.FirstGroup.StartDateTime,
				ranges.FirstGroup.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get first group data from MongoDB: %v", err)
				firstGroupData = []models.DeviceStatusHistory{}
			}

			// 查询第二组数据（历史数据，从MongoDB）
			secondGroupData, err := ds.GetDeviceStatusHistoryByTimeRange(
				deviceId,
				ranges.SecondGroup.StartDateTime,
				ranges.SecondGroup.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Warnf("⚠️ Failed to get second group data from MongoDB: %v", err)
				secondGroupData = []models.DeviceStatusHistory{}
			}

			// 合并两个时间段的数据
			statusHistory = append(firstGroupData, secondGroupData...)
			logger.Debugf("📊 跨日历史查询合并数据: 第一组%d条, 第二组%d条, 总计%d条",
				len(firstGroupData), len(secondGroupData), len(statusHistory))
		}

	} else if dateRange.Type == utils.QueryDateTypeOneDay {
		logger.Debugf("📅 Query date range is within one day: %s to %s",
			dateRange.StartDateTime.Format(time.RFC3339),
			dateRange.EndDateTime.Format(time.RFC3339))

		// 检查是否包含了今天
		if dateRange.ContainsToday {
			logger.Debugf("📅 Query date range contains today: %v", dateRange.ContainsToday)
			// 包含了今天, 查今天记录

			var err error
			// 从InfluxDB查询今天的数据
			statusHistory, err = ds.GetDeviceStatusHistoryByTimeRange(
				deviceId,
				dateRange.StartDateTime,
				dateRange.EndDateTime,
				"influxdb",
			)
			if err != nil {

				return []models.DeviceStatusHistory{}, err
			}
			logger.Debugf("📊 单日查询获取到 %d 条状态历史记录（InfluxDB）", len(statusHistory))
		} else {
			// 不包含今天, 查历史记录
			var err error
			// 从MongoDB查询历史数据
			statusHistory, err = ds.GetDeviceStatusHistoryByTimeRange(
				deviceId,
				dateRange.StartDateTime,
				dateRange.EndDateTime,
				"mongodb",
			)
			if err != nil {
				logger.Errorf("❌ Failed to get device status history from MongoDB: %v", err)

				return []models.DeviceStatusHistory{}, err
			}
			logger.Debugf("📊 单日查询获取到 %d 条状态历史记录（MongoDB）", len(statusHistory))
		}
	}

	// TODO： 将数据保存到 redis
	// TODO: key = "status_history_" + device_id + date + start_time
	// TODO: 将数据保存到 redis, 过期时间 48小时 + 随机 10 - 60 分钟
	return statusHistory, nil
}

// GenerateTimeSlotDataWithPreprocessing 生成时间段数据并预处理状态记录
func (ds *DataService) GenerateTimeSlotDataWithPreprocessing(statusHistory []models.DeviceStatusHistory, workTimeSettings *models.WorkTimeSettings, dateStr string) []models.TimeSlotData {
	// 动态获取工作开始时间，优先使用配置中的"每天开始时间"
	dayStartTime := "08:00" // 默认8:00开始，与generateDefaultTimeSlots()保持一致
	if workTimeSettings != nil && workTimeSettings.DayStartTime != "" {
		dayStartTime = workTimeSettings.DayStartTime
		logger.Debugf("📅 使用配置的每天开始时间: %s", dayStartTime)
	} else {
		logger.Debugf("📅 使用默认的每天开始时间: %s", dayStartTime)
	}

	// 获取显示设置中的过滤信号时长
	var filterSignalDuration int64 = 1 // 默认1秒

	// 通过DataService获取显示设置

	dbSettings, err := ds.GetDisplaySettings()
	if err == nil && dbSettings != nil {
		settingsData := dbSettings.Settings
		if duration, exists := settingsData["filtersignalduration"]; exists {
			filterSignalDuration = duration.(int64)
			logger.Debugf("🔧 显示设置中未找到过滤信号时长配置，使用默认值: %d秒", filterSignalDuration)
		}
	} else {
		logger.Debugf("🔧 获取显示设置失败，使用默认过滤信号时长: %d秒 (错误: %v)", filterSignalDuration, err)
	}

	// 生成12个时间段
	timeSlots := ds.generateTimeSlotsFromWorkTime(dayStartTime, dateStr)

	// 初始化时间段数据
	timeSlotDataList := make([]models.TimeSlotData, len(timeSlots))
	for i, slot := range timeSlots {
		timeSlotDataList[i] = models.TimeSlotData{
			Label:      slot["label"],
			Start:      slot["start"],       // 保持向后兼容
			End:        slot["end"],         // 保持向后兼容
			StartLocal: slot["start_local"], // 本地时区开始时间
			EndLocal:   slot["end_local"],   // 本地时区结束时间
			StartUTC:   slot["start_utc"],   // UTC开始时间
			EndUTC:     slot["end_utc"],     // UTC结束时间
			Records:    []models.TimeSlotStatusRecord{},
		}
	}

	// 如果没有状态历史数据，返回空的时间段
	if len(statusHistory) == 0 {
		return timeSlotDataList
	}

	// 验证日期格式
	_, err = time.Parse("2006-01-02", dateStr)
	if err != nil {
		logger.Errorf("❌ Failed to parse date %s: %v", dateStr, err)
		return timeSlotDataList
	}

	// 处理每个状态记录
	for idx, history := range statusHistory {

		if idx > 0 {
			history.PrevStartTime = statusHistory[idx-1].StartTime
			history.PrevEndTime = statusHistory[idx-1].EndTime
		}

		// 分配到对应的时间段（优化版本：直接使用history中的时间信息）
		ds.assignStatusToTimeSlots(&timeSlotDataList, history, filterSignalDuration)
	}

	// 计算每个时间段内状态条的位置和宽度
	for i := range timeSlotDataList {
		ds.calculateStatusBarPositions(&timeSlotDataList[i])
	}

	return timeSlotDataList
}

/**
 * GenerateUtilizationData 统计利用率
 *
 * 功能：根据设备状态历史数据和工作时间配置，计算设备利用率统计信息
 *
 * 计算内容：
 * - 各状态持续时长统计（生产、空闲、故障、停机）
 * - 运行时间（生产时间）
 * - 总时间（所有状态时间之和）
 * - 利用率百分比（运行时间/总时间*100）
 * - 有效工作时长（基于工作时间配置）
 *
 * @param {string} date - 查询日期，格式：YYYY-MM-DD
 * @param {[]models.DeviceStatusHistory} statusHistory - 设备状态历史数据
 * @param {*models.WorkTimeSettings} workTimeSettings - 工作时间配置
 * @returns {models.UtilizationData} 利用率统计数据
 */
func (ds *DataService) GenerateUtilizationData(date string, statusHistory []models.DeviceStatusHistory, workTimeSettings *models.WorkTimeSettings) models.UtilizationData {

	// TODO: 检查不是当天
	// TODO: 检查 redis 是否存在, 存在即直接返回 redis 数据
	// TODO: key = "utilization_"  + date + “worktime settings start_time"

	// 初始化结果结构，预先创建所有可能的状态键
	result := models.UtilizationData{
		UseWorkTime: workTimeSettings.UtilizationMode == "OP2", // OP2模式使用工作时间
		WorkTime:    0,
		Utilization: 0.0,
		RunningTime: 0,
		TotalTime:   0,
		StatusSummary: map[string]int64{
			"production": 0, // 生产时间（秒）
			"idle":       0, // 空闲时间（秒）
			"fault":      0, // 故障时间（秒）
			"shutdown":   0, // 停机时间（秒）
			"unknown":    0, // 未知状态时间（秒）
		},
	}

	logger.Debugf("📊 开始计算利用率统计: 日期=%s, 状态记录数=%d, 利用率模式=%s",
		date, len(statusHistory), workTimeSettings.UtilizationMode)

	// 计算各状态持续时长
	for _, history := range statusHistory {
		// 映射状态到标准状态名称
		status := ds.mapDeviceStatus(history.CurrentStatus)

		// 累加状态持续时长
		result.StatusSummary[status] += history.Duration

		logger.Debugf("📊 状态统计: 设备=%s, 原始状态=%s, 映射状态=%s, 持续时长=%d秒",
			history.DeviceID, history.CurrentStatus, status, history.Duration)
	}

	// 计算工作时长（有效工作时间）
	workingSeconds := ds.CalculatingWorkingSeconds(date, *workTimeSettings)
	result.WorkTime = workingSeconds

	// 计算运行时间（生产时间）
	result.RunningTime = result.StatusSummary["production"]

	// 计算总时间（所有状态时间之和）
	result.TotalTime = result.StatusSummary["production"] +
		result.StatusSummary["idle"] +
		result.StatusSummary["fault"] +
		result.StatusSummary["shutdown"] +
		result.StatusSummary["unknown"]

	// 计算利用率百分比
	if result.TotalTime > 0 {
		// 保存1位小数点, 四舍五入
		result.Utilization = utils.FixDecimal(float64(result.RunningTime) / float64(result.WorkTime) * 100)
	} else {
		result.Utilization = 0.0
	}

	logger.Debugf("📊 利用率统计结果: 运行时间=%d秒, 总时间=%d秒, 利用率=%.2f%%, 工作时长=%.0f秒",
		result.RunningTime, result.TotalTime, result.Utilization, result.WorkTime)

	// TODO: key = "utilization_"  + date + “worktime settings start_time"
	// TODO: 将数据保存到 redis
	// TODO: 将数据保存到 redis, 过期时间 48小时 + 随机 10 - 60 分钟
	return result
}

/**
 * 根据工作开始时间生成时间段
 *
 * 功能：根据工作时间设置中的每天开始时间，生成2小时间隔的时间段
 *
 * 时间段规则：
 * - 每个时间段为2小时
 * - 从开始时间开始，连续24小时（12个时间段）
 * - 跨天时间段自动处理
 * - 包含UTC时间戳信息
 *
 * 示例：
 * - 开始时间8:00 → 8:00-10:00, 10:00-12:00, ..., 06:00-08:00
 * - 开始时间8:30 → 8:30-10:30, 10:30-12:30, ..., 06:30-08:30
 *
 * @param {string} dayStartTime - 每天开始时间，格式：HH:MM
 * @param {string} date - 查询日期，格式：YYYY-MM-DD（可选，默认为当前日期）
 * @returns {[]map[string]string} 时间段列表，包含UTC时间信息
 */
func (ds *DataService) generateTimeSlotsFromWorkTime(dayStartTime string, dateStr string) []map[string]string {
	// 解析开始时间
	startHour, startMinute := utils.ParseTime(dayStartTime)

	// 确定查询日期,
	queryDate := time.Now()
	if len(dateStr) > 0 && dateStr != "" {

		// 参数 date 字符串转为 time.Time
		layout := "2006-01-02" // Go 的时间格式必须是这个形式
		// 转换字符串为时间对象
		queryDate, _ = time.Parse(layout, dateStr)
	}

	var timeSlots []map[string]string

	// 生成12个2小时时间段（覆盖24小时）
	for i := 0; i < 12; i++ {
		// 计算当前时间段的开始时间（从dayStartTime开始的小时偏移）
		startOffsetHours := i * 2
		endOffsetHours := (i + 1) * 2

		// 计算实际的小时和分钟
		currentHour := (startHour + startOffsetHours) % 24
		currentMinute := startMinute
		endHour := (startHour + endOffsetHours) % 24
		endMinute := startMinute

		// 格式化时间字符串
		startTime := fmt.Sprintf("%02d:%02d", currentHour, currentMinute)
		endTime := fmt.Sprintf("%02d:%02d", endHour, endMinute)

		// 生成标签
		label := fmt.Sprintf("%s-%s", startTime, endTime)

		// 解析查询日期
		startDateTime := time.Date(queryDate.Year(), queryDate.Month(), queryDate.Day(), startHour, startMinute, 0, 0, time.Local).Add(time.Duration(startOffsetHours) * time.Hour)
		endDateTime := time.Date(queryDate.Year(), queryDate.Month(), queryDate.Day(), startHour, startMinute, 0, 0, time.Local).Add(time.Duration(endOffsetHours) * time.Hour)

		// 计算本地时间戳（基于查询日期）
		startTimeLocal := time.Date(startDateTime.Year(), startDateTime.Month(), startDateTime.Day(), currentHour, currentMinute, 0, 0, time.Local)
		endTimeLocal := time.Date(endDateTime.Year(), endDateTime.Month(), endDateTime.Day(), endHour, endMinute, 0, 0, time.Local)

		// 转换为UTC时间
		startTimeUTC := startTimeLocal.UTC()
		endTimeUTC := endTimeLocal.UTC()

		timeSlot := map[string]string{
			"label":       label,
			"start":       startTime,                                    // 保持向后兼容
			"end":         endTime,                                      // 保持向后兼容
			"start_local": startTimeLocal.Format("2006-01-02 15:04:05"), // 本地时区完整日期时间
			"end_local":   endTimeLocal.Format("2006-01-02 15:04:05"),   // 本地时区完整日期时间
			"start_utc":   startTimeUTC.Format(time.RFC3339),            // 开始的UTC时间
			"end_utc":     endTimeUTC.Format(time.RFC3339),              // 结束的UTC时间
		}

		timeSlots = append(timeSlots, timeSlot)
	}

	return timeSlots
}

/**
 * 将状态记录分配到对应的时间段（终极优化版本）
 *
 * 功能：
 * 1. 利用TimeSlotData中的StartUTC和EndUTC字段进行精确的UTC时间匹配
 * 2. 使用DeviceStatusHistory的Timestamp字段进行时间筛选
 * 3. 处理跨时间段的状态记录（分割）
 * 4. 生成状态记录的基本信息
 *
 * 终极优化点：
 * - 移除冗余参数，直接使用history中的StartTime和EndTime
 * - 直接使用预解析的UTC时间，避免重复解析
 * - 利用Timestamp字段进行更精确的时间筛选
 * - 减少函数参数，简化调用接口
 * - 提高代码可读性和维护性
 *
 * @param timeSlotDataList 时间段数据列表指针
 * @param history 状态历史记录（包含所有必需的时间信息）
 */
func (ds *DataService) assignStatusToTimeSlots(timeSlotDataList *[]models.TimeSlotData, history models.DeviceStatusHistory, filterSignalDuration int64) {

	// 遍历所有时间段，找到匹配的时间段
	for i := range *timeSlotDataList {
		slot := &(*timeSlotDataList)[i]

		// 直接解析时间段的UTC时间（避免重复解析）
		slotStart, err1 := time.Parse(time.RFC3339, slot.StartUTC)
		slotEnd, err2 := time.Parse(time.RFC3339, slot.EndUTC)

		if err1 != nil || err2 != nil {
			logger.Errorf("❌ Failed to parse UTC time slot %s-%s: %v, %v", slot.StartUTC, slot.EndUTC, err1, err2)
			continue
		}

		// 实现前状态记录补充功能：
		// 如果时间段还没有记录且存在前状态信息，则添加前状态记录来补充时间段起始位置的空隙
		if len(slot.Records) == 0 && !history.PrevStartTime.IsZero() && !history.PrevEndTime.IsZero() &&
			history.PrevStatus != "" && slotStart.Before(history.StartTime) && history.PrevEndTime.Before(slotEnd) {
			// 检查前状态是否与当前时间段有重叠
			{
				// 计算前状态在当前时间段内的实际开始和结束时间
				prevActualStart := utils.MaxTime(history.PrevStartTime, slotStart)
				prevActualEnd := utils.MinTime(history.PrevEndTime, slotEnd)

				// 计算前状态持续时间（秒）
				prevDuration := int64(prevActualEnd.Sub(prevActualStart).Seconds())

				// 应用过滤信号时长：如果持续时间大于等于过滤阈值，才添加前状态记录
				if prevDuration >= filterSignalDuration {
					// 获取北京时区用于时间转换
					beijingLocation, _ := time.LoadLocation("Asia/Shanghai")

					// 创建前状态记录
					prevRecord := models.TimeSlotStatusRecord{
						ID:                fmt.Sprintf("%s-prev-%d-%d", history.DeviceID, prevActualStart.Unix(), i),
						DeviceID:          history.DeviceID,
						Status:            history.PrevStatus,
						StartTime:         prevActualStart.Format("15:04:05"),
						EndTime:           prevActualEnd.Format("15:04:05"),
						Duration:          prevDuration,
						Position:          0.0, // 将在calculateStatusBarPositions中计算
						Width:             0.0, // 将在calculateStatusBarPositions中计算
						OriginalStart:     history.PrevStartTime.In(beijingLocation).Format("2006-01-02 15:04:05"),
						OriginalEnd:       history.PrevEndTime.In(beijingLocation).Format("2006-01-02 15:04:05"),
						OriginalTimestamp: history.PrevStartTime.Format(time.RFC3339), // 使用前状态开始时间作为时间戳
						IsSegmented:       !history.PrevStartTime.Equal(prevActualStart) || !history.PrevEndTime.Equal(prevActualEnd),
					}

					// 生成前状态记录的tooltip（使用北京时间）
					prevOriginalStartBeijing := history.PrevStartTime.In(beijingLocation).Format("2006-01-02 15:04:05")
					prevOriginalEndBeijing := history.PrevEndTime.In(beijingLocation).Format("2006-01-02 15:04:05")

					// 计算前状态在时间段内的实际北京时间显示
					prevActualStartBeijing := prevActualStart.In(beijingLocation).Format("15:04:05")
					prevActualEndBeijing := prevActualEnd.In(beijingLocation).Format("15:04:05")

					// 获取前状态的中文名称和格式化持续时间
					prevStatusNameChinese := utils.GetStatusNameChinese(prevRecord.Status)
					prevDurationFormatted := utils.FormatDurationChinese(int(prevRecord.Duration))

					if prevRecord.IsSegmented {
						prevRecord.Tooltip = fmt.Sprintf("%s %s-%s (前状态, 原始: %s-%s, %s)",
							prevStatusNameChinese, prevActualStartBeijing, prevActualEndBeijing,
							prevOriginalStartBeijing, prevOriginalEndBeijing, prevDurationFormatted)
					} else {
						prevRecord.Tooltip = fmt.Sprintf("%s %s-%s (前状态, %s)",
							prevStatusNameChinese, prevActualStartBeijing, prevActualEndBeijing, prevDurationFormatted)
					}

					// 添加前状态记录到时间段
					slot.Records = append(slot.Records, prevRecord)

					// 调试日志：记录前状态记录的添加
					logger.Debugf("✅ 前状态记录已添加到时间段: 设备=%s, 前状态=%s, 时间段=%s, 实际时间=%s-%s, 持续=%d秒",
						history.DeviceID, history.PrevStatus, slot.Label,
						prevActualStart.Format("15:04:05"), prevActualEnd.Format("15:04:05"), prevDuration)
				} else {
					logger.Debugf("🔧 过滤前状态短信号: 设备=%s, 前状态=%s, 持续时间=%d秒 < 过滤阈值=%d秒",
						history.DeviceID, history.PrevStatus, prevDuration, filterSignalDuration)
				}
			}
		}

		// 检查状态记录是否与当前时间段有重叠
		// 使用UTC时间进行精确比较，无需处理跨日问题
		if utils.HasTimeOverlap(history.StartTime, history.EndTime, slotStart, slotEnd) {
			// 额外检查：使用Timestamp字段确保记录在时间段范围内
			// 这提供了双重验证，确保数据的准确性
			if history.Timestamp.Before(slotStart) || history.Timestamp.After(slotEnd) {
				// 如果Timestamp不在时间段范围内，但StartTime/EndTime有重叠
				// 这种情况下跳过记录，避免错误分配
				logger.Debugf("⚠️  状态记录时间重叠但Timestamp超出范围，跳过: 设备=%s, Timestamp=%s, 时间段=%s-%s",
					history.DeviceID, history.Timestamp.Format(time.RFC3339), slot.StartUTC, slot.EndUTC)
				continue
			}

			// 计算在当前时间段内的实际开始和结束时间
			actualStart := utils.MaxTime(history.StartTime, slotStart)
			actualEnd := utils.MinTime(history.EndTime, slotEnd)

			// 计算状态持续时间（秒）
			duration := int64(actualEnd.Sub(actualStart).Seconds())

			// 应用过滤信号时长：如果持续时间小于过滤阈值，跳过此记录
			if duration < filterSignalDuration {
				logger.Debugf("🔧 过滤短信号: 设备=%s, 状态=%s, 持续时间=%d秒 < 过滤阈值=%d秒",
					history.DeviceID, history.CurrentStatus, duration, filterSignalDuration)
				continue
			}

			// 获取北京时区用于时间转换
			beijingLocation, _ := time.LoadLocation("Asia/Shanghai")

			// 创建状态记录
			record := models.TimeSlotStatusRecord{
				ID:                fmt.Sprintf("%s-%d-%d", history.DeviceID, actualStart.Unix(), i),
				DeviceID:          history.DeviceID,
				Status:            history.CurrentStatus,
				StartTime:         actualStart.Format("15:04:05"),
				EndTime:           actualEnd.Format("15:04:05"),
				Duration:          duration,                                                                   // 使用已计算的持续时间
				Position:          0.0,                                                                        // 将在calculateStatusBarPositions中计算
				Width:             0.0,                                                                        // 将在calculateStatusBarPositions中计算
				OriginalStart:     history.StartTime.In(beijingLocation).Format("2006-01-02 15:04:05"),        // 将原始开始时间转为（北京时间）
				OriginalEnd:       history.EndTime.In(beijingLocation).Format("2006-01-02 15:04:05"),          // 将原始结束时间转为（北京时间）
				OriginalTimestamp: history.Timestamp.Format(time.RFC3339),                                     // InfluxDB原始timestamp字段（UTC格式）
				IsSegmented:       !history.StartTime.Equal(actualStart) || !history.EndTime.Equal(actualEnd), // 是否为分割的记录
			}

			// 生成tooltip，使用北京时间显示
			originalStartBeijing := history.StartTime.In(beijingLocation).Format("2006-01-02 15:04:05")
			originalEndBeijing := history.EndTime.In(beijingLocation).Format("2006-01-02 15:04:05")

			// 计算实际时间段内的北京时间显示
			actualStartBeijing := actualStart.In(beijingLocation).Format("15:04:05")
			actualEndBeijing := actualEnd.In(beijingLocation).Format("15:04:05")

			// 获取状态的中文名称
			statusNameChinese := utils.GetStatusNameChinese(record.Status)

			// 将秒数转换为时分秒格式
			durationFormatted := utils.FormatDurationChinese(int(record.Duration))

			if record.IsSegmented {
				record.Tooltip = fmt.Sprintf("%s %s-%s (原始: %s-%s, %s)",
					statusNameChinese, actualStartBeijing, actualEndBeijing,
					originalStartBeijing, originalEndBeijing, durationFormatted)
			} else {
				record.Tooltip = fmt.Sprintf("%s %s-%s (%s)",
					statusNameChinese, actualStartBeijing, actualEndBeijing, durationFormatted)
			}

			slot.Records = append(slot.Records, record)

			// 调试日志：记录成功分配的状态
			logger.Debugf("✅ 状态记录已分配到时间段: 设备=%s, 状态=%s, 时间段=%s, 实际时间=%s-%s, Timestamp=%s",
				history.DeviceID, history.CurrentStatus, slot.Label,
				actualStart.Format("15:04:05"), actualEnd.Format("15:04:05"),
				history.Timestamp.Format("15:04:05"))
		}
	}
}

/**
 * 计算时间段内状态条的位置和宽度（优化版本）
 *
 * 功能：
 * 1. 使用UTC时间进行精确计算，避免跨日问题
 * 2. 根据状态记录在时间段内的时间位置计算百分比位置
 * 3. 根据状态持续时间计算宽度百分比
 * 4. 确保状态条有最小可见宽度
 *
 * 优化点：
 * - 直接使用UTC时间进行计算，避免本地时间的跨日复杂性
 * - 使用时间段的StartUTC和EndUTC字段进行精确计算
 * - 改进错误处理和调试日志
 *
 * @param timeSlot 时间段数据指针
 */
func (ds *DataService) calculateStatusBarPositions(timeSlot *models.TimeSlotData) {
	if len(timeSlot.Records) == 0 {
		return
	}

	// 解析时间段的UTC开始和结束时间
	slotStartUTC, err1 := time.Parse(time.RFC3339, timeSlot.StartUTC)
	slotEndUTC, err2 := time.Parse(time.RFC3339, timeSlot.EndUTC)

	if err1 != nil || err2 != nil {
		logger.Errorf("❌ Failed to parse slot UTC times %s-%s: %v, %v", timeSlot.StartUTC, timeSlot.EndUTC, err1, err2)
		return
	}

	// 计算时间段总时长（秒）
	slotDurationSeconds := int64(slotEndUTC.Sub(slotStartUTC).Seconds())

	// 为每个状态记录计算位置和宽度
	for i := range timeSlot.Records {
		record := &timeSlot.Records[i]

		// 解析记录的开始和结束时间（这些时间已经是在时间段内的实际时间）
		recordStartTime, err := time.Parse("15:04:05", record.StartTime)
		if err != nil {
			logger.Errorf("❌ Failed to parse record start time %s: %v", record.StartTime, err)
			continue
		}

		recordEndTime, err := time.Parse("15:04:05", record.EndTime)
		if err != nil {
			logger.Errorf("❌ Failed to parse record end time %s: %v", record.EndTime, err)
			continue
		}

		// 将记录时间转换为相对于时间段开始的秒数
		// 注意：record.StartTime 和 record.EndTime 已经是在时间段内的实际时间
		recordStartSeconds := int64(recordStartTime.Hour()*3600 + recordStartTime.Minute()*60 + recordStartTime.Second())
		recordEndSeconds := int64(recordEndTime.Hour()*3600 + recordEndTime.Minute()*60 + recordEndTime.Second())
		slotStartSeconds := int64(slotStartUTC.Hour()*3600 + slotStartUTC.Minute()*60 + slotStartUTC.Second())

		// 处理跨日情况：如果记录时间小于时间段开始时间，说明是跨日的
		if recordStartSeconds < slotStartSeconds {
			recordStartSeconds += 24 * 3600
		}
		if recordEndSeconds < slotStartSeconds {
			recordEndSeconds += 24 * 3600
		}

		// 计算相对于时间段开始的偏移秒数
		offsetSeconds := recordStartSeconds - slotStartSeconds

		// 秒级精度计算：计算位置百分比（相对于时间段开始的位置）
		record.Position = float64(offsetSeconds) / float64(slotDurationSeconds) * 100.0

		// 秒级精度计算：计算宽度百分比（基于持续时间）
		record.Width = float64(record.Duration) / float64(slotDurationSeconds) * 100.0

		// 确保最小宽度（0.05%，提供更精细的控制）
		if record.Width < 0.05 {
			record.Width = 0.05
		}

		// 确保位置和宽度在合理范围内
		if record.Position < 0 {
			record.Position = 0
		}
		if record.Position > 100 {
			record.Position = 100
			record.Width = 0.05 // 如果位置超出范围，设置最小宽度
		}
		if record.Position+record.Width > 100 {
			record.Width = 100 - record.Position
		}

		// 秒级精度：保留更多小数位以提供精确的位置信息
		logger.Debugf("📊 状态条位置计算(秒级精度): %s %s 位置=%.4f%% 宽度=%.4f%% 持续=%d秒 (偏移=%d秒)",
			timeSlot.Label, record.Status, record.Position, record.Width, record.Duration, offsetSeconds)
	}
}

// (ds *DataService)

func (ds *DataService) mapDeviceStatus(status string) string {

	StatusMapping := map[string]string{
		"production":   "production", // 运行中 -> 生产中
		"running":      "production", // 运行中 -> 生产中
		"idle":         "idle",       // 待机 -> 空闲
		"standby":      "idle",       // 待机 -> 空闲
		"fault":        "fault",      // 报警 -> 故障
		"alarm":        "fault",      // 报警 -> 故障
		"shutdown":     "shutdown",
		"disconnected": "shutdown",
	}
	if mappedStatus, exists := StatusMapping[status]; exists {
		return mappedStatus
	}

	// 默认返回未知状态
	return "unknown"
}

/**
 * CalculatingWorkingSeconds 计算工作时长（秒）
 *
 * 功能：根据日期、工作时间配置和当前时间，计算有效的工作时长
 *
 * 计算逻辑：
 * 1. 如果是历史日期（非今天）：
 *    - OP1模式：固定24小时（86400秒）
 *    - OP2模式：24小时减去所有启用的休息时间
 *
 * 2. 如果是今天：
 *    - 基础工作时长 = 当前时间 - 每天开始时间
 *    - OP1模式：直接返回基础工作时长
 *    - OP2模式：基础工作时长减去已过的休息时间
 *
 * 休息时间处理（仅OP2模式）：
 * - 当前时间在休息时间内：扣除（当前时间 - 休息开始时间）
 * - 当前时间超过休息时间：扣除（休息结束时间 - 休息开始时间）
 * - 当前时间未到休息时间：不扣除
 *
 * @param {string} date - 查询日期，格式：YYYY-MM-DD，空字符串表示今天
 * @param {models.WorkTimeSettings} workTimeSettings - 工作时间配置
 * @returns {float64} 有效工作时长（秒）
 *
 * @example
 * // 今天从08:00开始，当前时间14:30，OP1模式
 * workingSeconds := ds.CalculatingWorkingSeconds("", workTimeSettings)
 * // 返回：6.5小时 = 23400秒
 *
 * // 今天从08:00开始，当前时间14:30，OP2模式，12:00-13:00休息
 * workingSeconds := ds.CalculatingWorkingSeconds("", workTimeSettings)
 * // 返回：5.5小时 = 19800秒（扣除1小时休息时间）
 */
func (ds *DataService) CalculatingWorkingSeconds(date string, workTimeSettings models.WorkTimeSettings) float64 {
	// 设置北京时区（UTC+8）
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Errorf("❌ 加载北京时区失败: %v", err)
		beijingLocation = time.FixedZone("CST", 8*3600) // 使用固定时区作为备选
	}

	// 获取当前时间（北京时间）
	now := time.Now().In(beijingLocation)

	// 确定查询日期
	var queryDate string
	if date == "" {
		queryDate = now.Format("2006-01-02")
	} else {
		queryDate = date
	}

	// 判断是否为今天
	today := now.Format("2006-01-02")
	isToday := queryDate == today

	logger.Debugf("⏰ 计算工作时长: 日期=%s, 是否今天=%v, 利用率模式=%s",
		queryDate, isToday, workTimeSettings.UtilizationMode)

	// 解析每天开始时间，默认为08:00
	dayStartTime := workTimeSettings.DayStartTime
	if dayStartTime == "" {
		dayStartTime = "08:00"
	}

	startHour, startMinute := utils.ParseTime(dayStartTime)

	if !isToday {
		// 历史日期：固定计算
		return ds.calculateHistoricalWorkingSeconds(&workTimeSettings)
	}

	// 今天：动态计算
	return ds.calculateCurrentDayWorkingSeconds(now, startHour, startMinute, &workTimeSettings, beijingLocation)
}

/**
 * calculateHistoricalWorkingSeconds 计算历史日期的工作时长
 *
 * 功能：计算非今天日期的固定工作时长
 *
 * 计算逻辑：
 * - OP1模式：固定24小时（86400秒）
 * - OP2模式：24小时减去所有启用的休息时间
 *
 * @param {models.WorkTimeSettings} workTimeSettings - 工作时间配置
 * @returns {float64} 历史日期工作时长（秒）
 */
func (ds *DataService) calculateHistoricalWorkingSeconds(workTimeSettings *models.WorkTimeSettings) float64 {
	// OP1模式：固定24小时
	if workTimeSettings.UtilizationMode == "OP1" {
		logger.Debugf("📊 [历史日期] OP1模式，固定工作时长: 24小时 = 86400秒")
		return 86400.0 // 24小时 * 3600秒
	}

	// OP2模式：24小时减去休息时间
	baseWorkingSeconds := 86400.0 // 24小时的秒数
	totalRestSeconds := 0.0

	// 计算所有启用的休息时间
	for _, restPeriod := range workTimeSettings.RestPeriods {
		if !restPeriod.Enabled {
			continue
		}

		restDuration := ds.CalculateRestDuration(restPeriod.StartTime, restPeriod.EndTime)
		totalRestSeconds += restDuration

		logger.Debugf("📊 [历史日期] 休息时间: %s-%s, 时长: %.0f秒",
			restPeriod.StartTime, restPeriod.EndTime, restDuration)
	}

	workingSeconds := baseWorkingSeconds - totalRestSeconds
	logger.Debugf("📊 [历史日期] OP2模式，工作时长: %.0f秒 - %.0f秒 = %.0f秒",
		baseWorkingSeconds, totalRestSeconds, workingSeconds)

	return workingSeconds
}

/**
 * calculateCurrentDayWorkingSeconds 计算当天的工作时长
 *
 * 功能：计算从开始时间到当前时间的实际工作时长，扣除已过的休息时间
 *
 * 计算逻辑：
 * 1. 计算基础工作时长：当前时间 - 每天开始时间
 * 2. OP1模式：直接返回基础工作时长
 * 3. OP2模式：基础工作时长减去已过的休息时间
 *
 * @param {time.Time} now - 当前时间（北京时间）
 * @param {int} startHour - 开始小时
 * @param {int} startMinute - 开始分钟
 * @param {models.WorkTimeSettings} workTimeSettings - 工作时间配置
 * @param {*time.Location} location - 时区信息
 * @returns {float64} 当天工作时长（秒）
 */
func (ds *DataService) calculateCurrentDayWorkingSeconds(now time.Time, startHour, startMinute int,
	workTimeSettings *models.WorkTimeSettings, location *time.Location) float64 {

	// 构建今日的开始时间
	today := time.Date(now.Year(), now.Month(), now.Day(), startHour, startMinute, 0, 0, location)

	// 如果当前时间早于今日开始时间，说明是跨天情况，使用昨天的开始时间
	if now.Before(today) {
		today = today.AddDate(0, 0, -1)
		logger.Debugf("⏰ [当天] 跨天情况，使用昨天的开始时间: %s", today.Format("2006-01-02 15:04:05"))
	}

	// 计算基础工作时长（秒）
	baseWorkingSeconds := now.Sub(today).Seconds()

	// 确保工作时长不为负数
	if baseWorkingSeconds < 0 {
		logger.Warnf("⚠️ [当天] 基础工作时长为负数: %.0f秒，设置为0", baseWorkingSeconds)
		baseWorkingSeconds = 0
	}

	logger.Debugf("⏰ [当天] 基础工作时长: 从 %s 到 %s = %.0f秒",
		today.Format("15:04:05"), now.Format("15:04:05"), baseWorkingSeconds)

	// OP1模式：直接返回基础工作时长
	if workTimeSettings.UtilizationMode == "OP1" {
		logger.Debugf("📊 [当天] OP1模式，工作时长: %.0f秒", baseWorkingSeconds)
		return baseWorkingSeconds
	}

	// OP2模式：扣除已过的休息时间
	totalRestSeconds := 0.0

	for _, restPeriod := range workTimeSettings.RestPeriods {
		if !restPeriod.Enabled {
			continue
		}

		restSeconds := ds.calculateCurrentDayRestSeconds(now, restPeriod, today, location)
		totalRestSeconds += restSeconds

		if restSeconds > 0 {
			logger.Debugf("📊 [当天] 休息时间: %s-%s, 已过时长: %.0f秒",
				restPeriod.StartTime, restPeriod.EndTime, restSeconds)
		}
	}

	workingSeconds := baseWorkingSeconds - totalRestSeconds

	// 确保工作时长不为负数
	if workingSeconds < 0 {
		logger.Warnf("⚠️ [当天] 工作时长为负数: %.0f秒，设置为0", workingSeconds)
		workingSeconds = 0
	}

	logger.Debugf("📊 [当天] OP2模式，工作时长: %.0f秒 - %.0f秒 = %.0f秒",
		baseWorkingSeconds, totalRestSeconds, workingSeconds)

	return workingSeconds
}

/**
 * calculateRestDuration 计算休息时间段的时长（秒）
 *
 * 功能：计算两个时间点之间的时长，支持跨天计算
 *
 * @param {string} startTime - 开始时间（格式：HH:MM）
 * @param {string} endTime - 结束时间（格式：HH:MM）
 * @returns {float64} 休息时长（秒）
 */
func (ds *DataService) CalculateRestDuration(startTime, endTime string) float64 {
	start, err := time.Parse("15:04", startTime)
	if err != nil {
		logger.Warnf("⚠️ 解析休息开始时间失败: %v", err)
		return 0
	}

	end, err := time.Parse("15:04", endTime)
	if err != nil {
		logger.Warnf("⚠️ 解析休息结束时间失败: %v", err)
		return 0
	}

	// 处理跨天情况
	if end.Before(start) || end.Equal(start) {
		end = end.Add(24 * time.Hour)
	}

	duration := end.Sub(start)
	return duration.Seconds()
}

/**
 * calculateCurrentDayRestSeconds 计算当天已过的休息时间（秒）
 *
 * 功能：根据当前时间和休息时间段，计算已经过去的休息时间
 *
 * 计算逻辑：
 * - 当前时间在休息时间之前：返回0（休息时间未到）
 * - 当前时间在休息时间内：返回（当前时间 - 休息开始时间）
 * - 当前时间在休息时间之后：返回（休息结束时间 - 休息开始时间）
 *
 * @param {time.Time} now - 当前时间
 * @param {models.RestPeriod} restPeriod - 休息时间配置
 * @param {time.Time} dayStart - 当天开始时间
 * @param {*time.Location} location - 时区信息
 * @returns {float64} 已过的休息时间（秒）
 */
func (ds *DataService) calculateCurrentDayRestSeconds(now time.Time, restPeriod models.RestPeriod,
	dayStart time.Time, location *time.Location) float64 {

	// 解析休息时间
	restStartHour, restStartMinute := utils.ParseTime(restPeriod.StartTime)
	restEndHour, restEndMinute := utils.ParseTime(restPeriod.EndTime)

	// 构建休息开始和结束时间
	restStart := time.Date(now.Year(), now.Month(), now.Day(),
		restStartHour, restStartMinute, 0, 0, location)
	restEnd := time.Date(now.Year(), now.Month(), now.Day(),
		restEndHour, restEndMinute, 0, 0, location)

	// 处理跨天休息时间
	if restEnd.Before(restStart) || restEnd.Equal(restStart) {
		restEnd = restEnd.Add(24 * time.Hour)
	}

	// 确保休息时间在工作时间范围内
	if restStart.Before(dayStart) {
		// 如果休息开始时间早于工作开始时间，可能是跨天情况
		// 需要调整到正确的日期
		if restEnd.After(dayStart) {
			// 休息时间跨越工作开始时间，调整休息开始时间为工作开始时间
			restStart = dayStart
		} else {
			// 整个休息时间都在工作开始之前，不计算
			return 0
		}
	}

	// 判断当前时间与休息时间的关系
	if now.Before(restStart) {
		// 当前时间在休息时间之前，休息时间未到
		return 0
	} else if now.After(restEnd) {
		// 当前时间在休息时间之后，返回完整的休息时长
		return restEnd.Sub(restStart).Seconds()
	} else {
		// 当前时间在休息时间内，返回已过的休息时长
		return now.Sub(restStart).Seconds()
	}
}

/**
 * 计算利用率区间统计
 */
func (h *DataService) CalculateUtilizationZones(deviceUtilizations []models.UtilizationData) []models.UtilizationZone {
	zones := []models.UtilizationZone{
		{Name: "高利用率", MinRate: 80, MaxRate: 100, Color: "#22c55e"},
		{Name: "中利用率", MinRate: 50, MaxRate: 80, Color: "#f59e0b"},
		{Name: "低利用率", MinRate: 0, MaxRate: 50, Color: "#ef4444"},
	}

	totalDevices := len(deviceUtilizations)

	for i := range zones {
		count := 0
		for _, device := range deviceUtilizations {
			if device.Utilization >= zones[i].MinRate && device.Utilization < zones[i].MaxRate {
				count++
			}
		}

		zones[i].DeviceCount = count
		if totalDevices > 0 {
			zones[i].Percentage = (float64(count) / float64(totalDevices)) * 100
		}
	}

	return zones
}
