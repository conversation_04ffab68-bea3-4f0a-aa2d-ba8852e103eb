package services

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"server-api/db"
	"server-api/models"
)

// DailyPlanService 每日计划管理服务
type DailyPlanService struct {
	mongoManager *db.MongoDBManager
}

// NewDailyPlanService 创建每日计划服务实例（使用统一的MongoDB管理器）
func NewDailyPlanService(mongoManager *db.MongoDBManager) *DailyPlanService {
	return &DailyPlanService{
		mongoManager: mongoManager,
	}
}

// getCollection 获取指定名称的集合（内部辅助方法）
func (s *DailyPlanService) getCollection(collectionName string) (*mongo.Collection, error) {
	if s.mongoManager == nil {
		return nil, fmt.Errorf("MongoDB manager is nil")
	}
	database := s.mongoManager.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("database not available")
	}
	return database.Collection(collectionName), nil
}

// GetDailyPlans 获取每日计划列表
func (s *DailyPlanService) GetDailyPlans(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"device_name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"product_name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"operator_name": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}
	if params.Status != "" {
		filter["status"] = params.Status
	}

	// 计算总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count daily plans: %v", err)
	}

	// 构建排序
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "plan_date"
	}
	sort := bson.D{{Key: sortBy, Value: 1}}
	if params.SortDesc {
		sort = bson.D{{Key: sortBy, Value: -1}}
	}

	// 分页查询
	skip := (params.Page - 1) * params.PageSize
	opts := options.Find().
		SetSort(sort).
		SetSkip(int64(skip)).
		SetLimit(int64(params.PageSize))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find daily plans: %v", err)
	}
	defer cursor.Close(ctx)

	var plans []models.DailyPlan
	if err = cursor.All(ctx, &plans); err != nil {
		return nil, fmt.Errorf("failed to decode daily plans: %v", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	return &models.ListResponse{
		Data:       plans,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateDailyPlan 创建每日计划
func (s *DailyPlanService) CreateDailyPlan(plan *models.DailyPlan) error {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 检查同一天同一设备是否已有计划
	filter := bson.M{
		"plan_date": bson.M{
			"$gte": time.Date(plan.PlanDate.Year(), plan.PlanDate.Month(), plan.PlanDate.Day(), 0, 0, 0, 0, plan.PlanDate.Location()),
			"$lt":  time.Date(plan.PlanDate.Year(), plan.PlanDate.Month(), plan.PlanDate.Day()+1, 0, 0, 0, 0, plan.PlanDate.Location()),
		},
		"device_id":  plan.DeviceID,
		"shift_type": plan.ShiftType,
	}

	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to check existing plan: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("plan already exists for this device on this date and shift")
	}

	plan.CreatedAt = time.Now()
	plan.UpdatedAt = time.Now()

	// 设置默认状态
	if plan.Status == "" {
		plan.Status = "planned"
	}

	// 初始化完成率
	plan.CompletionRate = 0.0

	_, err = collection.InsertOne(ctx, plan)
	if err != nil {
		return fmt.Errorf("failed to create daily plan: %v", err)
	}

	return nil
}

// GetDailyPlan 获取单个每日计划
func (s *DailyPlanService) GetDailyPlan(id string) (*models.DailyPlan, error) {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid plan ID: %v", err)
	}

	var plan models.DailyPlan
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&plan)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("daily plan not found")
		}
		return nil, fmt.Errorf("failed to find daily plan: %v", err)
	}

	return &plan, nil
}

// UpdateDailyPlan 更新每日计划
func (s *DailyPlanService) UpdateDailyPlan(id string, plan *models.DailyPlan) error {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid plan ID: %v", err)
	}

	// 计算完成率
	if plan.PlannedQuantity > 0 {
		plan.CompletionRate = float64(plan.ActualQuantity) / float64(plan.PlannedQuantity) * 100
		if plan.CompletionRate > 100 {
			plan.CompletionRate = 100
		}
	}

	plan.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"plan_date":        plan.PlanDate,
			"device_id":        plan.DeviceID,
			"device_name":      plan.DeviceName,
			"product_id":       plan.ProductID,
			"product_name":     plan.ProductName,
			"planned_quantity": plan.PlannedQuantity,
			"actual_quantity":  plan.ActualQuantity,
			"completion_rate":  plan.CompletionRate,
			"shift_type":       plan.ShiftType,
			"operator_name":    plan.OperatorName,
			"status":           plan.Status,
			"notes":            plan.Notes,
			"updated_at":       plan.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update daily plan: %v", err)
	}

	return nil
}

// DeleteDailyPlan 删除每日计划
func (s *DailyPlanService) DeleteDailyPlan(id string) error {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid plan ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete daily plan: %v", err)
	}

	return nil
}

// GetPlansByDate 根据日期获取计划
func (s *DailyPlanService) GetPlansByDate(date time.Time) ([]models.DailyPlan, error) {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 查询指定日期的计划
	filter := bson.M{
		"plan_date": bson.M{
			"$gte": time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location()),
			"$lt":  time.Date(date.Year(), date.Month(), date.Day()+1, 0, 0, 0, 0, date.Location()),
		},
	}

	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find plans by date: %v", err)
	}
	defer cursor.Close(ctx)

	var plans []models.DailyPlan
	if err = cursor.All(ctx, &plans); err != nil {
		return nil, fmt.Errorf("failed to decode plans: %v", err)
	}

	return plans, nil
}

// GetPlansByDevice 根据设备获取计划
func (s *DailyPlanService) GetPlansByDevice(deviceID string) ([]models.DailyPlan, error) {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cursor, err := collection.Find(ctx, bson.M{"device_id": deviceID})
	if err != nil {
		return nil, fmt.Errorf("failed to find plans by device: %v", err)
	}
	defer cursor.Close(ctx)

	var plans []models.DailyPlan
	if err = cursor.All(ctx, &plans); err != nil {
		return nil, fmt.Errorf("failed to decode plans: %v", err)
	}

	return plans, nil
}

// GetWeeklyPlans 获取周计划
func (s *DailyPlanService) GetWeeklyPlans(startDate time.Time) ([]models.DailyPlan, error) {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	endDate := startDate.AddDate(0, 0, 7)

	filter := bson.M{
		"plan_date": bson.M{
			"$gte": startDate,
			"$lt":  endDate,
		},
	}

	opts := options.Find().SetSort(bson.D{
		{Key: "plan_date", Value: 1},
		{Key: "device_id", Value: 1},
	})

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find weekly plans: %v", err)
	}
	defer cursor.Close(ctx)

	var plans []models.DailyPlan
	if err = cursor.All(ctx, &plans); err != nil {
		return nil, fmt.Errorf("failed to decode weekly plans: %v", err)
	}

	return plans, nil
}

// GetPlanStatistics 获取计划统计信息
func (s *DailyPlanService) GetPlanStatistics(startDate, endDate time.Time) (map[string]interface{}, error) {
	collection, err := s.getCollection("daily_plans")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 统计指定时间范围内的计划
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"plan_date": bson.M{
					"$gte": startDate,
					"$lte": endDate,
				},
			},
		},
		{
			"$group": bson.M{
				"_id":                 nil,
				"total_plans":         bson.M{"$sum": 1},
				"total_planned":       bson.M{"$sum": "$planned_quantity"},
				"total_actual":        bson.M{"$sum": "$actual_quantity"},
				"avg_completion_rate": bson.M{"$avg": "$completion_rate"},
			},
		},
	}

	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate plan statistics: %v", err)
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode statistics: %v", err)
	}

	if len(results) == 0 {
		return map[string]interface{}{
			"total_plans":         0,
			"total_planned":       0,
			"total_actual":        0,
			"avg_completion_rate": 0,
		}, nil
	}

	return results[0], nil
}
