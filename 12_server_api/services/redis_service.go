/**
 * Redis缓存服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的高速缓存层，负责管理Redis连接和实现
 * 智能的数据缓存策略，显著提升系统的响应性能和用户体验
 *
 * 核心功能：
 * - 连接管理：Redis连接池的建立、维护和健康检查
 * - 数据缓存：设备状态、生产数据、统计信息的高速缓存
 * - 缓存策略：实时数据和历史数据的差异化缓存处理
 * - 过期管理：自动清理过期缓存，优化内存使用
 * - 性能监控：缓存命中率、内存使用等性能指标收集
 *
 * 缓存分类：
 * - 实时数据缓存：当日设备状态、产量数据，短期过期（小时级）
 * - 历史数据缓存：非当日数据，长期缓存（天级），减少数据库查询
 * - 统计数据缓存：状态统计、汇总数据，定期更新
 * - 会话数据缓存：用户会话、临时计算结果
 *
 * 性能特性：
 * - 毫秒级响应：内存存储，极快的数据读写速度
 * - 连接池优化：高效的连接复用，减少连接开销
 * - 批量操作：支持批量读写，提高吞吐量
 * - 压缩存储：JSON序列化，平衡存储空间和性能
 *
 * 可靠性保证：
 * - 连接检查：实时监控Redis连接状态
 * - 优雅降级：Redis不可用时的降级处理
 * - 错误恢复：连接失败的自动重试机制
 * - 数据一致性：缓存更新的原子性保证
 *
 * 业务场景：
 * - 设备监控大屏：实时设备状态的高频查询
 * - 历史数据分析：减少数据库压力，提升查询速度
 * - API响应优化：缓存热点数据，提升接口响应速度
 * - 系统性能监控：缓存层的性能指标收集
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"server-api/models"
	"shared/config"
	"shared/logger"

	"github.com/redis/go-redis/v9"
)

/**
 * Redis缓存服务结构体
 *
 * 功能：作为系统的L1缓存层，提供高性能的数据缓存和快速访问
 *
 * 架构设计：
 * - 单例连接：统一的Redis客户端管理
 * - 键值规范：标准化的缓存键命名和管理
 * - 过期策略：基于数据类型的差异化过期时间
 * - 连接池：高效的连接复用和资源管理
 *
 * 缓存策略：
 * - 实时数据：短期缓存（1-6小时），高频更新
 * - 历史数据：长期缓存（1-7天），按需加载
 * - 统计数据：中期缓存（1-24小时），定期刷新
 * - 会话数据：临时缓存（30分钟-2小时），用户相关
 *
 * @struct RedisService
 */
type RedisService struct {
	/** Redis客户端实例，负责与Redis服务器的通信 */
	client *redis.Client

	/** 全局上下文，用于控制所有Redis操作的生命周期 */
	ctx context.Context

	/** Redis键值规范，定义了所有缓存键的命名规则和过期时间 */
	keys models.RedisKeys

	/** Redis连接配置，包含主机、端口、密码、数据库等信息 */
	config *config.RedisConfig

	/** 连接状态标志，用于快速判断Redis服务可用性 */
	isConnected bool
}

// NewRedisService 创建Redis服务实例
func NewRedisService(cfg *config.RedisConfig) *RedisService {
	return &RedisService{
		ctx:    context.Background(),
		keys:   models.GetRedisKeys(),
		config: cfg,
	}
}

// Connect 连接到Redis服务器
func (rs *RedisService) Connect() error {
	logger.Info("🔗 Connecting to Redis...")

	// 创建Redis客户端
	rs.client = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", rs.config.Host, rs.config.Port),
		Password: rs.config.Password,
		DB:       rs.config.DB,
		PoolSize: rs.config.PoolSize,
	})

	// 测试连接
	_, err := rs.client.Ping(rs.ctx).Result()
	if err != nil {
		logger.Errorf("❌ Failed to connect to Redis: %v", err)
		return err
	}

	rs.isConnected = true
	logger.Info("✅ Connected to Redis successfully")
	return nil
}

// Disconnect 断开Redis连接
func (rs *RedisService) Disconnect() error {
	if rs.client != nil {
		err := rs.client.Close()
		rs.isConnected = false
		return err
	}
	return nil
}

// IsConnected 检查Redis连接状态
func (rs *RedisService) IsConnected() bool {
	if !rs.isConnected || rs.client == nil {
		return false
	}

	// 快速ping测试
	_, err := rs.client.Ping(rs.ctx).Result()
	return err == nil
}

// SetDeviceStatus 设置设备状态到Redis
func (rs *RedisService) SetDeviceStatus(deviceID string, status *models.DeviceStatus) error {
	if !rs.IsConnected() {
		return fmt.Errorf("Redis not connected")
	}

	// 转换为JSON
	jsonData, err := status.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to marshal device status: %w", err)
	}

	// 设置到Redis
	key := fmt.Sprintf(rs.keys.DeviceStatusToday, deviceID)
	err = rs.client.Set(rs.ctx, key, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("failed to set device status to Redis: %w", err)
	}

	logger.Debugf("📝 Set device status for %s to Redis", deviceID)
	return nil
}

// GetDeviceStatus 从Redis获取设备状态
func (rs *RedisService) GetDeviceStatus(deviceID string) (*models.DeviceStatus, error) {
	if !rs.IsConnected() {
		return nil, fmt.Errorf("Redis not connected")
	}

	key := fmt.Sprintf(rs.keys.DeviceStatusToday, deviceID)
	jsonData, err := rs.client.Get(rs.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 数据不存在
		}
		return nil, fmt.Errorf("failed to get device status from Redis: %w", err)
	}

	// 解析JSON
	var status models.DeviceStatus
	err = status.FromJSON(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal device status: %w", err)
	}

	return &status, nil
}

// SetDeviceProduction 设置设备产量到Redis
func (rs *RedisService) SetDeviceProduction(deviceID string, production *models.DeviceProduction) error {
	if !rs.IsConnected() {
		return fmt.Errorf("Redis not connected")
	}

	jsonData, err := production.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to marshal device production: %w", err)
	}

	key := fmt.Sprintf(rs.keys.DeviceProductionToday, deviceID)
	err = rs.client.Set(rs.ctx, key, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("failed to set device production to Redis: %w", err)
	}

	logger.Debugf("📝 Set device production for %s to Redis", deviceID)
	return nil
}

// GetDeviceProduction 从Redis获取设备产量
func (rs *RedisService) GetDeviceProduction(deviceID string) (*models.DeviceProduction, error) {
	if !rs.IsConnected() {
		return nil, fmt.Errorf("Redis not connected")
	}

	key := fmt.Sprintf(rs.keys.DeviceProductionToday, deviceID)
	jsonData, err := rs.client.Get(rs.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get device production from Redis: %w", err)
	}

	var production models.DeviceProduction
	err = production.FromJSON(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal device production: %w", err)
	}

	return &production, nil
}

// SetAllDevicesStatus 设置所有设备状态到Redis
func (rs *RedisService) SetAllDevicesStatus(devices []models.DeviceStatus) error {
	if !rs.IsConnected() {
		return fmt.Errorf("Redis not connected")
	}

	jsonData, err := json.Marshal(devices)
	if err != nil {
		return fmt.Errorf("failed to marshal all devices status: %w", err)
	}

	err = rs.client.Set(rs.ctx, rs.keys.AllDevicesStatus, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("failed to set all devices status to Redis: %w", err)
	}

	logger.Debugf("📝 Set all devices status (%d devices) to Redis", len(devices))
	return nil
}

// GetAllDevicesStatus 从Redis获取所有设备状态
func (rs *RedisService) GetAllDevicesStatus() ([]models.DeviceStatus, error) {
	if !rs.IsConnected() {
		return nil, fmt.Errorf("Redis not connected")
	}

	jsonData, err := rs.client.Get(rs.ctx, rs.keys.AllDevicesStatus).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get all devices status from Redis: %w", err)
	}

	var devices []models.DeviceStatus
	err = json.Unmarshal([]byte(jsonData), &devices)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal all devices status: %w", err)
	}

	return devices, nil
}

// SetStatusCounts 设置状态统计到Redis
func (rs *RedisService) SetStatusCounts(counts models.StatusCounts) error {
	if !rs.IsConnected() {
		return fmt.Errorf("Redis not connected")
	}

	jsonData, err := json.Marshal(counts)
	if err != nil {
		return fmt.Errorf("failed to marshal status counts: %w", err)
	}

	err = rs.client.Set(rs.ctx, rs.keys.StatusCounts, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("failed to set status counts to Redis: %w", err)
	}

	logger.Debugf("📝 Set status counts to Redis")
	return nil
}

// GetStatusCounts 从Redis获取状态统计
func (rs *RedisService) GetStatusCounts() (*models.StatusCounts, error) {
	if !rs.IsConnected() {
		return nil, fmt.Errorf("Redis not connected")
	}

	jsonData, err := rs.client.Get(rs.ctx, rs.keys.StatusCounts).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get status counts from Redis: %w", err)
	}

	var counts models.StatusCounts
	err = json.Unmarshal([]byte(jsonData), &counts)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal status counts: %w", err)
	}

	return &counts, nil
}

// SetDeviceStatusHistory 设置设备状态历史到Redis
func (rs *RedisService) SetDeviceStatusHistory(deviceID, date string, history []models.DeviceStatusHistory) error {
	if !rs.IsConnected() {
		return fmt.Errorf("Redis not connected")
	}

	jsonData, err := json.Marshal(history)
	if err != nil {
		return fmt.Errorf("failed to marshal device status history: %w", err)
	}

	key := fmt.Sprintf(rs.keys.DeviceStatusHistory, deviceID, date)
	expiration := rs.keys.HistoryDataExpiration
	if models.IsToday(date) {
		expiration = rs.keys.TodayDataExpiration
	}

	err = rs.client.Set(rs.ctx, key, jsonData, expiration).Err()
	if err != nil {
		return fmt.Errorf("failed to set device status history to Redis: %w", err)
	}

	logger.Debugf("📝 Set device status history for %s:%s to Redis", deviceID, date)
	return nil
}

// GetDeviceStatusHistory 从Redis获取设备状态历史
func (rs *RedisService) GetDeviceStatusHistory(deviceID, date string) ([]models.DeviceStatusHistory, error) {
	if !rs.IsConnected() {
		return nil, fmt.Errorf("Redis not connected")
	}

	key := fmt.Sprintf(rs.keys.DeviceStatusHistory, deviceID, date)
	jsonData, err := rs.client.Get(rs.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get device status history from Redis: %w", err)
	}

	var history []models.DeviceStatusHistory
	err = json.Unmarshal([]byte(jsonData), &history)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal device status history: %w", err)
	}

	return history, nil
}

// DeleteExpiredKeys 删除过期的缓存键
func (rs *RedisService) DeleteExpiredKeys() error {
	if !rs.IsConnected() {
		return fmt.Errorf("Redis not connected")
	}

	// 获取昨天的日期
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	// 删除昨天的实时数据键
	pattern := fmt.Sprintf("device:*:%s", yesterday)
	keys, err := rs.client.Keys(rs.ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get expired keys: %w", err)
	}

	if len(keys) > 0 {
		err = rs.client.Del(rs.ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("failed to delete expired keys: %w", err)
		}
		logger.Infof("🗑️ Deleted %d expired Redis keys for date %s", len(keys), yesterday)
	}

	return nil
}

// GetCacheStats 获取缓存统计信息
func (rs *RedisService) GetCacheStats() (map[string]interface{}, error) {
	if !rs.IsConnected() {
		return nil, fmt.Errorf("Redis not connected")
	}

	info, err := rs.client.Info(rs.ctx, "memory").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis info: %w", err)
	}

	stats := map[string]interface{}{
		"connected":   rs.IsConnected(),
		"memory_info": info,
		"last_check":  time.Now(),
	}

	return stats, nil
}
