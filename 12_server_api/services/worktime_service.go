package services

import (
	"context"
	"fmt"
	"log"
	"time"

	"server-api/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type WorkTimeService struct {
	mongoClient *mongo.Client
	logger      *log.Logger
}

func NewWorkTimeService(mongoClient *mongo.Client, logger *log.Logger) *WorkTimeService {
	return &WorkTimeService{
		mongoClient: mongoClient,
		logger:      logger,
	}
}

// GetWorkTimeSettings 获取工作时间设置
func (w *WorkTimeService) GetWorkTimeSettings() (*models.WorkTimeSettings, error) {
	collection := w.mongoClient.Database("device_db").Collection("work_time_settings")

	var settings models.WorkTimeSettings
	err := collection.FindOne(context.Background(), bson.M{}).Decode(&settings)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// 如果没有设置，返回默认设置
			return w.getDefaultWorkTimeSettings(), nil
		}
		return nil, fmt.Errorf("获取工作时间设置失败: %v", err)
	}

	return &settings, nil
}

// GetWorkTimeSettings 获取工作时间设置
func (w *WorkTimeService) GetWorkTimeSettingsV3() (*models.WorkTimeSettings, error) {
	collection := w.mongoClient.Database("device_db").Collection("work_time_settings")

	var settings models.WorkTimeSettings
	err := collection.FindOne(context.Background(), bson.M{}).Decode(&settings)
	if err != nil {
		return nil, fmt.Errorf("获取工作时间设置失败: %v", err)
	}

	return &settings, nil
}

// SaveWorkTimeSettings 保存工作时间设置
func (w *WorkTimeService) SaveWorkTimeSettings(settings *models.WorkTimeSettings) error {
	collection := w.mongoClient.Database("device_db").Collection("work_time_settings")

	// 设置时间戳
	now := time.Now()
	if settings.CreatedAt.IsZero() {
		settings.CreatedAt = now
	}
	settings.UpdatedAt = now

	// 使用upsert操作
	filter := bson.M{}
	if !settings.ID.IsZero() {
		filter["_id"] = settings.ID
	}

	opts := options.Replace().SetUpsert(true)
	result, err := collection.ReplaceOne(context.Background(), filter, settings, opts)
	if err != nil {
		return fmt.Errorf("保存工作时间设置失败: %v", err)
	}

	w.logger.Printf("工作时间设置已保存: UpsertedID=%v, ModifiedCount=%d", result.UpsertedID, result.ModifiedCount)
	return nil
}

// getDefaultWorkTimeSettings 获取默认工作时间设置
func (w *WorkTimeService) getDefaultWorkTimeSettings() *models.WorkTimeSettings {
	return &models.WorkTimeSettings{
		DayStartTime: "08:00",
		Shifts: []models.ShiftSetting{
			{Name: "早班", StartTime: "08:00", EndTime: "16:00", Enabled: true},
			{Name: "中班", StartTime: "16:00", EndTime: "00:00", Enabled: true},
			{Name: "晚班", StartTime: "00:00", EndTime: "08:00", Enabled: true},
		},
		RestPeriods: []models.RestPeriod{
			{Name: "午休", StartTime: "12:00", EndTime: "13:00", Enabled: true},
			{Name: "晚餐", StartTime: "18:00", EndTime: "19:00", Enabled: false},
		},
		UtilizationMode: "OP1",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

// InitializeDefaultSettings 初始化默认设置
func (w *WorkTimeService) InitializeDefaultSettings() error {
	// 检查是否已有设置
	_, err := w.GetWorkTimeSettings()
	if err == nil {
		w.logger.Println("工作时间设置已存在，跳过初始化")
		return nil
	}

	// 保存默认设置
	defaultSettings := w.getDefaultWorkTimeSettings()
	err = w.SaveWorkTimeSettings(defaultSettings)
	if err != nil {
		return fmt.Errorf("初始化默认工作时间设置失败: %v", err)
	}

	w.logger.Println("默认工作时间设置已初始化")
	return nil
}

// ValidateWorkTimeSettings 验证工作时间设置
func (w *WorkTimeService) ValidateWorkTimeSettings(settings *models.WorkTimeSettings) error {
	// 验证每天开始时间格式
	if !w.isValidTimeFormat(settings.DayStartTime) {
		return fmt.Errorf("每天开始时间格式无效: %s", settings.DayStartTime)
	}

	// 验证班次设置
	for i, shift := range settings.Shifts {
		if shift.Name == "" {
			return fmt.Errorf("班次 %d 名称不能为空", i+1)
		}
		if !w.isValidTimeFormat(shift.StartTime) {
			return fmt.Errorf("班次 %s 开始时间格式无效: %s", shift.Name, shift.StartTime)
		}
		if !w.isValidTimeFormat(shift.EndTime) {
			return fmt.Errorf("班次 %s 结束时间格式无效: %s", shift.Name, shift.EndTime)
		}
	}

	// 验证休息时间设置
	for i, rest := range settings.RestPeriods {
		if rest.Name == "" {
			return fmt.Errorf("休息时间 %d 名称不能为空", i+1)
		}
		if !w.isValidTimeFormat(rest.StartTime) {
			return fmt.Errorf("休息时间 %s 开始时间格式无效: %s", rest.Name, rest.StartTime)
		}
		if !w.isValidTimeFormat(rest.EndTime) {
			return fmt.Errorf("休息时间 %s 结束时间格式无效: %s", rest.Name, rest.EndTime)
		}
	}

	// 验证利用率计算模式
	if settings.UtilizationMode != "OP1" && settings.UtilizationMode != "OP2" {
		return fmt.Errorf("利用率计算模式无效: %s，必须是 OP1 或 OP2", settings.UtilizationMode)
	}

	return nil
}

// isValidTimeFormat 验证时间格式 HH:MM
func (w *WorkTimeService) isValidTimeFormat(timeStr string) bool {
	_, err := time.Parse("15:04", timeStr)
	return err == nil
}

// GetShiftByTime 根据时间获取对应的班次
func (w *WorkTimeService) GetShiftByTime(targetTime time.Time) (*models.ShiftSetting, error) {
	settings, err := w.GetWorkTimeSettings()
	if err != nil {
		return nil, err
	}

	timeStr := targetTime.Format("15:04")

	for _, shift := range settings.Shifts {
		if !shift.Enabled {
			continue
		}

		if w.isTimeInShift(timeStr, shift.StartTime, shift.EndTime) {
			return &shift, nil
		}
	}

	return nil, fmt.Errorf("未找到时间 %s 对应的班次", timeStr)
}

// isTimeInShift 判断时间是否在班次范围内
func (w *WorkTimeService) isTimeInShift(targetTime, startTime, endTime string) bool {
	target, _ := time.Parse("15:04", targetTime)
	start, _ := time.Parse("15:04", startTime)
	end, _ := time.Parse("15:04", endTime)

	// 处理跨天班次
	if end.Before(start) || end.Equal(start) {
		// 跨天班次：如 22:00 - 06:00
		return target.After(start) || target.Before(end) || target.Equal(start) || target.Equal(end)
	} else {
		// 同天班次：如 08:00 - 16:00
		return (target.After(start) || target.Equal(start)) && (target.Before(end) || target.Equal(end))
	}
}

// CalculateWorkingHours 计算工作时间（排除休息时间）
func (w *WorkTimeService) CalculateWorkingHours(date time.Time) (float64, error) {
	settings, err := w.GetWorkTimeSettings()
	if err != nil {
		return 0, err
	}

	// 总工作时间（24小时）
	totalHours := 24.0

	// 如果是OP2模式，需要减去休息时间
	if settings.UtilizationMode == "OP2" {
		for _, rest := range settings.RestPeriods {
			if !rest.Enabled {
				continue
			}

			restDuration := w.calculateTimeDuration(rest.StartTime, rest.EndTime)
			totalHours -= restDuration
		}
	}

	return totalHours, nil
}

// calculateTimeDuration 计算时间段长度（小时）
func (w *WorkTimeService) calculateTimeDuration(startTime, endTime string) float64 {
	start, _ := time.Parse("15:04", startTime)
	end, _ := time.Parse("15:04", endTime)

	// 处理跨天情况
	if end.Before(start) || end.Equal(start) {
		end = end.Add(24 * time.Hour)
	}

	duration := end.Sub(start)
	return duration.Hours()
}
