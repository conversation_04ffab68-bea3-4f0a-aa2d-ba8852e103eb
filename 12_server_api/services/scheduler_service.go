/**
 * 任务调度服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的任务调度核心，负责管理和执行各种
 * 定时任务，特别是设备统计数据的定期计算和存储
 *
 * 主要功能：
 * - 定时任务调度：基于Cron表达式的精确任务调度
 * - 每日统计任务：自动计算前一天的设备利用率和产量统计
 * - 手动任务执行：支持手动触发特定日期的统计计算
 * - 任务状态监控：实时监控任务执行状态和调度情况
 * - 错误处理：任务执行失败的重试和错误记录
 *
 * 调度策略：
 * - 每日UTC时间0:30执行：避开业务高峰期，确保数据完整性
 * - UTC时区调度：使用UTC时间避免本地时区和夏令时影响
 * - 秒级精度：支持精确到秒的任务调度
 * - 并发控制：避免同一任务的重复执行
 * - 异常恢复：任务失败时的自动重试机制
 *
 * 业务场景：
 * - 设备利用率统计：每日自动计算设备的利用率指标
 * - 产量达成率统计：每日自动计算产量完成情况
 * - 历史数据归档：将实时数据转换为历史统计数据
 * - 报表数据准备：为日报、周报、月报准备基础数据
 *
 * 技术特性：
 * - 基于robfig/cron：成熟稳定的Cron调度库
 * - 秒级调度：支持秒级精度的任务调度
 * - 内存高效：轻量级的任务调度，低内存占用
 * - 线程安全：支持并发的任务调度和执行
 *
 * 数据流程：
 * 1. 定时触发 -> 获取设备列表 -> 逐个计算统计 -> 保存到MongoDB
 * 2. 手动触发 -> 指定日期 -> 批量计算 -> 结果反馈
 * 3. 状态监控 -> 任务列表 -> 执行状态 -> 下次执行时间
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"log"
	"time"

	"github.com/robfig/cron/v3"
)

/**
 * 任务调度服务结构体
 *
 * 功能：作为系统的定时任务管理中心，负责各种周期性任务的调度和执行
 *
 * 架构设计：
 * - 基于Cron：使用成熟的Cron调度引擎
 * - 服务协调：与统计服务协作完成数据计算
 * - 日志记录：完整的任务执行日志和错误记录
 * - 状态管理：任务状态的实时监控和管理
 *
 * 调度特性：
 * - 秒级精度：支持精确到秒的任务调度
 * - 并发安全：多任务并发执行的安全保证
 * - 错误恢复：任务失败的自动重试和恢复
 * - 状态监控：实时的任务执行状态监控
 *
 * @struct SchedulerService
 */
type SchedulerService struct {
	/** Cron调度器实例，负责任务的定时调度和执行 */
	cron *cron.Cron

	/** 统计服务实例，提供设备统计计算功能 */
	statisticsService *StatisticsService

	/** 日志记录器，记录任务执行过程和结果 */
	logger *log.Logger
}

func NewSchedulerService(statisticsService *StatisticsService, logger *log.Logger) *SchedulerService {
	// 创建带秒级精度和UTC时区的cron调度器
	// 使用UTC时区确保定时任务在UTC时间执行，避免本地时区影响
	utcLocation := time.UTC
	c := cron.New(cron.WithSeconds(), cron.WithLocation(utcLocation))

	return &SchedulerService{
		cron:              c,
		statisticsService: statisticsService,
		logger:            logger,
	}
}

// Start 启动定时任务调度器
func (s *SchedulerService) Start() error {
	s.logger.Println("启动定时任务调度器（UTC时区）...")

	// 添加每天UTC时间0:30执行的统计任务
	// 注意：这是UTC时间00:30，对应北京时间08:30
	_, err := s.cron.AddFunc("0 30 0 * * *", s.dailyStatisticsTask)
	if err != nil {
		return err
	}

	// 启动调度器
	s.cron.Start()
	s.logger.Println("定时任务调度器已启动（UTC时间00:30执行）")

	return nil
}

// Stop 停止定时任务调度器
func (s *SchedulerService) Stop() {
	s.logger.Println("停止定时任务调度器...")
	s.cron.Stop()
	s.logger.Println("定时任务调度器已停止")
}

// dailyStatisticsTask 每日统计任务
func (s *SchedulerService) dailyStatisticsTask() {
	s.logger.Println("开始执行每日统计任务（UTC时间）...", "; UTC时间:", time.Now().UTC().Format(time.RFC3339), "; 本地时间:", time.Now().Format("2006-01-02 15:04:05"))

	// 计算前一天的日期（使用UTC时间）
	// 由于任务在UTC时间00:30执行，此时计算的前一天是正确的UTC日期
	nowLocal := time.Now()
	yesterdayLocal := nowLocal.Add(-24 * time.Hour).Format("2006-01-02")

	s.logger.Printf("📅 当前UTC时间: %s, 处理日期: %s", nowLocal.Format(time.RFC3339), yesterdayLocal)

	// 获取所有设备ID
	deviceIDs, err := s.statisticsService.getAllDeviceIDs()
	if err != nil {
		s.logger.Printf("获取设备列表失败: %v", err)
		return
	}

	s.logger.Printf("开始计算 %s 的设备统计，共 %d 个设备", yesterdayLocal, len(deviceIDs))

	// 为每个设备计算统计数据
	successCount := 0
	errorCount := 0

	for _, deviceID := range deviceIDs {
		stats, err := s.statisticsService.CalculateDeviceStatistics(yesterdayLocal, deviceID)
		if err != nil {
			s.logger.Printf("计算设备 %s 统计失败: %v", deviceID, err)
			errorCount++
			continue
		}

		if stats == nil {
			s.logger.Printf("设备 %s 在 %s 没有数据", deviceID, yesterdayLocal)
			continue
		}

		// 保存统计数据到MongoDB
		if err := s.statisticsService.SaveDeviceStatistics(stats); err != nil {
			s.logger.Printf("保存设备 %s 统计失败: %v", deviceID, err)
			errorCount++
			continue
		}

		successCount++
		s.logger.Printf("设备 %s 统计完成: 利用率=%.2f%%, 产量达成率=%.2f%%",
			deviceID, stats.UtilizationRate, stats.OutputRate)
	}

	s.logger.Printf("每日统计任务完成: 成功=%d, 失败=%d, 日期=%s", successCount, errorCount, yesterdayLocal)
}

// RunDailyStatisticsManually 手动执行每日统计任务
func (s *SchedulerService) RunDailyStatisticsManually(date string) error {
	s.logger.Printf("手动执行统计任务: 日期=%s", date)

	// 获取所有设备ID
	deviceIDs, err := s.statisticsService.getAllDeviceIDs()
	if err != nil {
		return err
	}

	s.logger.Printf("开始计算 %s 的设备统计，共 %d 个设备", date, len(deviceIDs))

	// 为每个设备计算统计数据
	successCount := 0
	errorCount := 0

	for _, deviceID := range deviceIDs {
		stats, err := s.statisticsService.CalculateDeviceStatistics(date, deviceID)
		if err != nil {
			s.logger.Printf("计算设备 %s 统计失败: %v", deviceID, err)
			errorCount++
			continue
		}

		if stats == nil {
			s.logger.Printf("设备 %s 在 %s 没有数据", deviceID, date)
			continue
		}

		// 保存统计数据到MongoDB
		if err := s.statisticsService.SaveDeviceStatistics(stats); err != nil {
			s.logger.Printf("保存设备 %s 统计失败: %v", deviceID, err)
			errorCount++
			continue
		}

		successCount++
		s.logger.Printf("设备 %s 统计完成: 利用率=%.2f%%, 产量达成率=%.2f%%",
			deviceID, stats.UtilizationRate, stats.OutputRate)
	}

	s.logger.Printf("手动统计任务完成: 成功=%d, 失败=%d, 日期=%s", successCount, errorCount, date)
	return nil
}

// GetSchedulerStatus 获取调度器状态
func (s *SchedulerService) GetSchedulerStatus() map[string]interface{} {
	entries := s.cron.Entries()

	status := map[string]interface{}{
		"running":    len(entries) > 0,
		"task_count": len(entries),
		"timezone":   "UTC",
		"tasks":      make([]map[string]interface{}, 0),
	}

	for _, entry := range entries {
		taskInfo := map[string]interface{}{
			"next_run_utc": entry.Next.Format("2006-01-02 15:04:05") + " UTC",
			"prev_run_utc": entry.Prev.Format("2006-01-02 15:04:05") + " UTC",
			"next_run":     entry.Next.Format("2006-01-02 15:04:05"),
			"prev_run":     entry.Prev.Format("2006-01-02 15:04:05"),
		}
		status["tasks"] = append(status["tasks"].([]map[string]interface{}), taskInfo)
	}

	return status
}
