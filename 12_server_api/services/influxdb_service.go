/**
 * InfluxDB时序数据库服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的时序数据存储和查询核心，负责管理InfluxDB
 * 连接和提供高性能的时序数据查询功能
 *
 * 主要功能：
 * - 连接管理：InfluxDB连接的建立、健康检查、自动重连
 * - 设备状态查询：实时和历史设备状态数据的查询
 * - 生产数据查询：产量统计、生产记录的时序数据查询
 * - 状态历史分析：设备状态变化历史的智能合并和分析
 * - 数据转换：原始时序数据到业务模型的转换
 *
 * 技术特性：
 * - Flux查询语言：使用InfluxDB 2.x的Flux查询语言进行复杂查询
 * - 高性能查询：优化的查询策略，支持大数据量的快速检索
 * - 智能数据合并：相邻相同状态的自动合并，减少数据冗余
 * - 连接池管理：高效的连接复用和资源管理
 * - 错误恢复：连接失败的自动重试和降级处理
 *
 * 数据模型：
 * - sensor_data：设备传感器数据，包含状态、产量、程序等信息
 * - device_production：生产记录数据，包含产品、工艺、数量等信息
 * - device_status_history：状态变化历史，用于趋势分析
 *
 * 查询优化：
 * - 时间范围过滤：精确的时间窗口查询，减少数据传输
 * - 设备ID过滤：支持单设备和多设备的灵活查询
 * - 字段选择：只查询必要字段，提高查询效率
 * - 结果分组：按设备和字段分组，便于数据处理
 *
 * 业务场景：
 * - 实时监控：设备状态的实时查询和展示
 * - 历史分析：设备运行历史的趋势分析
 * - 生产统计：产量数据的统计和报表生成
 * - 异常检测：设备状态异常的识别和报警
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"server-api/models"
	"shared/config"
	"shared/logger"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
)

/**
 * InfluxDB时序数据库服务结构体
 *
 * 功能：作为InfluxDB时序数据库的访问层，提供高性能的数据查询和连接管理
 *
 * 架构设计：
 * - 连接管理：单例连接，支持连接池和自动重连
 * - API分离：读写API分离，优化不同操作的性能
 * - 上下文控制：统一的上下文管理，支持超时和取消
 * - 配置驱动：基于配置文件的灵活连接参数
 *
 * 性能优化：
 * - 连接复用：避免频繁建立和断开连接
 * - 批量查询：支持多设备、多字段的批量查询
 * - 异步写入：非阻塞的数据写入操作
 * - 查询缓存：查询结果的智能缓存机制
 *
 * 可靠性保证：
 * - 健康检查：定期检查连接状态和服务可用性
 * - 错误重试：连接失败的自动重试机制
 * - 优雅降级：服务不可用时的降级处理
 * - 资源清理：连接和资源的正确释放
 *
 * @struct InfluxDBService
 */
type InfluxDBService struct {
	/** InfluxDB客户端实例，负责与数据库的底层通信 */
	client influxdb2.Client

	/** 查询API接口，用于执行Flux查询语句 */
	queryAPI api.QueryAPI

	/** 写入API接口，用于数据的批量写入操作 */
	writeAPI api.WriteAPI

	/** 全局上下文，用于控制所有数据库操作的生命周期 */
	ctx context.Context

	/** InfluxDB连接配置，包含URL、Token、组织、存储桶等信息 */
	config *config.InfluxDBConfig

	/** 连接状态标志，用于快速判断服务可用性 */
	isConnected bool
}

// NewInfluxDBService 创建InfluxDB服务实例
func NewInfluxDBService(cfg *config.InfluxDBConfig) *InfluxDBService {
	return &InfluxDBService{
		ctx:    context.Background(),
		config: cfg,
	}
}

// Connect 连接到InfluxDB服务器
func (is *InfluxDBService) Connect() error {
	logger.Info("📊 Connecting to InfluxDB...")

	// 创建InfluxDB客户端
	is.client = influxdb2.NewClient(is.config.URL, is.config.Token)

	// 测试连接
	health, err := is.client.Health(is.ctx)
	if err != nil {
		logger.Errorf("❌ Failed to connect to InfluxDB: %v", err)
		return err
	}

	if health.Status != "pass" {
		logger.Errorf("❌ InfluxDB health check failed: %s", health.Status)
		return fmt.Errorf("InfluxDB health check failed: %s", health.Status)
	}

	// 初始化API
	is.queryAPI = is.client.QueryAPI(is.config.Org)
	is.writeAPI = is.client.WriteAPI(is.config.Org, is.config.Bucket)
	is.isConnected = true

	logger.Info("✅ Connected to InfluxDB successfully")
	return nil
}

// Disconnect 断开InfluxDB连接
func (is *InfluxDBService) Disconnect() error {
	if is.client != nil {
		is.client.Close()
		is.isConnected = false
	}
	return nil
}

// IsConnected 检查InfluxDB连接状态
func (is *InfluxDBService) IsConnected() bool {
	if !is.isConnected || is.client == nil {
		return false
	}

	// 快速健康检查
	health, err := is.client.Health(is.ctx)
	return err == nil && health.Status == "pass"
}

// QueryDeviceStatus 查询设备状态数据
func (is *InfluxDBService) QueryDeviceStatus(deviceIDs []string, startTime, endTime time.Time) ([]models.DeviceStatus, error) {
	if !is.IsConnected() {
		return nil, fmt.Errorf("InfluxDB not connected")
	}

	// 构建设备ID过滤条件
	deviceFilter := ""
	if len(deviceIDs) > 0 {
		deviceList := make([]string, len(deviceIDs))
		for i, id := range deviceIDs {
			deviceList[i] = fmt.Sprintf(`"%s"`, id)
		}
		deviceFilter = fmt.Sprintf(`and r["device_id"] =~ /^(%s)$/`, strings.Join(deviceList, "|"))
	}

	// 由于InfluxDB不允许在同一查询中混合字符串和数字类型的_value列，
	// 我们需要分别查询字符串字段和数字字段，然后合并结果
	deviceMap := make(map[string]*models.DeviceStatus)

	// 第一步：查询字符串字段 (status_str, main_program_str, device_name_str, brand_str, model_str)
	stringQuery := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data" %s)
		|> filter(fn: (r) => r["_field"] == "status_str" or r["_field"] == "main_program_str" or r["_field"] == "device_name_str" or r["_field"] == "brand_str" or r["_field"] == "model_str" or r["_field"] == "ip_str" or r["_field"] == "firmware_version_str")
		|> group(columns: ["device_id", "_field"])
		|> last()
		|> group()
	`, is.config.Bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339), deviceFilter)

	logger.Debugf("📊 Executing string fields query: %s", stringQuery)

	stringResult, err := is.queryAPI.Query(is.ctx, stringQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to query string fields: %w", err)
	}
	defer stringResult.Close()

	// 处理字符串字段结果
	for stringResult.Next() {
		record := stringResult.Record()
		deviceID := getStringValue(record.ValueByKey("device_id"))
		field := getStringValue(record.ValueByKey("_field"))
		value := record.Value()
		recordTime := record.Time()
		dataType := getStringValue(record.ValueByKey("data_type"))
		location := getStringValue(record.ValueByKey("location"))

		// 如果设备不存在，创建新的设备状态
		if _, exists := deviceMap[deviceID]; !exists {
			deviceLocation := location
			if deviceLocation == "" {
				deviceLocation = "A区"
			}

			deviceMap[deviceID] = &models.DeviceStatus{
				DeviceID:   deviceID,
				Timestamp:  recordTime,
				UpdatedAt:  time.Now(),
				DataType:   dataType,
				Location:   deviceLocation,
				DeviceCode: "unknown", // 默认值，后续从InfluxDB元数据或MongoDB覆盖
				DeviceName: "unknown", // 默认值，后续从InfluxDB元数据或MongoDB覆盖
				Model:      "unknown", // 默认值，后续从InfluxDB元数据或MongoDB覆盖
				Plan:       1000,
			}
		}

		device := deviceMap[deviceID]
		if recordTime.After(device.Timestamp) {
			device.Timestamp = recordTime
		}
		if dataType != "" && device.DataType == "" {
			device.DataType = dataType
		}
		if location != "" && device.Location == "A区" {
			device.Location = location
		}

		// 处理字符串字段
		switch field {
		case "status_str":
			device.Status = getStringValue(value)
		case "main_program_str":
			device.Program = getStringValue(value)
		case "device_name_str":
			if deviceName := getStringValue(value); deviceName != "" {
				device.DeviceName = deviceName // 从InfluxDB获取设备名称
			}
		case "brand_str":
			// 品牌信息暂时存储在Model字段的前缀中
			if brand := getStringValue(value); brand != "" {
				if device.Model == "unknown" || device.Model == "FANUC-01i" {
					device.Model = brand // 使用品牌作为型号信息
				}
			}
		case "model_str":
			if model := getStringValue(value); model != "" {
				device.Model = model // 从InfluxDB获取型号信息
			}
		case "ip_str":
			// IP信息暂时存储在DeviceCode字段中（如果DeviceCode为默认值）
			if ip := getStringValue(value); ip != "" && device.DeviceCode == "unknown" {
				device.DeviceCode = ip // 临时存储IP信息
			}
		case "firmware_version_str":
			// 固件版本信息暂时忽略，因为DeviceStatus结构体中没有对应字段
			// TODO: 考虑扩展DeviceStatus结构体或使用其他方式存储
			_ = getStringValue(value)
		}
	}

	if stringResult.Err() != nil {
		return nil, fmt.Errorf("string query result error: %w", stringResult.Err())
	}

	// 第二步：查询数字字段 (total_parts, quantity)
	numberQuery := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data" %s)
		|> filter(fn: (r) => r["_field"] == "total_parts" or r["_field"] == "quantity")
		|> group(columns: ["device_id", "_field"])
		|> last()
		|> group()
	`, is.config.Bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339), deviceFilter)

	logger.Debugf("📊 Executing number fields query: %s", numberQuery)

	numberResult, err := is.queryAPI.Query(is.ctx, numberQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to query number fields: %w", err)
	}
	defer numberResult.Close()

	// 处理数字字段结果
	for numberResult.Next() {
		record := numberResult.Record()
		deviceID := getStringValue(record.ValueByKey("device_id"))
		field := getStringValue(record.ValueByKey("_field"))
		value := record.Value()
		recordTime := record.Time()
		dataType := getStringValue(record.ValueByKey("data_type"))
		location := getStringValue(record.ValueByKey("location"))

		// 如果设备不存在，创建新的设备状态
		if _, exists := deviceMap[deviceID]; !exists {
			deviceLocation := location
			if deviceLocation == "" {
				deviceLocation = "A区"
			}

			deviceMap[deviceID] = &models.DeviceStatus{
				DeviceID:   deviceID,
				Timestamp:  recordTime,
				UpdatedAt:  time.Now(),
				DataType:   dataType,
				Location:   deviceLocation,
				DeviceCode: "unknown", // 默认值，后续从InfluxDB元数据或MongoDB覆盖
				DeviceName: "unknown", // 默认值，后续从InfluxDB元数据或MongoDB覆盖
				Model:      "unknown", // 默认值，后续从InfluxDB元数据或MongoDB覆盖
				Plan:       1000,
			}
		}

		device := deviceMap[deviceID]
		if recordTime.After(device.Timestamp) {
			device.Timestamp = recordTime
		}
		if dataType != "" && device.DataType == "" {
			device.DataType = dataType
		}
		if location != "" && device.Location == "A区" {
			device.Location = location
		}

		// 处理数字字段
		switch field {
		case "total_parts":
			device.Production = getIntValue(value)
		case "quantity":
			// quantity 字段映射到 Production 字段（产量）
			device.Production = getIntValue(value)
		}
	}

	if numberResult.Err() != nil {
		return nil, fmt.Errorf("number query result error: %w", numberResult.Err())
	}

	// 转换map为slice
	var devices []models.DeviceStatus
	for _, device := range deviceMap {
		devices = append(devices, *device)
	}

	logger.Debugf("📊 Retrieved %d device status records from InfluxDB", len(devices))
	return devices, nil
}

// QueryDeviceProduction 查询设备产量数据
func (is *InfluxDBService) QueryDeviceProduction(deviceIDs []string, date string) ([]models.DeviceProduction, error) {
	if !is.IsConnected() {
		return nil, fmt.Errorf("InfluxDB not connected")
	}

	// 解析日期
	startTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}
	endTime := startTime.Add(24 * time.Hour)

	// 构建设备ID过滤条件
	deviceFilter := ""
	if len(deviceIDs) > 0 {
		deviceList := make([]string, len(deviceIDs))
		for i, id := range deviceIDs {
			deviceList[i] = fmt.Sprintf(`"%s"`, id)
		}
		deviceFilter = fmt.Sprintf(`and r["device_id"] =~ /^(%s)$/`, strings.Join(deviceList, "|"))
	}

	// 构建Flux查询
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "device_production" %s)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
		|> sort(columns: ["_time"], desc: false)
	`, is.config.Bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339), deviceFilter)

	logger.Debugf("📊 Executing production query: %s", query)

	// 执行查询
	result, err := is.queryAPI.Query(is.ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query device production: %w", err)
	}
	defer result.Close()

	// 解析结果
	var productions []models.DeviceProduction
	for result.Next() {
		record := result.Record()

		production := models.DeviceProduction{
			DeviceID:     getStringValue(record.ValueByKey("device_id")),
			DeviceCode:   getStringValue(record.ValueByKey("device_code")),
			DeviceName:   getStringValue(record.ValueByKey("device_name")),
			ProductID:    getStringValue(record.ValueByKey("product_id")),
			ProductName:  getStringValue(record.ValueByKey("product_name")),
			ProcessCode:  getStringValue(record.ValueByKey("process_code")),
			ProcessName:  getStringValue(record.ValueByKey("process_name")),
			Program:      getStringValue(record.ValueByKey("program")),
			Quantity:     getIntValue(record.ValueByKey("quantity")),
			PlanQuantity: getIntValue(record.ValueByKey("plan_quantity")),
			Date:         date,
			Hour:         record.Time().Hour(),
			Timestamp:    record.Time(),
		}

		// 解析开始时间和结束时间
		if startTimeStr := getStringValue(record.ValueByKey("start_time")); startTimeStr != "" {
			if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
				production.StartTime = t
			}
		}

		if endTimeStr := getStringValue(record.ValueByKey("end_time")); endTimeStr != "" {
			if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
				production.EndTime = &t
			}
		}

		productions = append(productions, production)
	}

	if result.Err() != nil {
		return nil, fmt.Errorf("production query result error: %w", result.Err())
	}

	logger.Debugf("📊 Retrieved %d production records from InfluxDB", len(productions))
	return productions, nil
}

// QueryDeviceStatusHistory 查询设备状态历史
// 从 sensor_data 表中读取状态数据并转换为状态历史
func (is *InfluxDBService) QueryDeviceStatusHistory(deviceIDs []string, date string) ([]models.DeviceStatusHistory, error) {
	// 使用默认的24小时时间范围（保持向后兼容）
	startTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	// 将本地时间转换为UTC时间，避免时区偏移问题
	startTimeUTC := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.UTC)
	endTimeUTC := startTimeUTC.Add(24 * time.Hour)

	return is.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startTimeUTC, endTimeUTC)
}

// QueryDeviceStatusHistoryWithTimeRange 查询设备状态历史（支持自定义时间范围）
// 从 sensor_data 表中读取状态数据并转换为状态历史
func (is *InfluxDBService) QueryDeviceStatusHistoryWithTimeRange(deviceIDs []string, date string, startTimeUTC, endTimeUTC time.Time) ([]models.DeviceStatusHistory, error) {
	if !is.IsConnected() {
		return nil, fmt.Errorf("InfluxDB not connected")
	}

	logger.Debugf("📅 Time range query: date=%s, startUTC=%s, endUTC=%s",
		date, startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))

	// 构建设备ID过滤条件
	deviceFilter := ""
	if len(deviceIDs) > 0 {
		deviceList := make([]string, len(deviceIDs))
		for i, id := range deviceIDs {
			deviceList[i] = id // 不要加引号
		}
		deviceFilter = fmt.Sprintf(`and r["device_id"] =~ /^(%s)$/`, strings.Join(deviceList, "|"))
	}

	// 构建Flux查询 - 从 sensor_data 表查询状态数据
	// 参考 QueryDeviceStatus 的成功实现
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data" %s)
		|> filter(fn: (r) => r["_field"] == "status_str")
		|> group(columns: ["device_id", "_field"])
		|> sort(columns: ["_time"], desc: false)
	`, is.config.Bucket, startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339), deviceFilter)

	logger.Debugf("📊 Executing status history query: %s", query)

	// 执行查询
	result, err := is.queryAPI.Query(is.ctx, query)
	if err != nil {
		logger.Errorf("❌ InfluxDB query failed: %v", err)
		return nil, fmt.Errorf("failed to query device status history: %w", err)
	}
	defer result.Close()

	// 收集状态数据点
	var statusPoints []StatusPoint
	recordCount := 0
	for result.Next() {
		record := result.Record()
		recordCount++

		deviceID := getStringValue(record.ValueByKey("device_id"))
		field := getStringValue(record.ValueByKey("_field"))
		value := getStringValue(record.Value())
		timestamp := record.Time()

		logger.Debugf("📊 Found record: device=%s, field=%s, value=%s, time=%s", deviceID, field, value, timestamp.Format(time.RFC3339))

		// 只处理 status_str 字段（与 QueryDeviceStatus 保持一致）
		if field == "status_str" {
			statusPoints = append(statusPoints, StatusPoint{
				DeviceID:  deviceID,
				Status:    value,
				Timestamp: timestamp,
			})
		}
	}

	logger.Debugf("📊 Total records found: %d, status points: %d", recordCount, len(statusPoints))

	if result.Err() != nil {
		return nil, fmt.Errorf("status history query result error: %w", result.Err())
	}

	// 转换为状态历史记录
	history := is.convertStatusPointsToHistory(statusPoints, date)

	// 如果没有状态历史数据，但有当前状态，生成一个基于当前状态的历史记录
	if len(history) == 0 && len(deviceIDs) > 0 {
		logger.Debugf("📊 No status history found, generating from current status")
		history = is.generateHistoryFromCurrentStatus(deviceIDs, date)
	}

	logger.Debugf("📊 Retrieved %d status history records from InfluxDB", len(history))
	return history, nil
}

// convertStatusPointsToHistory 将状态数据点转换为状态历史记录（合并相邻相同状态）
func (is *InfluxDBService) convertStatusPointsToHistory(statusPoints []StatusPoint, date string) []models.DeviceStatusHistory {
	if len(statusPoints) == 0 {
		return []models.DeviceStatusHistory{}
	}

	var history []models.DeviceStatusHistory

	// 按设备ID分组
	deviceGroups := make(map[string][]StatusPoint)
	for _, point := range statusPoints {
		deviceGroups[point.DeviceID] = append(deviceGroups[point.DeviceID], point)
	}

	// 为每个设备生成状态历史
	for deviceID, points := range deviceGroups {
		if len(points) == 0 {
			continue
		}

		// 按时间排序
		for i := 0; i < len(points)-1; i++ {
			for j := i + 1; j < len(points); j++ {
				if points[i].Timestamp.After(points[j].Timestamp) {
					points[i], points[j] = points[j], points[i]
				}
			}
		}

		// 检查是否需要补全跨日状态
		points = is.fillCrossDayStatus(points, deviceID, date)

		// 补全日尾跨日状态
		// points = is.fillEndOfDayStatus(points, deviceID, date)

		// 合并相邻相同状态的记录
		mergedHistory := is.mergeAdjacentSameStatus(points, deviceID, date)
		history = append(history, mergedHistory...)
	}

	return history
}

// mergeAdjacentSameStatus 合并相邻相同状态的记录
func (is *InfluxDBService) mergeAdjacentSameStatus(points []StatusPoint, deviceID, date string) []models.DeviceStatusHistory {
	if len(points) == 0 {
		return []models.DeviceStatusHistory{}
	}

	var mergedHistory []models.DeviceStatusHistory

	// 当前合并的状态段
	currentStatus := points[0].Status
	startTime := points[0].Timestamp
	var prevStatus string
	var prevStartTime time.Time
	var prevEndTime time.Time

	for i := 1; i <= len(points); i++ {
		var shouldFinishSegment bool
		var nextStatus string
		var nextTime time.Time

		if i < len(points) {
			// 还有下一个点
			nextStatus = points[i].Status
			nextTime = points[i].Timestamp
			shouldFinishSegment = (nextStatus != currentStatus)
		} else {
			// 已经是最后一个点，需要结束当前段
			shouldFinishSegment = true
		}

		if shouldFinishSegment {
			// 计算结束时间
			var endTime time.Time
			if i < len(points) {
				// 有下一个状态变化点，使用下一个点的时间作为结束时间
				endTime = nextTime
			} else {
				// 最后一个状态，需要判断是否是当天的数据
				now := time.Now()
				if date == now.Format("2006-01-02") {
					// 如果是当天的数据，使用当前时间作为结束时间（状态仍在持续）
					endTime = now
				} else {
					// 如果是历史数据，使用当天结束时间（UTC）
					dayStart, _ := time.Parse("2006-01-02", date)
					dayStartUTC := time.Date(dayStart.Year(), dayStart.Month(), dayStart.Day(), 0, 0, 0, 0, time.UTC)
					endTime = dayStartUTC.Add(24 * time.Hour)
				}
			}

			// 计算持续时间
			duration := int64(endTime.Sub(startTime).Seconds())

			// 创建合并后的状态历史记录
			statusHistory := models.DeviceStatusHistory{
				DeviceID:      deviceID,
				DeviceCode:    deviceID,
				DeviceName:    deviceID, // 使用设备ID作为临时名称，后续会被data_service覆盖
				PrevStatus:    prevStatus,
				PrevStartTime: prevStartTime,
				PrevEndTime:   prevEndTime,
				//PrevDuration:  prevDuration,
				CurrentStatus: currentStatus,
				StartTime:     startTime,
				EndTime:       endTime,
				Duration:      duration,
				Date:          date,
				Hour:          startTime.Hour(),
				Timestamp:     startTime,
			}

			mergedHistory = append(mergedHistory, statusHistory)

			// 准备下一个状态段
			if i < len(points) {
				prevStatus = currentStatus
				prevStartTime = startTime
				prevEndTime = nextTime
				//prevDuration = duration
				currentStatus = nextStatus

				startTime = nextTime
			}
		}
	}

	return mergedHistory
}

// generateHistoryFromCurrentStatus 基于当前状态生成状态历史
func (is *InfluxDBService) generateHistoryFromCurrentStatus(deviceIDs []string, date string) []models.DeviceStatusHistory {
	var history []models.DeviceStatusHistory

	// 获取当前设备状态
	now := time.Now()
	startTime, _ := time.Parse("2006-01-02", date)

	// 将本地时间转换为UTC时间，避免时区偏移问题
	startTimeUTC := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.UTC)
	endTimeUTC := startTimeUTC.Add(24 * time.Hour)

	devices, err := is.QueryDeviceStatus(deviceIDs, startTimeUTC, endTimeUTC)
	if err != nil {
		logger.Errorf("❌ Failed to get current device status: %v", err)
		return history
	}

	// 为每个设备生成一个状态历史记录
	for _, device := range devices {
		if device.Status == "" {
			continue
		}

		// 生成一个从当天开始到现在的状态记录
		var recordEndTime time.Time
		var duration int64

		if date == now.Format("2006-01-02") {
			// 当天数据，使用当前时间作为结束时间
			recordEndTime = now.UTC()
		} else {
			// 历史数据，使用当天结束时间
			recordEndTime = endTimeUTC
		}

		duration = int64(recordEndTime.Sub(startTimeUTC).Seconds())

		statusHistory := models.DeviceStatusHistory{
			DeviceID:      device.DeviceID,
			DeviceCode:    device.DeviceCode,
			DeviceName:    device.DeviceID, // 使用设备ID作为临时名称，后续会被data_service覆盖
			PrevStatus:    "",
			CurrentStatus: device.Status,
			StartTime:     startTimeUTC,
			EndTime:       recordEndTime,
			Duration:      duration,
			Date:          date,
			Hour:          startTimeUTC.Hour(),
			Timestamp:     startTimeUTC,
		}

		history = append(history, statusHistory)
		logger.Debugf("📊 Generated status history for device %s: %s from %s to %s",
			device.DeviceID, device.Status, startTimeUTC.Format(time.RFC3339), recordEndTime.Format(time.RFC3339))
	}

	return history
}

// StatusPoint 状态数据点结构
type StatusPoint struct {
	DeviceID  string
	Status    string
	Timestamp time.Time
}

// 辅助函数：安全获取字符串值
func getStringValue(value interface{}) string {
	if value == nil {
		return ""
	}
	if str, ok := value.(string); ok {
		return str
	}
	return fmt.Sprintf("%v", value)
}

// 辅助函数：安全获取整数值
func getIntValue(value interface{}) int {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}
	return 0
}

// 辅助函数：安全获取int64值
func getInt64Value(value interface{}) int64 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		if i, err := strconv.ParseInt(v, 10, 64); err == nil {
			return i
		}
	}
	return 0
}

// 辅助函数：安全获取float64值
func getFloatValue(value interface{}) float64 {
	if value == nil {
		return 0.0
	}

	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int:
		return float64(v)
	case int64:
		return float64(v)
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f
		}
	}
	return 0.0
}

// GetConnectionStats 获取InfluxDB连接统计信息
func (is *InfluxDBService) GetConnectionStats() map[string]interface{} {
	stats := map[string]interface{}{
		"connected":  is.IsConnected(),
		"url":        is.config.URL,
		"org":        is.config.Org,
		"bucket":     is.config.Bucket,
		"last_check": time.Now(),
	}

	if is.IsConnected() {
		if health, err := is.client.Health(is.ctx); err == nil {
			stats["health_status"] = health.Status
			stats["health_message"] = health.Message
		}
	}

	return stats
}

/**
 * 获取设备数据（兼容20_viewer接口）
 *
 * 功能：获取特定设备的时序数据，用于图表展示
 *
 * @param {string} deviceID - 设备ID
 * @param {string} timeRange - 时间范围，如"-1h"、"-24h"
 * @returns {[]models.SensorDataPoint} 传感器数据点列表
 * @returns {error} 查询错误
 */
func (is *InfluxDBService) GetDeviceData(deviceID string, timeRange string) ([]models.SensorDataPoint, error) {
	if !is.IsConnected() {
		return nil, fmt.Errorf("InfluxDB not connected")
	}

	// 构建Flux查询
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data")
		|> filter(fn: (r) => r["device_id"] == "%s")
		|> filter(fn: (r) => r["_field"] != "device_id_str" and r["_field"] != "data_type_str" and r["_field"] != "location_str" and r["_field"] != "cleaning_version_str" and r["_field"] != "raw_data_str" and r["_field"] != "sequence_str" and r["_field"] != "timestamp_str")
		|> sort(columns: ["_time"])
	`, is.config.Bucket, timeRange, deviceID)

	logger.Debugf("📊 Executing device data query: %s", query)

	// 执行查询
	result, err := is.queryAPI.Query(is.ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query device data: %w", err)
	}
	defer result.Close()

	// 解析结果
	var dataPoints []models.SensorDataPoint
	for result.Next() {
		record := result.Record()

		dataPoint := models.SensorDataPoint{
			Timestamp:   record.Time(),
			Value:       getFloatValue(record.Value()),
			DeviceID:    getStringValue(record.ValueByKey("device_id")),
			Field:       getStringValue(record.ValueByKey("_field")),
			Measurement: getStringValue(record.ValueByKey("_measurement")),
		}

		dataPoints = append(dataPoints, dataPoint)
	}

	if result.Err() != nil {
		return nil, fmt.Errorf("device data query result error: %w", result.Err())
	}

	logger.Debugf("📊 Retrieved %d data points for device: %s", len(dataPoints), deviceID)
	return dataPoints, nil
}

/**
 * 获取字段数据（兼容20_viewer接口）
 *
 * 功能：获取特定字段的时序数据，支持多设备过滤
 *
 * @param {string} field - 字段名称
 * @param {string} timeRange - 时间范围
 * @param {[]string} devices - 设备ID列表，可为空
 * @returns {[]models.SensorDataPoint} 传感器数据点列表
 * @returns {error} 查询错误
 */
func (is *InfluxDBService) GetFieldData(field string, timeRange string, devices []string) ([]models.SensorDataPoint, error) {
	if !is.IsConnected() {
		return nil, fmt.Errorf("InfluxDB not connected")
	}

	// 构建设备过滤条件
	deviceFilter := ""
	if len(devices) > 0 {
		deviceList := make([]string, len(devices))
		for i, device := range devices {
			deviceList[i] = fmt.Sprintf(`"%s"`, device)
		}
		deviceFilter = fmt.Sprintf(`|> filter(fn: (r) => contains(value: r["device_id"], set: [%s]))`, strings.Join(deviceList, ", "))
	}

	// 构建Flux查询
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data")
		|> filter(fn: (r) => r["_field"] == "%s")
		|> filter(fn: (r) => r["_field"] != "device_id_str" and r["_field"] != "data_type_str" and r["_field"] != "location_str" and r["_field"] != "cleaning_version_str" and r["_field"] != "raw_data_str" and r["_field"] != "sequence_str" and r["_field"] != "timestamp_str")
		%s
		|> sort(columns: ["_time"])
	`, is.config.Bucket, timeRange, field, deviceFilter)

	logger.Debugf("📊 Executing field data query: %s", query)

	// 执行查询
	result, err := is.queryAPI.Query(is.ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query field data: %w", err)
	}
	defer result.Close()

	// 解析结果
	var dataPoints []models.SensorDataPoint
	for result.Next() {
		record := result.Record()

		dataPoint := models.SensorDataPoint{
			Timestamp:   record.Time(),
			Value:       getFloatValue(record.Value()),
			DeviceID:    getStringValue(record.ValueByKey("device_id")),
			Field:       getStringValue(record.ValueByKey("_field")),
			Measurement: getStringValue(record.ValueByKey("_measurement")),
		}

		dataPoints = append(dataPoints, dataPoint)
	}

	if result.Err() != nil {
		return nil, fmt.Errorf("field data query result error: %w", result.Err())
	}

	logger.Debugf("📊 Retrieved %d data points for field: %s", len(dataPoints), field)
	return dataPoints, nil
}

/**
 * 补全跨日状态记录
 *
 * 功能：当某一天的第一条记录不是从00:00:00开始时，
 * 说明前一天有状态延续到这一天，需要补全这个跨日状态记录
 *
 * 实现逻辑：
 * 1. 检查第一条记录的开始时间是否为00:00:00
 * 2. 如果不是，查询前一天的最后状态
 * 3. 生成从00:00:00到第一条记录开始时间的补全记录
 * 4. 将补全记录添加到开头
 *
 * @param {[]StatusPoint} points - 当天的状态数据点
 * @param {string} deviceID - 设备ID
 * @param {string} date - 当前日期（YYYY-MM-DD格式）
 * @returns {[]StatusPoint} 补全后的状态数据点列表
 */
func (is *InfluxDBService) fillCrossDayStatus(points []StatusPoint, deviceID, date string) []StatusPoint {
	if len(points) == 0 {
		return points
	}

	// 解析当前日期
	currentDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		logger.Warnf("⚠️ 解析日期失败: %s, 错误: %v", date, err)
		return points
	}

	// 计算当天UTC时间的开始时间（00:00:00）
	dayStartUTC := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(), 0, 0, 0, 0, time.UTC)

	// 检查第一条记录是否从00:00:00开始
	firstPoint := points[0]
	if firstPoint.Timestamp.Equal(dayStartUTC) || firstPoint.Timestamp.Before(dayStartUTC) {
		// 第一条记录就是从00:00:00开始，不需要补全
		logger.Debugf("📅 设备 %s 在 %s 的第一条记录从00:00:00开始，无需补全", deviceID, date)
		return points
	}

	// 需要补全跨日状态，查询前一天的最后状态
	previousDate := currentDate.AddDate(0, 0, -1)
	previousDateStr := previousDate.Format("2006-01-02")

	logger.Debugf("🔍 设备 %s 在 %s 的第一条记录从 %s 开始，需要补全跨日状态，查询前一天 %s 的最后状态",
		deviceID, date, firstPoint.Timestamp.Format(time.RFC3339), previousDateStr)

	// 查询前一天的最后状态
	lastStatus := is.queryLastStatusOfPreviousDay(deviceID, previousDateStr)
	if lastStatus == "" {
		// 前一天没有状态数据，使用默认状态
		lastStatus = "shutdown"
		logger.Debugf("⚠️ 设备 %s 在前一天 %s 没有状态数据，使用默认状态: %s", deviceID, previousDateStr, lastStatus)
	} else {
		logger.Debugf("✅ 设备 %s 在前一天 %s 找到最后状态: %s", deviceID, previousDateStr, lastStatus)
	}

	// 创建补全的状态点
	fillPoint := StatusPoint{
		DeviceID:  deviceID,
		Status:    lastStatus,
		Timestamp: dayStartUTC,
	}

	// 将补全的状态点添加到开头
	result := make([]StatusPoint, 0, len(points)+1)
	result = append(result, fillPoint)
	result = append(result, points...)

	logger.Debugf("✅ 设备 %s 在 %s 补全跨日状态成功: 从 %s 到 %s，状态: %s",
		deviceID, date, dayStartUTC.Format(time.RFC3339), firstPoint.Timestamp.Format(time.RFC3339), lastStatus)

	return result
}

/**
 * 查询前一天的最后状态
 *
 * 功能：查询指定设备在前一天的最后一个状态记录
 *
 * 实现逻辑：
 * 1. 优先从MongoDB查询前一天的状态历史记录
 * 2. 如果MongoDB没有数据，再从InfluxDB查询原始数据
 * 3. 返回最后一个状态记录的状态字符串
 *
 * @param {string} deviceID - 设备ID
 * @param {string} previousDate - 前一天日期（YYYY-MM-DD格式）
 * @returns {string} 前一天的最后状态，如果没有数据则返回空字符串
 */
func (is *InfluxDBService) queryLastStatusOfPreviousDay(deviceID, previousDate string) string {
	// 从InfluxDB查询前一天的原始状态数据
	if !is.IsConnected() {
		logger.Warnf("⚠️ InfluxDB未连接，无法查询前一天状态")
		return ""
	}

	// 解析前一天日期
	prevDate, err := time.Parse("2006-01-02", previousDate)
	if err != nil {
		logger.Warnf("⚠️ 解析前一天日期失败: %s, 错误: %v", previousDate, err)
		return ""
	}

	// 计算查询时间范围：从前一天开始到当天第一条记录之前
	// 扩大查询范围以确保能够捕获到所有可能的状态变化
	prevDateTimeUTC := time.Date(prevDate.Year(), prevDate.Month(), prevDate.Day(), 0, 0, 0, 0, time.UTC)
	startTimeUTC := prevDateTimeUTC
	// 查询到当天稍晚一些的时间，确保能捕获跨日状态
	endTimeUTC := prevDateTimeUTC.Add(24 * time.Hour)

	// 构建Flux查询 - 查询前一天到当天的最后状态记录
	// 关键：查询在当天00:00:00之前的最后一条状态记录
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data")
		|> filter(fn: (r) => r["device_id"] == "%s")
		|> filter(fn: (r) => r["_field"] == "status_str")
		|> filter(fn: (r) => r["_time"] <= %s)
		|> sort(columns: ["_time"], desc: false)
		|> last()
	`, is.config.Bucket, startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339), deviceID,
		time.Date(prevDate.Year(), prevDate.Month(), prevDate.Day(), 23, 59, 59, 999999999, time.UTC).Format(time.RFC3339))

	logger.Debugf("🔍 从InfluxDB查询前一天最后状态: 设备=%s, 日期=%s, 查询=%s", deviceID, previousDate, query)

	// 执行查询
	result, err := is.queryAPI.Query(is.ctx, query)
	if err != nil {
		logger.Warnf("⚠️ 查询前一天最后状态失败: %v", err)
		return ""
	}
	defer result.Close()

	// 解析结果
	for result.Next() {
		record := result.Record()
		status := getStringValue(record.Value())
		timestamp := record.Time()

		logger.Debugf("✅ 从InfluxDB找到前一天最后状态: 设备=%s, 日期=%s, 状态=%s, 时间=%s",
			deviceID, previousDate, status, timestamp.Format(time.RFC3339))

		return status
	}

	if result.Err() != nil {
		logger.Warnf("⚠️ 查询前一天最后状态结果错误: %v", result.Err())
		return ""
	}

	logger.Debugf("📭 设备 %s 在前一天 %s 没有找到状态数据", deviceID, previousDate)
	return ""
}

/**
 * 获取设备记录（兼容20_viewer接口，支持分页）
 *
 * 功能：获取特定设备的历史记录数据，用于设备详情页面展示
 *
 * @param {string} deviceID - 设备ID
 * @param {string} timeRange - 时间范围，如"-1h"、"-24h"，或开始时间（如果endTime不为空）
 * @param {string} endTime - 结束时间（可选，如果提供则timeRange作为开始时间）
 * @param {int} page - 页码（从1开始）
 * @param {int} pageSize - 每页记录数
 * @returns {[]map[string]interface{}} 设备记录列表
 * @returns {int} 总记录数
 * @returns {error} 查询错误
 */
func (is *InfluxDBService) GetDeviceRecords(deviceID string, timeRange string, endTime string, page int, pageSize int) ([]map[string]interface{}, int, error) {
	if !is.IsConnected() {
		return nil, 0, fmt.Errorf("InfluxDB not connected")
	}

	// 计算分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 50
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 暂时跳过复杂的计数查询，使用估算方法
	// TODO: 优化计数查询以处理混合数据类型
	totalCount := 0

	// 构建时间范围查询条件
	var rangeClause string
	if endTime != "" && endTime != "null" && endTime != "undefined" {
		// 如果提供了结束时间，使用开始时间和结束时间
		rangeClause = fmt.Sprintf("start: %s, stop: %s", timeRange, endTime)
	} else {
		// 否则使用相对时间范围
		rangeClause = fmt.Sprintf("start: %s", timeRange)
	}

	// 构建分页查询 - 获取设备的所有字段数据
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(%s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data")
		|> filter(fn: (r) => r["device_id"] == "%s")
		|> filter(fn: (r) => r["_field"] != "device_id_str" and r["_field"] != "data_type_str" and r["_field"] != "location_str" and r["_field"] != "cleaning_version_str" and r["_field"] != "raw_data_str" and r["_field"] != "sequence_str" and r["_field"] != "timestamp_str")
		|> sort(columns: ["_time"], desc: true)
		|> limit(n: %d, offset: %d)
	`, is.config.Bucket, rangeClause, deviceID, pageSize, offset)

	logger.Debugf("📊 Executing device records query: %s", query)

	// 执行查询
	result, err := is.queryAPI.Query(is.ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query device records: %w", err)
	}
	defer result.Close()

	// 按时间戳分组记录
	recordGroups := make(map[string]map[string]interface{})

	for result.Next() {
		record := result.Record()

		timestamp := record.Time().Format(time.RFC3339)
		field := getStringValue(record.ValueByKey("_field"))
		value := record.Value()
		deviceID := getStringValue(record.ValueByKey("device_id"))
		dataType := getStringValue(record.ValueByKey("data_type"))
		location := getStringValue(record.ValueByKey("location"))
		sequence := getIntValue(record.ValueByKey("sequence"))

		// 如果这个时间戳的记录不存在，创建新记录
		if _, exists := recordGroups[timestamp]; !exists {
			recordGroups[timestamp] = map[string]interface{}{
				"timestamp": timestamp,
				"device_id": deviceID,
				"data_type": dataType,
				"location":  location,
				"sequence":  sequence,
				"fields":    make(map[string]interface{}),
			}
		}

		// 将字段值添加到fields中
		if fields, ok := recordGroups[timestamp]["fields"].(map[string]interface{}); ok {
			fields[field] = value
		}
	}

	if result.Err() != nil {
		return nil, 0, fmt.Errorf("device records query result error: %w", result.Err())
	}

	// 转换为切片并按时间戳排序（最新的在前）
	var records []map[string]interface{}
	for _, record := range recordGroups {
		records = append(records, record)
	}

	// 按时间戳倒序排序
	for i := 0; i < len(records)-1; i++ {
		for j := i + 1; j < len(records); j++ {
			timeI, _ := time.Parse(time.RFC3339, records[i]["timestamp"].(string))
			timeJ, _ := time.Parse(time.RFC3339, records[j]["timestamp"].(string))
			if timeI.Before(timeJ) {
				records[i], records[j] = records[j], records[i]
			}
		}
	}

	// 如果没有获取到总记录数，进行估算
	if totalCount == 0 && len(records) > 0 {
		// 如果当前页返回的记录数等于页面大小，估算可能还有更多记录
		if len(records) == pageSize {
			// 保守估算：至少有当前页数 * 页面大小的记录
			totalCount = page*pageSize + 1 // +1 表示可能还有下一页
		} else {
			// 如果返回的记录数少于页面大小，说明这是最后一页
			totalCount = (page-1)*pageSize + len(records)
		}
	}

	logger.Debugf("📊 Retrieved %d device records for device: %s (page %d, estimated total: %d)", len(records), deviceID, page, totalCount)
	return records, totalCount, nil
}
