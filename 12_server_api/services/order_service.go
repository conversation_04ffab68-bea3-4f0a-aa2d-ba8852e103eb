package services

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"server-api/db"
	"server-api/models"
)

// OrderService 订单管理服务
type OrderService struct {
	mongoManager *db.MongoDBManager
}

// NewOrderService 创建订单服务实例（使用统一的MongoDB管理器）
func NewOrderService(mongoManager *db.MongoDBManager) *OrderService {
	return &OrderService{
		mongoManager: mongoManager,
	}
}

// getCollection 获取指定名称的集合（内部辅助方法）
func (s *OrderService) getCollection(collectionName string) (*mongo.Collection, error) {
	if s.mongoManager == nil {
		return nil, fmt.Errorf("MongoDB manager is nil")
	}
	database := s.mongoManager.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("database not available")
	}
	return database.Collection(collectionName), nil
}

// GetOrders 获取订单列表
func (s *OrderService) GetOrders(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("orders")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"order_number": bson.M{"$regex": params.Search, "$options": "i"}},
			{"customer_name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"product_name": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}
	if params.Status != "" {
		filter["status"] = params.Status
	}

	// 计算总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count orders: %v", err)
	}

	// 构建排序
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sort := bson.D{{Key: sortBy, Value: 1}}
	if params.SortDesc {
		sort = bson.D{{Key: sortBy, Value: -1}}
	}

	// 分页查询
	skip := (params.Page - 1) * params.PageSize
	opts := options.Find().
		SetSort(sort).
		SetSkip(int64(skip)).
		SetLimit(int64(params.PageSize))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find orders: %v", err)
	}
	defer cursor.Close(ctx)

	var orders []models.Order
	if err = cursor.All(ctx, &orders); err != nil {
		return nil, fmt.Errorf("failed to decode orders: %v", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	return &models.ListResponse{
		Data:       orders,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(order *models.Order) error {
	collection, err := s.getCollection("orders")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 检查订单号是否已存在
	count, err := collection.CountDocuments(ctx, bson.M{"order_number": order.OrderNumber})
	if err != nil {
		return fmt.Errorf("failed to check order number: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("order number already exists")
	}

	// 计算总金额
	var totalAmount float64
	if len(order.Products) > 0 {
		// 多产品模式：计算所有产品的总金额
		for _, product := range order.Products {
			totalAmount += float64(product.Quantity) * product.UnitPrice
		}
	} else {
		// 单产品兼容模式
		totalAmount = float64(order.Quantity) * order.UnitPrice
	}
	order.TotalAmount = totalAmount
	order.CreatedAt = time.Now()
	order.UpdatedAt = time.Now()

	// 设置默认状态
	if order.Status == "" {
		order.Status = models.OrderStatusPending
	}

	_, err = collection.InsertOne(ctx, order)
	if err != nil {
		return fmt.Errorf("failed to create order: %v", err)
	}

	return nil
}

// GetOrder 获取单个订单
func (s *OrderService) GetOrder(id string) (*models.Order, error) {
	collection, err := s.getCollection("orders")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID: %v", err)
	}

	var order models.Order
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&order)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("order not found")
		}
		return nil, fmt.Errorf("failed to find order: %v", err)
	}

	return &order, nil
}

// UpdateOrder 更新订单
func (s *OrderService) UpdateOrder(id string, order *models.Order) error {
	collection, err := s.getCollection("orders")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid order ID: %v", err)
	}

	// 重新计算总金额
	var totalAmount float64
	if len(order.Products) > 0 {
		// 多产品模式：计算所有产品的总金额
		for _, product := range order.Products {
			totalAmount += float64(product.Quantity) * product.UnitPrice
		}
	} else {
		// 单产品兼容模式
		totalAmount = float64(order.Quantity) * order.UnitPrice
	}
	order.TotalAmount = totalAmount
	order.UpdatedAt = time.Now()

	updateFields := bson.M{
		"customer_name": order.CustomerName,
		"products":      order.Products,
		"total_amount":  order.TotalAmount,
		"status":        order.Status,
		"priority":      order.Priority,
		"delivery_date": order.DeliveryDate,
		"description":   order.Description,
		"updated_at":    order.UpdatedAt,
	}

	// 如果有兼容字段，也更新它们
	if order.ProductID != "" {
		updateFields["product_id"] = order.ProductID
		updateFields["product_name"] = order.ProductName
		updateFields["quantity"] = order.Quantity
		updateFields["unit_price"] = order.UnitPrice
	}

	update := bson.M{"$set": updateFields}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update order: %v", err)
	}

	return nil
}

// DeleteOrder 删除订单
func (s *OrderService) DeleteOrder(id string) error {
	collection, err := s.getCollection("orders")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid order ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete order: %v", err)
	}

	return nil
}

// GetOrdersByStatus 根据状态获取订单
func (s *OrderService) GetOrdersByStatus(status string) ([]models.Order, error) {
	collection, err := s.getCollection("orders")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cursor, err := collection.Find(ctx, bson.M{"status": status})
	if err != nil {
		return nil, fmt.Errorf("failed to find orders by status: %v", err)
	}
	defer cursor.Close(ctx)

	var orders []models.Order
	if err = cursor.All(ctx, &orders); err != nil {
		return nil, fmt.Errorf("failed to decode orders: %v", err)
	}

	return orders, nil
}

// UpdateOrderStatus 更新订单状态
func (s *OrderService) UpdateOrderStatus(id string, status string) error {
	collection, err := s.getCollection("orders")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid order ID: %v", err)
	}

	update := bson.M{
		"$set": bson.M{
			"status":     status,
			"updated_at": time.Now(),
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update order status: %v", err)
	}

	return nil
}

// GetOrderStatistics 获取订单统计信息
func (s *OrderService) GetOrderStatistics() (map[string]interface{}, error) {
	collection, err := s.getCollection("orders")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 统计各状态订单数量
	pipeline := []bson.M{
		{
			"$group": bson.M{
				"_id":          "$status",
				"count":        bson.M{"$sum": 1},
				"total_amount": bson.M{"$sum": "$total_amount"},
			},
		},
	}

	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate order statistics: %v", err)
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode statistics: %v", err)
	}

	// 统计总数
	total, err := collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to count total orders: %v", err)
	}

	statistics := map[string]interface{}{
		"total_orders": total,
		"by_status":    results,
	}

	return statistics, nil
}
