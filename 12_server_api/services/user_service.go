/**
 * 用户管理服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的用户管理核心，负责用户账户的全生命周期管理，
 * 包括用户注册、认证、权限分配、密码管理等功能
 *
 * 主要功能：
 * - 用户账户管理：用户的创建、查询、更新、删除操作
 * - 密码安全：bcrypt加密存储、密码验证、密码修改
 * - 权限管理：基于角色的权限分配和管理
 * - 用户认证：用户名密码验证、登录信息记录
 * - 数据验证：用户名唯一性、邮箱唯一性检查
 * - 用户状态：活跃、禁用、锁定等状态管理
 *
 * 安全特性：
 * - 密码加密：使用bcrypt进行密码哈希存储，防止明文泄露
 * - 唯一性检查：用户名和邮箱的唯一性约束
 * - 权限分级：管理员、操作员、观察者的分级权限管理
 * - 登录追踪：记录用户登录时间和次数
 * - 状态控制：支持用户账户的启用和禁用
 *
 * 权限体系：
 * - 管理员(admin)：拥有所有系统权限，包括用户管理
 * - 操作员(operator)：拥有生产相关的操作权限
 * - 观察者(viewer)：只有数据查看权限
 * - 权限格式：resource:action（如 users:read、devices:write）
 *
 * 数据模型：
 * - users集合：存储用户基本信息、认证信息、权限配置
 * - 索引策略：username、email的唯一索引，status的普通索引
 * - 数据完整性：必填字段验证、数据类型检查
 *
 * 业务场景：
 * - 系统管理：管理员创建和管理用户账户
 * - 用户自服务：用户修改个人信息和密码
 * - 权限控制：为不同角色分配相应的系统权限
 * - 审计追踪：记录用户操作和登录历史
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/crypto/bcrypt"

	"server-api/db"
	"server-api/models"
)

/**
 * 用户管理服务结构体
 *
 * 功能：作为系统的用户管理层，提供完整的用户账户生命周期管理
 *
 * 架构设计：
 * - 数据访问层：直接操作MongoDB用户集合
 * - 业务逻辑层：封装用户管理的业务规则
 * - 安全控制层：密码加密、权限验证、数据校验
 * - 服务接口层：为上层服务提供统一的用户管理接口
 *
 * 核心职责：
 * - 用户CRUD操作：创建、查询、更新、删除用户
 * - 密码管理：密码加密、验证、修改
 * - 权限分配：基于角色的默认权限设置
 * - 数据验证：用户名、邮箱唯一性检查
 * - 登录管理：登录信息记录和统计
 *
 * 安全机制：
 * - 密码安全：bcrypt加密，防止彩虹表攻击
 * - 数据完整性：唯一性约束、必填字段验证
 * - 权限控制：基于角色的权限自动分配
 * - 操作审计：用户操作的完整记录
 *
 * @struct UserService
 */
type UserService struct {
	/** MongoDB管理器实例，提供统一的连接管理 */
	mongoManager *db.MongoDBManager
}

// NewUserService 创建用户服务实例（使用统一的MongoDB管理器）
func NewUserService(mongoManager *db.MongoDBManager) *UserService {
	return &UserService{
		mongoManager: mongoManager,
	}
}

// getCollection 获取指定名称的集合（内部辅助方法）
func (s *UserService) getCollection(collectionName string) (*mongo.Collection, error) {
	if s.mongoManager == nil {
		return nil, fmt.Errorf("MongoDB manager is nil")
	}
	database := s.mongoManager.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("database not available")
	}
	return database.Collection(collectionName), nil
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("users")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"username": bson.M{"$regex": params.Search, "$options": "i"}},
			{"email": bson.M{"$regex": params.Search, "$options": "i"}},
			{"profile.full_name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"profile.department": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}
	if params.Status != "" {
		filter["status"] = params.Status
	}

	// 计算总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count users: %v", err)
	}

	// 构建排序
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sort := bson.D{{Key: sortBy, Value: 1}}
	if params.SortDesc {
		sort = bson.D{{Key: sortBy, Value: -1}}
	}

	// 分页查询
	skip := (params.Page - 1) * params.PageSize
	opts := options.Find().
		SetSort(sort).
		SetSkip(int64(skip)).
		SetLimit(int64(params.PageSize))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find users: %v", err)
	}
	defer cursor.Close(ctx)

	var users []models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, fmt.Errorf("failed to decode users: %v", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	return &models.ListResponse{
		Data:       users,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

/**
 * 创建用户账户
 *
 * 功能：创建新的用户账户，包含完整的数据验证和安全处理
 *
 * 创建流程：
 * 1. 数据验证：检查用户名和邮箱的唯一性
 * 2. 密码安全：使用bcrypt进行密码哈希加密
 * 3. 权限分配：根据角色自动分配默认权限
 * 4. 时间戳：设置创建时间和更新时间
 * 5. 初始化：设置登录计数等初始值
 * 6. 数据持久化：将用户信息保存到数据库
 *
 * 安全措施：
 * - 唯一性检查：防止用户名和邮箱重复
 * - 密码加密：使用bcrypt.DefaultCost进行安全哈希
 * - 权限控制：基于角色的默认权限自动分配
 * - 数据完整性：必填字段验证和数据类型检查
 *
 * 验证规则：
 * - 用户名：必须唯一，不能为空
 * - 邮箱：必须唯一，符合邮箱格式
 * - 密码：明文输入，自动加密存储
 * - 角色：必须是有效的角色类型
 *
 * 权限分配：
 * - admin：拥有所有系统权限
 * - operator：拥有生产操作权限
 * - viewer：只有数据查看权限
 * - 自定义权限：可以覆盖默认权限设置
 *
 * 错误处理：
 * - 用户名重复：返回明确的错误信息
 * - 邮箱重复：返回明确的错误信息
 * - 密码加密失败：返回系统错误信息
 * - 数据库操作失败：返回详细的错误信息
 *
 * @param {*models.User} user - 用户信息对象
 *   - Username: 用户名（必需，唯一）
 *   - Email: 邮箱地址（必需，唯一）
 *   - Password: 密码（必需，明文）
 *   - Role: 用户角色（必需）
 *   - Profile: 用户资料（可选）
 *   - Permissions: 权限列表（可选，默认根据角色分配）
 * @returns {error} 创建过程中的错误信息
 *
 * @example
 * user := &models.User{
 *     Username: "john_doe",
 *     Email: "<EMAIL>",
 *     Password: "securePassword123",
 *     Role: models.RoleOperator,
 *     Profile: models.UserProfile{
 *         FullName: "John Doe",
 *         Department: "生产部",
 *         Position: "操作员",
 *     },
 * }
 * err := userService.CreateUser(user)
 */
func (s *UserService) CreateUser(user *models.User) error {
	collection, err := s.getCollection("users")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	// 创建带超时的操作上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 第一步：检查用户名唯一性
	count, err := collection.CountDocuments(ctx, bson.M{"username": user.Username})
	if err != nil {
		return fmt.Errorf("failed to check username: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("username already exists")
	}

	// 第二步：检查邮箱唯一性
	count, err = collection.CountDocuments(ctx, bson.M{"email": user.Email})
	if err != nil {
		return fmt.Errorf("failed to check email: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("email already exists")
	}

	// 第三步：密码安全加密
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %v", err)
	}
	user.Password = string(hashedPassword) // 替换为加密后的密码

	// 第四步：设置时间戳和初始值
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()
	user.LoginCount = 0 // 初始化登录计数

	// 第五步：权限分配
	if user.Permissions == nil {
		user.Permissions = s.getDefaultPermissions(user.Role)
	}

	// 第六步：数据持久化
	_, err = collection.InsertOne(ctx, user)
	if err != nil {
		return fmt.Errorf("failed to create user: %v", err)
	}

	return nil
}

// GetUser 获取单个用户
func (s *UserService) GetUser(id string) (*models.User, error) {
	collection, err := s.getCollection("users")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %v", err)
	}

	var user models.User
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to find user: %v", err)
	}

	return &user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	collection, err := s.getCollection("users")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var user models.User
	err = collection.FindOne(ctx, bson.M{"username": username}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to find user: %v", err)
	}

	return &user, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(id string, user *models.User) error {
	collection, err := s.getCollection("users")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid user ID: %v", err)
	}

	user.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"email":       user.Email,
			"role":        user.Role,
			"status":      user.Status,
			"profile":     user.Profile,
			"permissions": user.Permissions,
			"updated_at":  user.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update user: %v", err)
	}

	return nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(id string) error {
	collection, err := s.getCollection("users")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid user ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete user: %v", err)
	}

	return nil
}

// VerifyPassword 验证密码
func (s *UserService) VerifyPassword(user *models.User, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	return err == nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(id string, oldPassword, newPassword string) error {
	// 获取用户
	user, err := s.GetUser(id)
	if err != nil {
		return err
	}

	// 验证旧密码
	if !s.VerifyPassword(user, oldPassword) {
		return fmt.Errorf("old password is incorrect")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %v", err)
	}

	// 更新密码
	collection, err := s.getCollection("users")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, _ := primitive.ObjectIDFromHex(id)
	update := bson.M{
		"$set": bson.M{
			"password":   string(hashedPassword),
			"updated_at": time.Now(),
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update password: %v", err)
	}

	return nil
}

// UpdateLoginInfo 更新登录信息
func (s *UserService) UpdateLoginInfo(userID string) error {
	collection, err := s.getCollection("users")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %v", err)
	}

	now := time.Now()
	update := bson.M{
		"$set": bson.M{
			"last_login": now,
			"updated_at": now,
		},
		"$inc": bson.M{
			"login_count": 1,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update login info: %v", err)
	}

	return nil
}

// getDefaultPermissions 获取角色默认权限
func (s *UserService) getDefaultPermissions(role string) []string {
	switch role {
	case models.RoleAdmin:
		return []string{
			"users:read", "users:write", "users:delete",
			"devices:read", "devices:write", "devices:delete",
			"products:read", "products:write", "products:delete",
			"processes:read", "processes:write", "processes:delete",
			"orders:read", "orders:write", "orders:delete",
			"tasks:read", "tasks:write", "tasks:delete",
			"plans:read", "plans:write", "plans:delete",
			"data:manage", "statistics:read",
		}
	case models.RoleOperator:
		return []string{
			"devices:read", "devices:write",
			"products:read", "products:write",
			"processes:read", "processes:write",
			"orders:read", "orders:write",
			"tasks:read", "tasks:write",
			"plans:read", "plans:write",
			"statistics:read",
		}
	case models.RoleViewer:
		return []string{
			"devices:read",
			"products:read",
			"processes:read",
			"orders:read",
			"tasks:read",
			"plans:read",
			"statistics:read",
		}
	default:
		return []string{"statistics:read"}
	}
}
