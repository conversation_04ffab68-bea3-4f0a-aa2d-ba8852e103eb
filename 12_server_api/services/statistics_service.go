/**
 * 设备统计分析服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的核心统计分析模块，负责计算和管理设备的各项
 * 关键性能指标（KPI），包括设备利用率、产量达成率、班次统计等
 *
 * 主要功能：
 * - 设备利用率计算：基于运行时间和总时间计算设备利用率
 * - 产量统计分析：计算实际产量与计划产量的达成率
 * - 班次级别统计：支持多班次的独立统计分析
 * - 工作时间管理：支持灵活的工作时间配置和休息时间设置
 * - 数据持久化：统计结果自动保存到MongoDB，支持历史数据查询
 * - 实时计算：当日数据实时计算，历史数据缓存优化
 *
 * 技术特性：
 * - 多数据源整合：结合InfluxDB时序数据和MongoDB配置数据
 * - 灵活的时间计算：支持跨天班次和自定义工作时间
 * - 高性能查询：智能缓存机制，避免重复计算
 * - 容错设计：数据缺失时使用默认配置，确保服务可用性
 *
 * 业务规则：
 * - OP1模式：利用率 = 运行时间 / 24小时
 * - OP2模式：利用率 = 运行时间 / (24小时 - 休息时间)
 * - 产量达成率 = 实际产量 / 计划产量 × 100%
 * - 班次统计独立计算，支持跨天班次处理
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"server-api/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

/**
 * 统计分析服务结构体
 *
 * 功能：作为设备统计分析的核心服务，整合多个数据源进行统计计算
 *
 * 依赖关系：
 * - MongoDB：存储工作时间配置、统计结果缓存
 * - InfluxDB：获取设备状态历史数据、实时数据
 * - Logger：记录统计计算过程和错误信息
 *
 * 设计模式：
 * - 服务层模式：封装业务逻辑，提供统一接口
 * - 依赖注入：通过构造函数注入外部依赖
 * - 单一职责：专注于统计计算相关功能
 *
 * @struct StatisticsService
 */
type StatisticsService struct {
	/** MongoDB客户端，用于访问工作时间配置和保存统计结果 */
	mongoClient *mongo.Client

	/** InfluxDB服务，用于获取设备状态历史数据和实时数据 */
	influxService *InfluxDBService

	/** 工作时间服务，用于获取工作时间配置 */
	workTimeService *WorkTimeService

	/** 数据服务，用于统一的数据访问和缓存管理 */
	dataService *DataService

	/** 日志记录器，用于记录统计计算过程和调试信息 */
	logger *log.Logger
}

/**
 * 创建统计分析服务实例
 *
 * 功能：初始化统计分析服务，注入必要的依赖项
 *
 * 参数验证：
 * - mongoClient：必须是有效的MongoDB连接，用于配置和结果存储
 * - influxService：必须是已初始化的InfluxDB服务，用于数据查询
 * - workTimeService：工作时间配置管理服务，可为nil（使用内部方法）
 * - dataService：统一数据访问服务，可为nil（使用内部方法）
 * - logger：必须是有效的日志记录器，用于过程记录
 *
 * 初始化过程：
 * 1. 保存外部依赖的引用
 * 2. 验证依赖项的有效性
 * 3. 返回可用的服务实例
 *
 * 使用示例：
 * ```go
 * mongoClient := // MongoDB客户端
 * influxService := // InfluxDB服务
 * workTimeService := // 工作时间服务（可选）
 * dataService := // 数据服务（可选）
 * logger := log.New(os.Stdout, "[STATS] ", log.LstdFlags)
 * statsService := NewStatisticsService(mongoClient, influxService, workTimeService, dataService, logger)
 * ```
 *
 * @param {*mongo.Client} mongoClient - MongoDB数据库客户端
 * @param {*InfluxDBService} influxService - InfluxDB时序数据库服务
 * @param {*WorkTimeService} workTimeService - 工作时间配置管理服务（可选）
 * @param {*DataService} dataService - 统一数据访问服务（可选）
 * @param {*log.Logger} logger - 日志记录器
 * @returns {*StatisticsService} 初始化完成的统计服务实例
 */
func NewStatisticsService(mongoClient *mongo.Client, influxService *InfluxDBService, workTimeService *WorkTimeService, dataService *DataService, logger *log.Logger) *StatisticsService {
	return &StatisticsService{
		mongoClient:     mongoClient,     // 保存MongoDB客户端引用
		influxService:   influxService,   // 保存InfluxDB服务引用
		workTimeService: workTimeService, // 保存工作时间服务引用
		dataService:     dataService,     // 保存数据服务引用
		logger:          logger,          // 保存日志记录器引用
	}
}

/**
 * 计算设备统计数据
 *
 * 功能：这是统计服务的核心方法，计算指定设备在指定日期的完整统计数据
 *
 * 计算内容：
 * - 设备利用率：基于运行时间和有效工作时间
 * - 产量达成率：实际产量与计划产量的比较
 * - 各状态时间：运行、空闲、故障、维护时间统计
 * - 班次统计：每个班次的独立统计数据
 *
 * 数据来源：
 * - InfluxDB：设备状态历史数据、设备基本信息
 * - MongoDB：工作时间配置、班次设置
 *
 * 计算流程：
 * 1. 获取工作时间配置（失败时使用默认配置）
 * 2. 解析目标日期并计算时间范围
 * 3. 从InfluxDB查询设备状态历史
 * 4. 获取设备基本信息
 * 5. 计算各项统计指标
 * 6. 生成完整的统计报告
 *
 * 容错处理：
 * - 工作时间配置获取失败时使用默认配置
 * - 设备数据缺失时返回nil而不是错误
 * - 详细的日志记录便于问题诊断
 *
 * @param {string} date - 统计日期，格式：YYYY-MM-DD
 * @param {string} deviceID - 设备唯一标识符
 * @returns {*models.DeviceStatistics} 设备统计数据，数据缺失时返回nil
 * @returns {error} 计算过程中的错误信息
 *
 * @example
 * stats, err := service.CalculateDeviceStatistics("2025-06-05", "device_001")
 * if err != nil {
 *     log.Printf("计算失败: %v", err)
 * } else if stats != nil {
 *     log.Printf("利用率: %.2f%%", stats.UtilizationRate)
 * }
 */
func (s *StatisticsService) CalculateDeviceStatistics(date string, deviceID string) (*models.DeviceStatistics, error) {
	s.logger.Printf("开始计算设备统计: 日期=%s, 设备=%s", date, deviceID)

	/**
	 * 第一步：获取工作时间配置
	 * 工作时间配置决定了利用率的计算方式和班次划分
	 */
	workTimeSettings, err := s.getWorkTimeSettings()
	if err != nil {
		s.logger.Printf("获取工作时间设置失败: %v", err)
		// 容错处理：使用默认设置确保服务可用性
		workTimeSettings = s.getDefaultWorkTimeSettings()
	}

	/**
	 * 第二步：解析日期并计算时间范围
	 * 支持自定义的每日开始时间（如夜班从前一天开始）
	 */
	targetDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("日期格式错误: %v", err)
	}

	// 根据工作时间配置计算实际的统计时间范围
	startTime, endTime := s.calculateDayTimeRange(targetDate, workTimeSettings.DayStartTime)

	/**
	 * 第三步：获取设备状态历史数据
	 * 🔧 修复：使用时间范围查询，支持跨天数据获取
	 * 这是计算利用率和状态时间的基础数据
	 */
	s.logger.Printf("🔍 查询设备状态历史: 设备=%s, 时间范围=%s 到 %s",
		deviceID, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	var statusHistory []models.DeviceStatusHistory

	// 🔧 优先使用数据服务
	if s.dataService != nil {
		statusHistory, err = s.dataService.GetDeviceStatusHistory([]string{deviceID}, date, workTimeSettings)
		if err != nil {
			s.logger.Printf("⚠️ 数据服务查询失败: 设备=%s, 错误=%v，回退到直接InfluxDB查询", deviceID, err)
			// 回退到直接InfluxDB查询
			statusHistory, err = s.influxService.QueryDeviceStatusHistoryWithTimeRange([]string{deviceID}, date, startTime, endTime)
		} else {
			s.logger.Printf("✅ 从数据服务获取状态历史成功: 设备=%s, 记录数=%d", deviceID, len(statusHistory))
		}
	} else {
		// 直接使用InfluxDB查询
		statusHistory, err = s.influxService.QueryDeviceStatusHistoryWithTimeRange([]string{deviceID}, date, startTime, endTime)
	}

	if err != nil {
		s.logger.Printf("❌ 查询设备状态历史失败: 设备=%s, 错误=%v", deviceID, err)
		return nil, fmt.Errorf("查询设备状态历史失败: %v", err)
	}

	// 数据验证：无状态数据时返回nil而不是错误
	if len(statusHistory) == 0 {
		s.logger.Printf("⚠️ 设备 %s 在 %s 没有状态数据（时间范围: %s - %s）",
			deviceID, date, startTime.Format("15:04:05"), endTime.Format("15:04:05"))
		return nil, nil // 返回空，表示没有数据
	}

	s.logger.Printf("✅ 获取到设备状态历史: 设备=%s, 记录数=%d", deviceID, len(statusHistory))

	/**
	 * 第四步：获取设备基本信息
	 * 包含设备名称、计划产量等基础信息
	 */
	s.logger.Printf("🔍 查询设备信息: 设备=%s, 开始时间=%s, 结束时间=%s",
		deviceID, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	deviceInfoList, err := s.influxService.QueryDeviceStatus([]string{deviceID}, startTime, endTime)
	if err != nil {
		s.logger.Printf("❌ 查询设备信息失败: 设备=%s, 错误=%v", deviceID, err)
		return nil, fmt.Errorf("查询设备信息失败: %v", err)
	}

	s.logger.Printf("📊 设备信息查询结果: 设备=%s, 返回数量=%d", deviceID, len(deviceInfoList))

	var deviceInfo models.DeviceStatus
	if len(deviceInfoList) == 0 {
		s.logger.Printf("⚠️ 设备信息为空，尝试使用默认信息: 设备=%s", deviceID)
		// 使用默认设备信息，避免统计失败
		deviceInfo = models.DeviceStatus{
			DeviceID:   deviceID,
			DeviceName: fmt.Sprintf("设备_%s", deviceID),
			Plan:       1000,
			Status:     "unknown",
			Timestamp:  startTime,
		}
		s.logger.Printf("✅ 使用默认设备信息: 设备=%s, 名称=%s", deviceID, deviceInfo.DeviceName)
	} else {
		deviceInfo = deviceInfoList[0]
		s.logger.Printf("✅ 获取到设备信息: 设备=%s, 名称=%s, 计划=%d",
			deviceID, deviceInfo.DeviceName, deviceInfo.Plan)
	}

	/**
	 * 第五步：初始化统计数据结构
	 * 设置基础信息和时间戳
	 */
	stats := &models.DeviceStatistics{
		Date:       date,
		DeviceID:   deviceID,
		DeviceName: deviceInfo.DeviceName,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 计算总时间（通常为24小时，但支持自定义）
	totalDuration := endTime.Sub(startTime)
	stats.TotalTime = int64(totalDuration.Seconds())

	/**
	 * 第六步：执行各项统计计算
	 * 按照业务逻辑顺序进行计算
	 */

	// 计算各状态时间分布
	s.calculateStatusTimes(stats, statusHistory)

	// 计算设备利用率（基于工作时间配置）
	s.calculateUtilizationRate(stats, workTimeSettings)

	// 计算产量统计和达成率
	s.calculateOutputStats(stats, statusHistory, &deviceInfo)

	// 计算班次级别的详细统计
	s.calculateShiftStatistics(stats, statusHistory, workTimeSettings, startTime, endTime)

	s.logger.Printf("设备统计计算完成: 设备=%s, 利用率=%.2f%%, 产量达成率=%.2f%%",
		deviceID, stats.UtilizationRate, stats.OutputRate)

	return stats, nil
}

// calculateDayTimeRange 计算一天的时间范围
func (s *StatisticsService) calculateDayTimeRange(date time.Time, dayStartTime string) (time.Time, time.Time) {
	// 获取北京时区
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		s.logger.Printf("⚠️ Failed to load Beijing timezone, using UTC: %v", err)
		beijingLocation = time.UTC
	}

	// 解析开始时间
	startHour, startMinute := s.parseTime(dayStartTime)

	// 构建北京时间的开始时间
	startTimeLocal := time.Date(date.Year(), date.Month(), date.Day(), startHour, startMinute, 0, 0, beijingLocation)

	// 转换为UTC时间（与InfluxDB数据保持一致）
	startTimeUTC := startTimeLocal.UTC()

	// 结束时间是24小时后的北京时间，再转换为UTC
	endTimeLocal := startTimeLocal.Add(24 * time.Hour)
	endTimeUTC := endTimeLocal.UTC()

	s.logger.Printf("🕐 时间范围计算: 日期=%s, 开始时间=%s, 北京时间范围=[%s, %s], UTC时间范围=[%s, %s]",
		date.Format("2006-01-02"), dayStartTime,
		startTimeLocal.Format("2006-01-02 15:04:05 MST"), endTimeLocal.Format("2006-01-02 15:04:05 MST"),
		startTimeUTC.Format("2006-01-02 15:04:05 MST"), endTimeUTC.Format("2006-01-02 15:04:05 MST"))

	return startTimeUTC, endTimeUTC
}

// parseTime 解析时间字符串 HH:MM
func (s *StatisticsService) parseTime(timeStr string) (int, int) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		return 8, 0 // 默认8:00
	}

	hour, err1 := strconv.Atoi(parts[0])
	minute, err2 := strconv.Atoi(parts[1])

	if err1 != nil || err2 != nil {
		return 8, 0 // 默认8:00
	}

	return hour, minute
}

/**
 * 计算设备各状态时间分布
 *
 * 功能：统计设备在不同状态下的时间分布，为利用率计算提供基础数据
 *
 * 状态分类：
 * - 运行状态：production, running - 设备正常生产运行
 * - 空闲状态：idle - 设备空闲等待
 * - 故障状态：fault, error - 设备故障停机
 * - 维护状态：maintenance - 设备维护保养
 *
 * 计算逻辑：
 * 1. 遍历所有状态历史记录
 * 2. 根据状态类型累加持续时间
 * 3. 更新统计数据中的对应字段
 *
 * 数据来源：
 * - statusHistory：从InfluxDB查询的设备状态历史数据
 * - 每条记录包含状态类型和持续时间
 *
 * 业务规则：
 * - 运行时间是计算利用率的关键指标
 * - 故障时间影响设备可用性评估
 * - 维护时间通常不计入利用率计算
 *
 * @param {*models.DeviceStatistics} stats - 要更新的统计数据对象
 * @param {[]models.DeviceStatusHistory} statusHistory - 设备状态历史数据
 *
 * @example
 * // statusHistory包含多条状态记录，每条记录有状态和持续时间
 * s.calculateStatusTimes(stats, statusHistory)
 * // 结果：stats.RunningTime, stats.IdleTime等字段被更新
 */
func (s *StatisticsService) calculateStatusTimes(stats *models.DeviceStatistics, statusHistory []models.DeviceStatusHistory) {
	for _, status := range statusHistory {
		duration := status.Duration

		// 根据设备状态分类累加时间
		switch status.CurrentStatus {
		case "production", "running":
			// 生产运行状态：设备正常工作
			stats.RunningTime += duration
		case "idle":
			// 空闲状态：设备等待工作
			stats.IdleTime += duration
		case "fault", "error":
			// 故障状态：设备异常停机
			stats.FaultTime += duration
		case "maintenance":
			// 维护状态：设备保养维修
			stats.MaintenanceTime += duration
		}
	}
}

/**
 * 计算设备利用率
 *
 * 功能：根据工作时间配置计算设备利用率，支持两种计算模式
 *
 * 计算模式：
 * - OP1模式：利用率 = 运行时间 / 总时间（24小时）
 *   适用于连续生产的设备，不考虑休息时间
 *
 * - OP2模式：利用率 = 运行时间 / (总时间 - 休息时间)
 *   适用于有固定休息时间的生产线，扣除休息时间后计算
 *
 * 业务意义：
 * - 利用率是衡量设备效率的核心指标
 * - 高利用率表示设备得到充分使用
 * - 低利用率可能表示存在瓶颈或浪费
 *
 * 计算公式：
 * - 利用率(%) = (运行时间 / 有效时间) × 100
 * - 有效时间根据OP模式确定
 *
 * 数据验证：
 * - 有效时间必须大于0才进行计算
 * - 避免除零错误
 *
 * @param {*models.DeviceStatistics} stats - 要更新的统计数据对象
 * @param {*models.WorkTimeSettings} settings - 工作时间配置
 *
 * @example
 * // OP1模式：24小时连续生产
 * // 运行16小时，利用率 = 16/24 * 100 = 66.67%
 *
 * // OP2模式：扣除2小时休息时间
 * // 运行16小时，利用率 = 16/(24-2) * 100 = 72.73%
 */
/**
 * 计算设备利用率
 *
 * 功能：根据利用率计算模式计算设备利用率
 *
 * 计算逻辑：
 * - OP1模式：运行时间 ÷ 总工作时间 × 100%
 * - OP2模式：运行时间 ÷ 总工作时间 × 100%
 *
 * 注意：stats.TotalTime已经通过calculateTotalWorkTime方法计算，
 * 在OP2模式下已经扣除了休息时间，所以这里不需要再次扣除
 *
 * @param {*models.DeviceStatistics} stats - 设备统计数据
 * @param {*models.WorkTimeSettings} settings - 工作时间配置
 */
func (s *StatisticsService) calculateUtilizationRate(stats *models.DeviceStatistics, settings *models.WorkTimeSettings) {
	// 有效工作时间就是总工作时间
	// 因为stats.TotalTime已经根据利用率模式正确计算过了
	effectiveTime := stats.TotalTime

	s.logger.Printf("📊 [Utilization Rate] 利用率计算: 运行时间=%d秒, 有效工作时间=%d秒, 模式=%s",
		stats.RunningTime, effectiveTime, settings.UtilizationMode)

	// 计算利用率，避免除零错误
	if effectiveTime > 0 {
		stats.UtilizationRate = float64(stats.RunningTime) / float64(effectiveTime) * 100
		s.logger.Printf("📊 [Utilization Rate] 计算结果: %.2f%% = %d秒 ÷ %d秒 × 100",
			stats.UtilizationRate, stats.RunningTime, effectiveTime)
	} else {
		stats.UtilizationRate = 0
		s.logger.Printf("⚠️ [Utilization Rate] 有效工作时间为0，利用率设为0")
	}
}

// calculateRestTime 计算休息时间总长度
func (s *StatisticsService) calculateRestTime(restPeriods []models.RestPeriod) int64 {
	var totalRestTime int64

	for _, rest := range restPeriods {
		if !rest.Enabled {
			continue
		}

		startHour, startMinute := s.parseTime(rest.StartTime)
		endHour, endMinute := s.parseTime(rest.EndTime)

		startSeconds := int64(startHour*3600 + startMinute*60)
		endSeconds := int64(endHour*3600 + endMinute*60)

		// 处理跨天情况
		if endSeconds <= startSeconds {
			endSeconds += 24 * 3600
		}

		totalRestTime += endSeconds - startSeconds
	}

	return totalRestTime
}

/**
 * 计算总工作时间
 *
 * 功能：根据利用率模式和是否为今天来计算正确的总工作时间
 *
 * 计算逻辑：
 * - OP1模式：固定24小时（86400秒）
 * - OP2模式：
 *   - 今天：当前时间 - 开始时间 - 已过的休息时间
 *   - 历史：24小时 - 全部休息时间
 *
 * @param {string} date - 统计日期，格式：YYYY-MM-DD
 * @param {*models.WorkTimeSettings} settings - 工作时间配置
 * @returns {int64} 总工作时间（秒）
 */
func (s *StatisticsService) calculateTotalWorkTime(date string, settings *models.WorkTimeSettings) int64 {
	// OP1模式：固定24小时
	if settings.UtilizationMode == "OP1" {
		s.logger.Printf("📊 [Total Work Time] OP1模式，总工作时间: 24小时")
		return 24 * 3600
	}

	// OP2模式：需要考虑休息时间
	today := time.Now().Format("2006-01-02")
	isToday := date == today

	if isToday {
		// 当天：动态计算
		return s.calculateCurrentDayWorkTime(settings)
	} else {
		// 非当天：24小时 - 休息时间的和
		return s.calculateNonCurrentDayWorkTime(settings)
	}
}

/**
 * 计算当天的工作时间
 *
 * 功能：计算从开始时间到当前时间的实际工作时长，扣除已过的休息时间
 *
 * @param {*models.WorkTimeSettings} settings - 工作时间配置
 * @returns {int64} 当天工作时间（秒）
 */
func (s *StatisticsService) calculateCurrentDayWorkTime(settings *models.WorkTimeSettings) int64 {
	now := time.Now()

	// 解析每日开始时间
	dayStartTime, err := time.Parse("15:04", settings.DayStartTime)
	if err != nil {
		s.logger.Printf("⚠️ [Current Day] 解析开始时间失败: %v，使用默认08:00", err)
		dayStartTime, _ = time.Parse("15:04", "08:00")
	}

	// 构建今日的开始时间
	today := time.Date(now.Year(), now.Month(), now.Day(), dayStartTime.Hour(), dayStartTime.Minute(), 0, 0, now.Location())

	// 如果当前时间早于今日开始时间，说明是跨天情况，使用昨天的开始时间
	if now.Before(today) {
		today = today.AddDate(0, 0, -1)
	}

	// 计算基础工作时长：当前时间 - 每天开始时间
	baseWorkTime := int64(now.Sub(today).Seconds())

	s.logger.Printf("📊 [Current Day] 基础计算: 开始时间=%s, 当前时间=%s, 基础工作时长=%d秒",
		today.Format("15:04:05"), now.Format("15:04:05"), baseWorkTime)

	// 计算需要扣除的休息时间
	var restTimeToDeduct int64
	for _, restPeriod := range settings.RestPeriods {
		if !restPeriod.Enabled {
			continue
		}

		// 解析休息时间
		restStartHour, restStartMinute := s.parseTime(restPeriod.StartTime)
		restEndHour, restEndMinute := s.parseTime(restPeriod.EndTime)

		restStartSeconds := int64(restStartHour*3600 + restStartMinute*60)
		restEndSeconds := int64(restEndHour*3600 + restEndMinute*60)

		// 处理跨天情况
		if restEndSeconds <= restStartSeconds {
			restEndSeconds += 24 * 3600
		}

		// 计算当前时间相对于开始时间的秒数
		currentTimeSeconds := int64(now.Sub(today).Seconds())

		if currentTimeSeconds >= restStartSeconds && currentTimeSeconds < restEndSeconds {
			// 当前时间在休息时间内但未超过
			restTimeToDeduct += currentTimeSeconds - restStartSeconds
			s.logger.Printf("📊 [Current Day] 当前在%s时间内，扣除时间: %d秒", restPeriod.Name, currentTimeSeconds-restStartSeconds)
		} else if currentTimeSeconds >= restEndSeconds {
			// 当前时间已超过休息时间
			restTimeToDeduct += restEndSeconds - restStartSeconds
			s.logger.Printf("📊 [Current Day] 已超过%s时间，扣除时间: %d秒", restPeriod.Name, restEndSeconds-restStartSeconds)
		}
		// 如果当前时间还未到休息时间，不扣除
	}

	totalWorkTime := baseWorkTime - restTimeToDeduct
	s.logger.Printf("📊 [Current Day] 当天工作时间计算: %d秒 - %d秒 = %d秒", baseWorkTime, restTimeToDeduct, totalWorkTime)

	return totalWorkTime
}

/**
 * 计算非当天的工作时间
 *
 * 功能：计算历史日期的工作时间，24小时减去全部休息时间
 *
 * @param {*models.WorkTimeSettings} settings - 工作时间配置
 * @returns {int64} 非当天工作时间（秒）
 */
func (s *StatisticsService) calculateNonCurrentDayWorkTime(settings *models.WorkTimeSettings) int64 {
	baseWorkTime := int64(24 * 3600) // 24小时的秒数
	restTime := s.calculateRestTime(settings.RestPeriods)
	totalWorkTime := baseWorkTime - restTime

	s.logger.Printf("📊 [Non Current Day] 历史工作时间计算: %d秒 - %d秒 = %d秒", baseWorkTime, restTime, totalWorkTime)

	return totalWorkTime
}

// calculateOutputStats 计算产量统计
func (s *StatisticsService) calculateOutputStats(stats *models.DeviceStatistics, statusHistory []models.DeviceStatusHistory, deviceInfo *models.DeviceStatus) {
	// 计算实际产量（基于生产时间和设备效率）
	if stats.RunningTime > 0 {
		// 假设每小时标准产量，这里需要根据实际业务调整
		hourlyOutput := int64(60) // 每小时60件，可配置
		stats.ActualOutput = (stats.RunningTime / 3600) * hourlyOutput
	}

	// 计划产量（从设备信息获取）
	stats.PlannedOutput = int64(deviceInfo.Plan)

	// 计算产量达成率
	if stats.PlannedOutput > 0 {
		stats.OutputRate = float64(stats.ActualOutput) / float64(stats.PlannedOutput) * 100
	}
}

// calculateShiftStatistics 计算班次统计
func (s *StatisticsService) calculateShiftStatistics(stats *models.DeviceStatistics, statusHistory []models.DeviceStatusHistory, settings *models.WorkTimeSettings, dayStart, dayEnd time.Time) {
	stats.ShiftStatistics = make([]models.ShiftStatistics, 0)

	for _, shift := range settings.Shifts {
		if !shift.Enabled {
			continue
		}

		shiftStats := models.ShiftStatistics{
			ShiftName: shift.Name,
			StartTime: shift.StartTime,
			EndTime:   shift.EndTime,
		}

		// 计算班次时间范围
		shiftStart, shiftEnd := s.calculateShiftTimeRange(dayStart, shift.StartTime, shift.EndTime)
		shiftStats.TotalTime = int64(shiftEnd.Sub(shiftStart).Seconds())

		// 过滤班次内的状态历史
		shiftStatusHistory := s.filterStatusHistoryByTime(statusHistory, shiftStart, shiftEnd)

		// 计算班次内各状态时间
		s.calculateShiftStatusTimes(&shiftStats, shiftStatusHistory)

		// 计算班次利用率
		if shiftStats.TotalTime > 0 {
			shiftStats.UtilizationRate = float64(shiftStats.RunningTime) / float64(shiftStats.TotalTime) * 100
		}

		// 计算班次产量
		s.calculateShiftOutput(&shiftStats)

		stats.ShiftStatistics = append(stats.ShiftStatistics, shiftStats)
	}
}

// getWorkTimeSettings 获取工作时间设置
func (s *StatisticsService) getWorkTimeSettings() (*models.WorkTimeSettings, error) {
	// 🔧 优先使用工作时间服务
	if s.workTimeService != nil {
		settings, err := s.workTimeService.GetWorkTimeSettings()
		if err == nil && settings != nil {
			s.logger.Printf("✅ 从工作时间服务获取配置成功")
			return settings, nil
		}
		s.logger.Printf("⚠️ 工作时间服务获取配置失败: %v，回退到直接MongoDB查询", err)
	}

	// 回退到直接MongoDB查询
	collection := s.mongoClient.Database("device_db").Collection("work_time_settings")

	var settings models.WorkTimeSettings
	err := collection.FindOne(context.Background(), bson.M{}).Decode(&settings)
	if err != nil {
		return nil, err
	}

	return &settings, nil
}

// getDefaultWorkTimeSettings 获取默认工作时间设置
func (s *StatisticsService) getDefaultWorkTimeSettings() *models.WorkTimeSettings {
	return &models.WorkTimeSettings{
		DayStartTime: "08:00",
		Shifts: []models.ShiftSetting{
			{Name: "早班", StartTime: "08:00", EndTime: "16:00", Enabled: true},
			{Name: "中班", StartTime: "16:00", EndTime: "00:00", Enabled: true},
			{Name: "晚班", StartTime: "00:00", EndTime: "08:00", Enabled: true},
		},
		RestPeriods: []models.RestPeriod{
			{Name: "午休", StartTime: "12:00", EndTime: "13:00", Enabled: true},
		},
		UtilizationMode: "OP1",
	}
}

// calculateShiftTimeRange 计算班次时间范围
func (s *StatisticsService) calculateShiftTimeRange(dayStart time.Time, startTime, endTime string) (time.Time, time.Time) {
	startHour, startMinute := s.parseTime(startTime)
	endHour, endMinute := s.parseTime(endTime)

	shiftStart := time.Date(dayStart.Year(), dayStart.Month(), dayStart.Day(), startHour, startMinute, 0, 0, dayStart.Location())
	shiftEnd := time.Date(dayStart.Year(), dayStart.Month(), dayStart.Day(), endHour, endMinute, 0, 0, dayStart.Location())

	// 处理跨天班次
	if shiftEnd.Before(shiftStart) || shiftEnd.Equal(shiftStart) {
		shiftEnd = shiftEnd.Add(24 * time.Hour)
	}

	return shiftStart, shiftEnd
}

// filterStatusHistoryByTime 按时间过滤状态历史
func (s *StatisticsService) filterStatusHistoryByTime(statusHistory []models.DeviceStatusHistory, start, end time.Time) []models.DeviceStatusHistory {
	var filtered []models.DeviceStatusHistory

	for _, status := range statusHistory {
		statusStart := status.StartTime
		statusEnd := status.EndTime

		// 检查时间段是否有重叠
		if statusEnd.After(start) && statusStart.Before(end) {
			// 调整时间范围到班次内
			if statusStart.Before(start) {
				statusStart = start
			}
			if statusEnd.After(end) {
				statusEnd = end
			}

			// 重新计算持续时间
			adjustedStatus := status
			adjustedStatus.StartTime = statusStart
			adjustedStatus.EndTime = statusEnd
			adjustedStatus.Duration = int64(statusEnd.Sub(statusStart).Seconds())

			if adjustedStatus.Duration > 0 {
				filtered = append(filtered, adjustedStatus)
			}
		}
	}

	return filtered
}

// calculateShiftStatusTimes 计算班次内各状态时间
func (s *StatisticsService) calculateShiftStatusTimes(shiftStats *models.ShiftStatistics, statusHistory []models.DeviceStatusHistory) {
	for _, status := range statusHistory {
		duration := status.Duration

		switch status.CurrentStatus {
		case "production", "running":
			shiftStats.RunningTime += duration
		case "idle":
			shiftStats.IdleTime += duration
		case "fault", "error":
			shiftStats.FaultTime += duration
		case "maintenance":
			shiftStats.MaintenanceTime += duration
		}
	}
}

// calculateShiftOutput 计算班次产量
func (s *StatisticsService) calculateShiftOutput(shiftStats *models.ShiftStatistics) {
	if shiftStats.RunningTime > 0 {
		// 假设每小时标准产量
		hourlyOutput := int64(60)
		shiftStats.ActualOutput = (shiftStats.RunningTime / 3600) * hourlyOutput

		// 班次计划产量（按比例分配）
		totalHours := float64(shiftStats.TotalTime) / 3600
		shiftStats.PlannedOutput = int64(totalHours * float64(hourlyOutput))

		// 计算班次产量达成率
		if shiftStats.PlannedOutput > 0 {
			shiftStats.OutputRate = float64(shiftStats.ActualOutput) / float64(shiftStats.PlannedOutput) * 100
		}
	}
}

// SaveDeviceStatistics 保存设备统计到MongoDB
func (s *StatisticsService) SaveDeviceStatistics(stats *models.DeviceStatistics) error {
	collection := s.mongoClient.Database("device_db").Collection("device_statistics")

	// 检查是否已存在
	filter := bson.M{
		"date":      stats.Date,
		"device_id": stats.DeviceID,
	}

	// 更新时间戳
	stats.UpdatedAt = time.Now()

	// 使用upsert操作
	opts := options.Replace().SetUpsert(true)
	_, err := collection.ReplaceOne(context.Background(), filter, stats, opts)
	if err != nil {
		return fmt.Errorf("保存设备统计失败: %v", err)
	}

	s.logger.Printf("设备统计已保存: 日期=%s, 设备=%s", stats.Date, stats.DeviceID)
	return nil
}

/**
 * 获取设备统计数据
 *
 * 功能：获取指定设备在指定日期的统计数据，基于状态历史数据进行实时计算
 *
 * 数据处理流程：
 * 1. 获取工作时间配置
 * 2. 从数据服务获取设备状态历史
 * 3. 将状态历史转换为设备统计数据
 * 4. 计算利用率、产量等关键指标
 *
 * 业务逻辑：
 * - 基于状态历史数据实时计算统计指标
 * - 支持工作时间配置的利用率计算
 * - 提供完整的设备性能分析数据
 *
 * @param {string} date - 统计日期，格式：YYYY-MM-DD
 * @param {string} deviceID - 设备唯一标识符
 * @returns {*models.DeviceStatistics} 设备统计数据，无数据时返回nil
 * @returns {error} 查询或计算过程中的错误信息
 *
 * @example
 * // 获取设备统计数据
 * stats, err := service.GetDeviceStatistics("2025-06-20", "device_001")
 * if err != nil {
 *     log.Printf("获取失败: %v", err)
 * } else if stats != nil {
 *     log.Printf("利用率: %.2f%%", stats.UtilizationRate)
 * } else {
 *     log.Printf("无统计数据")
 * }
 */
func (s *StatisticsService) GetDeviceStatistics(date, deviceID string) (*models.DeviceStatistics, error) {
	s.logger.Printf("🔍 获取设备统计数据: 日期=%s, 设备=%s", date, deviceID)

	/**
	 * 第一步：获取工作时间配置
	 * 工作时间配置决定了利用率的计算方式
	 */
	workTimeSettings, err := s.getWorkTimeSettings()
	if err != nil {
		s.logger.Printf("⚠️ 获取工作时间设置失败: %v，使用默认配置", err)
		// 容错处理：使用默认设置确保服务可用性
		workTimeSettings = s.getDefaultWorkTimeSettings()
	}

	/**
	 * 第二步：从数据服务获取设备状态历史
	 * 使用数据服务的智能缓存和查询优化
	 */
	statusHistory, err := s.dataService.GetDeviceStatusHistory([]string{deviceID}, date, workTimeSettings)
	if err != nil {
		s.logger.Printf("❌ 获取设备状态历史失败: 日期=%s, 设备=%s, 错误=%v", date, deviceID, err)
		return nil, fmt.Errorf("获取设备状态历史失败: %v", err)
	}

	// 数据验证：无状态数据时返回nil而不是错误
	if len(statusHistory) == 0 {
		s.logger.Printf("📭 设备 %s 在 %s 没有状态数据", deviceID, date)
		return nil, nil // 返回nil表示无数据，不是错误
	}

	s.logger.Printf("✅ 获取到设备状态历史: 设备=%s, 记录数=%d", deviceID, len(statusHistory))

	/**
	 * 第三步：将状态历史转换为设备统计数据
	 * 基于状态历史数据计算各项统计指标
	 */
	stats, err := s.convertStatusHistoryToStatistics(statusHistory, date, deviceID, workTimeSettings)
	if err != nil {
		s.logger.Printf("❌ 状态历史转换失败: 日期=%s, 设备=%s, 错误=%v", date, deviceID, err)
		return nil, fmt.Errorf("状态历史转换失败: %v", err)
	}

	if stats != nil {
		s.logger.Printf("✅ 设备统计计算完成: 设备=%s, 利用率=%.2f%%", deviceID, stats.UtilizationRate)
	}

	return stats, nil
}

// GetMultipleDeviceStatistics 获取多个设备的统计数据
func (s *StatisticsService) GetMultipleDeviceStatistics(request *models.StatisticsRequest) (*models.StatisticsResponse, error) {
	s.logger.Printf("获取多设备统计: 开始日期=%s, 结束日期=%s, 设备数=%d",
		request.StartDate, request.EndDate, len(request.DeviceIDs))

	// 解析日期范围
	startDate, err := time.Parse("2006-01-02", request.StartDate)
	if err != nil {
		return nil, fmt.Errorf("开始日期格式错误: %v", err)
	}

	endDate, err := time.Parse("2006-01-02", request.EndDate)
	if err != nil {
		return nil, fmt.Errorf("结束日期格式错误: %v", err)
	}

	// 生成日期范围
	dateRange := s.generateDateRange(startDate, endDate)

	// 获取设备列表
	deviceIDs := request.DeviceIDs
	if len(deviceIDs) == 0 {
		// 如果没有指定设备，获取所有设备
		allDevices, err := s.getAllDeviceIDs()
		if err != nil {
			return nil, fmt.Errorf("获取设备列表失败: %v", err)
		}
		deviceIDs = allDevices
	}

	// 收集所有设备统计数据
	var allDeviceStats []models.DeviceStatistics
	var summaryStats models.DailyStatisticsSummary

	for _, date := range dateRange {
		for _, deviceID := range deviceIDs {

			stats, err := s.GetDeviceStatistics(date, deviceID)
			if err != nil {
				s.logger.Printf("获取设备统计失败: 日期=%s, 设备=%s, 错误=%v", date, deviceID, err)
				continue
			}
			if stats != nil {
				allDeviceStats = append(allDeviceStats, *stats)
			}
		}
	}

	// 计算汇总统计
	summaryStats = s.calculateSummaryStatistics(allDeviceStats, dateRange)

	response := &models.StatisticsResponse{
		Summary:     summaryStats,
		DeviceStats: allDeviceStats,
		DateRange:   dateRange,
	}

	s.logger.Printf("多设备统计完成: 总记录数=%d", len(allDeviceStats))
	return response, nil
}

// generateDateRange 生成日期范围
func (s *StatisticsService) generateDateRange(start, end time.Time) []string {
	var dates []string
	current := start

	for !current.After(end) {
		dates = append(dates, current.Format("2006-01-02"))
		current = current.Add(24 * time.Hour)
	}

	return dates
}

// getAllDeviceIDs 获取所有设备ID
func (s *StatisticsService) getAllDeviceIDs() ([]string, error) {
	// 从InfluxDB获取所有设备
	now := time.Now()
	yesterday := now.Add(-24 * time.Hour)

	devices, err := s.influxService.QueryDeviceStatus([]string{}, yesterday, now)
	if err != nil {
		return nil, err
	}

	var deviceIDs []string
	deviceMap := make(map[string]bool)

	for _, device := range devices {
		if !deviceMap[device.DeviceID] {
			deviceIDs = append(deviceIDs, device.DeviceID)
			deviceMap[device.DeviceID] = true
		}
	}

	return deviceIDs, nil
}

// calculateSummaryStatistics 计算汇总统计
func (s *StatisticsService) calculateSummaryStatistics(deviceStats []models.DeviceStatistics, dateRange []string) models.DailyStatisticsSummary {
	if len(deviceStats) == 0 {
		return models.DailyStatisticsSummary{}
	}

	// 按日期分组统计
	dateStatsMap := make(map[string][]models.DeviceStatistics)
	for _, stats := range deviceStats {
		dateStatsMap[stats.Date] = append(dateStatsMap[stats.Date], stats)
	}

	// 计算总体汇总
	var totalDevices int
	var totalTime, totalRunningTime, totalIdleTime, totalFaultTime int64
	var totalActualOutput, totalPlannedOutput int64
	var utilizationSum, outputRateSum float64
	var validUtilizationCount, validOutputRateCount int

	for _, stats := range deviceStats {
		totalTime += stats.TotalTime
		totalRunningTime += stats.RunningTime
		totalIdleTime += stats.IdleTime
		totalFaultTime += stats.FaultTime
		totalActualOutput += stats.ActualOutput
		totalPlannedOutput += stats.PlannedOutput

		if stats.UtilizationRate > 0 {
			utilizationSum += stats.UtilizationRate
			validUtilizationCount++
		}

		if stats.OutputRate > 0 {
			outputRateSum += stats.OutputRate
			validOutputRateCount++
		}
	}

	// 计算设备总数（去重）
	deviceMap := make(map[string]bool)
	for _, stats := range deviceStats {
		deviceMap[stats.DeviceID] = true
	}
	totalDevices = len(deviceMap)

	// 计算平均值
	var avgUtilizationRate, avgOutputRate float64
	if validUtilizationCount > 0 {
		avgUtilizationRate = utilizationSum / float64(validUtilizationCount)
	}
	if validOutputRateCount > 0 {
		avgOutputRate = outputRateSum / float64(validOutputRateCount)
	}

	// 使用第一个日期作为汇总日期（如果只有一天）或日期范围
	summaryDate := ""
	if len(dateRange) == 1 {
		summaryDate = dateRange[0]
	} else if len(dateRange) > 1 {
		summaryDate = fmt.Sprintf("%s 至 %s", dateRange[0], dateRange[len(dateRange)-1])
	}

	return models.DailyStatisticsSummary{
		Date:               summaryDate,
		TotalDevices:       totalDevices,
		TotalTime:          totalTime,
		TotalRunningTime:   totalRunningTime,
		TotalIdleTime:      totalIdleTime,
		TotalFaultTime:     totalFaultTime,
		AvgUtilizationRate: avgUtilizationRate,
		TotalActualOutput:  totalActualOutput,
		TotalPlannedOutput: totalPlannedOutput,
		AvgOutputRate:      avgOutputRate,
	}
}

/**
 * 将状态历史转换为设备统计数据
 *
 * 功能：基于设备状态历史数据计算完整的设备统计指标
 *
 * 计算内容：
 * - 各状态时间分布：运行、空闲、故障、维护时间统计
 * - 设备利用率：基于工作时间配置的利用率计算
 * - 产量统计：实际产量与计划产量的达成率
 * - 时间戳信息：创建和更新时间
 *
 * 业务逻辑：
 * - 遍历状态历史记录，累加各状态持续时间
 * - 根据工作时间配置计算有效工作时间
 * - 计算利用率 = 运行时间 / 有效工作时间
 * - 估算产量基于运行时间和标准产能
 *
 * @param {[]models.DeviceStatusHistory} statusHistory - 设备状态历史数据
 * @param {string} date - 统计日期
 * @param {string} deviceID - 设备ID
 * @param {*models.WorkTimeSettings} workTimeSettings - 工作时间配置
 * @returns {*models.DeviceStatistics} 设备统计数据
 * @returns {error} 转换过程中的错误
 */
func (s *StatisticsService) convertStatusHistoryToStatistics(
	statusHistory []models.DeviceStatusHistory,
	date, deviceID string,
	workTimeSettings *models.WorkTimeSettings,
) (*models.DeviceStatistics, error) {
	s.logger.Printf("🔄 开始转换状态历史为统计数据: 设备=%s, 记录数=%d", deviceID, len(statusHistory))

	// 初始化统计数据结构
	stats := &models.DeviceStatistics{
		Date:      date,
		DeviceID:  deviceID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 获取设备名称（从第一条记录或使用默认值）
	if len(statusHistory) > 0 {
		// 尝试从状态历史中获取设备名称
		stats.DeviceName = fmt.Sprintf("设备_%s", deviceID)
	} else {
		stats.DeviceName = fmt.Sprintf("设备_%s", deviceID)
	}

	// 计算总工作时间（根据利用率模式和是否为今天动态计算）
	stats.TotalTime = s.calculateTotalWorkTime(date, workTimeSettings)

	// 计算各状态时间分布
	s.calculateStatusTimes(stats, statusHistory)

	// 计算设备利用率（基于工作时间配置）
	s.calculateUtilizationRate(stats, workTimeSettings)

	// 计算产量统计（基于运行时间估算）
	s.calculateOutputStatsFromHistory(stats, statusHistory)

	s.logger.Printf("✅ 状态历史转换完成: 设备=%s, 利用率=%.2f%%, 运行时间=%d秒",
		deviceID, stats.UtilizationRate, stats.RunningTime)

	return stats, nil
}

/**
 * 基于状态历史计算产量统计
 *
 * 功能：根据设备运行时间估算产量指标
 *
 * 计算逻辑：
 * - 实际产量 = 运行时间(小时) × 标准时产量
 * - 计划产量 = 24小时 × 标准时产量（可配置）
 * - 产量达成率 = 实际产量 / 计划产量 × 100%
 *
 * @param {*models.DeviceStatistics} stats - 要更新的统计数据
 * @param {[]models.DeviceStatusHistory} statusHistory - 状态历史数据
 */
func (s *StatisticsService) calculateOutputStatsFromHistory(stats *models.DeviceStatistics, statusHistory []models.DeviceStatusHistory) {
	// 标准时产量（每小时件数，可配置）
	hourlyOutput := int64(60) // 默认每小时60件

	// 计算实际产量（基于运行时间）
	if stats.RunningTime > 0 {
		runningHours := float64(stats.RunningTime) / 3600.0 // 转换为小时
		stats.ActualOutput = int64(runningHours * float64(hourlyOutput))
	}

	// 计划产量（24小时标准产量）
	stats.PlannedOutput = 24 * hourlyOutput

	// 计算产量达成率
	if stats.PlannedOutput > 0 {
		stats.OutputRate = float64(stats.ActualOutput) / float64(stats.PlannedOutput) * 100
	}

	s.logger.Printf("📊 产量统计: 设备=%s, 实际产量=%d, 计划产量=%d, 达成率=%.2f%%",
		stats.DeviceID, stats.ActualOutput, stats.PlannedOutput, stats.OutputRate)
}
