/**
 * 数据管理服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的核心数据管理层，负责统一管理多个数据源
 * 的访问和缓存策略，实现高效的数据获取和智能缓存机制
 *
 * 核心功能：
 * - 多数据源整合：Redis缓存 + InfluxDB时序数据 + MongoDB业务数据
 * - 智能缓存策略：实时数据和历史数据的差异化缓存处理
 * - 数据一致性保证：多层缓存的数据同步和失效机制
 * - 性能优化：异步更新、批量操作、连接池管理
 * - 自动化任务：定时缓存更新、历史数据处理、过期清理
 *
 * 数据分类策略：
 * - 实时数据（当日）：优先Redis缓存，5秒更新周期，支持实时查询
 * - 历史数据（非当日）：MongoDB持久化，Redis短期缓存，按需加载
 * - 状态历史：InfluxDB原始数据，MongoDB聚合存储，分层查询
 *
 * 缓存策略：
 * - L1缓存：Redis内存缓存，毫秒级响应，适合高频查询
 * - L2缓存：MongoDB文档存储，秒级响应，适合复杂查询
 * - L3存储：InfluxDB时序存储，原始数据持久化，适合历史分析
 *
 * 性能特性：
 * - 缓存命中率监控：实时统计缓存效果和性能指标
 * - 异步更新机制：后台任务不阻塞前端查询响应
 * - 连接健康检查：自动检测数据源可用性并降级处理
 * - 批量操作优化：减少网络开销和数据库压力
 *
 * 业务场景：
 * - 设备状态监控：实时设备状态查询和历史趋势分析
 * - 生产数据统计：产量统计、效率分析、异常检测
 * - 报表数据生成：日报、周报、月报的数据支撑
 * - 系统性能监控：数据服务本身的性能监控和优化
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"server-api/models"
	"shared/config"
	"shared/logger"
)

/**
 * 数据管理服务结构体
 *
 * 功能：作为系统的数据访问层，统一管理多个数据源的访问和缓存策略
 *
 * 架构设计：
 * - 分层缓存：Redis(L1) -> MongoDB(L2) -> InfluxDB(L3)
 * - 智能路由：根据数据类型和时效性选择最优数据源
 * - 异步更新：后台任务保证缓存数据的实时性
 * - 降级处理：单点故障时的服务降级和容错机制
 *
 * 数据流向：
 * 1. 查询请求 -> 缓存检查 -> 数据源查询 -> 结果缓存 -> 响应返回
 * 2. 实时数据 -> Redis缓存 -> 定时更新 -> 过期清理
 * 3. 历史数据 -> MongoDB存储 -> 按需缓存 -> 长期保存
 *
 * 服务依赖：
 * - RedisService：高速缓存服务，毫秒级响应
 * - InfluxDBService：时序数据服务，原始数据存储
 * - MongoAdminService：文档数据服务，业务数据管理
 * - MonitoringService：监控服务，性能指标收集
 *
 * @struct DataService
 */
type DataService struct {
	/** Redis缓存服务，提供高速数据缓存和会话存储 */
	redis *RedisService

	/** InfluxDB时序数据服务，存储设备状态和生产数据的原始记录 */
	influx *InfluxDBService

	/** MongoDB管理服务，存储业务配置和聚合统计数据 */
	mongo *MongoAdminService

	/** 监控服务，收集缓存命中率、查询性能等指标 */
	monitoring *MonitoringService

	/** 缓存配置，控制缓存更新策略和频率 */
	cacheConfig *config.CacheConfig

	/** 服务活动状态标志，控制后台任务的生命周期 */
	isActive bool
}

/**
 * 创建数据管理服务实例
 *
 * 功能：初始化数据管理服务，建立多数据源的统一访问接口
 *
 * 参数说明：
 * - redis：Redis缓存服务，可为nil（降级为直接查询模式）
 * - influx：InfluxDB服务，必需（主要数据源）
 * - mongo：MongoDB服务，可为nil（历史数据功能受限）
 * - monitoring：监控服务，可为nil（无性能监控）
 *
 * 初始化策略：
 * 1. 保存所有服务依赖的引用
 * 2. 设置服务为活动状态
 * 3. 准备后台任务的执行环境
 * 4. 建立服务间的协调机制
 *
 * 容错设计：
 * - 允许部分服务为nil，实现优雅降级
 * - 服务不可用时自动切换到备用方案
 * - 连接失败时的重试和恢复机制
 *
 * 使用示例：
 * ```go
 * redisService := NewRedisService(...)
 * influxService := NewInfluxDBService(...)
 * mongoService := NewMongoAdminService(...)
 * monitoringService := NewMonitoringService()
 *
 * dataService := NewDataService(redisService, influxService, mongoService, monitoringService)
 * defer dataService.Stop()
 *
 * if err := dataService.Start(); err != nil {
 *     log.Fatal("Failed to start data service:", err)
 * }
 * ```
 *
 * @param {*RedisService} redis - Redis缓存服务实例
 * @param {*InfluxDBService} influx - InfluxDB时序数据服务实例
 * @param {*MongoAdminService} mongo - MongoDB管理服务实例
 * @param {*MonitoringService} monitoring - 监控服务实例
 * @returns {*DataService} 初始化完成的数据管理服务实例
 */
func NewDataService(redis *RedisService, influx *InfluxDBService, mongo *MongoAdminService, monitoring *MonitoringService, cacheConfig *config.CacheConfig) *DataService {
	// 设置默认缓存配置
	if cacheConfig == nil {
		cacheConfig = &config.CacheConfig{
			UpdateInterval:        5,    // 默认5秒更新间隔
			EnableAutoUpdate:      true, // 默认启用自动更新
			ForceRefreshOnStartup: true, // 默认启动时强制刷新
		}
	}

	return &DataService{
		redis:       redis,       // Redis缓存服务引用
		influx:      influx,      // InfluxDB时序数据服务引用
		mongo:       mongo,       // MongoDB管理服务引用
		monitoring:  monitoring,  // 监控服务引用
		cacheConfig: cacheConfig, // 缓存配置
		isActive:    true,        // 设置服务为活动状态
	}
}

// Start 启动数据服务
func (ds *DataService) Start() error {
	logger.Info("🚀 Starting Data Service...")

	// 连接Redis服务（如果可用）
	if ds.redis != nil {
		if err := ds.redis.Connect(); err != nil {
			logger.Errorf("⚠️ Failed to connect to Redis: %v", err)
			// Redis连接失败不阻止服务启动，只是降级运行
		} else {
			logger.Info("✅ Redis connected successfully")
		}
	}

	// 检查InfluxDB连接状态（如果可用）
	if ds.influx != nil {
		if !ds.influx.IsConnected() {
			logger.Errorf("⚠️ InfluxDB is not connected, attempting to connect...")
			if err := ds.influx.Connect(); err != nil {
				logger.Errorf("❌ Failed to connect to InfluxDB: %v", err)
				// InfluxDB连接失败不阻止服务启动，但会影响数据查询功能
			} else {
				logger.Info("✅ InfluxDB connected successfully")
			}
		} else {
			logger.Info("✅ InfluxDB is already connected")
		}
	} else {
		logger.Errorf("❌ InfluxDB service is nil")
	}

	// 启动后台数据更新任务
	go ds.startBackgroundTasks()

	logger.Info("✅ Data Service started successfully")
	return nil
}

// Stop 停止数据服务
func (ds *DataService) Stop() error {
	logger.Info("🛑 Stopping Data Service...")
	ds.isActive = false
	return nil
}

/**
 * 获取设备状态数据（智能缓存策略）
 *
 * 功能：这是数据服务的核心方法，实现智能的数据获取和缓存策略
 *
 * 智能策略：
 * - 实时数据（当日）：Redis缓存优先，5秒更新周期，确保数据实时性
 * - 历史数据（非当日）：分层缓存，MongoDB聚合，InfluxDB原始数据
 *
 * 数据流程：
 * 1. 日期判断：区分实时数据和历史数据的处理策略
 * 2. 缓存检查：优先从高速缓存获取数据
 * 3. 源数据查询：缓存未命中时从原始数据源查询
 * 4. 异步更新：后台更新缓存，不阻塞当前请求
 * 5. 性能监控：记录缓存命中率和查询性能
 *
 * 性能优化：
 * - 缓存命中：毫秒级响应，适合高频查询
 * - 批量查询：减少网络开销和数据库压力
 * - 异步更新：保证响应速度的同时更新缓存
 * - 连接检查：自动检测服务可用性并降级
 *
 * 容错机制：
 * - Redis不可用：直接查询InfluxDB，降级但不中断服务
 * - InfluxDB不可用：返回错误，记录日志便于问题诊断
 * - 部分数据缺失：返回可用数据，标记缺失设备
 *
 * 使用场景：
 * - 设备监控大屏：实时状态展示
 * - 历史数据分析：趋势分析和报表生成
 * - API接口调用：为前端提供统一的数据接口
 *
 * @param {[]string} deviceIDs - 设备ID列表，空数组表示查询所有设备
 * @param {string} date - 查询日期，格式：YYYY-MM-DD
 * @returns {[]models.DeviceStatus} 设备状态数据列表
 * @returns {error} 查询过程中的错误信息
 *
 * @example
 * // 查询当日所有设备状态
 * devices, err := dataService.GetDevicesStatus([]string{}, "2025-06-05")
 *
 * // 查询特定设备的历史状态
 * devices, err := dataService.GetDevicesStatus([]string{"device_001"}, "2025-06-01")
 */
func (ds *DataService) GetDevicesStatus(deviceIDs []string, date string) ([]models.DeviceStatus, error) {
	// 智能路由：根据数据时效性选择不同的处理策略
	if models.IsToday(date) {
		// 实时数据路径：当天数据优先从Redis缓存获取
		return ds.getTodayDevicesStatus(deviceIDs)
	} else {
		// 历史数据路径：非当天数据采用分层缓存策略
		return ds.getHistoryDevicesStatus(deviceIDs, date)
	}
}

// GetDeviceProduction 获取设备生产数据
func (ds *DataService) GetDeviceProduction(deviceIDs []string, date string) ([]models.ProductionRecord, error) {
	// 从InfluxDB获取生产数据
	return ds.getProductionFromInfluxDB(deviceIDs, date)
}

// getTodayDevicesStatus 获取当天设备状态（实时数据）
// 修改：直接从InfluxDB读取数据，移除Redis缓存依赖
func (ds *DataService) getTodayDevicesStatus(deviceIDs []string) ([]models.DeviceStatus, error) {
	logger.Debug("📊 Getting today's device status (real-time data) - Direct from InfluxDB")

	// 直接从InfluxDB查询，不使用Redis缓存
	devices, err := ds.queryDevicesFromInfluxDB(deviceIDs, models.GetTodayDate())
	if err != nil {
		return nil, fmt.Errorf("failed to query devices from InfluxDB: %w", err)
	}

	logger.Debugf("✅ Retrieved %d devices directly from InfluxDB", len(devices))
	return devices, nil
}

// getHistoryDevicesStatus 获取历史设备状态（固定数据）
func (ds *DataService) getHistoryDevicesStatus(deviceIDs []string, date string) ([]models.DeviceStatus, error) {
	logger.Debugf("📊 Getting historical device status for date: %s", date)

	// 尝试从Redis获取缓存的历史数据
	if ds.redis.IsConnected() {
		// 对于历史数据，我们可以尝试获取单个设备的缓存
		var cachedDevices []models.DeviceStatus
		for _, deviceID := range deviceIDs {
			if device, err := ds.redis.GetDeviceStatus(deviceID); err == nil && device != nil {
				// 检查缓存数据的日期是否匹配
				if device.Timestamp.Format("2006-01-02") == date {
					cachedDevices = append(cachedDevices, *device)
				}
			}
		}

		// 如果缓存中有部分数据，返回缓存数据
		if len(cachedDevices) > 0 && len(cachedDevices) == len(deviceIDs) {
			logger.Debug("✅ Retrieved historical device status from Redis cache")
			return cachedDevices, nil
		}
	}

	// 缓存未命中，从InfluxDB查询
	devices, err := ds.queryDevicesFromInfluxDB(deviceIDs, date)
	if err != nil {
		return nil, fmt.Errorf("failed to query historical devices from InfluxDB: %w", err)
	}

	// 缓存历史数据到Redis（较长的过期时间）
	if ds.redis.IsConnected() {
		go func() {
			for _, device := range devices {
				if err := ds.redis.SetDeviceStatus(device.DeviceID, &device); err != nil {
					logger.Errorf("Failed to cache historical device status to Redis: %v", err)
				}
			}
		}()
	}

	return devices, nil
}

// queryDevicesFromInfluxDB 从InfluxDB查询设备数据
func (ds *DataService) queryDevicesFromInfluxDB(deviceIDs []string, date string) ([]models.DeviceStatus, error) {
	if !ds.influx.IsConnected() {
		return nil, fmt.Errorf("InfluxDB not connected")
	}

	// 解析日期范围 - 修复：转换为UTC时间以匹配InfluxDB存储格式
	startTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	// 将本地时间转换为UTC时间，因为InfluxDB使用UTC存储时间戳
	startTimeUTC := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.UTC)
	endTimeUTC := startTimeUTC.Add(24 * time.Hour)

	logger.Debugf("🕐 Query time range: %s to %s (UTC)", startTimeUTC.Format("2006-01-02T15:04:05Z"), endTimeUTC.Format("2006-01-02T15:04:05Z"))

	// 查询InfluxDB
	rawDevices, err := ds.influx.QueryDeviceStatus(deviceIDs, startTimeUTC, endTimeUTC)
	if err != nil {
		return nil, err
	}

	// 转换为机器状态格式
	devices := ds.convertRawDevicesToMachineStatus(rawDevices)

	// 应用排序（从MongoDB获取排序信息）
	devices = ds.applySortOrderToDevices(devices)

	logger.Debugf("📊 Retrieved %d devices from InfluxDB for date %s", len(devices), date)
	return devices, nil
}

// convertRawDevicesToMachineStatus 将原始设备数据转换为机器状态格式
func (ds *DataService) convertRawDevicesToMachineStatus(rawDevices []models.DeviceStatus) []models.DeviceStatus {
	devices := make([]models.DeviceStatus, len(rawDevices))

	for i, raw := range rawDevices {
		device := models.DeviceStatus{
			DeviceID:   raw.DeviceID,
			DeviceCode: raw.DeviceCode,
			DeviceName: raw.DeviceName,
			Location:   raw.Location,
			Model:      raw.Model,
			DataType:   raw.DataType, // 🔧 修复：复制数据类型字段
			Status:     raw.Status,
			Production: raw.Production,
			Plan:       raw.Plan,
			Timestamp:  raw.Timestamp,
		}

		// 优先从MongoDB获取设备信息
		if ds.mongo != nil {
			if mongoDevice, err := ds.mongo.GetDeviceByDeviceID(raw.DeviceID); err == nil && mongoDevice != nil {
				// 使用MongoDB中的设备信息
				logger.Debugf("✅ Found device in MongoDB: %s -> %s", raw.DeviceID, mongoDevice.Name)
				if mongoDevice.DeviceID != "" {
					device.DeviceCode = mongoDevice.DeviceID // MongoDB中的device_id作为设备编码
				}
				if mongoDevice.Name != "" {
					device.DeviceName = mongoDevice.Name
				}
				if mongoDevice.Location != "" {
					device.Location = mongoDevice.Location
				}
				if mongoDevice.Model != "" {
					device.Model = mongoDevice.Model
				}
				if mongoDevice.Brand != "" {
					device.DataType = mongoDevice.Brand // 使用品牌作为数据类型
				}
			} else {
				// MongoDB中没有找到设备，尝试使用硬编码映射
				if err != nil {
					logger.Debugf("❌ MongoDB query failed for device %s: %v", raw.DeviceID, err)
				} else {
					logger.Debugf("❌ Device not found in MongoDB: %s", raw.DeviceID)
				}
				mapping := models.FindDeviceMapping(raw.DeviceID)
				if mapping != nil {
					logger.Debugf("🔧 Using hardcoded mapping for device %s -> %s", raw.DeviceID, mapping.DeviceName)
					if device.DeviceCode == "" {
						device.DeviceCode = mapping.DeviceCode
					}
					if device.DeviceName == "" {
						device.DeviceName = mapping.DeviceName
					}
					if device.Location == "" {
						device.Location = mapping.Location
					}
					if device.Model == "" {
						device.Model = mapping.Model
					}
				} else {
					logger.Debugf("❌ No hardcoded mapping found for device %s", raw.DeviceID)
				}
			}
		} else {
			// MongoDB不可用，使用硬编码映射
			logger.Debugf("❌ MongoDB service not available for device %s", raw.DeviceID)
			mapping := models.FindDeviceMapping(raw.DeviceID)
			if mapping != nil {
				if device.DeviceCode == "" {
					device.DeviceCode = mapping.DeviceCode
				}
				if device.DeviceName == "" {
					device.DeviceName = mapping.DeviceName
				}
				if device.Location == "" {
					device.Location = mapping.Location
				}
				if device.Model == "" {
					device.Model = mapping.Model
				}
			}
		}

		// 设置默认值（使用"unknown"让用户一目了然地知道这些设备信息未配置）
		if device.DeviceCode == "" {
			device.DeviceCode = "unknown"
		}
		if device.DeviceName == "" {
			device.DeviceName = "unknown"
		}
		if device.Location == "" {
			device.Location = "unknown"
		}
		if device.Model == "" {
			device.Model = "unknown"
		}
		if device.Plan == 0 {
			device.Plan = 1000 // 默认计划产量
		}

		devices[i] = device
	}

	return devices
}

// getProductionFromInfluxDB 从InfluxDB获取生产数据
func (ds *DataService) getProductionFromInfluxDB(deviceIDs []string, date string) ([]models.ProductionRecord, error) {
	// 从InfluxDB查询生产记录数据
	// 这里可以根据实际需要实现具体的查询逻辑
	// 暂时返回空数据，后续可以扩展
	logger.Debugf("📊 Querying production records from InfluxDB for date: %s, devices: %v", date, deviceIDs)

	// TODO: 实现从InfluxDB查询生产记录的逻辑
	// 目前返回空数据
	return []models.ProductionRecord{}, nil
}

// GetDeviceStatusHistory 获取设备状态历史
func (ds *DataService) GetDeviceStatusHistory(deviceIDs []string, date string, workTimeSettings *models.WorkTimeSettings) ([]models.DeviceStatusHistory, error) {
	// 使用默认的24小时时间范围（保持向后兼容）
	return ds.GetDeviceStatusHistoryWithWorkTime(deviceIDs, date, workTimeSettings)
}

/**
 * 按时间范围获取设备状态历史数据
 *
 * 功能：根据指定的时间范围和数据源类型获取设备状态历史数据
 *
 * 数据源选择：
 * - "mongodb": 从MongoDB获取历史数据
 * - "influxdb": 从InfluxDB获取实时数据
 * - 其他值: 自动选择数据源
 *
 * @param {string} deviceID - 设备ID
 * @param {time.Time} startDateTime - 开始时间（UTC时间）
 * @param {time.Time} endDateTime - 结束时间（UTC时间）
 * @param {string} dataSource - 数据源类型（"mongodb"/"influxdb"）
 * @returns {[]models.DeviceStatusHistory} 状态历史列表，按时间排序
 * @returns {error} 查询过程中的错误信息
 */
func (ds *DataService) GetDeviceStatusHistoryByTimeRange(deviceID string, startDateTime, endDateTime time.Time, dataSource string) ([]models.DeviceStatusHistory, error) {
	logger.Debugf("🔍 查询设备状态历史: 设备=%s, 时间范围=%s到%s, 数据源=%s",
		deviceID,
		startDateTime.Format(time.RFC3339),
		endDateTime.Format(time.RFC3339),
		dataSource)

	switch dataSource {
	case "mongodb":
		// 从MongoDB获取历史数据
		if ds.mongo == nil {
			return nil, fmt.Errorf("MongoDB服务不可用")
		}
		return ds.mongo.GetDeviceStatusHistoryByTimeRange(deviceID, startDateTime, endDateTime)

	case "influxdb":
		// 从InfluxDB获取实时数据
		if ds.influx == nil {
			return nil, fmt.Errorf("InfluxDB服务不可用")
		}
		// InfluxDB方法需要设备ID数组和日期字符串
		deviceIDs := []string{deviceID}
		date := startDateTime.Format("2006-01-02")
		return ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startDateTime, endDateTime)

	default:
		// 自动选择数据源（根据时间范围判断）
		now := time.Now().UTC()
		if startDateTime.Before(now.Truncate(24 * time.Hour)) {
			// 包含历史数据，优先使用MongoDB
			if ds.mongo != nil {
				return ds.mongo.GetDeviceStatusHistoryByTimeRange(deviceID, startDateTime, endDateTime)
			}
		}

		// 默认使用InfluxDB
		if ds.influx != nil {
			deviceIDs := []string{deviceID}
			date := startDateTime.Format("2006-01-02")
			return ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startDateTime, endDateTime)
		}

		return nil, fmt.Errorf("没有可用的数据源")
	}
}

// GetDeviceStatusHistoryWithWorkTime 获取设备状态历史（支持工作时间设置）
func (ds *DataService) GetDeviceStatusHistoryWithWorkTime(deviceIDs []string, date string, workTimeSettings *models.WorkTimeSettings) ([]models.DeviceStatusHistory, error) {
	// 计算查询时间范围
	startTimeUTC, endTimeUTC, err := ds.calculateQueryTimeRange(date, workTimeSettings)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate time range: %w", err)
	}

	// 📊 查询设备状态历史数据
	logger.Infof("📊 Querying device status history for %s: %s to %s (workTime: %v)", date, startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339), workTimeSettings != nil)

	// 🔧 修复：智能数据获取策略 - 检查查询时间范围是否跨越今天
	// 需要检查查询的时间范围是否包含今天的数据
	needsHybridQuery := ds.needsHybridDataQuery(startTimeUTC, endTimeUTC, date)
	logger.Errorf("🚨 FORCE DEBUG: Hybrid query check for date %s: needsHybrid=%v, timeRange=%s to %s", date, needsHybridQuery, startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))

	if needsHybridQuery {
		// 跨日查询：需要混合数据源（历史数据从MongoDB，今天数据从InfluxDB）
		logger.Errorf("🚨 FORCE DEBUG: Hybrid query needed for %s: time range spans today", date)
		return ds.getHybridHistoryData(deviceIDs, date, startTimeUTC, endTimeUTC)
	}

	// 🔧 修复：使用一致的时区判断当天数据
	// 判断是否为当天数据 - 使用北京时间进行判断，与calculateQueryTimeRange保持一致
	isToday := ds.isTodayWithTimezone(date)
	logger.Errorf("🚨 FORCE DEBUG: isToday check for date %s: isToday=%v", date, isToday)

	if isToday {
		// 当天数据：直接从InfluxDB获取最新数据
		logger.Errorf("🚨 FORCE DEBUG: Querying today's data from InfluxDB")
		return ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startTimeUTC, endTimeUTC)
	} else {
		// 历史数据：优先从缓存获取
		logger.Errorf("🚨 FORCE DEBUG: Querying historical data")
		return ds.getHistoryStatusHistoryWithTimeRange(deviceIDs, date, startTimeUTC, endTimeUTC)
	}
}

// calculateQueryTimeRange 计算查询时间范围
func (ds *DataService) calculateQueryTimeRange(date string, workTimeSettings *models.WorkTimeSettings) (time.Time, time.Time, error) {
	// 解析日期
	dateTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid date format: %w", err)
	}

	// 如果没有工作时间设置，使用默认的24小时范围
	if workTimeSettings == nil {
		startTimeUTC := time.Date(dateTime.Year(), dateTime.Month(), dateTime.Day(), 0, 0, 0, 0, time.UTC)
		endTimeUTC := startTimeUTC.Add(24 * time.Hour)
		return startTimeUTC, endTimeUTC, nil
	}

	// 解析每天开始时间
	dayStartTime := "00:00"
	if workTimeSettings.DayStartTime != "" {
		dayStartTime = workTimeSettings.DayStartTime
	}

	// 解析时间格式 "HH:MM"
	timeParts := strings.Split(dayStartTime, ":")
	if len(timeParts) != 2 {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid day start time format: %s", dayStartTime)
	}

	hour, err := strconv.Atoi(timeParts[0])
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid hour in day start time: %s", dayStartTime)
	}

	minute, err := strconv.Atoi(timeParts[1])
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid minute in day start time: %s", dayStartTime)
	}

	// 🔧 关键修正：正确处理时区转换
	// 假设工作时间设置是基于北京时间（UTC+8），需要转换为UTC时间
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Warnf("⚠️ Failed to load Beijing timezone, using UTC: %v", err)
		beijingLocation = time.UTC
	}

	// 构建本地时间（北京时间）的开始时间
	startTimeLocal := time.Date(dateTime.Year(), dateTime.Month(), dateTime.Day(), hour, minute, 0, 0, beijingLocation)

	// 转换为UTC时间
	startTimeUTC := startTimeLocal.UTC()

	// 获取当前时间（北京时间和UTC时间）
	nowUTC := time.Now().UTC()
	nowLocal := nowUTC.In(beijingLocation)

	// 计算结束时间
	var endTimeUTC time.Time

	// 正确判断是否为当天：比较本地日期
	currentDateLocal := nowLocal.Format("2006-01-02")
	isToday := date == currentDateLocal

	if isToday {
		// 如果是当天，需要检查当前时间是否已经到了工作开始时间
		if nowLocal.Before(startTimeLocal) {
			// 当前时间还没到工作开始时间，返回空时间范围或查询前一天
			logger.Debugf("📅 Current time (%s) is before work start time (%s), no data available for today",
				nowLocal.Format(time.RFC3339), startTimeLocal.Format(time.RFC3339))
			return time.Time{}, time.Time{}, fmt.Errorf("current time is before work start time")
		}

		// 如果是当天且已经到了工作开始时间，结束时间为当前UTC时间
		endTimeUTC = nowUTC
		logger.Debugf("📅 Today's query (with timezone): local_date=%s, start_local=%s, start_utc=%s, end_utc=%s",
			date, startTimeLocal.Format(time.RFC3339), startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))
	} else {
		// 如果是历史日期，结束时间为本地时间的下一天开始时间转换为UTC
		endTimeLocal := time.Date(dateTime.Year(), dateTime.Month(), dateTime.Day()+1, hour, minute, 0, 0, beijingLocation)
		endTimeUTC = endTimeLocal.UTC()
		logger.Debugf("📅 Historical query (with timezone): local_date=%s, start_local=%s, start_utc=%s, end_local=%s, end_utc=%s",
			date, startTimeLocal.Format(time.RFC3339), startTimeUTC.Format(time.RFC3339), endTimeLocal.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))
	}

	// 验证时间范围的有效性
	if endTimeUTC.Before(startTimeUTC) {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid time range: end time %s is before start time %s", endTimeUTC.Format(time.RFC3339), startTimeUTC.Format(time.RFC3339))
	}

	return startTimeUTC, endTimeUTC, nil
}

// isTodayWithTimezone 使用北京时间判断是否为当天
func (ds *DataService) isTodayWithTimezone(date string) bool {
	// 获取北京时间
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Warnf("⚠️ Failed to load Beijing timezone, using UTC: %v", err)
		beijingLocation = time.UTC
	}

	// 获取当前北京时间的日期
	nowUTC := time.Now().UTC()
	nowLocal := nowUTC.In(beijingLocation)
	currentDateLocal := nowLocal.Format("2006-01-02")

	// 比较日期
	return date == currentDateLocal
}

// getHistoryStatusHistoryWithTimeRange 获取历史状态历史数据（支持时间范围）
func (ds *DataService) getHistoryStatusHistoryWithTimeRange(deviceIDs []string, date string, startTimeUTC, endTimeUTC time.Time) ([]models.DeviceStatusHistory, error) {
	// 🔧 优化存储策略：历史数据优先从MongoDB获取，如果没有则从InfluxDB获取并保存到MongoDB

	// 🔧 修复：检查是否需要跨日期查询（工作时间跨越UTC日期边界）
	needsCrossDayQuery := ds.needsCrossDayQuery(startTimeUTC, endTimeUTC, date)
	logger.Debugf("🔍 Cross-day query check: date=%s, startTime=%s, endTime=%s, needsCrossDay=%v",
		date, startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339), needsCrossDayQuery)

	if needsCrossDayQuery {
		logger.Debugf("🔄 Cross-day query needed for %s: %s to %s", date, startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))
		logger.Errorf("🚨 FORCE DEBUG: About to call getCrossDayHistoryData for date=%s", date)
		result, err := ds.getCrossDayHistoryData(deviceIDs, date, startTimeUTC, endTimeUTC)
		logger.Errorf("🚨 FORCE DEBUG: getCrossDayHistoryData returned %d records, error=%v", len(result), err)
		return result, err
	}

	// 🔧 修复：跨日查询的智能数据获取策略
	// 当天数据从InfluxDB获取，历史数据从MongoDB获取，然后合并输出
	logger.Debugf("🔄 Cross-day query detected, using hybrid data strategy for date: %s", date)

	// MongoDB中没有数据或数据不完整，从InfluxDB查询并合并
	logger.Errorf("🚨 FORCE DEBUG: About to query InfluxDB with time range instead of cross-day query")
	history, err := ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startTimeUTC, endTimeUTC)
	if err != nil {
		return nil, err
	}

	logger.Debugf("📊 Retrieved %d merged status history records from InfluxDB for date %s", len(history), date)

	// 保存到MongoDB（单设备且有数据时）
	if len(deviceIDs) == 1 && len(history) > 0 && ds.mongo != nil {
		go func() {
			deviceID := deviceIDs[0]
			if err := ds.mongo.SaveDeviceStatusHistory(deviceID, date, history); err != nil {
				logger.Errorf("Failed to save device status history to MongoDB: %v", err)
			} else {
				logger.Debugf("✅ Saved %d status history records to MongoDB for %s:%s", len(history), deviceID, date)
			}
		}()
	}

	return history, nil
}

// isHistoryDataComplete 检查历史数据是否覆盖了完整的查询时间范围
func (ds *DataService) isHistoryDataComplete(history []models.DeviceStatusHistory, startTimeUTC, endTimeUTC time.Time) bool {
	if len(history) == 0 {
		return false
	}

	// 找到最早和最晚的时间点
	var earliestStart, latestEnd time.Time
	for i, record := range history {
		if i == 0 {
			earliestStart = record.StartTime
			latestEnd = record.EndTime
		} else {
			if record.StartTime.Before(earliestStart) {
				earliestStart = record.StartTime
			}
			if record.EndTime.After(latestEnd) {
				latestEnd = record.EndTime
			}
		}
	}

	// 检查是否覆盖了完整的查询时间范围
	// 允许一定的时间误差（1分钟）
	tolerance := 1 * time.Minute

	startCovered := earliestStart.Before(startTimeUTC.Add(tolerance)) || earliestStart.Equal(startTimeUTC)
	endCovered := latestEnd.After(endTimeUTC.Add(-tolerance)) || latestEnd.Equal(endTimeUTC)

	logger.Debugf("🔍 Data completeness check: query range [%s, %s], data range [%s, %s], start_covered=%v, end_covered=%v",
		startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339),
		earliestStart.Format(time.RFC3339), latestEnd.Format(time.RFC3339),
		startCovered, endCovered)

	return startCovered && endCovered
}

// needsHybridDataQuery 检查是否需要混合数据源查询
// 当查询时间范围跨越今天时，需要混合历史数据（MongoDB）和实时数据（InfluxDB）
func (ds *DataService) needsHybridDataQuery(startTimeUTC, endTimeUTC time.Time, date string) bool {
	// 获取北京时间的今天日期
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Warnf("⚠️ Failed to load Beijing timezone, using UTC: %v", err)
		beijingLocation = time.UTC
	}

	nowUTC := time.Now().UTC()
	nowLocal := nowUTC.In(beijingLocation)
	todayLocal := nowLocal.Format("2006-01-02")

	// 检查查询的时间范围是否包含今天的数据
	// 将今天的UTC时间范围与查询时间范围进行比较
	todayStartUTC := time.Date(nowLocal.Year(), nowLocal.Month(), nowLocal.Day(), 0, 0, 0, 0, beijingLocation).UTC()
	todayEndUTC := todayStartUTC.Add(24 * time.Hour)

	// 检查查询时间范围是否与今天有重叠
	hasOverlapWithToday := startTimeUTC.Before(todayEndUTC) && endTimeUTC.After(todayStartUTC)

	// 如果查询日期不是今天，但时间范围与今天有重叠，则需要混合查询
	needsHybrid := date != todayLocal && hasOverlapWithToday

	logger.Debugf("🔍 Hybrid query check: date=%s, today=%s, queryRange=[%s,%s], todayRange=[%s,%s], hasOverlap=%v, needsHybrid=%v",
		date, todayLocal,
		startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339),
		todayStartUTC.Format(time.RFC3339), todayEndUTC.Format(time.RFC3339),
		hasOverlapWithToday, needsHybrid)

	return needsHybrid
}

// needsCrossDayQuery 检查是否需要跨日期查询（工作时间跨越UTC日期边界）
func (ds *DataService) needsCrossDayQuery(startTimeUTC, endTimeUTC time.Time, date string) bool {
	// 解析查询日期
	queryDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return false
	}

	// 检查结束时间是否超过了查询日期的UTC边界
	nextDayStart := queryDate.AddDate(0, 0, 1).UTC()

	// 如果结束时间超过或等于次日的UTC 00:00，则需要跨日期查询
	needsCrossDay := endTimeUTC.After(nextDayStart) || endTimeUTC.Equal(nextDayStart)

	logger.Debugf("🔍 Cross-day check: date=%s, endTime=%s, nextDayStart=%s, needsCrossDay=%v",
		date, endTimeUTC.Format(time.RFC3339), nextDayStart.Format(time.RFC3339), needsCrossDay)

	return needsCrossDay
}

// getHybridHistoryData 获取混合数据源的历史数据
// 🔧 新增：智能混合数据获取策略 - 历史部分从MongoDB，今天部分从InfluxDB，合并输出
func (ds *DataService) getHybridHistoryData(deviceIDs []string, date string, startTimeUTC, endTimeUTC time.Time) ([]models.DeviceStatusHistory, error) {
	logger.Debugf("🔄 Getting hybrid history data for %d devices from %s to %s", len(deviceIDs), startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))

	// 获取北京时间的今天日期
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Warnf("⚠️ Failed to load Beijing timezone, using UTC: %v", err)
		beijingLocation = time.UTC
	}

	nowUTC := time.Now().UTC()
	nowLocal := nowUTC.In(beijingLocation)
	todayLocal := nowLocal.Format("2006-01-02")

	// 计算今天的UTC时间边界
	todayStartUTC := time.Date(nowLocal.Year(), nowLocal.Month(), nowLocal.Day(), 0, 0, 0, 0, beijingLocation).UTC()

	var allHistory []models.DeviceStatusHistory

	// 1. 获取历史部分的数据（从startTime到今天开始）
	if startTimeUTC.Before(todayStartUTC) {
		historicalEndTime := todayStartUTC
		if endTimeUTC.Before(todayStartUTC) {
			historicalEndTime = endTimeUTC
		}

		logger.Debugf("📊 Querying historical data from %s to %s", startTimeUTC.Format(time.RFC3339), historicalEndTime.Format(time.RFC3339))

		// 历史数据优先从MongoDB获取
		historicalHistory, err := ds.getHistoryDataByDateType(deviceIDs, date, false, startTimeUTC, historicalEndTime)
		if err != nil {
			logger.Errorf("Failed to query historical data: %v", err)
		} else {
			logger.Debugf("📊 Retrieved %d records from historical data", len(historicalHistory))
			allHistory = append(allHistory, historicalHistory...)
		}
	}

	// 2. 获取今天部分的数据（从今天开始到endTime）
	if endTimeUTC.After(todayStartUTC) {
		todayStartTime := todayStartUTC
		if startTimeUTC.After(todayStartUTC) {
			todayStartTime = startTimeUTC
		}

		logger.Debugf("📊 Querying today's data from %s to %s", todayStartTime.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))

		// 今天的数据直接从InfluxDB获取
		todayHistory, err := ds.getHistoryDataByDateType(deviceIDs, todayLocal, true, todayStartTime, endTimeUTC)
		if err != nil {
			logger.Errorf("Failed to query today's data: %v", err)
		} else {
			logger.Debugf("📊 Retrieved %d records from today's data", len(todayHistory))
			allHistory = append(allHistory, todayHistory...)
		}
	}

	// 3. 按时间排序合并数据
	sort.Slice(allHistory, func(i, j int) bool {
		return allHistory[i].StartTime.Before(allHistory[j].StartTime)
	})

	logger.Debugf("📊 Hybrid query completed: total %d records", len(allHistory))

	return allHistory, nil
}

// getCrossDayHistoryData 获取跨日期的历史数据
// 🔧 修复：智能数据获取策略 - 当天数据从InfluxDB，历史数据从MongoDB，合并输出
func (ds *DataService) getCrossDayHistoryData(deviceIDs []string, date string, startTimeUTC, endTimeUTC time.Time) ([]models.DeviceStatusHistory, error) {
	logger.Debugf("🔄 Getting cross-day history data for %d devices from %s to %s", len(deviceIDs), startTimeUTC.Format(time.RFC3339), endTimeUTC.Format(time.RFC3339))

	// 解析查询日期
	queryDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %s", date)
	}

	nextDay := queryDate.AddDate(0, 0, 1)
	nextDayStr := nextDay.Format("2006-01-02")
	nextDayStart := nextDay.UTC()

	// 判断主日期和次日是否为今天
	todayUTC := time.Now().UTC().Format("2006-01-02")
	isPrimaryToday := date == todayUTC
	isNextDayToday := nextDayStr == todayUTC

	logger.Debugf("🔍 Cross-day query analysis: primary=%s (today=%v), next=%s (today=%v)", date, isPrimaryToday, nextDayStr, isNextDayToday)

	var allHistory []models.DeviceStatusHistory

	// 1. 获取主日期的数据（从startTime到当日结束）
	primaryEndTime := nextDayStart
	primaryHistory, err := ds.getHistoryDataByDateType(deviceIDs, date, isPrimaryToday, startTimeUTC, primaryEndTime)
	if err != nil {
		logger.Errorf("Failed to query primary day data: %v", err)
	} else {
		logger.Debugf("📊 Retrieved %d records from primary day %s", len(primaryHistory), date)
		allHistory = append(allHistory, primaryHistory...)
	}

	// 2. 获取次日的数据（从次日开始到endTime）
	// 只有当结束时间真正晚于次日开始时间时才查询次日数据
	if endTimeUTC.After(nextDayStart) {
		nextDayHistory, err := ds.getHistoryDataByDateType(deviceIDs, nextDayStr, isNextDayToday, nextDayStart, endTimeUTC)
		if err != nil {
			logger.Errorf("Failed to query next day data: %v", err)
		} else {
			logger.Debugf("📊 Retrieved %d records from next day %s", len(nextDayHistory), nextDayStr)
			allHistory = append(allHistory, nextDayHistory...)
		}
	}

	// 3. 按时间排序合并数据
	sort.Slice(allHistory, func(i, j int) bool {
		return allHistory[i].StartTime.Before(allHistory[j].StartTime)
	})

	logger.Debugf("📊 Cross-day query completed: total %d records", len(allHistory))

	return allHistory, nil
}

// getHistoryDataByDateType 根据日期类型智能获取历史数据
// 🔧 新增：当天数据从InfluxDB获取，历史数据优先从MongoDB获取
func (ds *DataService) getHistoryDataByDateType(deviceIDs []string, date string, isToday bool, startTime, endTime time.Time) ([]models.DeviceStatusHistory, error) {
	if isToday {
		// 当天数据：直接从InfluxDB获取最新数据
		logger.Debugf("📊 Querying today's data from InfluxDB for date: %s", date)
		return ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startTime, endTime)
	} else {
		// 历史数据：优先从MongoDB获取，如果没有则从InfluxDB获取
		logger.Debugf("📊 Querying historical data for date: %s", date)

		// 单设备查询时，尝试从MongoDB获取
		if len(deviceIDs) == 1 && ds.mongo != nil {
			deviceID := deviceIDs[0]
			history, err := ds.mongo.GetDeviceStatusHistory(deviceID, date)
			if err != nil {
				logger.Errorf("Failed to query status history from MongoDB: %v", err)
			} else if history != nil && len(history) > 0 {
				// 过滤MongoDB数据到指定时间范围
				filteredHistory := ds.filterHistoryByTimeRange(history, startTime, endTime)
				logger.Debugf("📊 Retrieved %d status history records from MongoDB for %s:%s (filtered to %d)", len(history), deviceID, date, len(filteredHistory))
				return filteredHistory, nil
			}
		}

		// MongoDB中没有数据，从InfluxDB查询
		logger.Debugf("📊 No data in MongoDB, querying from InfluxDB for date: %s", date)
		return ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startTime, endTime)
	}
}

// filterHistoryByTimeRange 按时间范围过滤历史数据
// 🔧 新增：用于过滤MongoDB中的历史数据到指定时间范围
func (ds *DataService) filterHistoryByTimeRange(history []models.DeviceStatusHistory, startTime, endTime time.Time) []models.DeviceStatusHistory {
	var filtered []models.DeviceStatusHistory

	for _, record := range history {
		// 检查记录是否与时间范围有重叠
		if record.EndTime.After(startTime) && record.StartTime.Before(endTime) {
			// 调整记录的时间范围到查询范围内
			adjustedRecord := record
			if adjustedRecord.StartTime.Before(startTime) {
				adjustedRecord.StartTime = startTime
			}
			if adjustedRecord.EndTime.After(endTime) {
				adjustedRecord.EndTime = endTime
			}

			// 重新计算持续时间
			adjustedRecord.Duration = int64(adjustedRecord.EndTime.Sub(adjustedRecord.StartTime).Seconds())

			if adjustedRecord.Duration > 0 {
				filtered = append(filtered, adjustedRecord)
			}
		}
	}

	return filtered
}

// getHistoryStatusHistory 获取历史状态历史数据
func (ds *DataService) getHistoryStatusHistory(deviceIDs []string, date string) ([]models.DeviceStatusHistory, error) {
	// 🔧 优化存储策略：历史数据优先从MongoDB获取，如果没有则从InfluxDB获取并保存到MongoDB

	// 单设备查询时，尝试从MongoDB获取
	if len(deviceIDs) == 1 && ds.mongo != nil {
		deviceID := deviceIDs[0]
		history, err := ds.mongo.GetDeviceStatusHistory(deviceID, date)
		if err != nil {
			logger.Errorf("Failed to query status history from MongoDB: %v", err)
		} else if history != nil {
			logger.Debugf("📊 Retrieved %d status history records from MongoDB for %s:%s", len(history), deviceID, date)
			return history, nil
		}
		logger.Debugf("📊 No status history found in MongoDB for %s:%s, querying InfluxDB", deviceID, date)
	}

	// MongoDB中没有数据，从InfluxDB查询并合并
	history, err := ds.influx.QueryDeviceStatusHistory(deviceIDs, date)
	if err != nil {
		return nil, err
	}

	logger.Debugf("📊 Retrieved %d merged status history records from InfluxDB for date %s", len(history), date)

	// 保存到MongoDB（单设备且有数据时）
	if len(deviceIDs) == 1 && len(history) > 0 && ds.mongo != nil {
		go func() {
			deviceID := deviceIDs[0]
			if err := ds.mongo.SaveDeviceStatusHistory(deviceID, date, history); err != nil {
				logger.Errorf("Failed to save device status history to MongoDB: %v", err)
			} else {
				logger.Debugf("✅ Saved %d status history records to MongoDB for %s:%s", len(history), deviceID, date)
			}
		}()
	}

	return history, nil
}

/**
 * 获取显示设置
 *
 * 功能：从MongoDB获取显示设置，用于状态历史处理中的过滤信号时长等配置
 *
 * @returns {*models.SystemSettings} 显示设置数据
 * @returns {error} 获取过程中的错误信息
 */
func (ds *DataService) GetDisplaySettings() (*models.SystemSettings, error) {
	if ds.mongo == nil {
		return nil, fmt.Errorf("MongoDB service not available")
	}

	return ds.mongo.GetSettings("display")
}

// UpdateTodayCache 更新当天数据缓存
func (ds *DataService) UpdateTodayCache() error {
	if !ds.redis.IsConnected() || !ds.influx.IsConnected() {
		logger.Error("❌ Redis or InfluxDB not connected, skipping cache update")
		return fmt.Errorf("Redis or InfluxDB not connected")
	}

	logger.Info("🔄 Updating today's cache...")

	// 获取当天所有设备数据
	today := models.GetTodayDate()
	logger.Infof("📅 Updating cache for date: %s", today)

	devices, err := ds.queryDevicesFromInfluxDB(nil, today)
	if err != nil {
		logger.Errorf("❌ Failed to query devices from InfluxDB: %v", err)
		return fmt.Errorf("failed to query today's devices: %w", err)
	}

	logger.Infof("📊 Found %d devices from InfluxDB", len(devices))

	// 打印设备时间戳信息用于调试
	for i, device := range devices {
		if i < 2 { // 只打印前2个设备的详细信息
			logger.Infof("🔍 Device %d: ID=%s, Timestamp=%s, Status=%s",
				i+1, device.DeviceID, device.Timestamp.Format("2006-01-02T15:04:05Z"), device.Status)
		}
	}

	// 更新Redis缓存
	logger.Info("💾 Storing devices to Redis cache...")
	if err := ds.redis.SetAllDevicesStatus(devices); err != nil {
		logger.Errorf("❌ Failed to update Redis cache: %v", err)
		return fmt.Errorf("failed to update Redis cache: %w", err)
	}

	// 计算并缓存状态统计
	statusCounts := ds.calculateStatusCounts(devices)
	if err := ds.redis.SetStatusCounts(statusCounts); err != nil {
		logger.Errorf("Failed to cache status counts: %v", err)
	}

	logger.Infof("✅ Successfully updated cache with %d devices", len(devices))
	return nil
}

// startBackgroundTasks 启动后台任务
func (ds *DataService) startBackgroundTasks() {
	// 根据配置决定是否在启动时强制刷新缓存
	if ds.cacheConfig.ForceRefreshOnStartup {
		logger.Info("🔄 Force refreshing cache on startup...")
		if err := ds.UpdateTodayCache(); err != nil {
			logger.Errorf("Initial cache update failed: %v", err)
		}
	}

	// 检查是否启用自动更新
	if !ds.cacheConfig.EnableAutoUpdate {
		logger.Info("⏸️ Auto cache update is disabled")
		return
	}

	// 使用配置中的更新间隔创建定时器
	updateInterval := time.Duration(ds.cacheConfig.UpdateInterval) * time.Second
	logger.Infof("⏰ Starting cache update timer with interval: %v", updateInterval)

	cacheTicker := time.NewTicker(updateInterval)
	defer cacheTicker.Stop()

	// 定时清理过期缓存（每小时）
	cleanupTicker := time.NewTicker(1 * time.Hour)
	defer cleanupTicker.Stop()

	// 启动定时任务：每天00:10处理前一天的状态历史数据
	go ds.startDailyHistoryTask()

	for ds.isActive {
		select {
		case <-cacheTicker.C:
			if err := ds.UpdateTodayCache(); err != nil {
				logger.Errorf("Cache update failed: %v", err)
			}
		case <-cleanupTicker.C:
			if ds.redis.IsConnected() {
				if err := ds.redis.DeleteExpiredKeys(); err != nil {
					logger.Errorf("Cache cleanup failed: %v", err)
				}
			}
		}
	}
}

// filterDevicesByIDs 根据设备ID过滤设备列表
func (ds *DataService) filterDevicesByIDs(devices []models.DeviceStatus, deviceIDs []string) []models.DeviceStatus {
	if len(deviceIDs) == 0 {
		return devices
	}

	// 创建ID映射
	idMap := make(map[string]bool)
	for _, id := range deviceIDs {
		idMap[id] = true
	}

	// 过滤设备
	var filtered []models.DeviceStatus
	for _, device := range devices {
		if idMap[device.DeviceID] {
			filtered = append(filtered, device)
		}
	}

	return filtered
}

// calculateStatusCounts 计算状态统计
func (ds *DataService) calculateStatusCounts(devices []models.DeviceStatus) models.StatusCounts {
	counts := models.StatusCounts{}

	for _, device := range devices {
		switch strings.ToLower(device.Status) {
		case "production":
			counts.Production++
		case "idle":
			counts.Idle++
		case "fault":
			counts.Fault++
		case "adjusting":
			counts.Adjusting++
		case "shutdown":
			counts.Shutdown++
		case "disconnected":
			counts.Disconnected++
		}
	}

	return counts
}

// GetServiceStats 获取服务统计信息
func (ds *DataService) GetServiceStats() map[string]interface{} {
	stats := map[string]interface{}{
		"active":    ds.isActive,
		"timestamp": time.Now(),
	}

	if ds.redis != nil {
		if redisStats, err := ds.redis.GetCacheStats(); err == nil {
			stats["redis"] = redisStats
		}
	}

	if ds.influx != nil {
		stats["influxdb"] = ds.influx.GetConnectionStats()
	}

	return stats
}

/**
 * 获取设备配置信息
 *
 * 功能：根据设备ID从MongoDB中获取设备的详细配置信息
 *
 * 修复说明：
 * - 使用精确匹配而不是模糊搜索，避免返回错误的设备配置
 * - 直接调用MongoDB的精确查询方法，提高查询效率
 * - 确保返回的设备配置与请求的设备ID完全匹配
 *
 * @param {string} deviceID - 设备ID
 * @returns {*models.Device} 设备配置信息，如果未找到则返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (ds *DataService) GetDeviceConfig(deviceID string) (*models.Device, error) {
	// 检查MongoDB服务是否可用
	if ds.mongo == nil {
		return nil, fmt.Errorf("MongoDB service not available")
	}

	// 使用精确匹配查询设备配置
	device, err := ds.mongo.GetDeviceByDeviceID(deviceID)
	if err != nil {
		// 如果是"设备未找到"错误，返回nil而不是错误
		if err.Error() == "device not found" {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to query device config: %w", err)
	}

	return device, nil
}

// startDailyHistoryTask 启动每日状态历史数据处理任务
// 🔧 修复：使用UTC时间进行定时任务调度，确保时区一致性
func (ds *DataService) startDailyHistoryTask() {
	logger.Info("🕐 Starting daily history task scheduler...")

	for ds.isActive {
		// 🔧 修复：使用UTC时间计算下一次执行时间（每天UTC 00:10）
		nowUTC := time.Now().UTC()
		nextRunUTC := time.Date(nowUTC.Year(), nowUTC.Month(), nowUTC.Day()+1, 0, 10, 0, 0, time.UTC)

		// 如果当前UTC时间还没到今天的00:10，则设置为今天的00:10
		todayRunUTC := time.Date(nowUTC.Year(), nowUTC.Month(), nowUTC.Day(), 0, 10, 0, 0, time.UTC)
		if nowUTC.Before(todayRunUTC) {
			nextRunUTC = todayRunUTC
		}

		duration := nextRunUTC.Sub(nowUTC)
		logger.Infof("⏰ Next daily history task scheduled at: %s UTC (in %v)", nextRunUTC.Format("2006-01-02 15:04:05"), duration)

		// 等待到执行时间
		timer := time.NewTimer(duration)
		select {
		case <-timer.C:
			// 执行每日任务
			ds.processDailyHistoryDataUTC()
		case <-time.After(24 * time.Hour):
			// 防止无限等待，每24小时检查一次
			timer.Stop()
			continue
		}
		timer.Stop()

		// 如果服务已停止，退出循环
		if !ds.isActive {
			break
		}
	}
}

// processDailyHistoryData 处理每日状态历史数据（保留原方法用于兼容性）
func (ds *DataService) processDailyHistoryData() {
	// 调用新的UTC版本
	ds.processDailyHistoryDataUTC()
}

// processDailyHistoryDataUTC 处理每日状态历史数据（使用UTC时间）
// 🔧 修复：使用UTC时间进行数据截取，确保时区一致性和数据准确性
func (ds *DataService) processDailyHistoryDataUTC() {
	logger.Info("🔄 Starting daily history data processing with UTC time...")

	// 🔧 修复：使用UTC时间计算前一天的日期
	yesterdayUTC := time.Now().UTC().AddDate(0, 0, -1).Format("2006-01-02")

	// 获取所有设备ID
	if ds.mongo == nil {
		logger.Error("MongoDB service not available for daily history task")
		return
	}

	deviceIDs, err := ds.mongo.GetAllDeviceIDs()
	if err != nil {
		logger.Errorf("Failed to get device IDs for daily history task: %v", err)
		return
	}

	logger.Infof("📋 Processing history data for %d devices on UTC date: %s", len(deviceIDs), yesterdayUTC)

	successCount := 0
	errorCount := 0

	// 遍历所有设备，处理前一天的状态历史数据
	for _, deviceID := range deviceIDs {
		// 检查MongoDB中是否已存在该设备该日期的数据
		existingHistory, err := ds.mongo.GetDeviceStatusHistory(deviceID, yesterdayUTC)
		if err != nil {
			logger.Errorf("Failed to check existing history for %s:%s: %v", deviceID, yesterdayUTC, err)
			errorCount++
			continue
		}

		if existingHistory != nil && len(existingHistory) > 0 {
			logger.Debugf("📊 History data already exists for %s:%s (%d records), skipping", deviceID, yesterdayUTC, len(existingHistory))
			continue
		}

		// 🔧 修复：从InfluxDB获取UTC日期的数据
		history, err := ds.influx.QueryDeviceStatusHistory([]string{deviceID}, yesterdayUTC)
		if err != nil {
			logger.Errorf("Failed to query history from InfluxDB for %s:%s: %v", deviceID, yesterdayUTC, err)
			errorCount++
			continue
		}

		if len(history) == 0 {
			logger.Debugf("📊 No history data found for %s:%s", deviceID, yesterdayUTC)
			continue
		}

		// 保存到MongoDB
		err = ds.mongo.SaveDeviceStatusHistory(deviceID, yesterdayUTC, history)
		if err != nil {
			logger.Errorf("Failed to save history to MongoDB for %s:%s: %v", deviceID, yesterdayUTC, err)
			errorCount++
			continue
		}

		logger.Infof("✅ Processed history data for %s:%s (%d records)", deviceID, yesterdayUTC, len(history))
		successCount++

		// 添加小延迟，避免对数据库造成过大压力
		time.Sleep(100 * time.Millisecond)
	}

	logger.Infof("🎯 Daily history processing completed: %d success, %d errors", successCount, errorCount)
}

// ProcessHistoryDataManually 手动触发历史数据处理（用于测试）
func (ds *DataService) ProcessHistoryDataManually(date string) (int, int, error) {
	logger.Infof("🔄 Manually processing history data for date: %s", date)

	// 获取所有设备ID
	if ds.mongo == nil {
		return 0, 0, fmt.Errorf("MongoDB service not available")
	}

	deviceIDs, err := ds.mongo.GetAllDeviceIDs()
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get device IDs: %v", err)
	}

	logger.Infof("📋 Processing history data for %d devices on date: %s", len(deviceIDs), date)

	successCount := 0
	errorCount := 0

	// 遍历所有设备，处理指定日期的状态历史数据
	for _, deviceID := range deviceIDs {
		// 检查MongoDB中是否已存在该设备该日期的数据
		existingHistory, err := ds.mongo.GetDeviceStatusHistory(deviceID, date)
		if err != nil {
			logger.Errorf("Failed to check existing history for %s:%s: %v", deviceID, date, err)
			errorCount++
			continue
		}

		if existingHistory != nil && len(existingHistory) > 0 {
			logger.Debugf("📊 History data already exists for %s:%s (%d records), skipping", deviceID, date, len(existingHistory))
			continue
		}

		// 从InfluxDB获取合并后的状态历史数据
		history, err := ds.influx.QueryDeviceStatusHistory([]string{deviceID}, date)
		if err != nil {
			logger.Errorf("Failed to query history from InfluxDB for %s:%s: %v", deviceID, date, err)
			errorCount++
			continue
		}

		if len(history) == 0 {
			logger.Debugf("📊 No history data found for %s:%s", deviceID, date)
			continue
		}

		// 保存到MongoDB
		err = ds.mongo.SaveDeviceStatusHistory(deviceID, date, history)
		if err != nil {
			logger.Errorf("Failed to save history to MongoDB for %s:%s: %v", deviceID, date, err)
			errorCount++
			continue
		}

		logger.Infof("✅ Processed history data for %s:%s (%d records)", deviceID, date, len(history))
		successCount++

		// 添加小延迟，避免对数据库造成过大压力
		time.Sleep(100 * time.Millisecond)
	}

	logger.Infof("🎯 Manual history processing completed: %d success, %d errors", successCount, errorCount)
	return successCount, errorCount, nil
}

// RebuildHistoryData 重构指定日期的历史数据
// 🔧 新增：提供手动重构MongoDB历史数据的API接口
// 先删除指定日期的旧数据，再从InfluxDB重新获取并保存
func (ds *DataService) RebuildHistoryData(date string) (int, int, error) {
	logger.Infof("🔄 Rebuilding history data for date: %s", date)

	// 获取所有设备ID
	if ds.mongo == nil {
		return 0, 0, fmt.Errorf("MongoDB service not available")
	}

	deviceIDs, err := ds.mongo.GetAllDeviceIDs()
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get device IDs: %v", err)
	}

	logger.Infof("📋 Rebuilding history data for %d devices on date: %s", len(deviceIDs), date)

	successCount := 0
	errorCount := 0

	// 遍历所有设备，重构指定日期的状态历史数据
	for _, deviceID := range deviceIDs {
		// 🔧 关键：先删除MongoDB中该设备该日期的旧数据
		err := ds.mongo.DeleteDeviceStatusHistory(deviceID, date)
		if err != nil {
			logger.Errorf("Failed to delete existing history for %s:%s: %v", deviceID, date, err)
			errorCount++
			continue
		}
		logger.Debugf("🗑️ Deleted existing history data for %s:%s", deviceID, date)

		// 从InfluxDB获取合并后的状态历史数据
		history, err := ds.influx.QueryDeviceStatusHistory([]string{deviceID}, date)
		if err != nil {
			logger.Errorf("Failed to query history from InfluxDB for %s:%s: %v", deviceID, date, err)
			errorCount++
			continue
		}

		if len(history) == 0 {
			logger.Debugf("📊 No history data found for %s:%s", deviceID, date)
			continue
		}

		// 保存到MongoDB
		err = ds.mongo.SaveDeviceStatusHistory(deviceID, date, history)
		if err != nil {
			logger.Errorf("Failed to save history to MongoDB for %s:%s: %v", deviceID, date, err)
			errorCount++
			continue
		}

		logger.Infof("✅ Rebuilt history data for %s:%s (%d records)", deviceID, date, len(history))
		successCount++

		// 添加小延迟，避免对数据库造成过大压力
		time.Sleep(100 * time.Millisecond)
	}

	logger.Infof("🎯 History data rebuild completed: %d success, %d errors", successCount, errorCount)
	return successCount, errorCount, nil
}

/**
 * 应用设备排序
 *
 * 功能：从MongoDB获取设备的排序配置，并对设备列表进行排序
 *
 * 排序规则：
 * - 优先按照sort_order字段升序排序（数字越小越靠前）
 * - sort_order相同时，按照设备ID排序
 * - 没有配置sort_order的设备排在最后
 *
 * 性能考虑：
 * - 批量查询MongoDB，减少网络开销
 * - 使用map进行快速查找
 * - 稳定排序，保持相同排序值的设备顺序
 *
 * @param {[]models.DeviceStatus} devices - 原始设备状态列表
 * @returns {[]models.DeviceStatus} 排序后的设备状态列表
 */
func (ds *DataService) applySortOrderToDevices(devices []models.DeviceStatus) []models.DeviceStatus {
	// 如果MongoDB服务不可用，直接返回原始列表
	if ds.mongo == nil {
		return devices
	}

	// 获取所有设备的配置信息
	deviceConfigs, err := ds.getDeviceConfigsFromMongo()
	if err != nil {
		logger.Errorf("Failed to get device configs for sorting: %v", err)
		return devices
	}

	// 创建设备ID到排序值的映射
	sortOrderMap := make(map[string]int)
	for _, config := range deviceConfigs {
		sortOrderMap[config.DeviceID] = config.SortOrder
	}

	// 对设备列表进行排序
	sortedDevices := make([]models.DeviceStatus, len(devices))
	copy(sortedDevices, devices)

	// 使用稳定排序算法
	for i := 0; i < len(sortedDevices)-1; i++ {
		for j := i + 1; j < len(sortedDevices); j++ {
			device1 := sortedDevices[i]
			device2 := sortedDevices[j]

			// 获取排序值，默认为999999（最大值）
			sort1, exists1 := sortOrderMap[device1.DeviceID]
			if !exists1 {
				sort1 = 999999
			}

			sort2, exists2 := sortOrderMap[device2.DeviceID]
			if !exists2 {
				sort2 = 999999
			}

			// 比较排序值
			shouldSwap := false
			if sort1 > sort2 {
				shouldSwap = true
			} else if sort1 == sort2 {
				// 排序值相同时，按设备ID排序
				if device1.DeviceID > device2.DeviceID {
					shouldSwap = true
				}
			}

			// 交换位置
			if shouldSwap {
				sortedDevices[i], sortedDevices[j] = sortedDevices[j], sortedDevices[i]
			}
		}
	}

	return sortedDevices
}

/**
 * 从MongoDB获取所有设备配置
 *
 * 功能：批量获取设备配置信息，用于排序和其他用途
 *
 * @returns {[]models.Device} 设备配置列表
 * @returns {error} 查询错误
 */
func (ds *DataService) getDeviceConfigsFromMongo() ([]models.Device, error) {
	// 构建查询参数，获取所有设备
	params := &models.QueryParams{
		Page:     1,
		PageSize: 1000, // 获取所有设备
		SortBy:   "sort_order",
		SortDesc: false,
	}

	// 查询设备列表
	result, err := ds.mongo.GetDevices(params)
	if err != nil {
		return nil, fmt.Errorf("failed to query devices from MongoDB: %w", err)
	}

	// 类型断言获取设备列表
	if devices, ok := result.Data.([]models.Device); ok {
		return devices, nil
	}

	return []models.Device{}, nil
}

/**
 * 获取所有设备配置（公共方法）
 *
 * 功能：为外部服务提供获取所有设备配置的接口
 *
 * @returns {[]models.Device} 设备配置列表
 * @returns {error} 查询错误
 */
func (ds *DataService) GetAllDeviceConfigs() ([]models.Device, error) {
	return ds.getDeviceConfigsFromMongo()
}

func (ds *DataService) GetDeviceByDeviceId(deviceId string) (*models.Device, error) {
	return ds.mongo.GetDeviceByDeviceID(deviceId)
}

func (ds *DataService) GetDeviceList(page, pageSize int) ([]models.Device, error) {

	// 构建查询参数，获取所有设备
	params := &models.QueryParams{
		Page:     page,
		PageSize: pageSize, // 获取所有设备
		SortBy:   "sort_order",
		SortDesc: false,
	}

	// 查询设备列表
	result, err := ds.mongo.GetDevices(params)
	if err != nil {
		return nil, fmt.Errorf("failed to query devices from MongoDB: %w", err)
	}

	// 类型断言获取设备列表
	if devices, ok := result.Data.([]models.Device); ok {
		return devices, nil
	}

	return []models.Device{}, nil
}
