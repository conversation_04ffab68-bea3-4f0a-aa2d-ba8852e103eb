package services

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"server-api/db"
	"server-api/models"
)

// ProductionTaskService 生产任务管理服务
type ProductionTaskService struct {
	mongoManager *db.MongoDBManager
}

// NewProductionTaskService 创建生产任务服务实例（使用统一的MongoDB管理器）
func NewProductionTaskService(mongoManager *db.MongoDBManager) *ProductionTaskService {
	return &ProductionTaskService{
		mongoManager: mongoManager,
	}
}

// getCollection 获取指定名称的集合（内部辅助方法）
func (s *ProductionTaskService) getCollection(collectionName string) (*mongo.Collection, error) {
	if s.mongoManager == nil {
		return nil, fmt.Errorf("MongoDB manager is nil")
	}
	database := s.mongoManager.GetDatabase()
	if database == nil {
		return nil, fmt.Errorf("database not available")
	}
	return database.Collection(collectionName), nil
}

// GetProductionTasks 获取生产任务列表
func (s *ProductionTaskService) GetProductionTasks(params *models.QueryParams) (*models.ListResponse, error) {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if params.Search != "" {
		filter["$or"] = []bson.M{
			{"task_number": bson.M{"$regex": params.Search, "$options": "i"}},
			{"order_number": bson.M{"$regex": params.Search, "$options": "i"}},
			{"product_name": bson.M{"$regex": params.Search, "$options": "i"}},
			{"device_name": bson.M{"$regex": params.Search, "$options": "i"}},
		}
	}
	if params.Status != "" {
		filter["status"] = params.Status
	}

	// 计算总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count production tasks: %v", err)
	}

	// 构建排序
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sort := bson.D{{Key: sortBy, Value: 1}}
	if params.SortDesc {
		sort = bson.D{{Key: sortBy, Value: -1}}
	}

	// 分页查询
	skip := (params.Page - 1) * params.PageSize
	opts := options.Find().
		SetSort(sort).
		SetSkip(int64(skip)).
		SetLimit(int64(params.PageSize))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find production tasks: %v", err)
	}
	defer cursor.Close(ctx)

	var tasks []models.ProductionTask
	if err = cursor.All(ctx, &tasks); err != nil {
		return nil, fmt.Errorf("failed to decode production tasks: %v", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	return &models.ListResponse{
		Data:       tasks,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateProductionTask 创建生产任务
func (s *ProductionTaskService) CreateProductionTask(task *models.ProductionTask) error {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 生成任务编号
	if task.TaskNumber == "" {
		task.TaskNumber = s.generateTaskNumber()
	}

	// 检查任务编号是否已存在
	count, err := collection.CountDocuments(ctx, bson.M{"task_number": task.TaskNumber})
	if err != nil {
		return fmt.Errorf("failed to check task number: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("task number already exists")
	}

	task.CreatedAt = time.Now()
	task.UpdatedAt = time.Now()

	// 设置默认状态
	if task.Status == "" {
		task.Status = models.TaskStatusPlanned
	}

	// 初始化进度
	task.Progress = 0.0

	_, err = collection.InsertOne(ctx, task)
	if err != nil {
		return fmt.Errorf("failed to create production task: %v", err)
	}

	return nil
}

// GetProductionTask 获取单个生产任务
func (s *ProductionTaskService) GetProductionTask(id string) (*models.ProductionTask, error) {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid task ID: %v", err)
	}

	var task models.ProductionTask
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&task)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("production task not found")
		}
		return nil, fmt.Errorf("failed to find production task: %v", err)
	}

	return &task, nil
}

// UpdateProductionTask 更新生产任务
func (s *ProductionTaskService) UpdateProductionTask(id string, task *models.ProductionTask) error {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid task ID: %v", err)
	}

	// 计算进度
	if task.PlannedQuantity > 0 {
		task.Progress = float64(task.ActualQuantity) / float64(task.PlannedQuantity) * 100
		if task.Progress > 100 {
			task.Progress = 100
		}
	}

	task.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"order_id":           task.OrderID,
			"order_number":       task.OrderNumber,
			"product_id":         task.ProductID,
			"product_name":       task.ProductName,
			"device_id":          task.DeviceID,
			"device_name":        task.DeviceName,
			"planned_quantity":   task.PlannedQuantity,
			"actual_quantity":    task.ActualQuantity,
			"qualified_quantity": task.QualifiedQuantity,
			"defective_quantity": task.DefectiveQuantity,
			"status":             task.Status,
			"priority":           task.Priority,
			"planned_start_time": task.PlannedStartTime,
			"planned_end_time":   task.PlannedEndTime,
			"actual_start_time":  task.ActualStartTime,
			"actual_end_time":    task.ActualEndTime,
			"estimated_duration": task.EstimatedDuration,
			"actual_duration":    task.ActualDuration,
			"progress":           task.Progress,
			"notes":              task.Notes,
			"updated_at":         task.UpdatedAt,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update production task: %v", err)
	}

	return nil
}

// DeleteProductionTask 删除生产任务
func (s *ProductionTaskService) DeleteProductionTask(id string) error {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid task ID: %v", err)
	}

	_, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete production task: %v", err)
	}

	return nil
}

// StartTask 开始任务
func (s *ProductionTaskService) StartTask(id string) error {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid task ID: %v", err)
	}

	now := time.Now()
	update := bson.M{
		"$set": bson.M{
			"status":            models.TaskStatusInProgress,
			"actual_start_time": now,
			"updated_at":        now,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to start task: %v", err)
	}

	return nil
}

// CompleteTask 完成任务
func (s *ProductionTaskService) CompleteTask(id string) error {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid task ID: %v", err)
	}

	// 获取任务信息计算实际时长
	var task models.ProductionTask
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&task)
	if err != nil {
		return fmt.Errorf("failed to find task: %v", err)
	}

	now := time.Now()
	actualDuration := 0
	if task.ActualStartTime != nil {
		actualDuration = int(now.Sub(*task.ActualStartTime).Minutes())
	}

	update := bson.M{
		"$set": bson.M{
			"status":          models.TaskStatusCompleted,
			"actual_end_time": now,
			"actual_duration": actualDuration,
			"progress":        100.0,
			"updated_at":      now,
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to complete task: %v", err)
	}

	return nil
}

// GetTasksByDevice 根据设备获取任务
func (s *ProductionTaskService) GetTasksByDevice(deviceID string) ([]models.ProductionTask, error) {
	collection, err := s.getCollection("production_tasks")
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cursor, err := collection.Find(ctx, bson.M{"device_id": deviceID})
	if err != nil {
		return nil, fmt.Errorf("failed to find tasks by device: %v", err)
	}
	defer cursor.Close(ctx)

	var tasks []models.ProductionTask
	if err = cursor.All(ctx, &tasks); err != nil {
		return nil, fmt.Errorf("failed to decode tasks: %v", err)
	}

	return tasks, nil
}

// generateTaskNumber 生成任务编号
func (s *ProductionTaskService) generateTaskNumber() string {
	return fmt.Sprintf("TASK%s", time.Now().Format("20060102150405"))
}
