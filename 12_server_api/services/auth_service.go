/**
 * 用户认证和授权服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的安全核心，负责用户身份认证、权限管理、
 * JWT令牌生成和验证等安全相关功能
 *
 * 主要功能：
 * - 用户认证：用户名密码验证、登录状态管理
 * - JWT令牌管理：令牌生成、验证、刷新、过期处理
 * - 权限控制：基于角色和权限的访问控制（RBAC）
 * - 会话管理：用户会话的创建、维护、销毁
 * - 安全审计：登录日志、操作记录、安全事件追踪
 *
 * 安全特性：
 * - 密码加密：使用bcrypt进行密码哈希存储
 * - JWT安全：HMAC-SHA256签名，防止令牌篡改
 * - 权限分级：管理员、操作员、观察者等角色权限
 * - 会话超时：可配置的令牌过期时间
 * - 防暴力破解：登录失败次数限制和账户锁定
 *
 * 权限模型：
 * - 角色定义：admin（管理员）、operator（操作员）、viewer（观察者）
 * - 权限格式：resource:action（如 device:read、user:write）
 * - 通配符权限：*表示所有权限
 * - 资源分类：设备管理、用户管理、系统配置、数据查看
 *
 * JWT令牌结构：
 * - Header：算法类型（HS256）
 * - Payload：用户ID、用户名、角色、权限列表、过期时间
 * - Signature：HMAC-SHA256签名，确保令牌完整性
 *
 * 业务场景：
 * - 用户登录：Web界面和API的统一认证
 * - API访问控制：保护敏感接口和数据
 * - 操作审计：记录用户操作和权限使用
 * - 系统初始化：自动创建默认管理员账户
 *
 * @package services
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package services

import (
	"fmt"
	"time"

	"server-api/models"
	"shared/logger"

	"github.com/golang-jwt/jwt/v5"
)

/**
 * 认证服务结构体
 *
 * 功能：作为系统的安全网关，提供完整的用户认证和授权功能
 *
 * 架构设计：
 * - 服务分离：认证逻辑与用户管理逻辑分离
 * - 无状态设计：基于JWT的无状态认证机制
 * - 安全配置：可配置的密钥和过期时间
 * - 依赖注入：通过用户服务获取用户数据
 *
 * 安全机制：
 * - JWT签名：使用HMAC-SHA256确保令牌完整性
 * - 密钥管理：安全的密钥存储和轮换机制
 * - 过期控制：可配置的令牌生命周期
 * - 权限验证：细粒度的权限检查机制
 *
 * @struct AuthService
 */
type AuthService struct {
	/** 用户管理服务，提供用户数据访问和密码验证功能 */
	userService *UserService

	/** JWT签名密钥，用于令牌的生成和验证 */
	jwtSecret []byte

	/** 令牌过期时间，控制用户会话的生命周期 */
	tokenExpiry time.Duration
}

/**
 * JWT令牌声明结构体
 *
 * 功能：定义JWT令牌中包含的用户信息和权限数据
 *
 * 数据结构：
 * - 用户标识：用户ID和用户名，用于身份识别
 * - 权限信息：角色和权限列表，用于访问控制
 * - 标准声明：过期时间、签发时间等JWT标准字段
 *
 * 安全考虑：
 * - 最小化原则：只包含必要的用户信息
 * - 权限缓存：避免每次请求都查询数据库
 * - 过期控制：通过ExpiresAt控制令牌生命周期
 *
 * @struct JWTClaims
 */
type JWTClaims struct {
	/** 用户唯一标识符，用于关联用户数据 */
	UserID string `json:"user_id"`

	/** 用户名，用于显示和日志记录 */
	Username string `json:"username"`

	/** 用户角色，用于粗粒度的权限控制 */
	Role string `json:"role"`

	/** 用户权限列表，用于细粒度的权限控制 */
	Permissions []string `json:"permissions"`

	/** JWT标准声明，包含过期时间、签发时间等 */
	jwt.RegisteredClaims
}

// NewAuthService 创建认证服务实例
func NewAuthService(userService *UserService, jwtSecret string) *AuthService {
	return &AuthService{
		userService: userService,
		jwtSecret:   []byte(jwtSecret),
		tokenExpiry: 24 * time.Hour, // 默认24小时过期
	}
}

/**
 * 用户登录认证
 *
 * 功能：处理用户登录请求，验证身份并生成访问令牌
 *
 * 认证流程：
 * 1. 用户名验证：检查用户是否存在
 * 2. 状态检查：验证用户账户是否处于活跃状态
 * 3. 密码验证：使用bcrypt验证密码哈希
 * 4. 令牌生成：创建包含用户信息和权限的JWT令牌
 * 5. 登录记录：更新用户的最后登录时间和IP
 *
 * 安全措施：
 * - 统一错误信息：用户不存在和密码错误返回相同信息，防止用户名枚举
 * - 状态验证：禁用或锁定的账户无法登录
 * - 密码保护：使用安全的密码验证机制
 * - 审计日志：记录所有登录尝试，包括失败的尝试
 *
 * 错误处理：
 * - 用户不存在：返回通用的认证失败信息
 * - 账户禁用：返回账户状态信息
 * - 密码错误：返回通用的认证失败信息
 * - 令牌生成失败：返回系统错误信息
 *
 * 响应数据：
 * - JWT令牌：用于后续API请求的认证
 * - 用户信息：基本的用户资料（不包含敏感信息）
 * - 过期时间：令牌的有效期
 *
 * @param {*models.UserLoginRequest} req - 登录请求对象
 *   - Username: 用户名
 *   - Password: 密码（明文）
 * @returns {*models.UserLoginResponse} 登录响应对象
 *   - Token: JWT访问令牌
 *   - User: 用户基本信息
 *   - ExpiresAt: 令牌过期时间
 * @returns {error} 认证失败或系统错误信息
 *
 * @example
 * req := &models.UserLoginRequest{
 *     Username: "admin",
 *     Password: "admin123",
 * }
 * resp, err := authService.Login(req)
 * if err != nil {
 *     // 处理登录失败
 * } else {
 *     // 使用resp.Token进行后续API调用
 * }
 */
func (s *AuthService) Login(req *models.UserLoginRequest) (*models.UserLoginResponse, error) {
	// 第一步：获取用户信息
	user, err := s.userService.GetUserByUsername(req.Username)
	if err != nil {
		logger.Errorf("Login failed for user %s: user not found", req.Username)
		// 返回通用错误信息，防止用户名枚举攻击
		return nil, fmt.Errorf("invalid username or password")
	}

	// 第二步：检查用户账户状态
	if user.Status != models.UserStatusActive {
		logger.Errorf("Login failed for user %s: user status is %s", req.Username, user.Status)
		return nil, fmt.Errorf("user account is %s", user.Status)
	}

	// 第三步：验证密码
	if !s.userService.VerifyPassword(user, req.Password) {
		logger.Errorf("Login failed for user %s: invalid password", req.Username)
		// 返回通用错误信息，防止密码枚举攻击
		return nil, fmt.Errorf("invalid username or password")
	}

	// 第四步：生成JWT访问令牌
	token, expiresAt, err := s.GenerateToken(user)
	if err != nil {
		logger.Errorf("Failed to generate token for user %s: %v", req.Username, err)
		return nil, fmt.Errorf("failed to generate token")
	}

	// 第五步：更新用户登录信息
	err = s.userService.UpdateLoginInfo(user.ID.Hex())
	if err != nil {
		logger.Errorf("Failed to update login info for user %s: %v", req.Username, err)
		// 登录信息更新失败不影响登录成功，只记录错误
	}

	logger.Infof("User %s logged in successfully", req.Username)

	// 构建登录响应
	return &models.UserLoginResponse{
		Token:     token,     // JWT访问令牌
		User:      *user,     // 用户基本信息
		ExpiresAt: expiresAt, // 令牌过期时间
	}, nil
}

// GenerateToken 生成JWT token
func (s *AuthService) GenerateToken(user *models.User) (string, time.Time, error) {
	expiresAt := time.Now().Add(s.tokenExpiry)

	claims := &JWTClaims{
		UserID:      user.ID.Hex(),
		Username:    user.Username,
		Role:        user.Role,
		Permissions: user.Permissions,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "12_server_api",
			Subject:   user.Username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(s.jwtSecret)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to sign token: %v", err)
	}

	return tokenString, expiresAt, nil
}

// ValidateToken 验证JWT token
func (s *AuthService) ValidateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.jwtSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %v", err)
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// RefreshToken 刷新token
func (s *AuthService) RefreshToken(tokenString string) (*models.UserLoginResponse, error) {
	// 验证当前token
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, fmt.Errorf("invalid token: %v", err)
	}

	// 获取用户信息
	user, err := s.userService.GetUser(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %v", err)
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		return nil, fmt.Errorf("user account is %s", user.Status)
	}

	// 生成新token
	newToken, expiresAt, err := s.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new token: %v", err)
	}

	return &models.UserLoginResponse{
		Token:     newToken,
		User:      *user,
		ExpiresAt: expiresAt,
	}, nil
}

// HasPermission 检查用户是否有指定权限
func (s *AuthService) HasPermission(userPermissions []string, requiredPermission string) bool {
	for _, permission := range userPermissions {
		if permission == requiredPermission {
			return true
		}
		// 检查通配符权限
		if permission == "*" {
			return true
		}
	}
	return false
}

// IsAdmin 检查用户是否是管理员
func (s *AuthService) IsAdmin(role string) bool {
	return role == models.RoleAdmin
}

// CanAccessResource 检查用户是否可以访问资源
func (s *AuthService) CanAccessResource(userRole string, userPermissions []string, resource string, action string) bool {
	// 管理员有所有权限
	if s.IsAdmin(userRole) {
		return true
	}

	// 检查具体权限
	permission := fmt.Sprintf("%s:%s", resource, action)
	return s.HasPermission(userPermissions, permission)
}

// GetUserFromToken 从token中获取用户信息
func (s *AuthService) GetUserFromToken(tokenString string) (*models.User, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	user, err := s.userService.GetUser(claims.UserID)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// InitializeDefaultAdmin 初始化默认管理员账户
func (s *AuthService) InitializeDefaultAdmin() error {
	// 检查是否已存在管理员
	users, err := s.userService.GetUsers(&models.QueryParams{
		Page:     1,
		PageSize: 1,
		Status:   models.UserStatusActive,
	})
	if err != nil {
		return fmt.Errorf("failed to check existing users: %v", err)
	}

	// 如果已有用户，不创建默认管理员
	if users.Total > 0 {
		logger.Info("Users already exist, skipping default admin creation")
		return nil
	}

	// 创建默认管理员
	defaultAdmin := &models.User{
		Username: "admin",
		Email:    "<EMAIL>",
		Password: "admin123", // 将被加密
		Role:     models.RoleAdmin,
		Status:   models.UserStatusActive,
		Profile: models.UserProfile{
			FullName:   "系统管理员",
			Department: "IT部门",
			Position:   "系统管理员",
		},
	}

	err = s.userService.CreateUser(defaultAdmin)
	if err != nil {
		return fmt.Errorf("failed to create default admin: %v", err)
	}

	logger.Info("Default admin user created successfully (username: admin, password: admin123)")
	return nil
}
