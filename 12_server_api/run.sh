#!/bin/bash

# Go服务启动脚本
# 解决Go环境配置问题并启动12_server_api服务

echo "🚀 启动 12_server_api (Go版本)"
echo "=================================="

# 检查并修复Go环境
echo "🔧 检查Go环境..."

# 设置正确的Go环境变量
export PATH=/opt/homebrew/bin:$PATH
export GOROOT=/opt/homebrew/opt/go/libexec

# 验证Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: Go未安装或不在PATH中"
    echo "请确保Go已正确安装在 /opt/homebrew/opt/go"
    exit 1
fi

# 显示Go版本信息
echo "✅ Go版本: $(go version)"
echo "✅ GOROOT: $(go env GOROOT)"
echo "✅ GOPATH: $(go env GOPATH)"

# 检查是否在正确的目录
if [ ! -f "main.go" ]; then
    echo "❌ 错误: 未找到main.go文件"
    echo "请确保在12_server_api目录中运行此脚本"
    exit 1
fi

# 检查端口是否被占用
PORT=9005
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 $PORT 已被占用"
    echo "正在尝试停止占用端口的进程..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 创建logs目录（如果不存在）
if [ ! -d "logs" ]; then
    echo "📁 创建logs目录..."
    mkdir -p logs
fi

echo ""
echo "🌐 启动Go服务器..."
echo "端口: $PORT"
echo "访问地址: http://localhost:$PORT"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 启动Go服务
go run main.go
