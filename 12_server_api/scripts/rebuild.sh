#!/bin/bash

# =============================================================================
# 历史数据重构脚本
# 
# 功能描述：
# 调用12_server_api的重构历史数据API，用于重新计算和整理历史统计数据
# 适用于数据修复、算法更新后的数据重新计算等场景
#
# 使用方法：
# ./scripts/rebuild.sh -f 2025-06-18
# 
# 注意事项：
# 1. 确保12_server_api服务正在运行（端口9005）
# 2. 重构过程可能需要较长时间，请耐心等待
# 3. 重构期间建议避免其他数据操作
#
# <AUTHOR>
# @version 1.0.0
# @since 2025-06-21
# =============================================================================

# 脚本配置
API_HOST="http://localhost:9005"
API_ENDPOINT="/api/admin/rebuild-history"
SCRIPT_NAME="历史数据重构脚本"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 检查服务状态
check_service() {
    log_info "检查12_server_api服务状态..."
    
    # 检查服务是否运行
    if curl -s --connect-timeout 5 "${API_HOST}/health" > /dev/null 2>&1; then
        log_success "12_server_api服务运行正常"
        return 0
    else
        log_error "12_server_api服务未运行或无法连接"
        log_error "请确保服务已启动并监听端口9005"
        return 1
    fi
}

# 执行重构操作
rebuild_history() {
    local target_date="$1"
    local device_ids="$2"
    local force_rebuild="$3"

    log_info "开始执行历史数据重构..."
    log_info "目标日期: ${target_date}"
    log_warning "重构过程可能需要较长时间，请耐心等待..."

    # 构建请求体
    local request_body
    if [[ -n "$device_ids" ]]; then
        # 如果指定了设备ID，构建包含设备列表的请求
        request_body=$(cat <<EOF
{
    "date": "$target_date",
    "device_ids": [$device_ids],
    "force": $force_rebuild
}
EOF
)
    else
        # 默认重构所有设备
        request_body=$(cat <<EOF
{
    "date": "$target_date",
    "force": $force_rebuild
}
EOF
)
    fi

    log_info "请求参数: $request_body"

    # 记录开始时间
    start_time=$(date +%s)

    # 调用重构API
    response=$(curl -s -w "\n%{http_code}" -X POST "${API_HOST}${API_ENDPOINT}" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$request_body")

    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | sed '$d')

    # 计算耗时
    end_time=$(date +%s)
    duration=$((end_time - start_time))

    # 检查响应状态
    case $http_code in
        200)
            log_success "历史数据重构完成！"
            log_info "耗时: ${duration}秒"

            # 尝试解析响应内容
            if command -v jq >/dev/null 2>&1; then
                echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
            else
                echo "$response_body"
            fi
            return 0
            ;;
        400)
            log_error "请求参数错误 (HTTP 400)"
            echo "$response_body"
            return 1
            ;;
        500)
            log_error "服务器内部错误 (HTTP 500)"
            echo "$response_body"
            return 1
            ;;
        *)
            log_error "请求失败 (HTTP $http_code)"
            echo "$response_body"
            return 1
            ;;
    esac
}

# 获取默认日期（昨天）
get_default_date() {
    # 获取昨天的日期，因为通常重构的是历史数据
    if command -v gdate >/dev/null 2>&1; then
        # macOS with GNU date
        gdate -d "yesterday" +%Y-%m-%d
    elif date -d "yesterday" +%Y-%m-%d >/dev/null 2>&1; then
        # Linux with GNU date
        date -d "yesterday" +%Y-%m-%d
    else
        # macOS with BSD date
        date -v-1d +%Y-%m-%d
    fi
}

# 验证日期格式
validate_date() {
    local date_str="$1"
    if [[ ! "$date_str" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
        return 1
    fi

    # 尝试解析日期
    if command -v gdate >/dev/null 2>&1; then
        gdate -d "$date_str" >/dev/null 2>&1
    elif date -d "$date_str" >/dev/null 2>&1; then
        return 0
    else
        # macOS BSD date
        date -j -f "%Y-%m-%d" "$date_str" >/dev/null 2>&1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
${SCRIPT_NAME}

用法: $0 [选项] [日期]

参数:
    日期            要重构的日期，格式：YYYY-MM-DD（可选，默认为昨天）

选项:
    -h, --help      显示此帮助信息
    -c, --check     仅检查服务状态，不执行重构
    -v, --verbose   显示详细输出
    -d, --devices   指定设备ID列表，用逗号分隔（可选）
    -f, --force     强制重构，即使数据已存在

描述:
    此脚本用于调用12_server_api的历史数据重构API。
    重构操作将重新计算和整理指定日期的历史统计数据。

示例:
    $0                          # 重构昨天的所有设备数据
    $0 2025-06-20              # 重构指定日期的所有设备数据
    $0 -d "device1,device2"    # 重构昨天的指定设备数据
    $0 -f 2025-06-20           # 强制重构指定日期的数据
    $0 -c                      # 仅检查服务状态
    $0 --help                  # 显示帮助信息

注意:
    1. 确保12_server_api服务正在运行（端口9005）
    2. 重构过程可能需要较长时间，请耐心等待
    3. 重构期间建议避免其他数据操作
    4. 日期不能是今天或未来日期
    5. 强制重构会删除现有数据并重新生成

EOF
}

# 主函数
main() {
    # 显示脚本标题
    log_header "=================================================="
    log_header "🔧 ${SCRIPT_NAME}"
    log_header "=================================================="

    # 初始化变量
    local target_date=""
    local device_ids=""
    local force_rebuild="true"
    local check_only="false"

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                check_only="true"
                shift
                ;;
            -v|--verbose)
                set -x
                shift
                ;;
            -d|--devices)
                if [[ -n "$2" && "$2" != -* ]]; then
                    device_ids="$2"
                    shift 2
                else
                    log_error "选项 -d/--devices 需要指定设备ID列表"
                    exit 1
                fi
                ;;
            -f|--force)
                force_rebuild="true"
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                log_info "使用 -h 或 --help 查看帮助信息"
                exit 1
                ;;
            *)
                # 位置参数，应该是日期
                if [[ -z "$target_date" ]]; then
                    target_date="$1"
                else
                    log_error "只能指定一个日期参数"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # 如果只是检查服务状态
    if [[ "$check_only" == "true" ]]; then
        check_service
        exit $?
    fi

    # 如果没有指定日期，使用默认日期（昨天）
    if [[ -z "$target_date" ]]; then
        target_date=$(get_default_date)
        log_info "未指定日期，使用默认日期: $target_date"
    fi

    # 验证日期格式
    if ! validate_date "$target_date"; then
        log_error "日期格式无效: $target_date"
        log_error "期望格式: YYYY-MM-DD"
        exit 1
    fi

    # 检查日期不能是今天或未来
    local today=$(date +%Y-%m-%d)
    if [[ "$target_date" > "$today" ]] || [[ "$target_date" == "$today" ]]; then
        log_error "不能重构今天或未来的数据: $target_date"
        log_error "只能重构历史数据（昨天及之前）"
        exit 1
    fi

    # 执行检查和重构
    if check_service; then
        log_info "准备执行历史数据重构..."
        log_info "目标日期: $target_date"
        if [[ -n "$device_ids" ]]; then
            log_info "指定设备: $device_ids"
        else
            log_info "重构范围: 所有设备"
        fi
        log_info "强制重构: $force_rebuild"

        # 确认操作
        echo -e "${YELLOW}⚠️  警告: 此操作将重构指定日期的历史数据，可能需要较长时间${NC}"
        if [[ "$force_rebuild" == "true" ]]; then
            echo -e "${RED}⚠️  强制模式: 将删除现有数据并重新生成${NC}"
        fi
        read -p "是否继续? (y/N): " -n 1 -r
        echo

        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 转换设备ID格式
            local formatted_device_ids=""
            if [[ -n "$device_ids" ]]; then
                # 将逗号分隔的设备ID转换为JSON数组格式
                formatted_device_ids=$(echo "$device_ids" | sed 's/,/","/g' | sed 's/^/"/' | sed 's/$/"/')
            fi

            if rebuild_history "$target_date" "$formatted_device_ids" "$force_rebuild"; then
                log_header "=================================================="
                log_success "✅ 历史数据重构成功完成！"
                log_header "=================================================="
                exit 0
            else
                log_header "=================================================="
                log_error "❌ 历史数据重构失败！"
                log_header "=================================================="
                exit 1
            fi
        else
            log_info "操作已取消"
            exit 0
        fi
    else
        log_error "服务检查失败，无法执行重构操作"
        exit 1
    fi
}

# 脚本入口
main "$@"
