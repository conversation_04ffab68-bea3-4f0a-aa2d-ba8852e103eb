/**
 * MongoDB服务接口定义
 *
 * 功能概述：
 * 本接口定义了MongoDB服务的标准接口，用于解耦具体实现和使用方
 * 支持适配器模式，便于在新旧架构之间进行平滑过渡
 *
 * 设计目标：
 * - 接口隔离：定义清晰的服务边界
 * - 依赖倒置：高层模块不依赖低层模块的具体实现
 * - 可测试性：便于单元测试和模拟测试
 * - 可扩展性：支持多种MongoDB服务实现
 *
 * @package interfaces
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-14
 */
package interfaces

import (
	"server-api/models"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
)

/**
 * MongoDB管理服务接口
 *
 * 功能：定义MongoDB管理服务的标准接口，包含所有业务操作方法
 *
 * 接口设计原则：
 * - 单一职责：每个方法只负责一个具体的业务功能
 * - 接口隔离：将大接口拆分为多个小接口
 * - 依赖倒置：依赖抽象而不是具体实现
 * - 开闭原则：对扩展开放，对修改关闭
 *
 * 方法分类：
 * - 基础方法：连接管理、数据库访问
 * - 设备管理：设备的CRUD操作
 * - 产品管理：产品的CRUD操作
 * - 工序管理：工序的CRUD操作
 * - 计量单位：单位的CRUD操作
 * - 系统设置：配置的读写操作
 * - 数据管理：初始化、清理、统计
 * - 历史数据：状态历史的管理
 *
 * @interface MongoAdminServiceInterface
 */
type MongoAdminServiceInterface interface {
	/**
	 * ===== 基础连接和数据库访问方法 =====
	 */

	/** 获取数据库实例 */
	GetDatabase() *mongo.Database

	/** 获取MongoDB客户端实例 */
	GetClient() *mongo.Client

	/** 关闭连接 */
	Close() error

	/**
	 * ===== 设备管理方法 =====
	 */

	/** 获取设备列表（分页查询） */
	GetDevices(params *models.QueryParams) (*models.ListResponse, error)

	/** 创建设备 */
	CreateDevice(device *models.Device) error

	/** 更新设备 */
	UpdateDevice(id string, device *models.Device) error

	/** 删除设备 */
	DeleteDevice(id string) error

	/** 获取单个设备（通过MongoDB ObjectID） */
	GetDevice(id string) (*models.Device, error)

	/** 通过设备ID获取设备配置（精确匹配） */
	GetDeviceByDeviceID(deviceID string) (*models.Device, error)

	/** 获取设备信息映射（用于API响应） */
	GetDeviceInfoMap(deviceID string) (map[string]interface{}, error)

	/** 获取所有设备ID列表（用于定时任务） */
	GetAllDeviceIDs() ([]string, error)

	/**
	 * ===== 产品管理方法 =====
	 */

	/** 获取产品列表（分页查询） */
	GetProducts(params *models.QueryParams) (*models.ListResponse, error)

	/** 创建产品 */
	CreateProduct(product *models.Product) error

	/** 更新产品 */
	UpdateProduct(id string, product *models.Product) error

	/** 删除产品 */
	DeleteProduct(id string) error

	/** 获取单个产品 */
	GetProduct(id string) (*models.Product, error)

	/**
	 * ===== 工序管理方法 =====
	 */

	/** 获取工序列表（分页查询） */
	GetProcesses(params *models.QueryParams) (*models.ListResponse, error)

	/** 创建工序 */
	CreateProcess(process *models.Process) error

	/** 更新工序 */
	UpdateProcess(id string, process *models.Process) error

	/** 删除工序 */
	DeleteProcess(id string) error

	/** 获取单个工序 */
	GetProcess(id string) (*models.Process, error)

	/**
	 * ===== 计量单位管理方法 =====
	 */

	/** 获取计量单位列表（分页查询） */
	GetUnits(params *models.QueryParams) (*models.ListResponse, error)

	/** 创建计量单位 */
	CreateUnit(unit *models.Unit) error

	/** 更新计量单位 */
	UpdateUnit(id string, unit *models.Unit) error

	/** 删除计量单位 */
	DeleteUnit(id string) error

	/** 获取单个计量单位 */
	GetUnit(id string) (*models.Unit, error)

	/**
	 * ===== 系统设置方法 =====
	 */

	/** 获取系统设置 */
	GetSettings(settingsType string) (*models.SystemSettings, error)

	/** 保存系统设置 */
	SaveSettings(settingsType string, settingsData interface{}) error

	/**
	 * ===== 数据管理方法 =====
	 */

	/** 初始化示例数据 */
	InitializeData() error

	/** 清空所有数据 */
	ClearAllData() error

	/** 重置数据（清空后重新初始化） */
	ResetData() error

	/** 获取数据统计信息 */
	GetStatistics() (map[string]interface{}, error)

	/**
	 * ===== 设备状态历史数据管理方法 =====
	 */

	/** 获取设备状态历史数据 */
	GetDeviceStatusHistory(deviceID, date string) ([]models.DeviceStatusHistory, error)

	/** 按时间范围获取设备状态历史数据 */
	GetDeviceStatusHistoryByTimeRange(deviceID string, startDateTime, endDateTime time.Time) ([]models.DeviceStatusHistory, error)

	/** 保存设备状态历史数据 */
	SaveDeviceStatusHistory(deviceID, date string, statusHistory []models.DeviceStatusHistory) error

	/** 删除设备状态历史数据 */
	DeleteDeviceStatusHistory(deviceID, date string) error

	/**
	 * ===== 扩展方法（用于兼容现有代码） =====
	 */

	/** 批量删除计量单位 */
	BatchDeleteUnits(ids []string) error

	/** 设置默认计量单位 */
	SetDefaultUnit(unitID string) error
}
