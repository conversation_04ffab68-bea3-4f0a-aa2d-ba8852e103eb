/**
 * InfluxDB时序数据库服务 - 基于单例管理器的高级封装
 *
 * 功能概述：
 * 本模块基于InfluxDB管理器单例，提供高级的数据查询和操作服务
 * 封装了复杂的查询逻辑，提供面向业务的API接口
 *
 * 核心特性：
 * - 设备状态查询：实时和历史设备状态数据查询
 * - 数据聚合：多设备数据的聚合和统计
 * - 查询优化：智能查询优化和缓存机制
 * - 错误处理：完善的错误处理和重试机制
 * - 数据转换：原始数据到业务对象的转换
 *
 * 设计模式：
 * - 服务层模式：提供高级业务服务接口
 * - 适配器模式：适配InfluxDB原始API到业务需求
 * - 策略模式：支持不同的查询策略和优化方案
 *
 * @package cache
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-15
 */
package cache

import (
	"fmt"
	"strconv"
	"time"

	"server-api/models"
	"shared/logger"
)

/**
 * InfluxDB服务结构体
 *
 * 功能：基于InfluxDB管理器提供高级数据服务
 *
 * 设计特点：
 * - 依赖注入：依赖InfluxDB管理器单例
 * - 业务封装：将底层查询封装为业务方法
 * - 数据转换：自动处理数据类型转换和验证
 * - 错误处理：统一的错误处理和日志记录
 *
 * @struct InfluxDBService
 */
type InfluxDBService struct {
	/** InfluxDB管理器实例，提供底层连接和查询能力 */
	manager *InfluxDBManager
}

/**
 * 创建新的InfluxDB服务实例
 *
 * 功能：创建基于单例管理器的InfluxDB服务实例
 *
 * @returns {*InfluxDBService} InfluxDB服务实例
 *
 * @example
 * service := cache.NewInfluxDBService()
 * devices, err := service.QueryDeviceStatus(nil, startTime, endTime)
 */
func NewInfluxDBService() *InfluxDBService {
	return &InfluxDBService{
		manager: GetInfluxDBManager(),
	}
}

/**
 * 查询设备状态
 *
 * 功能：查询指定时间范围内的设备状态信息
 *
 * 查询流程：
 * 1. 验证连接状态
 * 2. 执行字符串字段查询
 * 3. 执行数值字段查询
 * 4. 合并查询结果
 * 5. 转换为业务对象
 *
 * 数据处理：
 * - 字段映射：将InfluxDB字段映射到设备属性
 * - 类型转换：自动处理数据类型转换
 * - 数据验证：验证数据的完整性和有效性
 * - 默认值：为缺失字段提供合理默认值
 *
 * @param {[]string} deviceIDs - 设备ID列表，nil表示查询所有设备
 * @param {time.Time} startTime - 查询开始时间
 * @param {time.Time} endTime - 查询结束时间
 * @returns {[]models.DeviceStatus} 设备状态列表
 * @returns {error} 查询错误信息
 *
 * @example
 * // 查询所有设备的当前状态
 * endTime := time.Now()
 * startTime := endTime.Add(-1 * time.Hour)
 * devices, err := service.QueryDeviceStatus(nil, startTime, endTime)
 * if err != nil {
 *     log.Printf("查询失败: %v", err)
 *     return
 * }
 *
 * for _, device := range devices {
 *     fmt.Printf("设备: %s, 状态: %s\n", device.DeviceName, device.Status)
 * }
 */
func (s *InfluxDBService) QueryDeviceStatus(deviceIDs []string, startTime, endTime time.Time) ([]models.DeviceStatus, error) {
	// 检查连接状态
	if !s.manager.IsConnected() {
		return nil, fmt.Errorf("InfluxDB未连接")
	}

	logger.Debugf("📊 查询设备状态: 时间范围 %s 到 %s", startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	// 查询字符串字段
	stringResult, err := s.manager.QueryStringFields(startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("failed to query string fields: %w", err)
	}
	defer stringResult.Close()

	// 解析字符串字段结果
	deviceMap := make(map[string]*models.DeviceStatus)
	recordCount := 0

	for stringResult.Next() {
		record := stringResult.Record()
		deviceID := record.ValueByKey("device_id").(string)
		field := record.ValueByKey("_field").(string)
		value := record.Value()
		timestamp := record.Time()

		// 过滤设备ID（如果指定了）
		if len(deviceIDs) > 0 {
			found := false
			for _, id := range deviceIDs {
				if id == deviceID {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		// 获取或创建设备状态对象
		if deviceMap[deviceID] == nil {
			deviceMap[deviceID] = &models.DeviceStatus{
				DeviceID:  deviceID,
				Timestamp: timestamp,
			}
		}

		device := deviceMap[deviceID]

		// 映射字段值
		switch field {
		case "status_str":
			if value != nil {
				device.Status = value.(string)
			}
		case "main_program_str":
			if value != nil {
				device.Program = value.(string)
			}
		case "device_name_str":
			if value != nil {
				device.DeviceName = value.(string)
			}
		case "brand_str":
			if value != nil {
				// DeviceStatus没有Brand字段，可以存储在DataType中或忽略
				device.DataType = value.(string)
			}
		case "model_str":
			if value != nil {
				device.Model = value.(string)
			}
		case "ip_str":
			if value != nil {
				// DeviceStatus没有IP字段，可以存储在Location中或忽略
				if device.Location == "" {
					device.Location = value.(string)
				}
			}
		case "firmware_version_str":
			if value != nil {
				// DeviceStatus没有FirmwareVersion字段，忽略或存储在其他地方
				// 暂时忽略
			}
		}

		// 更新时间戳为最新的
		if timestamp.After(device.Timestamp) {
			device.Timestamp = timestamp
		}

		recordCount++
	}

	// 检查字符串查询错误
	if stringResult.Err() != nil {
		return nil, fmt.Errorf("failed to query string fields: %w", stringResult.Err())
	}

	logger.Debugf("📊 检索到 %d 条字符串字段记录", recordCount)

	// 查询数值字段
	numberResult, err := s.manager.QueryNumberFields(startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("failed to query number fields: %w", err)
	}
	defer numberResult.Close()

	// 解析数值字段结果
	numberRecordCount := 0
	for numberResult.Next() {
		record := numberResult.Record()
		deviceID := record.ValueByKey("device_id").(string)
		field := record.ValueByKey("_field").(string)
		value := record.Value()
		timestamp := record.Time()

		// 过滤设备ID（如果指定了）
		if len(deviceIDs) > 0 {
			found := false
			for _, id := range deviceIDs {
				if id == deviceID {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		// 获取设备状态对象（应该已存在）
		device := deviceMap[deviceID]
		if device == nil {
			// 如果字符串查询中没有这个设备，创建新的
			device = &models.DeviceStatus{
				DeviceID:  deviceID,
				Timestamp: timestamp,
			}
			deviceMap[deviceID] = device
		}

		// 映射数值字段
		switch field {
		case "total_parts", "quantity":
			if value != nil {
				switch v := value.(type) {
				case int64:
					device.Production = int(v)
				case float64:
					device.Production = int(v)
				case string:
					if intVal, err := strconv.Atoi(v); err == nil {
						device.Production = intVal
					}
				}
			}
		}

		// 更新时间戳为最新的
		if timestamp.After(device.Timestamp) {
			device.Timestamp = timestamp
		}

		numberRecordCount++
	}

	// 检查数值查询错误
	if numberResult.Err() != nil {
		return nil, fmt.Errorf("failed to query number fields: %w", numberResult.Err())
	}

	logger.Debugf("📊 检索到 %d 条数值字段记录", numberRecordCount)

	// 转换为切片
	devices := make([]models.DeviceStatus, 0, len(deviceMap))
	for _, device := range deviceMap {
		// 设置默认值
		if device.Status == "" {
			device.Status = "unknown"
		}
		if device.DeviceName == "" {
			device.DeviceName = "unknown"
		}

		devices = append(devices, *device)
	}

	logger.Debugf("📊 最终返回 %d 个设备状态", len(devices))
	return devices, nil
}

/**
 * 查询设备状态历史
 *
 * 功能：查询指定设备在指定时间范围内的状态变化历史
 *
 * @param {string} deviceID - 设备ID
 * @param {time.Time} startTime - 查询开始时间
 * @param {time.Time} endTime - 查询结束时间
 * @returns {[]models.DeviceStatusHistory} 设备状态历史列表
 * @returns {error} 查询错误信息
 */
func (s *InfluxDBService) QueryDeviceStatusHistory(deviceID string, startTime, endTime time.Time) ([]models.DeviceStatusHistory, error) {
	// 检查连接状态
	if !s.manager.IsConnected() {
		return nil, fmt.Errorf("InfluxDB未连接")
	}

	logger.Debugf("📊 查询设备状态历史: 设备 %s, 时间范围 %s 到 %s", deviceID, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	// 查询状态历史
	result, err := s.manager.QueryDeviceStatusHistory([]string{deviceID}, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("failed to query device status history: %w", err)
	}
	defer result.Close()

	// 解析结果
	var history []models.DeviceStatusHistory
	for result.Next() {
		record := result.Record()
		status := record.Value().(string)
		timestamp := record.Time()

		history = append(history, models.DeviceStatusHistory{
			DeviceID:      deviceID,
			CurrentStatus: status,
			Timestamp:     timestamp,
		})
	}

	// 检查查询错误
	if result.Err() != nil {
		return nil, fmt.Errorf("failed to query device status history: %w", result.Err())
	}

	logger.Debugf("📊 检索到 %d 条状态历史记录", len(history))
	return history, nil
}

/**
 * 测试连接
 *
 * 功能：测试InfluxDB连接的可用性
 *
 * @returns {error} 连接测试结果
 */
func (s *InfluxDBService) TestConnection() error {
	return s.manager.TestConnection()
}

/**
 * 连接InfluxDB
 *
 * 功能：连接到InfluxDB服务器（实际上管理器已经在初始化时连接）
 *
 * @returns {error} 连接错误信息
 */
func (s *InfluxDBService) Connect() error {
	// 管理器已经在初始化时连接，这里只需要检查状态
	if !s.manager.IsConnected() {
		return fmt.Errorf("InfluxDB管理器未连接")
	}
	logger.Info("✅ InfluxDB服务连接成功")
	return nil
}

/**
 * 断开InfluxDB连接
 *
 * 功能：断开InfluxDB连接（实际上由管理器统一管理）
 */
func (s *InfluxDBService) Disconnect() {
	// 连接由管理器统一管理，这里只记录日志
	logger.Info("📊 InfluxDB服务断开连接（由管理器统一管理）")
}

/**
 * 检查连接状态
 *
 * 功能：检查InfluxDB连接的当前状态
 *
 * @returns {bool} 连接状态
 */
func (s *InfluxDBService) IsConnected() bool {
	return s.manager.IsConnected()
}
