/**
 * InfluxDB时序数据库管理器 - 单例模式实现
 *
 * 功能概述：
 * 本模块实现了InfluxDB时序数据库连接的单例管理模式，确保整个应用程序中只有一个InfluxDB连接实例
 * 提供线程安全的连接管理、自动重连、健康检查、查询优化等功能
 *
 * 核心特性：
 * - 单例模式：全局唯一的InfluxDB连接实例，避免连接资源浪费
 * - 线程安全：使用sync.Once确保初始化的原子性和线程安全
 * - 自动重连：连接断开时自动尝试重新连接
 * - 健康检查：定期检查连接状态，确保服务可用性
 * - 查询优化：提供常用查询方法的封装和优化
 * - 优雅关闭：支持优雅关闭连接，避免数据丢失
 *
 * 设计模式：
 * - 单例模式：确保全局唯一实例
 * - 工厂模式：提供统一的实例创建接口
 * - 适配器模式：封装InfluxDB客户端的复杂操作
 *
 * 使用场景：
 * - 设备数据查询：实时设备状态和历史数据查询
 * - 时序数据分析：设备运行趋势和统计分析
 * - 数据聚合：多设备数据的聚合和汇总
 * - 监控告警：基于时序数据的监控和告警
 *
 * @package cache
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-15
 */
package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	"shared/config"
	"shared/logger"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
)

/**
 * InfluxDB连接管理器结构体
 *
 * 功能：作为InfluxDB连接的单例管理器，提供全局唯一的连接实例
 *
 * 设计特点：
 * - 单例模式：确保全局只有一个InfluxDB连接实例
 * - 线程安全：使用互斥锁保护并发访问
 * - 自动重连：连接失败时自动尝试重新连接
 * - 状态管理：实时跟踪连接状态和健康状况
 * - 查询封装：提供常用查询操作的高级封装
 *
 * 生命周期：
 * 1. 初始化：通过GetInstance()获取单例实例
 * 2. 连接：调用Initialize()建立InfluxDB连接
 * 3. 使用：通过各种查询方法进行数据操作
 * 4. 监控：自动进行健康检查和状态更新
 * 5. 关闭：调用Close()优雅关闭连接
 *
 * @struct InfluxDBManager
 */
type InfluxDBManager struct {
	/** InfluxDB客户端实例，负责与InfluxDB服务器的通信 */
	client influxdb2.Client

	/** InfluxDB查询API接口，用于执行查询操作 */
	queryAPI api.QueryAPI

	/** InfluxDB连接配置，包含URL、Token、组织、存储桶等信息 */
	config *config.InfluxDBConfig

	/** 连接状态标志，用于快速判断InfluxDB服务可用性 */
	isConnected bool

	/** 互斥锁，保护并发访问时的线程安全 */
	mutex sync.RWMutex

	/** 全局上下文，用于控制所有InfluxDB操作的生命周期 */
	ctx context.Context

	/** 取消函数，用于优雅关闭时取消所有正在进行的操作 */
	cancel context.CancelFunc
}

/**
 * 单例实例变量
 *
 * 使用包级别的私有变量存储单例实例，确保全局唯一性
 * 配合sync.Once使用，保证初始化的原子性和线程安全
 */
var (
	/** InfluxDB管理器单例实例 */
	influxInstance *InfluxDBManager

	/** 单次初始化控制器，确保单例只被初始化一次 */
	influxOnce sync.Once
)

/**
 * 获取InfluxDB管理器单例实例
 *
 * 功能：实现线程安全的单例模式，确保全局只有一个InfluxDB连接管理器实例
 *
 * 实现细节：
 * - 使用sync.Once确保初始化代码只执行一次
 * - 即使在高并发环境下也能保证线程安全
 * - 延迟初始化：只有在第一次调用时才创建实例
 *
 * 单例优势：
 * - 资源节约：避免创建多个InfluxDB连接，节省系统资源
 * - 状态一致：全局共享同一个连接状态，避免状态不一致
 * - 配置统一：所有模块使用相同的InfluxDB配置
 * - 连接复用：提高连接利用率，减少连接开销
 *
 * 线程安全：
 * - sync.Once保证初始化的原子性
 * - 读写锁保护实例状态的并发访问
 * - 上下文控制保证操作的生命周期管理
 *
 * @returns {*InfluxDBManager} InfluxDB管理器单例实例
 *
 * @example
 * // 获取InfluxDB管理器实例
 * manager := cache.GetInfluxDBManager()
 *
 * // 配置并连接InfluxDB
 * err := manager.Initialize(influxConfig)
 * if err != nil {
 *     log.Fatal("Failed to initialize InfluxDB:", err)
 * }
 *
 * // 执行查询操作
 * devices, err := manager.QueryDeviceStatus(nil, startTime, endTime)
 * if err != nil {
 *     log.Printf("查询失败: %v", err)
 * }
 */
func GetInfluxDBManager() *InfluxDBManager {
	influxOnce.Do(func() {
		// 创建带取消功能的上下文，用于优雅关闭
		ctx, cancel := context.WithCancel(context.Background())

		influxInstance = &InfluxDBManager{
			ctx:         ctx,
			cancel:      cancel,
			isConnected: false,
		}

		logger.Info("🔧 InfluxDB管理器单例实例已创建")
	})
	return influxInstance
}

/**
 * 初始化InfluxDB连接
 *
 * 功能：使用提供的配置初始化InfluxDB连接，建立与InfluxDB服务器的通信
 *
 * 初始化流程：
 * 1. 保存InfluxDB配置信息
 * 2. 创建InfluxDB客户端实例
 * 3. 创建查询API接口
 * 4. 测试连接可用性
 * 5. 更新连接状态
 *
 * 配置验证：
 * - 检查服务器URL的有效性
 * - 验证认证Token的正确性
 * - 确认组织和存储桶的存在性
 * - 验证连接参数的合理性
 *
 * 错误处理：
 * - 配置无效时返回详细错误信息
 * - 连接失败时提供重试建议
 * - 认证失败时给出明确提示
 * - 网络问题时提供诊断信息
 *
 * @param {*config.InfluxDBConfig} cfg - InfluxDB连接配置
 * @returns {error} 初始化过程中的错误信息，成功时返回nil
 *
 * @example
 * config := &config.InfluxDBConfig{
 *     URL:    "http://localhost:8086",
 *     Token:  "your-token-here",
 *     Org:    "your-org",
 *     Bucket: "device-data",
 * }
 *
 * manager := cache.GetInfluxDBManager()
 * err := manager.Initialize(config)
 * if err != nil {
 *     log.Printf("InfluxDB初始化失败: %v", err)
 * }
 */
func (im *InfluxDBManager) Initialize(cfg *config.InfluxDBConfig) error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	logger.Info("📊 正在初始化InfluxDB连接管理器...")

	// 保存配置
	im.config = cfg

	// 创建InfluxDB客户端
	im.client = influxdb2.NewClient(cfg.URL, cfg.Token)

	// 创建查询API
	im.queryAPI = im.client.QueryAPI(cfg.Org)

	// 测试连接
	health, err := im.client.Health(im.ctx)
	if err != nil {
		logger.Errorf("❌ InfluxDB连接测试失败: %v", err)
		return fmt.Errorf("failed to connect to InfluxDB: %w", err)
	}

	if health.Status != "pass" {
		logger.Errorf("❌ InfluxDB健康检查失败: %s", health.Status)
		return fmt.Errorf("InfluxDB health check failed: %s", health.Status)
	}

	im.isConnected = true
	logger.Info("✅ InfluxDB连接管理器初始化成功")
	return nil
}

/**
 * 获取InfluxDB客户端实例
 *
 * 功能：线程安全地获取InfluxDB客户端实例，用于执行InfluxDB操作
 *
 * 安全检查：
 * - 检查管理器是否已初始化
 * - 验证连接状态是否正常
 * - 确保客户端实例可用
 *
 * @returns {influxdb2.Client} InfluxDB客户端实例，未初始化时返回nil
 */
func (im *InfluxDBManager) GetClient() influxdb2.Client {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if !im.isConnected || im.client == nil {
		logger.Warn("⚠️ InfluxDB客户端未初始化或连接已断开")
		return nil
	}

	return im.client
}

/**
 * 获取InfluxDB查询API实例
 *
 * 功能：线程安全地获取InfluxDB查询API实例，用于执行查询操作
 *
 * @returns {api.QueryAPI} InfluxDB查询API实例，未初始化时返回nil
 */
func (im *InfluxDBManager) GetQueryAPI() api.QueryAPI {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if !im.isConnected || im.queryAPI == nil {
		logger.Warn("⚠️ InfluxDB查询API未初始化或连接已断开")
		return nil
	}

	return im.queryAPI
}

/**
 * 检查InfluxDB连接状态
 *
 * 功能：线程安全地检查InfluxDB连接的当前状态
 *
 * @returns {bool} true表示已连接，false表示未连接
 */
func (im *InfluxDBManager) IsConnected() bool {
	im.mutex.RLock()
	defer im.mutex.RUnlock()
	return im.isConnected
}

/**
 * 获取InfluxDB配置信息
 *
 * 功能：线程安全地获取当前的InfluxDB配置信息
 *
 * @returns {*config.InfluxDBConfig} InfluxDB配置信息，未初始化时返回nil
 */
func (im *InfluxDBManager) GetConfig() *config.InfluxDBConfig {
	im.mutex.RLock()
	defer im.mutex.RUnlock()
	return im.config
}

/**
 * 关闭InfluxDB连接
 *
 * 功能：优雅关闭InfluxDB连接，清理相关资源
 *
 * 关闭流程：
 * 1. 取消所有正在进行的操作
 * 2. 关闭InfluxDB客户端连接
 * 3. 更新连接状态
 * 4. 清理资源引用
 *
 * @returns {error} 关闭过程中的错误信息
 */
func (im *InfluxDBManager) Close() error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	logger.Info("🔌 正在关闭InfluxDB连接...")

	// 取消上下文，停止所有操作
	if im.cancel != nil {
		im.cancel()
	}

	// 关闭InfluxDB客户端
	if im.client != nil {
		im.client.Close()
	}

	im.isConnected = false
	im.client = nil
	im.queryAPI = nil
	logger.Info("✅ InfluxDB连接已关闭")
	return nil
}

/**
 * 执行InfluxDB查询
 *
 * 功能：执行Flux查询语句并返回结果
 *
 * 查询特性：
 * - 自动超时控制：防止长时间查询阻塞
 * - 错误处理：提供详细的错误信息
 * - 结果迭代：支持大结果集的流式处理
 * - 资源管理：自动关闭查询结果资源
 *
 * @param {string} query - Flux查询语句
 * @returns {*api.QueryTableResult} 查询结果，错误时返回nil
 * @returns {error} 查询过程中的错误信息
 *
 * @example
 * query := `from(bucket: "device-data") |> range(start: -1h) |> limit(n: 10)`
 * result, err := manager.ExecuteQuery(query)
 * if err != nil {
 *     log.Printf("查询失败: %v", err)
 *     return
 * }
 * defer result.Close()
 *
 * for result.Next() {
 *     record := result.Record()
 *     fmt.Printf("时间: %s, 值: %v\n", record.Time(), record.Value())
 * }
 */
func (im *InfluxDBManager) ExecuteQuery(query string) (*api.QueryTableResult, error) {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if !im.isConnected || im.queryAPI == nil {
		return nil, fmt.Errorf("InfluxDB未连接或查询API不可用")
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(im.ctx, 30*time.Second)
	defer cancel()

	// 执行查询
	result, err := im.queryAPI.Query(ctx, query)
	if err != nil {
		logger.Errorf("❌ InfluxDB查询失败: %v", err)
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}

	return result, nil
}

/**
 * 执行字符串字段查询
 *
 * 功能：查询指定时间范围内的字符串类型字段数据
 *
 * 查询优化：
 * - 字段过滤：只查询字符串类型的字段
 * - 时间范围：精确的时间范围控制
 * - 分组聚合：按设备ID和字段名分组
 * - 最新值：获取每个字段的最新值
 *
 * @param {time.Time} startTime - 查询开始时间
 * @param {time.Time} endTime - 查询结束时间
 * @returns {*api.QueryTableResult} 查询结果
 * @returns {error} 查询错误信息
 */
func (im *InfluxDBManager) QueryStringFields(startTime, endTime time.Time) (*api.QueryTableResult, error) {
	if im.config == nil {
		return nil, fmt.Errorf("InfluxDB配置未初始化")
	}

	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data")
		|> filter(fn: (r) => r["_field"] == "status_str" or r["_field"] == "main_program_str" or r["_field"] == "device_name_str" or r["_field"] == "brand_str" or r["_field"] == "model_str" or r["_field"] == "ip_str" or r["_field"] == "firmware_version_str")
		|> group(columns: ["device_id", "_field"])
		|> last()
		|> group()
	`, im.config.Bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	logger.Debugf("📊 执行字符串字段查询: %s", query)
	return im.ExecuteQuery(query)
}

/**
 * 执行数值字段查询
 *
 * 功能：查询指定时间范围内的数值类型字段数据
 *
 * 查询优化：
 * - 字段过滤：只查询数值类型的字段
 * - 时间范围：精确的时间范围控制
 * - 分组聚合：按设备ID和字段名分组
 * - 最新值：获取每个字段的最新值
 *
 * @param {time.Time} startTime - 查询开始时间
 * @param {time.Time} endTime - 查询结束时间
 * @returns {*api.QueryTableResult} 查询结果
 * @returns {error} 查询错误信息
 */
func (im *InfluxDBManager) QueryNumberFields(startTime, endTime time.Time) (*api.QueryTableResult, error) {
	if im.config == nil {
		return nil, fmt.Errorf("InfluxDB配置未初始化")
	}

	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data")
		|> filter(fn: (r) => r["_field"] == "total_parts" or r["_field"] == "quantity")
		|> group(columns: ["device_id", "_field"])
		|> last()
		|> group()
	`, im.config.Bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	logger.Debugf("📊 执行数值字段查询: %s", query)
	return im.ExecuteQuery(query)
}

/**
 * 查询设备状态历史
 *
 * 功能：查询指定设备在指定时间范围内的状态历史记录
 *
 * 查询特性：
 * - 设备过滤：支持单个或多个设备查询
 * - 状态字段：专门查询设备状态相关字段
 * - 时间排序：按时间顺序返回历史记录
 * - 数据完整性：确保返回完整的状态变化记录
 *
 * @param {[]string} deviceIDs - 设备ID列表，nil表示查询所有设备
 * @param {time.Time} startTime - 查询开始时间
 * @param {time.Time} endTime - 查询结束时间
 * @returns {*api.QueryTableResult} 查询结果
 * @returns {error} 查询错误信息
 */
func (im *InfluxDBManager) QueryDeviceStatusHistory(deviceIDs []string, startTime, endTime time.Time) (*api.QueryTableResult, error) {
	if im.config == nil {
		return nil, fmt.Errorf("InfluxDB配置未初始化")
	}

	// 构建设备过滤条件
	deviceFilter := ""
	if len(deviceIDs) > 0 {
		deviceFilter = "|> filter(fn: (r) => "
		for i, deviceID := range deviceIDs {
			if i > 0 {
				deviceFilter += " or "
			}
			deviceFilter += fmt.Sprintf(`r["device_id"] == "%s"`, deviceID)
		}
		deviceFilter += ")"
	}

	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_data")
		%s
		|> filter(fn: (r) => r["_field"] == "status_str")
		|> sort(columns: ["_time"])
	`, im.config.Bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339), deviceFilter)

	logger.Debugf("📊 执行设备状态历史查询: %s", query)
	return im.ExecuteQuery(query)
}

/**
 * 测试InfluxDB连接
 *
 * 功能：测试当前InfluxDB连接的可用性
 *
 * 测试内容：
 * - 连接状态检查
 * - 健康状态验证
 * - 查询权限测试
 * - 响应时间测量
 *
 * @returns {error} 连接测试结果，成功时返回nil
 */
func (im *InfluxDBManager) TestConnection() error {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if !im.isConnected || im.client == nil {
		return fmt.Errorf("InfluxDB未连接")
	}

	// 测试健康状态
	ctx, cancel := context.WithTimeout(im.ctx, 10*time.Second)
	defer cancel()

	health, err := im.client.Health(ctx)
	if err != nil {
		return fmt.Errorf("健康检查失败: %w", err)
	}

	if health.Status != "pass" {
		return fmt.Errorf("健康状态异常: %s", health.Status)
	}

	// 测试简单查询
	testQuery := fmt.Sprintf(`from(bucket: "%s") |> range(start: -1m) |> limit(n: 1)`, im.config.Bucket)
	result, err := im.queryAPI.Query(ctx, testQuery)
	if err != nil {
		return fmt.Errorf("查询测试失败: %w", err)
	}
	defer result.Close()

	// 检查查询错误
	if result.Err() != nil {
		return fmt.Errorf("查询结果错误: %w", result.Err())
	}

	logger.Info("✅ InfluxDB连接测试成功")
	return nil
}
