/**
 * Redis缓存服务 - 业务层实现
 *
 * 功能概述：
 * 本服务基于Redis管理器单例，提供具体的业务缓存功能
 * 包括设备状态缓存、生产数据缓存、统计信息缓存等业务场景
 *
 * 核心功能：
 * - 设备状态缓存：实时设备状态的高速缓存和查询
 * - 生产数据缓存：产量数据、工艺参数的临时存储
 * - 统计信息缓存：汇总统计数据的缓存管理
 * - 会话数据缓存：用户会话、临时计算结果的存储
 *
 * 缓存策略：
 * - 实时数据：短期缓存（1-6小时），高频更新
 * - 历史数据：长期缓存（1-7天），按需加载
 * - 统计数据：中期缓存（1-24小时），定期刷新
 * - 会话数据：临时缓存（30分钟-2小时），用户相关
 *
 * 性能特性：
 * - 毫秒级响应：内存存储，极快的数据读写速度
 * - 批量操作：支持批量读写，提高吞吐量
 * - 压缩存储：JSON序列化，平衡存储空间和性能
 * - 过期管理：自动清理过期缓存，优化内存使用
 *
 * @package cache
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-14
 */
package cache

import (
	"encoding/json"
	"fmt"
	"time"

	"server-api/models"
	"shared/logger"

	"github.com/redis/go-redis/v9"
)

/**
 * Redis缓存服务结构体
 *
 * 功能：作为业务层的Redis缓存服务，提供具体的缓存操作接口
 *
 * 设计特点：
 * - 业务导向：针对具体业务场景设计的缓存接口
 * - 类型安全：强类型的缓存操作，避免类型错误
 * - 错误处理：完善的错误处理和降级机制
 * - 性能优化：批量操作和连接复用
 *
 * 缓存分类：
 * - 设备缓存：设备状态、产量数据等实时信息
 * - 统计缓存：状态统计、汇总数据等计算结果
 * - 历史缓存：非当日数据的长期缓存
 * - 会话缓存：用户会话、临时数据等短期存储
 *
 * @struct RedisService
 */
type RedisService struct {
	/** Redis管理器实例，提供底层连接管理 */
	manager *RedisManager

	/** Redis键值规范，定义了所有缓存键的命名规则和过期时间 */
	keys models.RedisKeys
}

/**
 * 创建Redis缓存服务实例
 *
 * 功能：基于Redis管理器单例创建缓存服务实例
 *
 * 初始化流程：
 * 1. 获取Redis管理器单例实例
 * 2. 初始化Redis键值规范
 * 3. 验证管理器连接状态
 * 4. 返回可用的服务实例
 *
 * 依赖关系：
 * - 依赖Redis管理器提供底层连接
 * - 依赖models包提供键值规范
 * - 依赖logger包提供日志功能
 *
 * @returns {*RedisService} Redis缓存服务实例
 *
 * @example
 * // 创建Redis缓存服务
 * service := cache.NewRedisService()
 *
 * // 设置设备状态
 * err := service.SetDeviceStatus("device_001", &deviceStatus)
 * if err != nil {
 *     log.Printf("设置设备状态失败: %v", err)
 * }
 */
func NewRedisService() *RedisService {
	logger.Info("🔧 创建Redis缓存服务实例...")

	return &RedisService{
		manager: GetRedisManager(),
		keys:    models.GetRedisKeys(),
	}
}

/**
 * 检查Redis服务可用性
 *
 * 功能：检查Redis连接状态和服务可用性
 *
 * 检查项目：
 * - Redis管理器是否已初始化
 * - Redis连接是否正常
 * - Redis客户端是否可用
 *
 * @returns {bool} true表示服务可用，false表示服务不可用
 */
func (rs *RedisService) IsAvailable() bool {
	if rs.manager == nil {
		return false
	}
	return rs.manager.IsConnected()
}

/**
 * 获取Redis客户端
 *
 * 功能：获取底层Redis客户端实例，用于执行原生Redis操作
 *
 * 使用场景：
 * - 执行复杂的Redis操作
 * - 使用Redis的高级功能
 * - 批量操作和事务处理
 *
 * @returns {*redis.Client} Redis客户端实例，服务不可用时返回nil
 */
func (rs *RedisService) GetClient() *redis.Client {
	if !rs.IsAvailable() {
		return nil
	}
	return rs.manager.GetClient()
}

/**
 * 设置设备状态到Redis
 *
 * 功能：将设备状态信息缓存到Redis，提供快速查询
 *
 * 缓存策略：
 * - 键名格式：device:status:today:{deviceID}
 * - 过期时间：当日数据过期时间（通常为次日凌晨）
 * - 数据格式：JSON序列化的设备状态对象
 *
 * 性能优化：
 * - 使用JSON序列化减少存储空间
 * - 设置合理的过期时间自动清理
 * - 异步操作避免阻塞主流程
 *
 * @param {string} deviceID - 设备唯一标识符
 * @param {*models.DeviceStatus} status - 设备状态对象
 * @returns {error} 操作过程中的错误信息，成功时返回nil
 *
 * @example
 * status := &models.DeviceStatus{
 *     DeviceID:    "device_001",
 *     Status:      "production",
 *     UpdatedAt:   time.Now(),
 *     Temperature: 75.5,
 *     Pressure:    2.3,
 * }
 *
 * err := service.SetDeviceStatus("device_001", status)
 * if err != nil {
 *     log.Printf("设置设备状态失败: %v", err)
 * }
 */
func (rs *RedisService) SetDeviceStatus(deviceID string, status *models.DeviceStatus) error {
	if !rs.IsAvailable() {
		return fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return fmt.Errorf("无法获取Redis客户端")
	}

	// 转换为JSON
	jsonData, err := status.ToJSON()
	if err != nil {
		return fmt.Errorf("序列化设备状态失败: %w", err)
	}

	// 设置到Redis
	key := fmt.Sprintf(rs.keys.DeviceStatusToday, deviceID)
	err = client.Set(rs.manager.ctx, key, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("设置设备状态到Redis失败: %w", err)
	}

	logger.Debugf("📝 已将设备 %s 的状态设置到Redis缓存", deviceID)
	return nil
}

/**
 * 从Redis获取设备状态
 *
 * 功能：从Redis缓存中获取设备状态信息
 *
 * 查询流程：
 * 1. 构建缓存键名
 * 2. 从Redis获取JSON数据
 * 3. 反序列化为设备状态对象
 * 4. 返回结果或错误信息
 *
 * 错误处理：
 * - 缓存未命中：返回nil，不报错
 * - 数据损坏：返回反序列化错误
 * - 连接失败：返回连接错误
 *
 * @param {string} deviceID - 设备唯一标识符
 * @returns {*models.DeviceStatus} 设备状态对象，未找到时返回nil
 * @returns {error} 查询过程中的错误信息
 *
 * @example
 * status, err := service.GetDeviceStatus("device_001")
 * if err != nil {
 *     log.Printf("获取设备状态失败: %v", err)
 *     return
 * }
 *
 * if status == nil {
 *     log.Println("设备状态未找到，可能需要从数据库加载")
 *     return
 * }
 *
 * log.Printf("设备状态: %s, 温度: %.1f°C", status.Status, status.Temperature)
 */
func (rs *RedisService) GetDeviceStatus(deviceID string) (*models.DeviceStatus, error) {
	if !rs.IsAvailable() {
		return nil, fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return nil, fmt.Errorf("无法获取Redis客户端")
	}

	key := fmt.Sprintf(rs.keys.DeviceStatusToday, deviceID)
	jsonData, err := client.Get(rs.manager.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 数据不存在，不是错误
		}
		return nil, fmt.Errorf("从Redis获取设备状态失败: %w", err)
	}

	// 解析JSON
	var status models.DeviceStatus
	err = status.FromJSON(jsonData)
	if err != nil {
		return nil, fmt.Errorf("反序列化设备状态失败: %w", err)
	}

	return &status, nil
}

/**
 * 设置设备产量到Redis
 *
 * 功能：将设备产量信息缓存到Redis
 *
 * @param {string} deviceID - 设备唯一标识符
 * @param {*models.DeviceProduction} production - 设备产量对象
 * @returns {error} 操作过程中的错误信息
 */
func (rs *RedisService) SetDeviceProduction(deviceID string, production *models.DeviceProduction) error {
	if !rs.IsAvailable() {
		return fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return fmt.Errorf("无法获取Redis客户端")
	}

	jsonData, err := production.ToJSON()
	if err != nil {
		return fmt.Errorf("序列化设备产量失败: %w", err)
	}

	key := fmt.Sprintf(rs.keys.DeviceProductionToday, deviceID)
	err = client.Set(rs.manager.ctx, key, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("设置设备产量到Redis失败: %w", err)
	}

	logger.Debugf("📝 已将设备 %s 的产量设置到Redis缓存", deviceID)
	return nil
}

/**
 * 从Redis获取设备产量
 *
 * 功能：从Redis缓存中获取设备产量信息
 *
 * @param {string} deviceID - 设备唯一标识符
 * @returns {*models.DeviceProduction} 设备产量对象，未找到时返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (rs *RedisService) GetDeviceProduction(deviceID string) (*models.DeviceProduction, error) {
	if !rs.IsAvailable() {
		return nil, fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return nil, fmt.Errorf("无法获取Redis客户端")
	}

	key := fmt.Sprintf(rs.keys.DeviceProductionToday, deviceID)
	jsonData, err := client.Get(rs.manager.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("从Redis获取设备产量失败: %w", err)
	}

	var production models.DeviceProduction
	err = production.FromJSON(jsonData)
	if err != nil {
		return nil, fmt.Errorf("反序列化设备产量失败: %w", err)
	}

	return &production, nil
}

/**
 * 设置所有设备状态到Redis
 *
 * 功能：批量设置所有设备的状态信息到Redis缓存
 *
 * @param {[]models.DeviceStatus} devices - 设备状态列表
 * @returns {error} 操作过程中的错误信息
 */
func (rs *RedisService) SetAllDevicesStatus(devices []models.DeviceStatus) error {
	if !rs.IsAvailable() {
		return fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return fmt.Errorf("无法获取Redis客户端")
	}

	jsonData, err := json.Marshal(devices)
	if err != nil {
		return fmt.Errorf("序列化所有设备状态失败: %w", err)
	}

	err = client.Set(rs.manager.ctx, rs.keys.AllDevicesStatus, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("设置所有设备状态到Redis失败: %w", err)
	}

	logger.Debugf("📝 已将 %d 个设备的状态设置到Redis缓存", len(devices))
	return nil
}

/**
 * 从Redis获取所有设备状态
 *
 * 功能：从Redis缓存中获取所有设备的状态信息
 *
 * @returns {[]models.DeviceStatus} 设备状态列表，未找到时返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (rs *RedisService) GetAllDevicesStatus() ([]models.DeviceStatus, error) {
	if !rs.IsAvailable() {
		return nil, fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return nil, fmt.Errorf("无法获取Redis客户端")
	}

	jsonData, err := client.Get(rs.manager.ctx, rs.keys.AllDevicesStatus).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("从Redis获取所有设备状态失败: %w", err)
	}

	var devices []models.DeviceStatus
	err = json.Unmarshal([]byte(jsonData), &devices)
	if err != nil {
		return nil, fmt.Errorf("反序列化所有设备状态失败: %w", err)
	}

	return devices, nil
}

/**
 * 设置状态统计到Redis
 *
 * 功能：将状态统计信息缓存到Redis
 *
 * @param {models.StatusCounts} counts - 状态统计对象
 * @returns {error} 操作过程中的错误信息
 */
func (rs *RedisService) SetStatusCounts(counts models.StatusCounts) error {
	if !rs.IsAvailable() {
		return fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return fmt.Errorf("无法获取Redis客户端")
	}

	jsonData, err := json.Marshal(counts)
	if err != nil {
		return fmt.Errorf("序列化状态统计失败: %w", err)
	}

	err = client.Set(rs.manager.ctx, rs.keys.StatusCounts, jsonData, rs.keys.TodayDataExpiration).Err()
	if err != nil {
		return fmt.Errorf("设置状态统计到Redis失败: %w", err)
	}

	logger.Debugf("📝 已将状态统计设置到Redis缓存")
	return nil
}

/**
 * 从Redis获取状态统计
 *
 * 功能：从Redis缓存中获取状态统计信息
 *
 * @returns {*models.StatusCounts} 状态统计对象，未找到时返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (rs *RedisService) GetStatusCounts() (*models.StatusCounts, error) {
	if !rs.IsAvailable() {
		return nil, fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return nil, fmt.Errorf("无法获取Redis客户端")
	}

	jsonData, err := client.Get(rs.manager.ctx, rs.keys.StatusCounts).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("从Redis获取状态统计失败: %w", err)
	}

	var counts models.StatusCounts
	err = json.Unmarshal([]byte(jsonData), &counts)
	if err != nil {
		return nil, fmt.Errorf("反序列化状态统计失败: %w", err)
	}

	return &counts, nil
}

/**
 * 设置设备状态历史到Redis
 *
 * 功能：将设备状态历史信息缓存到Redis
 *
 * @param {string} deviceID - 设备唯一标识符
 * @param {string} date - 日期字符串（YYYY-MM-DD格式）
 * @param {[]models.DeviceStatusHistory} history - 设备状态历史列表
 * @returns {error} 操作过程中的错误信息
 */
func (rs *RedisService) SetDeviceStatusHistory(deviceID, date string, history []models.DeviceStatusHistory) error {
	if !rs.IsAvailable() {
		return fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return fmt.Errorf("无法获取Redis客户端")
	}

	jsonData, err := json.Marshal(history)
	if err != nil {
		return fmt.Errorf("序列化设备状态历史失败: %w", err)
	}

	key := fmt.Sprintf(rs.keys.DeviceStatusHistory, deviceID, date)
	expiration := rs.keys.HistoryDataExpiration
	if models.IsToday(date) {
		expiration = rs.keys.TodayDataExpiration
	}

	err = client.Set(rs.manager.ctx, key, jsonData, expiration).Err()
	if err != nil {
		return fmt.Errorf("设置设备状态历史到Redis失败: %w", err)
	}

	logger.Debugf("📝 已将设备 %s:%s 的状态历史设置到Redis缓存", deviceID, date)
	return nil
}

/**
 * 从Redis获取设备状态历史
 *
 * 功能：从Redis缓存中获取设备状态历史信息
 *
 * @param {string} deviceID - 设备唯一标识符
 * @param {string} date - 日期字符串（YYYY-MM-DD格式）
 * @returns {[]models.DeviceStatusHistory} 设备状态历史列表，未找到时返回nil
 * @returns {error} 查询过程中的错误信息
 */
func (rs *RedisService) GetDeviceStatusHistory(deviceID, date string) ([]models.DeviceStatusHistory, error) {
	if !rs.IsAvailable() {
		return nil, fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return nil, fmt.Errorf("无法获取Redis客户端")
	}

	key := fmt.Sprintf(rs.keys.DeviceStatusHistory, deviceID, date)
	jsonData, err := client.Get(rs.manager.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("从Redis获取设备状态历史失败: %w", err)
	}

	var history []models.DeviceStatusHistory
	err = json.Unmarshal([]byte(jsonData), &history)
	if err != nil {
		return nil, fmt.Errorf("反序列化设备状态历史失败: %w", err)
	}

	return history, nil
}

/**
 * 删除过期的缓存键
 *
 * 功能：清理过期的Redis缓存键，优化内存使用
 *
 * @returns {error} 操作过程中的错误信息
 */
func (rs *RedisService) DeleteExpiredKeys() error {
	if !rs.IsAvailable() {
		return fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return fmt.Errorf("无法获取Redis客户端")
	}

	// 获取昨天的日期
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	// 删除昨天的实时数据键
	pattern := fmt.Sprintf("device:*:%s", yesterday)
	keys, err := client.Keys(rs.manager.ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("获取过期键失败: %w", err)
	}

	if len(keys) > 0 {
		err = client.Del(rs.manager.ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("删除过期键失败: %w", err)
		}
		logger.Infof("🗑️ 已删除 %d 个过期的Redis缓存键（日期：%s）", len(keys), yesterday)
	}

	return nil
}

/**
 * 获取缓存统计信息
 *
 * 功能：获取Redis缓存的统计信息，用于监控和诊断
 *
 * @returns {map[string]interface{}} 缓存统计信息
 * @returns {error} 操作过程中的错误信息
 */
func (rs *RedisService) GetCacheStats() (map[string]interface{}, error) {
	if !rs.IsAvailable() {
		return nil, fmt.Errorf("Redis服务不可用")
	}

	client := rs.GetClient()
	if client == nil {
		return nil, fmt.Errorf("无法获取Redis客户端")
	}

	info, err := client.Info(rs.manager.ctx, "memory").Result()
	if err != nil {
		return nil, fmt.Errorf("获取Redis信息失败: %w", err)
	}

	stats := map[string]interface{}{
		"connected":   rs.IsAvailable(),
		"memory_info": info,
		"last_check":  time.Now(),
	}

	return stats, nil
}
