/**
 * Redis缓存管理器 - 单例模式实现
 *
 * 功能概述：
 * 本模块实现了Redis缓存连接的单例管理模式，确保整个应用程序中只有一个Redis连接实例
 * 提供线程安全的连接管理、自动重连、健康检查等功能
 *
 * 核心特性：
 * - 单例模式：全局唯一的Redis连接实例，避免连接资源浪费
 * - 线程安全：使用sync.Once确保初始化的原子性和线程安全
 * - 自动重连：连接断开时自动尝试重新连接
 * - 健康检查：定期检查连接状态，确保服务可用性
 * - 优雅关闭：支持优雅关闭连接，避免数据丢失
 *
 * 设计模式：
 * - 单例模式：确保全局唯一实例
 * - 工厂模式：提供统一的实例创建接口
 * - 观察者模式：连接状态变化通知
 *
 * 使用场景：
 * - 设备状态缓存：实时设备状态的高速缓存
 * - 会话管理：用户会话信息的临时存储
 * - 数据预热：热点数据的预加载和缓存
 * - 分布式锁：基于Redis的分布式锁实现
 *
 * @package cache
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-14
 */
package cache

import (
	"context"
	"fmt"
	"sync"

	"shared/config"
	"shared/logger"

	"github.com/redis/go-redis/v9"
)

/**
 * Redis连接管理器结构体
 *
 * 功能：作为Redis连接的单例管理器，提供全局唯一的连接实例
 *
 * 设计特点：
 * - 单例模式：确保全局只有一个Redis连接实例
 * - 线程安全：使用互斥锁保护并发访问
 * - 自动重连：连接失败时自动尝试重新连接
 * - 状态管理：实时跟踪连接状态和健康状况
 *
 * 生命周期：
 * 1. 初始化：通过GetInstance()获取单例实例
 * 2. 连接：调用Connect()建立Redis连接
 * 3. 使用：通过GetClient()获取Redis客户端进行操作
 * 4. 监控：自动进行健康检查和状态更新
 * 5. 关闭：调用Close()优雅关闭连接
 *
 * @struct RedisManager
 */
type RedisManager struct {
	/** Redis客户端实例，负责与Redis服务器的通信 */
	client *redis.Client

	/** Redis连接配置，包含主机、端口、密码、数据库等信息 */
	config *config.RedisConfig

	/** 连接状态标志，用于快速判断Redis服务可用性 */
	isConnected bool

	/** 互斥锁，保护并发访问时的线程安全 */
	mutex sync.RWMutex

	/** 全局上下文，用于控制所有Redis操作的生命周期 */
	ctx context.Context

	/** 取消函数，用于优雅关闭时取消所有正在进行的操作 */
	cancel context.CancelFunc
}

/**
 * 单例实例变量
 *
 * 使用包级别的私有变量存储单例实例，确保全局唯一性
 * 配合sync.Once使用，保证初始化的原子性和线程安全
 */
var (
	/** Redis管理器单例实例 */
	instance *RedisManager

	/** 单次初始化控制器，确保单例只被初始化一次 */
	once sync.Once
)

/**
 * 获取Redis管理器单例实例
 *
 * 功能：实现线程安全的单例模式，确保全局只有一个Redis连接管理器实例
 *
 * 实现细节：
 * - 使用sync.Once确保初始化代码只执行一次
 * - 即使在高并发环境下也能保证线程安全
 * - 延迟初始化：只有在第一次调用时才创建实例
 *
 * 单例优势：
 * - 资源节约：避免创建多个Redis连接，节省系统资源
 * - 状态一致：全局共享同一个连接状态，避免状态不一致
 * - 配置统一：所有模块使用相同的Redis配置
 * - 连接复用：提高连接利用率，减少连接开销
 *
 * 线程安全：
 * - sync.Once保证初始化的原子性
 * - 读写锁保护实例状态的并发访问
 * - 上下文控制保证操作的生命周期管理
 *
 * @returns {*RedisManager} Redis管理器单例实例
 *
 * @example
 * // 获取Redis管理器实例
 * manager := cache.GetRedisManager()
 *
 * // 配置并连接Redis
 * err := manager.Initialize(redisConfig)
 * if err != nil {
 *     log.Fatal("Failed to initialize Redis:", err)
 * }
 *
 * // 获取Redis客户端进行操作
 * client := manager.GetClient()
 * result := client.Set(ctx, "key", "value", time.Hour)
 */
func GetRedisManager() *RedisManager {
	once.Do(func() {
		// 创建带取消功能的上下文，用于优雅关闭
		ctx, cancel := context.WithCancel(context.Background())

		instance = &RedisManager{
			ctx:         ctx,
			cancel:      cancel,
			isConnected: false,
		}

		logger.Info("🔧 Redis管理器单例实例已创建")
	})
	return instance
}

/**
 * 初始化Redis连接
 *
 * 功能：使用提供的配置初始化Redis连接，建立与Redis服务器的通信
 *
 * 初始化流程：
 * 1. 保存Redis配置信息
 * 2. 创建Redis客户端实例
 * 3. 测试连接可用性
 * 4. 更新连接状态
 * 5. 启动健康检查（可选）
 *
 * 配置验证：
 * - 检查主机地址和端口的有效性
 * - 验证认证信息的正确性
 * - 确认数据库索引的合法性
 * - 验证连接池参数的合理性
 *
 * 错误处理：
 * - 配置无效时返回详细错误信息
 * - 连接失败时提供重试建议
 * - 认证失败时给出明确提示
 * - 网络问题时提供诊断信息
 *
 * @param {*config.RedisConfig} cfg - Redis连接配置
 * @returns {error} 初始化过程中的错误信息，成功时返回nil
 *
 * @example
 * config := &config.RedisConfig{
 *     Host:     "localhost",
 *     Port:     6379,
 *     Password: "",
 *     DB:       0,
 *     PoolSize: 10,
 * }
 *
 * manager := cache.GetRedisManager()
 * err := manager.Initialize(config)
 * if err != nil {
 *     log.Printf("Redis初始化失败: %v", err)
 * }
 */
func (rm *RedisManager) Initialize(cfg *config.RedisConfig) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	logger.Info("🔗 正在初始化Redis连接管理器...")

	// 保存配置
	rm.config = cfg

	// 创建Redis客户端
	rm.client = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
		PoolSize: cfg.PoolSize,
	})

	// 测试连接
	_, err := rm.client.Ping(rm.ctx).Result()
	if err != nil {
		logger.Errorf("❌ Redis连接测试失败: %v", err)
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	rm.isConnected = true
	logger.Info("✅ Redis连接管理器初始化成功")
	return nil
}

/**
 * 获取Redis客户端实例
 *
 * 功能：线程安全地获取Redis客户端实例，用于执行Redis操作
 *
 * 安全检查：
 * - 检查管理器是否已初始化
 * - 验证连接状态是否正常
 * - 确保客户端实例可用
 *
 * @returns {*redis.Client} Redis客户端实例，未初始化时返回nil
 */
func (rm *RedisManager) GetClient() *redis.Client {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	if !rm.isConnected || rm.client == nil {
		logger.Warn("⚠️ Redis客户端未初始化或连接已断开")
		return nil
	}

	return rm.client
}

/**
 * 检查Redis连接状态
 *
 * 功能：线程安全地检查Redis连接的当前状态
 *
 * @returns {bool} true表示已连接，false表示未连接
 */
func (rm *RedisManager) IsConnected() bool {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return rm.isConnected
}

/**
 * 关闭Redis连接
 *
 * 功能：优雅关闭Redis连接，清理相关资源
 *
 * 关闭流程：
 * 1. 取消所有正在进行的操作
 * 2. 关闭Redis客户端连接
 * 3. 更新连接状态
 * 4. 清理资源引用
 *
 * @returns {error} 关闭过程中的错误信息
 */
func (rm *RedisManager) Close() error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	logger.Info("🔌 正在关闭Redis连接...")

	// 取消上下文，停止所有操作
	if rm.cancel != nil {
		rm.cancel()
	}

	// 关闭Redis客户端
	if rm.client != nil {
		err := rm.client.Close()
		if err != nil {
			logger.Errorf("❌ 关闭Redis客户端时出错: %v", err)
			return err
		}
	}

	rm.isConnected = false
	rm.client = nil
	logger.Info("✅ Redis连接已关闭")
	return nil
}
