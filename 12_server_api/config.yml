service:
  name: "server-api"
  host: "0.0.0.0"
  port: 9005
  debug: true
  timeout: 30

redis:
  host: "redis"
  port: 6379
  password: ""
  db: 5
  pool_size: 10

cache:
  update_interval: 5                    # 缓存更新间隔（秒）
  enable_auto_update: true              # 是否启用自动缓存更新
  force_refresh_on_startup: true       # 启动时是否强制刷新缓存

sqlite:
  data_dir: "/data/sqlite"
  retention_days: 7
  batch_size: 1000
  flush_interval: 5

nats:
  url: "nats://nats:4222"
  subject: "device.api"
  stream: "API_STREAM"
  consumer: "server-api-consumer"
  batch_size: 100

influxdb:
  url: "http://influxdb:8086"
  token: "mdc-token-123456789"
  org: "mdc-org"
  bucket: "device-data"

mongodb:
  uri: "************************************************************************"
  database: "mdc_admin"
  collection: "device_data"

logging:
  level: "error"
  format: "json"                      # 改为文本格式便于调试
  output: "logs/api.log"                    # 输出到控制台
  rotation:
    enabled: true            # 启用日志轮转
    max_size: 50             # 单个文件最大20MB
    max_age: 3               # 保留3天
    max_backups: 100           # 保留100个备份文件
    compress: true           # 压缩旧文件    
