/**
 * 简化的工作时长计算测试
 *
 * 功能：验证 CalculatingWorkingSeconds 函数的核心逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-24
 */
package main

import (
	"fmt"
	"strings"
	"time"
)

// 模拟 RestPeriod 结构
type RestPeriod struct {
	Name      string
	StartTime string
	EndTime   string
	Enabled   bool
}

// 模拟 WorkTimeSettings 结构
type WorkTimeSettings struct {
	DayStartTime    string
	UtilizationMode string
	RestPeriods     []RestPeriod
}

// 简化的工作时长计算函数（基于原实现逻辑）
func calculateWorkingSeconds(date string, settings WorkTimeSettings) float64 {
	// 获取当前时间
	now := time.Now()
	
	// 确定查询日期
	var queryDate string
	if date == "" {
		queryDate = now.Format("2006-01-02")
	} else {
		queryDate = date
	}
	
	// 判断是否为今天
	today := now.Format("2006-01-02")
	isToday := queryDate == today
	
	fmt.Printf("📅 计算参数: 日期=%s, 是否今天=%v, 模式=%s, 开始时间=%s\n", 
		queryDate, isToday, settings.UtilizationMode, settings.DayStartTime)

	if !isToday {
		// 历史日期：固定计算
		return calculateHistoricalWorkingSeconds(settings)
	}
	
	// 今天：动态计算（简化版本，假设从08:00开始工作到现在）
	return calculateCurrentDayWorkingSeconds(now, settings)
}

// 计算历史日期工作时长
func calculateHistoricalWorkingSeconds(settings WorkTimeSettings) float64 {
	// OP1模式：固定24小时
	if settings.UtilizationMode == "OP1" {
		fmt.Printf("📊 [历史] OP1模式，固定24小时 = 86400秒\n")
		return 86400.0
	}

	// OP2模式：24小时减去休息时间
	baseWorkingSeconds := 86400.0
	totalRestSeconds := 0.0

	for _, restPeriod := range settings.RestPeriods {
		if !restPeriod.Enabled {
			continue
		}

		restDuration := calculateRestDuration(restPeriod.StartTime, restPeriod.EndTime)
		totalRestSeconds += restDuration
		
		fmt.Printf("📊 [历史] 休息时间: %s-%s, 时长: %.0f秒\n", 
			restPeriod.StartTime, restPeriod.EndTime, restDuration)
	}

	workingSeconds := baseWorkingSeconds - totalRestSeconds
	fmt.Printf("📊 [历史] OP2模式，工作时长: %.0f秒 - %.0f秒 = %.0f秒\n", 
		baseWorkingSeconds, totalRestSeconds, workingSeconds)

	return workingSeconds
}

// 计算当天工作时长（简化版本）
func calculateCurrentDayWorkingSeconds(now time.Time, settings WorkTimeSettings) float64 {
	// 简化：假设从08:00开始工作
	startTime := "08:00"
	if settings.DayStartTime != "" {
		startTime = startTime
	}
	
	// 解析开始时间
	startHour, startMinute := parseTime(startTime)
	
	// 构建今日开始时间
	today := time.Date(now.Year(), now.Month(), now.Day(), startHour, startMinute, 0, 0, now.Location())
	
	// 计算基础工作时长
	baseWorkingSeconds := now.Sub(today).Seconds()
	
	if baseWorkingSeconds < 0 {
		baseWorkingSeconds = 0
	}
	
	fmt.Printf("⏰ [当天] 基础工作时长: 从 %s 到 %s = %.0f秒\n", 
		today.Format("15:04:05"), now.Format("15:04:05"), baseWorkingSeconds)
	
	// OP1模式：直接返回
	if settings.UtilizationMode == "OP1" {
		fmt.Printf("📊 [当天] OP1模式，工作时长: %.0f秒\n", baseWorkingSeconds)
		return baseWorkingSeconds
	}
	
	// OP2模式：扣除休息时间（简化处理）
	totalRestSeconds := 0.0
	for _, restPeriod := range settings.RestPeriods {
		if !restPeriod.Enabled {
			continue
		}
		
		// 简化：假设休息时间已过，扣除完整休息时长
		restDuration := calculateRestDuration(restPeriod.StartTime, restPeriod.EndTime)
		totalRestSeconds += restDuration
		
		fmt.Printf("📊 [当天] 休息时间: %s-%s, 时长: %.0f秒\n", 
			restPeriod.StartTime, restPeriod.EndTime, restDuration)
	}
	
	workingSeconds := baseWorkingSeconds - totalRestSeconds
	if workingSeconds < 0 {
		workingSeconds = 0
	}
	
	fmt.Printf("📊 [当天] OP2模式，工作时长: %.0f秒 - %.0f秒 = %.0f秒\n", 
		baseWorkingSeconds, totalRestSeconds, workingSeconds)
	
	return workingSeconds
}

// 计算休息时间段时长
func calculateRestDuration(startTime, endTime string) float64 {
	start, err := time.Parse("15:04", startTime)
	if err != nil {
		fmt.Printf("⚠️ 解析开始时间失败: %v\n", err)
		return 0
	}

	end, err := time.Parse("15:04", endTime)
	if err != nil {
		fmt.Printf("⚠️ 解析结束时间失败: %v\n", err)
		return 0
	}

	// 处理跨天情况
	if end.Before(start) || end.Equal(start) {
		end = end.Add(24 * time.Hour)
	}

	duration := end.Sub(start)
	return duration.Seconds()
}

// 解析时间字符串
func parseTime(timeStr string) (int, int) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		return 8, 0 // 默认08:00
	}
	
	hour := 8
	minute := 0
	
	fmt.Sscanf(parts[0], "%d", &hour)
	fmt.Sscanf(parts[1], "%d", &minute)
	
	return hour, minute
}

func main() {
	fmt.Println("🧪 简化版工作时长计算测试")
	fmt.Println(strings.Repeat("=", 50))

	// 测试用例1：历史日期 OP1模式
	fmt.Println("\n📋 测试1：历史日期 OP1模式")
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	settings1 := WorkTimeSettings{
		DayStartTime:    "08:00",
		UtilizationMode: "OP1",
		RestPeriods:     []RestPeriod{},
	}
	
	result1 := calculateWorkingSeconds(yesterday, settings1)
	fmt.Printf("✅ 结果: %.0f秒 (%.2f小时)\n", result1, result1/3600)

	// 测试用例2：历史日期 OP2模式 有休息时间
	fmt.Println("\n📋 测试2：历史日期 OP2模式 有休息时间")
	restPeriods := []RestPeriod{
		{Name: "午休", StartTime: "12:00", EndTime: "13:00", Enabled: true},
		{Name: "晚休", StartTime: "18:00", EndTime: "18:30", Enabled: true},
	}
	settings2 := WorkTimeSettings{
		DayStartTime:    "08:00",
		UtilizationMode: "OP2",
		RestPeriods:     restPeriods,
	}
	
	result2 := calculateWorkingSeconds(yesterday, settings2)
	fmt.Printf("✅ 结果: %.0f秒 (%.2f小时)\n", result2, result2/3600)

	// 测试用例3：当天 OP1模式
	fmt.Println("\n📋 测试3：当天 OP1模式")
	settings3 := WorkTimeSettings{
		DayStartTime:    "08:00",
		UtilizationMode: "OP1",
		RestPeriods:     []RestPeriod{},
	}
	
	result3 := calculateWorkingSeconds("", settings3)
	fmt.Printf("✅ 结果: %.0f秒 (%.2f小时)\n", result3, result3/3600)

	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("🎉 测试完成！")
}
