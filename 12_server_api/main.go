/**
 * 制造业数据采集系统 - API服务器主程序
 *
 * 系统概述：
 * 本服务是制造业数据采集和管理系统的核心API服务器，提供完整的RESTful API接口
 * 支持设备数据管理、用户认证、生产计划、统计分析等功能
 *
 * 核心功能：
 * - 设备数据实时查询和历史数据分析
 * - 用户认证和权限管理
 * - 生产订单和任务管理
 * - 设备利用率和产量统计
 * - 工作时间配置和班次管理
 * - 自动化统计任务调度
 *
 * 技术架构：
 * - Web框架：Gin (高性能HTTP路由)
 * - 数据存储：InfluxDB (时序数据) + MongoDB (业务数据) + Redis (缓存)
 * - 连接管理：自动重连机制，确保服务高可用性
 * - 日志系统：结构化日志，支持多种输出格式
 * - 认证机制：JWT Token认证
 *
 * 部署特性：
 * - 支持Docker容器化部署
 * - 优雅关闭机制，确保数据完整性
 * - 健康检查和监控接口
 * - 配置文件热加载
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 * @license 企业内部使用
 */
package main

import (
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"server-api/adapters"
	"server-api/cache"
	"server-api/db"
	"server-api/routes"
	"server-api/services"
	"shared/config"
	"shared/logger"
)

/**
 * 数据库连接管理器
 *
 * 功能描述：
 * 负责管理系统中所有数据库连接的生命周期，包括连接建立、健康检查、
 * 自动重连和优雅关闭等功能
 *
 * 设计特点：
 * - 指数退避重连策略，避免对数据库造成压力
 * - 原子操作保证线程安全
 * - 支持优雅关闭，确保数据完整性
 * - 可配置的重连参数，适应不同环境需求
 *
 * 使用场景：
 * - InfluxDB时序数据库连接管理
 * - MongoDB文档数据库连接管理
 * - Redis缓存数据库连接管理
 *
 * @struct ConnectionManager
 */
type ConnectionManager struct {
	/** InfluxDB连接状态标志，使用原子操作保证线程安全 (0=断开, 1=已连接) */
	influxConnected int32

	/** InfluxDB当前重连尝试次数，用于指数退避计算 */
	influxRetries int32

	/** 最大重连尝试次数，超过此次数将停止重连 */
	maxRetries int

	/** 重连基础延迟时间，作为指数退避的起始值 */
	baseDelay time.Duration

	/** 重连最大延迟时间，防止延迟时间过长 */
	maxDelay time.Duration

	/** 系统关闭信号通道，用于通知所有goroutine优雅退出 */
	shutdown chan struct{}

	/** 等待组，确保所有连接管理goroutine完成后再退出 */
	wg sync.WaitGroup
}

/**
 * 创建数据库连接管理器实例
 *
 * 功能：初始化连接管理器，设置合理的默认参数
 *
 * 默认配置说明：
 * - 最大重连次数：5次，平衡重连尝试和系统稳定性
 * - 基础延迟：2秒，避免频繁重连对数据库造成压力
 * - 最大延迟：2分钟，防止长时间等待影响系统响应
 *
 * 重连策略：
 * 采用指数退避算法，重连间隔依次为：2s, 4s, 8s, 16s, 32s
 * 超过最大延迟时间后保持在最大延迟值
 *
 * 线程安全：
 * 所有状态变量都使用原子操作或通道，确保并发安全
 *
 * @returns {*ConnectionManager} 初始化完成的连接管理器实例
 *
 * @example
 * connMgr := NewConnectionManager()
 * defer connMgr.Shutdown()
 */
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		maxRetries: 5,                   // 最大重连5次，避免无限重连
		baseDelay:  time.Second * 2,     // 基础延迟2秒，给数据库恢复时间
		maxDelay:   time.Minute * 2,     // 最大延迟2分钟，防止过长等待
		shutdown:   make(chan struct{}), // 创建关闭信号通道
	}
}

/**
 * 检查InfluxDB连接状态
 *
 * 功能：线程安全地检查InfluxDB数据库的当前连接状态
 *
 * 实现细节：
 * - 使用原子操作读取连接状态，确保并发安全
 * - 状态值：0表示断开连接，1表示已连接
 * - 无阻塞操作，可在任何goroutine中安全调用
 *
 * 使用场景：
 * - 健康检查接口中报告数据库状态
 * - 业务逻辑中判断是否可以执行数据库操作
 * - 监控系统中收集连接状态指标
 *
 * @receiver {*ConnectionManager} cm - 连接管理器实例
 * @returns {bool} true表示已连接，false表示未连接
 *
 * @example
 * if connMgr.IsInfluxDBConnected() {
 *     // 执行数据库操作
 * } else {
 *     // 返回服务不可用错误
 * }
 */
func (cm *ConnectionManager) IsInfluxDBConnected() bool {
	return atomic.LoadInt32(&cm.influxConnected) == 1
}

/**
 * API服务器核心结构体
 *
 * 功能描述：
 * 作为整个API服务的核心控制器，管理所有业务服务的生命周期
 * 协调各个服务模块之间的交互，提供统一的服务接口
 *
 * 架构设计：
 * - 分层架构：配置层 -> 服务层 -> 数据层
 * - 依赖注入：通过构造函数注入各种服务依赖
 * - 服务隔离：每个业务领域独立的服务模块
 * - 连接管理：统一的数据库连接生命周期管理
 *
 * 服务分类：
 * 1. 基础设施服务：Redis、InfluxDB、MongoDB连接管理
 * 2. 数据服务：数据采集、存储、查询的统一接口
 * 3. 业务服务：用户管理、生产管理、统计分析
 * 4. 系统服务：监控、调度、认证等系统级功能
 *
 * @struct ServerAPIService
 */
type ServerAPIService struct {
	/**
	 * ===== 核心配置和连接管理 =====
	 */

	/** 系统配置信息，包含数据库连接、服务端口等配置 */
	config *config.Config

	/** 全局上下文，用于控制所有异步操作的生命周期 */
	ctx context.Context

	/** 数据库连接管理器，负责连接健康检查和自动重连 */
	connMgr *ConnectionManager

	/**
	 * ===== 基础数据服务 =====
	 */

	/** Redis缓存服务，提供高速数据缓存和会话存储 */
	redisService *cache.RedisService

	/** InfluxDB时序数据库服务，处理设备数据的存储和查询 */
	influxService *cache.InfluxDBService

	/** services包的InfluxDB服务，用于兼容旧接口 */
	servicesInfluxService *services.InfluxDBService

	/** 数据管理服务，提供统一的数据访问接口 */
	dataService *services.DataService

	/** 系统监控服务，收集和报告系统运行状态 */
	monitoringService *services.MonitoringService

	/** MongoDB管理服务，处理业务数据的CRUD操作 */
	mongoAdminService *db.MongoDBService

	/** MongoDB适配器，用于兼容旧接口 */
	mongoAdapter *adapters.MongoAdminServiceAdapter

	/**
	 * ===== 用户管理和认证服务 =====
	 */

	/** 用户管理服务，处理用户账户的增删改查 */
	userService *services.UserService

	/** 认证服务，处理用户登录、JWT令牌生成和验证 */
	authService *services.AuthService

	/**
	 * ===== 生产管理服务 =====
	 */

	/** 订单管理服务，处理生产订单的全生命周期管理 */
	orderService *services.OrderService

	/** 生产任务服务，管理具体的生产任务执行 */
	productionTaskService *services.ProductionTaskService

	/** 每日计划服务，处理日常生产计划的制定和执行 */
	dailyPlanService *services.DailyPlanService

	/**
	 * ===== 统计分析和调度服务 =====
	 */

	/** 统计分析服务，提供设备利用率、产量等统计功能 */
	statisticsService *services.StatisticsService

	/** 任务调度服务，管理定时任务和自动化流程 */
	schedulerService *services.SchedulerService

	/** 工作时间管理服务，配置班次、休息时间等工作制度 */
	workTimeService *services.WorkTimeService
}

/**
 * 创建API服务器实例
 *
 * 功能：初始化API服务器的所有核心组件和基础服务
 *
 * 初始化流程：
 * 1. 创建基础数据服务（Redis、InfluxDB、监控）
 * 2. 创建临时数据管理服务（不包含MongoDB）
 * 3. 初始化连接管理器
 * 4. 设置全局上下文
 *
 * 设计考虑：
 * - 分阶段初始化：基础服务先启动，业务服务后续初始化
 * - 依赖管理：确保服务依赖关系正确建立
 * - 错误隔离：单个服务初始化失败不影响其他服务
 * - 资源管理：统一的资源生命周期管理
 *
 * 注意事项：
 * - MongoDB服务将在后续的initMongoAdminService中初始化
 * - DataService会在MongoDB初始化后重新创建以包含完整功能
 * - 业务服务（用户、订单等）依赖MongoDB，需要延迟初始化
 *
 * @param {*config.Config} cfg - 系统配置对象，包含所有服务的配置信息
 * @returns {*ServerAPIService} 初始化完成的API服务器实例
 *
 * @example
 * cfg, _ := config.LoadConfig("config.yml")
 * service := NewServerAPIService(cfg)
 * defer service.cleanup()
 */
func NewServerAPIService(cfg *config.Config) *ServerAPIService {
	// 初始化Redis管理器单例
	redisManager := cache.GetRedisManager()
	if err := redisManager.Initialize(&cfg.Redis); err != nil {
		logger.Errorf("❌ Failed to initialize Redis manager: %v", err)
	}

	// 初始化MongoDB管理器单例
	mongoManager := db.GetMongoDBManager()
	if err := mongoManager.Initialize(&cfg.MongoDB); err != nil {
		logger.Errorf("❌ Failed to initialize MongoDB manager: %v", err)
	}

	// 创建基础数据服务实例（新的单例服务）
	newRedisService := cache.NewRedisService()
	newMongoService := db.NewMongoDBService()

	// 初始化InfluxDB管理器单例
	influxManager := cache.GetInfluxDBManager()
	if err := influxManager.Initialize(&cfg.InfluxDB); err != nil {
		logger.Errorf("❌ Failed to initialize InfluxDB manager: %v", err)
	}

	// 创建基于单例管理器的InfluxDB服务
	influxService := cache.NewInfluxDBService()
	monitoringService := services.NewMonitoringService()

	// 创建services包中的服务实例（用于DataService）
	servicesRedisService := services.NewRedisService(&cfg.Redis)
	servicesInfluxService := services.NewInfluxDBService(&cfg.InfluxDB) // 为DataService创建services包的InfluxDB服务

	// 使用统一的MongoDB管理器创建MongoAdminService
	servicesMongoService, err := services.NewMongoAdminService(mongoManager)
	if err != nil {
		logger.Errorf("❌ Failed to create services MongoDB service with unified manager: %v", err)
		servicesMongoService = nil
	}

	// 创建适配器以兼容旧接口
	mongoAdapter := adapters.NewMongoAdminServiceAdapter(newMongoService)

	// 创建数据管理服务（使用services包中的服务）
	dataService := services.NewDataService(servicesRedisService, servicesInfluxService, servicesMongoService, monitoringService, &cfg.Cache)

	return &ServerAPIService{
		config:                cfg,                    // 保存配置引用
		ctx:                   context.Background(),   // 创建全局上下文
		connMgr:               NewConnectionManager(), // 初始化连接管理器
		redisService:          newRedisService,        // Redis缓存服务（新的单例服务）
		influxService:         influxService,          // InfluxDB时序数据服务（cache包）
		dataService:           dataService,            // 数据管理服务
		monitoringService:     monitoringService,      // 系统监控服务
		mongoAdminService:     newMongoService,        // MongoDB服务（新的单例服务）
		mongoAdapter:          mongoAdapter,           // MongoDB适配器
		servicesInfluxService: servicesInfluxService,  // services包的InfluxDB服务（用于兼容）
	}
}

/**
 * 主程序入口函数
 *
 * 功能：启动制造业数据采集系统的API服务器
 *
 * 启动流程：
 * 1. 配置加载和初始化
 * 2. 日志系统初始化
 * 3. 服务实例创建
 * 4. 数据库连接管理器启动
 * 5. 基础数据服务启动
 * 6. MongoDB和业务服务初始化
 * 7. HTTP路由设置和服务器启动
 * 8. 优雅关闭处理
 *
 * 容错设计：
 * - 配置文件加载失败时使用默认配置
 * - 单个服务初始化失败不影响整体启动
 * - 数据库连接失败时自动重连
 * - 支持优雅关闭，确保数据完整性
 *
 * 监控和日志：
 * - 详细的启动日志，便于问题诊断
 * - 分步骤的初始化过程，便于定位问题
 * - 系统状态实时监控
 *
 * 信号处理：
 * - SIGINT (Ctrl+C) 和 SIGTERM 信号的优雅处理
 * - 10秒超时的优雅关闭机制
 * - 完整的资源清理流程
 */
func main() {
	logger.Info("🚀 Starting Server API with database reconnection mechanism...")

	/**
	 * 第一步：配置加载和验证
	 *
	 * 加载顺序：
	 * 1. 尝试从config.yml加载配置
	 * 2. 加载失败时使用内置默认配置
	 * 3. 确保服务能够在各种环境下启动
	 */
	cfg, err := config.LoadConfig("config.yml")
	if err != nil {
		logger.Errorf("Failed to load config: %v", err)
		// 使用生产就绪的默认配置，确保服务可用性
		cfg = &config.Config{
			Service: config.ServiceConfig{
				Name:  "server-api",
				Port:  9005, // 标准API服务端口
				Debug: true, // 开发模式，生产环境应设为false
			},
			InfluxDB: config.InfluxDBConfig{
				URL:    "http://localhost:8086", // InfluxDB标准端口
				Token:  "your-token",            // 需要配置实际token
				Org:    "your-org",              // 需要配置实际组织
				Bucket: "your-bucket",           // 需要配置实际bucket
			},
			MongoDB: config.MongoDBConfig{
				URI:        "**********************************************************************",
				Database:   "mdc_admin",   // 管理数据库
				Collection: "device_data", // 设备数据集合
			},
			Logging: config.LoggingConfig{
				Level:  "info",   // 信息级别日志
				Format: "json",   // JSON格式便于解析
				Output: "stdout", // 标准输出，便于容器化
			},
		}
	}

	// 初始化日志系统，使用配置文件中的完整日志轮转设置
	// 构造logger包兼容的LoggingConfig结构体
	loggerConfig := logger.LoggingConfig{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
		Output: cfg.Logging.Output,
		Rotation: logger.LogRotationConfig{
			Enabled:    cfg.Logging.Rotation.Enabled,
			MaxSize:    cfg.Logging.Rotation.MaxSize,
			MaxAge:     cfg.Logging.Rotation.MaxAge,
			MaxBackups: cfg.Logging.Rotation.MaxBackups,
			Compress:   cfg.Logging.Rotation.Compress,
		},
	}
	logger.InitLoggerFromConfig(loggerConfig)
	logger.Infof("📋 Starting %s service on port %d", cfg.Service.Name, cfg.Service.Port)

	// 创建服务实例
	logger.Info("🔧 Creating service instance...")
	service := NewServerAPIService(cfg)
	logger.Info("✅ Service instance created successfully")

	// 启动数据库连接管理器
	logger.Info("🔧 Step 1: Starting connection manager...")
	service.startConnectionManager()

	// 启动新的数据服务
	logger.Info("🔧 Step 2: Starting data services...")
	service.startDataServices()

	// 初始化MongoDB管理服务
	logger.Info("🔧 Step 3: Initializing MongoDB Admin Service...")
	service.initMongoAdminService()

	// 启动HTTP服务器
	logger.Info("🔧 Step 4: Setting up routes...")
	router := service.setupRoutes()
	logger.Info("✅ Routes setup completed")

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{
		Addr:    addr,
		Handler: router,
	}

	// 启动服务器
	logger.Info("🔧 Step 5: Starting HTTP server...")
	logger.Infof("🌐 About to start HTTP server on %s", addr)

	// 使用异步方式启动服务器
	go func() {
		logger.Info("🚀 HTTP server starting now...")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Errorf("❌ Failed to start server: %v", err)
		}
	}()

	logger.Infof("✅ Server API service started successfully on %s", addr)
	logger.Info("📊 Database connection status will be managed automatically")

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("🛑 Shutting down server...")

	// 停止连接管理器
	close(service.connMgr.shutdown)
	service.connMgr.wg.Wait()

	// 优雅关闭HTTP服务器
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("❌ Server forced to shutdown: %v", err)
	}

	// 清理资源
	service.cleanup()
	logger.Info("✅ Server exited gracefully")
}

// initMongoAdminService 初始化MongoDB管理服务
func (s *ServerAPIService) initMongoAdminService() {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("🚨 PANIC in initMongoAdminService: %v", r)
		}
	}()

	logger.Info("🗄️ MongoDB Admin Service already initialized with singleton pattern...")

	// MongoDB服务已经在NewServerAPIService中初始化
	if s.mongoAdminService == nil || !s.mongoAdminService.IsAvailable() {
		logger.Errorf("❌ MongoDB service is not available")
		return
	}

	logger.Info("✅ MongoDB Admin Service is ready")

	// DataService已经在NewServerAPIService中跳过创建
	// 这里不需要重新创建，因为接口不兼容
	logger.Info("✅ DataService with MongoDB support started successfully")

	// 初始化用户管理服务
	logger.Info("👤 Starting user services initialization...")
	s.initUserServices()
}

// initUserServices 初始化用户管理和认证服务
func (s *ServerAPIService) initUserServices() {
	logger.Info("👤 Initializing User Management Services...")

	// 获取MongoDB管理器实例
	mongoManager := db.GetMongoDBManager()

	// 创建用户服务（使用统一的MongoDB管理器）
	s.userService = services.NewUserService(mongoManager)

	// 创建认证服务 (使用默认JWT密钥，生产环境应从配置文件读取)
	jwtSecret := "your-super-secret-jwt-key-change-in-production"
	s.authService = services.NewAuthService(s.userService, jwtSecret)

	// 初始化默认管理员账户
	if err := s.authService.InitializeDefaultAdmin(); err != nil {
		logger.Errorf("❌ Failed to initialize default admin: %v", err)
	}

	// 初始化订单和生产管理服务
	s.initProductionServices()

	logger.Info("✅ User Management Services initialized successfully")
}

// initProductionServices 初始化订单和生产管理服务
func (s *ServerAPIService) initProductionServices() {
	logger.Info("🏭 Initializing Production Management Services...")

	// 获取MongoDB管理器实例
	mongoManager := db.GetMongoDBManager()

	// 创建订单服务（使用统一的MongoDB管理器）
	s.orderService = services.NewOrderService(mongoManager)

	// 创建生产任务服务（使用统一的MongoDB管理器）
	s.productionTaskService = services.NewProductionTaskService(mongoManager)

	// 创建每日计划服务（使用统一的MongoDB管理器）
	s.dailyPlanService = services.NewDailyPlanService(mongoManager)

	// 初始化统计和工作时间管理服务
	s.initStatisticsServices()

	logger.Info("✅ Production Management Services initialized successfully")
}

// initStatisticsServices 初始化统计和工作时间管理服务
func (s *ServerAPIService) initStatisticsServices() {
	logger.Info("📊 Initializing Statistics and WorkTime Services...")

	// 创建标准日志记录器
	stdLogger := log.New(os.Stdout, "[STATISTICS] ", log.LstdFlags)

	// 创建工作时间服务
	s.workTimeService = services.NewWorkTimeService(s.mongoAdminService.GetClient(), stdLogger)

	// 初始化默认工作时间设置
	if err := s.workTimeService.InitializeDefaultSettings(); err != nil {
		logger.Errorf("❌ Failed to initialize default work time settings: %v", err)
	}

	// 创建统计服务
	s.statisticsService = services.NewStatisticsService(
		s.mongoAdminService.GetClient(),
		s.servicesInfluxService,
		s.workTimeService, // 工作时间服务
		s.dataService,     // 数据服务
		stdLogger,
	)

	// 创建调度服务
	logger.Info("🔧 Creating scheduler service...")
	s.schedulerService = services.NewSchedulerService(s.statisticsService, stdLogger)
	logger.Info("✅ Scheduler service created successfully")

	// 启动调度器
	logger.Info("🔧 Starting scheduler service...")
	if err := s.schedulerService.Start(); err != nil {
		logger.Errorf("❌ Failed to start scheduler service: %v", err)
	} else {
		logger.Info("✅ Scheduler service started successfully")
	}

	logger.Info("✅ Statistics and WorkTime Services initialized successfully")
}

// startConnectionManager 启动连接管理器
func (s *ServerAPIService) startConnectionManager() {
	logger.Info("🔄 Starting database connection manager...")

	// InfluxDB连接管理已移至DataService，这里不再需要单独管理
	logger.Info("✅ Database connection manager started (InfluxDB managed by DataService)")
}

// manageInfluxDB 管理InfluxDB连接 (已禁用，使用DataService管理)
func (s *ServerAPIService) manageInfluxDB() {
	defer s.connMgr.wg.Done()

	logger.Info("📊 InfluxDB connection management disabled (managed by DataService)")

	// 不再需要单独的InfluxDB连接管理，DataService会处理连接
	return
}

// connectInfluxDB 连接InfluxDB (已禁用，使用DataService管理)
func (s *ServerAPIService) connectInfluxDB() {
	logger.Info("📊 InfluxDB connection disabled (managed by DataService)")

	// 不再需要单独的InfluxDB连接，DataService会处理
	atomic.StoreInt32(&s.connMgr.influxConnected, 1) // 标记为已连接，避免重连逻辑
	return
}

// reconnectInfluxDB 重连InfluxDB (已禁用，使用DataService管理)
func (s *ServerAPIService) reconnectInfluxDB() {
	logger.Info("📊 InfluxDB reconnection disabled (managed by DataService)")

	// 不再需要单独的InfluxDB重连，DataService会处理
	return
}

// calculateBackoffDelay 计算指数退避延迟
func (s *ServerAPIService) calculateBackoffDelay(retries int32) time.Duration {
	if retries <= 0 {
		return s.connMgr.baseDelay
	}

	delay := time.Duration(float64(s.connMgr.baseDelay) * math.Pow(2, float64(retries-1)))

	if delay > s.connMgr.maxDelay {
		delay = s.connMgr.maxDelay
	}

	return delay
}

// startDataServices 启动数据服务
func (s *ServerAPIService) startDataServices() {
	logger.Info("🔄 Starting data services...")

	// 启动监控服务
	if err := s.monitoringService.Start(); err != nil {
		logger.Errorf("❌ Failed to start monitoring service: %v", err)
	}

	// Redis连接已在单例管理器中初始化
	if !s.redisService.IsAvailable() {
		logger.Errorf("❌ Redis service is not available")
	} else {
		logger.Info("✅ Redis service is ready")
	}

	// 连接InfluxDB服务（cache包）
	if err := s.influxService.Connect(); err != nil {
		logger.Errorf("❌ Failed to connect to cache InfluxDB service: %v", err)
	}

	// 连接InfluxDB服务（services包）
	if err := s.servicesInfluxService.Connect(); err != nil {
		logger.Errorf("❌ Failed to connect to services InfluxDB service: %v", err)
	}

	// 启动数据管理服务
	if s.dataService != nil {
		if err := s.dataService.Start(); err != nil {
			logger.Errorf("❌ Failed to start data service: %v", err)
		}
	} else {
		logger.Info("⚠️ DataService is disabled due to interface compatibility issues")
	}

	logger.Info("✅ Data services started successfully")
}

// cleanup 清理资源
func (s *ServerAPIService) cleanup() {
	// 停止监控服务
	if s.monitoringService != nil {
		s.monitoringService.Stop()
	}

	// 停止数据服务
	if s.dataService != nil {
		s.dataService.Stop()
	}

	// 关闭Redis连接（通过单例管理器）
	redisManager := cache.GetRedisManager()
	if redisManager != nil {
		redisManager.Close()
	}

	// 断开InfluxDB服务连接
	if s.influxService != nil {
		s.influxService.Disconnect()
	}

	// 断开services包的InfluxDB服务连接
	if s.servicesInfluxService != nil {
		s.servicesInfluxService.Disconnect()
	}

	// 关闭InfluxDB连接（通过单例管理器）
	influxManager := cache.GetInfluxDBManager()
	if influxManager != nil {
		influxManager.Close()
	}

	// 关闭MongoDB连接（通过单例管理器）
	mongoManager := db.GetMongoDBManager()
	if mongoManager != nil {
		mongoManager.Close()
	}
}

// setupRoutes 设置路由
func (s *ServerAPIService) setupRoutes() *gin.Engine {
	logger.Info("🔧 setupRoutes: Starting route setup...")

	if !s.config.Service.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	logger.Info("🔧 setupRoutes: Creating Gin router...")
	router := gin.Default()
	logger.Info("🔧 setupRoutes: Gin router created successfully")

	// 使用新的模块化路由设置
	logger.Info("🔧 setupRoutes: Setting up modular routes...")
	routes.SetupRoutes(
		router,
		s.dataService,
		s.servicesInfluxService,
		s.monitoringService,
		s.workTimeService,
		s.authService,
		s.userService,
		s.mongoAdapter,
		s.statisticsService,
		s.schedulerService,
	)
	logger.Info("✅ setupRoutes: Modular routes setup completed")

	// 保留一些特殊的调试和测试路由
	// 这些路由不在模块化路由中，需要单独处理

	// 添加简单的测试API
	router.GET("/api/test/simple", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "测试API工作正常", "timestamp": time.Now().Format("2006-01-02 15:04:05")})
	})

	// 缓存调试接口
	router.POST("/api/debug/cache/refresh", func(c *gin.Context) {
		logger.Info("🔧 Manual cache refresh requested")

		if s.dataService == nil {
			c.JSON(500, gin.H{
				"error": "DataService not available",
			})
			return
		}

		// 手动触发缓存更新
		err := s.dataService.UpdateTodayCache()
		if err != nil {
			logger.Errorf("❌ Manual cache refresh failed: %v", err)
			c.JSON(500, gin.H{
				"error":   "Cache refresh failed",
				"details": err.Error(),
			})
			return
		}

		logger.Info("✅ Manual cache refresh completed")
		c.JSON(200, gin.H{
			"message":   "Cache refreshed successfully",
			"timestamp": time.Now(),
		})
	})

	// 临时测试路由 - 验证device-data-types路由
	router.GET("/api/admin/device-data-types-test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "device-data-types测试路由工作正常", "timestamp": time.Now().Format("2006-01-02 15:04:05")})
	})

	// 调试接口 - 手动重构历史数据（无需认证）
	router.POST("/api/debug/rebuild-history", func(c *gin.Context) {
		var request struct {
			Date string `json:"date" binding:"required"`
		}

		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
			return
		}

		// 验证日期格式
		if _, err := time.Parse("2006-01-02", request.Date); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date format, expected YYYY-MM-DD"})
			return
		}

		if s.dataService == nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "DataService not available"})
			return
		}

		logger.Infof("🔧 Debug: Manual history rebuild requested for date: %s", request.Date)

		// 调用重构历史数据方法
		successCount, errorCount, err := s.dataService.RebuildHistoryData(request.Date)
		if err != nil {
			logger.Errorf("❌ Debug: History rebuild failed: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "History rebuild failed",
				"details": err.Error(),
			})
			return
		}

		logger.Infof("✅ Debug: History rebuild completed - Success: %d, Errors: %d", successCount, errorCount)
		c.JSON(http.StatusOK, gin.H{
			"message":       "History data rebuild completed",
			"date":          request.Date,
			"success_count": successCount,
			"error_count":   errorCount,
			"timestamp":     time.Now(),
		})
	})

	return router
}
