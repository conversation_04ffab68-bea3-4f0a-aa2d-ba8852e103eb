/**
 * 健康检查和基础路由
 *
 * 功能描述：
 * 提供系统健康检查、根路径响应等基础路由功能
 * 这些路由不需要认证，用于系统监控和基本信息展示
 *
 * 路由列表：
 * - GET /health - 健康检查接口
 * - GET / - 根路径欢迎信息
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"server-api/handlers/health"

	"github.com/gin-gonic/gin"
)

/**
 * 设置健康检查和基础路由
 *
 * 功能：注册系统基础路由，包括健康检查和欢迎页面
 *
 * 路由详情：
 * 1. GET /health - 返回服务健康状态
 * 2. GET / - 返回系统欢迎信息和版本信息
 *
 * 特点：
 * - 无需认证，公开访问
 * - 轻量级响应，适合监控系统调用
 * - 提供服务标识和状态信息
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 *
 * @example
 * router := gin.Default()
 * SetupHealthRoutes(router)
 */
func SetupHealthRoutes(router *gin.Engine) {
	// 创建健康检查处理器
	healthHandler := &health.HealthHandler{}

	// 健康检查接口
	// 用于负载均衡器、监控系统等检查服务状态
	router.GET("/health", healthHandler.GetHealth)
	router.GET("/api/health", healthHandler.GetHealth)

	// 根路径欢迎信息
	// 提供服务基本信息和状态确认
	router.GET("/", healthHandler.GetWelcome)
	router.GET("/api", healthHandler.GetWelcome)
}
