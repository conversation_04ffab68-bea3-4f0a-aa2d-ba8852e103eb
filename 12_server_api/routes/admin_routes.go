/**
 * 管理员路由配置文件
 *
 * 功能描述：
 * 配置管理员相关的API路由，包括设备管理、数据管理、产品管理、工序管理等功能
 * 提供完整的系统管理和配置路由支持
 *
 * 路由组织：
 * - /api/admin/* - 管理员相关API
 * - /api/admin/devices/* - 设备管理API
 * - /api/admin/products/* - 产品管理API
 * - /api/admin/processes/* - 工序管理API
 * - /api/admin/data/* - 数据管理API
 *
 * 功能模块：
 * - 设备管理：设备的增删改查、配置管理、状态控制
 * - 产品管理：产品信息维护、规格管理、分类管理
 * - 工序管理：工序定义、流程管理、参数配置
 * - 数据管理：系统数据初始化、清理、重置、统计
 *
 * 权限控制：
 * - 需要管理员权限才能访问
 * - 敏感操作需要额外验证
 * - 操作日志记录和审计
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"github.com/gin-gonic/gin"

	"server-api/adapters"
	"server-api/handlers/admin"
	"server-api/services"
	"shared/logger"
)

/**
 * 设置管理员API路由
 *
 * 功能：配置管理员相关的所有API端点和中间件
 *
 * 路由结构：
 * 设备管理：
 * - POST /api/admin/devices - 创建设备
 * - GET /api/admin/devices - 获取设备列表
 * - GET /api/admin/devices/configs - 获取设备配置
 * - GET /api/admin/devices/{id} - 获取单个设备
 * - PUT /api/admin/devices/{id} - 更新设备
 * - DELETE /api/admin/devices/{id} - 删除设备
 *
 * 产品管理：
 * - POST /api/admin/products - 创建产品
 * - GET /api/admin/products - 获取产品列表
 * - GET /api/admin/products/{id} - 获取单个产品
 * - PUT /api/admin/products/{id} - 更新产品
 * - DELETE /api/admin/products/{id} - 删除产品
 *
 * 工序管理：
 * - POST /api/admin/processes - 创建工序
 * - GET /api/admin/processes - 获取工序列表
 * - GET /api/admin/processes/{id} - 获取单个工序
 * - PUT /api/admin/processes/{id} - 更新工序
 * - DELETE /api/admin/processes/{id} - 删除工序
 *
 * 数据管理：
 * - POST /api/admin/initialize-data - 初始化系统数据
 * - DELETE /api/admin/clear-data - 清理系统数据
 * - POST /api/admin/reset-data - 重置系统数据
 * - POST /api/admin/rebuild-history - 重构历史数据
 * - GET /api/admin/statistics - 获取系统统计信息
 * - GET /api/admin/device-data-types - 获取设备数据类型选项
 *
 * 服务依赖：
 * - MongoAdapter：提供数据持久化
 * - MonitoringService：提供API性能监控
 * - DataService：提供历史数据重构功能
 *
 * 容错机制：
 * - 服务不可用时记录警告日志
 * - 不阻止其他路由正常工作
 * - 提供降级处理方案
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*adapters.MongoAdminServiceAdapter} mongoAdapter - MongoDB适配器实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.DataService} dataService - 数据服务实例，用于历史数据重构功能
 *
 * @example
 * SetupAdminRoutes(router, mongoAdapter, monitoringService, dataService)
 */
func SetupAdminRoutes(
	router *gin.Engine,
	mongoAdapter *adapters.MongoAdminServiceAdapter,
	monitoringService *services.MonitoringService,
	dataService *services.DataService,
) {
	logger.Info("🔧 Setting up admin routes...")

	// 检查必要的服务依赖
	if mongoAdapter == nil {
		logger.Warn("⚠️ MongoAdapter is nil, admin routes may not work properly")
	}
	if monitoringService == nil {
		logger.Warn("⚠️ MonitoringService is nil, API metrics will not be recorded")
	}

	// 数据服务已通过参数传入，用于历史数据重构功能
	// dataService参数提供完整的数据管理功能

	// 创建管理员处理器
	devicesHandler := admin.NewDevicesHandler(mongoAdapter, monitoringService)
	productsHandler := admin.NewProductsHandler(mongoAdapter, monitoringService)
	processesHandler := admin.NewProcessesHandler(mongoAdapter, monitoringService)
	dataHandler := admin.NewDataHandler(mongoAdapter, monitoringService, dataService)

	// 创建管理员路由组
	adminGroup := router.Group("/api/admin")
	{
		// 设备管理路由
		devicesGroup := adminGroup.Group("/devices")
		{
			// 基础CRUD操作
			devicesGroup.POST("", devicesHandler.CreateDevice)
			devicesGroup.GET("", devicesHandler.GetDevices)
			devicesGroup.GET("/:id", devicesHandler.GetDevice)
			devicesGroup.PUT("/:id", devicesHandler.UpdateDevice)
			devicesGroup.DELETE("/:id", devicesHandler.DeleteDevice)

			// 设备配置管理
			devicesGroup.GET("/configs", devicesHandler.GetDeviceConfigs)
		}

		// 产品管理路由
		productsGroup := adminGroup.Group("/products")
		{
			// 基础CRUD操作
			productsGroup.POST("", productsHandler.CreateProduct)
			productsGroup.GET("", productsHandler.GetProducts)
			productsGroup.GET("/:id", productsHandler.GetProduct)
			productsGroup.PUT("/:id", productsHandler.UpdateProduct)
			productsGroup.DELETE("/:id", productsHandler.DeleteProduct)
		}

		// 工序管理路由
		processesGroup := adminGroup.Group("/processes")
		{
			// 基础CRUD操作
			processesGroup.POST("", processesHandler.CreateProcess)
			processesGroup.GET("", processesHandler.GetProcesses)
			processesGroup.GET("/:id", processesHandler.GetProcess)
			processesGroup.PUT("/:id", processesHandler.UpdateProcess)
			processesGroup.DELETE("/:id", processesHandler.DeleteProcess)
		}

		// 数据管理路由
		adminGroup.POST("/initialize-data", dataHandler.InitializeData)
		adminGroup.DELETE("/clear-data", dataHandler.ClearData)
		adminGroup.POST("/reset-data", dataHandler.ResetData)
		adminGroup.POST("/rebuild-history", dataHandler.RebuildHistory)
		adminGroup.GET("/statistics", dataHandler.GetStatistics)
		adminGroup.GET("/device-data-types", dataHandler.GetDataTypeOptions)
	}

	logger.Info("✅ Admin routes setup completed")
}
