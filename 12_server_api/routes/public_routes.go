/**
 * 公开API路由
 *
 * 功能描述：
 * 提供无需认证的公开API接口，主要服务于设备端和外部系统集成
 * 这些API通常用于设备配置获取、状态上报等基础功能
 *
 * 路由列表：
 * - GET /api/public/devices/configs - 获取公开设备配置
 *
 * 访问权限：完全公开，无需任何认证
 *
 * 安全考虑：
 * - 只暴露必要的配置信息
 * - 敏感信息已过滤或脱敏
 * - 适合设备端和第三方系统调用
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"shared/logger"

	"github.com/gin-gonic/gin"

	"server-api/adapters"
	"server-api/handlers/admin"
	"server-api/services"
)

/**
 * 设置公开API路由
 *
 * 功能：注册所有无需认证的公开API路由
 *
 * 依赖服务：
 * - mongoAdapter: MongoDB适配器，提供数据库操作接口
 * - monitoringService: 监控服务，提供系统状态监控
 *
 * 路由分组：
 * - /api/public/* - 公开API，无需认证
 *
 * 使用场景：
 * 1. 设备端配置获取 - fanuc_v2等设备采集程序
 * 2. 第三方系统集成 - 外部监控系统、报表系统
 * 3. 移动端应用 - 简化认证流程的移动应用
 *
 * 数据安全：
 * - 只返回公开可见的配置信息
 * - 过滤敏感字段（密码、密钥等）
 * - 限制数据访问范围
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*adapters.MongoAdminServiceAdapter} mongoAdapter - MongoDB适配器实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 *
 * @example
 * SetupPublicRoutes(router, mongoAdapter, monitoringService)
 */
func SetupPublicRoutes(
	router *gin.Engine,
	mongoAdapter *adapters.MongoAdminServiceAdapter,
	monitoringService *services.MonitoringService,
) {
	logger.Info("🔧 Setting up public routes...")

	// 检查必要的服务是否可用
	if mongoAdapter == nil {
		logger.Warn("⚠️ MongoDB adapter not available, skipping public routes")
		return
	}

	// 创建设备管理处理器实例
	devicesHandler := admin.NewDevicesHandler(mongoAdapter, monitoringService)

	// 创建公开API路由组
	publicAPI := router.Group("/api/public")
	{
		// 设备配置API（专为fanuc_v2等设备采集程序设计）
		// 返回设备基本配置信息，不包含敏感数据
		publicAPI.GET("/devices/configs", devicesHandler.GetPublicDeviceConfigs)
	}

	logger.Info("✅ Public routes setup completed")
}
