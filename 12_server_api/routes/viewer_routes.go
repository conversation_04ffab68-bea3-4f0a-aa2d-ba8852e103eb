/**
 * 20_viewer API路由
 *
 * 功能描述：
 * 提供数据查看器相关的API接口，主要服务于20_viewer前端应用
 * 包括设备数据查询、字段信息获取、仪表板统计等功能
 *
 * 路由列表：
 * - GET /api/viewer/devices - 获取设备列表
 * - GET /api/viewer/devices/:id/data - 获取设备数据
 * - GET /api/viewer/devices/:id/records - 获取设备记录
 * - GET /api/viewer/fields/data - 获取字段数据
 * - GET /api/viewer/fields/available - 获取可用字段
 * - GET /api/viewer/data-types - 获取数据类型
 * - GET /api/viewer/dashboard/stats - 获取仪表板统计
 * - GET /api/viewer/connection/test - 测试连接状态
 *
 * 访问权限：公开访问，无需认证
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"shared/logger"

	"github.com/gin-gonic/gin"

	"server-api/handlers/viewer"
	"server-api/services"
)

/**
 * 设置20_viewer API路由
 *
 * 功能：注册所有与数据查看器相关的API路由
 *
 * 依赖服务：
 * - dataService: 数据管理服务，提供设备数据查询
 * - servicesInfluxService: InfluxDB服务，提供时序数据查询
 * - monitoringService: 监控服务，提供系统状态监控
 *
 * 路由分组：
 * - /api/viewer/* - 数据查看器专用API
 *
 * 服务检查：
 * - 检查必要服务的可用性
 * - 服务不可用时跳过路由注册
 * - 记录相应的日志信息
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*services.DataService} dataService - 数据管理服务实例
 * @param {*services.InfluxDBService} servicesInfluxService - InfluxDB服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 *
 * @example
 * SetupViewerRoutes(router, dataService, servicesInfluxService, monitoringService)
 */
func SetupViewerRoutes(
	router *gin.Engine,
	dataService *services.DataService,
	servicesInfluxService *services.InfluxDBService,
	monitoringService *services.MonitoringService,
) {
	logger.Info("🔧 Setting up viewer routes...")

	// 检查必要的服务是否可用
	if dataService == nil || servicesInfluxService == nil {
		logger.Warn("⚠️ Required services not available, skipping viewer routes")
		return
	}

	// 创建查看器处理器实例
	devicesHandler := viewer.NewDevicesHandler(dataService, servicesInfluxService, monitoringService)
	fieldsHandler := viewer.NewFieldsHandler(dataService, servicesInfluxService, monitoringService)
	dashboardHandler := viewer.NewDashboardHandler(dataService, servicesInfluxService, monitoringService)

	// 创建查看器API路由组
	viewerAPI := router.Group("/api/viewer")
	{
		// 设备相关API
		viewerAPI.GET("/devices", devicesHandler.GetDevices)
		viewerAPI.GET("/devices/:id/data", devicesHandler.GetDeviceData)
		viewerAPI.GET("/devices/:id/records", devicesHandler.GetDeviceRecords)

		// 字段和数据类型API
		viewerAPI.GET("/fields/data", fieldsHandler.GetFieldData)
		viewerAPI.GET("/fields/available", fieldsHandler.GetAvailableFields)
		viewerAPI.GET("/data-types", fieldsHandler.GetDataTypes)

		// 仪表板和连接API
		viewerAPI.GET("/dashboard/stats", dashboardHandler.GetDashboardStats)
		viewerAPI.GET("/connection/test", dashboardHandler.TestConnection)
	}

	logger.Info("✅ Viewer routes setup completed")
}
