/**
 * 生产管理路由配置文件
 *
 * 功能描述：
 * 配置生产管理相关的API路由，包括每日计划、订单管理、生产任务等功能
 * 提供完整的生产管理生命周期路由支持
 *
 * 路由组织：
 * - /api/production/* - 生产管理相关API
 * - /api/production/daily-plans/* - 每日计划管理API
 * - /api/production/orders/* - 订单管理API
 * - /api/production/tasks/* - 生产任务管理API
 *
 * 功能模块：
 * - 每日计划：计划创建、查询、更新、删除、进度跟踪
 * - 订单管理：订单创建、状态管理、统计分析
 * - 生产任务：任务创建、执行控制、状态跟踪
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"github.com/gin-gonic/gin"

	"server-api/adapters"
	"server-api/handlers/production"
	"server-api/services"
	"shared/logger"
)

/**
 * 设置生产管理API路由
 *
 * 功能：配置生产管理相关的所有API端点和中间件
 *
 * 路由结构：
 * 每日计划管理：
 * - GET /api/production/daily-plans - 获取每日计划列表
 * - POST /api/production/daily-plans - 创建每日计划
 * - GET /api/production/daily-plans/{id} - 获取单个计划
 * - PUT /api/production/daily-plans/{id} - 更新计划
 * - DELETE /api/production/daily-plans/{id} - 删除计划
 * - GET /api/production/daily-plans/by-date - 按日期查询计划
 * - GET /api/production/daily-plans/by-device/{device_id} - 按设备查询计划
 * - GET /api/production/daily-plans/weekly - 获取周计划
 * - GET /api/production/daily-plans/statistics - 获取计划统计
 * - PUT /api/production/daily-plans/{id}/progress - 更新计划进度
 *
 * 订单管理：
 * - GET /api/production/orders - 获取订单列表
 * - POST /api/production/orders - 创建订单
 * - GET /api/production/orders/{id} - 获取单个订单
 * - PUT /api/production/orders/{id} - 更新订单
 * - DELETE /api/production/orders/{id} - 删除订单
 * - PUT /api/production/orders/{id}/status - 更新订单状态
 * - GET /api/production/orders/statistics - 获取订单统计
 * - GET /api/production/orders/by-status/{status} - 按状态查询订单
 *
 * 生产任务管理：
 * - GET /api/production/tasks - 获取生产任务列表
 * - POST /api/production/tasks - 创建生产任务
 * - GET /api/production/tasks/{id} - 获取单个任务
 * - PUT /api/production/tasks/{id} - 更新任务
 * - DELETE /api/production/tasks/{id} - 删除任务
 * - POST /api/production/tasks/{id}/start - 开始任务
 * - POST /api/production/tasks/{id}/complete - 完成任务
 *
 * 服务依赖：
 * - MongoAdapter：提供数据持久化
 * - MonitoringService：提供API性能监控
 *
 * 容错机制：
 * - 服务不可用时记录警告日志
 * - 不阻止其他路由正常工作
 * - 提供降级处理方案
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*adapters.MongoAdminServiceAdapter} mongoAdapter - MongoDB适配器实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 *
 * @example
 * SetupProductionRoutes(router, mongoAdapter, monitoringService)
 */
func SetupProductionRoutes(
	router *gin.Engine,
	mongoAdapter *adapters.MongoAdminServiceAdapter,
	monitoringService *services.MonitoringService,
) {
	logger.Info("🔧 Setting up production routes...")

	// 检查必要的服务依赖
	if mongoAdapter == nil {
		logger.Warn("⚠️ MongoAdapter is nil, production routes may not work properly")
	}
	if monitoringService == nil {
		logger.Warn("⚠️ MonitoringService is nil, API metrics will not be recorded")
	}

	// 创建生产管理服务（简化版本，实际项目中需要完整实现）
	var dailyPlanService *services.DailyPlanService
	var orderService *services.OrderService
	var productionTaskService *services.ProductionTaskService

	// 如果MongoDB适配器可用，创建服务实例
	if mongoAdapter != nil {
		// 这里应该创建真实的服务实例
		// dailyPlanService = services.NewDailyPlanService(mongoAdapter)
		// orderService = services.NewOrderService(mongoAdapter)
		// productionTaskService = services.NewProductionTaskService(mongoAdapter)
		logger.Debug("🏭 Production services initialized")
	} else {
		logger.Warn("⚠️ Production services not available, using fallback")
	}

	// 创建生产管理处理器
	dailyPlanHandler := production.NewDailyPlanHandler(dailyPlanService)
	orderHandler := production.NewOrderHandler(orderService)
	taskHandler := production.NewProductionTaskHandler(productionTaskService)

	// 创建生产管理路由组
	productionGroup := router.Group("/api/production")
	{
		// 每日计划管理路由
		dailyPlansGroup := productionGroup.Group("/daily-plans")
		{
			// 基础CRUD操作
			dailyPlansGroup.GET("", dailyPlanHandler.GetDailyPlans)
			dailyPlansGroup.POST("", dailyPlanHandler.CreateDailyPlan)
			dailyPlansGroup.GET("/:id", dailyPlanHandler.GetDailyPlan)
			dailyPlansGroup.PUT("/:id", dailyPlanHandler.UpdateDailyPlan)
			dailyPlansGroup.DELETE("/:id", dailyPlanHandler.DeleteDailyPlan)

			// 查询操作
			dailyPlansGroup.GET("/by-date", dailyPlanHandler.GetPlansByDate)
			dailyPlansGroup.GET("/by-device/:device_id", dailyPlanHandler.GetPlansByDevice)
			dailyPlansGroup.GET("/weekly", dailyPlanHandler.GetWeeklyPlans)
			dailyPlansGroup.GET("/statistics", dailyPlanHandler.GetPlanStatistics)

			// 进度管理
			dailyPlansGroup.PUT("/:id/progress", dailyPlanHandler.UpdatePlanProgress)
		}

		// 订单管理路由
		ordersGroup := productionGroup.Group("/orders")
		{
			// 基础CRUD操作
			ordersGroup.GET("", orderHandler.GetOrders)
			ordersGroup.POST("", orderHandler.CreateOrder)
			ordersGroup.GET("/:id", orderHandler.GetOrder)
			ordersGroup.PUT("/:id", orderHandler.UpdateOrder)
			ordersGroup.DELETE("/:id", orderHandler.DeleteOrder)

			// 状态管理
			ordersGroup.PUT("/:id/status", orderHandler.UpdateOrderStatus)

			// 查询和统计
			ordersGroup.GET("/statistics", orderHandler.GetOrderStatistics)
			ordersGroup.GET("/by-status/:status", orderHandler.GetOrdersByStatus)
		}

		// 生产任务管理路由
		tasksGroup := productionGroup.Group("/tasks")
		{
			// 基础CRUD操作
			tasksGroup.GET("", taskHandler.GetProductionTasks)
			tasksGroup.POST("", taskHandler.CreateProductionTask)
			tasksGroup.GET("/:id", taskHandler.GetProductionTask)
			tasksGroup.PUT("/:id", taskHandler.UpdateProductionTask)
			tasksGroup.DELETE("/:id", taskHandler.DeleteProductionTask)

			// 任务执行控制
			tasksGroup.POST("/:id/start", taskHandler.StartTask)
			tasksGroup.POST("/:id/complete", taskHandler.CompleteTask)
		}
	}

	logger.Info("✅ Production routes setup completed")
}
