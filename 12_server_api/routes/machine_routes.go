/**
 * 机器监控API路由
 *
 * 功能描述：
 * 提供设备状态监控、生产历史查询、生产进度跟踪等核心业务API
 * 这些API主要服务于前端监控界面，提供实时设备数据和历史分析
 *
 * 路由列表：
 * - GET /api/machines - 获取所有设备列表
 * - GET /api/machines/:id - 获取指定设备详情
 * - GET /api/production-history - 获取生产历史数据
 * - GET /api/production/progress - 获取生产进度信息
 * - GET /api/devices/:id/status-history - 获取设备状态历史
 * - POST /api/admin/process-history - 手动触发历史数据处理
 *
 * 访问权限：公开访问，无需认证
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"shared/logger"

	"github.com/gin-gonic/gin"

	"server-api/handlers/machine"
	"server-api/services"
)

/**
 * 设置机器监控API路由
 *
 * 功能：注册所有与设备监控相关的API路由
 *
 * 依赖服务：
 * - dataService: 数据管理服务，提供设备数据查询
 * - monitoringService: 监控服务，提供系统状态监控
 * - workTimeService: 工作时间服务，提供班次和工作时间配置
 *
 * 路由分组：
 * - /api/* - 核心业务API，公开访问
 * - /api/admin/* - 管理功能API，用于数据维护
 *
 * 错误处理：
 * - 服务不可用时跳过路由注册
 * - 记录警告日志便于问题诊断
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*services.DataService} dataService - 数据管理服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 *
 * @example
 * SetupMachineRoutes(router, dataService, monitoringService, workTimeService)
 */
func SetupMachineRoutes(
	router *gin.Engine,
	dataService *services.DataService,
	monitoringService *services.MonitoringService,
	workTimeService *services.WorkTimeService,
) {
	logger.Info("🔧 Setting up machine routes...")

	// 检查必要的服务是否可用
	if dataService == nil {
		logger.Warn("⚠️ DataService not available, skipping machine routes")
		return
	}

	// 创建机器处理器实例
	machinesHandler := machine.NewMachinesHandler(dataService, monitoringService, workTimeService)
	productionHandler := machine.NewProductionHandler(dataService, monitoringService, workTimeService)
	statusHistoryHandler := machine.NewStatusHistoryHandler(dataService, monitoringService, workTimeService)

	// 创建API路由组
	api := router.Group("/api")
	{
		// 设备列表和详情API
		api.GET("/machines", machinesHandler.GetMachines)
		api.GET("/machines/:id", machinesHandler.GetMachine)

		// 新增：从MongoDB获取单台设备记录的API
		api.GET("/machine/:id", machinesHandler.GetMachineFromMongoDB)

		// 生产相关API
		api.GET("/production-history", productionHandler.GetProductionHistory)
		api.GET("/production/progress", productionHandler.GetProductionProgress)

		// 设备状态历史API (V2版本，支持时间段视图)
		api.GET("/devices/:id/status-history", statusHistoryHandler.GetDeviceStatusHistoryV2)

		// 管理功能API - 手动触发历史数据处理
		// 用于测试和维护，可以重新处理指定日期的历史数据
		api.POST("/admin/process-history", statusHistoryHandler.ProcessHistory)
	}

	logger.Info("✅ Machine routes setup completed")
}
