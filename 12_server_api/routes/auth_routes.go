/**
 * 认证API路由
 *
 * 功能描述：
 * 提供用户认证相关的API接口，包括登录、令牌刷新、用户资料管理等
 * 支持JWT令牌认证机制，提供安全的用户身份验证
 *
 * 路由列表：
 * - POST /api/auth/login - 用户登录
 * - POST /api/auth/refresh - 刷新访问令牌
 * - GET /api/user/profile - 获取用户资料（需认证）
 * - POST /api/user/change-password - 修改密码（需认证）
 *
 * 访问权限：
 * - 认证路由：公开访问
 * - 用户路由：需要有效JWT令牌
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"server-api/handlers/auth"
	"server-api/middleware"
	"server-api/services"
	"shared/logger"

	"github.com/gin-gonic/gin"
)

/**
 * 设置认证API路由
 *
 * 功能：注册所有与用户认证相关的API路由
 *
 * 依赖服务：
 * - authService: 认证服务，提供JWT令牌生成和验证
 * - userService: 用户服务，提供用户数据管理
 *
 * 路由分组：
 * 1. /api/auth/* - 认证相关API，无需认证
 * 2. /api/user/* - 用户管理API，需要认证
 *
 * 中间件：
 * - AuthMiddleware: JWT令牌验证中间件
 *
 * 安全特性：
 * - JWT令牌过期机制
 * - 密码加密存储
 * - 令牌刷新机制
 * - 用户权限验证
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*services.AuthService} authService - 认证服务实例
 * @param {*services.UserService} userService - 用户服务实例
 *
 * @example
 * SetupAuthRoutes(router, authService, userService)
 */
func SetupAuthRoutes(
	router *gin.Engine,
	authService *services.AuthService,
	userService *services.UserService,
) {
	logger.Info("🔧 Setting up auth routes...")

	// 检查必要的服务是否可用
	if authService == nil || userService == nil {
		logger.Warn("⚠️ Auth or User service not available, skipping auth routes")
		return
	}

	// 创建认证和用户处理器实例
	authHandler := auth.NewAuthHandler(userService, authService)
	userHandler := auth.NewUserHandler(userService, authService)

	// 认证API路由组（无需认证）
	authGroup := router.Group("/api/auth")
	{
		// 用户登录 - 验证用户名密码，返回JWT令牌
		authGroup.POST("/login", authHandler.Login)

		// 刷新令牌 - 使用刷新令牌获取新的访问令牌
		authGroup.POST("/refresh", authHandler.RefreshToken)
	}

	// 用户管理API路由组（需要认证）
	userGroup := router.Group("/api/user")
	userGroup.Use(middleware.AuthMiddleware(authService))
	{
		// 获取用户资料 - 返回当前登录用户的详细信息
		userGroup.GET("/profile", authHandler.GetProfile)

		// 修改密码 - 允许用户修改自己的登录密码
		userGroup.POST("/change-password", authHandler.ChangePassword)
	}

	// 管理员用户管理API路由组（需要管理员权限）
	adminUserGroup := router.Group("/api/admin/users")
	adminUserGroup.Use(middleware.AuthMiddleware(authService))
	{
		// 用户管理CRUD操作
		adminUserGroup.GET("", userHandler.GetUsers)
		adminUserGroup.POST("", userHandler.CreateUser)
		adminUserGroup.GET("/:id", userHandler.GetUser)
		adminUserGroup.PUT("/:id", userHandler.UpdateUser)
		adminUserGroup.DELETE("/:id", userHandler.DeleteUser)
	}

	logger.Info("✅ Auth routes setup completed")
}
