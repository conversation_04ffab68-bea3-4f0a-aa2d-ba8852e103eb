package routes

import (
	handlersV3 "server-api/handlers/v3"
	"server-api/services"
	"shared/logger"

	"github.com/gin-gonic/gin"
)

func SetupV3Routes(
	router *gin.Engine,
	dataService *services.DataService,
	monitoringService *services.MonitoringService,
	workTimeService *services.WorkTimeService,
	statisticsService *services.StatisticsService,
) {
	logger.Info("🔧 Setting up v3 routes...")

	// 创建设备利用率处理器V3
	deviceHandlerV3 := handlersV3.NewDeviceHandlerV3(
		dataService,
		workTimeService,
		statisticsService,
		monitoringService,
	)

	// 创建设置处理器V3
	settingsHandlerV3 := handlersV3.NewSettingHandlerV3(
		dataService,
		workTimeService,
		statisticsService,
		monitoringService,
	)

	// V3 API路由组
	v3Group := router.Group("/api/v3")
	{
		// 设备相关路由
		// 设备列表
		v3Group.GET("/devices", deviceHandlerV3.GetDevices)
		// 设备状态详细数据
		v3Group.GET("/device/:id/status_history", deviceHandlerV3.GetDeviceStatusHistoryByDate)
		//v3Group.GET("/device/:id/utilization", deviceHandlerV3.GetDeviceUtilizationByDate)

		// 设备利用率详细分析
		v3Group.GET("/utilization/machines", deviceHandlerV3.GetAllDevicesUtilizationByDate)
		// 设备利用率仪表盘 - 每日利用率趋势
		v3Group.GET("/utilization/daily", deviceHandlerV3.GetAllDevicesUtilizationByDateRange)
		// 设备利用率仪表盘 - 今日设备实时利用率排名
		v3Group.GET("/utilization/realtime", deviceHandlerV3.GetAllDevicesUtilizationByRealtime)

		// 设置相关路由
		v3Group.GET("/settings", settingsHandlerV3.GetSettings)
		v3Group.GET("/setting/display", settingsHandlerV3.GetDisplaySettings)
		v3Group.GET("/setting/refresh", settingsHandlerV3.GetRefreshSettings)
		v3Group.GET("/setting/worktime", settingsHandlerV3.GetWorktime)
		v3Group.GET("/info", settingsHandlerV3.GetSiteInfo)
	}

	logger.Info("✅ V3 routes setup completed")
}
