/**
 * 统计分析路由配置文件
 *
 * 功能描述：
 * 配置统计分析相关的API路由，包括设备统计、班次分析、工作时间管理等功能
 * 提供完整的制造业数据统计分析路由支持
 *
 * 路由组织：
 * - /api/statistics/* - 统计分析相关API
 * - /api/statistics/devices/* - 设备统计API
 * - /api/statistics/scheduler/* - 调度器管理API
 * - /api/statistics/worktime - 工作时间配置API
 *
 * 中间件：
 * - 认证中间件：确保用户已登录
 * - 权限中间件：检查用户权限
 * - 监控中间件：记录API调用指标
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"log"

	"github.com/gin-gonic/gin"

	"server-api/handlers/statistics"
	"server-api/services"
	"shared/logger"
)

/**
 * 设置统计分析API路由
 *
 * 功能：配置统计分析相关的所有API端点和中间件
 *
 * 路由结构：
 * - GET /api/statistics/devices/{deviceId} - 单设备统计
 * - POST /api/statistics/devices/multiple - 多设备统计
 * - GET /api/statistics/devices/{deviceId}/range - 设备日期范围统计
 * - GET /api/statistics/devices/{deviceId}/shifts - 班次统计
 * - GET /api/statistics/summary - 汇总统计
 * - POST /api/statistics/scheduler/run - 手动执行统计任务
 * - GET /api/statistics/scheduler/status - 调度器状态
 * - GET /api/statistics/worktime - 获取工作时间设置
 * - POST /api/statistics/worktime - 保存工作时间设置
 *
 * 服务依赖：
 * - DataService：提供数据访问和业务逻辑
 * - MonitoringService：提供API性能监控
 * - WorkTimeService：提供工作时间管理
 *
 * 容错机制：
 * - 服务不可用时记录警告日志
 * - 不阻止其他路由正常工作
 * - 提供降级处理方案
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 *
 * @example
 * SetupStatisticsRoutes(router, dataService, monitoringService, workTimeService)
 */
func SetupStatisticsRoutes(
	router *gin.Engine,
	dataService *services.DataService,
	monitoringService *services.MonitoringService,
	workTimeService *services.WorkTimeService,
	statisticsService *services.StatisticsService,
	schedulerService *services.SchedulerService,
) {
	logger.Info("🔧 Setting up statistics routes...")

	// 检查必要的服务依赖
	if dataService == nil {
		logger.Warn("⚠️ DataService is nil, statistics routes may not work properly")
	}
	if workTimeService == nil {
		logger.Warn("⚠️ WorkTimeService is nil, work time management may not work properly")
	}

	// 检查统计服务依赖
	if statisticsService == nil {
		logger.Warn("⚠️ StatisticsService is nil, statistics functionality may not work properly")
	}
	if schedulerService == nil {
		logger.Warn("⚠️ SchedulerService is nil, scheduler functionality may not work properly")
	}

	// 创建统计处理器
	statisticsHandler := statistics.NewStatisticsHandler(
		statisticsService,
		schedulerService,
		workTimeService,
		log.Default(), // 使用默认日志器
	)

	// 创建统计分析路由组
	statisticsGroup := router.Group("/api/statistics")
	{
		// 设备统计相关路由
		deviceGroup := statisticsGroup.Group("/devices")
		{
			// 单设备统计
			deviceGroup.GET("/:deviceId", statisticsHandler.GetDeviceStatistics)

			// 多设备统计
			deviceGroup.POST("/multiple", statisticsHandler.GetMultipleDeviceStatistics)

			// 设备日期范围统计
			deviceGroup.GET("/:deviceId/range", statisticsHandler.GetDeviceStatisticsByDateRange)

			// 班次统计
			deviceGroup.GET("/:deviceId/shifts", statisticsHandler.GetShiftStatistics)
		}

		// 汇总统计
		statisticsGroup.GET("/summary", statisticsHandler.GetDailyStatisticsSummary)

		// 调度器管理路由
		schedulerGroup := statisticsGroup.Group("/scheduler")
		{
			// 手动执行统计任务
			schedulerGroup.POST("/run", statisticsHandler.RunManualStatistics)

			// 获取调度器状态
			schedulerGroup.GET("/status", statisticsHandler.GetSchedulerStatus)
		}

		// 工作时间配置路由
		statisticsGroup.GET("/worktime", statisticsHandler.GetWorkTimeSettings)
		statisticsGroup.POST("/worktime", statisticsHandler.SaveWorkTimeSettings)
	}

	logger.Info("✅ Statistics routes setup completed")
}
