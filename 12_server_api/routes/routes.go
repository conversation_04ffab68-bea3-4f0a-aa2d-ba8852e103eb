/**
 * 主路由配置文件
 *
 * 功能描述：
 * 统一管理和配置所有API路由，提供模块化的路由组织结构
 * 负责整合各个功能模块的路由，并设置必要的中间件
 *
 * 路由模块：
 * - 健康检查路由 - 系统状态监控
 * - CORS中间件 - 跨域支持
 * - 机器监控路由 - 设备状态和生产数据
 * - 查看器路由 - 数据查看和分析
 * - 设置路由 - 系统配置管理
 * - 公开路由 - 无需认证的API
 * - 认证路由 - 用户认证和管理
 *
 * 设计原则：
 * - 模块化：每个功能模块独立管理路由
 * - 可扩展：新增功能模块时只需添加对应路由设置
 * - 容错性：单个模块失败不影响其他模块
 * - 清晰性：路由组织结构清晰，便于维护
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"github.com/gin-gonic/gin"

	"server-api/adapters"
	"server-api/services"
)

/**
 * 设置所有API路由
 *
 * 功能：统一配置和注册所有功能模块的路由
 *
 * 路由设置顺序：
 * 1. CORS中间件 - 必须最先设置，确保跨域请求正常
 * 2. 健康检查路由 - 基础系统监控，无依赖
 * 3. 公开路由 - 无需认证的基础API
 * 4. 认证路由 - 用户认证和管理
 * 5. 业务路由 - 核心业务功能API
 * 6. 管理路由 - 系统管理和配置API
 *
 * 服务依赖：
 * - dataService: 数据管理服务
 * - influxService: InfluxDB时序数据服务
 * - monitoringService: 系统监控服务
 * - workTimeService: 工作时间管理服务
 * - authService: 用户认证服务
 * - userService: 用户管理服务
 * - mongoAdapter: MongoDB数据库适配器
 * - statisticsService: 统计分析服务
 * - schedulerService: 任务调度服务
 *
 * 容错机制：
 * - 服务不可用时跳过对应路由注册
 * - 记录警告日志便于问题诊断
 * - 不阻止其他功能正常运行
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*services.DataService} dataService - 数据管理服务实例
 * @param {*services.InfluxDBService} influxService - InfluxDB服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 * @param {*services.AuthService} authService - 认证服务实例
 * @param {*services.UserService} userService - 用户服务实例
 * @param {*adapters.MongoAdminServiceAdapter} mongoAdapter - MongoDB适配器实例
 * @param {*services.StatisticsService} statisticsService - 统计分析服务实例
 * @param {*services.SchedulerService} schedulerService - 任务调度服务实例
 *
 * @example
 * SetupRoutes(router, dataService, influxService, monitoringService,
 *            workTimeService, authService, userService, mongoAdapter,
 *            statisticsService, schedulerService)
 */
func SetupRoutes(
	router *gin.Engine,
	dataService *services.DataService,
	influxService *services.InfluxDBService,
	monitoringService *services.MonitoringService,
	workTimeService *services.WorkTimeService,
	authService *services.AuthService,
	userService *services.UserService,
	mongoAdapter *adapters.MongoAdminServiceAdapter,
	statisticsService *services.StatisticsService,
	schedulerService *services.SchedulerService,
) {
	// 1. 设置CORS中间件（必须最先设置）
	SetupCORSMiddleware(router)

	// 2. 设置健康检查和基础路由（无依赖）
	SetupHealthRoutes(router)

	// 3. 设置公开API路由（无需认证）
	SetupPublicRoutes(router, mongoAdapter, monitoringService)

	// 4. 设置认证相关路由
	SetupAuthRoutes(router, authService, userService)

	// 5. 设置机器监控API路由（核心业务功能）
	SetupMachineRoutes(router, dataService, monitoringService, workTimeService)

	// 6. 设置数据查看器API路由
	SetupViewerRoutes(router, dataService, influxService, monitoringService)

	// 7. 设置系统设置API路由
	SetupSettingsRoutes(router, monitoringService, mongoAdapter)

	// 8. 设置统计分析API路由
	SetupStatisticsRoutes(router, dataService, monitoringService, workTimeService, statisticsService, schedulerService)

	// 9. 设置利用率仪表板API路由
	SetupUtilizationRoutes(router, dataService, monitoringService, workTimeService, statisticsService)

	// 10. 设置生产管理API路由
	SetupProductionRoutes(router, mongoAdapter, monitoringService)

	// 11. 设置管理员API路由
	SetupAdminRoutes(router, mongoAdapter, monitoringService, dataService)

	// 12. 设置V3版本API路由
	SetupV3Routes(router, dataService, monitoringService, workTimeService, statisticsService)
}
