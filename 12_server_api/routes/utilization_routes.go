/**
 * 设备利用率仪表板路由配置文件
 *
 * 功能描述：
 * 配置设备利用率仪表板相关的API路由，包括利用率概览、排名、趋势分析等功能
 * 提供完整的设备利用率统计、分析和可视化路由支持
 *
 * 路由组织：
 * - /api/utilization/* - 设备利用率相关API
 * - /api/utilization/overview - 利用率概览
 * - /api/utilization/devices - 设备利用率排名
 * - /api/utilization/shifts - 班次利用率排名
 * - /api/utilization/daily-trend - 每日利用率趋势
 * - /api/utilization/shift-trend - 班次利用率趋势
 *
 * 技术特性：
 * - 实时数据支持：当天数据实时计算
 * - 历史数据查询：支持历史数据分析
 * - 多维度统计：设备、班次、日期等维度
 * - 性能监控：集成API调用性能指标
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"github.com/gin-gonic/gin"

	"server-api/handlers/utilization"
	"server-api/services"
	"shared/logger"
)

/**
 * 设置设备利用率仪表板API路由
 *
 * 功能：配置设备利用率仪表板相关的所有API端点和中间件
 *
 * 路由结构：
 * - GET /api/utilization/overview - 获取设备利用率概览
 * - GET /api/utilization/devices - 获取各设备利用率排名
 * - GET /api/utilization/shifts - 获取各设备班次利用率排名
 * - GET /api/utilization/daily-trend - 获取每日利用率趋势
 * - GET /api/utilization/shift-trend - 获取每日班次利用率趋势
 *
 * 查询参数支持：
 * - date：指定日期查询
 * - start_date/end_date：日期范围查询
 * - days：天数查询（从今天往前推）
 *
 * 服务依赖：
 * - DataService：提供设备数据访问接口
 * - MonitoringService：提供API性能监控
 * - WorkTimeService：提供工作时间配置管理
 * - StatisticsService：提供统计数据计算和查询
 *
 * 容错机制：
 * - 服务不可用时记录警告日志
 * - 不阻止其他路由正常工作
 * - 提供数据降级处理
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*services.DataService} dataService - 数据服务实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*services.WorkTimeService} workTimeService - 工作时间服务实例
 * @param {*services.StatisticsService} statisticsService - 统计服务实例
 *
 * @example
 * SetupUtilizationRoutes(router, dataService, monitoringService, workTimeService, statisticsService)
 */
func SetupUtilizationRoutes(
	router *gin.Engine,
	dataService *services.DataService,
	monitoringService *services.MonitoringService,
	workTimeService *services.WorkTimeService,
	statisticsService *services.StatisticsService,
) {
	logger.Info("🔧 Setting up utilization routes...")

	// 检查必要的服务依赖
	if dataService == nil {
		logger.Warn("⚠️ DataService is nil, utilization routes may not work properly")
	}
	if workTimeService == nil {
		logger.Warn("⚠️ WorkTimeService is nil, work time calculations may not work properly")
	}
	if statisticsService == nil {
		logger.Warn("⚠️ StatisticsService is nil, statistics functionality may not work properly")
	}
	if monitoringService == nil {
		logger.Warn("⚠️ MonitoringService is nil, API metrics will not be recorded")
	}

	// 创建设备利用率处理器
	utilizationHandler := utilization.NewUtilizationHandler(
		dataService,
		workTimeService,
		statisticsService,
		monitoringService,
	)

	// 创建设备利用率路由组
	utilizationGroup := router.Group("/api/utilization")
	{
		// 设备利用率概览
		// 支持查询参数：date（日期，格式YYYY-MM-DD）
		utilizationGroup.GET("/overview", utilizationHandler.GetUtilizationOverview)

		// 各设备利用率排名
		// 支持查询参数：date（日期，格式YYYY-MM-DD）
		utilizationGroup.GET("/devices", utilizationHandler.GetDeviceUtilizationRanking)

		// 各设备班次利用率排名
		// 支持查询参数：date（日期，格式YYYY-MM-DD）
		utilizationGroup.GET("/shifts", utilizationHandler.GetShiftUtilizationRanking)

		// 每日利用率趋势
		// 支持查询参数：
		// - start_date, end_date（日期范围，格式YYYY-MM-DD）
		// - days（天数，从今天往前推）
		utilizationGroup.GET("/daily-trend", utilizationHandler.GetDailyUtilizationTrend)

		// 机器页面V2完整数据
		// 支持查询参数：date（日期，格式YYYY-MM-DD）
		// 返回页面所需的全部数据，包括概览、设备排名、工作时间设置等
		utilizationGroup.GET("/machine-v2", utilizationHandler.GetMachinePageV2Data)

		// 仪表板V2完整数据
		// 支持查询参数：start_date, end_date（日期范围，格式YYYY-MM-DD）
		// 返回仪表板所需的全部数据，包括概览、设备排名、每日趋势等
		utilizationGroup.GET("/dashboard-v2", utilizationHandler.GetDashboardV2Data)

		// 每日班次利用率趋势（预留接口）
		// 支持查询参数：
		// - start_date, end_date（日期范围，格式YYYY-MM-DD）
		// - days（天数，从今天往前推）
		utilizationGroup.GET("/shift-trend", func(c *gin.Context) {
			// 暂时返回空数据，后续可以实现
			c.JSON(200, gin.H{
				"start_date":         "",
				"end_date":           "",
				"shift_trend_data":   []interface{}{},
				"summary":            gin.H{},
				"work_time_settings": gin.H{},
				"last_updated":       "",
			})
		})
	}

	logger.Info("✅ Utilization routes setup completed")
}
