/**
 * 系统设置API路由
 *
 * 功能描述：
 * 提供系统配置管理相关的API接口，包括刷新设置、显示设置等
 * 这些设置主要用于前端界面的配置和系统行为控制
 *
 * 路由列表：
 * - GET /api/settings - 获取系统设置
 * - GET /api/settings/refresh - 获取刷新设置
 * - POST /api/settings/refresh - 更新刷新设置
 * - GET /api/settings/display - 获取显示设置
 * - POST /api/settings/display - 更新显示设置
 *
 * 访问权限：公开访问，无需认证
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"server-api/adapters"
	"server-api/handlers/settings"
	"server-api/services"
	"shared/logger"

	"github.com/gin-gonic/gin"
)

/**
 * 设置系统设置API路由
 *
 * 功能：注册所有与系统设置相关的API路由
 *
 * 依赖服务：
 * - monitoringService: 监控服务，提供系统状态监控
 * - mongoAdapter: MongoDB适配器，提供数据库操作接口
 *
 * 路由分组：
 * - /api/settings* - 系统设置相关API
 *
 * 设置类型：
 * 1. 刷新设置 - 控制前端数据刷新频率和行为
 * 2. 显示设置 - 控制前端界面显示样式和布局
 * 3. 通用设置 - 其他系统级配置选项
 *
 * 数据持久化：
 * - 设置数据存储在MongoDB中
 * - 支持实时更新和查询
 * - 提供默认值机制
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 * @param {*services.MonitoringService} monitoringService - 监控服务实例
 * @param {*adapters.MongoAdminServiceAdapter} mongoAdapter - MongoDB适配器实例
 *
 * @example
 * SetupSettingsRoutes(router, monitoringService, mongoAdapter)
 */
func SetupSettingsRoutes(
	router *gin.Engine,
	monitoringService *services.MonitoringService,
	mongoAdapter *adapters.MongoAdminServiceAdapter,
) {
	logger.Info("🔧 Setting up settings routes...")

	// 检查必要的服务是否可用
	if mongoAdapter == nil {
		logger.Warn("⚠️ MongoAdapter not available, skipping settings routes")
		return
	}

	// 创建设置处理器实例
	settingsHandler := settings.NewSettingsHandler(monitoringService, mongoAdapter)

	// 创建设置API路由组
	api := router.Group("/api")
	{
		// 通用设置API
		api.GET("/settings", settingsHandler.GetSettings)

		// 刷新设置API
		// 控制前端数据刷新间隔、自动刷新开关等
		api.GET("/settings/refresh", settingsHandler.GetRefreshSettings)
		api.POST("/settings/refresh", settingsHandler.UpdateRefreshSettings)

		// 显示设置API
		// 控制前端界面主题、布局、颜色方案等
		api.GET("/settings/display", settingsHandler.GetDisplaySettings)
		api.POST("/settings/display", settingsHandler.UpdateDisplaySettings)
	}

	logger.Info("✅ Settings routes setup completed")
}
