/**
 * CORS跨域中间件
 *
 * 功能描述：
 * 提供跨域资源共享(CORS)支持，允许前端应用从不同域名访问API
 * 配置了完整的CORS头部，支持预检请求和凭据传递
 *
 * 支持特性：
 * - 允许所有来源访问 (*)
 * - 支持常用HTTP方法 (GET, POST, PUT, DELETE, OPTIONS)
 * - 支持自定义请求头
 * - 支持凭据传递
 * - 预检请求缓存优化
 *
 * @package routes
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package routes

import (
	"github.com/gin-gonic/gin"
)

/**
 * 设置CORS跨域中间件
 *
 * 功能：为路由引擎添加CORS支持中间件
 *
 * CORS配置说明：
 * - Access-Control-Allow-Origin: * (允许所有来源)
 * - Access-Control-Allow-Methods: 支持GET, POST, PUT, DELETE, OPTIONS
 * - Access-Control-Allow-Headers: 支持常用请求头
 * - Access-Control-Allow-Credentials: true (支持凭据)
 * - Access-Control-Max-Age: 86400 (预检缓存24小时)
 *
 * 预检请求处理：
 * - OPTIONS请求直接返回204状态码
 * - 避免不必要的业务逻辑处理
 *
 * 安全考虑：
 * - 生产环境建议限制具体的来源域名
 * - 根据需要调整允许的HTTP方法
 * - 敏感API可考虑额外的安全验证
 *
 * @param {*gin.Engine} router - Gin路由引擎实例
 *
 * @example
 * router := gin.Default()
 * SetupCORSMiddleware(router)
 */
func SetupCORSMiddleware(router *gin.Engine) {
	router.Use(func(c *gin.Context) {
		// 设置允许的来源 - 生产环境建议指定具体域名
		c.Header("Access-Control-Allow-Origin", "*")
		
		// 设置允许的HTTP方法
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		
		// 设置允许的请求头
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, Cache-Control, Pragma, Expires, X-Requested-With, Accept, Origin, DNT, User-Agent, If-Modified-Since, If-None-Match")
		
		// 设置暴露的响应头
		c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Type")
		
		// 允许发送凭据 (cookies, authorization headers)
		c.Header("Access-Control-Allow-Credentials", "true")
		
		// 预检请求缓存时间 (24小时)
		c.Header("Access-Control-Max-Age", "86400")

		// 处理预检请求 (OPTIONS)
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		// 继续处理其他请求
		c.Next()
	})
}
