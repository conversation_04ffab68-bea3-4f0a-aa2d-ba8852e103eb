#!/bin/bash

# 环境变量功能演示脚本
# Environment Variables Functionality Demo Script

echo "🎬 环境变量配置功能演示"
echo "Environment Variables Configuration Demo"
echo "========================================"

# 演示1: 使用默认值
echo ""
echo "📋 演示1: 使用默认值 (不设置环境变量)"
echo "Demo 1: Using default values (no environment variables set)"
echo "------------------------------------------------------------"

# 清除可能存在的环境变量
unset SITE_NAME SITE_LOGO SITE_SUPPORT SITE_SUPPORT_INFO

echo "当前环境变量状态:"
echo "Current environment variables status:"
echo "SITE_NAME: ${SITE_NAME:-未设置}"
echo "SITE_LOGO: ${SITE_LOGO:-未设置}"
echo "SITE_SUPPORT: ${SITE_SUPPORT:-未设置}"
echo "SITE_SUPPORT_INFO: ${SITE_SUPPORT_INFO:-未设置}"

echo ""
echo "预期结果: 应该使用代码中的默认值"
echo "Expected result: Should use default values from code"
echo "- SITE_NAME: abc智能制造系统"
echo "- SITE_LOGO: (空字符串)"
echo "- SITE_SUPPORT: abc科技"
echo "- SITE_SUPPORT_INFO: 技术支持: 13800000000"

# 演示2: 使用自定义环境变量
echo ""
echo "📋 演示2: 使用自定义环境变量"
echo "Demo 2: Using custom environment variables"
echo "--------------------------------------------"

# 设置自定义环境变量
export SITE_NAME="我的智能制造系统"
export SITE_LOGO="/assets/my-company-logo.png"
export SITE_SUPPORT="我的科技公司"
export SITE_SUPPORT_INFO="技术支持: 400-MY-TECH (400-69-8324)"

echo "设置的环境变量:"
echo "Set environment variables:"
echo "SITE_NAME: $SITE_NAME"
echo "SITE_LOGO: $SITE_LOGO"
echo "SITE_SUPPORT: $SITE_SUPPORT"
echo "SITE_SUPPORT_INFO: $SITE_SUPPORT_INFO"

echo ""
echo "预期结果: 应该使用环境变量中的值"
echo "Expected result: Should use values from environment variables"

# 演示3: 部分环境变量设置
echo ""
echo "📋 演示3: 部分环境变量设置"
echo "Demo 3: Partial environment variables setup"
echo "--------------------------------------------"

# 清除所有环境变量
unset SITE_NAME SITE_LOGO SITE_SUPPORT SITE_SUPPORT_INFO

# 只设置部分环境变量
export SITE_NAME="部分配置系统"
export SITE_LOGO="/assets/partial-logo.png"
# SITE_SUPPORT 和 SITE_SUPPORT_INFO 不设置，应该使用默认值

echo "设置的环境变量:"
echo "Set environment variables:"
echo "SITE_NAME: $SITE_NAME"
echo "SITE_LOGO: $SITE_LOGO"
echo "SITE_SUPPORT: ${SITE_SUPPORT:-未设置，将使用默认值}"
echo "SITE_SUPPORT_INFO: ${SITE_SUPPORT_INFO:-未设置，将使用默认值}"

echo ""
echo "预期结果: 混合使用环境变量和默认值"
echo "Expected result: Mix of environment variables and default values"
echo "- SITE_NAME: 部分配置系统 (来自环境变量)"
echo "- SITE_LOGO: /assets/partial-logo.png (来自环境变量)"
echo "- SITE_SUPPORT: abc科技 (默认值)"
echo "- SITE_SUPPORT_INFO: 技术支持: 13800000000 (默认值)"

# 演示4: Docker环境变量设置示例
echo ""
echo "📋 演示4: Docker环境变量设置示例"
echo "Demo 4: Docker environment variables setup examples"
echo "----------------------------------------------------"

echo "Docker Run 命令示例:"
echo "Docker Run command example:"
echo 'docker run -d \'
echo '  -e SITE_NAME="我的智能制造系统" \'
echo '  -e SITE_LOGO="/assets/my-logo.png" \'
echo '  -e SITE_SUPPORT="我的科技公司" \'
echo '  -e SITE_SUPPORT_INFO="技术支持: 400-123-4567" \'
echo '  your-image:tag'

echo ""
echo "Docker Compose 配置示例:"
echo "Docker Compose configuration example:"
cat << 'EOF'
services:
  server-api:
    image: your-image:tag
    environment:
      - SITE_NAME=我的智能制造系统
      - SITE_LOGO=/assets/my-logo.png
      - SITE_SUPPORT=我的科技公司
      - SITE_SUPPORT_INFO=技术支持: 400-123-4567
EOF

# 演示5: .env文件使用示例
echo ""
echo "📋 演示5: .env文件使用示例"
echo "Demo 5: .env file usage example"
echo "--------------------------------"

echo "创建示例.env文件:"
echo "Creating example .env file:"

cat << 'EOF' > .env.demo
# 站点配置环境变量
SITE_NAME=演示智能制造系统
SITE_LOGO=/assets/demo-logo.png
SITE_SUPPORT=演示科技公司
SITE_SUPPORT_INFO=技术支持: 400-DEMO-123
EOF

echo "✅ 已创建 .env.demo 文件"
echo "✅ Created .env.demo file"

echo ""
echo "文件内容:"
echo "File content:"
cat .env.demo

echo ""
echo "使用方法:"
echo "Usage:"
echo "1. 复制为.env: cp .env.demo .env"
echo "2. 应用程序启动时会自动读取.env文件"
echo "3. 或者手动加载: source .env"

# 清理演示
echo ""
echo "🧹 清理演示环境"
echo "Cleaning up demo environment"
echo "----------------------------"

# 清除演示用的环境变量
unset SITE_NAME SITE_LOGO SITE_SUPPORT SITE_SUPPORT_INFO

echo "✅ 环境变量已清除"
echo "✅ Environment variables cleared"

echo ""
echo "🎯 演示总结"
echo "Demo Summary"
echo "============"
echo "✅ 展示了默认值的使用"
echo "✅ Demonstrated default values usage"
echo "✅ 展示了自定义环境变量的设置"
echo "✅ Demonstrated custom environment variables setup"
echo "✅ 展示了部分配置的灵活性"
echo "✅ Demonstrated flexibility of partial configuration"
echo "✅ 提供了Docker部署示例"
echo "✅ Provided Docker deployment examples"
echo "✅ 创建了.env文件示例"
echo "✅ Created .env file example"

echo ""
echo "📚 相关文件"
echo "Related Files"
echo "============="
echo "- handlers/v3/setting_handler.go (修改后的代码)"
echo "- .env.example (环境变量示例)"
echo "- ENVIRONMENT_VARIABLES_GUIDE.md (详细指南)"
echo "- .env.demo (演示用配置文件)"

echo ""
echo "🚀 下一步"
echo "Next Steps"
echo "=========="
echo "1. 根据需要设置环境变量"
echo "2. 测试API端点: GET /api/v3/setting/site-info"
echo "3. 验证返回的配置信息是否正确"
echo "4. 在生产环境中部署时设置相应的环境变量"

echo ""
echo "✅ 演示完成!"
echo "✅ Demo completed!"
