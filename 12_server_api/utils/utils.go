package utils

import (
	"fmt"
	"math"
	"shared/logger"
	"strconv"
	"strings"
	"time"
)

/**
 * 获取状态的中文名称
 *
 * 功能：将英文状态名称转换为中文显示名称
 *
 * @param {string} status - 英文状态名称
 * @returns {string} 中文状态名称
 */
func GetStatusNameChinese(status string) string {
	statusMap := map[string]string{
		"production":  "生产",
		"idle":        "空闲",
		"fault":       "故障",
		"shutdown":    "停机",
		"maintenance": "维护",
		"setup":       "设置",
		"unknown":     "未知",
	}

	if chineseName, exists := statusMap[status]; exists {
		return chineseName
	}
	return status // 如果没有找到对应的中文名称，返回原始状态名称
}

/**
 * 将秒数转换为中文时分秒格式
 *
 * 功能：将持续时间（秒）转换为易读的中文格式
 *
 * @param {int} seconds - 持续时间（秒）
 * @returns {string} 中文时分秒格式字符串
 *
 * @example
 * formatDurationChinese(3661) // 返回 "1时1分1秒"
 * formatDurationChinese(125)  // 返回 "2分5秒"
 * formatDurationChinese(45)   // 返回 "45秒"
 */
func FormatDurationChinese(seconds int) string {
	if seconds < 60 {
		return fmt.Sprintf("%d秒", seconds)
	}

	hours := seconds / 3600
	minutes := (seconds % 3600) / 60
	remainingSeconds := seconds % 60

	var parts []string

	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d时", hours))
	}

	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d分", minutes))
	}

	if remainingSeconds > 0 {
		parts = append(parts, fmt.Sprintf("%d秒", remainingSeconds))
	}

	return strings.Join(parts, "")
}

/**
 * 辅助方法：检查两个时间段是否有重叠
 */
func HasTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && end1.After(start2)
}

/**
 * 辅助方法：返回两个时间中较大的一个
 */
func MaxTime(t1, t2 time.Time) time.Time {
	if t1.After(t2) {
		return t1
	}
	return t2
}

/**
 * 辅助方法：返回两个时间中较小的一个
 */
func MinTime(t1, t2 time.Time) time.Time {
	if t1.Before(t2) {
		return t1
	}
	return t2
}

/**
 * 解析时间字符串
 *
 * 功能：解析HH:MM格式的时间字符串，返回小时和分钟
 *
 * @param {string} timeStr - 时间字符串，格式：HH:MM
 * @returns {int, int} 小时和分钟
 */
func ParseTime(timeStr string) (int, int) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		logger.Warnf("⚠️ Invalid time format: %s, using default 08:00", timeStr)
		return 8, 0
	}

	hour, err1 := strconv.Atoi(parts[0])
	minute, err2 := strconv.Atoi(parts[1])

	if err1 != nil || err2 != nil {
		logger.Warnf("⚠️ Failed to parse time: %s, using default 08:00", timeStr)
		return 8, 0
	}

	// 验证时间范围
	if hour < 0 || hour > 23 || minute < 0 || minute > 59 {
		logger.Warnf("⚠️ Invalid time range: %s (hour: %d, minute: %d), using default 08:00", timeStr, hour, minute)
		return 8, 0
	}

	return hour, minute
}

func FixDecimal(x float64) float64 {
	return math.Round(x*10) / 10
}
