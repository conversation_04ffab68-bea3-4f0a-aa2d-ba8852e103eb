/**
 * 日期范围辅助工具
 *
 * 功能描述：
 * 提供日期范围查询的辅助功能，用于确定查询时间范围的类型和具体的开始结束时间
 * 支持跨日查询、今日查询、历史查询等多种场景
 *
 * 主要功能：
 * - 解析查询日期和每天开始时间
 * - 计算查询的开始和结束时间（UTC）
 * - 判断查询类型（今天、混合、过去）
 * - 处理跨日查询场景
 *
 * 使用场景：
 * - 设备状态历史查询
 * - 生产数据统计分析
 * - 工作时间范围计算
 *
 * @package machine
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-15
 */
package utils

import (
	"fmt"
	"time"

	"shared/logger"
)

/**
 * 查询日期类型枚举
 *
 * 用于标识查询时间范围相对于当前时间的关系
 */
type QueryDateType string

const (
	// QueryDateTypeMixed 混合 - 查询时间范围跨越今天和其他日期
	QueryDateTypeMixed QueryDateType = "mixed"

	// QueryDateTypeOneDay 同一天 - 查询时间范围完全在同一天
	QueryDateTypeOneDay QueryDateType = "one_day"
)

/**
 * 查询日期范围结果
 *
 * 包含查询类型和具体的时间范围信息
 */
type QueryDateRangeResult struct {
	// Type 查询类型（今天、混合、过去）
	Type QueryDateType `json:"type"`

	// StartDateTime 查询开始时间（UTC）
	StartDateTime time.Time `json:"start_date_time"`

	// EndDateTime 查询结束时间（UTC）
	EndDateTime time.Time `json:"end_date_time"`

	// IsCrossDay 是否跨日查询
	IsCrossDay bool `json:"is_cross_day"`

	// ContainsToday 是否包含今天
	ContainsToday bool `json:"contains_today"`

	// BeginIsToday 开始时间是否在今天
	BeginIsToday bool `json:"begin_is_today"`

	// EndIsToday 结束时间是否在今天
	EndIsToday bool `json:"end_is_today"`
}

/**
 * 计算查询日期范围
 *
 * 功能：根据查询日期和每天开始时间，计算查询的时间范围和类型
 *
 * 逻辑说明：
 * 1. 查询的日期 + 每天开始时间 = 数据查询的开始日期时间
 * 2. 结束时间 = 开始时间 + 24小时
 * 3. 判断查询类型：
 *    - 今天：查询时间范围完全在今天
 *    - 混合：查询时间范围跨越今天和其他日期
 *    - 过去：查询时间范围完全在过去
 *
 * 参数说明：
 * @param {string} queryDate - 查询日期，格式：YYYY-MM-DD，空字符串表示今天
 * @param {string} dayStartTime - 每天开始时间，格式：HH:MM，空字符串使用默认值00:00
 *
 * 返回值：
 * @returns {QueryDateRangeResult} 查询日期范围结果
 * @returns {error} 错误信息
 *
 * 使用示例：
 * ```go
 * // 查询今天从8:00开始的24小时数据
 * result, err := CalculateQueryDateRange("", "08:00")
 *
 * // 查询2025-06-14从0:00开始的24小时数据
 * result, err := CalculateQueryDateRange("2025-06-14", "00:00")
 *
 * // 查询2025-06-15从22:00开始的24小时数据（跨日）
 * result, err := CalculateQueryDateRange("2025-06-15", "22:00")
 * ```
 */
func CalculateQueryDateRange(queryDate, dayStartTime string) (*QueryDateRangeResult, error) {
	// 设置默认的每天开始时间
	if dayStartTime == "" {
		dayStartTime = "00:00"
		logger.Debugf("📅 使用默认的每天开始时间: %s", dayStartTime)
	} else {
		logger.Debugf("📅 使用配置的每天开始时间: %s", dayStartTime)
	}

	// 设置默认的查询日期
	if queryDate == "" {
		queryDate = time.Now().Format("2006-01-02")
		logger.Debugf("📅 使用默认的查询日期: %s", queryDate)
	}

	// 设置北京时区（UTC+8）
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Errorf("❌ 加载北京时区失败: %v", err)
		return nil, err
	}

	// 解析查询开始时间（假设输入是北京时间）
	queryBeginDateTime, err := time.ParseInLocation("2006-01-02 15:04", queryDate+" "+dayStartTime, beijingLocation)
	if err != nil {
		logger.Errorf("❌ 解析查询开始时间失败: %v", err)
		return nil, err
	}

	// 转换为UTC时间
	queryBeginDateTimeUtc := queryBeginDateTime.UTC()

	// 计算查询结束时间（根据开始时间UTC的小时数决定逻辑）
	var queryEndDateTimeUtc time.Time

	queryEndDateTime := queryBeginDateTime.Add(24*time.Hour - time.Second)
	queryEndDateTimeUtc = queryEndDateTime.UTC()

	logger.Debugf("📅 查询开始时间: %s, 查询结束时间: %s",
		queryBeginDateTimeUtc.Format(time.RFC3339),
		queryEndDateTimeUtc.Format(time.RFC3339))

	// 是否跨天的条件：返回值的开始日期时间与结束日期时间的UTC日期不相同即为跨天
	sameDate := queryBeginDateTimeUtc.Day() == queryEndDateTimeUtc.Day() &&
		queryBeginDateTimeUtc.Month() == queryEndDateTimeUtc.Month() &&
		queryBeginDateTimeUtc.Year() == queryEndDateTimeUtc.Year()
	isCrossDay := !sameDate

	if isCrossDay {
		logger.Debugf("📅 查询时间范围跨天，需要跨日查询: %s to %s",
			queryBeginDateTimeUtc.Format(time.RFC3339),
			queryEndDateTimeUtc.Format(time.RFC3339))
	}

	// 是否当天的条件：返回值的开始日期时间或结束日期时间的UTC日期是否与当天的UTC日期相同
	nowUtc := time.Now().UTC()
	beginDateIsToday := queryBeginDateTimeUtc.Day() == nowUtc.Day() &&
		queryBeginDateTimeUtc.Month() == nowUtc.Month() &&
		queryBeginDateTimeUtc.Year() == nowUtc.Year()
	endDateIsToday := queryEndDateTimeUtc.Day() == nowUtc.Day() &&
		queryEndDateTimeUtc.Month() == nowUtc.Month() &&
		queryEndDateTimeUtc.Year() == nowUtc.Year()

	containsToday := beginDateIsToday || endDateIsToday

	logger.Debugf("📅 查询日期是否包含今天: %v (开始时间在今天: %v, 结束时间在今天: %v)",
		containsToday, beginDateIsToday, endDateIsToday)

	// 确定查询类型（基于跨天和包含今天的逻辑）
	var queryType QueryDateType
	if isCrossDay {
		// 跨天的情况使用混合类型
		queryType = QueryDateTypeMixed
	} else {
		// 不跨天的情况使用同一天类型
		queryType = QueryDateTypeOneDay
	}

	logger.Debugf("📅 查询类型: %s", queryType)

	return &QueryDateRangeResult{
		Type:          queryType,
		StartDateTime: queryBeginDateTimeUtc,
		EndDateTime:   queryEndDateTimeUtc,
		IsCrossDay:    isCrossDay,
		ContainsToday: containsToday,
		BeginIsToday:  beginDateIsToday,
		EndIsToday:    endDateIsToday,
	}, nil
}

/**
 * 获取查询类型的中文描述
 *
 * @param {QueryDateType} queryType - 查询类型
 * @returns {string} 中文描述
 */
func (t QueryDateType) String() string {
	switch t {
	case QueryDateTypeMixed:
		return "混合"
	case QueryDateTypeOneDay:
		return "同一天"
	default:
		return "未知"
	}
}

/**
 * 时间范围分组结果
 *
 * 用于存储分割后的时间范围组
 */
type TimeRangeGroup struct {
	// StartDateTime 开始时间
	StartDateTime time.Time `json:"start_date_time"`

	// EndDateTime 结束时间
	EndDateTime time.Time `json:"end_date_time"`
}

/**
 * 跨日时间范围分割结果
 *
 * 包含分割后的两组时间范围
 */
type CrossDayTimeRanges struct {
	// FirstGroup 第一组时间范围（开始日期的剩余时间）
	FirstGroup TimeRangeGroup `json:"first_group"`

	// SecondGroup 第二组时间范围（结束日期的开始时间）
	SecondGroup TimeRangeGroup `json:"second_group"`
}

/**
 * 分割跨日时间范围
 *
 * 功能：当查询类型为QueryDateTypeMixed（跨日）时，将时间范围分割成两组
 *
 * 重要说明：
 * - 参数必须是UTC时间
 * - 内部算法基于UTC时间计算
 * - 返回值也是UTC时间
 *
 * 分割逻辑：
 * 1. 第一组：从开始时间到开始日期的23:59:59 UTC
 * 2. 第二组：从结束日期的00:00:00 UTC到结束时间
 *
 * 使用场景：
 * - 跨日数据查询时需要分别查询两个日期的数据
 * - InfluxDB和MongoDB的分离查询
 * - 时间段数据的精确分割
 *
 * 参数说明：
 * @param {time.Time} startDateTime - 开始日期时间（必须是UTC时间）
 * @param {time.Time} endDateTime - 结束日期时间（必须是UTC时间）
 *
 * 返回值：
 * @returns {*CrossDayTimeRanges} 分割后的两组时间范围（UTC时间）
 * @returns {error} 错误信息
 *
 * 使用示例：
 * ```go
 * // 假设查询时间为：2025-06-15 10:00:00 到 2025-06-16 02:00:00 (UTC)
 * start := time.Date(2025, 6, 15, 10, 0, 0, 0, time.UTC)
 * end := time.Date(2025, 6, 16, 2, 0, 0, 0, time.UTC)
 *
 * ranges, err := SplitCrossDayTimeRange(start, end)
 * if err != nil {
 *     log.Fatal(err)
 * }
 *
 * // 第一组：2025-06-15 10:00:00 到 2025-06-15 23:59:59 (UTC)
 * fmt.Printf("第一组: %s 到 %s\n",
 *     ranges.FirstGroup.StartDateTime.Format(time.RFC3339),
 *     ranges.FirstGroup.EndDateTime.Format(time.RFC3339))
 *
 * // 第二组：2025-06-16 00:00:00 到 2025-06-16 02:00:00 (UTC)
 * fmt.Printf("第二组: %s 到 %s\n",
 *     ranges.SecondGroup.StartDateTime.Format(time.RFC3339),
 *     ranges.SecondGroup.EndDateTime.Format(time.RFC3339))
 * ```
 */
func SplitCrossDayTimeRange(startDateTime, endDateTime time.Time) (*CrossDayTimeRanges, error) {
	// 确保输入参数是UTC时间
	startDateTimeUtc := startDateTime.UTC()
	endDateTimeUtc := endDateTime.UTC()

	// 验证输入参数
	if startDateTimeUtc.After(endDateTimeUtc) {
		return nil, fmt.Errorf("开始时间不能晚于结束时间: start=%s, end=%s",
			startDateTimeUtc.Format(time.RFC3339),
			endDateTimeUtc.Format(time.RFC3339))
	}

	// 检查是否真的跨日（基于UTC时间）
	startDateUtc := time.Date(startDateTimeUtc.Year(), startDateTimeUtc.Month(), startDateTimeUtc.Day(), 0, 0, 0, 0, time.UTC)
	endDateUtc := time.Date(endDateTimeUtc.Year(), endDateTimeUtc.Month(), endDateTimeUtc.Day(), 0, 0, 0, 0, time.UTC)

	if startDateUtc.Equal(endDateUtc) {
		return nil, fmt.Errorf("时间范围不跨日，无需分割: start=%s, end=%s",
			startDateTimeUtc.Format(time.RFC3339),
			endDateTimeUtc.Format(time.RFC3339))
	}

	logger.Debugf("🔄 分割跨日时间范围 (UTC): %s 到 %s",
		startDateTimeUtc.Format(time.RFC3339),
		endDateTimeUtc.Format(time.RFC3339))

	// 第一组：开始时间到开始日期的23:59:59 UTC
	firstGroupStart := startDateTimeUtc
	firstGroupEnd := time.Date(startDateTimeUtc.Year(), startDateTimeUtc.Month(), startDateTimeUtc.Day(), 23, 59, 59, 0, time.UTC)

	// 第二组：结束日期的00:00:00 UTC到结束时间
	secondGroupStart := time.Date(endDateTimeUtc.Year(), endDateTimeUtc.Month(), endDateTimeUtc.Day(), 0, 0, 0, 0, time.UTC)
	secondGroupEnd := endDateTimeUtc

	logger.Debugf("📅 第一组时间范围 (UTC): %s 到 %s",
		firstGroupStart.Format(time.RFC3339),
		firstGroupEnd.Format(time.RFC3339))
	logger.Debugf("📅 第二组时间范围 (UTC): %s 到 %s",
		secondGroupStart.Format(time.RFC3339),
		secondGroupEnd.Format(time.RFC3339))

	return &CrossDayTimeRanges{
		FirstGroup: TimeRangeGroup{
			StartDateTime: firstGroupStart,
			EndDateTime:   firstGroupEnd,
		},
		SecondGroup: TimeRangeGroup{
			StartDateTime: secondGroupStart,
			EndDateTime:   secondGroupEnd,
		},
	}, nil
}

// IsCrossDay 判断两个时间点是否跨天
// example: IsCrossDay(time.Now(), time.Now().Add(24*time.Hour))
func IsCrossDay(startDateTime, endDateTime time.Time) bool {
	// 确保输入参数是UTC时间
	startDateTimeUtc := startDateTime.UTC()
	endDateTimeUtc := endDateTime.UTC()

	// 检查是否跨天
	return startDateTimeUtc.Day() != endDateTimeUtc.Day() ||
		startDateTimeUtc.Month() != endDateTimeUtc.Month() ||
		startDateTimeUtc.Year() != endDateTimeUtc.Year()
}
