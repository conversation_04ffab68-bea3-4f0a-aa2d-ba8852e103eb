# 日期范围辅助工具使用示例

## 功能概述

`CalculateQueryDateRange` 函数用于计算查询日期范围，根据查询日期和每天开始时间，返回查询类型（今天、混合、过去）和具体的时间范围。

## 函数签名

```go
func CalculateQueryDateRange(queryDate, dayStartTime string) (*QueryDateRangeResult, error)
```

## 参数说明

- `queryDate`: 查询日期，格式：`YYYY-MM-DD`，空字符串表示今天
- `dayStartTime`: 每天开始时间，格式：`HH:MM`，空字符串使用默认值`00:00`

## 返回值

```go
type QueryDateRangeResult struct {
    Type          QueryDateType `json:"type"`           // 查询类型（今天、混合、过去）
    StartDateTime time.Time     `json:"start_date_time"` // 查询开始时间（UTC）
    EndDateTime   time.Time     `json:"end_date_time"`   // 查询结束时间（UTC）
    IsCrossDay    bool          `json:"is_cross_day"`    // 是否跨日查询
    ContainsToday bool          `json:"contains_today"`  // 是否包含今天
}
```

## 查询类型说明

- `QueryDateTypeToday` ("today"): 查询时间范围完全在今天
- `QueryDateTypeMixed` ("mixed"): 查询时间范围跨越今天和其他日期
- `QueryDateTypePast` ("past"): 查询时间范围完全在过去

## 使用示例

### 示例1：查询今天从默认时间开始的数据

```go
result, err := CalculateQueryDateRange("", "")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("查询类型: %s\n", result.Type.String())
fmt.Printf("开始时间: %s\n", result.StartDateTime.Format(time.RFC3339))
fmt.Printf("结束时间: %s\n", result.EndDateTime.Format(time.RFC3339))
fmt.Printf("是否跨日: %v\n", result.IsCrossDay)
fmt.Printf("包含今天: %v\n", result.ContainsToday)
```

输出示例：
```
查询类型: 混合
开始时间: 2025-06-15T00:00:00Z
结束时间: 2025-06-16T00:00:00Z
是否跨日: true
包含今天: true
```

### 示例2：查询今天从8:00开始的数据

```go
result, err := CalculateQueryDateRange("", "08:00")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("查询类型: %s\n", result.Type.String())
// 输出: 查询类型: 混合
```

### 示例3：查询指定日期的数据

```go
result, err := CalculateQueryDateRange("2025-06-14", "08:00")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("查询类型: %s\n", result.Type.String())
// 输出: 查询类型: 过去
```

### 示例4：查询一周前的数据

```go
weekAgo := time.Now().AddDate(0, 0, -7).Format("2006-01-02")
result, err := CalculateQueryDateRange(weekAgo, "00:00")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("查询类型: %s\n", result.Type.String())
fmt.Printf("是否跨日: %v\n", result.IsCrossDay)
// 输出: 查询类型: 过去, 是否跨日: true
```

## 实际应用场景

### 1. 设备状态历史查询

```go
func GetDeviceStatusHistory(deviceID, date, startTime string) {
    // 计算查询范围
    result, err := CalculateQueryDateRange(date, startTime)
    if err != nil {
        return
    }

    switch result.Type {
    case QueryDateTypeToday:
        // 只查询InfluxDB当前数据
        data := queryInfluxDB(deviceID, result.StartDateTime, result.EndDateTime)
        
    case QueryDateTypeMixed:
        // 查询InfluxDB + MongoDB，然后合并
        influxData := queryInfluxDB(deviceID, result.StartDateTime, result.EndDateTime)
        mongoData := queryMongoDB(deviceID, result.StartDateTime, result.EndDateTime)
        data := mergeData(influxData, mongoData)
        
    case QueryDateTypePast:
        // 只查询MongoDB历史数据
        data := queryMongoDB(deviceID, result.StartDateTime, result.EndDateTime)
    }
}
```

### 2. 生产数据统计

```go
func GetProductionStatistics(date, workStartTime string) {
    result, err := CalculateQueryDateRange(date, workStartTime)
    if err != nil {
        return
    }

    if result.ContainsToday {
        // 包含今天的数据，需要实时计算
        stats := calculateRealTimeStats(result.StartDateTime, result.EndDateTime)
    } else {
        // 历史数据，可以使用缓存
        stats := getCachedStats(result.StartDateTime, result.EndDateTime)
    }
}
```

## 注意事项

1. **时区处理**: 输入的日期和时间被视为本地时间，返回的时间戳为UTC时间
2. **跨日判断**: 24小时查询通常会跨日（除非开始时间是00:00且不跨时区）
3. **边界条件**: 函数会正确处理月末、年末等边界情况
4. **性能**: 函数性能良好，每次调用约500纳秒
5. **错误处理**: 对无效的日期和时间格式会返回错误

## 测试覆盖

该函数包含完整的单元测试，覆盖以下场景：
- 今天查询（不同开始时间）
- 过去日期查询
- 跨日查询
- 混合查询
- 默认参数
- 错误输入
- 性能基准测试

运行测试：
```bash
go test ./handlers/machine/ -v
go test ./handlers/machine/ -bench=.
```
