service:
  name: "server-jet"
  host: "0.0.0.0"
  port: 8084
  debug: true
  timeout: 30

redis:
  host: "redis"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

sqlite:
  data_dir: "/data/sqlite"
  retention_days: 7
  batch_size: 1000
  flush_interval: 5

nats:
  url: "nats://nats:4222"
  subject: "device.data.*"
  stream: "DEVICE_STREAM"
  consumer: "server-jet-consumer"
  batch_size: 100

influxdb:
  url: "http://influxdb:8086"
  token: "your-token"
  org: "your-org"
  bucket: "device-data"

mongodb:
  uri: "mongodb://mongodb:27017"
  database: "device_db"
  collection: "device_data"

logging:
  level: "info"
  format: "json"
  output: "stdout"
