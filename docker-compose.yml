version: '3.8'

services:
  # Redis - 临时数据存储
  redis:
    image: redis:7-alpine
    container_name: mdc_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - mdc_network

  # NATS JetStream - 消息队列
  nats:
    image: nats:2.10-alpine
    container_name: mdc_nats
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["-js", "-m", "8222"]
    volumes:
      - nats_data:/data
    networks:
      - mdc_network

  # InfluxDB - 时序数据库
  influxdb:
    image: influxdb:2.7-alpine
    container_name: mdc_influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=password123
      - DOCKER_INFLUXDB_INIT_ORG=mdc-org
      - DOCKER_INFLUXDB_INIT_BUCKET=device-data
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=mdc-token-123456789
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - mdc_network

  # MongoDB - 文档数据库
  mongodb:
    image: mongo:7
    container_name: mdc_mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
    volumes:
      - mongodb_data:/data/db
    networks:
      - mdc_network

  # 设备数据采集服务
  device-collect:
    build:
      context: ./02_device_collect
      dockerfile: Dockerfile
    container_name: mdc_device_collect
    ports:
      - "8081:8081"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8081
      - REDIS_HOST=redis
      - NATS_URL=nats://nats:4222
    volumes:
      - ./configs/device-collect.yml:/app/config.yml
      - sqlite_data:/data/sqlite
    depends_on:
      - redis
      - nats
    networks:
      - mdc_network

  # 数据清洗服务
  data-cleaning:
    build:
      context: ./03_data_cleaning
      dockerfile: Dockerfile
    container_name: mdc_data_cleaning
    ports:
      - "8082:8082"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8082
      - REDIS_HOST=redis
      - NATS_URL=nats://nats:4222
    volumes:
      - ./configs/data-cleaning.yml:/app/config.yml
      - sqlite_data:/data/sqlite
    depends_on:
      - redis
      - nats
    networks:
      - mdc_network

  # 数据推送服务
  data-push:
    build:
      context: ./04_data_push
      dockerfile: Dockerfile
    container_name: mdc_data_push
    ports:
      - "8083:8083"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8083
      - REDIS_HOST=redis
      - NATS_URL=nats://nats:4222
    volumes:
      - ./configs/data-push.yml:/app/config.yml
      - sqlite_data:/data/sqlite
    depends_on:
      - redis
      - nats
    networks:
      - mdc_network

  # NATS JetStream消费服务
  server-jet:
    build:
      context: ./11_server_jet
      dockerfile: Dockerfile
    container_name: mdc_server_jet
    ports:
      - "8084:8084"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8084
      - NATS_URL=nats://nats:4222
      - INFLUXDB_URL=http://influxdb:8086
      - MONGODB_URI=*****************************************
    volumes:
      - ./configs/server-jet.yml:/app/config.yml
    depends_on:
      - nats
      - influxdb
      - mongodb
    networks:
      - mdc_network

  # API服务器
  server-api:
    build:
      context: ./12_server_api
      dockerfile: Dockerfile
    container_name: mdc_server_api
    ports:
      - "9005:9005"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=9005
      - INFLUXDB_URL=http://influxdb:8086
      - MONGODB_URI=*****************************************
    volumes:
      - ./configs/server-api.yml:/app/config.yml
    depends_on:
      - influxdb
      - mongodb
    networks:
      - mdc_network

volumes:
  redis_data:
  nats_data:
  influxdb_data:
  mongodb_data:
  sqlite_data:

networks:
  mdc_network:
    driver: bridge
