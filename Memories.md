# 设备数据采集服务集总览

- 描述: 这是一个设备数据采集服务集合，旨在从设备端采集数据，经过处理后存储到数据库中，并提供API接口供外部查询。
- 架构图:
```
   Device
      |
      V
   Data Acquisition
      |
      V
   Redis
      |
      V
   Data Cleaning/Transformation
      |
      V
   Local SQLite
      |
      V
   NATS JetStream
      |
      V
   InfluxDB -------> MongoDB
```

# 项目结构

- 描述: 项目采用模块化结构，每个模块负责特定的功能。
```
 ├── 00_device_admin/          # 设备管理界面
 ├── 01_device_api/           # 设备API接口
 ├── 02_device_collect/       # 设备数据采集服务
 ├── 02_generate_data/        # 设备数据模拟生成器
 ├── 03_data_cleaning/        # 数据清洗转换服务
 ├── 04_data_push/           # 数据推送服务
 ├── 11_server_jet/          # NATS JetStream消费服务
 ├── 12_server_api/          # API服务器
 ├── shared/                 # 共享组件和数据结构
 ├── configs/                # 配置文件
```

# 共享组件和数据结构

- 描述: 项目使用共享模块 @shared，以避免在各个项目中重复实现 logger, config, 和 models。

# 基础服务

- 描述: 项目依赖的基础服务，通过 docker-compose 进行管理。
- 配置文件: ./docker-service/docker-compose.yml
- 服务列表:
    - Redis
    - NATS
    - InfluxDB
    - MongoDB

# 端口配置

- 描述: 各个服务使用的端口号。
- 基础服务端口:
    - Redis: 6379
    - NATS: 4222
    - InfluxDB: 8086
    - MongoDB: 27017
- 服务端口:
    - **device-collect** (端口 8081): 设备数据采集服务
    - **data-cleaning** (端口 8082): 数据清洗转换服务
    - **data-push** (端口 8083): 数据推送服务
    - **server-jet** (端口 8084): NATS JetStream消费服务
    - **server-api** (端口 9005): API服务器

# 数据流程

- 描述: 数据在各个服务之间的流转过程。
1. **设备** → **device-collect** (HTTP API): 设备通过HTTP API将数据发送到数据采集服务。
2. **device-collect** → **Redis** (临时存储): 数据采集服务将数据临时存储到Redis中。
3. **data-cleaning** ← **Redis** (定时读取): 数据清洗服务定时从Redis中读取数据。
4. **data-cleaning** → **SQLite** (本地持久化): 数据清洗服务将清洗后的数据持久化到本地SQLite数据库中。
5. **data-push** ← **SQLite** (定时读取): 数据推送服务定时从SQLite数据库中读取数据。
6. **data-push** → **NATS JetStream** (消息队列): 数据推送服务将数据推送到NATS JetStream消息队列。
7. **server-jet** ← **NATS JetStream** (消费消息): NATS JetStream消费服务从消息队列中消费数据。
8. **server-jet** → **InfluxDB** + **MongoDB** (存储): NATS JetStream消费服务将数据存储到InfluxDB和MongoDB数据库中。
9. **server-api** ← **InfluxDB** + **MongoDB** (查询): API服务器从InfluxDB和MongoDB数据库中查询数据，并提供API接口。

# 代码注释规范
- 为所有代码添加企业生产级别的详细的中文注释
- 代码需要添加详细清晰的中文注释，函数和结构需要清晰地说明用途，并注明参数意义和使用示例。用户偏好详细的注释标准和代码文档规范。
- 提高代码的可读性和可维护性。

# 测试

- 描述: 用户希望创建模拟数据生成器，用于测试设备数据采集服务，目标 endpoint 为 http://localhost:8081/api/v1/device/data。


# 代码规范
- 善用设计模式, 根据实际情况选用合适算法
- 代码可读性优先
- 代码可维护性优先
- 代码可测试性优先
- 代码可理解性优先
- 代码可扩展性优先
- 代码可复用性优先

