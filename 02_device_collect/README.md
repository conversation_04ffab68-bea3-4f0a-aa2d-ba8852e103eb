# 设备数据采集服务 (Device Collect Service)

## 📋 **项目概述**

设备数据采集服务是一个高性能、高可靠的HTTP服务，用于接收设备发送的数据并临时存储到Redis缓存。该服务具备完善的稳定性保障机制，支持Redis降级和本地缓存，确保数据的FIFO顺序。

## 🏗️ **项目结构**

```
02_device_collect/
├── main.go           (117行) - 主入口和服务启动
├── sequence.go       (101行) - 序列号生成管理
├── cache.go          (172行) - 本地缓存实现
├── stability.go      (183行) - 稳定性管理器
├── service.go        (188行) - 核心服务结构
├── middleware.go     (123行) - HTTP中间件
├── storage.go        (282行) - 数据存储管理
├── handlers.go       (216行) - HTTP处理器
├── config.yml        - 服务配置文件
└── README.md         - 项目说明文档
```

**总计**: 8个Go文件，1382行代码

## 🔧 **模块功能说明**

### **1. main.go** - 主入口模块
- **职责**: 服务启动和生命周期管理
- **功能**: 配置加载、日志初始化、服务启动、优雅关闭
- **特点**: 清晰的启动流程，完善的错误处理

### **2. sequence.go** - 序列号生成模块
- **职责**: 生成有序的唯一标识符
- **功能**: 原子计数器、配置管理、格式化、循环重置
- **特点**: 线程安全、可配置、自动格式选择

### **3. cache.go** - 本地缓存模块
- **职责**: FIFO有序本地缓存实现
- **功能**: 有序存储、快速查找、容量控制、数据清理
- **特点**: 双重数据结构、O(1)查找、严格FIFO

### **4. stability.go** - 稳定性管理模块
- **职责**: 系统稳定性监控和保障
- **功能**: 内存监控、错误统计、熔断机制、状态管理
- **特点**: 实时监控、自动GC、错误率计算

### **5. service.go** - 核心服务模块
- **职责**: 服务结构定义和Redis管理
- **功能**: 服务初始化、Redis连接、健康检查、自动重连
- **特点**: 重试机制、降级处理、后台监控

### **6. middleware.go** - 中间件模块
- **职责**: HTTP中间件和路由配置
- **功能**: Panic恢复、稳定性检查、路由设置
- **特点**: 异常保护、负载控制、熔断保护

### **7. storage.go** - 数据存储模块
- **职责**: 数据存储和查询管理
- **功能**: Redis存储、本地缓存、数据查询、同步机制
- **特点**: 降级存储、FIFO同步、超时控制

### **8. handlers.go** - HTTP处理器模块
- **职责**: HTTP API接口实现
- **功能**: 健康检查、数据接收、数据查询、统计信息
- **特点**: 详细健康信息、数据验证、错误处理