/**
 * 数据存储模块
 *
 * 功能概述：
 * 本模块提供设备数据的存储功能，实现了Redis主存储和本地缓存降级的双重保障机制，
 * 确保在各种网络和系统故障情况下数据不丢失，保证数据采集服务的高可用性
 *
 * 主要功能：
 * - 数据存储：将设备数据存储到Redis缓存中
 * - 降级存储：Redis不可用时自动切换到本地缓存
 * - 数据查询：支持按设备ID查询历史数据
 * - 数据同步：Redis恢复后自动同步本地缓存数据
 * - 顺序保证：严格保证FIFO（先进先出）的数据顺序
 *
 * 技术特性：
 * - 双重保障：Redis + 本地缓存的双重存储机制
 * - 自动降级：Redis故障时无缝切换到本地缓存
 * - 自动恢复：Redis恢复后自动同步本地数据
 * - 顺序保证：使用序列ID确保数据的时间顺序
 * - 性能优化：Pipeline批量操作，减少网络开销
 *
 * 存储策略：
 * - 主存储：Redis，高性能、支持过期、易于扩展
 * - 降级存储：本地缓存，内存存储、快速访问、有限容量
 * - 数据格式：JSON序列化，便于调试和兼容性
 * - 键值设计：时间戳+序列号，确保唯一性和有序性
 *
 * 可靠性保证：
 * - 数据不丢失：降级机制确保数据始终有地方存储
 * - 顺序不乱：FIFO机制确保数据处理顺序正确
 * - 自动恢复：故障恢复后自动同步数据
 * - 错误处理：完善的错误处理和日志记录
 *
 * 业务场景：
 * - 设备数据缓存：临时存储设备数据，等待后续处理
 * - 故障容错：网络故障时的数据保护
 * - 数据查询：历史数据的快速查询
 * - 系统恢复：故障恢复后的数据完整性保证
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"shared/logger"
	"shared/models"
)

/**
 * 存储数据到Redis（支持本地缓存降级）
 *
 * 功能：这是数据存储的核心方法，实现了智能的存储策略和降级机制
 *
 * 存储流程：
 * 1. 序列ID生成：生成唯一的序列标识符，确保数据顺序
 * 2. 键值构造：构造唯一且有序的Redis键
 * 3. 数据序列化：将设备数据序列化为JSON格式
 * 4. 大小检查：验证序列化后的数据大小
 * 5. 存储选择：根据Redis可用性选择存储方式
 * 6. 降级处理：Redis失败时自动切换到本地缓存
 *
 * 键值设计：
 * - 格式：device:{设备ID}:{时间戳毫秒}:{序列ID}
 * - 唯一性：时间戳+序列ID确保全局唯一
 * - 有序性：时间戳保证时间顺序，序列ID保证同时刻顺序
 * - 可读性：包含设备ID，便于调试和查询
 *
 * 降级策略：
 * - 主路径：Redis可用时直接存储到Redis
 * - 降级路径：Redis不可用时存储到本地缓存
 * - 自动切换：Redis故障时自动标记不可用并降级
 * - 错误记录：记录Redis错误统计，用于监控
 *
 * @param {*models.DeviceData} data - 要存储的设备数据对象
 * @returns {error} 存储失败时返回错误信息
 */
func (s *DeviceCollectService) storeToRedisWithFallback(data *models.DeviceData) error {
	// 生成序列标识符，确保先来后到的顺序
	sequentialID := generateSequentialID()

	// 生成唯一且有序的Redis key
	timestampMs := data.Timestamp.UnixMilli()
	key := fmt.Sprintf("device:%s:%d:%s", data.DeviceID, timestampMs, sequentialID)

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	// 检查序列化后的数据大小
	if len(jsonData) > 50*1024 { // 50KB限制
		return fmt.Errorf("serialized data too large: %d bytes", len(jsonData))
	}

	// 检查Redis是否可用
	if s.stabilityManager.isRedisAvailable() {
		// 尝试存储到Redis
		err := s.storeToRedisDirectly(key, jsonData, data.DeviceID)
		if err != nil {
			logger.Warnf("Redis storage failed, falling back to local cache: %v", err)
			s.stabilityManager.recordRedisError()
			s.stabilityManager.setRedisAvailable(false)

			// 降级到本地缓存
			return s.storeToLocalCache(key, jsonData)
		}
		return nil
	} else {
		// Redis不可用，直接使用本地缓存
		logger.Debugf("Redis unavailable, storing to local cache: %s", key)
		return s.storeToLocalCache(key, jsonData)
	}
}

// storeToRedisDirectly 直接存储到Redis
// 使用Pipeline批量操作，提高性能
// 参数:
//   - key: Redis键
//   - jsonData: 序列化后的数据
//   - deviceID: 设备ID
//
// 返回:
//   - error: 存储失败时返回错误
func (s *DeviceCollectService) storeToRedisDirectly(key string, jsonData []byte, deviceID string) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)
	defer cancel()

	// 使用Pipeline批量执行Redis操作
	pipe := s.redisClient.Pipeline()

	// 存储到Redis，使用配置中的过期时间
	pipe.Set(ctx, key, jsonData, s.config.Redis.DataExpiration)

	// 添加到设备数据列表
	listKey := fmt.Sprintf("device_list:%s", deviceID)
	pipe.LPush(ctx, listKey, key)

	// 设置列表过期时间
	pipe.Expire(ctx, listKey, s.config.Redis.DataExpiration)

	// 执行Pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute Redis pipeline: %w", err)
	}

	logger.Debugf("Stored data to Redis with key %s", key)
	return nil
}

// storeToLocalCache 存储到本地缓存
// 当Redis不可用时的降级方案
// 参数:
//   - key: 缓存键
//   - jsonData: 序列化后的数据
//
// 返回:
//   - error: 存储失败时返回错误
func (s *DeviceCollectService) storeToLocalCache(key string, jsonData []byte) error {
	s.localCache.Set(key, jsonData)
	logger.Debugf("Stored data to local cache with key %s", key)
	return nil
}

// getDataWithFallback 获取数据，支持本地缓存降级
// 优先从Redis获取，失败时从本地缓存获取
// 参数:
//   - deviceID: 设备ID
//
// 返回:
//   - []models.DeviceData: 设备数据列表
//   - error: 获取失败时返回错误
func (s *DeviceCollectService) getDataWithFallback(deviceID string) ([]models.DeviceData, error) {
	// 检查Redis是否可用
	if s.stabilityManager.isRedisAvailable() {
		// 尝试从Redis获取
		data, err := s.getDataFromRedis(deviceID)
		if err != nil {
			logger.Warnf("Failed to get data from Redis, trying local cache: %v", err)
			s.stabilityManager.recordRedisError()

			// 降级到本地缓存
			return s.getDataFromLocalCache(deviceID)
		}
		return data, nil
	} else {
		// Redis不可用，直接从本地缓存获取
		return s.getDataFromLocalCache(deviceID)
	}
}

// getDataFromRedis 从Redis获取数据
// 参数:
//   - deviceID: 设备ID
//
// 返回:
//   - []models.DeviceData: 设备数据列表
//   - error: 获取失败时返回错误
func (s *DeviceCollectService) getDataFromRedis(deviceID string) ([]models.DeviceData, error) {
	// 从Redis获取设备数据列表
	listKey := fmt.Sprintf("device_list:%s", deviceID)

	ctx, cancel := context.WithTimeout(s.ctx, 3*time.Second)
	defer cancel()

	keys, err := s.redisClient.LRange(ctx, listKey, 0, 999).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get device list from Redis: %w", err)
	}

	var dataList []models.DeviceData
	for _, key := range keys {
		ctx, cancel := context.WithTimeout(s.ctx, 1*time.Second)
		jsonData, err := s.redisClient.Get(ctx, key).Result()
		cancel()

		if err != nil {
			logger.Debugf("Failed to get data for key %s: %v", key, err)
			continue
		}

		var data models.DeviceData
		if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
			logger.Debugf("Failed to unmarshal data for key %s: %v", key, err)
			continue
		}

		dataList = append(dataList, data)
	}

	return dataList, nil
}

// getDataFromLocalCache 从本地缓存获取数据
// 按FIFO顺序返回数据，保持时间顺序
// 参数:
//   - deviceID: 设备ID
//
// 返回:
//   - []models.DeviceData: 设备数据列表
//   - error: 获取失败时返回错误
func (s *DeviceCollectService) getDataFromLocalCache(deviceID string) ([]models.DeviceData, error) {
	var dataList []models.DeviceData

	// 获取有序的缓存项，确保FIFO顺序
	orderedItems := s.localCache.GetOrderedItems()

	// 按FIFO顺序遍历，查找匹配的设备数据
	for _, item := range orderedItems {
		// 检查key是否属于指定设备
		// key格式: device:{设备ID}:{时间戳}:{序列号}
		if len(item.Key) > len(deviceID) && item.Key[7:7+len(deviceID)] == deviceID {
			var data models.DeviceData
			if err := json.Unmarshal(item.Data, &data); err != nil {
				logger.Debugf("Failed to unmarshal cached data for key %s: %v", item.Key, err)
				continue
			}
			dataList = append(dataList, data)
		}
	}

	logger.Debugf("Retrieved %d data items from local cache for device %s (FIFO order)",
		len(dataList), deviceID)
	return dataList, nil
}

// syncLocalCacheToRedis 同步本地缓存数据到Redis
// 当Redis恢复时，将本地缓存中的数据按FIFO顺序同步到Redis
func (s *DeviceCollectService) syncLocalCacheToRedis() {
	if s.localCache.Size() == 0 {
		return
	}

	logger.Infof("Syncing %d items from local cache to Redis (FIFO order)", s.localCache.Size())

	// 获取有序的缓存项，确保FIFO顺序
	orderedItems := s.localCache.GetOrderedItems()
	syncCount := 0
	errorCount := 0

	// 按FIFO顺序同步数据，同时重建设备列表
	deviceLists := make(map[string][]string) // 设备ID -> key列表

	for i, item := range orderedItems {
		ctx, cancel := context.WithTimeout(s.ctx, 3*time.Second)

		// 尝试存储到Redis
		err := s.redisClient.Set(ctx, item.Key, item.Data, s.config.Redis.DataExpiration).Err()
		cancel()

		if err != nil {
			logger.Warnf("Failed to sync item %d/%d (key: %s) to Redis: %v",
				i+1, len(orderedItems), item.Key, err)
			errorCount++
		} else {
			logger.Debugf("Synced item %d/%d (key: %s) to Redis",
				i+1, len(orderedItems), item.Key)
			syncCount++

			// 提取设备ID，用于重建设备列表
			// key格式: device:{设备ID}:{时间戳}:{序列号}
			parts := strings.Split(item.Key, ":")
			if len(parts) >= 2 {
				deviceID := parts[1]
				deviceLists[deviceID] = append(deviceLists[deviceID], item.Key)
			}
		}
	}

	// 重建设备列表
	for deviceID, keys := range deviceLists {
		listKey := fmt.Sprintf("device_list:%s", deviceID)
		ctx, cancel := context.WithTimeout(s.ctx, 3*time.Second)

		// 清空现有列表
		s.redisClient.Del(ctx, listKey)

		// 按FIFO顺序重建列表（从后往前添加，因为LPush是头插）
		for i := len(keys) - 1; i >= 0; i-- {
			s.redisClient.LPush(ctx, listKey, keys[i])
		}

		// 设置列表过期时间
		s.redisClient.Expire(ctx, listKey, s.config.Redis.DataExpiration)
		cancel()

		logger.Debugf("Rebuilt device list for %s with %d items", deviceID, len(keys))
	}

	logger.Infof("Local cache sync completed: %d success, %d errors (FIFO order maintained)",
		syncCount, errorCount)

	// 清空本地缓存
	if errorCount == 0 {
		s.localCache.Clear()
		logger.Info("Local cache cleared after successful sync")
	} else {
		logger.Warnf("Local cache not cleared due to %d sync errors", errorCount)
	}
}
