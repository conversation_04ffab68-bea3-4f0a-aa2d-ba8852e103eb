/**
 * 核心服务模块
 *
 * 功能概述：
 * 本模块定义了设备数据采集服务的核心结构体和Redis连接管理功能，
 * 是整个服务的业务逻辑核心，负责协调各个组件完成数据采集任务
 *
 * 主要功能：
 * - 服务实例管理：创建和管理设备数据采集服务实例
 * - Redis连接管理：建立、维护和监控Redis连接状态
 * - 降级处理：Redis不可用时自动切换到本地缓存模式
 * - 健康检查：定期检查Redis连接状态和服务健康度
 * - 自动恢复：Redis恢复后自动同步本地缓存数据
 *
 * 技术特性：
 * - 连接池管理：高效的Redis连接池配置和管理
 * - 重试机制：智能的连接重试和错误恢复策略
 * - 超时控制：完善的超时控制，防止连接阻塞
 * - 状态监控：实时监控Redis连接状态和错误统计
 * - 优雅降级：服务不中断的降级和恢复机制
 *
 * 架构设计：
 * - 依赖注入：通过构造函数注入配置和依赖组件
 * - 状态管理：集中管理Redis连接状态和服务状态
 * - 异步处理：使用goroutine处理健康检查和重连
 * - 资源管理：正确的资源创建、使用和释放
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"

	"shared/config"
	"shared/logger"
)

/**
 * 设备数据采集服务结构体
 *
 * 功能：作为设备数据采集服务的核心控制器，封装所有业务逻辑和依赖组件
 *
 * 架构设计：
 * - 单一职责：专注于设备数据的接收、验证和临时存储
 * - 依赖管理：集中管理所有外部依赖和配置
 * - 状态协调：协调Redis、本地缓存和稳定性管理器的状态
 * - 生命周期管理：管理服务的启动、运行和关闭过程
 *
 * 核心职责：
 * - 数据接收：处理来自设备的HTTP请求
 * - 数据验证：验证数据格式和完整性
 * - 存储管理：协调Redis和本地缓存的数据存储
 * - 状态监控：监控服务运行状态和性能指标
 * - 错误处理：处理各种异常情况和降级场景
 *
 * @struct DeviceCollectService
 */
type DeviceCollectService struct {
	/** 服务配置信息，包含端口、Redis连接、日志等所有配置参数 */
	config *config.Config

	/** Redis客户端实例，用于临时存储设备数据，支持连接池和重试 */
	redisClient *redis.Client

	/** 全局上下文对象，用于控制goroutine生命周期和请求超时 */
	ctx context.Context

	/** 稳定性管理器，负责内存监控、熔断机制和服务状态管理 */
	stabilityManager *StabilityManager

	/** 本地缓存实例，Redis不可用时的降级存储方案 */
	localCache *LocalCache

	/** 设备监控器实例，负责设备离线检测和状态管理 */
	deviceMonitor *DeviceMonitor

	/** 设备数据日志器实例，负责记录设备推送的原始数据 */
	deviceLogger *DeviceLogger
}

// NewDeviceCollectService 创建设备数据采集服务实例
// 参数:
//   - cfg: 服务配置
//   - stabilityManager: 稳定性管理器
//
// 返回:
//   - *DeviceCollectService: 服务实例
func NewDeviceCollectService(cfg *config.Config, stabilityManager *StabilityManager) *DeviceCollectService {
	// 创建设备数据日志器
	deviceLogger := NewDeviceLogger(cfg)

	return &DeviceCollectService{
		config:           cfg,
		ctx:              context.Background(),
		stabilityManager: stabilityManager,
		localCache:       NewLocalCache(1000), // 本地缓存，最大1000条数据
		deviceLogger:     deviceLogger,
	}
}

// initRedis 初始化Redis连接
// 创建Redis客户端并测试连接，支持重试机制和降级处理
// 如果Redis连接失败，将启用本地缓存作为降级方案
// 返回:
//   - error: 连接失败时返回错误，但服务仍可继续运行
//
// 使用示例:
//
//	err := service.initRedis()
//	if err != nil {
//	    logger.Warn("Redis连接失败，启用本地缓存模式")
//	}
func (s *DeviceCollectService) initRedis() error {
	// 创建Redis客户端实例，增加超时和重试配置
	s.redisClient = redis.NewClient(&redis.Options{
		Addr:         s.config.GetRedisAddr(), // Redis服务器地址，格式："host:port"
		Password:     s.config.Redis.Password, // Redis认证密码，空字符串表示无密码
		DB:           s.config.Redis.DB,       // Redis数据库编号，0-15
		PoolSize:     s.config.Redis.PoolSize, // 连接池大小，控制最大并发连接数
		DialTimeout:  5 * time.Second,         // 连接超时
		ReadTimeout:  3 * time.Second,         // 读取超时
		WriteTimeout: 3 * time.Second,         // 写入超时
		MaxRetries:   3,                       // 最大重试次数
	})

	// 重试连接Redis，最多尝试3次
	var lastErr error
	for i := 0; i < 3; i++ {
		// 创建带超时的上下文
		ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)

		// 发送PING命令测试Redis连接
		_, err := s.redisClient.Ping(ctx).Result()
		cancel()

		if err == nil {
			// 连接成功
			s.stabilityManager.setRedisAvailable(true)
			logger.Info("Connected to Redis successfully")

			// 初始化设备监控器
			s.deviceMonitor = NewDeviceMonitor(s.config, s.redisClient, s)
			s.deviceMonitor.Start()

			// 启动Redis健康检查协程
			go s.startRedisHealthCheck()
			return nil
		}

		lastErr = err
		logger.Warnf("Redis connection attempt %d failed: %v", i+1, err)

		// 等待后重试
		if i < 2 {
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	// 所有重试都失败，启用降级模式
	s.stabilityManager.setRedisAvailable(false)
	logger.Errorf("Failed to connect to Redis after 3 attempts, enabling local cache mode: %v", lastErr)

	// 启动Redis重连协程
	go s.startRedisReconnect()

	// 返回错误但不阻止服务启动
	return fmt.Errorf("Redis connection failed, running in degraded mode: %w", lastErr)
}

// startRedisHealthCheck 启动Redis健康检查协程
// 定期检查Redis连接状态，发现异常时自动切换到本地缓存
func (s *DeviceCollectService) startRedisHealthCheck() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.checkRedisHealth()
		case <-s.ctx.Done():
			logger.Info("Redis health check stopped")
			return
		}
	}
}

// checkRedisHealth 检查Redis健康状态
// 发送PING命令测试连接，记录错误并更新状态
func (s *DeviceCollectService) checkRedisHealth() {
	ctx, cancel := context.WithTimeout(s.ctx, 3*time.Second)
	defer cancel()

	_, err := s.redisClient.Ping(ctx).Result()
	if err != nil {
		logger.Warnf("Redis health check failed: %v", err)
		s.stabilityManager.recordRedisError()
		s.stabilityManager.setRedisAvailable(false)
	} else {
		// Redis恢复正常，检查是否需要同步本地缓存数据
		if !s.stabilityManager.isRedisAvailable() {
			logger.Info("Redis health check passed, attempting to sync local cache")
			s.syncLocalCacheToRedis()
		}
		s.stabilityManager.setRedisAvailable(true)
	}
}

// startRedisReconnect 启动Redis重连协程
// 当Redis初始连接失败时，定期尝试重新连接
func (s *DeviceCollectService) startRedisReconnect() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒尝试重连
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !s.stabilityManager.isRedisAvailable() {
				s.attemptRedisReconnect()
			} else {
				// Redis已恢复，停止重连协程
				logger.Info("Redis reconnect stopped - connection restored")
				return
			}
		case <-s.ctx.Done():
			logger.Info("Redis reconnect stopped")
			return
		}
	}
}

// attemptRedisReconnect 尝试重新连接Redis
// 测试连接并更新状态，成功时同步本地缓存数据
func (s *DeviceCollectService) attemptRedisReconnect() {
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)
	defer cancel()

	_, err := s.redisClient.Ping(ctx).Result()
	if err != nil {
		logger.Debugf("Redis reconnect attempt failed: %v", err)
		s.stabilityManager.recordRedisError()
	} else {
		logger.Info("Redis reconnected successfully")
		s.stabilityManager.setRedisAvailable(true)

		// 初始化设备监控器（如果还没有初始化）
		if s.deviceMonitor == nil {
			s.deviceMonitor = NewDeviceMonitor(s.config, s.redisClient, s)
			s.deviceMonitor.Start()
		}

		// 同步本地缓存数据到Redis
		s.syncLocalCacheToRedis()

		// 启动健康检查
		go s.startRedisHealthCheck()
	}
}
