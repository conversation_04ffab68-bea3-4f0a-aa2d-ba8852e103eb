# 构建阶段
FROM golang:1.21-alpine AS builder

WORKDIR /app

# 复制go mod文件和shared模块
COPY 02_device_collect/go.mod 02_device_collect/go.sum ./
COPY shared ./shared
# 修改go.mod中的shared路径
RUN sed -i 's|replace shared => ../shared|replace shared => ./shared|' go.mod
RUN go mod download

# 复制源代码
COPY 02_device_collect/ .
# 再次确保go.mod中的shared路径正确
RUN sed -i 's|replace shared => ../shared|replace shared => ./shared|' go.mod

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段
FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .

# 创建数据目录
RUN mkdir -p /data/sqlite

# 暴露端口
EXPOSE 8081

# 运行应用
CMD ["./main"]
