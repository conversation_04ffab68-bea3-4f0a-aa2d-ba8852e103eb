/**
 * HTTP处理器模块
 *
 * 功能概述：
 * 本模块提供设备数据采集服务的HTTP API处理器，是服务与外部系统交互的接口层，
 * 负责处理来自设备的数据上报、系统健康检查、数据查询等核心业务功能
 *
 * 主要功能：
 * - 数据接收：处理设备通过HTTP POST发送的实时数据
 * - 健康检查：提供详细的服务健康状态和系统指标
 * - 数据查询：支持按设备ID查询历史数据
 * - 统计信息：提供系统运行统计和性能指标
 * - 错误处理：统一的错误处理和响应格式
 *
 * API设计特性：
 * - RESTful设计：遵循REST架构风格的API设计
 * - 统一响应格式：标准化的JSON响应结构
 * - 参数验证：完整的请求参数验证和错误处理
 * - 性能监控：请求计数、错误统计、响应时间监控
 * - 降级支持：Redis不可用时的本地缓存降级
 *
 * 安全特性：
 * - 数据验证：严格的数据格式和大小验证
 * - 防护机制：防止内存攻击和恶意请求
 * - 错误隔离：panic恢复机制，防止单个请求影响整个服务
 * - 日志记录：详细的请求日志和错误追踪
 *
 * 性能优化：
 * - 快速响应：优化的数据处理流程
 * - 内存控制：数据大小限制，防止内存溢出
 * - 并发处理：支持高并发的数据接收
 * - 缓存策略：智能的缓存使用和降级机制
 *
 * 业务场景：
 * - 设备数据上报：工业设备的实时数据采集
 * - 系统监控：服务健康状态的实时监控
 * - 数据查询：历史数据的查询和分析
 * - 运维管理：系统统计信息的获取和分析
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"fmt"
	"net/http"
	"runtime"
	"sync/atomic"
	"time"

	"github.com/gin-gonic/gin"

	"shared/logger"
	"shared/models"
)

/**
 * 健康检查API处理器
 *
 * 功能：提供服务的健康状态检查和详细的系统运行指标
 *
 * 检查项目：
 * - 服务状态：正常运行、降级模式、熔断状态
 * - 内存使用：堆内存、总内存、GC统计
 * - 系统资源：goroutine数量、系统负载
 * - 错误统计：请求错误率、Redis错误率
 * - 存储状态：Redis可用性、本地缓存状态
 *
 * 响应格式：
 * - 200 OK：服务正常运行
 * - JSON格式：包含详细的健康状态信息
 *
 * 业务用途：
 * - 负载均衡器健康检查
 * - 监控系统状态收集
 * - 运维人员故障诊断
 * - 自动化部署健康验证
 *
 * @route GET /health
 * @param {gin.Context} c - Gin上下文对象
 * @returns {JSON} 健康状态和系统指标
 *
 * @example
 * GET /health
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "service_name": "device-collect",
 *     "status": "healthy",
 *     "memory_usage_mb": 45,
 *     "redis_available": true,
 *     "error_rate": "0.12%"
 *   }
 * }
 */
func (s *DeviceCollectService) healthCheck(c *gin.Context) {
	// 获取内存统计信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 检查服务状态
	status := "healthy"
	if s.stabilityManager.isCircuitBreakerOpen() {
		status = "degraded"
	}

	// 构建健康检查响应
	healthData := map[string]interface{}{
		"service_name":        s.config.Service.Name,
		"status":              status,
		"timestamp":           time.Now(),
		"memory_usage_mb":     m.Alloc / 1024 / 1024,
		"heap_usage_mb":       m.HeapAlloc / 1024 / 1024,
		"gc_cycles":           m.NumGC,
		"goroutines":          runtime.NumGoroutine(),
		"request_count":       atomic.LoadUint64(&s.stabilityManager.requestCount),
		"error_count":         atomic.LoadUint64(&s.stabilityManager.errorCount),
		"error_rate":          fmt.Sprintf("%.2f%%", s.stabilityManager.getErrorRate()),
		"circuit_breaker":     s.stabilityManager.isCircuitBreakerOpen(),
		"redis_available":     s.stabilityManager.isRedisAvailable(),
		"redis_error_count":   atomic.LoadUint64(&s.stabilityManager.redisErrorCount),
		"redis_error_rate":    fmt.Sprintf("%.2f%%", s.stabilityManager.getRedisErrorRate()),
		"local_cache_enabled": s.stabilityManager.isLocalCacheEnabled(),
		"local_cache_size":    s.localCache.Size(),
	}

	// 添加设备监控统计信息
	if s.deviceMonitor != nil {
		healthData["device_monitor"] = s.deviceMonitor.GetStats()
	}

	// 添加设备日志统计信息
	if s.deviceLogger != nil {
		healthData["device_logger"] = s.deviceLogger.GetStats()
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    healthData,
	})
}

/**
 * 设备数据接收API处理器
 *
 * 功能：接收来自工业设备的实时数据，进行验证、处理和存储
 *
 * 处理流程：
 * 1. 数据解析：解析HTTP请求体中的JSON数据
 * 2. 数据验证：验证必要字段和数据格式
 * 3. 安全检查：检查数据大小，防止内存攻击
 * 4. 时间戳处理：设置或验证数据时间戳
 * 5. 数据存储：存储到Redis或本地缓存（降级）
 * 6. 响应返回：返回处理结果给设备
 *
 * 验证规则：
 * - device_id：必填字段，设备唯一标识
 * - 数据大小：限制100KB，防止内存攻击
 * - JSON格式：严格的JSON格式验证
 * - 时间戳：自动补充缺失的时间戳
 *
 * 错误处理：
 * - 400 Bad Request：数据格式错误或验证失败
 * - 500 Internal Server Error：存储失败或系统错误
 * - panic恢复：防止单个请求导致服务崩溃
 *
 * 性能特性：
 * - 高并发：支持大量设备同时发送数据
 * - 快速响应：优化的数据处理流程
 * - 降级支持：Redis不可用时使用本地缓存
 * - 错误统计：记录错误率和性能指标
 *
 * @route POST /api/device/data
 * @param {gin.Context} c - Gin上下文对象
 * @body {models.DeviceData} 设备数据对象
 * @returns {JSON} 处理结果响应
 *
 * @example
 * POST /api/device/data
 * {
 *   "device_id": "FANUC_001",
 *   "timestamp": "2025-06-05T10:30:00Z",
 *   "raw_data": {
 *     "status": "production",
 *     "program": "PART001.NC",
 *     "production": 150
 *   }
 * }
 */
func (s *DeviceCollectService) receiveDeviceData(c *gin.Context) {
	// 添加额外的panic保护
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("Panic in receiveDeviceData: %v", r)
			s.stabilityManager.recordError()
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Message: "Internal server error",
				Error:   "Data processing failed",
			})
		}
	}()

	// 创建设备数据结构体实例
	var deviceData models.DeviceData

	// 解析HTTP请求体中的JSON数据到结构体
	// ShouldBindJSON会自动验证JSON格式和必要字段
	if err := c.ShouldBindJSON(&deviceData); err != nil {
		// JSON解析失败，记录错误并返回400错误
		s.stabilityManager.recordError()
		logger.Warnf("Invalid request data from %s: %v", c.ClientIP(), err)
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// 数据验证：检查必要字段
	if deviceData.DeviceID == "" {
		s.stabilityManager.recordError()
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Device ID is required",
			Error:   "Missing device_id field",
		})
		return
	}

	// 数据验证：检查数据大小，防止内存攻击
	if len(fmt.Sprintf("%v", deviceData.RawData)) > 102400 { // 100KB限制
		s.stabilityManager.recordError()
		logger.Warnf("Data too large from device %s: %d bytes", deviceData.DeviceID, len(fmt.Sprintf("%v", deviceData.RawData)))
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Data too large",
			Error:   "Raw data exceeds 10KB limit",
		})
		return
	}

	// 设置接收时间戳
	// 如果客户端没有提供时间戳，使用服务器当前UTC时间（修复时区问题）
	if deviceData.Timestamp.IsZero() {
		deviceData.Timestamp = time.Now().UTC()
	}

	fmt.Printf("%v\r\n", deviceData)

	// 记录设备数据到日志文件（如果启用）
	if s.deviceLogger != nil {
		s.deviceLogger.LogDeviceData(&deviceData)
	}

	// 更新设备最后活跃时间（用于离线检测）
	if s.deviceMonitor != nil {
		if err := s.deviceMonitor.UpdateDeviceLastSeen(deviceData.DeviceID); err != nil {
			logger.Warnf("Failed to update device last seen time for %s: %v", deviceData.DeviceID, err)
			// 不影响主流程，继续处理数据
		}
	}

	// 将数据存储到Redis缓存，支持本地缓存降级
	// Redis作为临时存储，等待数据清洗服务处理
	if err := s.storeToRedisWithFallback(&deviceData); err != nil {
		// 存储失败，记录错误统计和日志，返回500错误
		s.stabilityManager.recordError()
		logger.Errorf("Failed to store data for device %s: %v", deviceData.DeviceID, err)
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to store data",
			Error:   err.Error(),
		})
		return
	}

	// 数据接收成功，返回成功响应
	logger.Debugf("Successfully received data from device %s", deviceData.DeviceID)
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Data received successfully",
	})
}

// getDeviceData 获取设备数据处理器
// 根据设备ID查询设备的历史数据
// 支持从Redis或本地缓存查询，具有降级能力
func (s *DeviceCollectService) getDeviceData(c *gin.Context) {
	deviceID := c.Param("device_id")

	if deviceID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Device ID is required",
		})
		return
	}

	// 从Redis或本地缓存获取设备数据，支持降级
	dataList, err := s.getDataWithFallback(deviceID)
	if err != nil {
		logger.Errorf("Failed to get device data for %s: %v", deviceID, err)
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get device data",
			Error:   err.Error(),
		})
		return
	}

	logger.Debugf("Retrieved %d data items for device %s", len(dataList), deviceID)

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    dataList,
	})
}

// getStats 获取统计信息处理器
// 返回系统的统计信息，包括请求数、错误数、设备数等
func (s *DeviceCollectService) getStats(c *gin.Context) {
	// 获取内存统计信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 构建统计信息
	stats := map[string]interface{}{
		"service_info": map[string]interface{}{
			"name":    s.config.Service.Name,
			"version": "1.0.0",
			"uptime":  time.Since(time.Now().Add(-time.Hour)), // 简化的运行时间
		},
		"request_stats": map[string]interface{}{
			"total_requests":   atomic.LoadUint64(&s.stabilityManager.requestCount),
			"total_errors":     atomic.LoadUint64(&s.stabilityManager.errorCount),
			"error_rate":       fmt.Sprintf("%.2f%%", s.stabilityManager.getErrorRate()),
			"redis_errors":     atomic.LoadUint64(&s.stabilityManager.redisErrorCount),
			"redis_error_rate": fmt.Sprintf("%.2f%%", s.stabilityManager.getRedisErrorRate()),
		},
		"system_stats": map[string]interface{}{
			"memory_usage_mb": m.Alloc / 1024 / 1024,
			"heap_usage_mb":   m.HeapAlloc / 1024 / 1024,
			"gc_cycles":       m.NumGC,
			"goroutines":      runtime.NumGoroutine(),
			"circuit_breaker": s.stabilityManager.isCircuitBreakerOpen(),
		},
		"storage_stats": map[string]interface{}{
			"redis_available":     s.stabilityManager.isRedisAvailable(),
			"local_cache_enabled": s.stabilityManager.isLocalCacheEnabled(),
			"local_cache_size":    s.localCache.Size(),
		},
	}

	// 添加设备监控统计信息
	if s.deviceMonitor != nil {
		stats["device_monitor"] = s.deviceMonitor.GetStats()
	}

	// 添加设备日志统计信息
	if s.deviceLogger != nil {
		stats["device_logger"] = s.deviceLogger.GetStats()
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    stats,
	})
}
