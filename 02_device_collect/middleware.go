/**
 * HTTP中间件模块
 *
 * 功能概述：
 * 本模块提供设备数据采集服务的HTTP中间件功能，包括panic恢复、稳定性检查、
 * 路由配置等核心功能，确保HTTP服务的稳定性、安全性和可观测性
 *
 * 主要功能：
 * - Panic恢复：捕获和处理所有panic异常，防止服务崩溃
 * - 稳定性检查：内存监控和熔断器保护，确保服务稳定
 * - 路由配置：统一的API路由配置和版本管理
 * - 错误处理：标准化的错误响应和日志记录
 * - 性能监控：请求统计和性能指标收集
 *
 * 中间件特性：
 * - 链式处理：支持中间件的链式调用
 * - 错误隔离：单个请求的错误不影响整个服务
 * - 自动恢复：panic后的自动恢复和资源清理
 * - 负载保护：高负载时的请求拒绝机制
 * - 统一响应：标准化的API响应格式
 *
 * 安全特性：
 * - 异常隔离：防止单个请求的异常影响其他请求
 * - 资源保护：内存过载时的自动保护机制
 * - 错误掩码：对外隐藏内部错误细节
 * - 日志记录：完整的错误日志和调用链追踪
 *
 * 性能优化：
 * - 轻量级：最小化中间件的性能开销
 * - 快速失败：熔断器快速拒绝请求
 * - 内存管理：panic后的强制GC清理
 * - 统计优化：原子操作的性能统计
 *
 * 业务价值：
 * - 服务稳定性：防止单点故障导致的服务中断
 * - 运维友好：详细的错误日志和监控指标
 * - 用户体验：友好的错误响应和快速失败
 * - 系统保护：过载保护和资源管理
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"net/http"
	"runtime"

	"github.com/gin-gonic/gin"

	"shared/logger"
	"shared/models"
)

/**
 * Panic恢复中间件
 *
 * 功能：捕获和处理所有panic异常，防止单个请求的错误导致整个服务崩溃
 *
 * 恢复机制：
 * 1. 异常捕获：使用defer+recover捕获所有panic
 * 2. 错误记录：详细记录panic信息到日志
 * 3. 统计更新：更新错误统计计数器
 * 4. 内存清理：强制触发GC，释放可能的内存泄漏
 * 5. 响应返回：返回标准化的500错误响应
 * 6. 请求中断：调用c.Abort()停止后续处理
 *
 * 安全特性：
 * - 异常隔离：单个请求的panic不影响其他请求
 * - 信息保护：对外隐藏内部错误细节
 * - 资源清理：panic后的内存清理和资源释放
 * - 服务连续性：确保服务在异常后继续运行
 *
 * @returns {gin.HandlerFunc} Gin中间件函数
 */
func (s *DeviceCollectService) panicRecoveryMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic错误
				logger.Errorf("Panic recovered: %v", err)

				// 记录错误统计
				s.stabilityManager.recordError()

				// 强制GC，释放可能的内存泄漏
				runtime.GC()

				// 返回500错误
				c.JSON(http.StatusInternalServerError, models.APIResponse{
					Success: false,
					Message: "Internal server error",
					Error:   "Service temporarily unavailable",
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// stabilityMiddleware 稳定性检查中间件
// 检查内存使用和熔断器状态，必要时拒绝请求
// 功能:
//   - 记录请求统计信息
//   - 检查内存使用情况
//   - 检查熔断器状态
//   - 高负载时拒绝新请求
//   - 保护系统稳定性
//
// 返回:
//   - gin.HandlerFunc: Gin中间件函数
func (s *DeviceCollectService) stabilityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求统计
		s.stabilityManager.recordRequest()

		// 检查内存使用情况
		s.stabilityManager.checkMemoryUsage()

		// 检查熔断器状态
		if s.stabilityManager.isCircuitBreakerOpen() {
			logger.Warnf("Circuit breaker is open, rejecting request from %s", c.ClientIP())
			c.JSON(http.StatusServiceUnavailable, models.APIResponse{
				Success: false,
				Message: "Service temporarily unavailable due to high load",
				Error:   "Circuit breaker is open",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// setupRoutes 设置路由
// 配置HTTP路由和中间件，定义API接口
// 功能:
//   - 配置Gin运行模式
//   - 添加稳定性中间件
//   - 定义健康检查接口
//   - 定义设备数据API接口
//
// 返回:
//   - *gin.Engine: 配置好的Gin路由引擎
func (s *DeviceCollectService) setupRoutes() *gin.Engine {
	// 根据配置设置Gin运行模式
	if !s.config.Service.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// 添加稳定性中间件
	router.Use(s.panicRecoveryMiddleware())
	router.Use(s.stabilityMiddleware())

	// 健康检查接口
	router.GET("/health", s.healthCheck)

	// 设备数据接收API组
	api := router.Group("/api/v1")
	{
		// 设备数据接收接口
		api.POST("/device/data", s.receiveDeviceData)

		// 设备数据查询接口
		api.GET("/device/data/:device_id", s.getDeviceData)

		// 统计信息查询接口
		api.GET("/stats", s.getStats)
	}

	return router
}
