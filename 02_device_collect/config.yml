service:
  name: "device-collect"
  host: "0.0.0.0"
  port: 9001
  debug: true
  timeout: 30

redis:
  host: "redis"
  port: 6379
  password: ""
  db: 2
  pool_size: 10
  data_expiration: 168h  # 7天过期时间 (168小时)
  max_sequence_value: 16777215  # 序列计数器最大值，默认16777215 (6位十六进制最大值)

sqlite:
  data_dir: "/data/sqlite"
  retention_days: 7
  batch_size: 1000
  flush_interval: 5

nats:
  url: "nats://nats:4222"
  subject: "device.data"
  stream: "DEVICE_STREAM"
  consumer: "device-collect-consumer"
  batch_size: 100

influxdb:
  url: "http://influxdb:8086"
  token: "your-token"
  org: "your-org"
  bucket: "device-data"

mongodb:
  uri: "mongodb://mongodb:27017"
  database: "device_db"
  collection: "device_data"

logging:
  level: "info"
  format: "json"
  output: "logs/device-collect.log"  # 输出到日志文件
  rotation:
    enabled: true       # 启用日志轮转
    max_size: 50        # 单个文件最大50MB
    max_age: 7          # 保留7天
    max_backups: 5      # 保留5个备份文件
    compress: true      # 压缩旧文件
  device_logs: true    # 是否记录设备数据日志，true时记录每个设备推送的数据到单独的日志文件

# 稳定性管理配置
# 用于配置内存监控和熔断机制，根据实际部署环境调整
stability:
  max_memory_mb: 512    # 最大内存限制(MB)，超过此值触发熔断器保护
  gc_threshold_mb: 256  # GC触发阈值(MB)，达到此值主动触发垃圾回收

# 设备离线检测配置
# 用于监控设备连接状态，自动检测设备离线并发送离线通知
device_monitor:
  enabled: true                    # 是否启用设备离线检测功能
  check_interval: 3               # 检查间隔(秒)，每隔多少秒检查一次设备状态
  offline_timeout: 60             # 离线超时(秒)，超过多少秒没有数据则判定为离线
  redis_key_prefix: "device:last_seen:"  # Redis中存储设备最后活跃时间的key前缀
  cleanup_interval: 3600           # 清理间隔(秒)，每隔多少秒清理过期的设备状态记录
  max_offline_devices: 1000        # 最大离线设备数量，防止内存泄露
