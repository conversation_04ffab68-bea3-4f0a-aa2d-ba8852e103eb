/**
 * 设备离线监控模块
 *
 * 功能概述：
 * 本模块负责监控设备的连接状态，自动检测设备离线并发送离线通知。
 * 通过Redis记录设备最后活跃时间，定期检查设备状态，实现设备离线检测功能。
 *
 * 主要功能：
 * - 设备状态跟踪：记录每个设备最后接收数据的时间戳
 * - 离线检测：定期检查设备是否超时未发送数据
 * - 离线通知：自动发送设备离线状态数据
 * - 状态管理：管理设备上线/离线状态转换
 * - 资源清理：定期清理过期的设备状态记录
 *
 * 技术特性：
 * - Redis存储：使用Redis存储设备最后活跃时间，支持分布式部署
 * - 定时检查：使用ticker定期检查设备状态
 * - 并发安全：使用sync.RWMutex保护并发访问
 * - 内存控制：限制最大离线设备数量，防止内存泄露
 * - 优雅关闭：支持优雅关闭和资源清理
 *
 * 业务场景：
 * - 设备故障检测：及时发现设备断线或故障
 * - 生产监控：监控生产线设备的连接状态
 * - 报警通知：设备离线时自动发送报警信息
 * - 运维管理：为运维人员提供设备状态信息
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-08
 */
package main

import (
	"context"
	"strconv"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"

	"shared/config"
	"shared/logger"
	"shared/models"
)

/**
 * 设备监控器结构体
 *
 * 功能：管理设备离线检测的核心逻辑和状态
 *
 * 架构设计：
 * - 状态管理：维护设备在线/离线状态
 * - 定时任务：定期检查设备状态和清理过期数据
 * - 并发控制：使用读写锁保护共享状态
 * - 资源管理：管理Redis连接和goroutine生命周期
 *
 * @struct DeviceMonitor
 */
type DeviceMonitor struct {
	/** 服务配置信息，包含监控参数和Redis连接配置 */
	config *config.Config

	/** Redis客户端实例，用于存储设备最后活跃时间 */
	redisClient *redis.Client

	/** 全局上下文对象，用于控制goroutine生命周期 */
	ctx context.Context

	/** 上下文取消函数，用于优雅关闭监控器 */
	cancel context.CancelFunc

	/** 设备数据采集服务实例，用于发送离线通知 */
	service *DeviceCollectService

	/** 离线设备集合，记录当前已知的离线设备，防止重复发送离线通知 */
	offlineDevices map[string]bool

	/** 读写锁，保护offlineDevices的并发访问 */
	mutex sync.RWMutex

	/** 等待组，用于等待所有goroutine完成 */
	wg sync.WaitGroup
}

// NewDeviceMonitor 创建设备监控器实例
// 参数:
//   - cfg: 服务配置
//   - redisClient: Redis客户端
//   - service: 设备数据采集服务实例
//
// 返回:
//   - *DeviceMonitor: 监控器实例
func NewDeviceMonitor(cfg *config.Config, redisClient *redis.Client, service *DeviceCollectService) *DeviceMonitor {
	ctx, cancel := context.WithCancel(context.Background())

	return &DeviceMonitor{
		config:         cfg,
		redisClient:    redisClient,
		ctx:            ctx,
		cancel:         cancel,
		service:        service,
		offlineDevices: make(map[string]bool),
		mutex:          sync.RWMutex{},
		wg:             sync.WaitGroup{},
	}
}

// Start 启动设备监控器
// 启动定期检查和清理任务的goroutine
func (dm *DeviceMonitor) Start() {
	if !dm.config.DeviceMonitor.Enabled {
		logger.Info("Device monitor is disabled")
		return
	}

	logger.Info("Starting device monitor...")

	// 启动设备状态检查任务
	dm.wg.Add(1)
	go dm.startStatusChecker()

	// 启动清理任务
	dm.wg.Add(1)
	go dm.startCleanupTask()

	logger.Infof("Device monitor started with check_interval=%ds, offline_timeout=%ds",
		dm.config.DeviceMonitor.CheckInterval,
		dm.config.DeviceMonitor.OfflineTimeout)
}

// Stop 停止设备监控器
// 优雅关闭所有goroutine并清理资源
func (dm *DeviceMonitor) Stop() {
	logger.Info("Stopping device monitor...")

	// 取消上下文，通知所有goroutine停止
	dm.cancel()

	// 等待所有goroutine完成
	dm.wg.Wait()

	logger.Info("Device monitor stopped")
}

// UpdateDeviceLastSeen 更新设备最后活跃时间
// 当接收到设备数据时调用此方法更新设备状态
// 参数:
//   - deviceID: 设备ID
//
// 返回:
//   - error: 更新失败时返回错误
func (dm *DeviceMonitor) UpdateDeviceLastSeen(deviceID string) error {
	if !dm.config.DeviceMonitor.Enabled {
		return nil
	}

	// 构建Redis key
	key := dm.config.DeviceMonitor.RedisKeyPrefix + deviceID

	// 获取当前时间戳
	timestamp := time.Now().Unix()

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(dm.ctx, 3*time.Second)
	defer cancel()

	// 将时间戳存储到Redis，设置过期时间为离线超时时间的2倍
	expiration := time.Duration(dm.config.DeviceMonitor.OfflineTimeout*2) * time.Second
	err := dm.redisClient.Set(ctx, key, timestamp, expiration).Err()
	if err != nil {
		logger.Warnf("Failed to update device last seen for %s: %v", deviceID, err)
		return err
	}

	// 如果设备之前是离线状态，现在重新上线，从离线设备列表中移除
	dm.mutex.Lock()
	if dm.offlineDevices[deviceID] {
		delete(dm.offlineDevices, deviceID)
		logger.Infof("Device %s is back online", deviceID)
	}
	dm.mutex.Unlock()

	logger.Debugf("Updated last seen time for device %s", deviceID)
	return nil
}

// startStatusChecker 启动设备状态检查任务
// 定期检查所有设备的最后活跃时间，发现离线设备并发送离线通知
func (dm *DeviceMonitor) startStatusChecker() {
	defer dm.wg.Done()

	ticker := time.NewTicker(time.Duration(dm.config.DeviceMonitor.CheckInterval) * time.Second)
	defer ticker.Stop()

	logger.Infof("Device status checker started, checking every %d seconds", dm.config.DeviceMonitor.CheckInterval)

	for {
		select {
		case <-ticker.C:
			dm.checkDeviceStatus()
		case <-dm.ctx.Done():
			logger.Info("Device status checker stopped")
			return
		}
	}
}

// checkDeviceStatus 检查设备状态
// 扫描Redis中的设备最后活跃时间，发现离线设备
func (dm *DeviceMonitor) checkDeviceStatus() {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(dm.ctx, 10*time.Second)
	defer cancel()

	// 扫描所有设备最后活跃时间的key
	pattern := dm.config.DeviceMonitor.RedisKeyPrefix + "*"
	keys, err := dm.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		logger.Warnf("Failed to scan device keys: %v", err)
		return
	}

	logger.Debugf("Checking status for %d devices", len(keys))

	currentTime := time.Now().Unix()
	offlineThreshold := int64(dm.config.DeviceMonitor.OfflineTimeout)

	for _, key := range keys {
		// 提取设备ID
		deviceID := key[len(dm.config.DeviceMonitor.RedisKeyPrefix):]

		// 获取设备最后活跃时间
		lastSeenStr, err := dm.redisClient.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				// key不存在，可能已过期
				logger.Debugf("Device %s key expired", deviceID)
				dm.handleDeviceOffline(deviceID)
			} else {
				logger.Warnf("Failed to get last seen time for device %s: %v", deviceID, err)
			}
			continue
		}

		// 解析时间戳
		lastSeen, err := strconv.ParseInt(lastSeenStr, 10, 64)
		if err != nil {
			logger.Warnf("Invalid timestamp for device %s: %s", deviceID, lastSeenStr)
			continue
		}

		// 检查是否超时
		if currentTime-lastSeen > offlineThreshold {
			logger.Infof("Device %s is offline (last seen: %d seconds ago)", deviceID, currentTime-lastSeen)
			dm.handleDeviceOffline(deviceID)
		}
	}
}

// handleDeviceOffline 处理设备离线
// 发送设备离线通知并更新离线设备列表
// 参数:
//   - deviceID: 离线的设备ID
func (dm *DeviceMonitor) handleDeviceOffline(deviceID string) {
	// 检查是否已经发送过离线通知
	dm.mutex.Lock()
	if dm.offlineDevices[deviceID] {
		dm.mutex.Unlock()
		return // 已经发送过离线通知，避免重复发送
	}

	// 检查离线设备数量限制
	if len(dm.offlineDevices) >= dm.config.DeviceMonitor.MaxOfflineDevices {
		logger.Warnf("Too many offline devices (%d), skipping device %s", len(dm.offlineDevices), deviceID)
		dm.mutex.Unlock()
		return
	}

	// 标记设备为离线
	dm.offlineDevices[deviceID] = true
	dm.mutex.Unlock()

	// 删除Redis中的设备状态记录
	key := dm.config.DeviceMonitor.RedisKeyPrefix + deviceID
	ctx, cancel := context.WithTimeout(dm.ctx, 3*time.Second)
	defer cancel()

	err := dm.redisClient.Del(ctx, key).Err()
	if err != nil {
		logger.Warnf("Failed to delete device key %s: %v", key, err)
	}

	// 发送设备离线通知
	dm.sendOfflineNotification(deviceID)

	logger.Infof("Device %s marked as offline and notification sent", deviceID)
}

// sendOfflineNotification 发送设备离线通知
// 构造离线状态数据并通过数据采集服务发送
// 参数:
//   - deviceID: 离线的设备ID
func (dm *DeviceMonitor) sendOfflineNotification(deviceID string) {
	// 构造离线状态数据
	offlineData := models.DeviceData{
		DeviceID:  deviceID,
		Timestamp: time.Now().UTC(), // 修复时区问题：使用UTC时间
		DataType:  "system",         // 系统生成的数据类型
		RawData: map[string]interface{}{
			"connected": false,
			"status":    "shutdown",
		},
		Metadata: map[string]interface{}{
			"source":      "device_monitor",
			"reason":      "offline_timeout",
			"description": "Device offline detected by monitoring system",
		},
	}

	// 通过数据采集服务存储离线通知
	err := dm.service.storeToRedisWithFallback(&offlineData)
	if err != nil {
		logger.Errorf("Failed to send offline notification for device %s: %v", deviceID, err)
		return
	}

	logger.Infof("Offline notification sent for device %s", deviceID)
}

// startCleanupTask 启动清理任务
// 定期清理过期的设备状态记录和离线设备列表
func (dm *DeviceMonitor) startCleanupTask() {
	defer dm.wg.Done()

	ticker := time.NewTicker(time.Duration(dm.config.DeviceMonitor.CleanupInterval) * time.Second)
	defer ticker.Stop()

	logger.Infof("Device cleanup task started, cleaning every %d seconds", dm.config.DeviceMonitor.CleanupInterval)

	for {
		select {
		case <-ticker.C:
			dm.cleanupExpiredDevices()
		case <-dm.ctx.Done():
			logger.Info("Device cleanup task stopped")
			return
		}
	}
}

// cleanupExpiredDevices 清理过期的设备状态记录
// 清理Redis中过期的设备状态记录和内存中的离线设备列表
func (dm *DeviceMonitor) cleanupExpiredDevices() {
	logger.Debug("Starting device cleanup task")

	// 清理内存中的离线设备列表
	dm.mutex.Lock()
	beforeCount := len(dm.offlineDevices)

	// 如果离线设备数量过多，清理一半
	if beforeCount > dm.config.DeviceMonitor.MaxOfflineDevices/2 {
		// 创建新的map，只保留一半设备
		newOfflineDevices := make(map[string]bool)
		count := 0
		maxKeep := dm.config.DeviceMonitor.MaxOfflineDevices / 4

		for deviceID := range dm.offlineDevices {
			if count < maxKeep {
				newOfflineDevices[deviceID] = true
				count++
			}
		}

		dm.offlineDevices = newOfflineDevices
		logger.Infof("Cleaned up offline devices list: %d -> %d", beforeCount, len(dm.offlineDevices))
	}
	dm.mutex.Unlock()

	// 清理Redis中过期的key（Redis会自动过期，这里只是记录统计）
	ctx, cancel := context.WithTimeout(dm.ctx, 10*time.Second)
	defer cancel()

	pattern := dm.config.DeviceMonitor.RedisKeyPrefix + "*"
	keys, err := dm.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		logger.Warnf("Failed to scan device keys during cleanup: %v", err)
		return
	}

	logger.Debugf("Cleanup completed. Active device keys: %d, Offline devices in memory: %d",
		len(keys), len(dm.offlineDevices))
}

// GetStats 获取设备监控统计信息
// 返回设备监控的运行状态和统计数据
// 返回:
//   - map[string]interface{}: 包含监控统计信息的map
func (dm *DeviceMonitor) GetStats() map[string]interface{} {
	if !dm.config.DeviceMonitor.Enabled {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	dm.mutex.RLock()
	offlineCount := len(dm.offlineDevices)
	dm.mutex.RUnlock()

	// 获取活跃设备数量
	ctx, cancel := context.WithTimeout(dm.ctx, 5*time.Second)
	defer cancel()

	pattern := dm.config.DeviceMonitor.RedisKeyPrefix + "*"
	keys, err := dm.redisClient.Keys(ctx, pattern).Result()
	activeCount := 0
	if err == nil {
		activeCount = len(keys)
	}

	return map[string]interface{}{
		"enabled":             true,
		"check_interval":      dm.config.DeviceMonitor.CheckInterval,
		"offline_timeout":     dm.config.DeviceMonitor.OfflineTimeout,
		"cleanup_interval":    dm.config.DeviceMonitor.CleanupInterval,
		"max_offline_devices": dm.config.DeviceMonitor.MaxOfflineDevices,
		"active_devices":      activeCount,
		"offline_devices":     offlineCount,
		"redis_key_prefix":    dm.config.DeviceMonitor.RedisKeyPrefix,
	}
}
