/**
 * 序列号生成模块
 *
 * 功能概述：
 * 本模块提供有序的唯一标识符生成功能，是确保设备数据FIFO（先进先出）顺序的核心组件，
 * 通过原子操作和循环计数机制，在高并发环境下保证数据处理的严格时序性
 *
 * 主要功能：
 * - 序列ID生成：生成严格有序的唯一标识符
 * - 并发安全：使用原子操作确保线程安全
 * - 循环计数：达到最大值后自动重置，避免溢出
 * - 配置驱动：支持通过配置文件调整最大序列值
 * - 格式自适应：根据最大值自动选择合适的十六进制位数
 *
 * 技术特性：
 * - 原子操作：使用sync/atomic包确保并发安全
 * - 无锁设计：避免锁竞争，提高并发性能
 * - 内存高效：使用uint64计数器，内存占用极小
 * - 性能优化：O(1)时间复杂度的ID生成
 * - 可配置性：最大序列值可通过配置文件调整
 *
 * 业务价值：
 * - 数据顺序保证：确保设备数据按接收顺序处理
 * - 高并发支持：支持数千台设备同时发送数据
 * - 系统稳定性：避免序列号冲突和重复
 * - 调试友好：十六进制格式便于调试和日志分析
 * - 容量规划：可配置的最大值支持不同规模部署
 *
 * 应用场景：
 * - 设备数据排序：确保数据处理的时间顺序
 * - Redis键值生成：生成唯一且有序的Redis键
 * - 日志追踪：为日志记录提供序列标识
 * - 性能监控：序列号重置可作为性能指标
 *
 * 设计原理：
 * - 时间戳+序列号：结合时间戳使用，确保全局唯一性
 * - 循环重置：避免长期运行导致的数值溢出
 * - 格式化输出：统一的十六进制格式，便于存储和比较
 * - 配置化管理：支持不同环境的容量需求
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"fmt"
	"sync/atomic"

	"shared/config"
	"shared/logger"
)

/**
 * 全局序列计数器
 *
 * 功能：生成有序的唯一标识符的核心计数器
 *
 * 技术特性：
 * - 原子操作：使用sync/atomic包确保线程安全
 * - 无锁设计：避免锁竞争，提高并发性能
 * - 严格递增：保证在高并发环境下的序列性
 * - 循环重置：达到最大值后自动重置为1
 *
 * 并发安全：
 * - 使用atomic.AddUint64进行原子递增
 * - 使用atomic.CompareAndSwapUint64进行原子重置
 * - 支持数千个goroutine同时访问
 *
 * 性能特性：
 * - O(1)时间复杂度
 * - 无内存分配
 * - CPU缓存友好
 *
 * @var {uint64} sequenceCounter - 全局序列计数器
 */
var sequenceCounter uint64

/**
 * 全局最大序列值配置
 *
 * 功能：定义序列计数器的最大值，控制循环重置的时机
 *
 * 配置来源：
 * - 优先使用配置文件中的redis.max_sequence_value
 * - 如果未配置或为0，使用默认值16777215
 *
 * 默认值说明：
 * - 16777215 = 0xFFFFFF（6位十六进制最大值）
 * - 支持1677万个唯一序列号
 * - 适合大多数生产环境的需求
 *
 * 容量规划：
 * - 6位十六进制：16,777,215个序列号
 * - 8位十六进制：4,294,967,295个序列号
 * - 12位十六进制：281,474,976,710,655个序列号
 * - 16位十六进制：uint64最大值
 *
 * 业务影响：
 * - 值越大，重置周期越长，序列号越稳定
 * - 值越小，重置越频繁，但内存占用更少
 * - 建议根据实际设备数量和数据频率调整
 *
 * @var {uint64} maxSequenceValue - 最大序列值，默认16777215
 */
var maxSequenceValue uint64 = 16777215

// initSequenceConfig 初始化序列配置
// 从配置文件读取最大序列值，如果未配置则使用默认值
// 参数:
//   - config: 配置对象，包含Redis配置信息
//
// 功能:
//   - 读取配置中的max_sequence_value
//   - 如果配置值为0或未设置，使用默认值16777215
//   - 记录配置加载结果到日志
func initSequenceConfig(config *config.Config) {
	if config.Redis.MaxSequenceValue > 0 {
		maxSequenceValue = config.Redis.MaxSequenceValue
		logger.Infof("Sequence max value loaded from config: %d (0x%x)", maxSequenceValue, maxSequenceValue)
	} else {
		logger.Infof("Sequence max value using default: %d (0x%x)", maxSequenceValue, maxSequenceValue)
	}

	// 记录格式化信息
	format := getSequenceFormat()
	logger.Infof("Sequence format: %s", format)
}

// getSequenceFormat 根据最大值确定十六进制格式化字符串
// 自动选择合适的位数以容纳最大值
// 返回:
//   - string: 格式化字符串，如"%06x"、"%08x"等
func getSequenceFormat() string {
	switch {
	case maxSequenceValue <= 0xFFFFFF: // 6位十六进制 (16,777,215)
		return "%06x"
	case maxSequenceValue <= 0xFFFFFFFF: // 8位十六进制 (4,294,967,295)
		return "%08x"
	case maxSequenceValue <= 0xFFFFFFFFFFFF: // 12位十六进制 (281,474,976,710,655)
		return "%012x"
	default: // 16位十六进制 (uint64最大值)
		return "%016x"
	}
}

// generateSequentialID 生成具有序列性的唯一标识符
// 使用原子计数器确保先来后到的顺序，同时保证唯一性
// 当计数器超过最大值时自动重置为1，实现循环计数
// 格式: {6位递增序列号}
// 返回:
//   - string: 6位十六进制序列号，如"000001", "000002", "0f423f"
//
// 特性:
//   - 线程安全：使用atomic操作确保并发安全
//   - 有序性：严格按照调用顺序递增
//   - 唯一性：在重置周期内保证唯一
//   - 循环性：达到最大值后重置为1，避免溢出
//   - 可控性：最大值可配置，便于调整容量
//
// 示例:
//
//	id1 := generateSequentialID() // "000001"
//	id2 := generateSequentialID() // "000002"
//	...
//	idMax := generateSequentialID() // "ffffff" (最大值的十六进制)
//	idReset := generateSequentialID() // "000001" (重置后的第一个)
func generateSequentialID() string {
	for {
		// 原子递增计数器，确保线程安全和顺序性
		currentSeq := atomic.AddUint64(&sequenceCounter, 1)

		// 检查是否超过最大值，如果超过则尝试重置
		if currentSeq > maxSequenceValue {
			// 尝试原子性地重置计数器为1
			// 使用CompareAndSwap确保只有一个goroutine执行重置
			if atomic.CompareAndSwapUint64(&sequenceCounter, currentSeq, 1) {
				// 重置成功，返回1
				logger.Infof("Sequence counter reset from %d to 1", currentSeq)
				return fmt.Sprintf(getSequenceFormat(), 1)
			}
			// 重置失败（其他goroutine已经重置），重新尝试获取序列号
			continue
		}

		// 正常情况，返回当前序列号
		return fmt.Sprintf(getSequenceFormat(), currentSeq)
	}
}
