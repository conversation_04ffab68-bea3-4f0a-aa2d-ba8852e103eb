/**
 * 本地缓存模块
 *
 * 功能概述：
 * 本模块提供FIFO（先进先出）有序本地缓存功能，作为Redis不可用时的降级存储方案，
 * 确保在网络故障或Redis服务异常时，设备数据仍能被安全存储和有序处理
 *
 * 主要功能：
 * - FIFO缓存：严格按照先进先出顺序存储和处理数据
 * - 降级存储：Redis故障时的自动降级存储方案
 * - 容量控制：可配置的最大缓存容量，防止内存溢出
 * - 快速查找：O(1)时间复杂度的数据查找
 * - 有序同步：Redis恢复后按顺序同步本地数据
 * - 线程安全：完整的并发访问保护
 *
 * 技术特性：
 * - 双重索引：切片保证顺序，map提供快速查找
 * - 内存高效：智能的内存管理和容量控制
 * - 并发安全：读写锁保护，支持高并发访问
 * - 顺序保证：严格的FIFO顺序，确保数据处理正确性
 * - 自动清理：容量超限时自动清理最旧数据
 *
 * 数据结构设计：
 * - 切片存储：保证数据的插入和访问顺序
 * - 哈希索引：提供O(1)的键值查找性能
 * - 时间戳：记录数据存储时间，便于调试和监控
 * - 容量限制：防止内存无限增长
 *
 * 降级策略：
 * - 自动启用：Redis故障时自动启用本地缓存
 * - 透明切换：对上层业务逻辑透明
 * - 数据保护：确保故障期间数据不丢失
 * - 有序恢复：Redis恢复后按FIFO顺序同步数据
 *
 * 业务价值：
 * - 高可用性：Redis故障时的服务连续性保证
 * - 数据完整性：故障期间数据的完整保护
 * - 性能保证：本地内存访问的高性能
 * - 运维友好：自动的故障处理和恢复机制
 * - 扩展性：支持不同规模的缓存需求
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"sync"
	"time"
)

/**
 * 缓存项结构体
 *
 * 功能：存储单个缓存数据项的完整信息
 *
 * 数据组成：
 * - 键值对：Redis兼容的键值存储
 * - 时间戳：数据存储时间，用于调试和监控
 * - 序列化：数据已序列化为字节数组，便于存储
 *
 * @struct CacheItem
 */
type CacheItem struct {
	/** Redis键，与Redis存储格式保持一致 */
	Key string

	/** 序列化后的数据，JSON格式的字节数组 */
	Data []byte

	/** 存储时间戳，记录数据进入缓存的时间 */
	Timestamp time.Time
}

/**
 * 本地缓存结构体
 *
 * 功能：提供FIFO有序的内存缓存，作为Redis的降级替代方案
 *
 * 架构设计：
 * - 双重索引：切片保证顺序，map提供快速查找
 * - 并发安全：读写锁保护，支持高并发访问
 * - 容量控制：可配置的最大容量，防止内存溢出
 * - FIFO保证：严格的先进先出顺序
 *
 * @struct LocalCache
 */
type LocalCache struct {
	/** 有序存储的数据项切片，保证FIFO顺序 */
	items []CacheItem

	/** 键到索引的映射，提供O(1)查找性能 */
	keyMap map[string]int

	/** 最大缓存条数，超出时清理最旧数据 */
	maxSize int

	/** 读写锁，保护并发访问安全 */
	mutex sync.RWMutex
}

// NewLocalCache 创建本地缓存
// 初始化有序缓存结构，确保FIFO顺序
// 参数:
//   - maxSize: 最大缓存条数，超出时清理最旧数据
//
// 返回:
//   - *LocalCache: 本地缓存实例
func NewLocalCache(maxSize int) *LocalCache {
	return &LocalCache{
		items:   make([]CacheItem, 0, maxSize),
		keyMap:  make(map[string]int),
		maxSize: maxSize,
	}
}

// Set 设置缓存数据
// 按照FIFO顺序存储数据，确保先进先出
// 参数:
//   - key: 缓存键
//   - value: 缓存值
//
// 特性:
//   - FIFO顺序：新数据添加到末尾
//   - 容量控制：超出最大容量时清理最旧数据
//   - 更新支持：相同key的数据会被更新而不是重复添加
//   - 线程安全：使用互斥锁保护并发访问
func (lc *LocalCache) Set(key string, value []byte) {
	lc.mutex.Lock()
	defer lc.mutex.Unlock()

	// 检查key是否已存在
	if index, exists := lc.keyMap[key]; exists {
		// 更新现有数据
		lc.items[index].Data = value
		lc.items[index].Timestamp = time.Now()
		return
	}

	// 检查缓存大小，如果超过限制则清理最旧的数据
	if len(lc.items) >= lc.maxSize {
		lc.evictOldest()
	}

	// 添加新数据到末尾（FIFO队列）
	newItem := CacheItem{
		Key:       key,
		Data:      value,
		Timestamp: time.Now(),
	}

	lc.items = append(lc.items, newItem)
	lc.keyMap[key] = len(lc.items) - 1
}

// Get 获取缓存数据
// 参数:
//   - key: 缓存键
//
// 返回:
//   - []byte: 缓存值
//   - bool: 是否找到
func (lc *LocalCache) Get(key string) ([]byte, bool) {
	lc.mutex.RLock()
	defer lc.mutex.RUnlock()

	if index, exists := lc.keyMap[key]; exists {
		return lc.items[index].Data, true
	}
	return nil, false
}

// evictOldest 清理最旧的数据
// 利用FIFO特性，直接移除第一个元素（最旧的）
// 特性:
//   - FIFO清理：总是清理最先进入的数据
//   - 索引更新：清理后更新所有索引映射
//   - 内存优化：移除数据后压缩切片
func (lc *LocalCache) evictOldest() {
	if len(lc.items) == 0 {
		return
	}

	// 移除第一个元素（最旧的）
	oldestItem := lc.items[0]
	lc.items = lc.items[1:]

	// 从keyMap中删除
	delete(lc.keyMap, oldestItem.Key)

	// 更新所有索引（因为切片前移了）
	for i := 0; i < len(lc.items); i++ {
		lc.keyMap[lc.items[i].Key] = i
	}
}

// Size 获取缓存大小
// 返回:
//   - int: 当前缓存的数据条数
func (lc *LocalCache) Size() int {
	lc.mutex.RLock()
	defer lc.mutex.RUnlock()
	return len(lc.items)
}

// Clear 清空缓存
// 清空所有缓存数据，但保留底层数组容量以提高性能
func (lc *LocalCache) Clear() {
	lc.mutex.Lock()
	defer lc.mutex.Unlock()
	lc.items = lc.items[:0] // 保留底层数组容量
	lc.keyMap = make(map[string]int)
}

// GetAllData 获取所有缓存数据（用于Redis恢复时同步）
// 返回按FIFO顺序排列的数据，确保同步时保持先进先出顺序
// 返回:
//   - map[string][]byte: key到数据的映射
//
// 注意:
//   - 虽然返回map，但内部遍历是按FIFO顺序的
//   - 主要用于兼容现有的同步接口
func (lc *LocalCache) GetAllData() map[string][]byte {
	lc.mutex.RLock()
	defer lc.mutex.RUnlock()

	result := make(map[string][]byte)
	for _, item := range lc.items {
		result[item.Key] = item.Data
	}
	return result
}

// GetOrderedItems 获取有序的缓存项列表
// 返回按FIFO顺序排列的缓存项，用于需要保持顺序的场景
// 返回:
//   - []CacheItem: 按FIFO顺序排列的缓存项副本
//
// 特性:
//   - 严格FIFO：返回的切片严格按照存储顺序排列
//   - 安全副本：返回副本，避免外部修改影响内部状态
//   - 完整信息：包含key、数据和时间戳的完整信息
func (lc *LocalCache) GetOrderedItems() []CacheItem {
	lc.mutex.RLock()
	defer lc.mutex.RUnlock()

	// 创建副本，避免外部修改
	result := make([]CacheItem, len(lc.items))
	copy(result, lc.items)
	return result
}
