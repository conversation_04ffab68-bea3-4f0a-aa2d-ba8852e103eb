#!/bin/bash

PORT=9001

# 设备数据采集服务启动脚本
# 解决Go环境配置问题并启动02_device_collect服务

echo "🚀 启动 设备数据采集服务"
echo "=================================="

# 检查并修复Go环境
echo "🔧 检查Go环境..."

# 设置正确的Go环境变量
export PATH=/opt/homebrew/bin:$PATH
export GOROOT=/opt/homebrew/opt/go/libexec

# 验证Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: Go未安装或不在PATH中"
    echo "请确保Go已正确安装在 /opt/homebrew/opt/go"
    exit 1
fi

# 显示Go版本信息
echo "✅ Go版本: $(go version)"
echo "✅ GOROOT: $(go env GOROOT)"
echo "✅ GOPATH: $(go env GOPATH)"

# 检查是否在正确的目录
if [ ! -f "main.go" ]; then
    echo "❌ 错误: 未找到main.go文件"
    echo "请确保在02_device_collect目录中运行此脚本"
    exit 1
fi

# 检查配置文件
echo "🔍 检查配置文件..."
if [ ! -f "config.yml" ]; then
    echo "❌ 错误: 未找到config.yml配置文件"
    echo "请确保配置文件存在"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 检查端口是否被占用

if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 $PORT 已被占用"
    echo "正在尝试停止占用端口的进程..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p cache
mkdir -p data

# 下载Go依赖
echo "📦 下载Go依赖..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "❌ 错误: Go依赖下载失败"
    exit 1
fi

echo "✅ Go依赖下载完成"

echo ""
echo "🌐 启动设备数据采集服务..."
echo "端口: $PORT"
echo "访问地址: http://localhost:$PORT"
echo "健康检查: http://localhost:$PORT/health"
echo ""
echo "功能特性:"
echo "  - 设备数据采集"
echo "  - 数据缓存和存储"
echo "  - REST API接口"
echo "  - 健康状态监控"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 启动设备数据采集服务
go run .
