/**
 * 稳定性管理模块
 *
 * 功能概述：
 * 本模块提供设备数据采集服务的稳定性保障功能，通过内存监控、错误统计、
 * 熔断机制等手段，确保服务在高负载和异常情况下的稳定运行
 *
 * 主要功能：
 * - 内存监控：实时监控内存使用情况，防止内存溢出
 * - 垃圾回收：智能GC触发，优化内存使用效率
 * - 熔断机制：内存过载时自动启用熔断器，保护系统
 * - 错误统计：统计请求错误率和Redis错误率
 * - 状态管理：管理Redis连接状态和本地缓存状态
 * - 健康检查：定期输出系统健康状态信息
 *
 * 技术特性：
 * - 实时监控：毫秒级的内存使用监控
 * - 自动调节：根据内存使用情况自动触发GC
 * - 智能熔断：基于内存阈值的熔断器机制
 * - 原子操作：使用原子计数器确保统计准确性
 * - 状态同步：实时同步Redis和缓存状态
 *
 * 稳定性策略：
 * - 内存保护：设置内存上限，防止OOM
 * - 优雅降级：Redis故障时自动切换到本地缓存
 * - 错误隔离：统计和隔离不同类型的错误
 * - 自动恢复：故障恢复后自动恢复正常状态
 * - 性能优化：通过GC调优提升性能
 *
 * 监控指标：
 * - 内存使用量：当前堆内存和总内存使用
 * - GC统计：GC次数和GC触发频率
 * - 请求统计：总请求数和错误请求数
 * - Redis状态：连接状态和错误统计
 * - 熔断状态：熔断器开关状态
 *
 * 业务价值：
 * - 服务稳定性：防止内存泄露和系统崩溃
 * - 高可用性：故障时的自动降级和恢复
 * - 性能优化：智能的内存管理和GC优化
 * - 运维友好：详细的监控指标和状态信息
 * - 故障预防：主动的资源监控和保护机制
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"runtime"
	"runtime/debug"
	"sync/atomic"
	"time"

	"shared/logger"
)

/**
 * 稳定性管理器结构体
 *
 * 功能：作为系统稳定性的核心控制器，负责监控、保护和优化系统运行状态
 *
 * 架构设计：
 * - 状态管理：集中管理系统各种状态信息
 * - 阈值控制：基于配置的阈值进行智能决策
 * - 原子操作：使用原子计数器确保并发安全
 * - 时间管理：记录关键操作的时间戳
 *
 * 核心职责：
 * - 内存监控：实时监控内存使用，防止OOM
 * - 错误统计：统计各类错误，计算错误率
 * - 熔断保护：内存过载时启用熔断器
 * - 状态协调：协调Redis和本地缓存状态
 * - 性能优化：智能GC触发和内存管理
 *
 * @struct StabilityManager
 */
type StabilityManager struct {
	/** 最大内存限制(MB)，超过此值启用熔断器 */
	maxMemoryMB uint64

	/** GC触发阈值(MB)，达到此值主动触发垃圾回收 */
	gcThresholdMB uint64

	/** 请求计数器，使用原子操作确保并发安全 */
	requestCount uint64

	/** 错误计数器，统计所有类型的错误 */
	errorCount uint64

	/** Redis错误计数器，专门统计Redis相关错误 */
	redisErrorCount uint64

	/** 上次GC时间，用于控制GC触发频率 */
	lastGCTime time.Time

	/** 熔断器状态，true表示熔断器开启 */
	circuitBreaker bool

	/** Redis可用状态，true表示Redis连接正常 */
	redisAvailable bool

	/** 上次健康检查时间，用于控制日志输出频率 */
	lastHealthCheck time.Time

	/** 上次Redis检查时间，用于控制Redis状态检查频率 */
	lastRedisCheck time.Time

	/** 本地缓存是否启用，Redis不可用时自动启用 */
	localCacheEnabled bool
}

// NewStabilityManager 创建稳定性管理器
// 初始化内存监控、错误统计和熔断机制
// 参数:
//   - maxMemoryMB: 最大内存限制(MB)，超过此值触发GC和告警
//   - gcThresholdMB: GC触发阈值(MB)，达到此值主动触发GC
//
// 返回:
//   - *StabilityManager: 稳定性管理器实例
func NewStabilityManager(maxMemoryMB, gcThresholdMB uint64) *StabilityManager {
	// 设置GC目标百分比，优化内存回收
	debug.SetGCPercent(50) // 降低GC触发阈值，更频繁回收

	// 记录稳定性管理器配置信息
	logger.Infof("Stability manager initialized with max_memory=%dMB, gc_threshold=%dMB",
		maxMemoryMB, gcThresholdMB)

	return &StabilityManager{
		maxMemoryMB:       maxMemoryMB,
		gcThresholdMB:     gcThresholdMB,
		requestCount:      0,
		errorCount:        0,
		redisErrorCount:   0,
		lastGCTime:        time.Now(),
		circuitBreaker:    false,
		redisAvailable:    true, // 初始假设Redis可用
		lastHealthCheck:   time.Now(),
		lastRedisCheck:    time.Now(),
		localCacheEnabled: false,
	}
}

// checkMemoryUsage 检查内存使用情况
// 监控当前内存使用量，必要时触发GC或启用熔断器
// 返回:
//   - bool: 是否需要触发熔断器
func (sm *StabilityManager) checkMemoryUsage() bool {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	currentMemoryMB := m.Alloc / 1024 / 1024

	// 记录内存使用情况
	if time.Since(sm.lastHealthCheck) > 30*time.Second {
		logger.Infof("Memory usage: %d MB, Heap: %d MB, GC cycles: %d",
			currentMemoryMB, m.HeapAlloc/1024/1024, m.NumGC)
		sm.lastHealthCheck = time.Now()
	}

	// 检查是否需要主动触发GC
	if currentMemoryMB > sm.gcThresholdMB && time.Since(sm.lastGCTime) > 10*time.Second {
		logger.Warnf("Memory usage (%d MB) exceeds GC threshold (%d MB), triggering GC",
			currentMemoryMB, sm.gcThresholdMB)
		runtime.GC()
		sm.lastGCTime = time.Now()
	}

	// 检查是否需要启用熔断器
	if currentMemoryMB > sm.maxMemoryMB {
		if !sm.circuitBreaker {
			logger.Errorf("Memory usage (%d MB) exceeds maximum limit (%d MB), enabling circuit breaker",
				currentMemoryMB, sm.maxMemoryMB)
			sm.circuitBreaker = true
		}
		return true
	}

	// 内存使用正常，关闭熔断器
	if sm.circuitBreaker && currentMemoryMB < sm.maxMemoryMB*8/10 { // 80%阈值关闭熔断器
		logger.Infof("Memory usage normalized (%d MB), disabling circuit breaker", currentMemoryMB)
		sm.circuitBreaker = false
	}

	return false
}

// recordRequest 记录请求统计
// 用于监控系统负载和错误率
func (sm *StabilityManager) recordRequest() {
	atomic.AddUint64(&sm.requestCount, 1)
}

// recordError 记录错误统计
// 用于监控系统错误率和稳定性
func (sm *StabilityManager) recordError() {
	atomic.AddUint64(&sm.errorCount, 1)
}

// recordRedisError 记录Redis错误统计
// 用于监控Redis连接稳定性
func (sm *StabilityManager) recordRedisError() {
	atomic.AddUint64(&sm.redisErrorCount, 1)
	atomic.AddUint64(&sm.errorCount, 1)
}

// setRedisAvailable 设置Redis可用状态
// 参数:
//   - available: Redis是否可用
func (sm *StabilityManager) setRedisAvailable(available bool) {
	if sm.redisAvailable != available {
		if available {
			logger.Infof("Redis connection restored")
		} else {
			logger.Errorf("Redis connection lost, enabling local cache")
		}
		sm.redisAvailable = available
		sm.localCacheEnabled = !available
	}
}

// isRedisAvailable 检查Redis是否可用
// 返回:
//   - bool: Redis是否可用
func (sm *StabilityManager) isRedisAvailable() bool {
	return sm.redisAvailable
}

// isLocalCacheEnabled 检查本地缓存是否启用
// 返回:
//   - bool: 本地缓存是否启用
func (sm *StabilityManager) isLocalCacheEnabled() bool {
	return sm.localCacheEnabled
}

// getRedisErrorRate 获取Redis错误率
// 计算Redis错误率百分比
// 返回:
//   - float64: Redis错误率(0-100)
func (sm *StabilityManager) getRedisErrorRate() float64 {
	requests := atomic.LoadUint64(&sm.requestCount)
	redisErrors := atomic.LoadUint64(&sm.redisErrorCount)

	if requests == 0 {
		return 0
	}

	return float64(redisErrors) / float64(requests) * 100
}

// getErrorRate 获取错误率
// 计算当前的错误率百分比
// 返回:
//   - float64: 错误率(0-100)
func (sm *StabilityManager) getErrorRate() float64 {
	requests := atomic.LoadUint64(&sm.requestCount)
	errors := atomic.LoadUint64(&sm.errorCount)

	if requests == 0 {
		return 0
	}

	return float64(errors) / float64(requests) * 100
}

// isCircuitBreakerOpen 检查熔断器状态
// 返回:
//   - bool: 熔断器是否开启
func (sm *StabilityManager) isCircuitBreakerOpen() bool {
	return sm.circuitBreaker
}
