/**
 * 设备数据日志模块
 *
 * 功能概述：
 * 本模块负责记录设备推送的原始数据到专门的日志文件中，支持按设备分类记录，
 * 并应用日志轮转、压缩等管理功能，为数据分析和故障排查提供详细的数据记录
 *
 * 主要功能：
 * - 设备数据记录：记录每个设备推送的完整数据到日志文件
 * - 分类存储：按设备ID或类型分类存储日志文件
 * - 日志轮转：应用配置的日志轮转、压缩、清理策略
 * - 性能优化：异步写入，不阻塞主数据处理流程
 * - 格式化输出：支持JSON和文本格式的日志输出
 *
 * 技术特性：
 * - 异步写入：使用goroutine和channel实现异步日志写入
 * - 批量处理：支持批量写入，提高I/O性能
 * - 内存控制：限制内存中的日志缓冲区大小
 * - 文件管理：自动创建目录和文件，支持文件轮转
 * - 错误处理：完善的错误处理和恢复机制
 *
 * 业务场景：
 * - 数据审计：记录所有设备数据用于审计和合规
 * - 故障排查：提供详细的设备数据历史用于故障分析
 * - 数据分析：为数据科学家提供原始数据源
 * - 系统监控：监控设备数据的质量和频率
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-08
 */
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"gopkg.in/natefinch/lumberjack.v2"

	"shared/config"
	"shared/logger"
	"shared/models"
)

/**
 * 设备数据日志器结构体
 *
 * 功能：管理设备数据的日志记录和文件管理
 *
 * 架构设计：
 * - 异步处理：使用channel和goroutine实现异步日志写入
 * - 文件管理：为每个设备或设备类型创建独立的日志文件
 * - 性能优化：批量写入和缓冲机制
 * - 资源管理：自动清理和文件轮转
 *
 * @struct DeviceLogger
 */
type DeviceLogger struct {
	/** 服务配置信息，包含日志配置和轮转参数 */
	config *config.Config

	/** 全局上下文对象，用于控制goroutine生命周期 */
	ctx context.Context

	/** 上下文取消函数，用于优雅关闭日志器 */
	cancel context.CancelFunc

	/** 日志数据通道，用于异步传递需要记录的设备数据 */
	logChannel chan *models.DeviceData

	/** 日志写入器映射，按设备ID或类型管理不同的日志文件 */
	loggers map[string]*lumberjack.Logger

	/** 读写锁，保护loggers映射的并发访问 */
	mutex sync.RWMutex

	/** 等待组，用于等待所有goroutine完成 */
	wg sync.WaitGroup

	/** 是否启用设备日志记录 */
	enabled bool
}

// NewDeviceLogger 创建设备数据日志器实例
// 参数:
//   - cfg: 服务配置
//
// 返回:
//   - *DeviceLogger: 日志器实例
func NewDeviceLogger(cfg *config.Config) *DeviceLogger {
	ctx, cancel := context.WithCancel(context.Background())

	return &DeviceLogger{
		config:     cfg,
		ctx:        ctx,
		cancel:     cancel,
		logChannel: make(chan *models.DeviceData, 1000), // 缓冲1000条日志
		loggers:    make(map[string]*lumberjack.Logger),
		mutex:      sync.RWMutex{},
		wg:         sync.WaitGroup{},
		enabled:    cfg.Logging.DeviceLogs,
	}
}

// Start 启动设备数据日志器
// 启动异步日志写入goroutine
func (dl *DeviceLogger) Start() {
	if !dl.enabled {
		logger.Info("Device data logging is disabled")
		return
	}

	logger.Info("Starting device data logger...")

	// 创建日志目录
	logDir := filepath.Dir(dl.config.Logging.Output)
	deviceLogDir := filepath.Join(logDir, "devices")
	if err := os.MkdirAll(deviceLogDir, 0755); err != nil {
		logger.Errorf("Failed to create device log directory: %v", err)
		return
	}

	// 启动日志写入任务
	dl.wg.Add(1)
	go dl.startLogWriter()

	logger.Infof("Device data logger started, log directory: %s", deviceLogDir)
}

// Stop 停止设备数据日志器
// 优雅关闭所有goroutine并清理资源
func (dl *DeviceLogger) Stop() {
	if !dl.enabled {
		return
	}

	logger.Info("Stopping device data logger...")

	// 关闭日志通道
	close(dl.logChannel)

	// 取消上下文，通知所有goroutine停止
	dl.cancel()

	// 等待所有goroutine完成
	dl.wg.Wait()

	// 关闭所有日志写入器
	dl.mutex.Lock()
	for _, logWriter := range dl.loggers {
		if logWriter != nil {
			logWriter.Close()
		}
	}
	dl.loggers = make(map[string]*lumberjack.Logger)
	dl.mutex.Unlock()

	logger.Info("Device data logger stopped")
}

// LogDeviceData 记录设备数据
// 异步记录设备数据到日志文件
// 参数:
//   - deviceData: 需要记录的设备数据
func (dl *DeviceLogger) LogDeviceData(deviceData *models.DeviceData) {
	if !dl.enabled || deviceData == nil {
		return
	}

	// 非阻塞发送到日志通道
	select {
	case dl.logChannel <- deviceData:
		// 成功发送到通道
	default:
		// 通道满了，丢弃日志（避免阻塞主流程）
		logger.Warnf("Device log channel is full, dropping log for device %s", deviceData.DeviceID)
	}
}

// startLogWriter 启动日志写入任务
// 从通道中读取设备数据并写入到对应的日志文件
func (dl *DeviceLogger) startLogWriter() {
	defer dl.wg.Done()

	logger.Info("Device log writer started")

	for {
		select {
		case deviceData, ok := <-dl.logChannel:
			if !ok {
				// 通道已关闭
				logger.Info("Device log channel closed, stopping log writer")
				return
			}
			dl.writeDeviceLog(deviceData)

		case <-dl.ctx.Done():
			// 上下文取消
			logger.Info("Device log writer stopped by context")
			return
		}
	}
}

// writeDeviceLog 写入设备数据日志
// 将设备数据写入到对应的日志文件中
// 参数:
//   - deviceData: 需要写入的设备数据
func (dl *DeviceLogger) writeDeviceLog(deviceData *models.DeviceData) {
	// 获取或创建设备对应的日志写入器
	logWriter := dl.getOrCreateLogger(deviceData.DeviceID)
	if logWriter == nil {
		logger.Errorf("Failed to get log writer for device %s", deviceData.DeviceID)
		return
	}

	// 构造日志记录
	logEntry := map[string]interface{}{
		"timestamp":  deviceData.Timestamp.Format(time.RFC3339Nano),
		"device_id":  deviceData.DeviceID,
		"data_type":  deviceData.DataType,
		"raw_data":   deviceData.RawData,
		"metadata":   deviceData.Metadata,
		"log_time":   time.Now().Format(time.RFC3339Nano),
		"log_source": "device_collect",
	}

	// 序列化为JSON
	logData, err := json.Marshal(logEntry)
	if err != nil {
		logger.Errorf("Failed to marshal device log for %s: %v", deviceData.DeviceID, err)
		return
	}

	// 写入日志文件
	if _, err := logWriter.Write(append(logData, '\n')); err != nil {
		logger.Errorf("Failed to write device log for %s: %v", deviceData.DeviceID, err)
		return
	}

	logger.Debugf("Device log written for %s", deviceData.DeviceID)
}

// getOrCreateLogger 获取或创建设备对应的日志写入器
// 为每个设备创建独立的日志文件，应用轮转配置
// 参数:
//   - deviceID: 设备ID
//
// 返回:
//   - *lumberjack.Logger: 日志写入器实例
func (dl *DeviceLogger) getOrCreateLogger(deviceID string) *lumberjack.Logger {
	dl.mutex.RLock()
	if logWriter, exists := dl.loggers[deviceID]; exists {
		dl.mutex.RUnlock()
		return logWriter
	}
	dl.mutex.RUnlock()

	// 需要创建新的日志写入器
	dl.mutex.Lock()
	defer dl.mutex.Unlock()

	// 双重检查，防止并发创建
	if logWriter, exists := dl.loggers[deviceID]; exists {
		return logWriter
	}

	// 构造日志文件路径
	logDir := filepath.Dir(dl.config.Logging.Output)
	deviceLogDir := filepath.Join(logDir, "devices")
	logFilePath := filepath.Join(deviceLogDir, fmt.Sprintf("device_%s.log", deviceID))

	// 创建lumberjack日志写入器，应用轮转配置
	logWriter := &lumberjack.Logger{
		Filename:   logFilePath,                           // 日志文件路径
		MaxSize:    dl.config.Logging.Rotation.MaxSize,    // 单个文件最大大小(MB)
		MaxAge:     dl.config.Logging.Rotation.MaxAge,     // 文件保留天数
		MaxBackups: dl.config.Logging.Rotation.MaxBackups, // 保留的备份文件数量
		Compress:   dl.config.Logging.Rotation.Compress,   // 是否压缩旧文件
		LocalTime:  true,                                  // 使用本地时间
	}

	// 存储到映射中
	dl.loggers[deviceID] = logWriter

	logger.Infof("Created device log writer for %s: %s", deviceID, logFilePath)
	return logWriter
}

// GetStats 获取设备日志统计信息
// 返回设备日志的运行状态和统计数据
// 返回:
//   - map[string]interface{}: 包含日志统计信息的map
func (dl *DeviceLogger) GetStats() map[string]interface{} {
	if !dl.enabled {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	dl.mutex.RLock()
	loggerCount := len(dl.loggers)
	dl.mutex.RUnlock()

	// 获取通道状态
	channelLen := len(dl.logChannel)
	channelCap := cap(dl.logChannel)

	return map[string]interface{}{
		"enabled":          true,
		"active_loggers":   loggerCount,
		"channel_length":   channelLen,
		"channel_capacity": channelCap,
		"channel_usage":    fmt.Sprintf("%.1f%%", float64(channelLen)/float64(channelCap)*100),
		"log_format":       dl.config.Logging.Format,
		"rotation_config": map[string]interface{}{
			"max_size":    dl.config.Logging.Rotation.MaxSize,
			"max_age":     dl.config.Logging.Rotation.MaxAge,
			"max_backups": dl.config.Logging.Rotation.MaxBackups,
			"compress":    dl.config.Logging.Rotation.Compress,
		},
	}
}

// CleanupOldLoggers 清理长时间未使用的日志写入器
// 定期清理不活跃的设备日志写入器，释放资源
func (dl *DeviceLogger) CleanupOldLoggers() {
	if !dl.enabled {
		return
	}

	dl.mutex.Lock()
	defer dl.mutex.Unlock()

	// 如果日志写入器数量不多，不需要清理
	if len(dl.loggers) <= 100 {
		return
	}

	logger.Infof("Cleaning up old device loggers, current count: %d", len(dl.loggers))

	// 简单策略：保留最近的50个日志写入器，关闭其他的
	if len(dl.loggers) > 50 {
		count := 0
		newLoggers := make(map[string]*lumberjack.Logger)

		for deviceID, logWriter := range dl.loggers {
			if count < 50 {
				newLoggers[deviceID] = logWriter
				count++
			} else {
				// 关闭旧的日志写入器
				if logWriter != nil {
					logWriter.Close()
				}
			}
		}

		dl.loggers = newLoggers
		logger.Infof("Device logger cleanup completed, remaining count: %d", len(dl.loggers))
	}
}
