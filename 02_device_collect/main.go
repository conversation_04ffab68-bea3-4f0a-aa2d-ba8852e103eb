/**
 * 设备数据采集服务
 *
 * 功能概述：
 * 本服务是制造业数据采集系统的核心组件，负责接收来自各种工业设备的实时数据，
 * 并将其临时存储到Redis缓存中，为后续的数据清洗和处理服务提供数据源
 *
 * 核心功能：
 * - 数据接收：通过HTTP POST接口接收设备发送的JSON格式数据
 * - 数据验证：验证数据格式、必要字段和数据完整性
 * - 序列管理：生成唯一且有序的Redis key，确保数据处理顺序
 * - 缓存存储：将验证后的数据存储到Redis，等待后续处理
 * - 健康监控：提供健康检查、统计信息和系统状态接口
 *
 * 技术特性：
 * - 高并发处理：支持数千台设备同时发送数据，采用goroutine池
 * - 数据有序性：使用时间戳+原子序列号确保FIFO顺序
 * - 系统可靠性：包含完整的错误处理、日志记录和异常恢复
 * - 服务可观测：提供详细的健康检查和运行统计接口
 * - 稳定性保证：内存监控、熔断机制、panic恢复、优雅关闭
 * - 降级能力：Redis不可用时自动切换到本地缓存模式
 * - FIFO保证：严格保证先进先出的数据处理顺序
 *
 * 架构设计：
 * - 分层架构：Handler -> Service -> Storage的清晰分层
 * - 中间件模式：请求日志、错误处理、性能监控中间件
 * - 配置驱动：所有参数可通过配置文件调整
 * - 微服务设计：独立部署、水平扩展、服务发现
 *
 * 性能优化：
 * - 连接池：Redis连接池复用，减少连接开销
 * - 批量操作：支持批量数据写入，提高吞吐量
 * - 内存管理：智能GC触发，防止内存泄露
 * - 异步处理：非阻塞的数据处理流程
 *
 * 业务场景：
 * - 设备数据采集：FANUC、西门子等工业设备的实时数据采集
 * - 生产监控：设备状态、产量、程序等关键指标的实时收集
 * - 数据预处理：为数据清洗服务提供原始数据源
 * - 系统集成：与MES、ERP等上层系统的数据集成
 *
 * 部署特性：
 * - Docker容器化：支持容器化部署和编排
 * - 配置外部化：配置文件外挂，支持不同环境配置
 * - 日志轮转：自动日志轮转，防止磁盘空间耗尽
 * - 健康检查：支持K8s健康检查和服务发现
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

import (
	"context"   // 用于上下文管理和超时控制
	"net/http"  // HTTP服务器功能
	"os"        // 操作系统接口，用于信号处理
	"os/signal" // 系统信号处理，用于优雅关闭
	"syscall"   // 系统调用，用于信号定义
	"time"      // 时间处理，用于超时控制

	"shared/config" // 共享配置模块
	"shared/logger" // 共享日志模块
)

// main 主函数 - 设备数据采集服务入口点
// 负责初始化配置、日志、稳定性管理器、服务实例等
// 启动HTTP服务器并处理优雅关闭
//
// 启动流程：
//  1. 加载配置文件
//  2. 初始化日志系统
//  3. 初始化序列配置
//  4. 创建稳定性管理器
//  5. 创建服务实例
//  6. 初始化Redis连接（支持降级）
//  7. 设置HTTP路由和启动服务器
//  8. 等待系统中断信号
//  9. 优雅关闭服务器
func main() {
	// 第一步：加载配置文件
	// 从config.yml文件中读取服务配置，包括端口、Redis连接信息等
	cfg, err := config.LoadConfig("config.yml")
	if err != nil {
		// 配置加载失败是致命错误，服务无法启动
		logger.Fatalf("Failed to load config: %v", err)
	}

	// 第二步：初始化日志系统
	// 根据配置设置日志级别、格式和输出目标，支持日志轮转
	logger.InitLoggerWithRotation(
		cfg.Logging.Level,
		cfg.Logging.Format,
		cfg.Logging.Output,
		logger.LogRotationConfig{
			Enabled:    cfg.Logging.Rotation.Enabled,
			MaxSize:    cfg.Logging.Rotation.MaxSize,
			MaxAge:     cfg.Logging.Rotation.MaxAge,
			MaxBackups: cfg.Logging.Rotation.MaxBackups,
			Compress:   cfg.Logging.Rotation.Compress,
		},
	)
	logger.Infof("Starting %s service on %s", cfg.Service.Name, cfg.GetServiceAddr())

	// 第三步：初始化序列配置
	// 从配置文件读取序列计数器最大值，如果未配置则使用默认值
	initSequenceConfig(cfg)

	// 第四步：创建稳定性管理器
	// 初始化内存监控和熔断机制，提高系统稳定性
	// 从配置文件读取稳定性参数，支持根据实际环境调整
	stabilityManager := NewStabilityManager(cfg.Stability.MaxMemoryMB, cfg.Stability.GCThresholdMB)

	// 第五步：创建服务实例
	// 初始化设备数据采集服务的核心结构体
	service := NewDeviceCollectService(cfg, stabilityManager)

	// 第六步：启动设备数据日志器（独立于Redis）
	// 设备日志器不依赖Redis，可以独立运行
	service.deviceLogger.Start()

	// 第七步：初始化Redis连接
	// Redis用于临时存储设备数据，等待后续处理
	// 如果Redis连接失败，服务将以降级模式运行
	if err := service.initRedis(); err != nil {
		logger.Warnf("Redis initialization failed, service running in degraded mode: %v", err)
	}

	// 第七步：设置HTTP路由和启动服务器
	// 配置路由、中间件，启动HTTP服务器
	router := service.setupRoutes()
	server := &http.Server{
		Addr:    cfg.GetServiceAddr(),
		Handler: router,
	}

	// 在单独的goroutine中启动服务器，避免阻塞主线程
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	logger.Infof("Device collect service started on %s", cfg.GetServiceAddr())

	// 第八步：等待系统中断信号
	// 创建信号通道，用于接收操作系统的关闭信号
	quit := make(chan os.Signal, 1)
	// 注册要监听的信号：SIGINT (Ctrl+C) 和 SIGTERM (kill命令)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	// 阻塞等待信号到达
	<-quit

	logger.Info("Shutting down server...")

	// 第九步：优雅关闭服务器和相关组件
	// 首先停止设备监控器
	if service.deviceMonitor != nil {
		service.deviceMonitor.Stop()
	}

	// 停止设备数据日志器
	if service.deviceLogger != nil {
		service.deviceLogger.Stop()
	}

	// 创建带超时的上下文，给服务器5秒时间完成当前请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel() // 确保上下文资源被释放

	// 调用服务器的优雅关闭方法
	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}

	logger.Info("Server exited")
}
