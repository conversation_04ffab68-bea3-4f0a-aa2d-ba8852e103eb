services:
  mdc_server_admin:
    image: mdc_server_admin:1.1.9
    container_name: mdc_server_admin
    ports:
      - "9101:80"
    restart: always
    # 如果有环境变量需要配置，可以取消下面的注释
    # environment:
    #   - NODE_ENV=production
    # 如果需要运行时配置，可以挂载config.js
    volumes:
      # 挂载配置文件
      - ./public/config.js:/usr/share/nginx/html/config.js 
      # 图标
      - ./public/logo192.png:/usr/share/nginx/html/logo192.png
      - ./public/logo512.png:/usr/share/nginx/html/logo512.png
      # 收藏夹图标
      - ./public/favicon.ico:/usr/share/nginx/html/favicon.ico
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    # 网络配置
    networks:
      - mdc_full_mdc_network

# 网络配置
networks:
  mdc_full_mdc_network:
    external: true
