#!/bin/bash

# 确保脚本在出错时停止执行
set -e

# 镜像名称和标签
TAG="1.1.9"

IMAGE_NAME="mdc_server_admin"
PLATFORM="linux/amd64"

echo "构建 Docker 镜像..."

# 设置Node.js环境
export PATH=/opt/homebrew/bin:$PATH

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ 错误: Node.js未安装或不在PATH中"
    echo "请确保Node.js已正确安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm未安装或不在PATH中"
    echo "请确保npm已正确安装"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"
echo "✅ npm版本: $(npm --version)"

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    echo "请确保在13_server_admin目录中运行此脚本"
    exit 1
fi

# 检查node_modules是否存在
if [ ! -d "node_modules" ]; then
    echo "📦 安装npm依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 错误: npm依赖安装失败"
        exit 1
    fi
else
    echo "✅ npm依赖已存在"
fi

echo "🔨 开始构建React应用..."
npm run build

# 启用 Docker BuildKit
export DOCKER_BUILDKIT=1

# 使用 DOCKER_DEFAULT_PLATFORM 环境变量指定目标平台
export DOCKER_DEFAULT_PLATFORM=${PLATFORM}

# 构建镜像
docker build -t ${IMAGE_NAME}:${TAG} .

echo "Docker 镜像构建完成: ${IMAGE_NAME}:${TAG}"
echo "可以使用以下命令运行容器:"
echo "  docker compose up -d" 

# 更新 ./docker_push_mdc_server_admin.sh 中的镜像版本号
# 确保 docker_push_mdc_server_admin.sh 文件存在并且路径正确
if [ -f "docker_push_mdc_server_admin.sh" ]; then
    sed -i '' "s/IMAGE_VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/IMAGE_VERSION=\"${TAG}\"/" docker_push_mdc_server_admin.sh
else
    echo "错误: docker_push_mdc_server_admin.sh 文件不存在"
fi


# 更新 ./docker-compose.yml 中的镜像版本号
#services:
#  server_admin:
#    image: server_admin:1.0.0
# 确保 docker-compose.yml 文件存在并且路径正确
if [ -f "docker-compose.yml" ]; then
    sed -i '' "s/image: ${IMAGE_NAME}:[0-9]*\.[0-9]*\.[0-9]*/image: ${IMAGE_NAME}:${TAG}/" docker-compose.yml
else
    echo "错误: docker-compose.yml 文件不存在"
fi
