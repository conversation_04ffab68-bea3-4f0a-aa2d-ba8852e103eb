# 环境配置说明

## 概述

13_server_admin 使用环境变量来管理不同环境下的配置，支持开发、测试、生产等多种环境。

## 配置文件

### 主配置文件
- `.env` - 默认配置文件，包含所有环境的基础配置
- `.env.local` - 本地配置文件（不提交到版本控制）

### 环境特定配置文件
- `.env.development` - 开发环境配置
- `.env.test` - 测试环境配置  
- `.env.production` - 生产环境配置

## 配置项说明

### API服务配置
```bash
# API服务基础URL
REACT_APP_API_BASE_URL=http://mdc_server_api:9005

# API请求超时时间（毫秒）
REACT_APP_API_TIMEOUT=10000
```

### 应用配置
```bash
# 应用名称
REACT_APP_APP_NAME=制造数据中心管理系统

# 应用版本
REACT_APP_APP_VERSION=1.0.0

# 公司名称
REACT_APP_COMPANY_NAME=制造数据中心

# 运行环境
REACT_APP_ENV=development
```

### 开发服务器配置
```bash
# 服务器端口
PORT=3302

# 是否自动打开浏览器
BROWSER=none

# 是否生成源码映射
GENERATE_SOURCEMAP=true
```

### 功能开关
```bash
# 是否启用Mock数据
REACT_APP_ENABLE_MOCK=false

# 是否启用调试模式
REACT_APP_ENABLE_DEBUG=true

# 是否启用数据分析
REACT_APP_ENABLE_ANALYTICS=false
```

### 主题配置
```bash
# 主色调
REACT_APP_PRIMARY_COLOR=#1890ff

# 成功色
REACT_APP_SUCCESS_COLOR=#52c41a

# 警告色
REACT_APP_WARNING_COLOR=#faad14

# 错误色
REACT_APP_ERROR_COLOR=#ff4d4f
```

## 使用方法

### 1. 开发环境
```bash
# 使用开发环境配置
npm start

# 或者显式指定环境
NODE_ENV=development npm start
```

### 2. 测试环境
```bash
# 使用测试环境配置
NODE_ENV=test npm start
```

### 3. 生产环境
```bash
# 构建生产版本
NODE_ENV=production npm run build

# 预览生产版本
npm run preview
```

### 4. 自定义配置
创建 `.env.local` 文件来覆盖默认配置：
```bash
# .env.local
REACT_APP_API_BASE_URL=http://192.168.1.100:9005
REACT_APP_ENABLE_DEBUG=true
```

## 配置优先级

React 会按以下优先级加载环境变量：

1. `.env.development.local`, `.env.local`, `.env.development`, `.env` (开发环境)
2. `.env.production.local`, `.env.local`, `.env.production`, `.env` (生产环境)
3. `.env.test.local`, `.env.test`, `.env` (测试环境)

## 注意事项

### 1. 环境变量命名
- 所有自定义环境变量必须以 `REACT_APP_` 开头
- 变量名使用大写字母和下划线

### 2. 安全性
- 不要在环境变量中存储敏感信息（如密码、密钥）
- `.env.local` 文件不应提交到版本控制系统

### 3. 类型转换
- 环境变量都是字符串类型
- 在配置文件中进行适当的类型转换

### 4. 重启要求
- 修改环境变量后需要重启开发服务器
- 生产环境需要重新构建

## 示例配置

### 开发环境示例
```bash
# 本地开发
REACT_APP_API_BASE_URL=http://mdc_server_api:9005
REACT_APP_ENABLE_DEBUG=true
REACT_APP_ENABLE_MOCK=false
```

### 测试环境示例
```bash
# 测试服务器
REACT_APP_API_BASE_URL=http://test-api.company.com
REACT_APP_ENABLE_DEBUG=true
REACT_APP_ENABLE_MOCK=true
```

### 生产环境示例
```bash
# 生产服务器
REACT_APP_API_BASE_URL=https://api.company.com
REACT_APP_ENABLE_DEBUG=false
REACT_APP_ENABLE_ANALYTICS=true
```

## 配置验证

应用启动时会自动验证配置：
- 检查必需的环境变量是否存在
- 验证URL格式是否正确
- 确认数值类型的有效性

## 故障排除

### 1. 环境变量不生效
- 检查变量名是否以 `REACT_APP_` 开头
- 确认是否重启了开发服务器
- 验证配置文件语法是否正确

### 2. API连接失败
- 检查 `REACT_APP_API_BASE_URL` 是否正确
- 确认后端服务是否正常运行
- 验证网络连接和防火墙设置

### 3. 配置冲突
- 检查多个配置文件中的重复设置
- 确认配置优先级是否符合预期
- 使用浏览器开发者工具查看实际配置值
