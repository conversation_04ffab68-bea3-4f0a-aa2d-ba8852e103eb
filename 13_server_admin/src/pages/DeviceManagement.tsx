import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Space,
  Popconfirm,
  Card,
  Row,
  Col,
  Statistic,
  Divider,
  Tabs,
  Tag,
  Tooltip,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { deviceAPI, Device } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;

const DeviceManagement: React.FC = () => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDevice, setEditingDevice] = useState<Device | null>(null);
  const [dataTypeOptions, setDataTypeOptions] = useState<Array<{
    value: string;
    label: string;
    description: string;
  }>>([]);
  const [form] = Form.useForm();

  // 搜索和筛选状态（保留用于搜索功能）
  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');

  // 获取设备列表
  const fetchDevices = async () => {
    setLoading(true);
    try {
      const response = await deviceAPI.getDevices();
      const deviceList = (response.data as any)?.data || [];
      // 确保返回的数据是数组，如果不是则设置为空数组
      setDevices(Array.isArray(deviceList) ? deviceList : []);
    } catch (error) {
      message.error('获取设备列表失败');
      console.error('Error fetching devices:', error);
      // 发生错误时设置为空数组
      setDevices([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取系统类型选项
  const fetchDataTypeOptions = async () => {
    try {
      console.log('正在获取系统类型选项...');
      const response = await deviceAPI.getDataTypeOptions();
      console.log('API响应:', response);
      console.log('响应数据:', response.data);

      // 检查不同的响应格式
      if (response.data) {
        // 如果响应直接是数组
        if (Array.isArray(response.data)) {
          console.log('响应是数组格式，直接使用');
          setDataTypeOptions(response.data);
          return;
        }

        // 如果响应有success字段且为true
        if ((response.data as any)?.success && (response.data as any)?.data) {
          console.log('响应有success字段且为true，使用data字段');
          setDataTypeOptions((response.data as any).data);
          return;
        }

        // 如果响应有data字段但没有success字段
        if ((response.data as any)?.data && Array.isArray((response.data as any).data)) {
          console.log('响应有data字段，使用data字段');
          setDataTypeOptions((response.data as any).data);
          return;
        }
      }

      console.log('API响应格式不符合预期，使用默认选项');
    } catch (error) {
      console.error('获取系统类型选项失败:', error);
    }
  };

  useEffect(() => {
    // 先设置默认选项，确保下拉框有内容
    // 然后获取数据
    fetchDevices();
    fetchDataTypeOptions();
  }, []);

  // 搜索功能
  const getColumnSearchProps = (dataIndex: string, placeholder: string) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
      <div style={{ padding: 8 }}>
        <Input
          placeholder={`搜索 ${placeholder}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            搜索
          </Button>
          <Button
            onClick={() => handleReset(clearFilters, confirm)}
            size="small"
            style={{ width: 90 }}
          >
            重置
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value: any, record: Device) =>
      record[dataIndex as keyof Device]
        ? String(record[dataIndex as keyof Device]).toLowerCase().includes(String(value).toLowerCase())
        : false,
  });

  const handleSearch = (selectedKeys: any, confirm: any, dataIndex: string) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: any, confirm: any) => {
    clearFilters();
    setSearchText('');
    confirm();
  };

  // 验证设备ID和IP是否重复
  const validateDeviceUniqueness = (values: Device): string | null => {
    const { device_id, ip } = values;

    // 检查device_id是否重复
    const duplicateDeviceId = devices.find(device =>
      device.device_id === device_id &&
      (!editingDevice || (device.id || device._id) !== (editingDevice.id || editingDevice._id))
    );

    if (duplicateDeviceId) {
      return `设备ID "${device_id}" 已存在，请使用不同的设备ID`;
    }

    // 检查IP是否重复（如果提供了IP）
    if (ip) {
      const duplicateIp = devices.find(device =>
        device.ip === ip &&
        (!editingDevice || (device.id || device._id) !== (editingDevice.id || editingDevice._id))
      );

      if (duplicateIp) {
        return `IP地址 "${ip}" 已被设备 "${duplicateIp.name}" 使用，请使用不同的IP地址`;
      }
    }

    return null;
  };

  // 处理表单验证失败
  const handleSubmitFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
    const { errorFields } = errorInfo;
    if (errorFields && errorFields.length > 0) {
      const firstError = errorFields[0];
      const fieldName = firstError.name[0];
      const errorMessage = firstError.errors[0];
      message.error(`${fieldName}: ${errorMessage}`);
    } else {
      message.error('表单验证失败，请检查输入内容');
    }
  };

  // 创建或更新设备
  const handleSubmit = async (values: Device) => {
    try {
      // 验证必填项（仅在需要网络连接的设备类型中验证IP和端口）
      const networkRequiredTypes = ['fanuc_30i', 'siemens_840d', 'plc_modbus', 'sensor_tcp'];
      const requiresNetwork = networkRequiredTypes.includes(values.data_type);

      if (requiresNetwork) {
        if (!values.ip) {
          message.error('此设备类型需要IP地址，请填写IP地址');
          return;
        }

        if (!values.port) {
          message.error('此设备类型需要端口号，请填写端口号');
          return;
        }
      }

      // 验证唯一性
      const validationError = validateDeviceUniqueness(values);
      if (validationError) {
        message.error(validationError);
        return;
      }

      // 关键修复：确保提交的数据包含所有必要字段
      // 如果用户没有切换到"采集"标签页，某些字段可能不在values中
      let submitData = { ...values };

      if (editingDevice) {
        // 编辑模式：确保布尔字段有正确的值
        // 如果values中没有这些字段，使用原始设备数据或默认值
        submitData = {
          ...editingDevice,  // 先使用原始设备数据
          ...values,         // 再覆盖用户修改的字段
          // 确保关键字段有正确的值
          enabled: values.enabled !== undefined ? values.enabled :
            (editingDevice.enabled !== undefined ? editingDevice.enabled : true),
          auto_start: values.auto_start !== undefined ? values.auto_start :
            (editingDevice.auto_start !== undefined ? editingDevice.auto_start : true),
          collect_interval: values.collect_interval || editingDevice.collect_interval || 3000,
          timeout: values.timeout || editingDevice.timeout || 10,
          retry_count: values.retry_count !== undefined ? values.retry_count :
            (editingDevice.retry_count !== undefined ? editingDevice.retry_count : 3),
          retry_delay: values.retry_delay || editingDevice.retry_delay || 15,
          sort_order: values.sort_order !== undefined ? values.sort_order :
            (editingDevice.sort_order || 0),
        };

        // 使用MongoDB ObjectID进行更新
        const deviceId = editingDevice.id || editingDevice._id;
        if (!deviceId) {
          message.error('设备ID不存在，无法更新');
          return;
        }
        await deviceAPI.updateDevice(deviceId, submitData);
        message.success('设备更新成功');
      } else {
        // 新增模式：确保有默认值
        submitData = {
          ...values,
          enabled: values.enabled !== undefined ? values.enabled : true,
          auto_start: values.auto_start !== undefined ? values.auto_start : true,
          collect_interval: values.collect_interval || 3000,
          timeout: values.timeout || 10,
          retry_count: values.retry_count !== undefined ? values.retry_count : 3,
          retry_delay: values.retry_delay || 15,
          sort_order: values.sort_order || 0,
          status: values.status || 'active',
        };
        await deviceAPI.createDevice(submitData);
        message.success('设备创建成功');
      }
      setModalVisible(false);
      setEditingDevice(null);
      form.resetFields();
      fetchDevices();
    } catch (error: any) {
      console.error('Error saving device:', error);
      const errorMsg = error.response?.data?.error || error.message;
      message.error(editingDevice ? `设备更新失败: ${errorMsg}` : `设备创建失败: ${errorMsg}`);
    }
  };

  // 删除设备
  const handleDelete = async (device: Device) => {
    try {
      const deviceId = device.id || device._id;
      if (!deviceId) {
        message.error('设备ID不存在，无法删除');
        return;
      }
      await deviceAPI.deleteDevice(deviceId);
      message.success('设备删除成功');
      fetchDevices();
    } catch (error: any) {
      console.error('Error deleting device:', error);
      const errorMsg = error.response?.data?.error || error.message;
      message.error(`设备删除失败: ${errorMsg}`);
    }
  };

  // 生成UUID的函数
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : ((r & 0x3) | 0x8);
      return v.toString(16);
    });
  };

  // 打开编辑模态框
  const handleEdit = (device: Device) => {
    setEditingDevice(device);

    // 处理布尔字段的默认值，确保Switch组件正确显示
    const formValues = {
      ...device,
      // 如果enabled字段未定义，默认为true
      enabled: device.enabled !== undefined ? device.enabled : true,
      // 如果auto_start字段未定义，默认为true
      auto_start: device.auto_start !== undefined ? device.auto_start : true,
      // 确保数值字段有默认值
      collect_interval: device.collect_interval || 3000,
      timeout: device.timeout || 10,
      retry_count: device.retry_count !== undefined ? device.retry_count : 3,
      retry_delay: device.retry_delay || 15,
      sort_order: device.sort_order || 0,
    };

    form.setFieldsValue(formValues);
    setModalVisible(true);
  };

  // 打开新增模态框
  const handleAdd = () => {
    setEditingDevice(null);
    form.resetFields();
    // 新增时自动生成UUID
    form.setFieldsValue({
      device_id: generateUUID()
    });
    setModalVisible(true);
  };



  const columns = [
    // 设备ID列隐藏，不需要排序
    // {
    //   title: '设备ID',
    //   dataIndex: 'device_id',
    //   key: 'device_id',
    //   sorter: true,
    // },
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      sorter: true,
      ...getColumnSearchProps('name', '设备名称'),
    },
    {
      title: '系统类型',
      dataIndex: 'data_type',
      key: 'data_type',
      width: 120,
      filters: dataTypeOptions.map(option => ({
        text: option.label,
        value: option.value,
      })),
      onFilter: (value: any, record: Device) => record.data_type === value,
      render: (dataType: string) => {
        const option = dataTypeOptions.find(opt => opt.value === dataType);
        return (
          <Tooltip title={option?.description || dataType}>
            <Tag color="blue">{option?.label || dataType}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '设备位置',
      dataIndex: 'location',
      key: 'location',
      width: 140,
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      align: 'center' as const,
      render: (value: number) => value || 0,
      sorter: true,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 140,
      ...getColumnSearchProps('ip', 'IP地址'),
      render: (ip: string, record: Device) => (
        <div>
          <div>{ip || '-'}</div>
          {/* {record.port && <div style={{ fontSize: '12px', color: '#666' }}>端口: {record.port}</div>} */}
        </div>
      ),
    },
    {
      title: '启用采集',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 90,
      align: 'center' as const,
      filters: [
        { text: '已启用', value: true },
        { text: '已禁用', value: false },
      ],
      onFilter: (value: any, record: Device) => record.enabled === value,
      render: (enabled: boolean) => (
        <Tag color={enabled ? 'green' : 'red'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '自动启动',
      dataIndex: 'auto_start',
      key: 'auto_start',
      width: 90,
      align: 'center' as const,
      filters: [
        { text: '已启用', value: true },
        { text: '已禁用', value: false },
      ],
      onFilter: (value: any, record: Device) => record.auto_start === value,
      render: (autoStart: boolean) => (
        <Tag color={autoStart ? 'blue' : 'gray'}>
          {autoStart ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '采集间隔',
      dataIndex: 'collect_interval',
      key: 'collect_interval',
      width: 90,
      align: 'center' as const,
      render: (interval: number) => (
        <span>{interval ? `${interval}ms` : '-'}</span>
      ),
      sorter: true,
    },
    // 创建时间列隐藏，不显示
    // {
    //   title: '创建时间',
    //   dataIndex: 'created_at',
    //   key: 'created_at',
    //   render: (date: string) => date ? new Date(date).toLocaleString() : '-',
    // },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: Device) => (
        <Space size="small" >
          <Button
            type="link"
            icon={< EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            编辑
          </Button>
          < Popconfirm
            title="确定要删除这个设备吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={< DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 统计卡片 */}
      < Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="设备总数"
              value={Array.isArray(devices) ? devices.length : 0}
              suffix="台"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        < Col span={6} >
          <Card>
            <Statistic
              title="启用采集"
              value={Array.isArray(devices) ? devices.filter(d => d.enabled).length : 0}
              suffix="台"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        < Col span={6} >
          <Card>
            <Statistic
              title="自动启动"
              value={Array.isArray(devices) ? devices.filter(d => d.auto_start).length : 0}
              suffix="台"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        < Col span={6} >
          <Card>
            <Statistic
              title="平均间隔"
              value={Array.isArray(devices) && devices.length > 0 ? Math.round(devices.reduce((sum, d) => sum + (d.collect_interval || 0), 0) / devices.length) : 0}
              suffix="ms"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={< PlusOutlined />}
            onClick={handleAdd}
          >
            新增设备
          </Button>
          < Button
            icon={< ReloadOutlined />}
            onClick={fetchDevices}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 设备表格 */}
      <Table
        columns={columns}
        dataSource={devices}
        rowKey="device_id"
        loading={loading}
        scroll={{ x: 1200 }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
      />

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingDevice ? '编辑设备' : '新增设备'}
        open={modalVisible}
        onCancel={() => { }} // 空函数，禁用默认关闭行为
        closable={false}
        maskClosable={false}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onFinishFailed={handleSubmitFailed}
        >
          <Tabs
            defaultActiveKey="basic"
            items={[
              {
                key: 'basic',
                label: '设备信息',
                children: (
                  <>
                    <Form.Item
                      name="device_id"
                      label="设备ID"
                      rules={[
                        { required: true, message: '设备ID不能为空' },
                        {
                          validator: async (_, value) => {
                            if (value) {
                              const duplicateDevice = devices.find(device =>
                                device.device_id === value &&
                                (!editingDevice || (device.id || device._id) !== (editingDevice.id || editingDevice._id))
                              );
                              if (duplicateDevice) {
                                throw new Error(`设备ID "${value}" 已存在，请使用不同的设备ID`);
                              }
                            }
                          }
                        }
                      ]}
                      tooltip="设备ID为系统自动生成的UUID，不可修改"
                    >
                      <Input
                        placeholder="系统自动生成UUID"
                        readOnly
                        style={{ backgroundColor: '#f5f5f5' }}
                      />
                    </Form.Item>

                    <Form.Item
                      name="name"
                      label="设备名称"
                      rules={[{ required: true, message: '请输入设备名称' }]}
                    >
                      <Input placeholder="请输入设备名称" />
                    </Form.Item>

                    <Form.Item
                      name="location"
                      label="设备位置"
                      rules={[{ required: true, message: '请输入设备位置' }]}
                    >
                      <Input placeholder="请输入设备位置" />
                    </Form.Item>

                    <Form.Item
                      name="sort_order"
                      label="显示排序"
                      initialValue={0}
                      tooltip="控制设备在30_machine_status主页的显示顺序，数字越小越靠前"
                      rules={[
                        { type: 'number', min: 0, max: 9999, message: '排序值范围0-9999' }
                      ]}
                    >
                      <InputNumber
                        placeholder="0"
                        style={{ width: '100%' }}
                        min={0}
                        max={9999}
                      />
                    </Form.Item>

                    <Divider orientation="left">设备元数据</Divider>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="brand"
                          label="设备品牌"
                          tooltip="用于数据推送时的metadata字段，如'Fanuc'"
                        >
                          <Input placeholder="请输入设备品牌" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="model"
                          label="设备型号详情"
                          tooltip="用于数据推送时的metadata字段，如'Fanuc 30i'"
                        >
                          <Input placeholder="请输入设备型号详情" />
                        </Form.Item>
                      </Col>
                    </Row>

                    {/* 设备状态字段隐藏，默认为active */}
                    <Form.Item
                      name="status"
                      initialValue="active"
                      style={{ display: 'none' }}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      name="description"
                      label="设备描述"
                    >
                      <TextArea
                        rows={4}
                        placeholder="请输入设备描述"
                      />
                    </Form.Item>
                  </>
                )
              },
              {
                key: 'collection',
                label: '采集',
                children: (
                  <>
                    <Form.Item
                      name="data_type"
                      label="系统类型"
                      rules={[{ required: true, message: '请选择系统类型' }]}
                      tooltip="选择设备的数据采集类型，用于确定采集协议和数据格式"
                    >
                      <Select
                        placeholder="请选择系统类型"
                        optionLabelProp="label"
                      >
                        {dataTypeOptions.map(option => (
                          <Option
                            key={option.value}
                            value={option.value}
                            label={option.label}
                            title={option.description}
                          >
                            <div>
                              <div>{option.label}</div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                {option.description}
                              </div>
                            </div>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Divider orientation="left">网络配置</Divider>

                    <Row gutter={16}>
                      <Col span={16}>
                        <Form.Item
                          name="ip"
                          label="IP地址"
                          rules={[
                            { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址' },
                            {
                              validator: async (_, value) => {
                                if (value) {
                                  const duplicateDevice = devices.find(device =>
                                    device.ip === value &&
                                    (!editingDevice || (device.id || device._id) !== (editingDevice.id || editingDevice._id))
                                  );
                                  if (duplicateDevice) {
                                    throw new Error(`IP地址 "${value}" 已被设备 "${duplicateDevice.name}" 使用，请使用不同的IP地址`);
                                  }
                                }
                              }
                            }
                          ]}
                          tooltip="网络设备需要填写IP地址，如FANUC、西门子等"
                        >
                          <Input placeholder="请输入IP地址，如：************" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="port"
                          label="端口"
                          rules={[
                            { type: 'number', min: 1, max: 65535, message: '端口范围1-65535' }
                          ]}
                          tooltip="网络设备需要填写端口号"
                        >
                          <InputNumber placeholder="1000" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Divider orientation="left">采集配置</Divider>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="enabled"
                          label="启用采集"
                          valuePropName="checked"
                          initialValue={true}
                        >
                          <Switch />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="auto_start"
                          label="自动启动"
                          valuePropName="checked"
                          initialValue={true}
                        >
                          <Switch />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="collect_interval"
                          label="采集间隔(ms)"
                          initialValue={3000}
                          rules={[
                            { type: 'number', min: 100, message: '采集间隔不能小于100ms' }
                          ]}
                        >
                          <InputNumber placeholder="3000" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="timeout"
                          label="超时时间(s)"
                          initialValue={10}
                          rules={[
                            { type: 'number', min: 1, message: '超时时间不能小于1秒' }
                          ]}
                        >
                          <InputNumber placeholder="10" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="retry_count"
                          label="重试次数"
                          initialValue={3}
                          rules={[
                            { type: 'number', min: 0, max: 10, message: '重试次数范围0-10' }
                          ]}
                        >
                          <InputNumber placeholder="3" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="retry_delay"
                          label="重试延迟(s)"
                          initialValue={15}
                          rules={[
                            { type: 'number', min: 1, message: '重试延迟不能小于1秒' }
                          ]}
                        >
                          <InputNumber placeholder="15" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>

                  </>
                )
              }
            ]}
          />

          <Form.Item style={{ marginTop: 24 }}>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingDevice ? '更新' : '创建'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingDevice(null);
                form.resetFields();
              }}>
                关闭
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DeviceManagement;
