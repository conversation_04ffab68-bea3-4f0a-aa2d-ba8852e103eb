import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Tag,
  Card,
  Row,
  Col,
  Typography,
  Tooltip,
  Grid,
  Progress,
  Statistic,
  Calendar,
  Badge
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  CalendarOutlined,
  BarChartOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { dailyPlanAPI, deviceAPI, productAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { useBreakpoint } = Grid;

interface DailyPlan {
  id: string;
  plan_date: string;
  device_id: string;
  device_name: string;
  product_id: string;
  product_name: string;
  planned_quantity: number;
  actual_quantity: number;
  completion_rate: number;
  shift_type: string;
  operator_name: string;
  status: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

const DailyPlanManagement: React.FC = () => {
  const [plans, setPlans] = useState<DailyPlan[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [progressModalVisible, setProgressModalVisible] = useState(false);
  const [editingPlan, setEditingPlan] = useState<DailyPlan | null>(null);
  const [form] = Form.useForm();
  const [progressForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [dateFilter, setDateFilter] = useState(dayjs().format('YYYY-MM-DD'));
  const [devices, setDevices] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'table' | 'calendar'>('table');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // 获取每日计划列表
  const fetchPlans = async (params?: any) => {
    setLoading(true);
    try {
      const response = await dailyPlanAPI.getPlans({
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText,
        ...params,
      });

      // 确保返回的数据是数组，如果不是则设置为空数组
      const plansData = (response.data as any)?.data;
      setPlans(Array.isArray(plansData) ? plansData : []);
      setPagination(prev => ({
        ...prev,
        total: (response.data as any).total,
        current: (response.data as any).page,
      }));
    } catch (error: any) {
      message.error('获取每日计划列表失败');
      console.error('Fetch plans error:', error);
      // 发生错误时设置为空数组
      setPlans([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取指定日期的计划
  const fetchPlansByDate = async (date: string) => {
    try {
      const response = await dailyPlanAPI.getPlansByDate(date);
      // 确保返回的数据是数组，如果不是则设置为空数组
      const plansData = (response.data as any)?.data;
      setPlans(Array.isArray(plansData) ? plansData : []);
    } catch (error: any) {
      message.error('获取计划失败');
      console.error('Fetch plans by date error:', error);
      // 发生错误时设置为空数组
      setPlans([]);
    }
  };

  // 获取基础数据
  const fetchBaseData = async () => {
    try {
      const [devicesRes, productsRes] = await Promise.all([
        deviceAPI.getDevices({ page_size: 1000 }),
        productAPI.getProducts({ page_size: 1000 }),
      ]);

      // 确保返回的数据都是数组，如果不是则设置为空数组
      const devicesData = (devicesRes.data as any)?.data;
      const productsData = (productsRes.data as any)?.data;

      setDevices(Array.isArray(devicesData) ? devicesData : []);
      setProducts(Array.isArray(productsData) ? productsData : []);
    } catch (error) {
      console.error('Fetch base data error:', error);
      // 发生错误时设置为空数组
      setDevices([]);
      setProducts([]);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const startDate = dayjs().startOf('month').format('YYYY-MM-DD');
      const endDate = dayjs().endOf('month').format('YYYY-MM-DD');
      const response = await dailyPlanAPI.getPlanStatistics(startDate, endDate);
      setStatistics((response.data as any).data);
    } catch (error) {
      console.error('Fetch statistics error:', error);
    }
  };

  useEffect(() => {
    if (viewMode === 'table') {
      fetchPlans();
    } else {
      fetchPlansByDate(dateFilter);
    }
    fetchBaseData();
    fetchStatistics();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, pagination.pageSize, viewMode, dateFilter]);

  // 搜索计划
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchPlans();
  };

  // 创建/编辑计划
  const handleSubmit = async (values: any) => {
    try {
      const planData = {
        ...values,
        plan_date: values.plan_date.toISOString(),
      };

      if (editingPlan) {
        await dailyPlanAPI.updatePlan(editingPlan.id, planData);
        message.success('每日计划更新成功');
      } else {
        await dailyPlanAPI.createPlan(planData);
        message.success('每日计划创建成功');
      }

      setModalVisible(false);
      setEditingPlan(null);
      form.resetFields();
      if (viewMode === 'table') {
        fetchPlans();
      } else {
        fetchPlansByDate(dateFilter);
      }
      fetchStatistics();
    } catch (error: any) {
      message.error(error.response?.data?.error || '操作失败');
    }
  };

  // 更新进度
  const handleUpdateProgress = async (values: any) => {
    if (!editingPlan) return;

    try {
      await dailyPlanAPI.updatePlanProgress(editingPlan.id, values);
      message.success('进度更新成功');

      setProgressModalVisible(false);
      setEditingPlan(null);
      progressForm.resetFields();
      if (viewMode === 'table') {
        fetchPlans();
      } else {
        fetchPlansByDate(dateFilter);
      }
      fetchStatistics();
    } catch (error: any) {
      message.error(error.response?.data?.error || '更新失败');
    }
  };

  // 删除计划
  const handleDelete = async (id: string) => {
    try {
      await dailyPlanAPI.deletePlan(id);
      message.success('每日计划删除成功');
      if (viewMode === 'table') {
        fetchPlans();
      } else {
        fetchPlansByDate(dateFilter);
      }
      fetchStatistics();
    } catch (error: any) {
      message.error(error.response?.data?.error || '删除失败');
    }
  };

  // 打开编辑模态框
  const handleEdit = (plan: DailyPlan) => {
    setEditingPlan(plan);
    form.setFieldsValue({
      plan_date: dayjs(plan.plan_date),
      device_id: plan.device_id,
      product_id: plan.product_id,
      planned_quantity: plan.planned_quantity,
      shift_type: plan.shift_type,
      operator_name: plan.operator_name,
      notes: plan.notes,
    });
    setModalVisible(true);
  };

  // 打开进度更新模态框
  const handleUpdateProgressModal = (plan: DailyPlan) => {
    setEditingPlan(plan);
    progressForm.setFieldsValue({
      actual_quantity: plan.actual_quantity,
      status: plan.status,
      notes: plan.notes,
    });
    setProgressModalVisible(true);
  };

  // 状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'blue';
      case 'in_progress': return 'orange';
      case 'completed': return 'green';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  };

  // 状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned': return '已计划';
      case 'in_progress': return '进行中';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  // 班次标签颜色
  const getShiftColor = (shift: string) => {
    switch (shift) {
      case 'morning': return 'blue';
      case 'afternoon': return 'orange';
      case 'night': return 'purple';
      default: return 'default';
    }
  };

  // 班次文本
  const getShiftText = (shift: string) => {
    switch (shift) {
      case 'morning': return '早班';
      case 'afternoon': return '中班';
      case 'night': return '夜班';
      default: return shift;
    }
  };

  // 获取设备名称
  const getDeviceName = (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    return device ? device.name : deviceId;
  };

  // 获取产品名称
  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId || p.product_id === productId);
    return product ? product.name : productId;
  };

  // 表格列定义
  const columns = [
    {
      title: '日期',
      dataIndex: 'plan_date',
      key: 'plan_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '设备',
      dataIndex: 'device_id',
      key: 'device_id',
      responsive: ['md'] as any,
      render: (deviceId: string) => (
        <div>
          <div style={{ fontWeight: 500 }}>{getDeviceName(deviceId)}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{deviceId}</div>
        </div>
      ),
    },
    {
      title: '产品',
      dataIndex: 'product_id',
      key: 'product_id',
      responsive: ['lg'] as any,
      render: (productId: string) => (
        <div>
          <div style={{ fontWeight: 500 }}>{getProductName(productId)}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{productId}</div>
        </div>
      ),
    },
    {
      title: '班次',
      dataIndex: 'shift_type',
      key: 'shift_type',
      render: (shift: string) => (
        <Tag color={getShiftColor(shift)}>
          {getShiftText(shift)}
        </Tag>
      ),
    },
    {
      title: '操作员',
      dataIndex: 'operator_name',
      key: 'operator_name',
      responsive: ['lg'] as any,
    },
    {
      title: '计划/实际',
      key: 'quantity',
      render: (_: any, record: DailyPlan) => (
        <div>
          <div>{record.planned_quantity.toLocaleString()}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.actual_quantity.toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: '完成率',
      key: 'completion_rate',
      render: (_: any, record: DailyPlan) => (
        <Progress
          percent={Math.round(record.completion_rate)}
          size="small"
          status={record.completion_rate >= 100 ? 'success' : 'active'}
        />
      ),
      responsive: ['md'] as any,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: DailyPlan) => (
        <Space size="small">
          <Tooltip title="更新进度">
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => handleUpdateProgressModal(record)}
              size="small"
              style={{ color: '#1890ff' }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个计划吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 日历单元格渲染
  const dateCellRender = (value: dayjs.Dayjs) => {
    const dateStr = value.format('YYYY-MM-DD');
    const dayPlans = (plans || []).filter(plan =>
      dayjs(plan.plan_date).format('YYYY-MM-DD') === dateStr
    );

    return (
      <div style={{ fontSize: '12px' }}>
        {dayPlans.map(plan => (
          <div key={plan.id} style={{ marginBottom: 2 }}>
            <Badge
              color={getStatusColor(plan.status)}
              text={`${getDeviceName(plan.device_id)} - ${Math.round(plan.completion_rate)}%`}
            />
          </div>
        ))}
      </div>
    );
  };

  return (
    <div>
      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="本月计划数"
                value={statistics.total_plans || 0}
                prefix={<CalendarOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="计划产量"
                value={statistics.total_planned || 0}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="实际产量"
                value={statistics.total_actual || 0}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="平均完成率"
                value={statistics.avg_completion_rate || 0}
                precision={1}
                suffix="%"
                valueStyle={{
                  color: (statistics.avg_completion_rate || 0) >= 90 ? '#3f8600' : '#cf1322'
                }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col>
            <Space>
              <Title level={4} style={{ margin: 0 }}>
                每日计划管理
              </Title>
              <Button.Group>
                <Button
                  type={viewMode === 'table' ? 'primary' : 'default'}
                  onClick={() => setViewMode('table')}
                >
                  列表视图
                </Button>
                <Button
                  type={viewMode === 'calendar' ? 'primary' : 'default'}
                  onClick={() => setViewMode('calendar')}
                >
                  日历视图
                </Button>
              </Button.Group>
            </Space>
          </Col>
          <Col>
            <Space wrap>
              {viewMode === 'calendar' && (
                <DatePicker
                  value={dayjs(dateFilter)}
                  onChange={(date) => setDateFilter(date?.format('YYYY-MM-DD') || dayjs().format('YYYY-MM-DD'))}
                  picker="month"
                />
              )}
              {viewMode === 'table' && (
                <Input.Search
                  placeholder="搜索设备、产品、操作员"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  onSearch={handleSearch}
                  style={{ width: isMobile ? 200 : 300 }}
                  enterButton={<SearchOutlined />}
                />
              )}
              <Button
                icon={<ReloadOutlined />}
                onClick={() => viewMode === 'table' ? fetchPlans() : fetchPlansByDate(dateFilter)}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingPlan(null);
                  form.resetFields();
                  setModalVisible(true);
                }}
              >
                新增计划
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Card style={{ marginTop: 16 }}>
        {viewMode === 'table' ? (
          <Table
            columns={columns}
            dataSource={plans}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                setPagination(prev => ({ ...prev, current: page, pageSize }));
              },
            }}
            scroll={{ x: 1000 }}
            size={isMobile ? 'small' : 'middle'}
          />
        ) : (
          <Calendar
            dateCellRender={dateCellRender}
            value={dayjs(dateFilter)}
            onPanelChange={(date) => setDateFilter(date.format('YYYY-MM-DD'))}
          />
        )}
      </Card>

      {/* 创建/编辑计划模态框 */}
      <Modal
        title={editingPlan ? '编辑每日计划' : '新增每日计划'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingPlan(null);
          form.resetFields();
        }}
        footer={null}
        width={isMobile ? '90%' : 600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="plan_date"
                label="计划日期"
                rules={[{ required: true, message: '请选择计划日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="shift_type"
                label="班次"
                rules={[{ required: true, message: '请选择班次' }]}
              >
                <Select>
                  <Select.Option value="morning">早班</Select.Option>
                  <Select.Option value="afternoon">中班</Select.Option>
                  <Select.Option value="night">夜班</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="device_id"
                label="生产设备"
                rules={[{ required: true, message: '请选择生产设备' }]}
              >
                <Select
                  placeholder="选择设备"
                  showSearch
                  optionFilterProp="children"
                >
                  {Array.isArray(devices) && devices.map(device => (
                    <Select.Option key={device.id} value={device.id}>
                      {device.name} - {device.location}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="product_id"
                label="产品"
                rules={[{ required: true, message: '请选择产品' }]}
              >
                <Select
                  placeholder="选择产品"
                  showSearch
                  optionFilterProp="children"
                >
                  {Array.isArray(products) && products.map(product => (
                    <Select.Option key={product.id} value={product.id}>
                      {product.name} - {product.model}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="planned_quantity"
                label="计划数量"
                rules={[{ required: true, message: '请输入计划数量' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="operator_name"
                label="操作员"
                rules={[{ required: true, message: '请输入操作员姓名' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingPlan ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 更新进度模态框 */}
      <Modal
        title="更新生产进度"
        open={progressModalVisible}
        onCancel={() => {
          setProgressModalVisible(false);
          setEditingPlan(null);
          progressForm.resetFields();
        }}
        footer={null}
        width={isMobile ? '90%' : 500}
      >
        <Form
          form={progressForm}
          layout="vertical"
          onFinish={handleUpdateProgress}
        >
          <Form.Item
            name="actual_quantity"
            label="实际产量"
            rules={[{ required: true, message: '请输入实际产量' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
          >
            <Select>
              <Select.Option value="planned">已计划</Select.Option>
              <Select.Option value="in_progress">进行中</Select.Option>
              <Select.Option value="completed">已完成</Select.Option>
              <Select.Option value="cancelled">已取消</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setProgressModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DailyPlanManagement;
