import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  message,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Typography,
  Tag,
  Statistic,
  Checkbox,
  Grid,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { unitAPI, Unit } from '../services/unitAPI';

const { Title } = Typography;
const { Option } = Select;
const { useBreakpoint } = Grid;

// 单位类别选项
const UNIT_CATEGORIES = [
  { value: 'count', label: '计数单位', color: 'blue' },
  { value: 'weight', label: '重量单位', color: 'green' },
  { value: 'length', label: '长度单位', color: 'orange' },
  { value: 'volume', label: '体积单位', color: 'purple' },
  { value: 'area', label: '面积单位', color: 'cyan' },
];

const UnitManagement: React.FC = () => {
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUnit, setEditingUnit] = useState<Unit | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');

  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // 获取计量单位列表
  const fetchUnits = async () => {
    setLoading(true);
    try {
      const response = await unitAPI.getUnits({
        search: searchText,
        category: categoryFilter,
        page_size: 1000, // 获取所有数据，前端分页
        sort_by: 'sort_order',
        sort_order: 'asc',
      });

      // 确保返回的数据是数组，如果不是则设置为空数组
      const unitsData = (response.data as any)?.data;
      setUnits(Array.isArray(unitsData) ? unitsData : []);
    } catch (error) {
      message.error('获取计量单位列表失败');
      console.error('Error fetching units:', error);
      // 发生错误时设置为空数组
      setUnits([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUnits();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchText, categoryFilter]);

  // 统计数据
  const getStatistics = () => {
    const safeUnits = Array.isArray(units) ? units : [];
    const total = safeUnits.length;
    const defaultUnit = safeUnits.find(u => u.is_default);
    const categoryCounts = UNIT_CATEGORIES.map(cat => ({
      ...cat,
      count: safeUnits.filter(u => u.category === cat.value).length
    }));

    return { total, defaultUnit, categoryCounts };
  };

  // 创建或更新计量单位
  const handleSubmit = async (values: any) => {
    try {
      const unitData = {
        name: values.name,
        symbol: values.symbol,
        category: values.category,
        description: values.description,
        is_default: values.is_default || false,
        sort_order: parseInt(values.sort_order) || 999,
      };

      if (editingUnit) {
        // 更新计量单位
        await unitAPI.updateUnit(editingUnit.id, unitData);
        message.success('计量单位更新成功');
      } else {
        // 创建计量单位
        await unitAPI.createUnit(unitData);
        message.success('计量单位创建成功');
      }

      setModalVisible(false);
      setEditingUnit(null);
      form.resetFields();
      fetchUnits();
    } catch (error: any) {
      const errorMsg = error.response?.data?.error || error.message;
      message.error(editingUnit ? `计量单位更新失败: ${errorMsg}` : `计量单位创建失败: ${errorMsg}`);
      console.error('Error saving unit:', error);
    }
  };

  // 删除计量单位
  const handleDelete = async (id: string) => {
    try {
      await unitAPI.deleteUnit(id);
      message.success('计量单位删除成功');
      fetchUnits();
    } catch (error: any) {
      const errorMsg = error.response?.data?.error || error.message;
      message.error(`计量单位删除失败: ${errorMsg}`);
      console.error('Error deleting unit:', error);
    }
  };

  // 打开编辑模态框
  const handleEdit = (unit: Unit) => {
    setEditingUnit(unit);
    form.setFieldsValue({
      name: unit.name,
      symbol: unit.symbol,
      category: unit.category,
      description: unit.description,
      is_default: unit.is_default,
      sort_order: unit.sort_order,
    });
    setModalVisible(true);
  };

  // 获取类别标签颜色
  const getCategoryColor = (category: string) => {
    const categoryInfo = UNIT_CATEGORIES.find(cat => cat.value === category);
    return categoryInfo?.color || 'default';
  };

  // 获取类别标签文本
  const getCategoryLabel = (category: string) => {
    const categoryInfo = UNIT_CATEGORIES.find(cat => cat.value === category);
    return categoryInfo?.label || category;
  };

  // 搜索计量单位
  const handleSearch = () => {
    fetchUnits();
  };

  const statistics = getStatistics();

  // 表格列定义
  const columns = [
    {
      title: '单位名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Unit) => (
        <div>
          <strong>{text}</strong>
          {record.is_default && <Tag color="gold" style={{ marginLeft: 8 }}>默认</Tag>}
        </div>
      ),
    },
    {
      title: '单位符号',
      dataIndex: 'symbol',
      key: 'symbol',
      width: 100,
      render: (text: string) => <Tag>{text}</Tag>,
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: string) => (
        <Tag color={getCategoryColor(category)}>
          {getCategoryLabel(category)}
        </Tag>
      ),
      responsive: ['md'] as any,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      responsive: ['lg'] as any,
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      responsive: ['xl'] as any,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: Unit) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个计量单位吗？"
            description="删除后可能影响相关产品和订单数据"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
                disabled={record.is_default} // 默认单位不能删除
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="计量单位总数"
              value={statistics.total}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="默认单位"
              value={statistics.defaultUnit?.name || '未设置'}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        {statistics.categoryCounts.slice(0, 2).map((cat, index) => (
          <Col xs={24} sm={12} md={6} key={cat.value}>
            <Card>
              <Statistic
                title={cat.label}
                value={cat.count}
                suffix="个"
                valueStyle={{ color: cat.color === 'blue' ? '#1890ff' : '#52c41a' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              计量单位管理
            </Title>
          </Col>
          <Col>
            <Space wrap>
              <Select
                placeholder="类别筛选"
                value={categoryFilter}
                onChange={setCategoryFilter}
                style={{ width: 120 }}
                allowClear
              >
                {UNIT_CATEGORIES.map(cat => (
                  <Option key={cat.value} value={cat.value}>
                    {cat.label}
                  </Option>
                ))}
              </Select>
              <Input.Search
                placeholder="搜索单位名称、符号、描述"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={handleSearch}
                style={{ width: isMobile ? 200 : 300 }}
                enterButton={<SearchOutlined />}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchUnits()}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingUnit(null);
                  form.resetFields();
                  setModalVisible(true);
                }}
              >
                新增单位
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Card style={{ marginTop: 16 }}>
        <Table
          columns={columns}
          dataSource={units}
          rowKey="id"
          loading={loading}
          pagination={{
            total: Array.isArray(units) ? units.length : 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 800 }}
          size={isMobile ? 'small' : 'middle'}
        />
      </Card>

      {/* 创建/编辑计量单位模态框 */}
      <Modal
        title={editingUnit ? '编辑计量单位' : '新增计量单位'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUnit(null);
          form.resetFields();
        }}
        footer={null}
        width={isMobile ? '90%' : 600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            category: 'count',
            is_default: false,
            sort_order: 999,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="单位名称"
                rules={[
                  { required: true, message: '请输入单位名称' },
                  { max: 20, message: '单位名称不能超过20个字符' }
                ]}
              >
                <Input placeholder="如：个、件、千克" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="symbol"
                label="单位符号"
                rules={[
                  { required: true, message: '请输入单位符号' },
                  { max: 10, message: '单位符号不能超过10个字符' }
                ]}
              >
                <Input placeholder="如：个、件、kg" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="单位类别"
                rules={[{ required: true, message: '请选择单位类别' }]}
              >
                <Select placeholder="选择单位类别">
                  {UNIT_CATEGORIES.map(cat => (
                    <Option key={cat.value} value={cat.value}>
                      <Tag color={cat.color}>{cat.label}</Tag>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sort_order"
                label="排序顺序"
                rules={[{ required: true, message: '请输入排序顺序' }]}
              >
                <Input
                  type="number"
                  placeholder="数字越小排序越靠前"
                  min={1}
                  max={9999}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="单位描述"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入单位描述信息（可选）"
              maxLength={200}
              showCount
            />
          </Form.Item>

          <Form.Item
            name="is_default"
            valuePropName="checked"
          >
            <Checkbox>
              设为默认单位
              <span style={{ marginLeft: 8, color: '#666', fontSize: '12px' }}>
                （默认单位将在产品和订单中优先显示）
              </span>
            </Checkbox>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingUnit(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingUnit ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UnitManagement;
