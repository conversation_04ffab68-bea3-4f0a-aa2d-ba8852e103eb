import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  TimePicker,
  InputNumber,
  Space,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Typography,
  Tooltip,
  Tag,
  AutoComplete,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  CalendarOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { orderAPI, productAPI, processAPI, deviceAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;

interface ProductionTask {
  id?: string;
  _id?: string;
  date: string;
  order_id: string;
  order_number: string;
  product_id: string;
  product_name: string;
  process_id: string;
  process_name: string;
  device_id: string;
  device_name: string;
  program_number: string;
  cumulative_planned_quantity: number;
  planned_quantity: number;
  start_time: string;
  end_time: string;
  shift: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

const ProductionScheduleManagement: React.FC = () => {
  const [tasks, setTasks] = useState<ProductionTask[]>([]);
  const [orders, setOrders] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [processes, setProcesses] = useState<any[]>([]);
  const [devices, setDevices] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState<ProductionTask | null>(null);
  const [form] = Form.useForm();
  const [batchForm] = Form.useForm();

  // 筛选状态
  const [dateFilter, setDateFilter] = useState<string>('');
  const [orderFilter, setOrderFilter] = useState<string>('');
  const [productFilter, setProductFilter] = useState<string>('');

  // 获取生产任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      // 这里需要调用合并后的API
      const response = await fetch('/api/production-tasks', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      // 确保返回的数据是数组，如果不是则设置为空数组
      const tasksData = data?.data;
      setTasks(Array.isArray(tasksData) ? tasksData : []);
    } catch (error) {
      message.error('获取生产任务列表失败');
      console.error('Error fetching tasks:', error);
      // 发生错误时设置为空数组
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取基础数据
  const fetchBaseData = async () => {
    try {
      const [ordersRes, productsRes, processesRes, devicesRes] = await Promise.all([
        orderAPI.getOrders({ page: 1, page_size: 1000 }),
        productAPI.getProducts({ page: 1, page_size: 1000 }),
        processAPI.getProcesses({ page: 1, page_size: 1000 }),
        deviceAPI.getDevices({ page: 1, page_size: 1000 })
      ]);

      setOrders((ordersRes.data as any).data || []);
      setProducts((productsRes.data as any).data || []);
      setProcesses((processesRes.data as any).data || []);
      setDevices((devicesRes.data as any).data || []);
    } catch (error) {
      console.error('Error fetching base data:', error);
    }
  };

  useEffect(() => {
    fetchTasks();
    fetchBaseData();
  }, [dateFilter, orderFilter, productFilter]);

  // 根据时间自动计算班次
  const calculateShift = (startTime: string, endTime: string) => {
    const start = dayjs(startTime, 'HH:mm');
    const end = dayjs(endTime, 'HH:mm');
    const startHour = start.hour();

    if (startHour >= 6 && startHour < 14) return 'morning';
    if (startHour >= 14 && startHour < 22) return 'afternoon';
    return 'night';
  };

  // 获取班次文本
  const getShiftText = (shift: string) => {
    switch (shift) {
      case 'morning': return '早班';
      case 'afternoon': return '中班';
      case 'night': return '夜班';
      default: return shift;
    }
  };

  // 获取班次颜色
  const getShiftColor = (shift: string) => {
    switch (shift) {
      case 'morning': return 'blue';
      case 'afternoon': return 'green';
      case 'night': return 'purple';
      default: return 'default';
    }
  };

  // 获取订单号
  const getOrderNumber = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    return order ? order.order_number : orderId;
  };

  // 获取产品名称
  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId || p.product_id === productId);
    return product ? product.name : productId;
  };

  // 获取工序名称
  const getProcessName = (processId: string) => {
    const process = processes.find(p => p.id === processId || p.process_id === processId);
    return process ? process.name : processId;
  };

  // 获取设备名称
  const getDeviceName = (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    return device ? device.name : deviceId;
  };

  // 根据订单筛选产品
  const getProductsByOrder = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || !order.products) return products;

    const orderProductIds = order.products.map((p: any) => p.product_id);
    return products.filter(p => orderProductIds.includes(p.product_id));
  };

  // 根据产品筛选工序
  const getProcessesByProduct = (productId: string) => {
    return processes.filter(p => p.product_id === productId);
  };

  // 创建/编辑任务
  const handleSubmit = async (values: any) => {
    try {
      const taskData = {
        ...values,
        date: values.date.format('YYYY-MM-DD'),
        start_time: values.start_time.format('HH:mm'),
        end_time: values.end_time.format('HH:mm'),
        shift: calculateShift(
          values.start_time.format('HH:mm'),
          values.end_time.format('HH:mm')
        )
      };

      if (editingTask) {
        // 更新任务
        await fetch(`/api/production-tasks/${editingTask.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(taskData)
        });
        message.success('生产任务更新成功');
      } else {
        // 创建任务
        await fetch('/api/production-tasks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(taskData)
        });
        message.success('生产任务创建成功');
      }

      setModalVisible(false);
      setEditingTask(null);
      form.resetFields();
      fetchTasks();
    } catch (error: any) {
      message.error(editingTask ? '任务更新失败' : '任务创建失败');
    }
  };

  // 批量创建任务
  const handleBatchSubmit = async (values: any) => {
    try {
      const tasksData = values.tasks.map((task: any) => ({
        ...task,
        date: task.date.format('YYYY-MM-DD'),
        start_time: task.start_time.format('HH:mm'),
        end_time: task.end_time.format('HH:mm'),
        shift: calculateShift(
          task.start_time.format('HH:mm'),
          task.end_time.format('HH:mm')
        )
      }));

      await fetch('/api/production-tasks/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tasks: tasksData })
      });

      message.success(`成功创建 ${tasksData.length} 个生产任务`);
      setBatchModalVisible(false);
      batchForm.resetFields();
      fetchTasks();
    } catch (error: any) {
      message.error('批量创建任务失败');
    }
  };

  // 删除任务
  const handleDelete = async (id: string) => {
    try {
      await fetch(`/api/production-tasks/${id}`, {
        method: 'DELETE'
      });
      message.success('生产任务删除成功');
      fetchTasks();
    } catch (error: any) {
      message.error('任务删除失败');
    }
  };

  // 打开编辑模态框
  const handleEdit = (task: ProductionTask) => {
    setEditingTask(task);
    form.setFieldsValue({
      ...task,
      date: dayjs(task.date),
      start_time: dayjs(task.start_time, 'HH:mm'),
      end_time: dayjs(task.end_time, 'HH:mm')
    });
    setModalVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 100,
      render: (date: string) => dayjs(date).format('MM-DD')
    },
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id',
      width: 120,
      render: (orderId: string) => (
        <Tag color="blue">{getOrderNumber(orderId)}</Tag>
      )
    },
    {
      title: '产品',
      dataIndex: 'product_id',
      key: 'product_id',
      width: 150,
      render: (productId: string) => (
        <div>
          <div style={{ fontWeight: 500 }}>{getProductName(productId)}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{productId}</div>
        </div>
      )
    },
    {
      title: '工序',
      dataIndex: 'process_id',
      key: 'process_id',
      width: 120,
      render: (processId: string) => (
        <Tag color="green">{getProcessName(processId)}</Tag>
      )
    },
    {
      title: '设备',
      dataIndex: 'device_id',
      key: 'device_id',
      width: 120,
      render: (deviceId: string) => (
        <Tag color="orange">{getDeviceName(deviceId)}</Tag>
      )
    },
    {
      title: '程序号',
      dataIndex: 'program_number',
      key: 'program_number',
      width: 100
    },
    {
      title: '累计计划',
      dataIndex: 'cumulative_planned_quantity',
      key: 'cumulative_planned_quantity',
      width: 100,
      render: (qty: number) => qty?.toLocaleString()
    },
    {
      title: '计划数量',
      dataIndex: 'planned_quantity',
      key: 'planned_quantity',
      width: 100,
      render: (qty: number) => qty?.toLocaleString()
    },
    {
      title: '时间段',
      key: 'time_range',
      width: 150,
      render: (_: any, record: ProductionTask) => (
        <div>
          <div>{record.start_time} - {record.end_time}</div>
          <Tag color={getShiftColor(record.shift)}>
            {getShiftText(record.shift)}
          </Tag>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: ProductionTask) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个任务吗？"
            onConfirm={() => handleDelete(record.id || record._id || '')}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <CalendarOutlined /> 生产排程管理
      </Title>

      {/* 操作栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={6} md={4}>
            <DatePicker
              placeholder="选择日期"
              value={dateFilter ? dayjs(dateFilter) : null}
              onChange={(date) => setDateFilter(date ? date.format('YYYY-MM-DD') : '')}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={6} md={4}>
            <Select
              placeholder="选择订单"
              value={orderFilter}
              onChange={setOrderFilter}
              allowClear
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {orders.map(order => (
                <Select.Option key={order.id} value={order.id}>
                  {order.order_number}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={6} md={4}>
            <Select
              placeholder="选择产品"
              value={productFilter}
              onChange={setProductFilter}
              allowClear
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {products.map(product => (
                <Select.Option key={product.product_id} value={product.product_id}>
                  {product.name}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={6} md={12}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchTasks}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingTask(null);
                  form.resetFields();
                  setModalVisible(true);
                }}
              >
                新建任务
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  batchForm.resetFields();
                  setBatchModalVisible(true);
                }}
                style={{ backgroundColor: '#52c41a' }}
              >
                批量新建
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 任务表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey={(record) => record.id || record._id || ''}
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            total: Array.isArray(tasks) ? tasks.length : 0,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 单个任务编辑模态框 */}
      <Modal
        title={editingTask ? '编辑生产任务' : '新建生产任务'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTask(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            date: dayjs(),
            start_time: dayjs('08:00', 'HH:mm'),
            end_time: dayjs('16:00', 'HH:mm'),
            cumulative_planned_quantity: 0,
            planned_quantity: 1
          }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="日期"
                name="date"
                rules={[{ required: true, message: '请选择日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="订单"
                name="order_id"
                rules={[{ required: true, message: '请选择订单' }]}
              >
                <Select
                  placeholder="选择订单"
                  showSearch
                  optionFilterProp="children"
                  onChange={(value) => {
                    // 清空产品和工序选择
                    form.setFieldsValue({ product_id: undefined, process_id: undefined });
                  }}
                >
                  {orders.map(order => (
                    <Select.Option key={order.id} value={order.id}>
                      {order.order_number} - {order.customer_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="产品"
                name="product_id"
                rules={[{ required: true, message: '请选择产品' }]}
              >
                <AutoComplete
                  placeholder="选择或输入产品"
                  options={getProductsByOrder(form.getFieldValue('order_id')).map(p => ({
                    value: p.product_id,
                    label: `${p.name} (${p.product_id})`
                  }))}
                  onChange={(value) => {
                    // 清空工序选择
                    form.setFieldsValue({ process_id: undefined });
                  }}
                  filterOption={(inputValue, option) =>
                    option!.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="工序"
                name="process_id"
                rules={[{ required: true, message: '请选择工序' }]}
              >
                <AutoComplete
                  placeholder="选择或输入工序"
                  options={getProcessesByProduct(form.getFieldValue('product_id')).map(p => ({
                    value: p.process_id,
                    label: `${p.name} (${p.process_id})`
                  }))}
                  filterOption={(inputValue, option) =>
                    option!.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="设备"
                name="device_id"
                rules={[{ required: true, message: '请选择设备' }]}
              >
                <Select
                  placeholder="选择设备"
                  showSearch
                  optionFilterProp="children"
                >
                  {devices.map(device => (
                    <Select.Option key={device.id} value={device.id}>
                      {device.name} ({device.device_id})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="程序号"
                name="program_number"
                rules={[{ required: true, message: '请输入程序号' }]}
              >
                <Input placeholder="请输入程序号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="累计计划数量"
                name="cumulative_planned_quantity"
                rules={[{ required: true, message: '请输入累计计划数量' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="计划数量"
                name="planned_quantity"
                rules={[{ required: true, message: '请输入计划数量' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="开始时间"
                name="start_time"
                rules={[{ required: true, message: '请选择开始时间' }]}
              >
                <TimePicker
                  format="HH:mm"
                  style={{ width: '100%' }}
                  onChange={() => {
                    // 自动计算班次
                    const startTime = form.getFieldValue('start_time');
                    const endTime = form.getFieldValue('end_time');
                    if (startTime && endTime) {
                      const shift = calculateShift(
                        startTime.format('HH:mm'),
                        endTime.format('HH:mm')
                      );
                      form.setFieldsValue({ shift });
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="结束时间"
                name="end_time"
                rules={[{ required: true, message: '请选择结束时间' }]}
              >
                <TimePicker
                  format="HH:mm"
                  style={{ width: '100%' }}
                  onChange={() => {
                    // 自动计算班次
                    const startTime = form.getFieldValue('start_time');
                    const endTime = form.getFieldValue('end_time');
                    if (startTime && endTime) {
                      const shift = calculateShift(
                        startTime.format('HH:mm'),
                        endTime.format('HH:mm')
                      );
                      form.setFieldsValue({ shift });
                    }
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingTask(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingTask ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量新建模态框 */}
      <Modal
        title="批量新建生产任务"
        open={batchModalVisible}
        onCancel={() => {
          setBatchModalVisible(false);
          batchForm.resetFields();
        }}
        footer={null}
        width={1200}
      >
        <Form
          form={batchForm}
          layout="vertical"
          onFinish={handleBatchSubmit}
        >
          <Form.List name="tasks">
            {(fields, { add, remove }) => (
              <>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                  <span style={{ fontWeight: 500 }}>任务列表</span>
                  <Button type="dashed" onClick={() => add({
                    date: dayjs(),
                    start_time: dayjs('08:00', 'HH:mm'),
                    end_time: dayjs('16:00', 'HH:mm'),
                    cumulative_planned_quantity: 0,
                    planned_quantity: 1
                  })} icon={<PlusOutlined />}>
                    添加任务
                  </Button>
                </div>

                {/* 表格头部 */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: '100px 120px 150px 120px 120px 100px 80px 80px 100px 100px 60px',
                  gap: '8px',
                  padding: '8px',
                  backgroundColor: '#fafafa',
                  fontWeight: 500,
                  marginBottom: 8,
                  borderRadius: '4px'
                }}>
                  <div>日期</div>
                  <div>订单</div>
                  <div>产品</div>
                  <div>工序</div>
                  <div>设备</div>
                  <div>程序号</div>
                  <div>累计计划</div>
                  <div>计划数量</div>
                  <div>开始时间</div>
                  <div>结束时间</div>
                  <div>操作</div>
                </div>

                {fields.map(({ key, name, ...restField }) => (
                  <div key={key} style={{
                    display: 'grid',
                    gridTemplateColumns: '100px 120px 150px 120px 120px 100px 80px 80px 100px 100px 60px',
                    gap: '8px',
                    padding: '8px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    marginBottom: 8,
                    alignItems: 'center'
                  }}>
                    <Form.Item
                      {...restField}
                      name={[name, 'date']}
                      rules={[{ required: true, message: '请选择日期' }]}
                      style={{ margin: 0 }}
                    >
                      <DatePicker size="small" style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'order_id']}
                      rules={[{ required: true, message: '请选择订单' }]}
                      style={{ margin: 0 }}
                    >
                      <Select
                        size="small"
                        placeholder="选择订单"
                        showSearch
                        optionFilterProp="children"
                        onChange={(value) => {
                          const currentTasks = batchForm.getFieldValue('tasks') || [];
                          currentTasks[name] = {
                            ...currentTasks[name],
                            product_id: undefined,
                            process_id: undefined
                          };
                          batchForm.setFieldsValue({ tasks: currentTasks });
                        }}
                      >
                        {orders.map(order => (
                          <Select.Option key={order.id} value={order.id}>
                            {order.order_number}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'product_id']}
                      rules={[{ required: true, message: '请选择产品' }]}
                      style={{ margin: 0 }}
                    >
                      <AutoComplete
                        size="small"
                        placeholder="选择产品"
                        options={(() => {
                          const currentTasks = batchForm.getFieldValue('tasks') || [];
                          const orderId = currentTasks[name]?.order_id;
                          return getProductsByOrder(orderId).map(p => ({
                            value: p.product_id,
                            label: p.name
                          }));
                        })()}
                        onChange={(value) => {
                          const currentTasks = batchForm.getFieldValue('tasks') || [];
                          currentTasks[name] = {
                            ...currentTasks[name],
                            process_id: undefined
                          };
                          batchForm.setFieldsValue({ tasks: currentTasks });
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'process_id']}
                      rules={[{ required: true, message: '请选择工序' }]}
                      style={{ margin: 0 }}
                    >
                      <AutoComplete
                        size="small"
                        placeholder="选择工序"
                        options={(() => {
                          const currentTasks = batchForm.getFieldValue('tasks') || [];
                          const productId = currentTasks[name]?.product_id;
                          return getProcessesByProduct(productId).map(p => ({
                            value: p.process_id,
                            label: p.name
                          }));
                        })()}
                      />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'device_id']}
                      rules={[{ required: true, message: '请选择设备' }]}
                      style={{ margin: 0 }}
                    >
                      <Select
                        size="small"
                        placeholder="选择设备"
                        showSearch
                        optionFilterProp="children"
                      >
                        {devices.map(device => (
                          <Select.Option key={device.id} value={device.id}>
                            {device.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'program_number']}
                      rules={[{ required: true, message: '请输入程序号' }]}
                      style={{ margin: 0 }}
                    >
                      <Input size="small" placeholder="程序号" />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'cumulative_planned_quantity']}
                      rules={[{ required: true, message: '请输入累计计划数量' }]}
                      style={{ margin: 0 }}
                    >
                      <InputNumber size="small" min={0} style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'planned_quantity']}
                      rules={[{ required: true, message: '请输入计划数量' }]}
                      style={{ margin: 0 }}
                    >
                      <InputNumber size="small" min={1} style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'start_time']}
                      rules={[{ required: true, message: '请选择开始时间' }]}
                      style={{ margin: 0 }}
                    >
                      <TimePicker size="small" format="HH:mm" style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'end_time']}
                      rules={[{ required: true, message: '请选择结束时间' }]}
                      style={{ margin: 0 }}
                    >
                      <TimePicker size="small" format="HH:mm" style={{ width: '100%' }} />
                    </Form.Item>

                    <Button
                      type="text"
                      danger
                      size="small"
                      onClick={() => remove(name)}
                      icon={<DeleteOutlined />}
                    />
                  </div>
                ))}

                {fields.length === 0 && (
                  <div style={{ textAlign: 'center', padding: '40px', border: '1px dashed #d9d9d9', borderRadius: '4px' }}>
                    <Button type="dashed" onClick={() => add({
                      date: dayjs(),
                      start_time: dayjs('08:00', 'HH:mm'),
                      end_time: dayjs('16:00', 'HH:mm'),
                      cumulative_planned_quantity: 0,
                      planned_quantity: 1
                    })} icon={<PlusOutlined />}>
                      添加第一个任务
                    </Button>
                  </div>
                )}
              </>
            )}
          </Form.List>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setBatchModalVisible(false);
                  batchForm.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                批量创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductionScheduleManagement;
