import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, message, Typography, Row, Col, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { authAPI, LoginRequest } from '../services/api';
import config from '../config';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 检测URL参数，判断是否显示调试信息
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const debugParam = urlParams.get('debug');
    setShowDebugInfo(debugParam === 'alan');
  }, [location.search]);

  const onFinish = async (values: LoginRequest) => {
    setLoading(true);
    try {
      const response = await authAPI.login(values);
      const { token, user } = (response.data as any).data;

      // 保存token和用户信息
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      message.success('登录成功！');
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);
      message.error(error.response?.data?.error || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Row justify="center" style={{ width: '100%', maxWidth: '1200px' }}>
        <Col xs={22} sm={16} md={12} lg={8} xl={6}>
          <Card
            style={{
              borderRadius: '12px',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
              border: 'none'
            }}
            bodyStyle={{ padding: '40px 32px' }}
          >
            <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
              {/* Logo和标题 */}
              <div>
                <div style={{
                  width: '64px',
                  height: '64px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 16px',
                  fontSize: '24px',
                  color: 'white'
                }}>
                  <LoginOutlined />
                </div>
                <Title level={2} style={{ margin: '0 0 8px', color: '#1f2937' }}>
                  制造数据中心
                </Title>
                <Text type="secondary" style={{ fontSize: '16px' }}>
                  管理系统登录
                </Text>
              </div>

              {/* 登录表单 */}
              <Form
                name="login"
                onFinish={onFinish}
                autoComplete="off"
                size="large"
                style={{ width: '100%' }}
              >
                <Form.Item
                  name="username"
                  rules={[{ required: true, message: '请输入用户名!' }]}
                >
                  <Input
                    prefix={<UserOutlined style={{ color: '#9ca3af' }} />}
                    placeholder="用户名"
                    style={{
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      padding: '12px 16px'
                    }}
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[{ required: true, message: '请输入密码!' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined style={{ color: '#9ca3af' }} />}
                    placeholder="密码"
                    style={{
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      padding: '12px 16px'
                    }}
                  />
                </Form.Item>

                <Form.Item style={{ marginBottom: '16px' }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    style={{
                      width: '100%',
                      height: '48px',
                      borderRadius: '8px',
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none',
                      fontSize: '16px',
                      fontWeight: '500'
                    }}
                  >
                    {loading ? '登录中...' : '登录'}
                  </Button>
                </Form.Item>
              </Form>

              {/* 调试信息 - 仅在debug=alan时显示 */}
              {showDebugInfo && (
                <div style={{
                  background: '#f8fafc',
                  padding: '16px',
                  borderRadius: '8px',
                  border: '1px solid #e2e8f0'
                }}>
                  <Text type="secondary" style={{ fontSize: '14px', display: 'block', marginBottom: '8px' }}>
                    调试信息：
                  </Text>
                  <Text code style={{ fontSize: '13px', display: 'block', marginBottom: '4px' }}>
                    用户名: admin
                  </Text>
                  <Text code style={{ fontSize: '13px', display: 'block', marginBottom: '4px' }}>
                    密码: admin123
                  </Text>
                  <Text code style={{ fontSize: '13px', display: 'block', marginBottom: '4px', wordBreak: 'break-all' }}>
                    环境变量API: {process.env.REACT_APP_API_BASE_URL || 'unknown'}
                  </Text>
                  <Text code style={{ fontSize: '13px', display: 'block', wordBreak: 'break-all' }}>
                    配置文件API: {config.api.baseURL}
                  </Text>
                </div>
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Login;
