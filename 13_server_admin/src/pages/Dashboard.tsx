import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Button, Space, Typography, Alert, Spin } from 'antd';
import {
  ApiOutlined,
  ProductOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { deviceAPI, productAPI, processAPI, healthAPI, Device, Product, Process } from '../services/api';

const { Title, Paragraph } = Typography;

interface SystemStats {
  devices: {
    total: number;
    active: number;
    maintenance: number;
    error: number;
    inactive: number;
  };
  products: {
    total: number;
  };
  processes: {
    total: number;
  };
  apiStatus: 'healthy' | 'error' | 'loading';
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<SystemStats>({
    devices: { total: 0, active: 0, maintenance: 0, error: 0, inactive: 0 },
    products: { total: 0 },
    processes: { total: 0 },
    apiStatus: 'loading'
  });
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  const fetchSystemStats = async () => {
    setLoading(true);
    try {
      // 并行获取所有数据
      const [devicesRes, productsRes, processesRes, healthRes] = await Promise.allSettled([
        deviceAPI.getDevices(),
        productAPI.getProducts(),
        processAPI.getProcesses(),
        healthAPI.getHealth()
      ]);

      const newStats: SystemStats = {
        devices: { total: 0, active: 0, maintenance: 0, error: 0, inactive: 0 },
        products: { total: 0 },
        processes: { total: 0 },
        apiStatus: 'loading'
      };

      // 处理设备数据
      if (devicesRes.status === 'fulfilled') {
        const devices: Device[] = (devicesRes.value.data as any)?.data || [];
        newStats.devices.total = devices.length;
        newStats.devices.active = devices.filter((d: Device) => d.status === 'active').length;
        newStats.devices.maintenance = devices.filter((d: Device) => d.status === 'maintenance').length;
        newStats.devices.error = devices.filter((d: Device) => d.status === 'error').length;
        newStats.devices.inactive = devices.filter((d: Device) => d.status === 'inactive').length;
      }

      // 处理产品数据
      if (productsRes.status === 'fulfilled') {
        const products: Product[] = (productsRes.value.data as any)?.data || [];
        newStats.products.total = products.length;
      }

      // 处理工序数据
      if (processesRes.status === 'fulfilled') {
        const processes: Process[] = (processesRes.value.data as any)?.data || [];
        newStats.processes.total = processes.length;
      }

      // 处理健康检查
      if (healthRes.status === 'fulfilled') {
        newStats.apiStatus = 'healthy';
      } else {
        newStats.apiStatus = 'error';
      }

      setStats(newStats);
    } catch (error) {
      console.error('Error fetching system stats:', error);
      setStats(prev => ({ ...prev, apiStatus: 'error' }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemStats();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy':
        return '正常运行';
      case 'error':
        return '服务异常';
      default:
        return '检查中...';
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载系统数据...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>系统概览</Title>
        <Paragraph>
          欢迎使用制造数据中心管理系统。这里是系统的整体运行状况和关键指标。
        </Paragraph>
      </div>

      {/* API状态提醒 */}
      {stats.apiStatus === 'error' && (
        <Alert
          message="API服务连接异常"
          description="无法连接到后端API服务，请检查服务状态或联系系统管理员。"
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
          action={
            <Button size="small" danger onClick={fetchSystemStats}>
              重试连接
            </Button>
          }
        />
      )}

      {/* 核心统计数据 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="设备总数"
              value={stats.devices.total}
              suffix="台"
              prefix={<ApiOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="产品种类"
              value={stats.products.total}
              suffix="种"
              prefix={<ProductOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="工序数量"
              value={stats.processes.total}
              suffix="个"
              prefix={<ToolOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={getStatusText(stats.apiStatus)}
              prefix={getStatusIcon(stats.apiStatus)}
              valueStyle={{
                color: stats.apiStatus === 'healthy' ? '#52c41a' : '#ff4d4f',
                fontSize: '16px'
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 设备状态详情 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="设备状态分布" extra={
            <Button
              type="link"
              onClick={() => navigate('/devices')}
            >
              查看详情
            </Button>
          }>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="运行中"
                  value={stats.devices.active}
                  suffix="台"
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="维护中"
                  value={stats.devices.maintenance}
                  suffix="台"
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Statistic
                  title="故障"
                  value={stats.devices.error}
                  suffix="台"
                  prefix={<ExclamationCircleOutlined />}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="停机"
                  value={stats.devices.inactive}
                  suffix="台"
                  prefix={<StopOutlined />}
                  valueStyle={{ color: '#d9d9d9' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="快速操作">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                block
                icon={<ApiOutlined />}
                onClick={() => navigate('/devices')}
              >
                管理设备
              </Button>
              <Button
                type="default"
                block
                icon={<ProductOutlined />}
                onClick={() => navigate('/products')}
              >
                管理产品
              </Button>
              <Button
                type="default"
                block
                icon={<ToolOutlined />}
                onClick={() => navigate('/processes')}
              >
                管理工序
              </Button>
              <Button
                type="dashed"
                block
                icon={<ReloadOutlined />}
                onClick={fetchSystemStats}
              >
                刷新数据
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>


    </div>
  );
};

export default Dashboard;
