import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
  Typography,
  Divider,
  InputNumber
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  SettingOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { Process, processAPI, Product, productAPI } from '../services/api';

const { Title } = Typography;
const { Option } = Select;

// 时间格式转换工具函数
const formatSecondsToTime = (seconds: number): string => {
  if (!seconds || seconds <= 0) return '0秒';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const parts = [];
  if (hours > 0) parts.push(`${hours}时`);
  if (minutes > 0) parts.push(`${minutes}分`);
  if (remainingSeconds > 0) parts.push(`${remainingSeconds}秒`);

  return parts.join('');
};

const ProcessManagement: React.FC = () => {
  const [processes, setProcesses] = useState<Process[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProcess, setEditingProcess] = useState<Process | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [form] = Form.useForm();

  // 获取工序列表
  const fetchProcesses = async () => {
    setLoading(true);
    try {
      const response = await processAPI.getProcesses({
        page: 1,
        page_size: 100,
        search: searchText,
        product_id: selectedProduct
      });
      // 确保返回的数据是数组，如果不是则设置为空数组
      const processesData = (response.data as any)?.data;
      setProcesses(Array.isArray(processesData) ? processesData : []);
    } catch (error) {
      message.error('获取工序列表失败');
      console.error('Error fetching processes:', error);
      // 发生错误时设置为空数组
      setProcesses([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取产品列表
  const fetchProducts = async () => {
    try {
      const response = await productAPI.getProducts({
        page: 1,
        page_size: 100
      });
      // 确保返回的数据是数组，如果不是则设置为空数组
      const productsData = (response.data as any)?.data;
      setProducts(Array.isArray(productsData) ? productsData : []);
    } catch (error) {
      console.error('Error fetching products:', error);
      // 发生错误时设置为空数组
      setProducts([]);
    }
  };

  useEffect(() => {
    fetchProcesses();
  }, [searchText, selectedProduct]);

  useEffect(() => {
    fetchProducts();
  }, []);

  // 创建或更新工序
  const handleSubmit = async (values: Process) => {
    try {
      if (editingProcess) {
        // 使用MongoDB ObjectID进行更新
        const processId = editingProcess.id || editingProcess._id;
        if (!processId) {
          message.error('工序ID不存在，无法更新');
          return;
        }
        await processAPI.updateProcess(processId, values);
        message.success('工序更新成功');
      } else {
        await processAPI.createProcess(values);
        message.success('工序创建成功');
      }
      setModalVisible(false);
      setEditingProcess(null);
      form.resetFields();
      fetchProcesses();
    } catch (error: any) {
      console.error('Error saving process:', error);
      const errorMsg = error.response?.data?.error || error.message;
      message.error(editingProcess ? `工序更新失败: ${errorMsg}` : `工序创建失败: ${errorMsg}`);
    }
  };

  // 删除工序
  const handleDelete = async (process: Process) => {
    try {
      const processId = process.id || process._id;
      if (!processId) {
        message.error('工序ID不存在，无法删除');
        return;
      }
      await processAPI.deleteProcess(processId);
      message.success('工序删除成功');
      fetchProcesses();
    } catch (error: any) {
      console.error('Error deleting process:', error);
      const errorMsg = error.response?.data?.error || error.message;
      message.error(`工序删除失败: ${errorMsg}`);
    }
  };

  // 打开编辑模态框
  const handleEdit = (process: Process) => {
    setEditingProcess(process);
    form.setFieldsValue(process);
    setModalVisible(true);
  };

  // 打开新建模态框
  const handleAdd = () => {
    setEditingProcess(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 获取产品名称
  const getProductName = (productId: string) => {
    const product = products.find(p => p.product_id === productId);
    return product ? product.name : productId;
  };

  // 统计数据
  const getStatistics = () => {
    // 确保 processes 是数组
    const safeProcesses = Array.isArray(processes) ? processes : [];
    const total = safeProcesses.length;
    const productSet = new Set(safeProcesses.map(p => p.product_id));
    const productCount = Array.from(productSet).length;

    return { total, productCount };
  };

  const statistics = getStatistics();

  // 表格列定义
  const columns = [
    {
      title: '工序编号',
      dataIndex: 'process_id',
      key: 'process_id',
      width: 180, // 加宽以显示完整文字
      render: (text: string) => (
        <Tag
          color="blue"
          style={{
            whiteSpace: 'normal',
            wordBreak: 'break-all',
            lineHeight: '1.4',
            padding: '4px 8px',
            minHeight: '24px',
            display: 'inline-block'
          }}
        >
          {text}
        </Tag>
      )
    },
    {
      title: '工序名称',
      dataIndex: 'name',
      key: 'name',
      width: 200, // 加宽以显示完整文字
      render: (text: string) => (
        <strong
          style={{
            whiteSpace: 'normal',
            wordBreak: 'break-word',
            lineHeight: '1.4',
            display: 'block'
          }}
        >
          {text}
        </strong>
      )
    },
    {
      title: '关联产品',
      dataIndex: 'product_id',
      key: 'product_id',
      width: 150,
      render: (text: string) => (
        <Tooltip title={`产品ID: ${text}`}>
          <Tag color="green">{getProductName(text)}</Tag>
        </Tooltip>
      )
    },

    {
      title: '工序顺序',
      dataIndex: 'sequence',
      key: 'sequence',
      width: 100,
      sorter: (a: Process, b: Process) => (a.sequence || 0) - (b.sequence || 0), // 添加排序功能
      render: (text: number) => (
        <Tag color="purple">第{text}序</Tag>
      )
    },
    {
      title: '预计时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 120,
      render: (text: number) => (
        <Space>
          <ClockCircleOutlined />
          <span>{formatSecondsToTime(text || 0)}</span>
        </Space>
      )
    },
    {
      title: '准备时间',
      dataIndex: 'preparation_time',
      key: 'preparation_time',
      width: 120,
      render: (text: number) => (
        <Space>
          <ClockCircleOutlined />
          <span>{formatSecondsToTime(text || 0)}</span>
        </Space>
      )
    },
    {
      title: '总时长',
      key: 'total_duration',
      width: 120,
      render: (record: Process) => {
        const totalSeconds = (record.duration || 0) + (record.preparation_time || 0);
        return (
          <Space>
            <ClockCircleOutlined />
            <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
              {formatSecondsToTime(totalSeconds)}
            </span>
          </Space>
        );
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: Process) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个工序吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <SettingOutlined /> 工序管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={12}>
          <Card>
            <Statistic
              title="工序总数"
              value={statistics.total}
              prefix={<SettingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={12}>
          <Card>
            <Statistic
              title="关联产品"
              value={statistics.productCount}
              prefix={<SettingOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>

      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Input
              placeholder="搜索工序名称或编号"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              placeholder="选择产品"
              value={selectedProduct}
              onChange={setSelectedProduct}
              allowClear
              style={{ width: '100%' }}
            >
              {Array.isArray(products) && products.map(product => (
                <Option key={product.product_id} value={product.product_id}>
                  {product.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={24} md={10}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新建工序
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchProcesses}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 工序表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={processes}
          rowKey={(record) => record.id || record._id || record.process_id}
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            total: Array.isArray(processes) ? processes.length : 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 新建/编辑工序模态框 */}
      <Modal
        title={editingProcess ? '编辑工序' : '新建工序'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingProcess(null);
          form.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            sequence: 1,
            duration: 1800, // 30分钟 = 1800秒
            preparation_time: 300 // 5分钟 = 300秒
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="工序编号"
                name="process_id"
                rules={[
                  { required: true, message: '请输入工序编号' },
                  {
                    validator: async (_, value) => {
                      if (!value) return;
                      // 检查工序编号是否重复（排除当前编辑的工序）
                      const existingProcess = processes.find(p =>
                        p.process_id === value && p.id !== editingProcess?.id
                      );
                      if (existingProcess) {
                        throw new Error('工序编号已存在，请使用其他编号');
                      }
                    }
                  }
                ]}
              >
                <Input placeholder="请输入工序编号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="工序名称"
                name="name"
                rules={[{ required: true, message: '请输入工序名称' }]}
              >
                <Input placeholder="请输入工序名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="关联产品"
                name="product_id"
                rules={[{ required: true, message: '请选择关联产品' }]}
              >
                <Select
                  placeholder="选择产品"
                  showSearch
                  optionFilterProp="children"
                >
                  {Array.isArray(products) && products.map(product => (
                    <Option key={product.product_id} value={product.product_id}>
                      {product.name} ({product.product_id})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="工序顺序"
                name="sequence"
                dependencies={['process_id']} // 添加依赖关系
                rules={[
                  { required: true, message: '请输入工序顺序' },
                  { type: 'number', min: 1, message: '工序顺序必须大于0' },
                  {
                    validator: async (_, value) => {
                      if (!value) return;
                      const currentProcessId = form.getFieldValue('process_id');
                      if (!currentProcessId) return;

                      // 检查同一工序编号下是否有相同的工序顺序（排除当前编辑的工序）
                      const existingProcess = processes.find(p =>
                        p.process_id === currentProcessId &&
                        p.sequence === value &&
                        p.id !== editingProcess?.id
                      );
                      if (existingProcess) {
                        throw new Error(`工序编号 ${currentProcessId} 下已存在第${value}序，请使用其他顺序`);
                      }
                    }
                  }
                ]}
              >
                <InputNumber
                  min={1}
                  max={100}
                  placeholder="工序在产品中的顺序"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="预计时长(秒)"
                name="duration"
                rules={[
                  { required: true, message: '请输入预计时长' },
                  { type: 'number', min: 1, message: '时长必须大于0秒' }
                ]}
              >
                <InputNumber
                  min={1}
                  max={86400} // 24小时 = 86400秒
                  placeholder="预计完成时长"
                  style={{ width: '100%' }}
                  addonAfter={
                    <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                      prevValues.duration !== currentValues.duration
                    }>
                      {({ getFieldValue }) => {
                        const duration = getFieldValue('duration') || 0;
                        return (
                          <span style={{ color: '#1890ff', fontSize: '12px' }}>
                            秒 ({formatSecondsToTime(Number(duration))})
                          </span>
                        );
                      }}
                    </Form.Item>
                  }
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="准备时间(秒)"
                name="preparation_time"
                rules={[
                  { required: true, message: '请输入准备时间' },
                  { type: 'number', min: 0, message: '准备时间不能小于0秒' }
                ]}
              >
                <InputNumber
                  min={0}
                  max={86400} // 24小时 = 86400秒
                  placeholder="工序准备时间"
                  style={{ width: '100%' }}
                  addonAfter={
                    <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                      prevValues.preparation_time !== currentValues.preparation_time
                    }>
                      {({ getFieldValue }) => {
                        const preparationTime = getFieldValue('preparation_time') || 0;
                        return (
                          <span style={{ color: '#1890ff', fontSize: '12px' }}>
                            秒 ({formatSecondsToTime(Number(preparationTime))})
                          </span>
                        );
                      }}
                    </Form.Item>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="总时长预览">
                <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                  prevValues.duration !== currentValues.duration ||
                  prevValues.preparation_time !== currentValues.preparation_time
                }>
                  {({ getFieldValue }) => {
                    const duration = getFieldValue('duration') || 0;
                    const preparationTime = getFieldValue('preparation_time') || 0;
                    const totalSeconds = Number(duration) + Number(preparationTime);
                    return (
                      <div style={{
                        padding: '8px 12px',
                        backgroundColor: '#f0f2f5',
                        borderRadius: '6px',
                        border: '1px solid #d9d9d9',
                        fontSize: '14px',
                        color: '#1890ff',
                        fontWeight: 'bold'
                      }}>
                        {formatSecondsToTime(totalSeconds)}
                      </div>
                    );
                  }}
                </Form.Item>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="工序描述"
            name="description"
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入工序的详细描述、操作要求等"
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingProcess(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingProcess ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProcessManagement;
