import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Tag,
  Card,
  Row,
  Col,
  Typography,
  Tooltip,
  Grid,
  Progress
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { productionTaskAPI, orderAPI, deviceAPI, productAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { useBreakpoint } = Grid;
const { RangePicker } = DatePicker;

interface ProductionTask {
  id: string;
  task_number: string;
  order_id: string;
  order_number: string;
  product_id: string;
  product_name: string;
  device_id: string;
  device_name: string;
  planned_quantity: number;
  actual_quantity: number;
  qualified_quantity: number;
  defective_quantity: number;
  status: string;
  priority: number;
  planned_start_time: string;
  planned_end_time: string;
  actual_start_time?: string;
  actual_end_time?: string;
  estimated_duration: number;
  actual_duration: number;
  progress: number;
  notes: string;
  created_at: string;
  updated_at: string;
}

const ProductionTaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState<ProductionTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState<ProductionTask | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [orders, setOrders] = useState<any[]>([]);
  const [devices, setDevices] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // 获取生产任务列表
  const fetchTasks = async (params?: any) => {
    setLoading(true);
    try {
      const response = await productionTaskAPI.getTasks({
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText,
        status: statusFilter,
        ...params,
      });

      // 确保返回的数据是数组，如果不是则设置为空数组
      const tasksData = (response.data as any)?.data;
      setTasks(Array.isArray(tasksData) ? tasksData : []);
      setPagination(prev => ({
        ...prev,
        total: (response.data as any).total,
        current: (response.data as any).page,
      }));
    } catch (error: any) {
      message.error('获取生产任务列表失败');
      console.error('Fetch tasks error:', error);
      // 发生错误时设置为空数组
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取基础数据
  const fetchBaseData = async () => {
    try {
      const [ordersRes, devicesRes, productsRes] = await Promise.all([
        orderAPI.getOrders({ page_size: 1000 }),
        deviceAPI.getDevices({ page_size: 1000 }),
        productAPI.getProducts({ page_size: 1000 }),
      ]);

      // 确保返回的数据都是数组，如果不是则设置为空数组
      const ordersData = (ordersRes.data as any)?.data;
      const devicesData = (devicesRes.data as any)?.data;
      const productsData = (productsRes.data as any)?.data;

      setOrders(Array.isArray(ordersData) ? ordersData : []);
      setDevices(Array.isArray(devicesData) ? devicesData : []);
      setProducts(Array.isArray(productsData) ? productsData : []);
    } catch (error) {
      console.error('Fetch base data error:', error);
      // 发生错误时设置为空数组
      setOrders([]);
      setDevices([]);
      setProducts([]);
    }
  };

  useEffect(() => {
    fetchTasks();
    fetchBaseData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, pagination.pageSize, statusFilter]);

  // 搜索任务
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchTasks();
  };

  // 创建/编辑任务
  const handleSubmit = async (values: any) => {
    try {
      const taskData = {
        ...values,
        planned_start_time: values.planned_time[0].toISOString(),
        planned_end_time: values.planned_time[1].toISOString(),
      };
      delete taskData.planned_time;

      if (editingTask) {
        await productionTaskAPI.updateTask(editingTask.id, taskData);
        message.success('生产任务更新成功');
      } else {
        await productionTaskAPI.createTask(taskData);
        message.success('生产任务创建成功');
      }

      setModalVisible(false);
      setEditingTask(null);
      form.resetFields();
      fetchTasks();
    } catch (error: any) {
      message.error(error.response?.data?.error || '操作失败');
    }
  };

  // 删除任务
  const handleDelete = async (id: string) => {
    try {
      await productionTaskAPI.deleteTask(id);
      message.success('生产任务删除成功');
      fetchTasks();
    } catch (error: any) {
      message.error(error.response?.data?.error || '删除失败');
    }
  };

  // 开始任务
  const handleStartTask = async (id: string) => {
    try {
      await productionTaskAPI.startTask(id);
      message.success('任务已开始');
      fetchTasks();
    } catch (error: any) {
      message.error(error.response?.data?.error || '开始任务失败');
    }
  };

  // 完成任务
  const handleCompleteTask = async (id: string) => {
    try {
      await productionTaskAPI.completeTask(id);
      message.success('任务已完成');
      fetchTasks();
    } catch (error: any) {
      message.error(error.response?.data?.error || '完成任务失败');
    }
  };

  // 打开编辑模态框
  const handleEdit = (task: ProductionTask) => {
    setEditingTask(task);
    form.setFieldsValue({
      order_id: task.order_id,
      product_id: task.product_id,
      device_id: task.device_id,
      planned_quantity: task.planned_quantity,
      priority: task.priority,
      planned_time: [dayjs(task.planned_start_time), dayjs(task.planned_end_time)],
      estimated_duration: task.estimated_duration,
      notes: task.notes,
    });
    setModalVisible(true);
  };

  // 状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'blue';
      case 'in_progress': return 'orange';
      case 'completed': return 'green';
      case 'paused': return 'yellow';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  };

  // 状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned': return '已计划';
      case 'in_progress': return '进行中';
      case 'completed': return '已完成';
      case 'paused': return '暂停';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  // 优先级标签颜色
  const getPriorityColor = (priority: number) => {
    if (priority >= 4) return 'red';
    if (priority >= 3) return 'orange';
    if (priority >= 2) return 'blue';
    return 'green';
  };

  // 获取订单号
  const getOrderNumber = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    return order ? order.order_number : orderId;
  };

  // 获取产品名称
  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId || p.product_id === productId);
    return product ? product.name : productId;
  };

  // 获取设备名称
  const getDeviceName = (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    return device ? device.name : deviceId;
  };

  // 表格列定义
  const columns = [
    {
      title: '任务号',
      dataIndex: 'task_number',
      key: 'task_number',
      render: (text: string) => (
        <span style={{ fontWeight: 500, color: '#1890ff' }}>{text}</span>
      ),
    },
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id',
      responsive: ['md'] as any,
      render: (orderId: string) => (
        <span style={{ color: '#1890ff' }}>{getOrderNumber(orderId)}</span>
      ),
    },
    {
      title: '产品',
      dataIndex: 'product_id',
      key: 'product_id',
      responsive: ['lg'] as any,
      render: (productId: string) => (
        <div>
          <div style={{ fontWeight: 500 }}>{getProductName(productId)}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{productId}</div>
        </div>
      ),
    },
    {
      title: '设备',
      dataIndex: 'device_id',
      key: 'device_id',
      responsive: ['lg'] as any,
      render: (deviceId: string) => (
        <div>
          <div style={{ fontWeight: 500 }}>{getDeviceName(deviceId)}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{deviceId}</div>
        </div>
      ),
    },
    {
      title: '计划数量',
      dataIndex: 'planned_quantity',
      key: 'planned_quantity',
      render: (quantity: number) => quantity.toLocaleString(),
    },
    {
      title: '进度',
      key: 'progress',
      render: (_: any, record: ProductionTask) => (
        <div>
          <Progress
            percent={Math.round(record.progress)}
            size="small"
            status={record.status === 'completed' ? 'success' : 'active'}
          />
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.actual_quantity}/{record.planned_quantity}
          </div>
        </div>
      ),
      responsive: ['md'] as any,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: number) => (
        <Tag color={getPriorityColor(priority)}>
          {priority === 5 ? '紧急' :
            priority === 4 ? '高' :
              priority === 3 ? '中' :
                priority === 2 ? '低' : '最低'}
        </Tag>
      ),
      responsive: ['xl'] as any,
    },
    {
      title: '计划时间',
      key: 'planned_time',
      render: (_: any, record: ProductionTask) => (
        <div style={{ fontSize: '12px' }}>
          <div>{dayjs(record.planned_start_time).format('MM-DD HH:mm')}</div>
          <div>{dayjs(record.planned_end_time).format('MM-DD HH:mm')}</div>
        </div>
      ),
      responsive: ['xl'] as any,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ProductionTask) => (
        <Space size="small">
          {record.status === 'planned' && (
            <Tooltip title="开始任务">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleStartTask(record.id)}
                size="small"
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          {record.status === 'in_progress' && (
            <Tooltip title="完成任务">
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleCompleteTask(record.id)}
                size="small"
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个生产任务吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              生产任务管理
            </Title>
          </Col>
          <Col>
            <Space wrap>
              <Select
                placeholder="状态筛选"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
                allowClear
              >
                <Select.Option value="planned">已计划</Select.Option>
                <Select.Option value="in_progress">进行中</Select.Option>
                <Select.Option value="completed">已完成</Select.Option>
                <Select.Option value="paused">暂停</Select.Option>
                <Select.Option value="cancelled">已取消</Select.Option>
              </Select>
              <Input.Search
                placeholder="搜索任务号、订单号、产品"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={handleSearch}
                style={{ width: isMobile ? 200 : 300 }}
                enterButton={<SearchOutlined />}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchTasks()}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingTask(null);
                  form.resetFields();
                  setModalVisible(true);
                }}
              >
                新增任务
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Card style={{ marginTop: 16 }}>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize }));
            },
          }}
          scroll={{ x: 1000 }}
          size={isMobile ? 'small' : 'middle'}
        />
      </Card>

      {/* 创建/编辑任务模态框 */}
      <Modal
        title={editingTask ? '编辑生产任务' : '新增生产任务'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTask(null);
          form.resetFields();
        }}
        footer={null}
        width={isMobile ? '90%' : 800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="order_id"
                label="关联订单"
                rules={[{ required: true, message: '请选择关联订单' }]}
              >
                <Select
                  placeholder="选择订单"
                  showSearch
                  optionFilterProp="children"
                >
                  {Array.isArray(orders) && orders.map(order => (
                    <Select.Option key={order.id} value={order.id}>
                      {order.order_number} - {order.customer_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="product_id"
                label="产品"
                rules={[{ required: true, message: '请选择产品' }]}
              >
                <Select
                  placeholder="选择产品"
                  showSearch
                  optionFilterProp="children"
                >
                  {Array.isArray(products) && products.map(product => (
                    <Select.Option key={product.id} value={product.id}>
                      {product.name} - {product.model}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="device_id"
                label="生产设备"
                rules={[{ required: true, message: '请选择生产设备' }]}
              >
                <Select
                  placeholder="选择设备"
                  showSearch
                  optionFilterProp="children"
                >
                  {Array.isArray(devices) && devices.map(device => (
                    <Select.Option key={device.id} value={device.id}>
                      {device.name} - {device.location}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="planned_quantity"
                label="计划数量"
                rules={[{ required: true, message: '请输入计划数量' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select>
                  <Select.Option value={1}>最低</Select.Option>
                  <Select.Option value={2}>低</Select.Option>
                  <Select.Option value={3}>中</Select.Option>
                  <Select.Option value={4}>高</Select.Option>
                  <Select.Option value={5}>紧急</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="estimated_duration"
                label="预计时长(分钟)"
                rules={[{ required: true, message: '请输入预计时长' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="planned_time"
            label="计划时间"
            rules={[{ required: true, message: '请选择计划时间' }]}
          >
            <RangePicker
              showTime
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingTask ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductionTaskManagement;
