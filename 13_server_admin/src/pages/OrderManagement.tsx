import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Tag,
  Card,
  Row,
  Col,
  Typography,
  Tooltip,
  Grid,
  Statistic,
  AutoComplete,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { orderAPI, productAPI } from '../services/api';
import { unitAPI, Unit } from '../services/unitAPI';
import dayjs from 'dayjs';

const { Title } = Typography;
const { useBreakpoint } = Grid;

interface OrderProduct {
  product_id: string;
  product_name: string;
  quantity: number;
  unit?: string; // 添加计量单位字段
}

interface Order {
  id: string;
  order_number: string;
  customer_name: string;
  products: OrderProduct[];
  status: string;
  priority: number;
  delivery_date: string;
  description: string;
  created_at: string;
  updated_at: string;
}

const OrderManagement: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOrder, setEditingOrder] = useState<Order | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [statistics, setStatistics] = useState<any>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // 获取订单列表
  const fetchOrders = async (params?: any) => {
    setLoading(true);
    try {
      const response = await orderAPI.getOrders({
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText,
        status: statusFilter,
        ...params,
      });

      // 确保返回的数据是数组，如果不是则设置为空数组
      const ordersData = (response.data as any)?.data;
      setOrders(Array.isArray(ordersData) ? ordersData : []);
      setPagination(prev => ({
        ...prev,
        total: (response.data as any).total,
        current: (response.data as any).page,
      }));
    } catch (error: any) {
      message.error('获取订单列表失败');
      console.error('Fetch orders error:', error);
      // 发生错误时设置为空数组
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取产品数据
  const fetchProducts = async () => {
    try {
      const response = await productAPI.getProducts({
        page: 1,
        page_size: 1000
      });
      // 确保返回的数据是数组，如果不是则设置为空数组
      const productsData = (response.data as any)?.data;
      setProducts(Array.isArray(productsData) ? productsData : []);
    } catch (error) {
      console.error('Fetch products error:', error);
      // 发生错误时设置为空数组
      setProducts([]);
    }
  };

  // 获取计量单位数据
  const fetchUnits = async () => {
    try {
      const response = await unitAPI.getUnits({
        page_size: 1000,
        sort_by: 'sort_order',
        sort_order: 'asc',
      });
      // 确保返回的数据是数组，如果不是则设置为空数组
      const unitsData = (response.data as any)?.data;
      setUnits(Array.isArray(unitsData) ? unitsData : []);
    } catch (error) {
      console.error('Fetch units error:', error);
      // 发生错误时设置为空数组
      setUnits([]);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await orderAPI.getOrderStatistics();
      setStatistics((response.data as any).data);
    } catch (error) {
      console.error('Fetch statistics error:', error);
    }
  };

  useEffect(() => {
    fetchOrders();
    fetchProducts();
    fetchUnits();
    fetchStatistics();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, pagination.pageSize, statusFilter]);

  // 搜索订单
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchOrders();
  };

  // 检查产品是否存在，如果不存在则创建
  const ensureProductExists = async (productData: any) => {
    const existingProduct = products.find(p => p.product_id === productData.product_id);

    if (!existingProduct) {
      // 产品不存在，需要创建
      const newProductData = {
        product_id: productData.product_id,
        name: productData.product_name,
        description: `自动创建的产品 - ${productData.product_name}`,
        unit: productData.unit || 'pcs', // 默认单位
        specifications: {}
      };

      await productAPI.createProduct(newProductData);
      message.success(`产品 ${productData.product_name} 已自动创建`);

      // 重新获取产品列表
      await fetchProducts();
    }
  };

  // 创建/编辑订单
  const handleSubmit = async (values: any) => {
    try {
      // 处理多产品数据
      const orderProducts = values.products && values.products.length > 0 ? values.products : [];

      // 为每个新产品创建产品记录
      for (const product of orderProducts) {
        if (product.product_id && product.product_name && product.unit) {
          const existingProduct = products.find(p => p.product_id === product.product_id);
          if (!existingProduct) {
            // 只有当产品不存在时才创建
            await ensureProductExists(product);
          }
        }
      }

      // 准备订单数据，支持多产品
      const orderData = {
        order_number: values.order_number,
        customer_name: values.customer_name,
        products: orderProducts.map((product: any) => ({
          product_id: product.product_id,
          product_name: product.product_name,
          quantity: product.quantity,
          unit: product.unit,
          unit_price: 0.01, // 设为0.01，因为后端要求大于0
        })),
        priority: values.priority || 1,
        delivery_date: values.delivery_date.toISOString(),
        description: values.description || '',
      };

      if (editingOrder) {
        await orderAPI.updateOrder(editingOrder.id, orderData);
        message.success('订单更新成功');
      } else {
        await orderAPI.createOrder(orderData);
        message.success('订单创建成功');
      }

      setModalVisible(false);
      setEditingOrder(null);
      form.resetFields();
      fetchOrders();
      fetchStatistics();
    } catch (error: any) {
      message.error(error.response?.data?.error || '操作失败');
    }
  };

  // 删除订单
  const handleDelete = async (id: string) => {
    try {
      await orderAPI.deleteOrder(id);
      message.success('订单删除成功');
      fetchOrders();
      fetchStatistics();
    } catch (error: any) {
      message.error(error.response?.data?.error || '删除失败');
    }
  };

  // 更新订单状态
  const handleStatusChange = async (id: string, status: string) => {
    try {
      await orderAPI.updateOrderStatus(id, status);
      message.success('订单状态更新成功');
      fetchOrders();
      fetchStatistics();
    } catch (error: any) {
      message.error(error.response?.data?.error || '状态更新失败');
    }
  };

  // 打开编辑模态框
  const handleEdit = (order: Order) => {
    setEditingOrder(order);

    // 将单产品格式转换为多产品格式以兼容新的表单结构
    const orderProducts = order.products && order.products.length > 0
      ? order.products
      : [{
        product_id: (order as any).product_id || '',
        product_name: (order as any).product_name || '',
        quantity: (order as any).quantity || 0,
        unit: (order as any).unit || ''
      }];

    // 为每个产品补充完整的产品信息（名称和单位）
    const enrichedProducts = orderProducts.map(orderProduct => {
      const existingProduct = products.find(p => p.product_id === orderProduct.product_id);

      if (existingProduct) {
        // 如果是现有产品，使用产品库中的完整信息
        return {
          ...orderProduct,
          product_name: existingProduct.name,
          unit: existingProduct.unit
        };
      } else {
        // 如果是新产品或找不到的产品，保持原有信息
        return orderProduct;
      }
    });

    form.setFieldsValue({
      order_number: order.order_number,
      customer_name: order.customer_name,
      products: enrichedProducts,
      priority: order.priority,
      delivery_date: dayjs(order.delivery_date),
      description: order.description,
    });
    setModalVisible(true);
  };

  // 状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange';
      case 'processing': return 'blue';
      case 'completed': return 'green';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  };

  // 状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待处理';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  // 优先级标签颜色
  const getPriorityColor = (priority: number) => {
    if (priority >= 4) return 'red';
    if (priority >= 3) return 'orange';
    if (priority >= 2) return 'blue';
    return 'green';
  };

  // 获取产品名称
  const getProductName = (productId: string) => {
    const product = products.find(p => p.product_id === productId);
    return product ? product.name : productId;
  };

  // 将订单数据转换为表格行数据（支持多产品行合并）
  const getTableData = () => {
    const tableData: any[] = [];

    orders.forEach((order) => {
      // 兼容单产品和多产品格式
      const productList = order.products && order.products.length > 0
        ? order.products
        : [{
          product_id: (order as any).product_id || '',
          product_name: (order as any).product_name || '',
          quantity: (order as any).quantity || 0,
          unit: (order as any).unit || ''
        }];

      productList.forEach((product, index) => {
        tableData.push({
          key: `${order.id}-${index}`,
          order_id: order.id,
          order_number: order.order_number,
          customer_name: order.customer_name,
          product_id: product.product_id,
          product_name: product.product_name,
          quantity: product.quantity,
          unit: product.unit,
          status: order.status,
          priority: order.priority,
          delivery_date: order.delivery_date,
          description: order.description,
          // 用于合并单元格的标识
          isFirstRow: index === 0,
          rowSpan: index === 0 ? productList.length : 0,
          originalOrder: order
        });
      });
    });

    return tableData;
  };

  // 表格列定义
  const columns = [
    {
      title: '订单号',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 160,
      render: (text: string, record: any) => ({
        children: <span style={{ fontWeight: 500, color: '#1890ff' }}>{text}</span>,
        props: {
          rowSpan: record.rowSpan,
        },
      }),
    },
    {
      title: '客户',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 140,
      responsive: ['md'] as any,
      render: (text: string, record: any) => ({
        children: text,
        props: {
          rowSpan: record.rowSpan,
        },
      }),
    },
    {
      title: '产品信息',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 220,
      render: (text: string, record: any) => (
        <div style={{ fontWeight: 500 }}>{text} ({record.product_id})</div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      align: 'right' as const,
      render: (quantity: number) => (
        <span style={{ fontWeight: 500, fontSize: '14px' }}>
          {quantity.toLocaleString()}
        </span>
      ),
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
      align: 'center' as const,
      render: (unit: string) => (
        <Tag color="blue">{unit || '未设置'}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 110,
      render: (status: string, record: any) => {
        const statusMap = {
          'pending': { text: '待处理', color: 'orange' },
          'processing': { text: '处理中', color: 'blue' },
          'completed': { text: '已完成', color: 'green' },
          'cancelled': { text: '已取消', color: 'red' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
        return {
          children: <Tag color={statusInfo.color}>{statusInfo.text}</Tag>,
          props: {
            rowSpan: record.rowSpan,
          },
        };
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      responsive: ['lg'] as any,
      render: (priority: number, record: any) => {
        const priorityTag = (
          <Tag color={getPriorityColor(priority)}>
            {priority === 5 ? '紧急' :
              priority === 4 ? '高' :
                priority === 3 ? '中' :
                  priority === 2 ? '低' : '最低'}
          </Tag>
        );
        return {
          children: priorityTag,
          props: {
            rowSpan: record.rowSpan,
          },
        };
      },
    },
    {
      title: '交期',
      dataIndex: 'delivery_date',
      key: 'delivery_date',
      width: 110,
      responsive: ['xl'] as any,
      render: (date: string, record: any) => ({
        children: dayjs(date).format('YYYY-MM-DD'),
        props: {
          rowSpan: record.rowSpan,
        },
      }),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: any) => {
        const actionButtons = (
          <Space size="small">
            <Tooltip title="编辑">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record.originalOrder)}
                size="small"
              />
            </Tooltip>
            <Popconfirm
              title="确定要删除这个订单吗？"
              onConfirm={() => handleDelete(record.order_id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        );
        return {
          children: actionButtons,
          props: {
            rowSpan: record.rowSpan,
          },
        };
      },
    },
  ];

  return (
    <div>
      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总订单数"
                value={statistics.total_orders}
                prefix={<ShoppingCartOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="待处理"
                value={statistics.by_status?.find((s: any) => s._id === 'pending')?.count || 0}
                valueStyle={{ color: '#faad14' }}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="处理中"
                value={statistics.by_status?.find((s: any) => s._id === 'processing')?.count || 0}
                valueStyle={{ color: '#1890ff' }}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>

        </Row>
      )}

      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              订单管理
            </Title>
          </Col>
          <Col>
            <Space wrap>
              <Select
                placeholder="状态筛选"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
                allowClear
              >
                <Select.Option value="pending">待处理</Select.Option>
                <Select.Option value="processing">处理中</Select.Option>
                <Select.Option value="completed">已完成</Select.Option>
                <Select.Option value="cancelled">已取消</Select.Option>
              </Select>
              <Input.Search
                placeholder="搜索订单号、客户、产品"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={handleSearch}
                style={{ width: isMobile ? 200 : 300 }}
                enterButton={<SearchOutlined />}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchOrders()}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingOrder(null);
                  form.resetFields();
                  setModalVisible(true);
                }}
              >
                新增订单
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Card style={{ marginTop: 16 }}>
        <Table
          columns={columns}
          dataSource={getTableData()}
          rowKey="key"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize }));
            },
          }}
          scroll={{ x: 800 }}
          size={isMobile ? 'small' : 'middle'}
          bordered={true}
        />
      </Card>

      {/* 创建/编辑订单模态框 */}
      <Modal
        title={editingOrder ? '编辑订单' : '新增订单'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingOrder(null);
          form.resetFields();
        }}
        footer={null}
        width={isMobile ? '90%' : 800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="order_number"
                label="订单号"
                rules={[{ required: true, message: '请输入订单号' }]}
              >
                <Input disabled={!!editingOrder} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="customer_name"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.List name="products">
            {(fields, { add, remove }) => (
              <>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                  <span style={{ fontWeight: 500 }}>产品列表</span>
                  <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />}>
                    添加产品
                  </Button>
                </div>
                {fields.map(({ key, name, ...restField }) => (
                  <Card key={key} size="small" style={{ marginBottom: 16 }}>
                    <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => {
                      const prevProduct = prevValues.products?.[name];
                      const currentProduct = currentValues.products?.[name];
                      return prevProduct?.product_id !== currentProduct?.product_id;
                    }}>
                      {({ getFieldValue }) => {
                        const currentProductId = getFieldValue(['products', name, 'product_id']);
                        const existingProduct = products.find(p => p.product_id === currentProductId);
                        const isNewProduct = currentProductId && !existingProduct;

                        return (
                          <>
                            <Row gutter={16}>
                              <Col span={6}>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'product_id']}
                                  label="产品编号"
                                  rules={[{ required: true, message: '请输入产品编号' }]}
                                >
                                  <AutoComplete
                                    placeholder="输入或选择产品编号"
                                    options={Array.isArray(products) ? products.map(p => ({ value: p.product_id, label: p.product_id })) : []}
                                    onChange={(value) => {
                                      // 当产品编号改变时，清空产品名称和单位
                                      const currentProducts = form.getFieldValue('products') || [];
                                      const product = Array.isArray(products) ? products.find(p => p.product_id === value) : null;

                                      if (product) {
                                        // 存在的产品，自动填充名称和单位
                                        currentProducts[name] = {
                                          ...currentProducts[name],
                                          product_id: value,
                                          product_name: product.name,
                                          unit: product.unit
                                        };
                                      } else {
                                        // 新产品，清空名称和单位
                                        currentProducts[name] = {
                                          ...currentProducts[name],
                                          product_id: value,
                                          product_name: '',
                                          unit: ''
                                        };
                                      }
                                      form.setFieldsValue({ products: currentProducts });
                                    }}
                                    filterOption={(inputValue, option) =>
                                      option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                                    }
                                  />
                                </Form.Item>
                              </Col>
                              <Col span={6}>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'product_name']}
                                  label="产品名称"
                                  rules={[{ required: true, message: '请输入产品名称' }]}
                                >
                                  {isNewProduct ? (
                                    <Input placeholder="请输入产品名称" />
                                  ) : (
                                    <Input placeholder="产品名称" disabled />
                                  )}
                                </Form.Item>
                              </Col>
                              <Col span={4}>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'unit']}
                                  label="计量单位"
                                  rules={[{ required: true, message: '请选择计量单位' }]}
                                >
                                  {isNewProduct ? (
                                    <Select placeholder="选择单位">
                                      {units.map(unit => (
                                        <Select.Option key={unit.id} value={unit.symbol}>
                                          {unit.name}
                                        </Select.Option>
                                      ))}
                                    </Select>
                                  ) : (
                                    <Input placeholder="计量单位" disabled />
                                  )}
                                </Form.Item>
                              </Col>
                              <Col span={6}>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'quantity']}
                                  label="数量"
                                  rules={[{ required: true, message: '请输入数量' }]}
                                >
                                  <InputNumber min={1} style={{ width: '100%' }} />
                                </Form.Item>
                              </Col>
                              <Col span={2}>
                                <Form.Item label=" ">
                                  <Button type="text" danger onClick={() => remove(name)} icon={<DeleteOutlined />} />
                                </Form.Item>
                              </Col>
                            </Row>
                          </>
                        );
                      }}
                    </Form.Item>
                  </Card>
                ))}
                {fields.length === 0 && (
                  <Card style={{ textAlign: 'center', marginBottom: 16 }}>
                    <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />}>
                      添加第一个产品
                    </Button>
                  </Card>
                )}
              </>
            )}
          </Form.List>

          <Divider />

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select>
                  <Select.Option value={1}>最低</Select.Option>
                  <Select.Option value={2}>低</Select.Option>
                  <Select.Option value={3}>中</Select.Option>
                  <Select.Option value={4}>高</Select.Option>
                  <Select.Option value={5}>紧急</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="delivery_date"
            label="交期"
            rules={[{ required: true, message: '请选择交期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="description"
            label="备注"
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingOrder ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OrderManagement;
