import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
  Typography,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { Product, productAPI } from '../services/api';
import { unitAPI, Unit } from '../services/unitAPI';

const { Title } = Typography;
const { Option } = Select;

const ProductManagement: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  // 获取产品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await productAPI.getProducts({
        page: 1,
        page_size: 100,
        search: searchText
      });
      // 确保返回的数据是数组，如果不是则设置为空数组
      const productsData = (response.data as any)?.data;
      setProducts(Array.isArray(productsData) ? productsData : []);
    } catch (error) {
      message.error('获取产品列表失败');
      console.error('Error fetching products:', error);
      // 发生错误时设置为空数组
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取计量单位列表
  const fetchUnits = async () => {
    try {
      const response = await unitAPI.getUnits({
        page_size: 1000,
        sort_by: 'sort_order',
        sort_order: 'asc',
      });
      // 确保返回的数据是数组，如果不是则设置为空数组
      const unitsData = (response.data as any)?.data;
      setUnits(Array.isArray(unitsData) ? unitsData : []);
    } catch (error) {
      console.error('Error fetching units:', error);
      // 发生错误时设置为空数组
      setUnits([]);
    }
  };

  useEffect(() => {
    fetchProducts();
    fetchUnits();
  }, [searchText]);

  // 创建或更新产品
  const handleSubmit = async (values: Product) => {
    try {
      if (editingProduct) {
        // 使用MongoDB ObjectID进行更新
        const productId = editingProduct.id || editingProduct._id;
        if (!productId) {
          message.error('产品ID不存在，无法更新');
          return;
        }
        await productAPI.updateProduct(productId, values);
        message.success('产品更新成功');
      } else {
        await productAPI.createProduct(values);
        message.success('产品创建成功');
      }
      setModalVisible(false);
      setEditingProduct(null);
      form.resetFields();
      fetchProducts();
    } catch (error: any) {
      console.error('Error saving product:', error);
      const errorMsg = error.response?.data?.error || error.message;
      message.error(editingProduct ? `产品更新失败: ${errorMsg}` : `产品创建失败: ${errorMsg}`);
    }
  };

  // 删除产品
  const handleDelete = async (product: Product) => {
    try {
      const productId = product.id || product._id;
      if (!productId) {
        message.error('产品ID不存在，无法删除');
        return;
      }
      await productAPI.deleteProduct(productId);
      message.success('产品删除成功');
      fetchProducts();
    } catch (error: any) {
      console.error('Error deleting product:', error);
      const errorMsg = error.response?.data?.error || error.message;
      message.error(`产品删除失败: ${errorMsg}`);
    }
  };

  // 打开编辑模态框
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    form.setFieldsValue(product);
    setModalVisible(true);
  };

  // 打开新建模态框
  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setModalVisible(true);
  };



  // 统计数据
  const getStatistics = () => {
    // 确保 products 是数组
    const safeProducts = Array.isArray(products) ? products : [];
    const total = safeProducts.length;
    const activeProducts = safeProducts.filter(p => p.product_id).length;

    return { total, activeProducts };
  };

  const statistics = getStatistics();

  // 表格列定义
  const columns = [
    {
      title: '产品编号',
      dataIndex: 'product_id',
      key: 'product_id',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
      render: (text: string) => <Tag>{text}</Tag>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: Product) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个产品吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <AppstoreOutlined /> 产品管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={12}>
          <Card>
            <Statistic
              title="产品总数"
              value={statistics.total}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={12}>
          <Card>
            <Statistic
              title="有效产品"
              value={statistics.activeProducts}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={12} md={12}>
            <Input
              placeholder="搜索产品名称或编号"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={12}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新建产品
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchProducts}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 产品表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          rowKey={(record) => record.id || record._id || record.product_id}
          loading={loading}
          scroll={{ x: 1000 }}
          pagination={{
            total: products.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 新建/编辑产品模态框 */}
      <Modal
        title={editingProduct ? '编辑产品' : '新建产品'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingProduct(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            unit: '个'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="产品编号"
                name="product_id"
                rules={[
                  { required: true, message: '请输入产品编号' },
                  { pattern: /^[A-Z0-9_]+$/, message: '产品编号只能包含大写字母、数字和下划线' }
                ]}
              >
                <Input placeholder="如: PROD_001" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="产品名称"
                name="name"
                rules={[{ required: true, message: '请输入产品名称' }]}
              >
                <Input placeholder="请输入产品名称" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="计量单位"
            name="unit"
            rules={[{ required: true, message: '请选择计量单位' }]}
          >
            <Select placeholder="选择计量单位" showSearch optionFilterProp="children">
              {Array.isArray(units) && units.map(unit => (
                <Option key={unit.id} value={unit.symbol}>
                  {unit.name} ({unit.symbol})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="产品描述"
            name="description"
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入产品描述信息"
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingProduct(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingProduct ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductManagement;
