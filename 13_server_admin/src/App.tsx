import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'antd/dist/reset.css';

import ResponsiveLayout from './components/ResponsiveLayout';
// import ConfigValidator from './components/ConfigValidator';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import DeviceManagement from './pages/DeviceManagement';
import ProductManagement from './pages/ProductManagement';
import ProcessManagement from './pages/ProcessManagement';
import UserManagement from './pages/UserManagement';
import OrderManagement from './pages/OrderManagement';
import ProductionScheduleManagement from './pages/ProductionScheduleManagement';
import UnitManagement from './pages/UnitManagement';

// 私有路由组件
const PrivateRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const token = localStorage.getItem('token');
  return token ? <>{children}</> : <Navigate to="/login" replace />;
};

// 公共路由组件（已登录用户重定向到仪表板）
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const token = localStorage.getItem('token');
  return token ? <Navigate to="/dashboard" replace /> : <>{children}</>;
};

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      {/* <ConfigValidator /> */}
      <Router>
        <Routes>
          {/* 公共路由 */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            }
          />

          {/* 私有路由 */}
          <Route
            path="/*"
            element={
              <PrivateRoute>
                <ResponsiveLayout>
                  <Routes>
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/devices" element={<DeviceManagement />} />
                    <Route path="/products" element={<ProductManagement />} />
                    <Route path="/processes" element={<ProcessManagement />} />
                    <Route path="/users" element={<UserManagement />} />
                    <Route path="/orders" element={<OrderManagement />} />
                    <Route path="/units" element={<UnitManagement />} />
                    <Route path="/schedule" element={<ProductionScheduleManagement />} />
                  </Routes>
                </ResponsiveLayout>
              </PrivateRoute>
            }
          />
        </Routes>
      </Router>
    </ConfigProvider>
  );
}

export default App;
