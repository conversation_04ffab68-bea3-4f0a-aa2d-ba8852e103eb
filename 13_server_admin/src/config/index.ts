// 应用配置文件
// 从环境变量中读取配置，提供默认值和类型安全

interface AppConfig {
  // API配置
  api: {
    baseURL: string;
    timeout: number;
  };

  // 应用信息
  app: {
    name: string;
    version: string;
    companyName: string;
    env: string;
  };

  // 功能开关
  features: {
    enableMock: boolean;
    enableDebug: boolean;
    enableAnalytics: boolean;
  };

  // 主题配置
  theme: {
    primaryColor: string;
    successColor: string;
    warningColor: string;
    errorColor: string;
  };
}

// 从环境变量读取配置
const config: AppConfig = {
  api: {
    baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:9005',
    timeout: parseInt(process.env.REACT_APP_API_TIMEOUT || '10000', 10),
  },

  app: {
    name: process.env.REACT_APP_APP_NAME || '制造数据中心管理系统',
    version: process.env.REACT_APP_APP_VERSION || '1.0.0',
    companyName: process.env.REACT_APP_COMPANY_NAME || '制造数据中心',
    env: process.env.REACT_APP_ENV || 'development',
  },

  features: {
    enableMock: process.env.REACT_APP_ENABLE_MOCK === 'true',
    enableDebug: process.env.REACT_APP_ENABLE_DEBUG === 'true',
    enableAnalytics: process.env.REACT_APP_ENABLE_ANALYTICS === 'true',
  },

  theme: {
    primaryColor: process.env.REACT_APP_PRIMARY_COLOR || '#1890ff',
    successColor: process.env.REACT_APP_SUCCESS_COLOR || '#52c41a',
    warningColor: process.env.REACT_APP_WARNING_COLOR || '#faad14',
    errorColor: process.env.REACT_APP_ERROR_COLOR || '#ff4d4f',
  },
};

// 开发环境下打印配置信息
if (config.features.enableDebug && config.app.env === 'development') {
  console.log('🔧 应用配置:', config);
}

// 导出配置
export default config;

// 导出具体配置项，方便直接使用
export const { api, app, features, theme } = config;

// API相关配置
export const API_BASE_URL = config.api.baseURL;
export const API_TIMEOUT = config.api.timeout;

// 应用信息
export const APP_NAME = config.app.name;
export const APP_VERSION = config.app.version;
export const COMPANY_NAME = config.app.companyName;
export const APP_ENV = config.app.env;

// 功能开关
export const ENABLE_MOCK = config.features.enableMock;
export const ENABLE_DEBUG = config.features.enableDebug;
export const ENABLE_ANALYTICS = config.features.enableAnalytics;

// 主题颜色
export const PRIMARY_COLOR = config.theme.primaryColor;
export const SUCCESS_COLOR = config.theme.successColor;
export const WARNING_COLOR = config.theme.warningColor;
export const ERROR_COLOR = config.theme.errorColor;

// 环境判断工具函数
export const isDevelopment = () => config.app.env === 'development';
export const isProduction = () => config.app.env === 'production';
export const isTesting = () => config.app.env === 'test';

// API URL构建工具函数
export const buildApiUrl = (path: string): string => {
  const baseURL = config.api.baseURL.endsWith('/')
    ? config.api.baseURL.slice(0, -1)
    : config.api.baseURL;
  const apiPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseURL}${apiPath}`;
};

// 日志工具函数
export const log = {
  debug: (...args: any[]) => {
    if (config.features.enableDebug) {
      console.log('[DEBUG]', ...args);
    }
  },
  info: (...args: any[]) => {
    console.info('[INFO]', ...args);
  },
  warn: (...args: any[]) => {
    console.warn('[WARN]', ...args);
  },
  error: (...args: any[]) => {
    console.error('[ERROR]', ...args);
  },
};
