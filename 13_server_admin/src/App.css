/* 重置默认样式 */
.App {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
}

/* AMIS 主题定制 */
.cxd-Layout {
  height: 100vh !important;
}

.cxd-Layout-aside {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.cxd-Layout-header {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%) !important;
  border-bottom: none !important;
}

/* 自定义卡片样式 */
.cxd-Card {
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.cxd-Card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-2px) !important;
}

/* 按钮样式优化 */
.cxd-Button--primary {
  background: linear-gradient(45deg, #667eea, #764ba2) !important;
  border: none !important;
}

.cxd-Button--success {
  background: linear-gradient(45deg, #56ab2f, #a8e6cf) !important;
  border: none !important;
}

.cxd-Button--info {
  background: linear-gradient(45deg, #4facfe, #00f2fe) !important;
  border: none !important;
}

/* 表格样式优化 */
.cxd-Table {
  border-radius: 8px !important;
  overflow: hidden !important;
}

.cxd-Table-table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

.cxd-Table-table th {
  background: linear-gradient(90deg, #f8f9fa, #e9ecef) !important;
  font-weight: 600 !important;
}

/* 状态标签样式 */
.label-success {
  background-color: #28a745 !important;
  color: white !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  font-size: 12px !important;
}

.label-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  font-size: 12px !important;
}

.label-danger {
  background-color: #dc3545 !important;
  color: white !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  font-size: 12px !important;
}

.label-default {
  background-color: #6c757d !important;
  color: white !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  font-size: 12px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cxd-Layout-aside {
    width: 200px !important;
  }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}