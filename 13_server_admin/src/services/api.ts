import axios from 'axios';
import { API_BASE_URL, API_TIMEOUT, log } from '../config';

// 类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface Device {
  _id?: string;
  id?: string; // MongoDB ObjectID (有时API返回id而不是_id)
  device_id: string;
  name: string;
  data_type: string; // 设备数据类型，如fanuc_30i、siemens_840d等
  location: string;
  status: 'active' | 'maintenance' | 'error' | 'inactive';
  sort_order?: number; // 设备显示排序，数字越小越靠前，用于控制30_machine_status主页的显示顺序
  // 设备元数据信息，用于数据推送时的metadata字段
  brand?: string; // 设备品牌
  model?: string; // 设备型号详情
  // 采集相关配置
  ip?: string;
  port?: number;
  enabled?: boolean;
  auto_start?: boolean;
  collect_interval?: number;
  timeout?: number;
  retry_count?: number;
  retry_delay?: number;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Product {
  _id?: string;
  id?: string; // MongoDB ObjectID
  product_id: string;
  name: string;
  unit: string;
  description?: string;
  specifications?: { key: string; value: string }[];
  created_at?: string;
  updated_at?: string;
}

export interface Process {
  _id?: string;
  id?: string; // MongoDB ObjectID
  process_id: string;
  name: string;
  product_id: string;
  duration: number; // 标准工时(秒)
  preparation_time: number; // 准备时间(秒)
  sequence: number;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  status: string;
  last_login?: string;
  login_count: number;
  profile: {
    full_name: string;
    phone: string;
    department: string;
    position: string;
    avatar: string;
  };
  permissions: string[];
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  expires_at: string;
}

export interface ListResponse<T> {
  data: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface QueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  status?: string;
  sort_by?: string;
  sort_desc?: boolean;
}

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    log.debug('API Request:', config.method?.toUpperCase(), config.url);

    // 添加认证token
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    log.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证错误
apiClient.interceptors.response.use(
  (response) => {
    log.debug('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    log.error('API Error:', error.response?.status, error.config?.url, error.message);

    // 处理认证错误
    if (error.response?.status === 401) {
      log.warn('Authentication failed, redirecting to login');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

// API接口定义
export const deviceAPI = {
  // 获取设备列表
  getDevices: (params?: any) =>
    apiClient.get('/admin/devices', { params }),

  // 获取单个设备
  getDevice: (id: string) =>
    apiClient.get(`/admin/devices/${id}`),

  // 创建设备
  createDevice: (data: any) =>
    apiClient.post('/admin/devices', data),

  // 更新设备
  updateDevice: (id: string, data: any) =>
    apiClient.put(`/admin/devices/${id}`, data),

  // 删除设备
  deleteDevice: (id: string) =>
    apiClient.delete(`/admin/devices/${id}`),

  // 获取设备数据类型选项
  getDataTypeOptions: () =>
    apiClient.get('/admin/device-data-types'),
};

export const productAPI = {
  // 获取产品列表
  getProducts: (params?: any) =>
    apiClient.get('/admin/products', { params }),

  // 获取单个产品
  getProduct: (id: string) =>
    apiClient.get(`/admin/products/${id}`),

  // 创建产品
  createProduct: (data: any) =>
    apiClient.post('/admin/products', data),

  // 更新产品
  updateProduct: (id: string, data: any) =>
    apiClient.put(`/admin/products/${id}`, data),

  // 删除产品
  deleteProduct: (id: string) =>
    apiClient.delete(`/admin/products/${id}`),
};

export const processAPI = {
  // 获取工序列表
  getProcesses: (params?: any) =>
    apiClient.get('/admin/processes', { params }),

  // 获取单个工序
  getProcess: (id: string) =>
    apiClient.get(`/admin/processes/${id}`),

  // 创建工序
  createProcess: (data: any) =>
    apiClient.post('/admin/processes', data),

  // 更新工序
  updateProcess: (id: string, data: any) =>
    apiClient.put(`/admin/processes/${id}`, data),

  // 删除工序
  deleteProcess: (id: string) =>
    apiClient.delete(`/admin/processes/${id}`),
};

// 认证API
export const authAPI = {
  login: (data: LoginRequest) =>
    apiClient.post('/auth/login', data),

  refreshToken: () =>
    apiClient.post('/auth/refresh'),

  getProfile: () =>
    apiClient.get('/user/profile'),

  changePassword: (data: { old_password: string; new_password: string }) =>
    apiClient.post('/user/change-password', data),
};

// 用户管理API
export const userAPI = {
  getUsers: (params?: QueryParams) =>
    apiClient.get('/admin/users', { params }),

  createUser: (data: Partial<User>) =>
    apiClient.post('/admin/users', data),

  getUser: (id: string) =>
    apiClient.get(`/admin/users/${id}`),

  updateUser: (id: string, data: Partial<User>) =>
    apiClient.put(`/admin/users/${id}`, data),

  deleteUser: (id: string) =>
    apiClient.delete(`/admin/users/${id}`),
};

// 数据管理API
export const dataAPI = {
  initializeData: () =>
    apiClient.post('/admin/initialize-data'),

  clearData: () =>
    apiClient.delete('/admin/clear-data'),

  resetData: () =>
    apiClient.post('/admin/reset-data'),

  getStatistics: () =>
    apiClient.get('/admin/statistics'),
};

// 健康检查
export const healthAPI = {
  getHealth: () => apiClient.get('/health'),
  getStatus: () => apiClient.get('/status'),
  getMetrics: () => apiClient.get('/metrics'),
};

// 订单管理API
export const orderAPI = {
  getOrders: (params?: QueryParams) => apiClient.get('/admin/orders', { params }),
  getOrder: (id: string) => apiClient.get(`/admin/orders/${id}`),
  getOrdersByStatus: (status: string) => apiClient.get(`/admin/orders/status/${status}`),
  getOrderStatistics: () => apiClient.get('/admin/orders/statistics'),
  createOrder: (data: any) => apiClient.post('/admin/orders', data),
  updateOrder: (id: string, data: any) => apiClient.put(`/admin/orders/${id}`, data),
  updateOrderStatus: (id: string, status: string) => apiClient.put(`/admin/orders/${id}/status`, { status }),
  deleteOrder: (id: string) => apiClient.delete(`/admin/orders/${id}`),
};

// 生产任务管理API
export const productionTaskAPI = {
  getTasks: (params?: QueryParams) => apiClient.get('/admin/tasks', { params }),
  getTask: (id: string) => apiClient.get(`/admin/tasks/${id}`),
  createTask: (data: any) => apiClient.post('/admin/tasks', data),
  updateTask: (id: string, data: any) => apiClient.put(`/admin/tasks/${id}`, data),
  startTask: (id: string) => apiClient.post(`/admin/tasks/${id}/start`),
  completeTask: (id: string) => apiClient.post(`/admin/tasks/${id}/complete`),
  deleteTask: (id: string) => apiClient.delete(`/admin/tasks/${id}`),
};

// 每日计划管理API
export const dailyPlanAPI = {
  getPlans: (params?: QueryParams) => apiClient.get('/admin/plans', { params }),
  getPlan: (id: string) => apiClient.get(`/admin/plans/${id}`),
  getPlansByDate: (date: string) => apiClient.get('/admin/plans/date', { params: { date } }),
  getPlansByDevice: (deviceId: string) => apiClient.get(`/admin/plans/device/${deviceId}`),
  getWeeklyPlans: (startDate: string) => apiClient.get('/admin/plans/weekly', { params: { start_date: startDate } }),
  getPlanStatistics: (startDate: string, endDate: string) => apiClient.get('/admin/plans/statistics', {
    params: { start_date: startDate, end_date: endDate }
  }),
  createPlan: (data: any) => apiClient.post('/admin/plans', data),
  updatePlan: (id: string, data: any) => apiClient.put(`/admin/plans/${id}`, data),
  updatePlanProgress: (id: string, data: { actual_quantity: number; status?: string; notes?: string }) =>
    apiClient.put(`/admin/plans/${id}/progress`, data),
  deletePlan: (id: string) => apiClient.delete(`/admin/plans/${id}`),
};

export default apiClient;
