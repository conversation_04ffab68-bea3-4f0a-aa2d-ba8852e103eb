import apiClient from './api';

// 计量单位接口定义
export interface Unit {
  id: string;
  name: string;           // 单位名称，如 "个"、"件"、"千克"
  symbol: string;         // 单位符号，如 "个"、"件"、"kg"
  category: string;       // 单位类别：count(计数)、weight(重量)、length(长度)、volume(体积)、area(面积)
  description?: string;   // 单位描述
  is_default: boolean;    // 是否为默认单位
  sort_order: number;     // 排序顺序
  created_at: string;
  updated_at: string;
}

// 创建计量单位的请求参数
export interface CreateUnitRequest {
  name: string;
  symbol: string;
  category: string;
  description?: string;
  is_default?: boolean;
  sort_order?: number;
}

// 更新计量单位的请求参数
export interface UpdateUnitRequest extends Partial<CreateUnitRequest> { }

// 查询计量单位的请求参数
export interface GetUnitsRequest {
  page?: number;
  page_size?: number;
  search?: string;
  category?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// 计量单位API服务类
export class UnitAPI {
  /**
   * 获取计量单位列表
   * @param params 查询参数
   * @returns 计量单位列表响应
   */
  static async getUnits(params: GetUnitsRequest = {}) {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.page_size) queryParams.append('page_size', params.page_size.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.category) queryParams.append('category', params.category);
    if (params.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params.sort_order) queryParams.append('sort_order', params.sort_order);

    const url = `/api/admin/units${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get(url);
  }

  /**
   * 根据ID获取单个计量单位
   * @param id 计量单位ID
   * @returns 计量单位详情
   */
  static async getUnit(id: string) {
    return apiClient.get(`/api/admin/units/${id}`);
  }

  /**
   * 创建新的计量单位
   * @param data 计量单位数据
   * @returns 创建结果
   */
  static async createUnit(data: CreateUnitRequest) {
    return apiClient.post('/api/admin/units', data);
  }

  /**
   * 更新计量单位
   * @param id 计量单位ID
   * @param data 更新数据
   * @returns 更新结果
   */
  static async updateUnit(id: string, data: UpdateUnitRequest) {
    return apiClient.put(`/api/admin/units/${id}`, data);
  }

  /**
   * 删除计量单位
   * @param id 计量单位ID
   * @returns 删除结果
   */
  static async deleteUnit(id: string) {
    return apiClient.delete(`/api/admin/units/${id}`);
  }

  /**
   * 批量删除计量单位
   * @param ids 计量单位ID数组
   * @returns 删除结果
   */
  static async batchDeleteUnits(ids: string[]) {
    return apiClient.post('/api/admin/units/batch-delete', { ids });
  }

  /**
   * 设置默认计量单位
   * @param id 计量单位ID
   * @returns 设置结果
   */
  static async setDefaultUnit(id: string) {
    return apiClient.post(`/api/admin/units/${id}/set-default`);
  }

  /**
   * 获取计量单位统计信息
   * @returns 统计信息
   */
  static async getUnitsStatistics() {
    return apiClient.get('/api/admin/units/statistics');
  }

  /**
   * 获取按类别分组的计量单位
   * @returns 按类别分组的计量单位
   */
  static async getUnitsByCategory() {
    return apiClient.get('/api/admin/units/by-category');
  }

  /**
   * 导出计量单位数据
   * @param format 导出格式 (csv, excel)
   * @returns 导出文件
   */
  static async exportUnits(format: 'csv' | 'excel' = 'excel') {
    return apiClient.get(`/api/admin/units/export?format=${format}`);
  }

  /**
   * 导入计量单位数据
   * @param file 导入文件
   * @returns 导入结果
   */
  static async importUnits(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post('/api/admin/units/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}

// 导出默认实例
export const unitAPI = UnitAPI;
