import React, { useState, useEffect } from 'react';
import { Layout, Menu, Dropdown, Avatar, Button, Space, Typography, Grid, Drawer, Badge, Modal, Form, Input, message } from 'antd';
import {
  DashboardOutlined,
  SettingOutlined,
  DatabaseOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuOutlined,
  TeamOutlined,
  Bar<PERSON>hartOutlined,
  ToolOutlined,
  AppstoreOutlined,
  ShoppingCartOutlined,
  CalendarOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  KeyOutlined,
  FundOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { User } from '../services/api';
import { APP_NAME, COMPANY_NAME } from '../config';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;
const { useBreakpoint } = Grid;

interface ResponsiveLayoutProps {
  children: React.ReactNode;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const screens = useBreakpoint();

  // 获取当前用户信息
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  useEffect(() => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      setCurrentUser(JSON.parse(userStr));
    }
  }, []);

  // 响应式处理
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;

  // 根据屏幕大小自动调整侧边栏
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    } else if (isTablet) {
      setCollapsed(true);
    } else {
      setCollapsed(false);
    }
  }, [isMobile, isTablet]);

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/devices',
      icon: <DatabaseOutlined />,
      label: '设备管理',
    },
    {
      key: '/products',
      icon: <AppstoreOutlined />,
      label: '产品管理',
    },
    {
      key: '/units',
      icon: <FundOutlined />,
      label: '计量单位',
    },
    {
      key: '/processes',
      icon: <ToolOutlined />,
      label: '工序管理',
    },
    {
      key: '/orders',
      icon: <ShoppingCartOutlined />,
      label: '订单管理',
    },
    {
      key: '/schedule',
      icon: <CalendarOutlined />,
      label: '生产排程',
    },
    {
      key: '/users',
      icon: <TeamOutlined />,
      label: '用户管理',
    },
    {
      key: '/statistics',
      icon: <BarChartOutlined />,
      label: '数据统计',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'change-password',
      icon: <KeyOutlined />,
      label: '修改密码',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  function handleLogout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    message.success('退出登录成功');
    navigate('/login');
  }

  // 修改密码
  const handleChangePassword = async (values: any) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/api/user/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          old_password: values.oldPassword,
          new_password: values.newPassword,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        message.success('密码修改成功');
        setPasswordModalVisible(false);
        form.resetFields();
      } else {
        message.error(data.error || '密码修改失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'change-password':
        setPasswordModalVisible(true);
        break;
      case 'logout':
        handleLogout();
        break;
      default:
        break;
    }
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };

  // 侧边栏内容
  const siderContent = (
    <>
      <div style={{
        height: 64,
        display: 'flex',
        alignItems: 'center',
        justifyContent: collapsed && !isMobile ? 'center' : 'flex-start',
        padding: collapsed && !isMobile ? '0' : '0 24px',
        color: 'white',
        fontSize: collapsed && !isMobile ? '16px' : '18px',
        fontWeight: 'bold',
        borderBottom: '1px solid #1f1f1f',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        {collapsed && !isMobile ? 'MDC' : COMPANY_NAME}
      </div>
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          borderRight: 0,
          background: 'linear-gradient(180deg, #001529 0%, #1f1f1f 100%)'
        }}
      />
    </>
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 桌面端和平板端侧边栏 */}
      {!isMobile && (
        <Sider
          theme="dark"
          width={220}
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          breakpoint="lg"
          collapsedWidth={isTablet ? 0 : 80}
          style={{
            background: 'linear-gradient(180deg, #001529 0%, #1f1f1f 100%)'
          }}
        >
          {siderContent}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title={
            <div style={{ color: 'white', fontWeight: 'bold' }}>
              {COMPANY_NAME}
            </div>
          }
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          width={280}
          styles={{
            body: { padding: 0 },
            header: {
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderBottom: 'none'
            }
          }}
        >
          <div style={{
            background: 'linear-gradient(180deg, #001529 0%, #1f1f1f 100%)',
            minHeight: '100%'
          }}>
            <Menu
              theme="dark"
              mode="inline"
              selectedKeys={[location.pathname]}
              items={menuItems}
              onClick={handleMenuClick}
              style={{
                borderRight: 0,
                background: 'transparent'
              }}
            />
          </div>
        </Drawer>
      )}

      <Layout>
        <Header style={{
          background: 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)',
          padding: '0 16px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '64px'
        }}>
          {/* 左侧 */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {isMobile ? (
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={() => setMobileDrawerVisible(true)}
                style={{ fontSize: '16px', color: 'white' }}
              />
            ) : (
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                style={{ fontSize: '16px', color: 'white' }}
              />
            )}
            <Text strong style={{
              fontSize: isMobile ? '16px' : '20px',
              color: 'white',
              marginLeft: '12px'
            }}>
              {isMobile ? '管理系统' : APP_NAME}
            </Text>
          </div>

          {/* 右侧 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            height: '100%'
          }}>
            {/* 通知按钮 */}
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: '16px', color: 'white' }}
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems, onClick: handleUserMenuClick }}
              placement="bottomRight"
              trigger={['click']}
              overlayStyle={{ marginTop: '8px' }}
            >
              <div style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '4px 8px',
                borderRadius: '6px',
                transition: 'background-color 0.2s',
              }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <Avatar
                  size={isMobile ? 'default' : 'large'}
                  icon={<UserOutlined />}
                  style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                />
                {!isMobile && currentUser && (
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    lineHeight: 1.2,
                    minWidth: '80px'
                  }}>
                    <span style={{
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'white',
                      whiteSpace: 'nowrap'
                    }}>
                      {currentUser.profile?.full_name || currentUser.username}
                    </span>
                    <span style={{
                      fontSize: '12px',
                      color: 'rgba(255,255,255,0.8)',
                      whiteSpace: 'nowrap'
                    }}>
                      {currentUser.role === 'admin' ? '系统管理员' :
                        currentUser.role === 'operator' ? '操作员' : '查看者'}
                    </span>
                  </div>
                )}
              </div>
            </Dropdown>
          </div>
        </Header>

        <Content style={{
          margin: isMobile ? '8px' : '16px',
          padding: isMobile ? '16px' : '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)',
          overflow: 'auto'
        }}>
          {children}
        </Content>
      </Layout>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={400}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="oldPassword"
            label="当前密码"
            rules={[
              { required: true, message: '请输入当前密码' },
            ]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' },
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => {
                setPasswordModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};

export default ResponsiveLayout;
