import React, { useEffect, useState } from 'react';
import { Alert, Card, Descriptions, Tag, Typography, Space, Button } from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import config, {
  API_BASE_URL,
  APP_NAME,
  APP_VERSION,
  APP_ENV,
  ENABLE_DEBUG,
  isDevelopment,
  isProduction,
  log
} from '../config';

const { Title, Text } = Typography;

interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

const ConfigValidator: React.FC = () => {
  const [validationResult, setValidationResult] = useState<ConfigValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  });
  const [isVisible, setIsVisible] = useState(false);

  // 验证配置
  const validateConfig = (): ConfigValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证API URL
    if (!API_BASE_URL) {
      errors.push('API_BASE_URL 未配置');
    } else {
      try {
        new URL(API_BASE_URL);
      } catch {
        errors.push('API_BASE_URL 格式无效');
      }
    }

    // 验证应用名称
    if (!APP_NAME) {
      warnings.push('APP_NAME 未配置，使用默认值');
    }

    // 验证版本号
    if (!APP_VERSION) {
      warnings.push('APP_VERSION 未配置，使用默认值');
    }

    // 验证环境
    if (!['development', 'test', 'production'].includes(APP_ENV)) {
      warnings.push(`未知的环境类型: ${APP_ENV}`);
    }

    // 生产环境特殊检查
    if (isProduction()) {
      if (ENABLE_DEBUG) {
        warnings.push('生产环境建议关闭调试模式');
      }
      if (API_BASE_URL.includes('localhost')) {
        warnings.push('生产环境使用localhost API，请确认这是Docker环境部署');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  };

  useEffect(() => {
    const result = validateConfig();
    setValidationResult(result);

    // 开发环境下自动显示配置信息
    if (isDevelopment() && ENABLE_DEBUG) {
      setIsVisible(true);
      log.info('配置验证结果:', result);
    }

    // 如果有错误，强制显示
    if (!result.isValid) {
      setIsVisible(true);
    }

    // 生产环境下，如果有警告也显示（比如localhost API警告）
    if (isProduction() && result.warnings.length > 0) {
      setIsVisible(true);
    }
  }, []);

  // 获取环境标签颜色
  const getEnvTagColor = (env: string) => {
    switch (env) {
      case 'development': return 'blue';
      case 'test': return 'orange';
      case 'production': return 'green';
      default: return 'default';
    }
  };

  // 获取状态图标
  const getStatusIcon = () => {
    if (!validationResult.isValid) {
      return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
    if (validationResult.warnings.length > 0) {
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    }
    return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
  };

  // 如果不显示且没有错误，则不渲染
  if (!isVisible && validationResult.isValid) {
    return null;
  }

  return (
    <Card
      size="small"
      style={{
        margin: '16px',
        border: validationResult.isValid ? '1px solid #d9d9d9' : '1px solid #ff4d4f'
      }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            {getStatusIcon()}
            <Title level={5} style={{ margin: 0 }}>
              配置状态
            </Title>
            <Tag color={getEnvTagColor(APP_ENV)}>
              {APP_ENV.toUpperCase()}
            </Tag>
          </Space>
          <Space>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => {
                const result = validateConfig();
                setValidationResult(result);
                log.info('配置重新验证:', result);
              }}
            >
              重新验证
            </Button>
            <Button
              size="small"
              onClick={() => setIsVisible(false)}
            >
              隐藏
            </Button>
          </Space>
        </div>

        {/* 错误信息 */}
        {validationResult.errors.length > 0 && (
          <Alert
            type="error"
            message="配置错误"
            description={
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {validationResult.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            }
            showIcon
          />
        )}

        {/* 警告信息 */}
        {validationResult.warnings.length > 0 && (
          <Alert
            type="warning"
            message="配置警告"
            description={
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {validationResult.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            }
            showIcon
          />
        )}

        {/* 配置详情 */}
        <Descriptions size="small" column={1} bordered>
          <Descriptions.Item label="应用名称">
            <Text code>{APP_NAME}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="应用版本">
            <Text code>{APP_VERSION}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="运行环境">
            <Tag color={getEnvTagColor(APP_ENV)}>{APP_ENV}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="API地址">
            <Text code>{API_BASE_URL}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="API超时">
            <Text code>{config.api.timeout}ms</Text>
          </Descriptions.Item>
          <Descriptions.Item label="调试模式">
            <Tag color={ENABLE_DEBUG ? 'green' : 'red'}>
              {ENABLE_DEBUG ? '开启' : '关闭'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Mock数据">
            <Tag color={config.features.enableMock ? 'green' : 'red'}>
              {config.features.enableMock ? '开启' : '关闭'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="数据分析">
            <Tag color={config.features.enableAnalytics ? 'green' : 'red'}>
              {config.features.enableAnalytics ? '开启' : '关闭'}
            </Tag>
          </Descriptions.Item>
        </Descriptions>

        {/* 开发环境提示 */}
        {isDevelopment() && (
          <Alert
            type="info"
            message="开发环境"
            description="当前运行在开发环境，配置信息仅在开发时显示。生产环境部署时请确保配置正确。"
            showIcon
          />
        )}
      </Space>
    </Card>
  );
};

export default ConfigValidator;
