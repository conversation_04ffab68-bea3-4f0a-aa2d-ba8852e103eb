# 13_server_admin

制造数据中心管理系统前端界面

## 🎯 功能特性

### 核心管理功能
- 👥 **用户管理** - 用户CRUD、角色权限、状态管理
- 🔧 **设备管理** - 设备信息、状态监控、维护记录
- 📱 **产品管理** - 产品信息、规格参数、分类管理
- ⚙️ **工序管理** - 工序流程、时间管理、设备关联
- 📦 **订单管理** - 订单全生命周期、状态跟踪、优先级管理
- 🏭 **生产任务** - 任务分配、进度跟踪、质量控制
- 📅 **每日计划** - 班次管理、产量计划、完成率统计

### 用户体验
- 📱 **响应式设计** - 完美适配电脑、平板、手机
- 🎨 **现代化UI** - 基于Ant Design 5的美观界面
- 🔐 **权限控制** - 基于角色的细粒度权限管理
- 🌐 **国际化支持** - 中文界面，支持多语言扩展
- 📊 **数据可视化** - 统计图表、进度条、状态标签

## 🛠 技术栈

- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design 5** - 企业级UI组件库
- **React Router 7** - 单页应用路由
- **Axios** - HTTP客户端
- **Day.js** - 轻量级日期处理库

## 🔧 环境配置

### 环境变量配置
项目使用环境变量来管理不同环境的配置，支持开发、测试、生产等多种环境。

#### 主要配置项
```bash
# API服务配置
REACT_APP_API_BASE_URL=http://mdc_server_api:9005
REACT_APP_API_TIMEOUT=10000

# 应用配置
REACT_APP_APP_NAME=制造数据中心管理系统
REACT_APP_COMPANY_NAME=制造数据中心

# 功能开关
REACT_APP_ENABLE_DEBUG=true
REACT_APP_ENABLE_MOCK=false
```

#### 配置文件
- `.env` - 默认配置
- `.env.development` - 开发环境配置
- `.env.test` - 测试环境配置
- `.env.production` - 生产环境配置
- `.env.local` - 本地配置（不提交到版本控制）

详细配置说明请参考 [ENV_CONFIG.md](./ENV_CONFIG.md)

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发环境
```bash
# 使用默认配置启动
npm start

# 使用开发环境配置
npm run start:dev

# 使用测试环境配置
npm run start:test
```

### 生产构建
```bash
# 构建生产版本
npm run build:prod

# 预览生产版本
npm run preview
```

### 测试
```bash
# 运行测试
npm test

# 生成测试覆盖率报告
npm run test:coverage
```

## 📁 项目结构

```
13_server_admin/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 通用组件
│   │   ├── Layout.tsx     # 原始布局组件
│   │   ├── ResponsiveLayout.tsx  # 响应式布局
│   │   └── ConfigValidator.tsx   # 配置验证组件
│   ├── config/            # 配置管理
│   │   └── index.ts       # 环境配置
│   ├── pages/             # 页面组件
│   │   ├── Login.tsx      # 登录页面
│   │   ├── Dashboard.tsx  # 仪表板
│   │   ├── UserManagement.tsx        # 用户管理
│   │   ├── DeviceManagement.tsx      # 设备管理
│   │   ├── ProductManagement.tsx     # 产品管理
│   │   ├── ProcessManagement.tsx     # 工序管理
│   │   ├── OrderManagement.tsx       # 订单管理
│   │   ├── ProductionTaskManagement.tsx  # 生产任务
│   │   └── DailyPlanManagement.tsx   # 每日计划
│   ├── services/          # API服务
│   │   └── api.ts         # API接口定义
│   ├── App.tsx            # 应用主组件
│   └── index.tsx          # 应用入口
├── .env                   # 默认环境配置
├── .env.development       # 开发环境配置
├── .env.test             # 测试环境配置
├── .env.production       # 生产环境配置
└── ENV_CONFIG.md         # 环境配置说明
```

## 🌐 API集成

### 后端服务
- **API地址**: 通过 `REACT_APP_API_BASE_URL` 配置
- **认证方式**: JWT Token
- **请求格式**: JSON
- **响应格式**: 统一的API响应格式

### API模块
- **认证API** - 登录、刷新token、用户信息
- **用户管理API** - 用户CRUD、权限管理
- **设备管理API** - 设备信息管理
- **产品管理API** - 产品信息管理
- **工序管理API** - 工序流程管理
- **订单管理API** - 订单全生命周期管理
- **生产任务API** - 任务管理和控制
- **每日计划API** - 计划管理和统计

## 📱 响应式设计

### 断点设置
- **手机端**: < 768px
- **平板端**: 768px - 1199px
- **电脑端**: ≥ 1200px

### 适配特性
- **布局自适应**: 侧边栏自动折叠/展开
- **表格响应式**: 列自动隐藏/显示
- **表单优化**: 移动端友好的表单布局
- **触摸优化**: 适合触摸操作的按钮大小

## 🔐 权限管理

### 角色类型
- **管理员 (admin)**: 所有权限
- **操作员 (operator)**: 设备、产品、工序、订单、任务管理权限
- **查看者 (viewer)**: 只读权限

### 权限控制
- **路由级权限**: 基于角色的路由访问控制
- **功能级权限**: 按钮、操作的细粒度权限控制
- **数据级权限**: 基于用户角色的数据访问控制

## 🎨 主题配置

### 颜色配置
```bash
REACT_APP_PRIMARY_COLOR=#1890ff    # 主色调
REACT_APP_SUCCESS_COLOR=#52c41a    # 成功色
REACT_APP_WARNING_COLOR=#faad14    # 警告色
REACT_APP_ERROR_COLOR=#ff4d4f      # 错误色
```

### 自定义主题
可以通过环境变量自定义应用的主题颜色，支持品牌化定制。

## 🧪 测试

### 测试类型
- **单元测试**: 组件和工具函数测试
- **集成测试**: API集成测试
- **E2E测试**: 端到端功能测试

### 测试工具
- **Jest**: 测试框架
- **React Testing Library**: React组件测试
- **MSW**: API Mock服务

## 📦 部署

### 构建优化
- **代码分割**: 按路由自动分割代码
- **资源压缩**: CSS、JS文件压缩
- **缓存优化**: 静态资源缓存策略

### 部署方式
- **静态部署**: 构建后部署到CDN或静态服务器
- **Docker部署**: 容器化部署
- **CI/CD**: 自动化构建和部署

## 🔍 故障排除

### 常见问题
1. **API连接失败**: 检查 `REACT_APP_API_BASE_URL` 配置
2. **环境变量不生效**: 确保变量名以 `REACT_APP_` 开头
3. **权限错误**: 检查用户角色和权限配置
4. **响应式问题**: 检查断点设置和CSS媒体查询

### 调试工具
- **配置验证组件**: 自动检查配置问题
- **开发者工具**: 浏览器调试工具
- **日志系统**: 分级日志输出

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 支持

如有问题，请联系开发团队或查看项目文档。
