package logger

import (
	"io"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger 全局日志实例
// 使用logrus作为底层日志库，支持结构化日志和多种输出格式
var Logger *logrus.Logger

// LogRotationConfig 日志轮转配置结构体
// 用于配置日志文件的自动轮转、压缩和清理策略
type LogRotationConfig struct {
	Enabled    bool // 是否启用日志轮转
	MaxSize    int  // 单个日志文件最大大小(MB)
	MaxAge     int  // 日志文件保留天数
	MaxBackups int  // 保留的旧日志文件数量
	Compress   bool // 是否压缩旧日志文件
}

// InitLogger 初始化日志系统
// 根据配置参数设置日志级别、格式和输出目标
// 支持文本和JSON格式，可输出到控制台或文件，支持日志轮转
// 参数:
//   - level: 日志级别，支持"debug", "info", "warn", "error"
//   - format: 日志格式，"json"为结构化格式，其他为文本格式
//   - output: 输出目标，"stdout"为控制台，其他为文件路径
// 示例:
//   InitLogger("info", "json", "/var/log/app.log")
//   InitLogger("debug", "text", "stdout")
func InitLogger(level, format, output string) {
	InitLoggerWithRotation(level, format, output, LogRotationConfig{})
}

// InitLoggerWithRotation 初始化带日志轮转功能的日志系统
// 支持日志文件自动轮转、压缩和清理功能
// 参数:
//   - level: 日志级别，支持"debug", "info", "warn", "error"
//   - format: 日志格式，"json"为结构化格式，其他为文本格式
//   - output: 输出目标，"stdout"为控制台，其他为文件路径
//   - rotation: 日志轮转配置
// 示例:
//   rotation := LogRotationConfig{
//       Enabled: true,
//       MaxSize: 100,    // 100MB
//       MaxAge: 30,      // 30天
//       MaxBackups: 10,  // 10个备份文件
//       Compress: true,  // 压缩旧文件
//   }
//   InitLoggerWithRotation("info", "json", "/var/log/app.log", rotation)
func InitLoggerWithRotation(level, format, output string, rotation LogRotationConfig) {
	// 创建新的日志实例
	Logger = logrus.New()

	// 根据字符串设置日志级别
	// 日志级别从低到高：debug < info < warn < error < fatal
	switch level {
	case "debug":
		Logger.SetLevel(logrus.DebugLevel) // 显示所有级别的日志
	case "info":
		Logger.SetLevel(logrus.InfoLevel)  // 显示info及以上级别的日志
	case "warn":
		Logger.SetLevel(logrus.WarnLevel)  // 显示warn及以上级别的日志
	case "error":
		Logger.SetLevel(logrus.ErrorLevel) // 只显示error和fatal级别的日志
	default:
		Logger.SetLevel(logrus.InfoLevel)  // 默认使用info级别
	}

	// 根据配置设置日志输出格式
	if format == "json" {
		// JSON格式：结构化日志，便于日志分析工具处理
		Logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05", // 自定义时间格式
		})
	} else {
		// 文本格式：人类可读，便于开发调试
		Logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,                   // 显示完整时间戳
			TimestampFormat: "2006-01-02 15:04:05", // 自定义时间格式
		})
	}

	// 设置日志输出目标
	var writer io.Writer
	if output == "" || output == "stdout" {
		// 输出到标准输出
		writer = os.Stdout
	} else if output == "stderr" {
		// 输出到标准错误
		writer = os.Stderr
	} else {
		// 输出到文件
		if rotation.Enabled {
			// 启用日志轮转
			writer = createRotatingWriter(output, rotation)
		} else {
			// 普通文件输出
			file, err := os.OpenFile(output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				// 文件打开失败，回退到标准输出
				Logger.Errorf("Failed to open log file %s: %v, falling back to stdout", output, err)
				writer = os.Stdout
			} else {
				writer = file
			}
		}
	}

	Logger.SetOutput(writer)

	// 记录日志系统初始化信息
	if rotation.Enabled && output != "stdout" && output != "stderr" {
		Logger.Infof("Logger initialized with rotation: file=%s, maxSize=%dMB, maxAge=%dd, maxBackups=%d, compress=%v",
			output, rotation.MaxSize, rotation.MaxAge, rotation.MaxBackups, rotation.Compress)
	} else {
		Logger.Infof("Logger initialized: level=%s, format=%s, output=%s", level, format, output)
	}
}

// createRotatingWriter 创建支持轮转的日志写入器
// 使用lumberjack库实现日志文件的自动轮转、压缩和清理
// 参数:
//   - filename: 日志文件路径
//   - rotation: 日志轮转配置
// 返回:
//   - io.Writer: 支持轮转的写入器
func createRotatingWriter(filename string, rotation LogRotationConfig) io.Writer {
	// 确保日志目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		// 目录创建失败，记录错误并回退到标准输出
		logrus.Errorf("Failed to create log directory %s: %v, falling back to stdout", dir, err)
		return os.Stdout
	}

	// 设置默认值
	maxSize := rotation.MaxSize
	if maxSize <= 0 {
		maxSize = 100 // 默认100MB
	}

	maxAge := rotation.MaxAge
	if maxAge <= 0 {
		maxAge = 30 // 默认30天
	}

	maxBackups := rotation.MaxBackups
	if maxBackups <= 0 {
		maxBackups = 10 // 默认10个备份文件
	}

	// 创建lumberjack轮转写入器
	return &lumberjack.Logger{
		Filename:   filename,           // 日志文件路径
		MaxSize:    maxSize,            // 单个文件最大大小(MB)
		MaxAge:     maxAge,             // 文件保留天数
		MaxBackups: maxBackups,         // 保留的备份文件数量
		Compress:   rotation.Compress,  // 是否压缩旧文件
		LocalTime:  true,               // 使用本地时间
	}
}

// InitLoggerFromConfig 从配置对象初始化日志系统
// 支持完整的日志轮转配置
// 参数:
//   - config: 日志配置对象
// 示例:
//   config := LoggingConfig{
//       Level: "info",
//       Format: "json",
//       Output: "/var/log/app.log",
//       Rotation: LogRotationConfig{
//           Enabled: true,
//           MaxSize: 100,
//           MaxAge: 30,
//           MaxBackups: 10,
//           Compress: true,
//       },
//   }
//   InitLoggerFromConfig(config)
func InitLoggerFromConfig(config LoggingConfig) {
	rotation := LogRotationConfig{
		Enabled:    config.Rotation.Enabled,
		MaxSize:    config.Rotation.MaxSize,
		MaxAge:     config.Rotation.MaxAge,
		MaxBackups: config.Rotation.MaxBackups,
		Compress:   config.Rotation.Compress,
	}
	InitLoggerWithRotation(config.Level, config.Format, config.Output, rotation)
}

// LoggingConfig 日志配置结构体（为了兼容性）
// 与shared/config包中的LoggingConfig保持一致
type LoggingConfig struct {
	Level    string            `yaml:"level"`
	Format   string            `yaml:"format"`
	Output   string            `yaml:"output"`
	Rotation LogRotationConfig `yaml:"rotation"`
}

// Info 记录信息级别日志
// 用于记录一般的程序运行信息，如服务启动、配置加载等
// 参数:
//   - args: 可变参数，支持多个值的输出
// 示例:
//   Info("服务启动成功")
//   Info("连接数据库:", "localhost:5432")
func Info(args ...interface{}) {
	if Logger != nil {
		Logger.Info(args...)
	}
}

// Infof 记录格式化的信息级别日志
// 支持printf风格的格式化字符串，便于输出结构化信息
// 参数:
//   - format: 格式化字符串，支持%s, %d, %v等占位符
//   - args: 格式化参数
// 示例:
//   Infof("用户 %s 登录成功，IP: %s", username, clientIP)
//   Infof("处理了 %d 条数据，耗时 %v", count, duration)
func Infof(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Infof(format, args...)
	}
}

// Debug 记录调试级别日志
// 用于开发和调试阶段的详细信息输出
// 只在日志级别设置为debug时才会输出
// 参数:
//   - args: 可变参数，支持多个值的输出
// 示例:
//   Debug("进入函数 processData")
//   Debug("变量值:", variable)
func Debug(args ...interface{}) {
	if Logger != nil {
		Logger.Debug(args...)
	}
}

// Debugf 记录格式化的调试级别日志
// 支持详细的调试信息输出，便于问题排查
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
// 示例:
//   Debugf("SQL查询: %s, 参数: %v", query, params)
//   Debugf("HTTP请求: %s %s, 响应码: %d", method, url, statusCode)
func Debugf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Debugf(format, args...)
	}
}

// Warn 记录警告级别日志
// 用于记录可能的问题或异常情况，但不影响程序继续运行
// 参数:
//   - args: 可变参数，支持多个值的输出
// 示例:
//   Warn("配置文件不存在，使用默认配置")
//   Warn("连接超时，正在重试")
func Warn(args ...interface{}) {
	if Logger != nil {
		Logger.Warn(args...)
	}
}

// Warnf 记录格式化的警告级别日志
// 用于输出格式化的警告信息
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
// 示例:
//   Warnf("磁盘使用率达到 %d%%，请注意清理", usage)
//   Warnf("API调用失败，错误码: %d，将在 %d 秒后重试", code, delay)
func Warnf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Warnf(format, args...)
	}
}

// Error 记录错误级别日志
// 用于记录程序运行中的错误，但程序可以继续运行
// 参数:
//   - args: 可变参数，支持多个值的输出
// 示例:
//   Error("数据库连接失败")
//   Error("处理请求时发生错误:", err)
func Error(args ...interface{}) {
	if Logger != nil {
		Logger.Error(args...)
	}
}

// Errorf 记录格式化的错误级别日志
// 用于输出详细的错误信息，便于问题定位
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
// 示例:
//   Errorf("用户 %s 认证失败: %v", userID, err)
//   Errorf("文件 %s 读取失败，错误: %s", filename, err.Error())
func Errorf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Errorf(format, args...)
	}
}

// Fatal 记录致命错误日志并退出程序
// 用于记录导致程序无法继续运行的严重错误
// 调用后程序会立即退出（os.Exit(1)）
// 参数:
//   - args: 可变参数，支持多个值的输出
// 示例:
//   Fatal("配置文件加载失败，程序无法启动")
//   Fatal("关键服务连接失败:", err)
func Fatal(args ...interface{}) {
	if Logger != nil {
		Logger.Fatal(args...)
	}
}

// Fatalf 记录格式化的致命错误日志并退出程序
// 支持格式化输出致命错误信息
// 调用后程序会立即退出（os.Exit(1)）
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
// 示例:
//   Fatalf("端口 %d 已被占用，程序无法启动", port)
//   Fatalf("初始化失败: %v", err)
func Fatalf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Fatalf(format, args...)
	}
}
