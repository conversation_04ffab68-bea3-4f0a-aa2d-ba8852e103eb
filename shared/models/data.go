package models

import (
	"encoding/json"
	"time"
)

// DeviceData 设备数据结构体
// 用于在整个数据流程中传递设备采集的数据
// 支持原始数据、处理后数据和元数据的存储
// 兼容JSON和BSON序列化，适用于不同的存储系统
type DeviceData struct {
	DeviceID      string                 `json:"device_id" bson:"device_id"`           // 设备唯一标识符，如"sensor_001"
	Timestamp     time.Time              `json:"timestamp" bson:"timestamp"`           // 数据采集时间戳，用于时序分析
	DataType      string                 `json:"data_type" bson:"data_type"`           // 数据类型，如"sensor"、"status"、"config"
	RawData       map[string]interface{} `json:"raw_data" bson:"raw_data"`             // 原始数据，设备直接上报的数据
	ProcessedData map[string]interface{} `json:"processed_data,omitempty" bson:"processed_data,omitempty"` // 处理后数据，经过清洗和转换
	Metadata      map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`             // 元数据，包含处理信息、位置等
}

// ToJSON 将设备数据转换为JSON字符串
// 用于数据序列化，便于网络传输和存储
// 返回:
//   - string: JSON格式的字符串
//   - error: 序列化过程中的错误
// 示例:
//   data := &DeviceData{DeviceID: "sensor_001", Timestamp: time.Now()}
//   jsonStr, err := data.ToJSON()
//   if err != nil {
//       log.Printf("序列化失败: %v", err)
//   }
func (d *DeviceData) ToJSON() (string, error) {
	// 使用标准库进行JSON序列化
	data, err := json.Marshal(d)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析设备数据
// 用于数据反序列化，从存储或网络中恢复数据对象
// 参数:
//   - jsonStr: JSON格式的字符串
// 返回:
//   - error: 解析过程中的错误
// 示例:
//   var data DeviceData
//   err := data.FromJSON(`{"device_id":"sensor_001","timestamp":"2025-05-26T10:00:00Z"}`)
//   if err != nil {
//       log.Printf("解析失败: %v", err)
//   }
func (d *DeviceData) FromJSON(jsonStr string) error {
	// 使用标准库进行JSON反序列化
	return json.Unmarshal([]byte(jsonStr), d)
}

// DeviceInfo 设备信息结构体
// 用于存储设备的基本信息和配置
// 主要在设备管理和API查询中使用
type DeviceInfo struct {
	DeviceID    string            `json:"device_id" bson:"device_id"`       // 设备唯一标识符
	DeviceName  string            `json:"device_name" bson:"device_name"`   // 设备显示名称，如"温度传感器-01"
	DeviceType  string            `json:"device_type" bson:"device_type"`   // 设备类型，如"temperature_sensor"
	Location    string            `json:"location" bson:"location"`         // 设备安装位置，如"机房A-机柜1"
	Status      string            `json:"status" bson:"status"`             // 设备状态：online, offline, error
	Config      map[string]string `json:"config" bson:"config"`             // 设备配置参数，如采样频率等
	CreatedAt   time.Time         `json:"created_at" bson:"created_at"`     // 设备创建时间
	UpdatedAt   time.Time         `json:"updated_at" bson:"updated_at"`     // 设备信息最后更新时间
}

// ProcessingRule 数据处理规则结构体
// 定义如何处理特定类型设备的数据
// 支持脚本化的数据转换和清洗逻辑
type ProcessingRule struct {
	RuleID      string                 `json:"rule_id" bson:"rule_id"`           // 规则唯一标识符
	DeviceType  string                 `json:"device_type" bson:"device_type"`   // 适用的设备类型
	DataType    string                 `json:"data_type" bson:"data_type"`       // 适用的数据类型
	Script      string                 `json:"script" bson:"script"`             // 处理脚本，支持JavaScript等
	Enabled     bool                   `json:"enabled" bson:"enabled"`           // 规则是否启用
	Config      map[string]interface{} `json:"config" bson:"config"`             // 规则配置参数
	CreatedAt   time.Time              `json:"created_at" bson:"created_at"`     // 规则创建时间
	UpdatedAt   time.Time              `json:"updated_at" bson:"updated_at"`     // 规则最后更新时间
}

// DataBatch 批量数据结构体
// 用于批量处理和传输设备数据
// 提高数据处理效率，减少网络开销
type DataBatch struct {
	BatchID   string        `json:"batch_id"`   // 批次唯一标识符
	Data      []DeviceData  `json:"data"`       // 批量数据数组
	Timestamp time.Time     `json:"timestamp"`  // 批次创建时间
	Count     int           `json:"count"`      // 数据条数，用于快速统计
}

// ServiceStatus 服务状态结构体
// 用于监控各个微服务的运行状态
// 支持健康检查和性能指标收集
type ServiceStatus struct {
	ServiceName string                 `json:"service_name"`        // 服务名称，如"device-collect"
	Status      string                 `json:"status"`              // 服务状态：running, stopped, error
	Timestamp   time.Time              `json:"timestamp"`           // 状态更新时间
	Metrics     map[string]interface{} `json:"metrics,omitempty"`   // 性能指标，如CPU、内存使用率
}

// APIResponse 通用API响应结构体
// 统一所有API接口的响应格式
// 提供标准化的成功/失败状态和错误信息
type APIResponse struct {
	Success bool        `json:"success"`           // 请求是否成功
	Message string      `json:"message"`           // 响应消息，描述操作结果
	Data    interface{} `json:"data,omitempty"`    // 响应数据，成功时返回具体数据
	Error   string      `json:"error,omitempty"`   // 错误信息，失败时返回错误详情
}
