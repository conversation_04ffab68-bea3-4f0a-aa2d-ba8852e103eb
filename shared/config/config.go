package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 全局配置结构体
// 包含所有服务的配置信息，用于统一管理系统配置
// 支持从YAML文件加载配置，并可通过环境变量覆盖
// 用于设备数据采集服务集的统一配置管理
type Config struct {
	Service       ServiceConfig       `yaml:"service"`        // 服务基础配置（端口、主机等）
	Redis         RedisConfig         `yaml:"redis"`          // Redis缓存配置，用于临时数据存储
	Cache         CacheConfig         `yaml:"cache"`          // 缓存管理配置，用于控制缓存更新策略
	SQLite        SQLiteConfig        `yaml:"sqlite"`         // SQLite本地数据库配置，用于数据持久化
	NATS          NATSConfig          `yaml:"nats"`           // NATS消息队列配置，用于服务间通信
	InfluxDB      InfluxDBConfig      `yaml:"influxdb"`       // InfluxDB时序数据库配置，用于存储传感器数据
	MongoDB       MongoDBConfig       `yaml:"mongodb"`        // MongoDB文档数据库配置，用于存储设备元数据
	Logging       LoggingConfig       `yaml:"logging"`        // 日志系统配置，统一管理日志输出
	Stability     StabilityConfig     `yaml:"stability"`      // 稳定性管理配置，用于内存监控和熔断机制
	DeviceMonitor DeviceMonitorConfig `yaml:"device_monitor"` // 设备离线监控配置，用于检测设备连接状态
}

// ServiceConfig 服务基础配置结构体
// 定义每个微服务的基本运行参数
// 包括网络监听、调试模式、超时设置等
type ServiceConfig struct {
	Name    string `yaml:"name"`    // 服务名称，用于日志标识和服务发现，如"device-collect"
	Port    int    `yaml:"port"`    // 监听端口号，每个服务使用不同端口避免冲突
	Host    string `yaml:"host"`    // 监听主机地址，"0.0.0.0"表示监听所有接口
	Debug   bool   `yaml:"debug"`   // 调试模式开关，影响日志级别和Gin框架模式
	Timeout int    `yaml:"timeout"` // 请求超时时间（秒），用于HTTP客户端和数据库连接
}

// RedisConfig Redis缓存配置结构体
// Redis用作临时数据存储，缓存设备数据等待后续处理
// 支持连接池配置以提高并发性能
type RedisConfig struct {
	Host             string        `yaml:"host"`               // Redis服务器主机地址，如"localhost"或"redis"
	Port             int           `yaml:"port"`               // Redis服务器端口，默认6379
	Password         string        `yaml:"password"`           // Redis认证密码，空字符串表示无密码认证
	DB               int           `yaml:"db"`                 // Redis数据库编号，0-15，用于数据隔离
	PoolSize         int           `yaml:"pool_size"`          // 连接池大小，控制最大并发连接数
	DataExpiration   time.Duration `yaml:"data_expiration"`    // 数据过期时间，如"168h"表示7天
	MaxSequenceValue uint64        `yaml:"max_sequence_value"` // 序列计数器最大值，达到后重置为1，默认16777215
}

// CacheConfig 缓存管理配置结构体
// 用于控制缓存更新策略、频率和行为
// 支持动态调整缓存更新间隔和启用/禁用自动更新
type CacheConfig struct {
	UpdateInterval        int  `yaml:"update_interval"`          // 缓存更新间隔（秒），控制自动更新频率，默认5秒
	EnableAutoUpdate      bool `yaml:"enable_auto_update"`       // 是否启用自动缓存更新，默认true
	ForceRefreshOnStartup bool `yaml:"force_refresh_on_startup"` // 启动时是否强制刷新缓存，默认true
}

// SQLiteConfig SQLite本地数据库配置结构体
// SQLite用于本地持久化存储，作为数据处理的中间层
// 支持数据保留策略和批处理配置
type SQLiteConfig struct {
	DataDir       string `yaml:"data_dir"`       // 数据文件存储目录路径，如"./data/sqlite"
	RetentionDays int    `yaml:"retention_days"` // 数据保留天数，超期数据自动清理
	BatchSize     int    `yaml:"batch_size"`     // 批处理大小，控制单次处理的记录数
	FlushInterval int    `yaml:"flush_interval"` // 刷新间隔（秒），定时处理数据的频率
}

// NATSConfig NATS消息队列配置结构体
// NATS JetStream用于服务间异步消息传递和数据流转
// 支持持久化消息存储和消费者管理
type NATSConfig struct {
	URL       string `yaml:"url"`        // NATS服务器连接URL，如"nats://localhost:4222"
	Subject   string `yaml:"subject"`    // 消息主题，支持通配符模式，如"device.data.*"
	Stream    string `yaml:"stream"`     // JetStream流名称，用于持久化消息存储
	Consumer  string `yaml:"consumer"`   // 消费者名称，用于消息消费管理和状态跟踪
	BatchSize int    `yaml:"batch_size"` // 批量拉取消息数量，提高消息处理效率
}

// InfluxDBConfig InfluxDB时序数据库配置结构体
// InfluxDB用于存储时序数据，支持高效的时间范围查询和聚合
// 适合存储传感器数据等时间序列信息
type InfluxDBConfig struct {
	URL    string `yaml:"url"`    // InfluxDB服务器URL，如"http://localhost:8086"
	Token  string `yaml:"token"`  // 访问令牌，用于API认证和权限控制
	Org    string `yaml:"org"`    // 组织名称，InfluxDB的命名空间概念
	Bucket string `yaml:"bucket"` // 存储桶名称，类似数据库的概念，存储相关数据
}

// MongoDBConfig MongoDB文档数据库配置结构体
// MongoDB用于存储设备元数据、配置信息和聚合统计数据
// 支持复杂查询和文档结构存储
type MongoDBConfig struct {
	URI        string `yaml:"uri"`        // MongoDB连接URI，包含认证信息和连接参数
	Database   string `yaml:"database"`   // 数据库名称，如"device_db"
	Collection string `yaml:"collection"` // 集合名称，类似关系数据库的表概念
}

// LoggingConfig 日志系统配置结构体
// 统一管理所有服务的日志输出格式、级别和目标
// 支持结构化日志、多种输出方式和日志文件轮转
type LoggingConfig struct {
	Level      string            `yaml:"level"`       // 日志级别：debug, info, warn, error, fatal
	Format     string            `yaml:"format"`      // 日志格式：text（人类可读）, json（结构化）
	Output     string            `yaml:"output"`      // 输出目标：stdout, stderr, 或具体文件路径
	Rotation   LogRotationConfig `yaml:"rotation"`    // 日志轮转配置
	DeviceLogs bool              `yaml:"device_logs"` // 是否记录设备数据日志，true时记录每个设备推送的数据到单独的日志文件
}

// LogRotationConfig 日志轮转配置结构体
// 配置日志文件的自动轮转、压缩和清理策略
type LogRotationConfig struct {
	Enabled    bool `yaml:"enabled"`     // 是否启用日志轮转，默认false
	MaxSize    int  `yaml:"max_size"`    // 单个日志文件最大大小(MB)，默认100MB
	MaxAge     int  `yaml:"max_age"`     // 日志文件保留天数，默认30天
	MaxBackups int  `yaml:"max_backups"` // 保留的旧日志文件数量，默认10个
	Compress   bool `yaml:"compress"`    // 是否压缩旧日志文件，默认true
}

// StabilityConfig 稳定性管理配置结构体
// 用于配置内存监控、熔断机制和系统稳定性保障参数
// 支持根据实际部署环境调整稳定性策略
type StabilityConfig struct {
	MaxMemoryMB   uint64 `yaml:"max_memory_mb"`   // 最大内存限制(MB)，超过此值触发熔断器，默认512MB
	GCThresholdMB uint64 `yaml:"gc_threshold_mb"` // GC触发阈值(MB)，达到此值主动触发垃圾回收，默认256MB
}

// DeviceMonitorConfig 设备离线监控配置结构体
// 用于配置设备连接状态监控、离线检测和通知机制
// 支持根据业务需求调整监控策略和超时参数
type DeviceMonitorConfig struct {
	Enabled           bool   `yaml:"enabled"`             // 是否启用设备离线检测功能，默认true
	CheckInterval     int    `yaml:"check_interval"`      // 检查间隔(秒)，每隔多少秒检查一次设备状态，默认30秒
	OfflineTimeout    int    `yaml:"offline_timeout"`     // 离线超时(秒)，超过多少秒没有数据则判定为离线，默认120秒
	RedisKeyPrefix    string `yaml:"redis_key_prefix"`    // Redis中存储设备最后活跃时间的key前缀，默认"device:last_seen:"
	CleanupInterval   int    `yaml:"cleanup_interval"`    // 清理间隔(秒)，每隔多少秒清理过期的设备状态记录，默认3600秒
	MaxOfflineDevices int    `yaml:"max_offline_devices"` // 最大离线设备数量，防止内存泄露，默认1000
}

// LoadConfig 从指定路径加载配置文件
// 支持YAML格式的配置文件，并可通过环境变量覆盖配置项
// 参数:
//   - configPath: 配置文件路径，通常为"config.yml"
//
// 返回:
//   - *Config: 解析后的配置对象，包含所有服务配置
//   - error: 加载或解析过程中的错误
//
// 示例:
//
//	config, err := LoadConfig("config.yml")
//	if err != nil {
//	    log.Fatal("配置加载失败:", err)
//	}
//	fmt.Printf("服务端口: %d\n", config.Service.Port)
func LoadConfig(configPath string) (*Config, error) {
	// 创建配置对象实例
	config := &Config{}

	// 读取配置文件内容到内存
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析YAML格式的配置内容到结构体
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 从环境变量覆盖配置项（支持容器化部署）
	config.overrideFromEnv()

	return config, nil
}

// overrideFromEnv 从环境变量覆盖配置项
// 支持在容器化部署时通过环境变量动态调整配置
// 优先级：环境变量 > 配置文件 > 默认值
// 支持的环境变量：
//   - SERVICE_HOST: 服务监听主机
//   - SERVICE_PORT: 服务监听端口
//   - REDIS_HOST: Redis主机地址
//   - REDIS_PORT: Redis端口
//   - NATS_URL: NATS连接URL
//   - INFLUXDB_URL: InfluxDB连接URL
//   - MONGODB_URI: MongoDB连接URI
//
// 示例:
//
//	export SERVICE_PORT=9005
//	export REDIS_HOST=redis-server
//	# 配置会自动从环境变量更新
func (c *Config) overrideFromEnv() {
	// 覆盖服务主机配置
	if host := os.Getenv("SERVICE_HOST"); host != "" {
		c.Service.Host = host
	}
	// 覆盖服务端口配置
	if port := os.Getenv("SERVICE_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			c.Service.Port = p
		}
	}
	// 覆盖Redis主机配置
	if redisHost := os.Getenv("REDIS_HOST"); redisHost != "" {
		c.Redis.Host = redisHost
	}
	// 覆盖Redis端口配置
	if redisPort := os.Getenv("REDIS_PORT"); redisPort != "" {
		if p, err := strconv.Atoi(redisPort); err == nil {
			c.Redis.Port = p
		}
	}
	// 覆盖NATS连接URL配置
	if natsURL := os.Getenv("NATS_URL"); natsURL != "" {
		c.NATS.URL = natsURL
	}
	// 覆盖InfluxDB连接URL配置
	if influxURL := os.Getenv("INFLUXDB_URL"); influxURL != "" {
		c.InfluxDB.URL = influxURL
	}
	// 覆盖MongoDB连接URI配置
	if mongoURI := os.Getenv("MONGODB_URI"); mongoURI != "" {
		c.MongoDB.URI = mongoURI
	}
}

// GetRedisAddr 获取Redis完整连接地址
// 将Redis主机和端口组合成完整的连接地址字符串
// 返回:
//   - string: 格式为"host:port"的Redis连接地址
//
// 示例:
//
//	addr := config.GetRedisAddr() // 返回 "localhost:6379"
//	client := redis.NewClient(&redis.Options{Addr: addr})
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Redis.Host, c.Redis.Port)
}

// GetServiceAddr 获取服务监听地址
// 将服务主机和端口组合成完整的监听地址字符串
// 用于HTTP服务器启动时指定监听地址
// 返回:
//   - string: 格式为"host:port"的服务监听地址
//
// 示例:
//
//	addr := config.GetServiceAddr() // 返回 "0.0.0.0:9005"
//	server.ListenAndServe(addr, handler)
func (c *Config) GetServiceAddr() string {
	return fmt.Sprintf("%s:%d", c.Service.Host, c.Service.Port)
}

// GetSQLiteFile 根据日期获取SQLite数据库文件路径
// 支持按日期分片存储，每天创建一个独立的数据库文件
// 便于数据管理和清理过期数据
// 参数:
//   - date: 目标日期，用于生成对应的数据库文件名
//
// 返回:
//   - string: 完整的SQLite数据库文件路径
//
// 示例:
//
//	today := time.Now()
//	dbPath := config.GetSQLiteFile(today) // 返回 "./data/sqlite/data_2025-05-26.db"
//	db, err := sql.Open("sqlite", dbPath)
func (c *Config) GetSQLiteFile(date time.Time) string {
	return fmt.Sprintf("%s/data_%s.db", c.SQLite.DataDir, date.Format("2006-01-02"))
}
