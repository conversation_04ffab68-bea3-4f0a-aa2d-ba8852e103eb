// 数据类型定义

export interface SensorData {
  _time: string;
  _value: number;
  _field: string;
  _measurement: string;
  device_id: string;
  data_type: string;
  location?: string;
  sequence?: string;
  cleaning_version_str?: string;
  processing_time_ms?: number;
}

export interface DeviceInfo {
  device_id: string;
  data_type: string;
  location?: string;
  last_seen: string;
  total_records: number;
  latest_value?: number;
  status: 'online' | 'offline' | 'warning';
  device_status?: string; // 设备状态，来自raw_data.status
}

export interface TemperatureData {
  timestamp: string;
  temperature: number;
  temperature_fahrenheit?: number;
  humidity?: number;
  device_id: string;
}

export interface PressureData {
  timestamp: string;
  pressure: number;
  altitude_meters?: number;
  altitude_feet?: number;
  device_id: string;
}

export interface MotionData {
  timestamp: string;
  acceleration_x: number;
  acceleration_y: number;
  acceleration_z: number;
  total_acceleration?: number;
  device_id: string;
}

export interface ChartDataPoint {
  timestamp: string;
  value: number;
  device_id: string;
  field: string;
}

export interface InfluxDBConfig {
  url: string;
  token: string;
  org: string;
  bucket: string;
}

export interface DashboardStats {
  total_devices: number;
  online_devices: number;
  total_records: number;
  records_per_second: number;
  last_update: string;
}

export interface AlertInfo {
  id: string;
  device_id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

export interface TimeRange {
  start: string;
  end: string;
  label: string;
}

export interface FilterOptions {
  devices: string[];
  dataTypes: string[];
  timeRange: TimeRange;
  fields: string[];
}

// API响应类型
export interface InfluxDBResponse {
  results: Array<{
    series?: Array<{
      name: string;
      columns: string[];
      values: any[][];
    }>;
  }>;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'data' | 'status' | 'error';
  payload: any;
  timestamp: string;
}

// 组件Props类型
export interface ChartProps {
  data: ChartDataPoint[];
  title: string;
  height?: number;
  showLegend?: boolean;
  timeRange?: TimeRange;
}

export interface DeviceCardProps {
  device: DeviceInfo;
  onClick?: (device: DeviceInfo) => void;
}

export interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'yellow' | 'red';
}
