/**
 * API配置文件
 *
 * 功能概述：
 * 本文件管理20_viewer与12_server_api的连接配置
 * 支持不同环境的配置管理和动态配置
 *
 * 主要功能：
 * - API基础URL配置：支持开发、测试、生产环境
 * - 超时和重试配置：网络请求的性能和可靠性设置
 * - 环境检测：自动检测当前运行环境
 * - 配置验证：确保配置的有效性
 *
 * 设计原则：
 * - 环境隔离：不同环境使用不同的配置
 * - 配置集中：所有API相关配置集中管理
 * - 易于维护：清晰的配置结构和注释
 * - 向后兼容：保持与原有配置的兼容性
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */

/**
 * 环境类型枚举
 */
export enum Environment {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  PRODUCTION = 'production'
}

/**
 * API配置接口
 */
export interface APIConfig {
  /** API服务器基础URL */
  baseURL: string;

  /** 请求超时时间（毫秒） */
  timeout: number;

  /** 重试次数 */
  retryCount: number;

  /** 重试延迟（毫秒） */
  retryDelay: number;

  /** 是否启用调试模式 */
  debug: boolean;

  /** 环境名称 */
  environment: Environment;
}

/**
 * 获取当前环境
 */
function getCurrentEnvironment(): Environment {
  // 从环境变量获取
  const env = process.env.NODE_ENV || process.env.REACT_APP_ENV;

  switch (env) {
    case 'production':
      return Environment.PRODUCTION;
    case 'test':
      return Environment.TESTING;
    case 'development':
    default:
      return Environment.DEVELOPMENT;
  }
}

/**
 * 获取API基础URL
 */
function getAPIBaseURL(): string {
  // 优先使用环境变量配置
  const envURL = process.env.REACT_APP_API_URL;
  if (envURL) {
    return envURL;
  }

  // 根据环境自动配置
  const environment = getCurrentEnvironment();

  switch (environment) {
    case Environment.PRODUCTION:
      // 生产环境：使用相对路径或配置的域名
      return window.location.origin.replace(':3000', ':9005');

    case Environment.TESTING:
      // 测试环境
      return 'http://localhost:9005';

    case Environment.DEVELOPMENT:
    default:
      // 开发环境：默认本地12_server_api端口
      return 'http://localhost:9005';
  }
}

/**
 * 默认API配置
 */
const defaultConfig: APIConfig = {
  baseURL: getAPIBaseURL(),
  timeout: 10000, // 10秒
  retryCount: 3,
  retryDelay: 1000, // 1秒
  debug: getCurrentEnvironment() === Environment.DEVELOPMENT,
  environment: getCurrentEnvironment(),
};

/**
 * 验证API配置
 */
function validateConfig(config: APIConfig): boolean {
  try {
    // 验证baseURL格式
    new URL(config.baseURL);

    // 验证数值配置
    if (config.timeout <= 0 || config.retryCount < 0 || config.retryDelay < 0) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * 获取API配置
 */
export function getAPIConfig(): APIConfig {
  const config = { ...defaultConfig };

  // 验证配置
  if (!validateConfig(config)) {
    console.warn('API配置验证失败，使用默认配置');
    return defaultConfig;
  }

  // 调试信息
  if (config.debug) {
    console.log('🔧 API配置:', {
      baseURL: config.baseURL,
      environment: config.environment,
      timeout: config.timeout,
      retryCount: config.retryCount,
    });
  }

  return config;
}

/**
 * 测试API连接
 */
export async function testAPIConnection(config?: APIConfig): Promise<boolean> {
  const apiConfig = config || getAPIConfig();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    const response = await fetch(`${apiConfig.baseURL}/api/viewer/connection/test`, {
      method: 'GET',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      return data.connected === true;
    }

    return false;
  } catch (error) {
    console.error('API连接测试失败:', error);
    return false;
  }
}

/**
 * 获取API健康状态
 */
export async function getAPIHealth(config?: APIConfig): Promise<{
  connected: boolean;
  latency?: number;
  error?: string;
}> {
  const apiConfig = config || getAPIConfig();
  const startTime = Date.now();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    const response = await fetch(`${apiConfig.baseURL}/api/viewer/connection/test`, {
      method: 'GET',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    const latency = Date.now() - startTime;

    if (response.ok) {
      const data = await response.json();
      return {
        connected: data.connected === true,
        latency,
      };
    }

    return {
      connected: false,
      latency,
      error: `HTTP ${response.status}: ${response.statusText}`,
    };
  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      connected: false,
      latency,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 导出默认配置
 */
export default getAPIConfig();

/**
 * 配置常量
 */
export const API_ENDPOINTS = {
  // 设备相关
  DEVICES: '/api/viewer/devices',
  DEVICE_DATA: '/api/viewer/devices/:id/data',
  DEVICE_RECORDS: '/api/viewer/devices/:id/records',

  // 字段和数据类型
  FIELD_DATA: '/api/viewer/fields/data',
  AVAILABLE_FIELDS: '/api/viewer/fields/available',
  DATA_TYPES: '/api/viewer/data-types',

  // 仪表板和连接
  DASHBOARD_STATS: '/api/viewer/dashboard/stats',
  CONNECTION_TEST: '/api/viewer/connection/test',
} as const;

/**
 * 构建API URL
 */
export function buildAPIURL(endpoint: string, params?: Record<string, string>): string {
  const config = getAPIConfig();
  let url = config.baseURL + endpoint;

  // 替换路径参数
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`:${key}`, encodeURIComponent(value));
    });
  }

  return url;
}
