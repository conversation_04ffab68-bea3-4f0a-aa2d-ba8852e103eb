import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useNavigate, useSearchParams } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import TestConnection from './components/TestConnection';
import DeviceDetails from './components/DeviceDetails';
import { Database, TestTube, Home } from 'lucide-react';
import './App.css';

// 设备详情页面包装组件
const DeviceDetailsPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const deviceId = searchParams.get('id');

  const handleBack = () => {
    navigate('/');
  };

  if (!deviceId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            设备ID缺失
          </h3>
          <p className="text-gray-500 mb-4">
            请提供有效的设备ID参数
          </p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回主页
          </button>
        </div>
      </div>
    );
  }

  return <DeviceDetails deviceId={deviceId} onBack={handleBack} />;
};

// 仪表板页面包装组件
const DashboardPage: React.FC = () => {
  const navigate = useNavigate();

  const handleViewDeviceDetails = (deviceId: string) => {
    navigate(`/detail?id=${encodeURIComponent(deviceId)}`);
  };

  return <Dashboard onViewDeviceDetails={handleViewDeviceDetails} />;
};

function App() {
  return (
    <Router>
      <div className="App min-h-screen bg-gray-50">
        {/* 导航栏 */}
        <nav className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-3">
                <Database className="w-8 h-8 text-blue-600" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">MDC Data Viewer</h1>
                  <p className="text-sm text-gray-500">InfluxDB 数据查看器</p>
                </div>
              </div>

              <div className="flex items-center space-x-1">
                <Link
                  to="/"
                  className="px-4 py-2 rounded-lg flex items-center transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                >
                  <Home className="w-4 h-4 mr-2" />
                  仪表板
                </Link>
                <Link
                  to="/test"
                  className="px-4 py-2 rounded-lg flex items-center transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  连接测试
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* 主内容区域 */}
        <main>
          <Routes>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/test" element={<TestConnection />} />
            <Route path="/detail" element={<DeviceDetailsPage />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
