// 设备卡片组件
import React from 'react';
import { DeviceCardProps } from '../types';

const DeviceCard: React.FC<DeviceCardProps> = ({ device, onClick }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'offline':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return '🟢';
      case 'offline':
        return '🔴';
      case 'warning':
        return '🟡';
      default:
        return '⚪';
    }
  };

  const getDeviceTypeIcon = (dataType: string) => {
    switch (dataType) {
      case 'temperature':
        return '🌡️';
      case 'pressure':
        return '📊';
      case 'motion':
        return '📱';
      default:
        return '📟';
    }
  };

  const formatLastSeen = (lastSeen: string) => {
    const date = new Date(lastSeen);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div
      className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:border-blue-300"
      onClick={() => onClick?.(device)}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{getDeviceTypeIcon(device.data_type)}</span>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {device.device_id}
            </h3>
            <p className="text-sm text-gray-500 capitalize">
              {device.data_type} Sensor
            </p>
          </div>
        </div>
        <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(device.status)}`}>
          <span className="mr-1">{getStatusIcon(device.status)}</span>
          {device.status}
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Location:</span>
          <span className="text-sm font-medium text-gray-900">
            {device.location || 'Unknown'}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Records:</span>
          <span className="text-sm font-medium text-gray-900">
            {device.total_records.toLocaleString()}
          </span>
        </div>

        {device.latest_value !== undefined && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Latest Value:</span>
            <span className="text-sm font-medium text-gray-900">
              {typeof device.latest_value === 'number'
                ? device.latest_value.toFixed(2)
                : device.latest_value}
            </span>
          </div>
        )}

        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <span className="text-sm text-gray-500">Last Seen:</span>
          <span className="text-sm font-medium text-gray-900">
            {formatLastSeen(device.last_seen)}
          </span>
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            device.status === 'online' ? 'bg-green-400 animate-pulse' :
            device.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'
          }`}></div>
          <span className="text-xs text-gray-500">
            {device.status === 'online' ? 'Receiving data' :
             device.status === 'warning' ? 'Intermittent' : 'No data'}
          </span>
        </div>

        <button
          className="text-xs text-blue-600 hover:text-blue-800 font-medium"
          onClick={(e) => {
            e.stopPropagation();
            alert(`Device Details:\n\nDevice ID: ${device.device_id}\nType: ${device.data_type}\nLocation: ${device.location}\nStatus: ${device.status}\nRecords: ${device.total_records}\nLast Value: ${device.latest_value}\nLast Seen: ${device.last_seen}`);
          }}
        >
          View Details →
        </button>
      </div>
    </div>
  );
};

export default DeviceCard;
