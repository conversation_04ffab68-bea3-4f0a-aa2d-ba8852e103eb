// 实时图表组件
import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { ChartProps } from '../types';

const RealtimeChart: React.FC<ChartProps & {
  type?: 'line' | 'area';
  showGrid?: boolean;
  colors?: string[];
}> = ({
  data,
  title,
  height = 300,
  showLegend = true,
  type = 'line',
  showGrid = true,
  colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6']
}) => {
  // 处理数据，按设备分组
  const processedData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // 按时间戳分组
    const timeGroups = data.reduce((acc, point) => {
      const time = new Date(point.timestamp).toLocaleTimeString();
      if (!acc[time]) {
        acc[time] = { timestamp: time, time: point.timestamp };
      }
      acc[time][point.device_id] = point.value;
      return acc;
    }, {} as any);

    return Object.values(timeGroups).sort((a: any, b: any) =>
      new Date(a.time).getTime() - new Date(b.time).getTime()
    );
  }, [data]);

  // 获取唯一的设备ID列表
  const deviceIds = useMemo(() => {
    const ids = new Set(data.map(point => point.device_id));
    return Array.from(ids);
  }, [data]);

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">{`Time: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${entry.value?.toFixed(2) || 'N/A'}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // 格式化X轴标签
  const formatXAxisLabel = (tickItem: string) => {
    return tickItem.split(':').slice(0, 2).join(':'); // 只显示小时:分钟
  };

  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <div className="text-4xl mb-2">📊</div>
            <p>No data available</p>
            <p className="text-sm">Waiting for sensor data...</p>
          </div>
        </div>
      </div>
    );
  }

  const ChartComponent = type === 'area' ? AreaChart : LineChart;

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-sm text-gray-500">Live</span>
        </div>
      </div>

      <ResponsiveContainer width="100%" height={height}>
        <ChartComponent data={processedData}>
          {showGrid && (
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          )}
          <XAxis
            dataKey="timestamp"
            tickFormatter={formatXAxisLabel}
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis
            stroke="#6b7280"
            fontSize={12}
          />
          <Tooltip content={<CustomTooltip />} />
          {showLegend && <Legend />}

          {deviceIds.map((deviceId, index) => {
            const color = colors[index % colors.length];

            if (type === 'area') {
              return (
                <Area
                  key={deviceId}
                  type="monotone"
                  dataKey={deviceId}
                  stroke={color}
                  fill={color}
                  fillOpacity={0.1}
                  strokeWidth={2}
                  dot={{ fill: color, strokeWidth: 2, r: 3 }}
                  activeDot={{ r: 5, stroke: color, strokeWidth: 2 }}
                />
              );
            } else {
              return (
                <Line
                  key={deviceId}
                  type="monotone"
                  dataKey={deviceId}
                  stroke={color}
                  strokeWidth={2}
                  dot={{ fill: color, strokeWidth: 2, r: 3 }}
                  activeDot={{ r: 5, stroke: color, strokeWidth: 2 }}
                />
              );
            }
          })}
        </ChartComponent>
      </ResponsiveContainer>

      {/* 图表底部信息 */}
      <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
        <span>{data.length} data points</span>
        <span>{deviceIds.length} devices</span>
        <span>Updated: {new Date().toLocaleTimeString()}</span>
      </div>
    </div>
  );
};

export default RealtimeChart;
