// 增强的InfluxDB连接测试组件 - 提供详细的测试过程和故障排除信息
import React, { useState } from 'react';
import { influxDBService } from '../services/influxdb';
import { CheckCircle, XCircle, AlertCircle, Loader2, Server, Database, Wifi } from 'lucide-react';

interface TestStep {
  name: string;
  description: string;
  status: 'pending' | 'running' | 'success' | 'error';
  error?: string;
  icon: React.ReactNode;
}

const TestConnection: React.FC = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testSteps, setTestSteps] = useState<TestStep[]>([
    {
      name: 'Docker检查',
      description: '检查Docker是否运行',
      status: 'pending',
      icon: <Server className="w-5 h-5" />
    },
    {
      name: 'InfluxDB服务',
      description: '检查InfluxDB容器是否运行',
      status: 'pending',
      icon: <Database className="w-5 h-5" />
    },
    {
      name: '网络连接',
      description: '测试到InfluxDB的网络连接',
      status: 'pending',
      icon: <Wifi className="w-5 h-5" />
    },
    {
      name: 'API认证',
      description: '验证API Token和权限',
      status: 'pending',
      icon: <CheckCircle className="w-5 h-5" />
    }
  ]);

  const updateStepStatus = (index: number, status: TestStep['status'], error?: string) => {
    setTestSteps(prev => prev.map((step, i) =>
      i === index ? { ...step, status, error } : step
    ));
  };

  const resetSteps = () => {
    setTestSteps(prev => prev.map(step => ({ ...step, status: 'pending', error: undefined })));
  };

  const checkDockerStatus = async (): Promise<boolean> => {
    try {
      // 通过检查Docker API或者简单的健康检查来验证Docker状态
      // 这里我们通过尝试连接InfluxDB来间接检查Docker
      const response = await fetch('http://localhost:8086/ping', {
        method: 'GET',
        signal: AbortSignal.timeout(3000)
      });
      return response.ok;
    } catch (error) {
      throw new Error('Docker可能未运行或InfluxDB容器未启动');
    }
  };

  const checkInfluxDBService = async (): Promise<boolean> => {
    try {
      const response = await fetch('http://localhost:8086/health', {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });

      if (!response.ok) {
        throw new Error(`InfluxDB健康检查失败: ${response.status} ${response.statusText}`);
      }

      const healthData = await response.json();
      if (healthData.status !== 'pass') {
        throw new Error(`InfluxDB状态异常: ${healthData.status}`);
      }

      return true;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('连接超时，请检查InfluxDB是否正在运行');
        }
        throw error;
      }
      throw new Error('InfluxDB服务检查失败');
    }
  };

  const checkNetworkConnection = async (): Promise<boolean> => {
    try {
      // 测试基本的网络连接
      const response = await fetch('http://localhost:8086/ping', {
        method: 'GET',
        signal: AbortSignal.timeout(3000)
      });

      if (!response.ok) {
        throw new Error(`网络连接失败: ${response.status}`);
      }

      return true;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('网络连接超时');
        }
        throw error;
      }
      throw new Error('网络连接测试失败');
    }
  };

  const checkAPIAuthentication = async (): Promise<boolean> => {
    try {
      const result = await influxDBService.testConnection();
      if (!result) {
        throw new Error('API认证失败，请检查Token和权限配置');
      }
      return true;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('API认证测试失败');
    }
  };

  const testConnection = async () => {
    setIsLoading(true);
    setIsConnected(null);
    resetSteps();

    const testFunctions = [
      checkDockerStatus,
      checkInfluxDBService,
      checkNetworkConnection,
      checkAPIAuthentication
    ];

    try {
      for (let i = 0; i < testFunctions.length; i++) {
        updateStepStatus(i, 'running');

        try {
          await testFunctions[i]();
          updateStepStatus(i, 'success');
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          updateStepStatus(i, 'error', errorMessage);
          throw error;
        }

        // 添加小延迟以便用户看到进度
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      setIsConnected(true);
    } catch (error) {
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  const getStepIcon = (step: TestStep) => {
    switch (step.status) {
      case 'running':
        return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 text-gray-400">{step.icon}</div>;
    }
  };

  const getStepBgColor = (step: TestStep) => {
    switch (step.status) {
      case 'running':
        return 'bg-blue-50 border-blue-200';
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <Database className="w-6 h-6 mr-2" />
          InfluxDB 连接测试
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 配置信息 */}
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-700 mb-3 flex items-center">
                <Server className="w-4 h-4 mr-2" />
                连接配置
              </h3>
              <div className="text-sm text-gray-600 space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">URL:</span>
                  <span className="text-blue-600">http://localhost:8086</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">组织:</span>
                  <span>mdc-org</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">存储桶:</span>
                  <span>device-data</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Token:</span>
                  <span className="font-mono text-xs">mdc-token-***</span>
                </div>
              </div>
            </div>

            <button
              onClick={testConnection}
              disabled={isLoading}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center ${isLoading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  测试中...
                </>
              ) : (
                '开始测试连接'
              )}
            </button>

            {/* 总体结果 */}
            {isConnected !== null && (
              <div className={`p-4 rounded-lg border ${isConnected
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
                }`}>
                <div className="flex items-center">
                  {isConnected ? (
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500 mr-3" />
                  )}
                  <span className={`font-medium ${isConnected ? 'text-green-800' : 'text-red-800'
                    }`}>
                    {isConnected ? '✅ 所有测试通过，连接正常' : '❌ 连接测试失败'}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* 测试步骤 */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-700 mb-3 flex items-center">
              <AlertCircle className="w-4 h-4 mr-2" />
              测试步骤
            </h3>

            {testSteps.map((step, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border transition-all duration-300 ${getStepBgColor(step)}`}
              >
                <div className="flex items-start">
                  <div className="mr-3 mt-0.5">
                    {getStepIcon(step)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-800">{step.name}</div>
                    <div className="text-sm text-gray-600 mt-1">{step.description}</div>
                    {step.error && (
                      <div className="text-sm text-red-600 mt-2 bg-red-100 p-2 rounded">
                        <strong>错误:</strong> {step.error}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 故障排除提示 */}
        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-semibold text-yellow-800 mb-2 flex items-center">
            <AlertCircle className="w-4 h-4 mr-2" />
            故障排除提示
          </h4>
          <div className="text-sm text-yellow-700 space-y-1">
            <p>• <strong>Docker检查失败:</strong> 确保Docker Desktop正在运行</p>
            <p>• <strong>InfluxDB服务失败:</strong> 运行 <code className="bg-yellow-100 px-1 rounded">docker-compose up -d</code> 启动服务</p>
            <p>• <strong>网络连接失败:</strong> 检查端口8086是否被占用或防火墙设置</p>
            <p>• <strong>API认证失败:</strong> 验证Token配置是否正确</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestConnection;
