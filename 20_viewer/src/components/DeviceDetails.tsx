// 设备详情页面组件 - 显示设备的完整记录表格
import React, { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';
import { ArrowLeft, RefreshCw, Calendar, Database, Trash2, AlertTriangle, ChevronLeft, ChevronRight } from 'lucide-react';

interface DeviceRecord {
  timestamp: string;
  device_id: string;
  data_type: string;
  location: string;
  sequence: number;
  fields: Record<string, any>;
}

interface DeviceDetailsProps {
  deviceId: string;
  onBack: () => void;
}

const DeviceDetails: React.FC<DeviceDetailsProps> = ({ deviceId, onBack }) => {
  const [records, setRecords] = useState<DeviceRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('-1h');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [pagination, setPagination] = useState<any>(null);
  const [dialogContent, setDialogContent] = useState<{ field: string, value: any } | null>(null);
  const [customTimeRange, setCustomTimeRange] = useState(false);
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');

  // 时间范围选项
  const timeRangeOptions = [
    { value: '-1h', label: '最近1小时' },
    { value: '-6h', label: '最近6小时' },
    { value: '-24h', label: '最近24小时' },
    { value: '-7d', label: '最近7天' },
    { value: '-30d', label: '最近30天' },
    { value: 'custom', label: '自定义时间范围' }
  ];

  // 加载设备记录（支持分页）
  const loadRecords = useCallback(async (page: number = 1) => {
    setLoading(true);
    try {
      let actualTimeRange = timeRange;
      let actualEndTime: string | undefined = undefined;

      // 如果是自定义时间范围，使用开始和结束时间
      if (timeRange === 'custom' && startTime && endTime) {
        actualTimeRange = startTime;
        actualEndTime = endTime;
      } else if (timeRange === 'custom') {
        // 如果选择了自定义但没有设置时间，使用默认的最近1小时
        actualTimeRange = '-1h';
      }

      const response = await apiService.getDeviceRecords(deviceId, actualTimeRange, actualEndTime, page, pageSize);
      // API已经按时间戳倒序排序，直接使用
      setRecords(response.records);
      setPagination(response.pagination);
      setCurrentPage(page);
    } catch (error) {
      console.error('Failed to load device records:', error);
    } finally {
      setLoading(false);
    }
  }, [deviceId, timeRange, startTime, endTime, pageSize]);

  // 删除设备（暂时禁用，需要通过12_server_api实现）
  const handleDeleteDevice = async () => {
    alert('删除设备功能暂时不可用，请通过管理界面操作。');
    setShowDeleteConfirm(false);
    // TODO: 实现通过12_server_api删除设备的功能
    /*
    setDeleting(true);
    try {
      // await apiService.deleteDevice(deviceId);
      alert('设备删除成功！');
      onBack(); // 返回主页面
    } catch (error) {
      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
    */
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 格式化字段值
  const formatFieldValue = (value: any) => {
    if (typeof value === 'number') {
      return value.toFixed(2);
    }
    if (typeof value === 'boolean') {
      return value ? '是' : '否';
    }
    return String(value);
  };

  // 获取所有字段名
  const getAllFields = () => {
    const fieldSet = new Set<string>();
    records.forEach(record => {
      Object.keys(record.fields).forEach(field => fieldSet.add(field));
    });
    return Array.from(fieldSet).sort();
  };

  // 分页逻辑（使用API返回的分页信息）
  const totalPages = pagination?.totalPages || 1;
  const totalCount = pagination?.totalCount || 0;
  const startIndex = pagination ? (pagination.page - 1) * pagination.pageSize + 1 : 1;
  const endIndex = pagination ? Math.min(pagination.page * pagination.pageSize, totalCount) : records.length;

  // 分页导航（调用API获取新页面数据）
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      loadRecords(page);
    }
  };

  // 显示详细内容对话框
  const handleShowDialog = (field: string, value: any) => {
    setDialogContent({ field, value });
  };

  // 关闭对话框
  const handleCloseDialog = () => {
    setDialogContent(null);
  };

  // 处理时间范围变更
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    setCurrentPage(1);

    if (value === 'custom') {
      setCustomTimeRange(true);
      // 设置默认的自定义时间范围（最近24小时）
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      setStartTime(yesterday.toISOString().slice(0, 16)); // YYYY-MM-DDTHH:mm格式
      setEndTime(now.toISOString().slice(0, 16));
    } else {
      setCustomTimeRange(false);
      setStartTime('');
      setEndTime('');
      // 立即重新加载数据
      loadRecords(1);
    }
  };

  // 应用自定义时间范围
  const applyCustomTimeRange = () => {
    if (startTime && endTime) {
      // 转换为ISO格式的时间戳
      const startISO = new Date(startTime).toISOString();
      const endISO = new Date(endTime).toISOString();
      setStartTime(startISO);
      setEndTime(endISO);
      setCurrentPage(1);
      loadRecords(1);
    }
  };

  // 单元格内容组件
  const CellContent: React.FC<{ value: any, field: string, onShowDialog: (field: string, value: any) => void }> = ({ value, field, onShowDialog }) => {
    const formattedValue = formatFieldValue(value);
    const isLongContent = typeof formattedValue === 'string' && formattedValue.length > 30;

    if (isLongContent) {
      const truncatedValue = formattedValue.substring(0, 30) + '...';
      return (
        <div
          className="cursor-pointer hover:bg-blue-50 hover:text-blue-700 transition-colors rounded px-2 py-1"
          onDoubleClick={() => onShowDialog(field, value)}
          title="双击查看完整内容"
        >
          <span className="font-mono text-xs">{truncatedValue}</span>
        </div>
      );
    }

    return (
      <span className={typeof value === 'number' ? 'font-mono' : ''}>
        {formattedValue}
      </span>
    );
  };

  useEffect(() => {
    loadRecords(1);
  }, [deviceId]);

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg">
        {/* 头部 */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-4 p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800 flex items-center">
                  <Database className="w-6 h-6 mr-2" />
                  设备详情
                </h1>
                <p className="text-gray-600 mt-1">设备ID: {deviceId}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* 时间范围选择 */}
              <select
                value={timeRange}
                onChange={(e) => handleTimeRangeChange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {timeRangeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>

              {/* 自定义时间范围选择器 */}
              {customTimeRange && (
                <div className="flex items-center space-x-2 bg-gray-50 p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-600 whitespace-nowrap">开始时间:</label>
                    <input
                      type="datetime-local"
                      value={startTime.slice(0, 16)} // 转换为datetime-local格式
                      onChange={(e) => setStartTime(e.target.value)}
                      className="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-600 whitespace-nowrap">结束时间:</label>
                    <input
                      type="datetime-local"
                      value={endTime.slice(0, 16)} // 转换为datetime-local格式
                      onChange={(e) => setEndTime(e.target.value)}
                      className="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <button
                    onClick={applyCustomTimeRange}
                    disabled={!startTime || !endTime}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 whitespace-nowrap"
                  >
                    应用
                  </button>
                </div>
              )}

              {/* 每页显示数量选择 */}
              <select
                value={pageSize}
                onChange={(e) => {
                  const newPageSize = Number(e.target.value);
                  setPageSize(newPageSize);
                  setCurrentPage(1);
                  // 重新加载第一页数据
                  loadRecords(1);
                }}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={25}>25条/页</option>
                <option value={50}>50条/页</option>
                <option value={100}>100条/页</option>
                <option value={200}>200条/页</option>
              </select>

              {/* 刷新按钮 */}
              <button
                onClick={() => loadRecords(currentPage)}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </button>

              {/* 删除设备按钮 */}
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                删除设备
              </button>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="p-6 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg">
              <div className="text-sm text-gray-600">总记录数</div>
              <div className="text-2xl font-bold text-blue-600">{totalCount}</div>
            </div>
            <div className="bg-white p-4 rounded-lg">
              <div className="text-sm text-gray-600">字段数量</div>
              <div className="text-2xl font-bold text-green-600">{getAllFields().length}</div>
            </div>
            <div className="bg-white p-4 rounded-lg">
              <div className="text-sm text-gray-600">最新记录</div>
              <div className="text-sm font-medium text-gray-800">
                {records.length > 0 ? formatTimestamp(records[0].timestamp) : '无数据'}
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg">
              <div className="text-sm text-gray-600">时间范围</div>
              <div className="text-sm font-medium text-gray-800">
                {timeRange === 'custom' && startTime && endTime ? (
                  <div>
                    <div>{new Date(startTime).toLocaleString('zh-CN')}</div>
                    <div className="text-xs text-gray-500">至</div>
                    <div>{new Date(endTime).toLocaleString('zh-CN')}</div>
                  </div>
                ) : (
                  timeRangeOptions.find(opt => opt.value === timeRange)?.label
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 记录表格 */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : records.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">该时间范围内没有找到记录</p>
            </div>
          ) : (
            <div className="overflow-x-auto border border-gray-200 rounded-lg">
              <table className="min-w-full divide-y divide-gray-200 border-collapse" style={{ minWidth: '1800px' }}>
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-16 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap border-r-2 border-gray-500">
                      时间戳
                    </th>
                    <th className="px-16 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap border-r-2 border-gray-500">
                      数据类型
                    </th>
                    <th className="px-16 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap border-r-2 border-gray-500">
                      connected
                    </th>
                    <th className="px-16 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap border-r-2 border-gray-500">
                      位置
                    </th>
                    <th className="px-16 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap border-r-2 border-gray-500">
                      序列号
                    </th>
                    {getAllFields().filter(field => field !== 'connected').map((field, index) => (
                      <th key={field} className={`px-16 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap ${index < getAllFields().filter(field => field !== 'connected').length - 1 ? 'border-r-2 border-gray-500' : ''
                        }`}>
                        {field}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {records.map((record, index) => (
                    <tr key={index} className="hover:bg-gray-50 transition-colors">
                      <td className="px-16 py-4 whitespace-nowrap text-sm text-gray-900 border-r-2 border-gray-500">
                        {formatTimestamp(record.timestamp)}
                      </td>
                      <td className="px-16 py-4 whitespace-nowrap text-sm text-gray-900 border-r-2 border-gray-500">
                        {record.data_type}
                      </td>
                      <td className="px-16 py-4 whitespace-nowrap text-sm text-gray-900 border-r-2 border-gray-500">
                        <CellContent
                          value={record.fields.connected}
                          field="connected"
                          onShowDialog={handleShowDialog}
                        />
                      </td>
                      <td className="px-16 py-4 whitespace-nowrap text-sm text-gray-900 border-r-2 border-gray-500">
                        {record.location}
                      </td>
                      <td className="px-16 py-4 whitespace-nowrap text-sm text-gray-900 border-r-2 border-gray-500">
                        {record.sequence}
                      </td>
                      {getAllFields().filter(field => field !== 'connected').map((field, fieldIndex) => (
                        <td key={field} className={`px-16 py-4 text-sm text-gray-900 ${fieldIndex < getAllFields().filter(field => field !== 'connected').length - 1 ? 'border-r-2 border-gray-500' : ''
                          }`}>
                          {record.fields[field] !== undefined ? (
                            <CellContent
                              value={record.fields[field]}
                              field={field}
                              onShowDialog={handleShowDialog}
                            />
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* 分页控件 */}
              {totalCount > 0 && (
                <div className="mt-6 flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    显示第 {startIndex} - {endIndex} 条，共 {totalCount} 条记录
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => goToPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400 flex items-center"
                    >
                      <ChevronLeft className="w-4 h-4 mr-1" />
                      上一页
                    </button>

                    <div className="flex items-center space-x-1">
                      {/* 页码显示 */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum: number;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => goToPage(pageNum)}
                            className={`px-3 py-2 text-sm rounded-lg ${currentPage === pageNum
                              ? 'bg-blue-600 text-white'
                              : 'border border-gray-300 hover:bg-gray-50'
                              }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => goToPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400 flex items-center"
                    >
                      下一页
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-red-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-800">确认删除设备</h3>
            </div>
            <p className="text-gray-600 mb-6">
              您确定要删除设备 <strong>{deviceId}</strong> 及其所有记录吗？此操作不可撤销。
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleting}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleDeleteDevice}
                disabled={deleting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 flex items-center"
              >
                {deleting ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    删除中...
                  </>
                ) : (
                  '确认删除'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 详细内容对话框 */}
      {dialogContent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-hidden">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                字段详细内容: {dialogContent.field}
              </h3>
              <button
                onClick={handleCloseDialog}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 max-h-64 overflow-auto">
              <div className="text-sm text-gray-600 mb-2">
                数据类型: {typeof dialogContent.value}
              </div>
              <div className="font-mono text-sm text-gray-900 whitespace-pre-wrap break-all">
                {typeof dialogContent.value === 'object'
                  ? JSON.stringify(dialogContent.value, null, 2)
                  : String(dialogContent.value)
                }
              </div>
            </div>

            <div className="mt-4 flex justify-end space-x-3">
              <button
                onClick={() => {
                  navigator.clipboard.writeText(
                    typeof dialogContent.value === 'object'
                      ? JSON.stringify(dialogContent.value, null, 2)
                      : String(dialogContent.value)
                  );
                  alert('内容已复制到剪贴板');
                }}
                className="px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
              >
                复制内容
              </button>
              <button
                onClick={handleCloseDialog}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeviceDetails;
