// 主仪表板组件 - 增强设备管理和详情跳转功能
import React, { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';
import { DeviceInfo, DashboardStats, ChartDataPoint } from '../types';
import StatsCard from './StatsCard';
import RealtimeChart from './RealtimeChart';
import { RefreshCw, Eye, Trash2, AlertTriangle, Database, Wifi, WifiOff } from 'lucide-react';

interface DashboardProps {
  onViewDeviceDetails: (deviceId: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onViewDeviceDetails }) => {
  const [devices, setDevices] = useState<DeviceInfo[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    total_devices: 0,
    online_devices: 0,
    total_records: 0,
    records_per_second: 0,
    last_update: new Date().toISOString()
  });
  const [temperatureData, setTemperatureData] = useState<ChartDataPoint[]>([]);
  const [pressureData, setPressureData] = useState<ChartDataPoint[]>([]);
  const [motionData, setMotionData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [deletingDevice, setDeletingDevice] = useState<string | null>(null);

  // 获取数据的函数
  const fetchData = useCallback(async () => {
    try {
      setError(null);
      console.log('开始获取数据...');

      // 并行获取所有数据
      const [
        devicesData,
        statsData,
        tempData,
        pressData,
        motionDataResult
      ] = await Promise.all([
        apiService.getDevices(),
        apiService.getDashboardStats(),
        apiService.getFieldData('temperature', '-1h'),
        apiService.getFieldData('pressure', '-1h'),
        apiService.getFieldData('acceleration_x', '-1h')
      ]);

      console.log('获取到的数据:', {
        devices: devicesData.length,
        stats: statsData,
        tempData: tempData.length,
        pressData: pressData.length,
        motionData: motionDataResult.length
      });

      setDevices(devicesData);
      setStats(statsData);
      setTemperatureData(tempData);
      setPressureData(pressData);
      setMotionData(motionDataResult);
      setLastUpdate(new Date());

    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to fetch data from API server');
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始加载
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 定时刷新数据
  useEffect(() => {
    const interval = setInterval(fetchData, 10000); // 每10秒刷新
    return () => clearInterval(interval);
  }, [fetchData]);

  // 测试API连接
  useEffect(() => {
    const testConnection = async () => {
      const isConnected = await apiService.testConnection();
      if (!isConnected) {
        setError('Cannot connect to API server. Please check the connection.');
      }
    };
    testConnection();
  }, []);

  // 删除单个设备（暂时禁用，需要通过12_server_api实现）
  const handleDeleteDevice = async (deviceId: string) => {
    alert('删除设备功能暂时不可用，请通过管理界面操作。');
    // TODO: 实现通过12_server_api删除设备的功能
    /*
    if (!window.confirm(`确定要删除设备 ${deviceId} 及其所有记录吗？此操作不可撤销。`)) {
      return;
    }

    setDeletingDevice(deviceId);
    try {
      // await apiService.deleteDevice(deviceId);
      alert('设备删除成功！');
      fetchData(); // 重新加载数据
    } catch (error) {
      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setDeletingDevice(null);
    }
    */
  };

  // 清空所有数据（暂时禁用，需要通过12_server_api实现）
  const handleClearAllData = async () => {
    alert('清空数据功能暂时不可用，请通过管理界面操作。');
    // TODO: 实现通过12_server_api清空数据的功能
    /*
    setClearing(true);
    try {
      // await apiService.clearAllData();
      alert('所有数据清空成功！');
      fetchData(); // 重新加载数据
    } catch (error) {
      alert(`清空失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setClearing(false);
      setShowClearConfirm(false);
    }
    */
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">


            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">
                  最后更新: {lastUpdate.toLocaleTimeString()}
                </span>
              </div>
              <button
                onClick={fetchData}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </button>

            </div>
          </div>
        </div>
      </header>

      <main className="w-full max-w-none px-4 sm:px-6 lg:px-8 py-8">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-red-400 mr-3">⚠️</div>
              <div>
                <h3 className="text-sm font-medium text-red-800">连接错误</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 设备列表 */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            {/* 设备列表头部 */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <Database className="w-5 h-5 mr-2" />
                  设备状态
                </h2>
                <span className="text-sm text-gray-500">
                  总计 {devices.length} 个设备
                </span>
              </div>
            </div>

            {/* 设备统计 */}
            <div className="p-6 bg-gray-50 border-b border-gray-200">
              <div className="grid grid-cols-3 gap-6 w-full">
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-center">
                    <div className="flex-shrink-0">
                      <Wifi className="w-10 h-10 text-green-500" />
                    </div>
                    <div className="ml-4 text-center">
                      <div className="text-sm font-medium text-gray-600">在线设备</div>
                      <div className="text-3xl font-bold text-green-600">
                        {devices.filter(d => d.status === 'online').length}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-center">
                    <div className="flex-shrink-0">
                      <WifiOff className="w-10 h-10 text-red-500" />
                    </div>
                    <div className="ml-4 text-center">
                      <div className="text-sm font-medium text-gray-600">离线设备</div>
                      <div className="text-3xl font-bold text-red-600">
                        {devices.filter(d => d.status === 'offline').length}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-center">
                    <div className="flex-shrink-0">
                      <Database className="w-10 h-10 text-blue-500" />
                    </div>
                    <div className="ml-4 text-center">
                      <div className="text-sm font-medium text-gray-600">总记录数</div>
                      <div className="text-3xl font-bold text-blue-600">
                        {devices.reduce((sum, d) => sum + d.total_records, 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 设备内容 */}
            <div className="p-6">
              {devices.length === 0 ? (
                <div className="text-center py-12">
                  <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    没有找到设备
                  </h3>
                  <p className="text-gray-500">
                    InfluxDB中没有设备数据。请确保数据推送服务正在运行。
                  </p>
                </div>
              ) : (
                <div className="w-full overflow-x-auto">
                  <table className="w-full min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                          设备ID
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                          状态
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                          数据类型
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                          位置
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">
                          时间戳
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                          活动
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[140px]">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {devices.sort((a, b) => a.device_id.localeCompare(b.device_id)).map((device) => (
                        <tr key={device.device_id} className="hover:bg-gray-50 transition-colors duration-150">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">
                            <div className="max-w-[200px] truncate" title={device.device_id}>
                              {device.device_id}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${device.status === 'online'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                              }`}>
                              {device.status === 'online' ? (
                                <>
                                  <Wifi className="w-3 h-3 mr-1" />
                                  在线
                                </>
                              ) : (
                                <>
                                  <WifiOff className="w-3 h-3 mr-1" />
                                  离线
                                </>
                              )}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="max-w-[120px] truncate" title={device.data_type}>
                              {device.data_type}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="max-w-[120px] truncate" title={device.location}>
                              {device.location}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="max-w-[150px] truncate" title={new Date(device.last_seen).toLocaleString()}>
                              {new Date(device.last_seen).toLocaleString()}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${device.device_status === 'production' ? 'bg-green-100 text-green-800' :
                              device.device_status === 'idle' ? 'bg-yellow-100 text-yellow-800' :
                                device.device_status === 'alarm' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                              }`}>
                              {device.device_status || 'unknown'}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => onViewDeviceDetails(device.device_id)}
                                className="inline-flex items-center px-2 py-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded transition-colors duration-150"
                                title="查看设备详情"
                              >
                                <Eye className="w-4 h-4 mr-1" />
                                详情
                              </button>
                              <button
                                onClick={() => handleDeleteDevice(device.device_id)}
                                disabled={deletingDevice === device.device_id}
                                className="inline-flex items-center px-2 py-1 text-red-600 hover:text-red-900 hover:bg-red-50 rounded transition-colors duration-150 disabled:text-gray-400 disabled:hover:bg-transparent"
                                title="删除设备"
                              >
                                {deletingDevice === device.device_id ? (
                                  <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                                ) : (
                                  <Trash2 className="w-4 h-4 mr-1" />
                                )}
                                删除
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 页脚信息 */}
        <footer className="text-center text-sm text-gray-500 py-4 border-t border-gray-200">
          <p>
            连接到 API 服务器: localhost:9005 |
            数据提供: 12_server_api |
            状态: 已迁移
          </p>
        </footer>
      </main>

      {/* 清空所有数据确认对话框 */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-red-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-800">确认清空所有数据</h3>
            </div>
            <p className="text-gray-600 mb-6">
              您确定要清空所有设备和记录吗？此操作将删除：
            </p>
            <ul className="text-sm text-gray-600 mb-6 list-disc list-inside">
              <li>所有 {devices.length} 个设备</li>
              <li>总计 {devices.reduce((sum, d) => sum + d.total_records, 0).toLocaleString()} 条记录</li>
              <li>所有历史数据</li>
            </ul>
            <p className="text-red-600 text-sm font-medium mb-6">
              ⚠️ 此操作不可撤销！
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowClearConfirm(false)}
                disabled={clearing}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleClearAllData}
                disabled={clearing}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 flex items-center"
              >
                {clearing ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    清空中...
                  </>
                ) : (
                  '确认清空'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
