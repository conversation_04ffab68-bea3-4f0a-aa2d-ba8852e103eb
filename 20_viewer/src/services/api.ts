/**
 * API服务 - 替换直接的InfluxDB连接
 *
 * 功能概述：
 * 本服务是20_viewer数据提供迁移的核心，将原来直接连接InfluxDB的方式
 * 改为通过12_server_api统一提供数据服务
 *
 * 主要功能：
 * - 设备数据查询：获取设备列表、状态、详情等信息
 * - 图表数据查询：获取用于图表展示的时序数据
 * - 仪表板统计：获取系统概览和统计信息
 * - 字段和类型查询：获取可用的数据字段和设备类型
 * - 连接测试：测试与后端API的连接状态
 *
 * 设计原则：
 * - 接口兼容性：保持与原有InfluxDBService完全兼容的接口
 * - 数据一致性：确保迁移前后数据格式和内容完全一致
 * - 错误处理：提供详细的错误信息和重试机制
 * - 性能优化：利用HTTP缓存和请求优化
 *
 * 技术特性：
 * - Axios HTTP客户端：标准的HTTP请求库
 * - 自动重试机制：网络失败时的自动重试
 * - 超时控制：防止请求长时间挂起
 * - 错误统一处理：标准化的错误响应格式
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { SensorData, DeviceInfo, ChartDataPoint, DashboardStats } from '../types';
import { getAPIConfig, buildAPIURL, API_ENDPOINTS } from '../config/api';

/**
 * API服务类
 *
 * 功能：提供与12_server_api的统一接口，替换原有的InfluxDBService
 *
 * 设计模式：
 * - 单例模式：全局唯一的API服务实例
 * - 适配器模式：适配原有的InfluxDBService接口
 * - 重试模式：网络失败时的自动重试机制
 *
 * 接口兼容性：
 * - 保持与原有InfluxDBService相同的方法签名
 * - 返回相同格式的数据结构
 * - 提供相同的错误处理机制
 */
class APIService {
  private client: AxiosInstance;
  private config = getAPIConfig();
  private retryCount: number;
  private retryDelay: number;

  constructor() {
    this.retryCount = this.config.retryCount;
    this.retryDelay = this.config.retryDelay;

    // 创建Axios实例
    this.client = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 添加响应拦截器用于错误处理
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API请求失败:', error);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  /**
   * 带重试机制的HTTP请求
   */
  private async makeRequest<T>(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    data?: any,
    retries: number = this.retryCount
  ): Promise<T> {
    for (let i = 0; i <= retries; i++) {
      try {
        let response: AxiosResponse<T>;

        switch (method) {
          case 'get':
            response = await this.client.get(url, data);
            break;
          case 'post':
            response = await this.client.post(url, data);
            break;
          case 'put':
            response = await this.client.put(url, data);
            break;
          case 'delete':
            response = await this.client.delete(url, data);
            break;
          default:
            throw new Error(`Unsupported HTTP method: ${method}`);
        }

        return response.data;
      } catch (error) {
        console.warn(`API请求失败 (尝试 ${i + 1}/${retries + 1}):`, error);

        if (i === retries) {
          throw error;
        }

        // 指数退避延迟
        const delay = this.retryDelay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new Error('Request failed after all retries');
  }

  /**
   * 错误处理
   */
  private handleError(error: any): Error {
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNREFUSED') {
        return new Error('无法连接到API服务器，请检查服务是否运行');
      } else if (error.response?.status === 401) {
        return new Error('API认证失败，请检查配置');
      } else if (error.response?.status === 404) {
        return new Error('API接口不存在');
      } else if (error.code === 'ENOTFOUND') {
        return new Error('API服务器地址无法解析');
      } else if (error.code === 'ETIMEDOUT') {
        return new Error('API请求超时');
      }
    }

    return error instanceof Error ? error : new Error('未知错误');
  }

  /**
   * 获取设备列表（兼容原有接口）
   *
   * 功能：获取所有设备的状态和基本信息
   *
   * @returns {Promise<DeviceInfo[]>} 设备信息列表
   */
  async getDevices(): Promise<DeviceInfo[]> {
    if (this.config.debug) {
      console.log('📊 Getting devices from API...');
    }

    try {
      const devices = await this.makeRequest<DeviceInfo[]>('get', API_ENDPOINTS.DEVICES);
      if (this.config.debug) {
        console.log(`✅ Retrieved ${devices.length} devices from API`);
      }
      return devices;
    } catch (error) {
      console.error('❌ Failed to get devices from API:', error);
      throw error;
    }
  }

  /**
   * 获取设备数据（兼容原有接口）
   *
   * 功能：获取特定设备的时序数据，用于图表展示
   *
   * @param {string} deviceId - 设备ID
   * @param {string} timeRange - 时间范围，如"-1h"、"-24h"等
   * @returns {Promise<ChartDataPoint[]>} 图表数据点列表
   */
  async getDeviceData(deviceId: string, timeRange: string = '-1h'): Promise<ChartDataPoint[]> {
    if (this.config.debug) {
      console.log(`📊 Getting device data for ${deviceId}, timeRange: ${timeRange}`);
    }

    try {
      const url = buildAPIURL(API_ENDPOINTS.DEVICE_DATA, { id: deviceId }) + `?timeRange=${timeRange}`;
      const data = await this.makeRequest<ChartDataPoint[]>('get', url);
      if (this.config.debug) {
        console.log(`✅ Retrieved ${data.length} data points for device ${deviceId}`);
      }
      return data;
    } catch (error) {
      console.error(`❌ Failed to get device data for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * 获取字段数据（兼容原有接口）
   *
   * 功能：获取特定字段的时序数据，支持多设备过滤
   *
   * @param {string} field - 字段名称
   * @param {string} timeRange - 时间范围
   * @param {string[]} devices - 设备ID列表，可选
   * @returns {Promise<ChartDataPoint[]>} 图表数据点列表
   */
  async getFieldData(field: string, timeRange: string = '-1h', devices?: string[]): Promise<ChartDataPoint[]> {
    console.log(`📊 Getting field data for ${field}, timeRange: ${timeRange}, devices: ${devices}`);

    try {
      const params = new URLSearchParams({
        field,
        timeRange,
      });

      if (devices && devices.length > 0) {
        params.append('devices', devices.join(','));
      }

      const data = await this.makeRequest<ChartDataPoint[]>(
        'get',
        `/api/viewer/fields/data?${params.toString()}`
      );
      console.log(`✅ Retrieved ${data.length} data points for field ${field}`);
      return data;
    } catch (error) {
      console.error(`❌ Failed to get field data for ${field}:`, error);
      throw error;
    }
  }

  /**
   * 获取仪表板统计信息（兼容原有接口）
   *
   * @returns {Promise<DashboardStats>} 仪表板统计信息
   */
  async getDashboardStats(): Promise<DashboardStats> {
    console.log('📊 Getting dashboard stats from API...');

    try {
      const stats = await this.makeRequest<DashboardStats>('get', '/api/viewer/dashboard/stats');
      console.log('✅ Retrieved dashboard stats from API');
      return stats;
    } catch (error) {
      console.error('❌ Failed to get dashboard stats from API:', error);
      throw error;
    }
  }

  /**
   * 获取可用字段列表（兼容原有接口）
   *
   * @returns {Promise<string[]>} 可用字段列表
   */
  async getAvailableFields(): Promise<string[]> {
    console.log('📊 Getting available fields from API...');

    try {
      const fields = await this.makeRequest<string[]>('get', '/api/viewer/fields/available');
      console.log(`✅ Retrieved ${fields.length} available fields from API`);
      return fields;
    } catch (error) {
      console.error('❌ Failed to get available fields from API:', error);
      throw error;
    }
  }

  /**
   * 获取数据类型列表（兼容原有接口）
   *
   * @returns {Promise<string[]>} 数据类型列表
   */
  async getDataTypes(): Promise<string[]> {
    console.log('📊 Getting data types from API...');

    try {
      const dataTypes = await this.makeRequest<string[]>('get', '/api/viewer/data-types');
      console.log(`✅ Retrieved ${dataTypes.length} data types from API`);
      return dataTypes;
    } catch (error) {
      console.error('❌ Failed to get data types from API:', error);
      throw error;
    }
  }

  /**
   * 获取设备记录（支持分页）
   *
   * @param {string} deviceId - 设备ID
   * @param {string} timeRange - 时间范围或开始时间
   * @param {string} endTime - 结束时间（可选）
   * @param {number} page - 页码（从1开始）
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<{records: any[], pagination: any}>} 设备记录列表和分页信息
   */
  async getDeviceRecords(deviceId: string, timeRange: string = '-1h', endTime?: string, page: number = 1, pageSize: number = 50): Promise<{ records: any[], pagination: any }> {
    if (endTime) {
      console.log(`📊 Getting device records for ${deviceId}, timeRange: ${timeRange} to ${endTime}, page: ${page}, pageSize: ${pageSize}`);
    } else {
      console.log(`📊 Getting device records for ${deviceId}, timeRange: ${timeRange}, page: ${page}, pageSize: ${pageSize}`);
    }

    try {
      const params = new URLSearchParams({
        timeRange,
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      // 如果提供了结束时间，添加到参数中
      if (endTime) {
        params.append('endTime', endTime);
      }

      const response = await this.makeRequest<{ records: any[], pagination: any }>(
        'get',
        `/api/viewer/devices/${deviceId}/records?${params.toString()}`
      );
      console.log(`✅ Retrieved ${response.records.length} records for device ${deviceId} (page ${page}/${response.pagination.totalPages})`);
      return response;
    } catch (error) {
      console.error(`❌ Failed to get device records for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * 测试连接（兼容原有接口）
   *
   * @returns {Promise<boolean>} 连接状态
   */
  async testConnection(): Promise<boolean> {
    console.log('📊 Testing API connection...');

    try {
      const result = await this.makeRequest<any>('get', '/api/viewer/connection/test');
      console.log('✅ API connection test successful');
      return result.connected === true;
    } catch (error) {
      console.error('❌ API connection test failed:', error);
      return false;
    }
  }
}

// 导出单例实例
export const apiService = new APIService();
export default APIService;
