// InfluxDB服务
import axios from 'axios';
import { SensorData, DeviceInfo, ChartDataPoint, DashboardStats, InfluxDBConfig } from '../types';

// InfluxDB配置
const INFLUXDB_CONFIG: InfluxDBConfig = {
  url: 'http://localhost:8086',
  token: 'mdc-token-123456789',
  org: 'mdc-org',
  bucket: 'device-data'
};

class InfluxDBService {
  private config: InfluxDBConfig;
  private retryCount: number = 3;
  private retryDelay: number = 1000; // 1秒
  private connectionPool: Map<string, Promise<any>> = new Map();

  constructor(config: InfluxDBConfig = INFLUXDB_CONFIG) {
    this.config = config;
  }

  // 带重试机制的HTTP请求
  private async makeRequest(url: string, options: any, retries: number = this.retryCount): Promise<any> {
    for (let i = 0; i <= retries; i++) {
      try {
        const response = await axios({
          url,
          timeout: 10000, // 10秒超时
          ...options
        });
        return response;
      } catch (error) {
        console.warn(`InfluxDB请求失败 (尝试 ${i + 1}/${retries + 1}):`, error);

        if (i === retries) {
          throw error;
        }

        // 指数退避延迟
        const delay = this.retryDelay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 创建Flux查询的通用方法 - 增强稳定性
  private async executeQuery(query: string): Promise<any> {
    const queryKey = `query_${Date.now()}_${Math.random()}`;

    try {
      // 避免重复查询
      if (this.connectionPool.has(queryKey)) {
        return await this.connectionPool.get(queryKey);
      }

      const queryPromise = this.makeRequest(
        `${this.config.url}/api/v2/query?org=${this.config.org}`,
        {
          method: 'POST',
          data: query,
          headers: {
            'Authorization': `Token ${this.config.token}`,
            'Content-Type': 'application/vnd.flux',
            'Accept': 'application/csv'
          }
        }
      );

      this.connectionPool.set(queryKey, queryPromise);

      const response = await queryPromise;
      this.connectionPool.delete(queryKey);

      return this.parseCSVResponse(response.data);
    } catch (error) {
      this.connectionPool.delete(queryKey);
      console.error('InfluxDB query error:', error);

      // 提供更详细的错误信息
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          throw new Error('无法连接到InfluxDB服务器，请检查服务是否运行');
        } else if (error.response?.status === 401) {
          throw new Error('InfluxDB认证失败，请检查Token配置');
        } else if (error.response?.status === 404) {
          throw new Error('InfluxDB组织或存储桶不存在');
        } else if (error.code === 'ENOTFOUND') {
          throw new Error('InfluxDB服务器地址无法解析');
        }
      }

      throw error;
    }
  }

  // 获取数值字段过滤器
  private getNumericFieldFilter(): string {
    return `|> filter(fn: (r) => r._field == "temperature" or r._field == "humidity" or r._field == "pressure" or r._field == "acceleration_x" or r._field == "acceleration_y" or r._field == "acceleration_z" or r._field == "total_acceleration" or r._field == "temperature_fahrenheit" or r._field == "pressure_atm" or r._field == "pressure_pa" or r._field == "pressure_psi" or r._field == "altitude_meters" or r._field == "altitude_feet" or r._field == "processing_time_ms")`;
  }

  // 解析CSV响应
  private parseCSVResponse(csvData: string): any[] {
    const lines = csvData.trim().split('\n');
    if (lines.length < 2) return [];

    const headers = lines[0].split(',');
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',');
      const row: any = {};

      headers.forEach((header, index) => {
        const cleanHeader = header.trim();
        let value = values[index]?.trim() || '';

        // 移除引号
        if (value.startsWith('"') && value.endsWith('"')) {
          value = value.slice(1, -1);
        }

        // 尝试转换数字
        if (!isNaN(Number(value)) && value !== '') {
          row[cleanHeader] = Number(value);
        } else {
          row[cleanHeader] = value;
        }
      });

      data.push(row);
    }

    return data;
  }

  // 获取最近的传感器数据
  async getRecentSensorData(timeRange: string = '-1h', limit: number = 1000): Promise<SensorData[]> {
    const query = `
      from(bucket: "${this.config.bucket}")
        |> range(start: ${timeRange})
        |> filter(fn: (r) => r._measurement == "sensor_data")
        |> limit(n: ${limit})
        |> sort(columns: ["_time"], desc: true)
    `;

    const data = await this.executeQuery(query);
    return data.map((row: any) => ({
      _time: row._time,
      _value: row._value,
      _field: row._field,
      _measurement: row._measurement,
      device_id: row.device_id,
      data_type: row.data_type,
      location: row.location,
      sequence: row.sequence,
      cleaning_version_str: row.cleaning_version_str,
      processing_time_ms: row.processing_time_ms
    }));
  }

  // 获取设备列表 - 优化状态检测，从raw_data.connected获取在线状态
  async getDevices(): Promise<DeviceInfo[]> {
    try {
      // 首先获取最新的连接状态数据
      const connectionQuery = `
        from(bucket: "${this.config.bucket}")
          |> range(start: -1h)
          |> filter(fn: (r) => r._measurement == "sensor_data")
          |> filter(fn: (r) => r._field == "connected")
          |> group(columns: ["device_id"])
          |> last()
          |> group()
      `;

      // 获取设备状态数据（来自raw_data.status）
      const statusQuery = `
        from(bucket: "${this.config.bucket}")
          |> range(start: -1h)
          |> filter(fn: (r) => r._measurement == "sensor_data")
          |> filter(fn: (r) => r._field == "status_str")
          |> group(columns: ["device_id"])
          |> last()
          |> group()
      `;

      // 获取设备的基本信息
      const deviceQuery = `
        from(bucket: "${this.config.bucket}")
          |> range(start: -24h)
          |> filter(fn: (r) => r._measurement == "sensor_data")
          ${this.getNumericFieldFilter()}
          |> group(columns: ["device_id", "data_type"])
          |> last()
          |> group()
          |> sort(columns: ["_time"], desc: true)
      `;

      const [connectionData, statusData, deviceData] = await Promise.all([
        this.executeQuery(connectionQuery).catch(() => []), // 如果连接状态查询失败，返回空数组
        this.executeQuery(statusQuery).catch(() => []), // 如果状态查询失败，返回空数组
        this.executeQuery(deviceQuery)
      ]);

      const deviceMap = new Map<string, DeviceInfo>();
      const connectionMap = new Map<string, { connected: boolean, lastUpdate: string }>();
      const statusMap = new Map<string, { status: string, lastUpdate: string }>();

      // 处理连接状态数据
      connectionData.forEach((row: any) => {
        if (row.device_id && row._value !== undefined) {
          connectionMap.set(row.device_id, {
            connected: Boolean(row._value),
            lastUpdate: row._time
          });
        }
      });

      // 处理设备状态数据
      statusData.forEach((row: any) => {
        if (row.device_id && row._value !== undefined) {
          statusMap.set(row.device_id, {
            status: String(row._value),
            lastUpdate: row._time
          });
        }
      });

      // 处理设备基本信息
      deviceData.forEach((row: any) => {
        const deviceId = row.device_id;
        if (!deviceMap.has(deviceId)) {
          const lastSeen = new Date(row._time);

          // 优先从raw_data.connected获取状态，否则使用时间判断
          let isOnline = false;
          const connectionInfo = connectionMap.get(deviceId);

          if (connectionInfo) {
            // 使用raw_data.connected字段
            isOnline = connectionInfo.connected;

            // 如果连接状态数据太旧（超过10分钟），认为离线
            const connectionTime = new Date(connectionInfo.lastUpdate);
            if (Date.now() - connectionTime.getTime() > 10 * 60 * 1000) {
              isOnline = false;
            }
          } else {
            // 回退到时间判断（5分钟内为在线）
            isOnline = Date.now() - lastSeen.getTime() < 5 * 60 * 1000;
          }

          // 获取设备状态
          const statusInfo = statusMap.get(deviceId);
          const deviceStatus = statusInfo ? statusInfo.status : 'unknown';

          // 修复：当设备状态为 shutdown 时，强制设置为离线
          if (deviceStatus === 'shutdown') {
            isOnline = false;
          }

          // 从metadata获取位置信息，或根据设备类型生成
          let location = row.location || 'Unknown';
          if (location === 'Unknown' || !location) {
            if (deviceId.includes('temp_sensor')) {
              location = 'Temperature Zone';
            } else if (deviceId.includes('pressure_sensor')) {
              location = 'Pressure Zone';
            } else if (deviceId.includes('motion_sensor')) {
              location = 'Motion Zone';
            } else if (row.data_type) {
              location = `${row.data_type} Zone`;
            }
          }

          // 确定最终状态：考虑连接状态和设备状态
          let finalStatus: 'online' | 'offline' | 'warning' = 'offline';

          if (isOnline) {
            // 在线状态下，根据设备状态细分
            if (deviceStatus === 'running' || deviceStatus === 'idle' || deviceStatus === 'standby') {
              finalStatus = 'online';
            } else if (deviceStatus === 'alarm' || deviceStatus === 'error') {
              finalStatus = 'warning';
            } else if (deviceStatus === 'shutdown') {
              finalStatus = 'offline';
            } else {
              finalStatus = 'online'; // 默认在线
            }
          } else {
            // 离线状态
            finalStatus = 'offline';
          }

          deviceMap.set(deviceId, {
            device_id: deviceId,
            data_type: row.data_type || 'unknown',
            location: location,
            last_seen: row._time,
            total_records: 1,
            latest_value: row._value,
            status: finalStatus,
            device_status: deviceStatus
          });
        } else {
          const device = deviceMap.get(deviceId)!;
          device.total_records += 1;
        }
      });

      return Array.from(deviceMap.values());
    } catch (error) {
      console.error('Error fetching devices:', error);
      // 返回空数组而不是抛出错误，提高稳定性
      return [];
    }
  }

  // 获取特定设备的数据
  async getDeviceData(deviceId: string, timeRange: string = '-1h'): Promise<ChartDataPoint[]> {
    const query = `
      from(bucket: "${this.config.bucket}")
        |> range(start: ${timeRange})
        |> filter(fn: (r) => r._measurement == "sensor_data")
        |> filter(fn: (r) => r.device_id == "${deviceId}")
        |> filter(fn: (r) => r._field != "device_id_str" and r._field != "data_type_str" and r._field != "location_str" and r._field != "cleaning_version_str" and r._field != "raw_data_str" and r._field != "sequence_str" and r._field != "timestamp_str")
        |> sort(columns: ["_time"])
    `;

    const data = await this.executeQuery(query);
    return data.map((row: any) => ({
      timestamp: row._time,
      value: row._value,
      device_id: row.device_id,
      field: row._field
    }));
  }

  // 获取特定字段的数据（用于图表）
  async getFieldData(field: string, timeRange: string = '-1h', devices?: string[]): Promise<ChartDataPoint[]> {
    let deviceFilter = '';
    if (devices && devices.length > 0) {
      const deviceList = devices.map(d => `"${d}"`).join(', ');
      deviceFilter = `|> filter(fn: (r) => contains(value: r.device_id, set: [${deviceList}]))`;
    }

    const query = `
      from(bucket: "${this.config.bucket}")
        |> range(start: ${timeRange})
        |> filter(fn: (r) => r._measurement == "sensor_data")
        |> filter(fn: (r) => r._field == "${field}")
        |> filter(fn: (r) => r._field != "device_id_str" and r._field != "data_type_str" and r._field != "location_str" and r._field != "cleaning_version_str" and r._field != "raw_data_str" and r._field != "sequence_str" and r._field != "timestamp_str")
        ${deviceFilter}
        |> sort(columns: ["_time"])
    `;

    const data = await this.executeQuery(query);
    return data.map((row: any) => ({
      timestamp: row._time,
      value: row._value,
      device_id: row.device_id,
      field: row._field
    }));
  }

  // 获取仪表板统计信息
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // 简化的统计查询 - 直接从设备列表计算
      const devices = await this.getDevices();
      const onlineDevices = devices.filter(d => d.status === 'online');

      // 获取总记录数（简化查询）
      const recordsQuery = `
        from(bucket: "${this.config.bucket}")
          |> range(start: -24h)
          |> filter(fn: (r) => r._measurement == "sensor_data")
          ${this.getNumericFieldFilter()}
          |> count()
          |> group()
          |> sum()
      `;

      // 获取每秒记录数（最近1分钟）
      const rateQuery = `
        from(bucket: "${this.config.bucket}")
          |> range(start: -1m)
          |> filter(fn: (r) => r._measurement == "sensor_data")
          ${this.getNumericFieldFilter()}
          |> count()
          |> group()
          |> sum()
      `;

      const [recordsData, rateData] = await Promise.all([
        this.executeQuery(recordsQuery),
        this.executeQuery(rateQuery)
      ]);

      return {
        total_devices: devices.length,
        online_devices: onlineDevices.length,
        total_records: recordsData[0]?._value || 0,
        records_per_second: Math.round((rateData[0]?._value || 0) / 60),
        last_update: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        total_devices: 0,
        online_devices: 0,
        total_records: 0,
        records_per_second: 0,
        last_update: new Date().toISOString()
      };
    }
  }

  // 获取可用的字段列表
  async getAvailableFields(): Promise<string[]> {
    const query = `
      from(bucket: "${this.config.bucket}")
        |> range(start: -1h)
        |> filter(fn: (r) => r._measurement == "sensor_data")
        |> filter(fn: (r) => r._field != "device_id_str" and r._field != "data_type_str" and r._field != "location_str" and r._field != "cleaning_version_str" and r._field != "raw_data_str" and r._field != "sequence_str" and r._field != "timestamp_str")
        |> group(columns: ["_field"])
        |> distinct(column: "_field")
        |> group()
    `;

    const data = await this.executeQuery(query);
    return data.map((row: any) => row._field).filter(Boolean);
  }

  // 获取数据类型列表
  async getDataTypes(): Promise<string[]> {
    const query = `
      from(bucket: "${this.config.bucket}")
        |> range(start: -1h)
        |> filter(fn: (r) => r._measurement == "sensor_data")
        |> filter(fn: (r) => r._field != "device_id_str" and r._field != "data_type_str" and r._field != "location_str" and r._field != "cleaning_version_str" and r._field != "raw_data_str" and r._field != "sequence_str" and r._field != "timestamp_str")
        |> group(columns: ["data_type"])
        |> distinct(column: "data_type")
        |> group()
    `;

    const data = await this.executeQuery(query);
    return data.map((row: any) => row.data_type).filter(Boolean);
  }

  // 删除指定设备的所有记录
  async deleteDevice(deviceId: string): Promise<boolean> {
    try {
      // 注意：InfluxDB的删除操作需要使用DELETE API
      const response = await this.makeRequest(
        `${this.config.url}/api/v2/delete?org=${this.config.org}&bucket=${this.config.bucket}`,
        {
          method: 'POST',
          data: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天前
            stop: new Date().toISOString(),
            predicate: `_measurement="sensor_data" AND device_id="${deviceId}"`
          },
          headers: {
            'Authorization': `Token ${this.config.token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.status === 204;
    } catch (error) {
      console.error('Error deleting device:', error);
      throw new Error(`删除设备 ${deviceId} 失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 清空所有设备和记录
  async clearAllData(): Promise<boolean> {
    try {
      // 删除所有sensor_data记录
      const response = await this.makeRequest(
        `${this.config.url}/api/v2/delete?org=${this.config.org}&bucket=${this.config.bucket}`,
        {
          method: 'POST',
          data: {
            start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年前
            stop: new Date().toISOString(),
            predicate: '_measurement="sensor_data"'
          },
          headers: {
            'Authorization': `Token ${this.config.token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.status === 204;
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw new Error(`清空所有数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 获取设备详细记录（用于详情页面）- 修复时间筛选数据一致性问题，包含system类型记录
  async getDeviceRecords(deviceId: string, timeRange: string = '-1h', limit: number = 1000): Promise<any[]> {
    try {
      // 修复：确保包含所有data_type的记录，特别是system类型的连接状态记录
      // 使用更大的初始限制来确保获取足够的数据进行分组
      const query = `
        from(bucket: "${this.config.bucket}")
          |> range(start: ${timeRange})
          |> filter(fn: (r) => r._measurement == "sensor_data")
          |> filter(fn: (r) => r.device_id == "${deviceId}")
          |> sort(columns: ["_time"], desc: true)
          |> limit(n: ${limit * 20})
      `;

      const data = await this.executeQuery(query);



      // 按时间戳分组记录，确保包含所有data_type
      const recordMap = new Map<string, any>();

      data.forEach((row: any) => {
        const timestamp = row._time;
        const key = `${timestamp}_${row.data_type || 'unknown'}`;

        if (!recordMap.has(key)) {
          recordMap.set(key, {
            timestamp: timestamp,
            device_id: row.device_id,
            data_type: row.data_type || 'unknown',
            location: row.location,
            sequence: row.sequence,
            fields: {}
          });
        }

        const record = recordMap.get(key);
        if (row._field) {
          record.fields[row._field] = row._value;
        }
      });

      // 过滤掉无效时间戳的记录，然后按时间戳倒序排序
      const allRecords = Array.from(recordMap.values());
      const validRecords = allRecords.filter((record: any) => {
        const timestamp = new Date(record.timestamp).getTime();
        return !isNaN(timestamp);
      });

      const sortedRecords = validRecords.sort((a, b) => {
        const timeA = new Date(a.timestamp).getTime();
        const timeB = new Date(b.timestamp).getTime();
        return timeB - timeA; // 倒序排序，最新的在前面
      });

      // 应用最终的记录数限制
      const finalRecords = sortedRecords.slice(0, limit);

      return finalRecords;
    } catch (error) {
      console.error('Error fetching device records:', error);
      return [];
    }
  }

  // 测试连接 - 增强稳定性
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest(`${this.config.url}/health`, {
        method: 'GET'
      });
      return response.status === 200;
    } catch (error) {
      console.error('InfluxDB connection test failed:', error);
      return false;
    }
  }
}

export const influxDBService = new InfluxDBService();
export default InfluxDBService;
