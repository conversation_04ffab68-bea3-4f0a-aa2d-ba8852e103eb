回答用户关于"记录数"和"最新值"字段来源的问题：

20_viewer主页显示的"记录数"和"最新值"字段来源分析：

## 1. 记录数 (total_records)
**数据来源**：InfluxDB中的`sensor_data` measurement
**计算逻辑**（位于20_viewer/src/services/influxdb.ts第249-256行）：
- 初始值：每个设备第一次出现时设置为1（第249行）
- 累加逻辑：每遇到同一设备的新记录，total_records += 1（第255行）
- 查询范围：最近24小时的数据（第179行：range(start: -24h)）
- 字段过滤：只统计数值字段，通过getNumericFieldFilter()过滤

**getNumericFieldFilter()包含的字段**（第98-100行）：
```
temperature, humidity, pressure, acceleration_x, acceleration_y, acceleration_z, 
total_acceleration, temperature_fahrenheit, pressure_atm, pressure_pa, 
pressure_psi, altitude_meters, altitude_feet, processing_time_ms
```

## 2. 最新值 (latest_value)
**数据来源**：InfluxDB中的`sensor_data` measurement的`_value`字段
**获取逻辑**（位于第250行）：
- 来源：`row._value`
- 查询方式：使用`|> last()`获取每个设备的最新记录
- 数据范围：最近24小时内的最新数值字段数据
- 字段类型：只包含数值类型字段（通过getNumericFieldFilter()过滤）

## 3. 查询结构
```flux
from(bucket: "device-data")
  |> range(start: -24h)
  |> filter(fn: (r) => r._measurement == "sensor_data")
  |> filter(fn: (r) => r._field == "temperature" or r._field == "pressure" or ...)
  |> group(columns: ["device_id", "data_type"])
  |> last()
```

因此：
- **记录数**：统计每个设备在24小时内的数值字段记录总数
- **最新值**：每个设备最新的数值字段的_value值