回答用户关于设备状态字段来源的问题：

20_viewer主页显示的"状态"字段来源分析：

1. **主要数据源**：InfluxDB中的`sensor_data` measurement

2. **状态判断逻辑**（位于20_viewer/src/services/influxdb.ts第162-265行）：
   - **优先级1**：从InfluxDB的`connected`字段获取状态
     - 查询条件：`r._field == "connected"`
     - 数据来源：raw_data.connected字段
     - 判断逻辑：Boolean(row._value)
   
   - **优先级2**：时间判断作为备用方案
     - 如果connected字段数据超过10分钟，认为离线
     - 如果没有connected字段，则根据最后数据时间判断（5分钟内为在线）

3. **具体实现**：
   ```typescript
   // 第166-174行：查询connected字段
   const connectionQuery = `
     from(bucket: "${this.config.bucket}")
       |> range(start: -1h)
       |> filter(fn: (r) => r._measurement == "sensor_data")
       |> filter(fn: (r) => r._field == "connected")
   `;
   
   // 第216-228行：状态判断逻辑
   if (connectionInfo) {
     isOnline = connectionInfo.connected; // 使用connected字段
     // 如果数据太旧（超过10分钟），认为离线
   } else {
     // 回退到时间判断（5分钟内为在线）
     isOnline = Date.now() - lastSeen.getTime() < 5 * 60 * 1000;
   }
   ```

4. **最终状态值**：
   - `'online'`：设备在线
   - `'offline'`：设备离线

因此，状态主要取自InfluxDB的`connected`字段，这个字段应该由数据推送服务（如05_data_push）写入到sensor_data measurement中。