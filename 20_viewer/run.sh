#!/bin/bash

# 数据查看器前端启动脚本
# 解决Node.js环境配置问题并启动20_viewer前端服务

echo "🚀 启动 数据查看器前端"
echo "=================================="

# 检查并修复Node.js环境
echo "🔧 检查Node.js环境..."

# 设置正确的Node.js环境变量
export PATH=/opt/homebrew/bin:$PATH

# 验证Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ 错误: Node.js未安装或不在PATH中"
    echo "请确保Node.js已正确安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm未安装或不在PATH中"
    echo "请确保npm已正确安装"
    exit 1
fi

# 显示Node.js版本信息
echo "✅ Node.js版本: $(node --version)"
echo "✅ npm版本: $(npm --version)"

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    echo "请确保在20_viewer目录中运行此脚本"
    exit 1
fi

echo "✅ 项目文件检查通过"

# 检查端口是否被占用
PORT=9102
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 $PORT 已被占用"
    echo "正在尝试停止占用端口的进程..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 检查node_modules是否存在
if [ ! -d "node_modules" ]; then
    echo "📦 安装npm依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 错误: npm依赖安装失败"
        exit 1
    fi
else
    echo "✅ npm依赖已存在"
fi

# 设置环境变量
echo "🔧 设置环境变量..."
export PORT=$PORT
export REACT_APP_API_HOST=${REACT_APP_API_HOST:-"http://localhost:9005"}

echo "✅ 环境变量设置完成"
echo "  - PORT: $PORT"
echo "  - REACT_APP_API_HOST: $REACT_APP_API_HOST"

echo ""
echo "🌐 启动数据查看器前端..."
echo "端口: $PORT"
echo "访问地址: http://localhost:$PORT"
echo "API后端: $REACT_APP_API_HOST"
echo ""
echo "功能特性:"
echo "  - 数据可视化界面"
echo "  - 实时数据展示"
echo "  - 图表和报表"
echo "  - 数据分析工具"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 启动React开发服务器
npm start
