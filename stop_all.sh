#!/bin/bash

# 全系统停止脚本
# 停止所有服务并清理端口

echo "🛑 停止完整的设备数据采集系统"
echo "=============================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务端口配置
declare -A SERVICE_PORTS=(
    ["Docker Redis"]="6379"
    ["Docker NATS"]="4222"
    ["Docker InfluxDB"]="8086"
    ["Docker MongoDB"]="27017"
    ["设备数据采集服务"]="9001"
    ["数据推送服务"]="9003"
    ["NATS JetStream服务"]="9004"
    ["Go API服务"]="9005"
    ["FANUC设备采集器"]="8080"
    ["管理后台前端"]="9101"
    ["数据查看器前端"]="3000"
    ["机器状态前端"]="9100"
)

# 停止指定端口的进程
stop_port() {
    local port=$1
    local service_name=$2
    
    echo -e "${BLUE}🔍 检查端口 $port ($service_name)...${NC}"
    
    # 查找占用端口的进程
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -z "$pids" ]; then
        echo -e "${GREEN}✅ 端口 $port 未被占用${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}⚠️  端口 $port 被以下进程占用:${NC}"
    lsof -i:$port 2>/dev/null | head -10
    
    echo -e "${BLUE}🔄 正在停止端口 $port 的进程...${NC}"
    
    # 首先尝试优雅停止 (SIGTERM)
    echo "$pids" | xargs kill -TERM 2>/dev/null || true
    sleep 3
    
    # 检查是否还有进程
    local remaining_pids=$(lsof -ti:$port 2>/dev/null)
    if [ -n "$remaining_pids" ]; then
        echo -e "${YELLOW}⚠️  优雅停止失败，强制停止进程...${NC}"
        echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        sleep 1
    fi
    
    # 最终检查
    local final_pids=$(lsof -ti:$port 2>/dev/null)
    if [ -z "$final_pids" ]; then
        echo -e "${GREEN}✅ 端口 $port 已成功释放${NC}"
    else
        echo -e "${RED}❌ 端口 $port 仍被占用${NC}"
    fi
    
    echo ""
}

# 停止Docker服务
stop_docker_services() {
    echo -e "${BLUE}🐳 停止Docker服务...${NC}"
    
    if command -v docker-compose &> /dev/null; then
        if [ -f "docker-service/docker-compose.yml" ]; then
            echo "停止docker-service中的服务..."
            cd docker-service
            docker-compose down
            cd ..
        fi
        
        if [ -f "docker-compose.yml" ]; then
            echo "停止根目录的Docker服务..."
            docker-compose down
        fi
    else
        echo -e "${YELLOW}⚠️  docker-compose未安装，跳过Docker服务停止${NC}"
    fi
    
    echo ""
}

# 停止Node.js进程
stop_nodejs_processes() {
    echo -e "${BLUE}🟢 停止Node.js相关进程...${NC}"
    
    # 查找并停止Node.js进程
    local node_pids=$(pgrep -f "node.*start\|npm.*start\|npm.*dev\|next.*dev" 2>/dev/null || true)
    
    if [ -n "$node_pids" ]; then
        echo -e "${YELLOW}⚠️  发现Node.js进程，正在停止...${NC}"
        echo "$node_pids" | xargs kill -TERM 2>/dev/null || true
        sleep 3
        
        # 强制停止仍在运行的进程
        local remaining_node_pids=$(pgrep -f "node.*start\|npm.*start\|npm.*dev\|next.*dev" 2>/dev/null || true)
        if [ -n "$remaining_node_pids" ]; then
            echo "$remaining_node_pids" | xargs kill -9 2>/dev/null || true
        fi
        
        echo -e "${GREEN}✅ Node.js进程已停止${NC}"
    else
        echo -e "${GREEN}✅ 未发现Node.js进程${NC}"
    fi
    
    echo ""
}

# 停止Go进程
stop_go_processes() {
    echo -e "${BLUE}🔵 停止Go相关进程...${NC}"
    
    # 查找并停止Go进程
    local go_pids=$(pgrep -f "go run\|main\.go" 2>/dev/null || true)
    
    if [ -n "$go_pids" ]; then
        echo -e "${YELLOW}⚠️  发现Go进程，正在停止...${NC}"
        echo "$go_pids" | xargs kill -TERM 2>/dev/null || true
        sleep 3
        
        # 强制停止仍在运行的进程
        local remaining_go_pids=$(pgrep -f "go run\|main\.go" 2>/dev/null || true)
        if [ -n "$remaining_go_pids" ]; then
            echo "$remaining_go_pids" | xargs kill -9 2>/dev/null || true
        fi
        
        echo -e "${GREEN}✅ Go进程已停止${NC}"
    else
        echo -e "${GREEN}✅ 未发现Go进程${NC}"
    fi
    
    echo ""
}

# 清理临时文件
cleanup_temp_files() {
    echo -e "${BLUE}🧹 清理临时文件...${NC}"
    
    # 清理日志文件（可选）
    # find . -name "*.log" -type f -mtime +7 -delete 2>/dev/null || true
    
    # 清理Go构建缓存
    go clean -cache 2>/dev/null || true
    
    echo -e "${GREEN}✅ 临时文件清理完成${NC}"
    echo ""
}

# 显示系统状态
show_system_status() {
    echo -e "${BLUE}📊 系统状态检查...${NC}"
    
    echo "端口占用情况:"
    for service_name in "${!SERVICE_PORTS[@]}"; do
        port="${SERVICE_PORTS[$service_name]}"
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo -e "  端口 $port ($service_name): ${RED}占用${NC}"
        else
            echo -e "  端口 $port ($service_name): ${GREEN}空闲${NC}"
        fi
    done
    
    echo ""
    echo "进程情况:"
    local node_count=$(pgrep -f "node" 2>/dev/null | wc -l || echo "0")
    local go_count=$(pgrep -f "go run" 2>/dev/null | wc -l || echo "0")
    echo "  Node.js进程: $node_count 个"
    echo "  Go进程: $go_count 个"
    
    echo ""
}

# 主程序
main() {
    echo "停止顺序:"
    echo "1. 停止所有端口的进程"
    echo "2. 停止Docker服务"
    echo "3. 停止Node.js进程"
    echo "4. 停止Go进程"
    echo "5. 清理临时文件"
    echo ""
    
    # 按端口停止服务
    for service_name in "${!SERVICE_PORTS[@]}"; do
        port="${SERVICE_PORTS[$service_name]}"
        stop_port "$port" "$service_name"
    done
    
    # 停止Docker服务
    stop_docker_services
    
    # 停止Node.js进程
    stop_nodejs_processes
    
    # 停止Go进程
    stop_go_processes
    
    # 清理临时文件
    cleanup_temp_files
    
    # 显示最终状态
    show_system_status
    
    echo -e "${GREEN}🎉 所有服务已停止！${NC}"
    echo ""
    echo "如果仍有进程未停止，请手动检查并停止："
    echo "  - 查看端口占用: lsof -i:端口号"
    echo "  - 停止进程: kill -9 进程ID"
    echo "  - 查看所有相关进程: ps aux | grep -E 'node|go run'"
}

# 执行主程序
main "$@"
