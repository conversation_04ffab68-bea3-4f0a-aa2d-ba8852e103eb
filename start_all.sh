#!/bin/bash

# 全系统启动脚本
# 按正确顺序启动所有服务

echo "🚀 启动完整的设备数据采集系统"
echo "=============================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务配置
declare -A SERVICES=(
    ["docker-service"]="Docker基础服务"
    ["02_generate_data"]="数据生成服务"
    ["02_device_collect"]="设备数据采集服务"
    ["05_data_push"]="数据推送服务"
    ["11_server_jet"]="NATS JetStream服务"
    ["12_server_api"]="Go API服务"
    ["01_device_api/fanuc_v2"]="FANUC设备采集器(Docker容器)"
    ["13_server_admin"]="管理后台前端"
    ["20_viewer"]="数据查看器前端"
    ["30_machine_status"]="机器状态前端"
)

declare -A SERVICE_PORTS=(
    ["docker-service"]="6379,4222,8086,27017"
    ["02_generate_data"]="无HTTP端口"
    ["02_device_collect"]="9001"
    ["05_data_push"]="9003"
    ["11_server_jet"]="9004"
    ["12_server_api"]="9005"
    ["01_device_api/fanuc_v2"]="8080"
    ["13_server_admin"]="9101"
    ["20_viewer"]="3000"
    ["30_machine_status"]="9100"
)

# 检查服务是否存在
check_service() {
    local service_dir=$1
    if [ ! -d "$service_dir" ]; then
        echo -e "${RED}❌ 服务目录不存在: $service_dir${NC}"
        return 1
    fi
    
    if [ ! -f "$service_dir/run.sh" ]; then
        echo -e "${RED}❌ 运行脚本不存在: $service_dir/run.sh${NC}"
        return 1
    fi
    
    return 0
}

# 启动单个服务
start_service() {
    local service_dir=$1
    local service_name=$2
    local service_port=$3
    
    echo -e "${BLUE}🔄 启动 $service_name...${NC}"
    echo "   目录: $service_dir"
    echo "   端口: $service_port"
    
    if ! check_service "$service_dir"; then
        return 1
    fi
    
    # 在新的终端窗口中启动服务
    if command -v osascript &> /dev/null; then
        # macOS
        osascript -e "tell application \"Terminal\" to do script \"cd $(pwd)/$service_dir && ./run.sh\""
    elif command -v gnome-terminal &> /dev/null; then
        # Linux with GNOME
        gnome-terminal --working-directory="$(pwd)/$service_dir" -- bash -c "./run.sh; exec bash"
    elif command -v xterm &> /dev/null; then
        # Linux with xterm
        xterm -e "cd $(pwd)/$service_dir && ./run.sh" &
    else
        # 后台启动
        echo -e "${YELLOW}⚠️  无法检测到终端，将在后台启动服务${NC}"
        cd "$service_dir" && ./run.sh &
        cd ..
    fi
    
    echo -e "${GREEN}✅ $service_name 启动命令已执行${NC}"
    echo ""
    sleep 2
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -l, --list     列出所有服务"
    echo "  -s, --service  启动指定服务"
    echo "  -a, --all      启动所有服务（默认）"
    echo ""
    echo "示例:"
    echo "  $0                           # 启动所有服务"
    echo "  $0 --service 12_server_api   # 只启动API服务"
    echo "  $0 --list                    # 列出所有服务"
}

# 列出所有服务
list_services() {
    echo "可用的服务:"
    echo "============"
    for service_dir in "${!SERVICES[@]}"; do
        service_name="${SERVICES[$service_dir]}"
        service_port="${SERVICE_PORTS[$service_dir]}"
        echo "  $service_dir - $service_name (端口: $service_port)"
    done
}

# 启动指定服务
start_single_service() {
    local target_service=$1
    
    if [[ -z "${SERVICES[$target_service]}" ]]; then
        echo -e "${RED}❌ 未找到服务: $target_service${NC}"
        echo ""
        list_services
        exit 1
    fi
    
    service_name="${SERVICES[$target_service]}"
    service_port="${SERVICE_PORTS[$target_service]}"
    
    start_service "$target_service" "$service_name" "$service_port"
}

# 启动所有服务
start_all_services() {
    echo "启动顺序:"
    echo "1. Docker基础服务 (Redis, NATS, InfluxDB, MongoDB)"
    echo "2. 数据生成服务"
    echo "3. 设备数据采集服务"
    echo "4. 数据推送服务"
    echo "5. NATS JetStream服务"
    echo "6. Go API服务"
    echo "7. FANUC设备采集器"
    echo "8. 前端服务"
    echo ""
    
    # 按顺序启动服务
    local services_order=(
        "docker-service"
        "02_generate_data"
        "02_device_collect"
        "05_data_push"
        "11_server_jet"
        "12_server_api"
        "01_device_api/fanuc_v2"
        "13_server_admin"
        "20_viewer"
        "30_machine_status"
    )
    
    for service_dir in "${services_order[@]}"; do
        if [[ -n "${SERVICES[$service_dir]}" ]]; then
            service_name="${SERVICES[$service_dir]}"
            service_port="${SERVICE_PORTS[$service_dir]}"
            start_service "$service_dir" "$service_name" "$service_port"
        fi
    done
    
    echo -e "${GREEN}🎉 所有服务启动命令已执行完成！${NC}"
    echo ""
    echo "访问地址:"
    echo "  - API服务: http://localhost:9005"
    echo "  - 管理后台: http://localhost:9101"
    echo "  - 数据查看器: http://localhost:3000"
    echo "  - 机器状态: http://localhost:9100"
    echo "  - FANUC采集器: http://localhost:8080"
    echo ""
    echo "请检查各个终端窗口确认服务启动状态"
}

# 主程序
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -l|--list)
            list_services
            ;;
        -s|--service)
            if [ -z "${2:-}" ]; then
                echo -e "${RED}❌ 请指定要启动的服务${NC}"
                show_help
                exit 1
            fi
            start_single_service "$2"
            ;;
        -a|--all|"")
            start_all_services
            ;;
        *)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"
