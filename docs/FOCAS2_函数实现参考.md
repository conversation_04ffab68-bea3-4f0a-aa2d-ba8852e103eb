# FOCAS2 函数实现参考

## 📋 概述

本文档提供了FANUC FOCAS2/Ethernet各个函数的标准实现模式和参考代码，基于真机验证的成功实现。

## 🔗 连接管理函数

### cnc_startupprocess - 启动FOCAS进程
```go
func startupFOCAS() bool {
    log_fname := C.CString("focas.log")
    defer C.free(unsafe.Pointer(log_fname))
    
    ret := C.cnc_startupprocess(C.long(0), log_fname)
    return checkError("cnc_startupprocess", ret)
}
```

### cnc_allclibhndl3 - 建立以太网连接
```go
func connectToFANUC(ip string, port int) (C.ushort, bool) {
    var libh C.ushort
    
    _ip := C.CString(ip)
    defer C.free(unsafe.Pointer(_ip))
    
    ret := C.cnc_allclibhndl3(_ip, C.ushort(port), 10, &libh)
    if checkError("cnc_allclibhndl3", ret) {
        return libh, true
    }
    return 0, false
}
```

### cnc_freelibhndl - 释放连接句柄
```go
func disconnectFromFANUC(libh C.ushort) {
    ret := C.cnc_freelibhndl(libh)
    checkError("cnc_freelibhndl", ret)
}
```

### cnc_exitprocess - 退出FOCAS进程
```go
func exitFOCAS() {
    ret := C.cnc_exitprocess()
    checkError("cnc_exitprocess", ret)
}
```

## 📊 系统信息函数

### cnc_rdcncid - 读取CNC ID
```go
func readCNCID(libh C.ushort) string {
    var cnc_ids [4]C.ulong
    ret := C.cnc_rdcncid(libh, &cnc_ids[0])
    
    if checkError("cnc_rdcncid", ret) {
        return fmt.Sprintf("%08x-%08x-%08x-%08x", 
            cnc_ids[0], cnc_ids[1], cnc_ids[2], cnc_ids[3])
    }
    return ""
}
```

### cnc_sysinfo - 读取系统信息
```go
type SystemInfo struct {
    AddInfo  int16
    MaxAxis  int16
    CNCType  string
    MTType   string
    Series   string
    Version  string
    Axes     string
}

func readSystemInfo(libh C.ushort) *SystemInfo {
    var sysinfo C.ODBSYS
    ret := C.cnc_sysinfo(libh, &sysinfo)
    
    if checkError("cnc_sysinfo", ret) {
        return &SystemInfo{
            AddInfo: int16(sysinfo.addinfo),
            MaxAxis: int16(sysinfo.max_axis),
            CNCType: C.GoString(&sysinfo.cnc_type[0]),
            MTType:  C.GoString(&sysinfo.mt_type[0]),
            Series:  C.GoString(&sysinfo.series[0]),
            Version: C.GoString(&sysinfo.version[0]),
            Axes:    C.GoString(&sysinfo.axes[0]),
        }
    }
    return nil
}
```

### cnc_statinfo - 读取状态信息
```go
type StatusInfo struct {
    Alarm     int16
    Auto      int16
    Edit      int16
    Emergency int16
    HDCK      int16
    Motion    int16
    MSTB      int16
    Run       int16
    TMMode    int16
}

func readStatusInfo(libh C.ushort) *StatusInfo {
    var statinfo C.ODBST
    ret := C.cnc_statinfo(libh, &statinfo)
    
    if checkError("cnc_statinfo", ret) {
        return &StatusInfo{
            Alarm:     int16(statinfo.alarm),
            Auto:      int16(statinfo.aut),
            Edit:      int16(statinfo.edit),
            Emergency: int16(statinfo.emergency),
            HDCK:      int16(statinfo.hdck),
            Motion:    int16(statinfo.motion),
            MSTB:      int16(statinfo.mstb),
            Run:       int16(statinfo.run),
            TMMode:    int16(statinfo.tmmode),
        }
    }
    return nil
}
```

### cnc_rdaxisname - 读取轴名称
```go
func readAxisNames(libh C.ushort) []string {
    var axes [C.MAX_AXIS]C.ODBAXISNAME
    var cnt C.short = C.MAX_AXIS
    
    ret := C.cnc_rdaxisname(libh, &cnt, (*C.ODBAXISNAME)(unsafe.Pointer(&axes)))
    
    if checkError("cnc_rdaxisname", ret) {
        var axisNames []string
        for i := 0; i < int(cnt); i++ {
            name := C.GoString(&axes[i].name)
            axisNames = append(axisNames, name)
        }
        return axisNames
    }
    return nil
}
```

## 📍 位置和动态数据函数

### cnc_rddynamic2 - 读取动态数据 (推荐)
```go
type DynamicData struct {
    Axis     int16
    Alarm    int32
    PrgNum   int32
    PrgMNum  int32
    SeqNum   int32
    ActF     int32
    ActS     int32
    Absolute []int32
    Machine  []int32
    Relative []int32
    Distance []int32
}

func readDynamicData(libh C.ushort) *DynamicData {
    var dynamic C.ODBDY2_T
    ret := C.cnc_rddynamic2(libh, C.ALL_AXES, C.sizeof_ODBDY2_T, 
                           (*C.ODBDY2)(unsafe.Pointer(&dynamic)))
    
    if checkError("cnc_rddynamic2", ret) {
        pos := (*C.ODBDY2_T_POS)(unsafe.Pointer(&dynamic.pos[0]))
        
        data := &DynamicData{
            Axis:    int16(dynamic.axis),
            Alarm:   int32(dynamic.alarm),
            PrgNum:  int32(dynamic.prgnum),
            PrgMNum: int32(dynamic.prgmnum),
            SeqNum:  int32(dynamic.seqnum),
            ActF:    int32(dynamic.actf),
            ActS:    int32(dynamic.acts),
        }
        
        // 读取位置数据 (假设7轴)
        for i := 0; i < 7; i++ {
            data.Absolute = append(data.Absolute, int32(pos.faxis.absolute[i]))
            data.Machine = append(data.Machine, int32(pos.faxis.machine[i]))
            data.Relative = append(data.Relative, int32(pos.faxis.relative[i]))
            data.Distance = append(data.Distance, int32(pos.faxis.distance[i]))
        }
        
        return data
    }
    return nil
}
```

### cnc_actf - 读取实际进给速度
```go
func readActualFeedrate(libh C.ushort) int32 {
    var feedrate C.ODBACT
    ret := C.cnc_actf(libh, &feedrate)
    
    if checkError("cnc_actf", ret) {
        return int32(feedrate.data)
    }
    return 0
}
```

### cnc_acts - 读取实际主轴转速
```go
func readActualSpindleSpeed(libh C.ushort) int32 {
    var spindle C.ODBACT
    ret := C.cnc_acts(libh, &spindle)
    
    if checkError("cnc_acts", ret) {
        return int32(spindle.data)
    }
    return 0
}
```

## 📄 程序信息函数

### cnc_rdprgnum - 读取程序号
```go
type ProgramInfo struct {
    Current int32
    Main    int32
}

func readProgramNumber(libh C.ushort) *ProgramInfo {
    var prog C.ODBPRO
    ret := C.cnc_rdprgnum(libh, &prog)
    
    if checkError("cnc_rdprgnum", ret) {
        return &ProgramInfo{
            Current: int32(prog.data),
            Main:    int32(prog.mdata),
        }
    }
    return nil
}
```

### cnc_rdseqnum - 读取序列号
```go
func readSequenceNumber(libh C.ushort) int32 {
    var seq C.ODBSEQ
    ret := C.cnc_rdseqnum(libh, &seq)
    
    if checkError("cnc_rdseqnum", ret) {
        return int32(seq.data)
    }
    return 0
}
```

### cnc_exeprgname - 读取执行程序名
```go
type ExecutingProgram struct {
    Name   string
    Number int32
}

func readExecutingProgramName(libh C.ushort) *ExecutingProgram {
    var exeprog C.ODBEXEPRG
    ret := C.cnc_exeprgname(libh, &exeprog)
    
    if checkError("cnc_exeprgname", ret) {
        return &ExecutingProgram{
            Name:   C.GoString(&exeprog.name[0]),
            Number: int32(exeprog.o_num),
        }
    }
    return nil
}
```

### 程序内容读取 - cnc_upstart4/cnc_upload4/cnc_upend4
```go
func readProgramContent(libh C.ushort) ([]byte, error) {
    var programContents []byte
    path := ""
    var _type C.short = 0
    
    // 转换路径为C字符串
    _path := C.CString(path)
    defer C.free(unsafe.Pointer(_path))
    
    // 开始上传
    ret := C.cnc_upstart4(libh, _type, _path)
    if !checkError("cnc_upstart4", ret) {
        return nil, fmt.Errorf("failed to start upload")
    }
    
    // 读取程序内容
    for {
        var l C.long = 1280
        buf := make([]byte, l)
        ret := C.cnc_upload4(libh, &l, (*C.char)(unsafe.Pointer(&buf[0])))
        
        if ret == C.EW_BUFFER {
            continue
        } else if ret != C.EW_OK {
            break
        }
        
        if l > 0 {
            programContents = append(programContents, buf[0:l]...)
            if buf[l-1] == '%' {
                break
            }
        } else {
            break
        }
    }
    
    // 结束上传
    ret = C.cnc_upend4(libh)
    checkError("cnc_upend4", ret)
    
    return programContents, nil
}
```

## 🚨 报警信息函数

### cnc_alarm - 读取报警状态
```go
func readAlarmStatus(libh C.ushort) int16 {
    var alarm C.ODBALM
    ret := C.cnc_alarm(libh, &alarm)
    
    if checkError("cnc_alarm", ret) {
        return int16(alarm.data)
    }
    return -1
}
```

### cnc_rdalmmsg - 读取报警消息
```go
type AlarmMessage struct {
    Number  int32
    Type    int16
    Axis    int16
    MsgLen  int16
    Message string
}

func readAlarmMessage(libh C.ushort) *AlarmMessage {
    var almmsg C.ODBALMMSG
    var num C.short = 1
    
    ret := C.cnc_rdalmmsg(libh, -1, &num, &almmsg)
    
    if checkError("cnc_rdalmmsg", ret) && num > 0 && almmsg.alm_no != 0 {
        return &AlarmMessage{
            Number:  int32(almmsg.alm_no),
            Type:    int16(almmsg._type),
            Axis:    int16(almmsg.axis),
            MsgLen:  int16(almmsg.msg_len),
            Message: C.GoString(&almmsg.alm_msg[0]),
        }
    }
    return nil
}
```

## ⚙️ 参数读取函数

### cnc_rdparam - 读取参数
```go
func readParameter(libh C.ushort, number int) int32 {
    var param C.IODBPSD
    length := C.short(4 + 4) // 4字节头部 + 4字节数据
    
    ret := C.cnc_rdparam(libh, C.short(number), 0, length, &param)
    
    if checkError(fmt.Sprintf("cnc_rdparam(%d)", number), ret) {
        ldata := *(*C.long)(unsafe.Pointer(&param.u[0]))
        return int32(ldata)
    }
    return 0
}

func readAxisParameter(libh C.ushort, number int, axisCount int) []int32 {
    var param C.IODBPSD
    length := C.short(4 + 4*axisCount)
    
    ret := C.cnc_rdparam(libh, C.short(number), C.ALL_AXES, length, &param)
    
    if checkError(fmt.Sprintf("cnc_rdparam(%d,ALL_AXES)", number), ret) {
        ldatas := (*[32]C.long)(unsafe.Pointer(&param.u[0]))
        var result []int32
        for i := 0; i < axisCount; i++ {
            result = append(result, int32(ldatas[i]))
        }
        return result
    }
    return nil
}
```

## 🔍 诊断数据函数

### cnc_diagnoss - 读取诊断数据
```go
func readDiagnostic(libh C.ushort, number int) int32 {
    var diag C.ODBDGN
    length := C.short(4 + 4)
    
    ret := C.cnc_diagnoss(libh, C.short(number), 0, length, &diag)
    
    if checkError(fmt.Sprintf("cnc_diagnoss(%d)", number), ret) {
        ldata := *(*C.long)(unsafe.Pointer(&diag.u[0]))
        return int32(ldata)
    }
    return 0
}
```

## 🔧 刀具信息函数

### cnc_rdtofsinfo - 读取刀具偏置信息
```go
type ToolOffsetInfo struct {
    OffsetType int16
    UseNumber  int16
}

func readToolOffsetInfo(libh C.ushort) *ToolOffsetInfo {
    var tofsinfo C.ODBTLINF
    ret := C.cnc_rdtofsinfo(libh, &tofsinfo)
    
    if checkError("cnc_rdtofsinfo", ret) {
        return &ToolOffsetInfo{
            OffsetType: int16(tofsinfo.ofs_type),
            UseNumber:  int16(tofsinfo.use_no),
        }
    }
    return nil
}
```

### cnc_rdtofs - 读取刀具偏置值
```go
func readToolOffset(libh C.ushort, toolNumber int, offsetType int) int32 {
    var tofs C.ODBTOFS
    length := C.short(unsafe.Sizeof(tofs))
    
    ret := C.cnc_rdtofs(libh, C.short(toolNumber), C.short(offsetType), length, &tofs)
    
    if checkError(fmt.Sprintf("cnc_rdtofs(T%d,type%d)", toolNumber, offsetType), ret) {
        return int32(tofs.data)
    }
    return 0
}
```

## 🛠️ 通用工具函数

### 错误检查函数
```go
func checkError(funcName string, ret C.short) bool {
    if ret != C.EW_OK {
        fmt.Printf("❌ %s failed (error code: %d)\n", funcName, ret)
        return false
    }
    fmt.Printf("✅ %s success\n", funcName)
    return true
}
```

### 完整连接管理
```go
type FANUCConnection struct {
    Handle C.ushort
    IP     string
    Port   int
}

func NewFANUCConnection(ip string, port int) (*FANUCConnection, error) {
    // 启动FOCAS进程
    if !startupFOCAS() {
        return nil, fmt.Errorf("failed to startup FOCAS")
    }
    
    // 建立连接
    handle, success := connectToFANUC(ip, port)
    if !success {
        exitFOCAS()
        return nil, fmt.Errorf("failed to connect to FANUC")
    }
    
    return &FANUCConnection{
        Handle: handle,
        IP:     ip,
        Port:   port,
    }, nil
}

func (fc *FANUCConnection) Close() {
    disconnectFromFANUC(fc.Handle)
    exitFOCAS()
}
```

## 🔋 负载信息函数

### cnc_rdsvmeter - 读取伺服负载计量器数据
```go
type ServoLoadData struct {
    AxisName     string
    LoadValue    float64
    Unit         string
    RawValue     int32
    DecimalPlace int16
}

func readServoLoadMeter(libh C.ushort, maxAxis int) []ServoLoadData {
    var loadmeter [8]C.ODBSVLOAD
    var data_num C.short = C.short(maxAxis)

    ret := C.cnc_rdsvmeter(libh, &data_num, (*C.ODBSVLOAD)(unsafe.Pointer(&loadmeter[0])))

    if checkError("cnc_rdsvmeter", ret) {
        var results []ServoLoadData
        for i := 0; i < int(data_num); i++ {
            load := &loadmeter[i]
            axisName := C.GoString(&load.svload.name)
            loadValue := int32(load.svload.data)
            decimalPlace := int16(load.svload.dec)
            unit := int16(load.svload.unit)

            // 根据小数位数计算实际值
            actualValue := float64(loadValue)
            if decimalPlace > 0 {
                for j := 0; j < int(decimalPlace); j++ {
                    actualValue /= 10.0
                }
            }

            unitStr := "%"
            if unit == 1 {
                unitStr = "rpm"
            }

            results = append(results, ServoLoadData{
                AxisName:     axisName,
                LoadValue:    actualValue,
                Unit:         unitStr,
                RawValue:     loadValue,
                DecimalPlace: decimalPlace,
            })
        }
        return results
    }
    return nil
}
```

### cnc_rdspmeter - 读取主轴负载计量器数据
```go
type SpindleLoadData struct {
    SpindleName  string
    LoadValue    float64
    Unit         string
    RawValue     int32
    DecimalPlace int16
}

func readSpindleLoadMeter(libh C.ushort, dataType int, maxSpindle int) []SpindleLoadData {
    var loadmeter [4]C.ODBSPLOAD
    var data_num C.short = C.short(maxSpindle)

    ret := C.cnc_rdspmeter(libh, C.short(dataType), &data_num, (*C.ODBSPLOAD)(unsafe.Pointer(&loadmeter[0])))

    if checkError(fmt.Sprintf("cnc_rdspmeter(type=%d)", dataType), ret) {
        var results []SpindleLoadData
        for i := 0; i < int(data_num); i++ {
            load := &loadmeter[i]

            // 根据类型选择数据源
            var dataSource *C.LOADELM
            if dataType == 0 {
                dataSource = &load.spload  // 负载数据
            } else {
                dataSource = &load.spspeed // 转速数据
            }

            spindleName := C.GoString(&dataSource.name)
            suff1 := C.GoString(&dataSource.suff1)
            loadValue := int32(dataSource.data)
            decimalPlace := int16(dataSource.dec)
            unit := int16(dataSource.unit)

            // 根据小数位数计算实际值
            actualValue := float64(loadValue)
            if decimalPlace > 0 {
                for j := 0; j < int(decimalPlace); j++ {
                    actualValue /= 10.0
                }
            }

            unitStr := "%"
            if unit == 1 {
                unitStr = "rpm"
            }

            results = append(results, SpindleLoadData{
                SpindleName:  spindleName + suff1,
                LoadValue:    actualValue,
                Unit:         unitStr,
                RawValue:     loadValue,
                DecimalPlace: decimalPlace,
            })
        }
        return results
    }
    return nil
}
```

### cnc_rdspload - 读取主轴负载信息
```go
type SpindleLoadInfo struct {
    SpindleNumber int16
    Type          int16
    LoadData      []int16
}

func readSpindleLoadInfo(libh C.ushort, spindleNumber int) *SpindleLoadInfo {
    var spload C.ODBSPN
    var spNum C.short

    if spindleNumber == -1 {
        spNum = C.ALL_SPINDLES
    } else {
        spNum = C.short(spindleNumber)
    }

    ret := C.cnc_rdspload(libh, spNum, &spload)

    if checkError(fmt.Sprintf("cnc_rdspload(%d)", spindleNumber), ret) {
        info := &SpindleLoadInfo{
            SpindleNumber: int16(spload.datano),
            Type:          int16(spload._type),
        }

        // 读取负载数据
        for i := 0; i < 4; i++ {
            if spload.data[i] != 0 {
                info.LoadData = append(info.LoadData, int16(spload.data[i]))
            }
        }

        return info
    }
    return nil
}
```

## 🎯 负载信息使用示例

### 完整的负载信息读取
```go
func readAllLoadInfo(libh C.ushort) {
    // 读取伺服负载
    servoLoads := readServoLoadMeter(libh, 8)
    if servoLoads != nil {
        fmt.Printf("伺服负载数据:\n")
        for _, load := range servoLoads {
            fmt.Printf("  %s轴: %.1f%s\n", load.AxisName, load.LoadValue, load.Unit)
        }
    }

    // 读取主轴负载
    spindleLoads := readSpindleLoadMeter(libh, 0, 4) // type=0: 负载
    if spindleLoads != nil {
        fmt.Printf("主轴负载数据:\n")
        for _, load := range spindleLoads {
            fmt.Printf("  %s: %.1f%s\n", load.SpindleName, load.LoadValue, load.Unit)
        }
    }

    // 读取主轴转速
    spindleSpeeds := readSpindleLoadMeter(libh, 1, 4) // type=1: 转速
    if spindleSpeeds != nil {
        fmt.Printf("主轴转速数据:\n")
        for _, speed := range spindleSpeeds {
            fmt.Printf("  %s: %.1f%s\n", speed.SpindleName, speed.LoadValue, speed.Unit)
        }
    }

    // 读取主轴负载信息
    spindleInfo := readSpindleLoadInfo(libh, -1) // 所有主轴
    if spindleInfo != nil {
        fmt.Printf("主轴负载信息: 主轴号=%d, 类型=%d\n",
            spindleInfo.SpindleNumber, spindleInfo.Type)
    }
}
```

这些函数实现提供了完整的FOCAS2功能访问能力，包括负载信息读取，经过真机验证，可以直接在项目中使用。
