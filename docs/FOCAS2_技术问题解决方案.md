# FOCAS2 技术问题解决方案

## 📋 概述

本文档详细记录了在FANUC FOCAS2/Ethernet实现过程中遇到的技术问题及其解决方案，为后续开发提供参考。

## 🚨 核心技术问题

### 问题1: Go语言联合体访问问题

#### 问题描述
Go语言中，FOCAS结构体的联合体字段被CGO定义为字节数组，无法直接访问具体字段。

#### 错误现象
```go
// 编译错误
./main.go:748:71: param.u.ldata undefined (type [256]byte has no field or method ldata)
./main.go:763:58: param.u.ldatas undefined (type [256]byte has no field or method ldatas)
```

#### 原因分析
CGO将C语言的联合体转换为Go的字节数组，失去了原有的字段结构。

#### 解决方案
使用`unsafe.Pointer`进行类型转换：

```go
// ✅ 读取单个long值
ldata := *(*C.long)(unsafe.Pointer(&param.u[0]))

// ✅ 读取long数组
ldatas := (*[7]C.long)(unsafe.Pointer(&param.u[0]))

// ✅ 实际应用示例
func readSingleParameter(libh C.ushort, number int, name string) {
    var param C.IODBPSD
    length := C.short(4 + 4)
    ret := C.cnc_rdparam(libh, C.short(number), 0, length, &param)
    
    if checkError(fmt.Sprintf("cnc_rdparam(%d)", number), ret) {
        ldata := *(*C.long)(unsafe.Pointer(&param.u[0]))
        fmt.Printf("参数%d: %d\n", param.datano, ldata)
    }
}
```

#### 关键要点
- 必须导入`unsafe`包
- 理解Go内存布局和C结构体对应关系
- 确保类型转换的安全性

---

### 问题2: 参数传递错误

#### 问题描述
使用错误的参数值导致FOCAS函数调用失败。

#### 错误现象
```
❌ cnc_absolute failed (error code: 2)
❌ cnc_machine failed (error code: 2)
```

#### 原因分析
1. 使用`-1`而不是`C.ALL_AXES`
2. length参数计算错误
3. 轴号参数不正确

#### 解决方案

**正确的参数传递方式：**
```go
// ❌ 错误方式
ret := C.cnc_absolute(libh, -1, 8, &axis_data)

// ✅ 正确方式
ret := C.cnc_absolute(libh, C.ALL_AXES, 8, &axis_data)
```

**正确的length计算：**
```go
// ❌ 错误方式
length := C.short(4 + 4*C.MAX_AXIS)

// ✅ 正确方式 - 使用预定义常量
ret := C.cnc_rddynamic2(libh, C.ALL_AXES, C.sizeof_ODBDY2_T, &dynamic)

// ✅ 正确方式 - 使用unsafe.Sizeof
length := C.short(unsafe.Sizeof(dynamic))
```

---

### 问题3: 自定义结构体定义

#### 问题描述
Go中无法直接处理C语言的复杂联合体结构，特别是ODBDY2中的位置数据。

#### 解决方案
在CGO注释中定义自定义结构体：

```c
/*
// 自定义结构体定义，解决Go中联合体的问题
typedef struct odbdy2_t {
    short dummy;
    short axis;
    long alarm;
    long prgnum;
    long prgmnum;
    long seqnum;
    long actf;
    long acts;
    union {
        struct {
            long absolute[MAX_AXIS];
            long machine[MAX_AXIS];
            long relative[MAX_AXIS];
            long distance[MAX_AXIS];
        } faxis;
    } pos;
} ODBDY2_T;

// 位置数据访问结构
typedef struct odbdy2_t_pos {
    struct {
        long absolute[MAX_AXIS];
        long machine[MAX_AXIS];
        long relative[MAX_AXIS];
        long distance[MAX_AXIS];
    } faxis;
} ODBDY2_T_POS;
*/
```

**使用方式：**
```go
func readAllDynamicData(libh C.ushort) {
    var dynamic C.ODBDY2_T
    ret := C.cnc_rddynamic2(libh, C.ALL_AXES, C.sizeof_ODBDY2_T, 
                           (*C.ODBDY2)(unsafe.Pointer(&dynamic)))
    
    if checkError("cnc_rddynamic2", ret) {
        // 访问位置数据
        pos := (*C.ODBDY2_T_POS)(unsafe.Pointer(&dynamic.pos[0]))
        for i := 0; i < 7; i++ {
            fmt.Printf("轴%d: 绝对=%d, 机械=%d, 相对=%d, 剩余=%d\n",
                i+1,
                pos.faxis.absolute[i],
                pos.faxis.machine[i],
                pos.faxis.relative[i],
                pos.faxis.distance[i])
        }
    }
}
```

---

### 问题4: 错误代码理解和处理

#### 常见错误代码含义

| 错误代码 | 含义 | 解决方案 |
|---------|------|----------|
| 2 | EW_LENGTH - 数据长度错误 | 检查length参数计算 |
| 3 | EW_NUMBER - 数据号错误 | 检查参数/诊断号是否存在 |
| 4 | EW_ATTRIB - 数据属性错误 | 检查功能是否启用或权限 |
| -8 | EW_FUNC - 功能不支持 | 检查CNC状态或配置 |
| -16 | EW_DATA - 数据范围错误 | 检查数据范围是否有效 |

#### 错误处理最佳实践
```go
func checkError(funcName string, ret C.short) bool {
    if ret != C.EW_OK {
        errorMsg := getErrorMessage(ret)
        fmt.Printf("❌ %s failed (error code: %d) - %s\n", 
                   funcName, ret, errorMsg)
        return false
    }
    fmt.Printf("✅ %s success\n", funcName)
    return true
}

func getErrorMessage(errorCode C.short) string {
    switch errorCode {
    case 2:
        return "数据长度错误，检查length参数"
    case 3:
        return "数据号错误，参数/诊断号不存在"
    case 4:
        return "数据属性错误，功能未启用或权限不足"
    case -8:
        return "功能不支持，检查CNC状态"
    case -16:
        return "数据范围错误，检查输入范围"
    default:
        return "未知错误"
    }
}
```

---

### 问题5: 编译和链接问题

#### 问题描述
CGO编译时找不到库文件或头文件。

#### 解决方案
**正确的CGO配置：**
```go
/*
#cgo CFLAGS: -I./fwlib
#cgo LDFLAGS: -L./fwlib -lfwlib32 -Wl,-rpath=./fwlib
#include <stdlib.h>
#include <string.h>
#include "fwlib32.h"
*/
import "C"
```

**文件结构要求：**
```
fanuc_v2/
├── main.go
├── fwlib/
│   ├── fwlib32.h
│   └── libfwlib32.so
```

**编译命令：**
```bash
# 在Docker容器中编译
go build main.go
```

---

### 问题6: 内存管理

#### 问题描述
C字符串内存泄漏和指针安全问题。

#### 解决方案
**正确的内存管理：**
```go
func initializeConnection() (C.ushort, bool) {
    // 创建C字符串
    ip := C.CString(CNC_IP)
    defer C.free(unsafe.Pointer(ip))  // 确保释放内存
    
    log_fname := C.CString("focas.log")
    defer C.free(unsafe.Pointer(log_fname))
    
    // 使用字符串...
    ret := C.cnc_allclibhndl3(ip, C.ushort(CNC_PORT), 10, &libh)
    
    return libh, ret == C.EW_OK
}
```

**关键要点：**
- 所有`C.CString`都要配对`C.free`
- 使用`defer`确保内存释放
- 避免在循环中创建大量C字符串

---

## 🎯 成功实现模式总结

### 1. 标准函数调用模式
```go
func readStandardData(libh C.ushort) {
    var data C.ODBXXX
    length := C.short(unsafe.Sizeof(data))
    
    ret := C.cnc_xxxxx(libh, param1, param2, length, &data)
    
    if checkError("cnc_xxxxx", ret) {
        // 处理数据
        processData(&data)
    }
}
```

### 2. 轴数据读取模式
```go
func readAxisData(libh C.ushort) {
    var data C.ODBAXIS
    ret := C.cnc_absolute(libh, C.ALL_AXES, 8, &data)
    
    if checkError("cnc_absolute", ret) {
        axisCount := 7  // 已知轴数
        for i := 0; i < axisCount; i++ {
            fmt.Printf("轴%d: %d\n", i+1, data.data[i])
        }
    }
}
```

### 3. 参数读取模式
```go
func readParameter(libh C.ushort, number int) {
    var param C.IODBPSD
    length := C.short(4 + 4)  // 头部4字节 + 数据4字节
    
    ret := C.cnc_rdparam(libh, C.short(number), 0, length, &param)
    
    if checkError(fmt.Sprintf("cnc_rdparam(%d)", number), ret) {
        ldata := *(*C.long)(unsafe.Pointer(&param.u[0]))
        fmt.Printf("参数%d: %d\n", param.datano, ldata)
    }
}
```

## 📈 调试技巧

### 1. 逐步验证
- 先确保连接成功
- 逐个测试简单函数
- 再实现复杂功能

### 2. 错误代码分析
- 记录所有错误代码
- 查阅官方文档理解含义
- 针对性调整参数

### 3. 数据验证
- 与参考实现对比结果
- 验证数据的合理性
- 检查数据类型和范围

## 🎯 最佳实践建议

1. **深入学习文档** - 理解每个函数的详细要求
2. **参考成功实现** - 学习开源代码的处理方式
3. **渐进式开发** - 从简单功能开始逐步扩展
4. **完善错误处理** - 提供清晰的错误信息
5. **真机验证** - 在实际设备上测试所有功能

这些解决方案和模式为FOCAS2开发提供了完整的技术指导，确保项目的成功实施。
