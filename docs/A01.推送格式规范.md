
# 推送数据格式规范
```
{
  "device_id": "string",
  "timestamp": "2024-01-01T00:00:00Z",
  "data_type": "string",
  "raw_data": {},
  "metadata": {}
}
```

## device_id
设备唯一标识符，UUID 格式, 如 "00000000-0000-0000-0000-000000000000"
从采集配置中获取.

##  timestamp
当前时间戳, 格式为 ISO 8601，例如 "2024-01-01T00:00:00Z"。
自动获取

##  data_type
数据类型，如 "fanuc_30i", "fanuc_0i", "fanuc_31i", "siemens_840d", "mitsubishi_m700", "mitsubishi_m80", "brother_b00", "brother_c00", "brother_d00", "modbus_tcp", "generic_sensor",
"generic_actuator", "modbus_rs232", "modbus_485" 等。
从采集配置中获取

## raw_data 存储内容规范
raw_data 是一个对象，存储设备从传感器收集的原始数据。具体字段如下：

data_id: (string) 数据唯一标识符, 使用雪花ID，例如 "1000000000000000000"。
connected: (bool) 设备是否连接, true表示连接, false表示断开。
status: 设备状态，如 "production" 生产, "idle" 空闲, "fault" 报警, “shutdown” 关机, "disconnected" 未连接, "maintenance" 保养, "debugging" 调试 等。

status_data: {

} 状态数据

quantity : int 累计产量
current_program : string 当前加工程序的名称
main_program: string 主加工程序的名称
actual_feedrate: int 实际进给速度 (mm/min)
actual_spindle_speed: int 实际主轴转速 (rpm)


示例：

``` json
"raw_data": {
  "data_id": "sensor_001",
  "connected": true,
  "status": "production",
  "status_data": {
    "emergency_stop": 0,
    "run_status": 2
  },
  "quantity": 95,
  "current_program": "1001",
  "main_program": "1002",
  "actual_feedrate": 1500,
  "actual_spindle_speed": 2000
}
```

## metadata 存储内容规范
metadata 是一个对象，包含关于数据的附加信息。具体字段如下：
device_id: (string) 设备的ID, 可选, 采集获取.
device_name: (string) 设备的型号或版本号，例如 "Model X123"。从配置中获取.
brand: (string) 设备的品牌，例如 "Fanuc"。从配置中获取.
model: (string) 设备的型号，例如 "Fanuc 30i".从配置中获取.
ip: (string) 设备的IP地址，例如 "*************"。从配置中获取.
port: (int) 设备的端口，例如 8193。从配置中获取.
firmware_version: (string) 设备的固件版本，例如 "v1.0.0"。 可选, 采集获取
location: (string) 设备的地理位置，通常以字符串形式表示，如 "Room 101, Building A"。 从配置中获取.

示例：

``` json
"metadata": {
  "location": "Room 101, Building A",
  "device_name": "FANUC 30i",
  "brand": "Fanuc",
  "model": "Fanuc 30i",
  "ip": "*************",
  "port": 8193,
  "firmware_version": "v1.0.0",
  "device_id": "000000000000"
}
```
