## 设备利用率统计流程详细分析报告

### 🎯 分析概述

通过访问`http://localhost:9100/device_utilization_machine?date=2025-06-19`页面并分析网络请求，深入研究了前端与后端的设备利用率统计完整流程。

### 📊 数据流架构图

```
前端页面 (30_machine_status)
    ↓
Next.js API代理 (/api/devices/{id}/status-detail)
    ↓
后端API (12_server_api/api/devices/{id}/status-history)
    ↓
数据服务 (DataService.GetDeviceStatusHistory)
    ↓
数据源 (InfluxDB/MongoDB)
    ↓
前端计算 (calculateUtilizationStats)
    ↓
页面展示
```

### 🔍 网络请求分析

#### **1. 页面加载请求**
从网络请求可以看到，页面为每台设备发起了独立的API请求：

```
GET /api/devices/d6b6c35f-2468-429f-831b-c292c821170a/status-detail?date=2025-06-19
GET /api/devices/8e0aab17-4168-4a71-82fa-426ad2d77d83/status-detail?date=2025-06-19
...
GET /api/devices/test-47/status-detail?date=2025-06-19
```

**总计**：47台设备 × 2次请求 = 94个API请求

#### **2. 请求模式**
- **并行请求**：所有设备的状态详情同时请求
- **重复请求**：每台设备被请求了2次（可能是组件重复渲染导致）
- **配置请求**：同时请求工作时间设置和设备配置

### 🏗️ 前端实现架构

#### **1. 页面组件结构**

**主页面**：`device_utilization_machine/page.tsx`

**核心功能**：
```typescript
// 获取设备排名数据（使用与设备详细页面一致的计算方式）
const fetchDeviceRanking = async (date: string) => {
  // 1. 获取所有设备列表
  const devicesResponse = await fetch(`${API_HOST}/api/public/devices/configs`);
  const devices = devicesData.data || devicesData.devices || [];

  // 2. 并行获取所有设备的状态详细数据
  const devicePromises = devices.map(async (device: any) => {
    const deviceId = device.id || device.device_id;
    const response = await fetch(`/api/devices/${deviceId}/status-detail?date=${date}`);
    const data = await response.json();

    // 3. 转换为设备利用率格式
    return {
      device_id: deviceId,
      device_name: device.name || `设备 ${deviceId}`,
      utilization_rate: data.utilizationStats?.utilizationRate || 0,
      // ... 其他字段
    };
  });

  const validDevices = (await Promise.all(devicePromises)).filter(device => device !== null);
  return { device_utilizations: validDevices };
};
```

#### **2. 利用率计算逻辑**

**前端计算**：
```typescript
// 计算总体利用率（使用加权平均算法）
const totalWorkingTime = devices.reduce((sum, device) => sum + device.total_working_time, 0);
const totalProductiveTime = devices.reduce((sum, device) => sum + device.productive_time, 0);
const overallUtilization = totalWorkingTime > 0 ? (totalProductiveTime / totalWorkingTime) * 100 : 0;
```

### 🔧 后端API实现

#### **1. Next.js API代理层**

**文件**：`30_machine_status/src/app/api/devices/[id]/status-detail/route.ts`

**功能**：
```typescript
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const deviceId = params.id;
  const date = searchParams.get('date') || new Date().toISOString().split('T')[0];

  // 调用后端API获取状态历史数据
  const apiUrl = `${backendUrl}/api/devices/${deviceId}/status-history?date=${date}&use_work_time=true`;
  const response = await fetch(apiUrl);
  const data = await response.json();

  // 处理状态历史数据
  const statusHistory = data.statusHistory || [];
  const statusRecords = processStatusHistory(statusHistory, date);

  // 获取工作时间配置并计算利用率统计
  const workTimeConfig = await getWorkTimeConfig();
  const utilizationStats = await calculateUtilizationStats(statusRecords, date, workTimeConfig);

  return NextResponse.json({
    success: true,
    deviceId,
    date,
    statusRecords,
    utilizationStats,
    total: statusRecords.length
  });
}
```

#### **2. 前端利用率计算**

**核心算法**：
```typescript
async function calculateUtilizationStats(
  records: StatusRecord[],
  date: string,
  workTimeConfig?: WorkTimeConfig
): Promise {
  let productionTime = 0;
  let idleTime = 0;
  let faultTime = 0;
  let shutdownTime = 0;

  // 计算各状态时间（与统计服务保持一致的状态映射）
  records.forEach(record => {
    switch (record.status) {
      case 'production':
      case 'running':  // 🔧 修复：添加running状态
        productionTime += record.duration;
        break;
      case 'idle':
        idleTime += record.duration;
        break;
      case 'fault':
      case 'error':  // 🔧 修复：添加error状态
        faultTime += record.duration;
        break;
      case 'shutdown':
        shutdownTime += record.duration;
        break;
    }
  });

  // 计算总工作时间
  const totalWorkTime = await calculateTotalWorkTime(date, workTimeConfig);
  
  // 计算利用率
  const utilizationRate = totalWorkTime > 0 ? (productionTime / totalWorkTime) * 100 : 0;

  return {
    totalTime: 24 * 60 * 60, // 24小时的秒数
    productionTime,
    idleTime,
    faultTime,
    shutdownTime,
    utilizationRate
  };
}
```

#### **3. 工作时间计算**

**OP1模式**（24小时连续）：
```typescript
if (utilization_mode === 'OP1') {
  return 24 * 60 * 60; // 24小时的秒数
}
```

**OP2模式**（扣除休息时间）：
```typescript
// OP2模式：24小时 - 休息时间
let totalRestTime = 0;
rest_periods?.forEach(period => {
  if (period.enabled) {
    totalRestTime += calculatePeriodDuration(period.start_time, period.end_time);
  }
});

return (24 * 60 * 60) - totalRestTime;
```

### 🏢 后端服务实现

#### **1. 状态历史API**

**文件**：`12_server_api/handlers/machine/status_history_handler.go`

**核心方法**：
```go
func (h *StatusHistoryHandler) GetDeviceStatusHistoryV2(c *gin.Context) {
  deviceID := c.Param("id")
  date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))
  
  // 获取工作时间设置
  workTimeSettings, _ := h.workTimeService.GetWorkTimeSettings()
  
  // 从数据服务获取设备状态历史
  statusHistory, err := h.dataService.GetDeviceStatusHistory([]string{deviceID}, date, workTimeSettings)
  
  // 生成时间段并预处理数据
  timeSlots := h.generateTimeSlotDataWithPreprocessing(statusHistory, workTimeSettings, date)
  
  // 为前端兼容性添加扁平的statusHistory数组
  var flatStatusHistory []models.TimeSlotStatusRecord
  for _, slot := range timeSlots {
    flatStatusHistory = append(flatStatusHistory, slot.Records...)
  }
  
  c.JSON(http.StatusOK, gin.H{
    "device_info": deviceInfo,
    "time_slots": timeSlots,
    "statusHistory": flatStatusHistory,
    "date": date,
    "total": len(flatStatusHistory),
  })
}
```

#### **2. 数据服务层**

**文件**：`12_server_api/services/data_service.go`

**智能数据路由**：
```go
func (ds *DataService) GetDeviceStatusHistory(deviceIDs []string, date string, workTimeSettings *models.WorkTimeSettings) ([]models.DeviceStatusHistory, error) {
  // 智能路由：根据数据时效性选择不同的处理策略
  if models.IsToday(date) {
    // 实时数据路径：当天数据从InfluxDB获取
    return ds.getTodayStatusHistory(deviceIDs, date, workTimeSettings)
  } else {
    // 历史数据路径：非当天数据优先从MongoDB获取
    return ds.getHistoryStatusHistory(deviceIDs, date)
  }
}
```

**数据源选择逻辑**：
```go
// 单设备查询时，尝试从MongoDB获取
if len(deviceIDs) == 1 && ds.mongo != nil {
  deviceID := deviceIDs[0]
  history, err := ds.mongo.GetDeviceStatusHistory(deviceID, date)
  if err == nil && history != nil && len(history) > 0 {
    // 过滤MongoDB数据到指定时间范围
    filteredHistory := ds.filterHistoryByTimeRange(history, startTime, endTime)
    return filteredHistory, nil
  }
}

// 如果MongoDB没有数据，从InfluxDB获取
return ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startDateTime, endDateTime)
```

### 📈 利用率计算对比

#### **1. 前端计算方式**

**特点**：
- **实时计算**：每次请求都重新计算
- **状态映射**：在前端进行状态标准化
- **工作时间**：前端计算工作时间
- **数据源**：直接从状态历史计算

**算法**：
```typescript
// 前端利用率 = 生产时间 / 工作时间 × 100
const utilizationRate = totalWorkTime > 0 ? (productionTime / totalWorkTime) * 100 : 0;
```

#### **2. 后端统计服务计算方式**

**特点**：
- **缓存优化**：历史数据使用MongoDB缓存
- **批量处理**：支持多设备并行计算
- **智能路由**：根据日期选择数据源
- **统一标准**：使用define包的状态常量

**算法**：
```go
// 后端利用率 = 运行时间 / 工作时间 × 100
utilizationRate := (productiveTime / workingHours) * 100
```

### 🔄 数据流详细步骤

#### **步骤1：前端页面加载**
```typescript
// 1. 获取设备列表
const devicesResponse = await fetch(`${API_HOST}/api/public/devices/configs`);

// 2. 并行获取所有设备状态详情
const devicePromises = devices.map(device => 
  fetch(`/api/devices/${device.id}/status-detail?date=${date}`)
);
```

#### **步骤2：Next.js API代理**
```typescript
// 3. 代理到后端API
const apiUrl = `${backendUrl}/api/devices/${deviceId}/status-history?date=${date}&use_work_time=true`;
const response = await fetch(apiUrl);
```

#### **步骤3：后端数据获取**
```go
// 4. 获取工作时间设置
workTimeSettings, _ := h.workTimeService.GetWorkTimeSettings()

// 5. 从数据服务获取状态历史
statusHistory, err := h.dataService.GetDeviceStatusHistory([]string{deviceID}, date, workTimeSettings)
```

#### **步骤4：数据源查询**
```go
// 6. 智能选择数据源
if models.IsToday(date) {
  // 当天数据：InfluxDB
  return ds.influx.QueryDeviceStatusHistoryWithTimeRange(deviceIDs, date, startDateTime, endDateTime)
} else {
  // 历史数据：MongoDB优先，InfluxDB备用
  return ds.mongo.GetDeviceStatusHistory(deviceID, date)
}
```

#### **步骤5：前端数据处理**
```typescript
// 7. 处理状态历史数据
const statusRecords = processStatusHistory(statusHistory, date);

// 8. 计算利用率统计
const utilizationStats = await calculateUtilizationStats(statusRecords, date, workTimeConfig);
```

#### **步骤6：页面渲染**
```typescript
// 9. 计算总体利用率
const overallUtilization = totalWorkingTime > 0 ? (totalProductiveTime / totalWorkingTime) * 100 : 0;

// 10. 更新页面状态
setOverview(overviewData);
setDeviceRanking(rankingData);
```

### ⚡ 性能特征分析

#### **1. 请求数量**
- **设备数量**：47台设备
- **API请求**：94次（每设备2次，可能重复渲染）
- **并发处理**：所有请求并行发起

#### **2. 数据传输**
- **单设备数据**：状态历史记录 + 利用率统计
- **总数据量**：47台设备的完整状态历史
- **网络开销**：较高（每设备独立请求）

#### **3. 计算复杂度**
- **前端计算**：每台设备独立计算利用率
- **状态映射**：前端进行状态标准化
- **工作时间**：每次都重新计算

### 🔧 优化建议

#### **1. 请求优化**
```typescript
// 建议：批量获取设备状态
const response = await fetch(`/api/devices/batch-status-detail?date=${date}&devices=${deviceIds.join(',')}`);
```

#### **2. 缓存优化**
```typescript
// 建议：添加前端缓存
const cacheKey = `device-utilization-${date}`;
const cachedData = localStorage.getItem(cacheKey);
```

#### **3. 计算优化**
```go
// 建议：后端批量计算
func (h *UtilizationHandler) GetBatchDeviceUtilization(c *gin.Context) {
  // 一次性计算所有设备利用率
  utilizations := h.calculateDeviceUtilizationsParallel(devices, date, workingHours, workTimeSettings, isRealtime)
}
```

### ✨ 总结

#### **1. 架构特点**
- **分层设计**：前端 → Next.js代理 → 后端API → 数据服务 → 数据源
- **智能路由**：根据日期自动选择InfluxDB或MongoDB
- **实时计算**：前端基于状态历史实时计算利用率
- **并行处理**：支持多设备并发查询

#### **2. 数据一致性**
- **状态映射**：前后端使用一致的状态映射逻辑
- **工作时间**：统一的工作时间计算方法
- **利用率公式**：前后端使用相同的计算公式

#### **3. 性能考虑**
- **优势**：实时性强，数据准确
- **劣势**：请求数量多，计算开销大
- **改进空间**：批量请求、缓存优化、后端聚合

这个分析揭示了一个完整的设备利用率统计系统，从前端用户界面到后端数据存储的全链路数据流，为系统优化和维护提供了详细的技术参考。