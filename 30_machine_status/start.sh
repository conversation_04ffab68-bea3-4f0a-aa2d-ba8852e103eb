#!/bin/bash

# 添加 Node.js 和相关工具到 PATH
export PATH="/opt/homebrew/bin:$PATH"

# 显示版本信息以确认环境已正确设置
echo "Node.js 环境已设置:"
echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"
echo "yarn 版本: $(yarn --version)"
echo "pnpm 版本: $(pnpm --version)"

# 如果这是一个交互式 shell，保持环境变量
if [[ $- == *i* ]]; then
  echo "环境变量已设置，可以使用 node、npm、yarn 和 pnpm 命令"
else
  echo "请使用 'source setup-node-env.sh' 来设置环境变量"
fi
# 启动设备状态监控系统（前端和后端）

# 设置终端颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}启动设备状态监控系统...${NC}"

# 启动后端
echo -e "${BLUE}启动后端 API 服务...${NC}"
cd backend
source .venv/bin/activate
python run.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "等待后端服务启动..."
sleep 3

# 启动前端
echo -e "${BLUE}启动前端服务...${NC}"
npm run dev &
FRONTEND_PID=$!

# 捕获 CTRL+C 信号
trap "echo -e '${GREEN}正在关闭服务...${NC}'; kill $BACKEND_PID; kill $FRONTEND_PID; exit" INT

# 从环境变量读取配置
API_URL=${NEXT_PUBLIC_API_BASE_URL:-"http://localhost:9005"}
FRONTEND_PORT=${NEXT_PUBLIC_APP_PORT:-9100}

# 保持脚本运行
echo -e "${GREEN}服务已启动！${NC}"
echo "后端 API: $API_URL"
echo "前端应用: http://localhost:$FRONTEND_PORT"
echo "按 CTRL+C 停止所有服务"

wait
