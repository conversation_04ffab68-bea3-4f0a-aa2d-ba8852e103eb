#!/bin/bash

# ================================
# 30_machine_status 部署脚本
# ================================
# 此脚本用于在不同环境中部署 30_machine_status 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  local       本地开发环境"
    echo "  docker      Docker 容器环境"
    echo "  production  生产环境"
    echo ""
    echo "选项:"
    echo "  --api-url URL    指定 API 基础 URL"
    echo "  --port PORT      指定应用端口 (默认: 9100)"
    echo "  --build          重新构建镜像"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 local"
    echo "  $0 docker --build"
    echo "  $0 production --api-url /api"
    echo "  $0 production --api-url http://api.example.com"
}

# 检测当前主机IP
detect_host_ip() {
    # 尝试多种方法获取主机IP
    local ip=""
    
    # 方法1: 通过默认路由
    ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    
    # 方法2: 通过hostname
    if [ -z "$ip" ]; then
        ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi
    
    # 方法3: 通过ifconfig
    if [ -z "$ip" ]; then
        ip=$(ifconfig 2>/dev/null | grep -oP 'inet \K192\.168\.\d+\.\d+' | head -1)
    fi
    
    # 默认回退
    if [ -z "$ip" ]; then
        ip="localhost"
    fi
    
    echo "$ip"
}

# 生成环境配置
generate_env_config() {
    local env_type="$1"
    local api_url="$2"
    local port="$3"
    local config_file=".env.deploy"
    
    log_info "生成 $env_type 环境配置..."
    
    case "$env_type" in
        "local")
            cat > "$config_file" << EOF
# 本地开发环境配置
NEXT_PUBLIC_API_BASE_URL=${api_url:-http://localhost:9005}
NEXT_PUBLIC_APP_PORT=${port:-9100}
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_DEBUG_API=true
EOF
            ;;
        "docker")
            cat > "$config_file" << EOF
# Docker 容器环境配置
NEXT_PUBLIC_API_BASE_URL=${api_url:-http://mdc_server_api:9005}
NEXT_PUBLIC_APP_PORT=${port:-9100}
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_DEBUG_API=false
EOF
            ;;
        "production")
            local host_ip=$(detect_host_ip)
            cat > "$config_file" << EOF
# 生产环境配置
NEXT_PUBLIC_API_BASE_URL=${api_url:-/api}
NEXT_PUBLIC_APP_PORT=${port:-9100}
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_DEBUG_API=false
NEXT_PUBLIC_API_TIMEOUT=15000
NEXT_PUBLIC_API_RETRY_COUNT=3

# 检测到的主机信息
# 主机IP: $host_ip
# 如果需要直接访问API，可以使用: http://$host_ip:9005
EOF
            ;;
    esac
    
    log_success "配置文件已生成: $config_file"
}

# 部署到本地环境
deploy_local() {
    local api_url="$1"
    local port="$2"
    
    log_info "部署到本地开发环境..."
    
    generate_env_config "local" "$api_url" "$port"
    
    log_info "启动开发服务器..."
    npm run dev
}

# 部署到Docker环境
deploy_docker() {
    local api_url="$1"
    local port="$2"
    local build_flag="$3"
    
    log_info "部署到Docker环境..."
    
    generate_env_config "docker" "$api_url" "$port"
    
    if [ "$build_flag" = "true" ]; then
        log_info "重新构建Docker镜像..."
        ./docker_build_mdc_dashboard.sh
    fi
    
    log_info "启动Docker服务..."
    docker-compose --env-file .env.deploy up -d
    
    log_success "Docker服务已启动"
    log_info "访问地址: http://localhost:${port:-9100}"
}

# 部署到生产环境
deploy_production() {
    local api_url="$1"
    local port="$2"
    local build_flag="$3"
    
    log_info "部署到生产环境..."
    
    generate_env_config "production" "$api_url" "$port"
    
    if [ "$build_flag" = "true" ]; then
        log_info "构建生产版本..."
        npm run build
    fi
    
    log_info "生成Nginx配置建议..."
    cat > nginx.conf.example << EOF
# Nginx 配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端应用
    location / {
        proxy_pass http://localhost:${port:-9100};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:9005/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    log_success "生产环境配置完成"
    log_info "Nginx配置示例已生成: nginx.conf.example"
    log_warning "请根据实际情况调整Nginx配置"
}

# 主函数
main() {
    local env_type=""
    local api_url=""
    local port=""
    local build_flag="false"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            local|docker|production)
                env_type="$1"
                shift
                ;;
            --api-url)
                api_url="$2"
                shift 2
                ;;
            --port)
                port="$2"
                shift 2
                ;;
            --build)
                build_flag="true"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [ -z "$env_type" ]; then
        log_error "请指定部署环境"
        show_help
        exit 1
    fi
    
    log_info "开始部署到 $env_type 环境..."
    
    case "$env_type" in
        "local")
            deploy_local "$api_url" "$port"
            ;;
        "docker")
            deploy_docker "$api_url" "$port" "$build_flag"
            ;;
        "production")
            deploy_production "$api_url" "$port" "$build_flag"
            ;;
    esac
}

# 执行主函数
main "$@"
