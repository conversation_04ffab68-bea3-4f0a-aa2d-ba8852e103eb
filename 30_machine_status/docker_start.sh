#!/bin/bash

# ================================
# 30_machine_status Docker 启动脚本
# ================================
# 此脚本用于在 Docker 环境中启动 30_machine_status 服务
# 并确保与 12_server_api 的正确连接

set -e  # 遇到错误时停止执行

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否运行
check_docker() {
    log_info "检查 Docker 服务状态..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    log_success "Docker 服务正常运行"
}

# 检查网络是否存在
check_network() {
    log_info "检查 Docker 网络 mdc_full_mdc_network..."
    if ! docker network ls | grep -q "mdc_full_mdc_network"; then
        log_warning "网络 mdc_full_mdc_network 不存在，正在创建..."
        docker network create mdc_full_mdc_network
        log_success "网络 mdc_full_mdc_network 创建成功"
    else
        log_success "网络 mdc_full_mdc_network 已存在"
    fi
}

# 检查 12_server_api 是否运行
check_api_service() {
    log_info "检查 12_server_api 服务状态..."
    if ! docker ps | grep -q "mdc_server_api"; then
        log_warning "12_server_api 服务未运行"
        log_info "尝试启动 12_server_api 服务..."
        
        # 检查 12_server_api 目录是否存在
        if [ -d "../12_server_api" ]; then
            cd ../12_server_api
            if [ -f "docker-compose.yml" ]; then
                docker-compose up -d
                cd - > /dev/null
                log_success "12_server_api 服务启动成功"
            else
                log_error "12_server_api/docker-compose.yml 文件不存在"
                exit 1
            fi
        else
            log_error "12_server_api 目录不存在，请确保项目结构正确"
            exit 1
        fi
    else
        log_success "12_server_api 服务正在运行"
    fi
}

# 构建镜像
build_image() {
    log_info "构建 30_machine_status Docker 镜像..."
    if [ -f "Dockerfile" ]; then
        docker build -t mdc_dashboard:1.1.13 .
        log_success "镜像构建完成"
    else
        log_error "Dockerfile 不存在"
        exit 1
    fi
}

# 启动服务
start_service() {
    log_info "启动 30_machine_status 服务..."
    
    # 停止现有容器（如果存在）
    if docker ps -a | grep -q "mdc_dashboard"; then
        log_info "停止现有的 mdc_dashboard 容器..."
        docker stop mdc_dashboard >/dev/null 2>&1 || true
        docker rm mdc_dashboard >/dev/null 2>&1 || true
    fi
    
    # 使用 Docker 环境配置启动服务
    docker-compose --env-file .env.docker up -d
    log_success "30_machine_status 服务启动成功"
}

# 验证服务连接
verify_connection() {
    log_info "验证服务连接..."
    
    # 等待服务启动
    sleep 5
    
    # 检查容器是否正在运行
    if docker ps | grep -q "mdc_dashboard"; then
        log_success "mdc_dashboard 容器正在运行"
    else
        log_error "mdc_dashboard 容器启动失败"
        docker logs mdc_dashboard
        exit 1
    fi
    
    # 测试容器间网络连接
    log_info "测试容器间网络连接..."
    if docker exec mdc_dashboard curl -f http://mdc_server_api:9005/health >/dev/null 2>&1; then
        log_success "容器间网络连接正常"
    else
        log_warning "容器间网络连接测试失败，但服务可能仍然正常工作"
    fi
}

# 显示服务信息
show_service_info() {
    echo ""
    log_info "服务信息："
    echo "  - 前端访问地址: http://localhost:9100"
    echo "  - API 服务地址: http://localhost:9005"
    echo "  - 容器名称: mdc_dashboard"
    echo "  - 网络名称: mdc_full_mdc_network"
    echo ""
    log_info "常用命令："
    echo "  - 查看日志: docker logs -f mdc_dashboard"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    echo "  - 进入容器: docker exec -it mdc_dashboard sh"
    echo ""
}

# 主函数
main() {
    log_info "开始启动 30_machine_status Docker 服务..."
    echo ""
    
    check_docker
    check_network
    check_api_service
    build_image
    start_service
    verify_connection
    show_service_info
    
    log_success "30_machine_status 服务启动完成！"
}

# 执行主函数
main "$@"
