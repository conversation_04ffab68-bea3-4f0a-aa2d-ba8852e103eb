#!/bin/bash

# ================================
# API 路由更新脚本
# ================================
# 此脚本用于批量更新所有 API 路由文件，使其使用智能 API 配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 查找所有需要更新的 API 路由文件
find_api_routes() {
    log_info "查找需要更新的 API 路由文件..."
    
    # 查找所有包含 process.env.NEXT_PUBLIC_API_BASE_URL 的文件
    local files=$(grep -r "process.env.NEXT_PUBLIC_API_BASE_URL" src/app/api/ --include="*.ts" --include="*.tsx" -l 2>/dev/null || true)
    
    if [ -z "$files" ]; then
        log_warning "未找到需要更新的文件"
        return 1
    fi
    
    echo "$files"
}

# 检查文件是否已经更新
is_file_updated() {
    local file="$1"
    
    # 检查是否已经导入了 getBackendApiUrl
    if grep -q "getBackendApiUrl" "$file"; then
        return 0  # 已更新
    else
        return 1  # 未更新
    fi
}

# 更新单个文件
update_single_file() {
    local file="$1"
    
    log_info "更新文件: $file"
    
    # 检查文件是否已经更新
    if is_file_updated "$file"; then
        log_warning "文件已经更新，跳过: $file"
        return 0
    fi
    
    # 创建备份
    cp "$file" "$file.backup"
    
    # 计算相对路径深度
    local depth=$(echo "$file" | sed 's|src/app/api/||' | tr -cd '/' | wc -c)
    local relative_path=""
    for ((i=0; i<depth; i++)); do
        relative_path="../$relative_path"
    done
    relative_path="${relative_path}../../config/api"
    
    # 更新导入语句
    if grep -q "import.*NextResponse.*from 'next/server'" "$file"; then
        # 在 NextResponse 导入后添加 API 配置导入
        sed -i.tmp "/import.*NextResponse.*from 'next\/server'/a\\
import { getBackendApiUrl } from '$relative_path';" "$file"
    else
        # 在文件开头添加导入
        sed -i.tmp "1i\\
import { getBackendApiUrl } from '$relative_path';" "$file"
    fi
    
    # 添加智能 API 配置函数
    sed -i.tmp "/const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL/a\\
\\
// 使用智能API配置，支持多环境部署\\
const getApiBaseUrl = () => getBackendApiUrl();" "$file"
    
    # 替换所有使用 API_BASE_URL 的地方
    sed -i.tmp 's/\${API_BASE_URL}/\${getApiBaseUrl()}/g' "$file"
    sed -i.tmp 's/API_BASE_URL/getApiBaseUrl()/g' "$file"
    
    # 清理临时文件
    rm -f "$file.tmp"
    
    log_success "文件更新完成: $file"
}

# 验证更新结果
verify_updates() {
    log_info "验证更新结果..."
    
    local files=$(find_api_routes)
    local updated_count=0
    local total_count=0
    
    for file in $files; do
        total_count=$((total_count + 1))
        if is_file_updated "$file"; then
            updated_count=$((updated_count + 1))
            log_success "✓ $file"
        else
            log_error "✗ $file"
        fi
    done
    
    log_info "更新统计: $updated_count/$total_count 文件已更新"
    
    if [ $updated_count -eq $total_count ]; then
        log_success "所有文件更新完成！"
        return 0
    else
        log_error "部分文件更新失败"
        return 1
    fi
}

# 恢复备份
restore_backups() {
    log_warning "恢复备份文件..."
    
    find src/app/api/ -name "*.backup" | while read backup_file; do
        original_file="${backup_file%.backup}"
        mv "$backup_file" "$original_file"
        log_info "恢复: $original_file"
    done
}

# 清理备份文件
cleanup_backups() {
    log_info "清理备份文件..."
    
    find src/app/api/ -name "*.backup" -delete
    log_success "备份文件清理完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --dry-run    仅显示需要更新的文件，不执行更新"
    echo "  --restore    恢复备份文件"
    echo "  --cleanup    清理备份文件"
    echo "  --verify     验证更新结果"
    echo "  --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0           执行更新"
    echo "  $0 --dry-run 查看需要更新的文件"
    echo "  $0 --verify  验证更新结果"
}

# 主函数
main() {
    local action="update"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                action="dry-run"
                shift
                ;;
            --restore)
                action="restore"
                shift
                ;;
            --cleanup)
                action="cleanup"
                shift
                ;;
            --verify)
                action="verify"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    case "$action" in
        "dry-run")
            log_info "查看需要更新的文件（干运行模式）..."
            local files=$(find_api_routes)
            if [ $? -eq 0 ]; then
                echo "$files"
            fi
            ;;
        "restore")
            restore_backups
            ;;
        "cleanup")
            cleanup_backups
            ;;
        "verify")
            verify_updates
            ;;
        "update")
            log_info "开始更新 API 路由文件..."
            
            local files=$(find_api_routes)
            if [ $? -ne 0 ]; then
                exit 1
            fi
            
            for file in $files; do
                update_single_file "$file"
            done
            
            verify_updates
            
            if [ $? -eq 0 ]; then
                log_success "所有 API 路由文件更新完成！"
                log_info "运行 'npm run build' 验证构建是否成功"
            else
                log_error "更新过程中出现错误"
                log_info "运行 '$0 --restore' 恢复备份文件"
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
