#!/usr/bin/env python3
"""
批量更新 API 路由文件脚本
用于将所有 API 路由文件更新为使用智能 API 配置
"""

import os
import re
import glob
from pathlib import Path

def calculate_relative_path(file_path):
    """计算从 API 文件到 config/api.ts 的相对路径"""
    # 计算从 src/app/api/... 到 src/config/api.ts 的相对路径
    parts = file_path.replace('src/app/api/', '').split('/')
    depth = len([p for p in parts if p]) - 1  # 减去文件名本身
    
    relative_path = '../' * (depth + 2) + 'config/api'
    return relative_path

def update_api_file(file_path):
    """更新单个 API 文件"""
    print(f"更新文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经更新过
    if 'getBackendApiUrl' in content:
        print(f"  跳过（已更新）: {file_path}")
        return True
    
    # 检查是否包含需要更新的内容
    if 'process.env.NEXT_PUBLIC_API_BASE_URL' not in content:
        print(f"  跳过（无需更新）: {file_path}")
        return True
    
    # 创建备份
    backup_path = file_path + '.backup'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 计算相对路径
    relative_path = calculate_relative_path(file_path)
    
    # 更新内容
    lines = content.split('\n')
    new_lines = []
    import_added = False
    api_config_added = False
    
    for i, line in enumerate(lines):
        # 添加导入语句
        if not import_added and "import { NextResponse }" in line:
            new_lines.append(line)
            new_lines.append(f"import {{ getBackendApiUrl }} from '{relative_path}';")
            import_added = True
            continue
        
        # 更新 API_BASE_URL 定义
        if 'const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL' in line:
            new_lines.append(line)
            new_lines.append("")
            new_lines.append("// 使用智能API配置，支持多环境部署")
            new_lines.append("const getApiBaseUrl = () => getBackendApiUrl();")
            api_config_added = True
            continue
        
        # 替换使用 API_BASE_URL 的地方
        if '${API_BASE_URL}' in line:
            line = line.replace('${API_BASE_URL}', '${getApiBaseUrl()}')
        elif 'API_BASE_URL' in line and 'const API_BASE_URL' not in line and 'process.env' not in line:
            line = line.replace('API_BASE_URL', 'getApiBaseUrl()')
        
        new_lines.append(line)
    
    # 如果没有找到合适的位置添加导入，在文件开头添加
    if not import_added:
        # 找到第一个 import 语句后添加
        for i, line in enumerate(new_lines):
            if line.startswith('import ') and 'next/server' in line:
                new_lines.insert(i + 1, f"import {{ getBackendApiUrl }} from '{relative_path}';")
                break
    
    # 写入更新后的内容
    updated_content = '\n'.join(new_lines)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"  ✓ 更新完成: {file_path}")
    return True

def find_api_files():
    """查找所有需要更新的 API 文件"""
    api_files = []
    
    # 查找所有 TypeScript 文件
    for pattern in ['src/app/api/**/*.ts', 'src/app/api/**/*.tsx']:
        files = glob.glob(pattern, recursive=True)
        api_files.extend(files)
    
    # 过滤出包含 process.env.NEXT_PUBLIC_API_BASE_URL 的文件
    filtered_files = []
    for file_path in api_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'process.env.NEXT_PUBLIC_API_BASE_URL' in content:
                    filtered_files.append(file_path)
        except Exception as e:
            print(f"读取文件失败: {file_path}, 错误: {e}")
    
    return filtered_files

def verify_updates():
    """验证更新结果"""
    print("\n验证更新结果...")
    
    api_files = find_api_files()
    updated_count = 0
    total_count = len(api_files)
    
    for file_path in api_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'getBackendApiUrl' in content:
                    updated_count += 1
                    print(f"  ✓ {file_path}")
                else:
                    print(f"  ✗ {file_path}")
        except Exception as e:
            print(f"  ✗ {file_path} (读取失败: {e})")
    
    print(f"\n更新统计: {updated_count}/{total_count} 文件已更新")
    
    if updated_count == total_count:
        print("✅ 所有文件更新完成！")
        return True
    else:
        print("❌ 部分文件更新失败")
        return False

def restore_backups():
    """恢复备份文件"""
    print("恢复备份文件...")
    
    backup_files = glob.glob('src/app/api/**/*.backup', recursive=True)
    for backup_file in backup_files:
        original_file = backup_file.replace('.backup', '')
        try:
            os.rename(backup_file, original_file)
            print(f"  恢复: {original_file}")
        except Exception as e:
            print(f"  恢复失败: {original_file}, 错误: {e}")

def cleanup_backups():
    """清理备份文件"""
    print("清理备份文件...")
    
    backup_files = glob.glob('src/app/api/**/*.backup', recursive=True)
    for backup_file in backup_files:
        try:
            os.remove(backup_file)
            print(f"  删除: {backup_file}")
        except Exception as e:
            print(f"  删除失败: {backup_file}, 错误: {e}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        action = sys.argv[1]
        if action == '--verify':
            verify_updates()
            return
        elif action == '--restore':
            restore_backups()
            return
        elif action == '--cleanup':
            cleanup_backups()
            return
        elif action == '--dry-run':
            files = find_api_files()
            print("需要更新的文件:")
            for file_path in files:
                print(f"  {file_path}")
            print(f"\n总计: {len(files)} 个文件")
            return
        elif action == '--help':
            print("用法: python3 batch_update_api.py [选项]")
            print("")
            print("选项:")
            print("  --dry-run    仅显示需要更新的文件")
            print("  --verify     验证更新结果")
            print("  --restore    恢复备份文件")
            print("  --cleanup    清理备份文件")
            print("  --help       显示此帮助信息")
            return
    
    # 执行更新
    print("🚀 开始批量更新 API 路由文件...")
    
    api_files = find_api_files()
    if not api_files:
        print("未找到需要更新的文件")
        return
    
    print(f"找到 {len(api_files)} 个需要更新的文件")
    
    success_count = 0
    for file_path in api_files:
        try:
            if update_api_file(file_path):
                success_count += 1
        except Exception as e:
            print(f"  ❌ 更新失败: {file_path}, 错误: {e}")
    
    print(f"\n更新完成: {success_count}/{len(api_files)} 文件成功更新")
    
    # 验证结果
    if verify_updates():
        print("\n🎉 所有 API 路由文件更新完成！")
        print("💡 建议运行 'npm run build' 验证构建是否成功")
    else:
        print("\n⚠️  更新过程中出现错误")
        print("💡 运行 'python3 batch_update_api.py --restore' 恢复备份文件")

if __name__ == '__main__':
    main()
