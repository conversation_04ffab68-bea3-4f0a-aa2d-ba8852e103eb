#!/bin/bash

# ================================
# Docker 环境快速配置脚本
# ================================
# 此脚本帮助快速配置 30_machine_status 的 Docker 环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "================================"
    echo "30_machine_status Docker 环境配置"
    echo "================================"
    echo ""
    log_info "此脚本将帮助您配置 Docker 环境以实现与 12_server_api 的正确通信"
    echo ""
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 创建网络
create_network() {
    log_info "创建 Docker 网络..."
    
    if docker network ls | grep -q "mdc_full_mdc_network"; then
        log_success "网络 mdc_full_mdc_network 已存在"
    else
        docker network create mdc_full_mdc_network
        log_success "网络 mdc_full_mdc_network 创建成功"
    fi
}

# 配置环境变量
configure_env() {
    log_info "配置环境变量..."
    
    # 检查是否存在 .env.local
    if [ -f ".env.local" ]; then
        log_warning ".env.local 文件已存在"
        read -p "是否覆盖现有配置？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过环境变量配置"
            return
        fi
    fi
    
    # 创建 .env.local 文件
    cat > .env.local << EOF
# 30_machine_status Docker 环境配置
# 自动生成于 $(date)

# Docker 容器间通信配置
NEXT_PUBLIC_API_BASE_URL=http://mdc_server_api:9005

# 应用配置
NEXT_PUBLIC_APP_PORT=9100
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# 调试配置
NEXT_PUBLIC_DEBUG_API=false
NEXT_PUBLIC_API_TIMEOUT=10000
NEXT_PUBLIC_API_RETRY_COUNT=3
EOF
    
    log_success "环境变量配置完成 (.env.local)"
}

# 验证 12_server_api
check_api_service() {
    log_info "检查 12_server_api 服务..."
    
    if docker ps | grep -q "mdc_server_api"; then
        log_success "12_server_api 服务正在运行"
        return 0
    fi
    
    log_warning "12_server_api 服务未运行"
    
    # 检查是否存在 12_server_api 目录
    if [ -d "../12_server_api" ]; then
        read -p "是否启动 12_server_api 服务？(Y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Nn]$ ]]; then
            log_warning "请手动启动 12_server_api 服务"
            return 1
        fi
        
        log_info "启动 12_server_api 服务..."
        cd ../12_server_api
        if [ -f "docker-compose.yml" ]; then
            docker-compose up -d
            cd - > /dev/null
            log_success "12_server_api 服务启动成功"
        else
            log_error "12_server_api/docker-compose.yml 不存在"
            cd - > /dev/null
            return 1
        fi
    else
        log_error "12_server_api 目录不存在"
        return 1
    fi
}

# 构建镜像
build_image() {
    log_info "构建 Docker 镜像..."
    
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile 不存在"
        return 1
    fi
    
    docker build -t mdc_dashboard:1.1.13 .
    log_success "镜像构建完成"
}

# 显示配置摘要
show_summary() {
    echo ""
    echo "================================"
    log_success "配置完成！"
    echo "================================"
    echo ""
    
    log_info "配置摘要："
    echo "  ✓ Docker 网络: mdc_full_mdc_network"
    echo "  ✓ 环境变量: .env.local"
    echo "  ✓ API URL: http://mdc_server_api:9005"
    echo "  ✓ 应用端口: 9100"
    echo ""
    
    log_info "下一步操作："
    echo "  1. 启动服务: ./docker_start.sh"
    echo "  2. 测试连接: ./test_docker_connection.sh"
    echo "  3. 访问应用: http://localhost:9100"
    echo ""
    
    log_info "常用命令："
    echo "  - 查看日志: docker logs -f mdc_dashboard"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    echo ""
}

# 询问是否立即启动
ask_start_service() {
    read -p "是否立即启动服务？(Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        log_info "启动服务..."
        if [ -x "./docker_start.sh" ]; then
            ./docker_start.sh
        else
            log_warning "docker_start.sh 不存在或不可执行，使用 docker-compose 启动"
            docker-compose --env-file .env.local up -d
        fi
    fi
}

# 主函数
main() {
    show_welcome
    check_prerequisites
    echo ""
    
    create_network
    echo ""
    
    configure_env
    echo ""
    
    check_api_service
    echo ""
    
    build_image
    echo ""
    
    show_summary
    ask_start_service
}

# 执行主函数
main "$@"
