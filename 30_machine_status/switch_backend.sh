#!/bin/bash

# 后端服务快速切换脚本
# Backend Service Quick Switch Script

echo "🔄 30_machine_status 后端服务切换工具"
echo "Backend Service Switch Tool"
echo "==========================================="

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查 .env 文件
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ 错误: .env 文件不存在${NC}"
    echo -e "${YELLOW}💡 正在从 .env.example 创建 .env 文件...${NC}"
    
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo -e "${GREEN}✅ .env 文件创建成功${NC}"
    else
        echo -e "${RED}❌ 错误: .env.example 文件也不存在${NC}"
        exit 1
    fi
fi

# 显示当前配置
echo ""
echo -e "${BLUE}📊 当前配置${NC}"
echo "Current Configuration"
echo "--------------------"

# 读取当前配置
CURRENT_API_BASE_URL=$(grep "^NEXT_PUBLIC_API_BASE_URL=" .env | cut -d'=' -f2)

echo "NEXT_PUBLIC_API_BASE_URL: ${CURRENT_API_BASE_URL:-未设置}"

# 检测当前使用的后端
if [[ "$CURRENT_API_BASE_URL" == *":9005"* ]]; then
    CURRENT_BACKEND="Go (12_server_api)"
elif [[ "$CURRENT_API_BASE_URL" == *":9980"* ]]; then
    CURRENT_BACKEND="Python (31_machine_backend)"
else
    CURRENT_BACKEND="自定义"
fi

echo -e "当前后端: ${CYAN}$CURRENT_BACKEND${NC}"

# 显示选项菜单
echo ""
echo -e "${BLUE}🔧 选择要切换的后端服务${NC}"
echo "Select Backend Service to Switch"
echo "--------------------------------"
echo "1) Go 后端 (12_server_api) - 端口 9005 [推荐]"
echo "2) Python 后端 (31_machine_backend) - 端口 9980"
echo "3) 自定义后端地址"
echo "4) 显示当前配置"
echo "5) 验证配置"
echo "6) 退出"

echo ""
read -p "请选择 (1-6): " choice

case $choice in
    1)
        echo -e "${YELLOW}🔄 切换到 Go 后端 (12_server_api)...${NC}"

        # 更新 .env 文件
        sed -i.bak 's|^NEXT_PUBLIC_API_BASE_URL=.*|NEXT_PUBLIC_API_BASE_URL=http://localhost:9005|' .env

        echo -e "${GREEN}✅ 已切换到 Go 后端${NC}"
        echo "NEXT_PUBLIC_API_BASE_URL=http://localhost:9005"
        ;;

    2)
        echo -e "${YELLOW}🔄 切换到 Python 后端 (31_machine_backend)...${NC}"

        # 更新 .env 文件
        sed -i.bak 's|^NEXT_PUBLIC_API_BASE_URL=.*|NEXT_PUBLIC_API_BASE_URL=http://localhost:9980|' .env

        echo -e "${GREEN}✅ 已切换到 Python 后端${NC}"
        echo "NEXT_PUBLIC_API_BASE_URL=http://localhost:9980"
        ;;
        
    3)
        echo -e "${YELLOW}🔧 自定义后端地址${NC}"
        echo ""
        echo "请输入后端API地址 (例如: http://localhost:8080 或 https://api.example.com):"
        read -p "API地址: " custom_url
        
        # 验证URL格式
        if [[ ! "$custom_url" =~ ^https?://[^/]+$ ]]; then
            echo -e "${RED}❌ 错误: URL格式无效${NC}"
            echo "正确格式: http://domain:port 或 https://domain:port"
            exit 1
        fi
        
        echo -e "${YELLOW}🔄 设置自定义后端地址...${NC}"

        # 更新 .env 文件
        sed -i.bak "s|^NEXT_PUBLIC_API_BASE_URL=.*|NEXT_PUBLIC_API_BASE_URL=$custom_url|" .env

        echo -e "${GREEN}✅ 已设置自定义后端地址${NC}"
        echo "NEXT_PUBLIC_API_BASE_URL=$custom_url"
        ;;
        
    4)
        echo -e "${BLUE}📊 当前完整配置${NC}"
        echo "Current Full Configuration"
        echo "-------------------------"
        cat .env | grep -v '^#' | grep -v '^$'
        ;;
        
    5)
        echo -e "${BLUE}🔍 验证配置...${NC}"
        if [ -f "validate_env_config.sh" ]; then
            chmod +x validate_env_config.sh
            ./validate_env_config.sh
        else
            echo -e "${RED}❌ 验证脚本不存在${NC}"
        fi
        ;;
        
    6)
        echo -e "${BLUE}👋 退出${NC}"
        exit 0
        ;;
        
    *)
        echo -e "${RED}❌ 无效选择${NC}"
        exit 1
        ;;
esac

# 清理备份文件
rm -f .env.bak

# 测试连接
echo ""
echo -e "${BLUE}🌐 测试后端连接${NC}"
echo "Testing Backend Connection"
echo "--------------------------"

NEW_API_URL=$(grep "^NEXT_PUBLIC_API_BASE_URL=" .env | cut -d'=' -f2)

if command -v curl >/dev/null 2>&1; then
    echo "正在测试连接到: $NEW_API_URL"
    
    if curl -s --connect-timeout 5 "$NEW_API_URL/api/v3/info" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 后端连接成功${NC}"
    else
        echo -e "${RED}❌ 后端连接失败${NC}"
        echo -e "${YELLOW}💡 请确保后端服务正在运行:${NC}"
        
        if [[ "$NEW_API_URL" == *":9005"* ]]; then
            echo "   cd ../12_server_api && ./run.sh"
        elif [[ "$NEW_API_URL" == *":9980"* ]]; then
            echo "   cd ../31_machine_backend && ./run.sh"
        else
            echo "   请检查自定义后端服务是否运行"
        fi
    fi
else
    echo -e "${YELLOW}⚠️  curl 未安装，跳过连接测试${NC}"
fi

# 提示重启应用
echo ""
echo -e "${BLUE}🔄 下一步操作${NC}"
echo "Next Steps"
echo "----------"
echo -e "${YELLOW}⚠️  重要: 修改环境变量后需要重启应用${NC}"
echo ""
echo "重启开发服务器:"
echo "  npm run dev"
echo ""
echo "或使用运行脚本:"
echo "  ./run.sh"
echo ""
echo "Docker 部署:"
echo "  docker-compose down && docker-compose up -d"

echo ""
echo -e "${GREEN}🎉 后端切换完成！${NC}"
