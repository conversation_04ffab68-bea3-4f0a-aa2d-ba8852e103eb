Create detailed components with these requirements:
1. Use 'use client' directive for client-side components
2. Style with Tailwind CSS utility classes for responsive design
3. Use Lucide React for icons (from lucide-react package). Do NOT use other UI libraries unless requested
4. Use stock photos from picsum.photos where appropriate, only valid URLs you know exist
5. Configure next.config.js image remotePatterns to enable stock photos from picsum.photos
6. Create root layout.tsx page that wraps necessary navigation items to all pages
7. MUST implement the navigation elements items in their rightful place i.e. Left sidebar, Top header
8. Accurately implement necessary grid layouts
9. Follow proper import practices:
   - Use @/ path aliases
   - Keep component imports organized
   - Update current src/app/page.tsx with new comprehensive code
   - Don't forget root route (page.tsx) handling
   - You MUST complete the entire prompt before stopping

Manufacturing Production Dashboard monitor with Real-time Machine status
</summary_title>

<image_analysis>

1. Navigation Elements:
- Top bar with: Utilization Rate and Completion Rate gauges
- Timeline navigation bar with 24-hour intervals (08:00-08:00)
- Status indicators with: Completed, New, In Production, Paused, Finished, Cancelled, Closed


2. Layout Components:
- Header section: 150px height with dual gauge displays
- Timeline: 80px height with hour markers
- List items: ~60px height per production record
- Full width container (100% viewport)


3. Content Sections:
- Gauge meters section (2 columns)
- Timeline navigation bar
- Production records list with columns for:
  - Item number (#11-#21)
  - Product code
  - Product name
  - Production order
  - Completion status
  - Production location


4. Interactive Controls:
- Gauge meters with real-time updates
- Color-coded status indicators
- Timeline scrubber/navigation
- Sortable list items


5. Colors:
- Background: #2A2A2A
- Green (Active/Complete): #2E8B57
- Blue (In Progress): #4169E1
- Red (Warning): #FF0000
- Gray (Inactive): #999005
- White (Text): #FFFFFF


6. Grid/Layout Structure:
- 2-column header layout
- Full-width timeline
- List view with fixed columns
- Responsive scaling for different screen sizes
</image_analysis>

<development_planning>

1. Project Structure:
```
src/
├── components/
│   ├── layout/
│   │   ├── Header
│   │   ├── Timeline
│   │   └── ProductionList
│   ├── features/
│   │   ├── GaugeMeter
│   │   ├── StatusIndicator
│   │   └── ProductionItem
│   └── shared/
├── assets/
├── styles/
├── hooks/
└── utils/
```


2. Key Features:
- Real-time data updates
- Progress tracking
- Status management
- Timeline navigation
- Production metrics calculation


3. State Management:
```typescript
interface AppState {
├── production: {
│   ├── items: ProductionItem[]
│   ├── currentStatus: Status
│   ├── completionRate: number
│   └── utilizationRate: number
├── timeline: {
│   ├── currentTime: string
│   ├── viewRange: TimeRange
│   └── markers: TimeMarker[]
├── }
}
```


4. Routes:
```typescript
const routes = [
├── '/dashboard',
├── '/production/*',
├── '/reports/*',
└── '/settings/*'
]
```


5. Component Architecture:
- DashboardContainer
- GaugeMeterComponent
- TimelineNavigator
- ProductionListView
- StatusIndicatorComponent


6. Responsive Breakpoints:
```scss
$breakpoints: (
├── 'sm': 576px,
├── 'md': 768px,
├── 'lg': 992px,
└── 'xl': 1200px
);
```
</development_planning>