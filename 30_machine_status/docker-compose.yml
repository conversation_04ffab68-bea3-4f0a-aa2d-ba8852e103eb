services:
  mdc_dashboard:
    image: mdc_dashboard:1.1.17
    container_name: mdc_dashboard
    ports:
      - "${NEXT_PUBLIC_APP_PORT:-9100}:${NEXT_PUBLIC_APP_PORT:-9100}"
    restart: always
    environment:
      # 基础配置
      NODE_ENV: ${NODE_ENV:-production}
      NEXT_TELEMETRY_DISABLED: ${NEXT_TELEMETRY_DISABLED:-1}
      PORT: ${NEXT_PUBLIC_APP_PORT:-9100}
      HOSTNAME: 0.0.0.0

      # 统一的API配置 - 使用环境变量，支持多环境部署
      # 本地开发: http://mdc_server_api:9005
      # 生产环境: /api (通过反向代理)
      NEXT_PUBLIC_API_BASE_URL: ${NEXT_PUBLIC_API_BASE_URL:-/api}

      # 调试配置
      NEXT_PUBLIC_DEBUG_API: ${NEXT_PUBLIC_DEBUG_API:-false}
      NEXT_PUBLIC_API_TIMEOUT: ${NEXT_PUBLIC_API_TIMEOUT:-10000}
      NEXT_PUBLIC_API_RETRY_COUNT: ${NEXT_PUBLIC_API_RETRY_COUNT:-3}

    # 网络配置
    networks:
      - mdc_full_mdc_network

    # 如果需要挂载配置文件，可以取消下面的注释
    # volumes:
    #   - ./config:/app/config
    #   - ./data:/app/data
    #   - ./.env:/app/.env:ro  # 挂载环境变量文件（只读）

# 网络配置
networks:
  mdc_full_mdc_network:
    external: true
