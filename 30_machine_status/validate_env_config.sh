#!/bin/bash

# 环境变量配置验证脚本
# Environment Variables Configuration Validation Script

echo "🔍 30_machine_status 环境变量配置验证"
echo "Environment Variables Configuration Validation"
echo "=============================================="

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 验证函数
validate_check() {
    local description="$1"
    local condition="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$condition"; then
        echo -e "✅ ${GREEN}PASS${NC}: $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "❌ ${RED}FAIL${NC}: $description"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 检查 .env 文件
echo ""
echo -e "${BLUE}📋 检查配置文件${NC}"
echo "Checking configuration files"
echo "----------------------------"

validate_check ".env 文件存在" "[ -f '.env' ]"
validate_check ".env.example 文件存在" "[ -f '.env.example' ]"

# 加载环境变量
if [ -f ".env" ]; then
    echo -e "${YELLOW}📄 加载 .env 文件...${NC}"
    export $(grep -v '^#' .env | grep -v '^$' | xargs)
else
    echo -e "${RED}⚠️  警告: .env 文件不存在，使用默认值${NC}"
fi

# 检查核心环境变量
echo ""
echo -e "${BLUE}🔧 检查核心环境变量${NC}"
echo "Checking core environment variables"
echo "-----------------------------------"

validate_check "NEXT_PUBLIC_API_BASE_URL 已设置" "[ ! -z '$NEXT_PUBLIC_API_BASE_URL' ]"
validate_check "NEXT_PUBLIC_APP_PORT 已设置" "[ ! -z '$NEXT_PUBLIC_APP_PORT' ]"

# 检查环境变量值的有效性
echo ""
echo -e "${BLUE}🌐 检查API地址有效性${NC}"
echo "Checking API URL validity"
echo "-------------------------"

# 检查NEXT_PUBLIC_API_BASE_URL格式
if [[ "$NEXT_PUBLIC_API_BASE_URL" =~ ^https?://[^/]+$ ]]; then
    validate_check "NEXT_PUBLIC_API_BASE_URL 格式正确" "true"
else
    validate_check "NEXT_PUBLIC_API_BASE_URL 格式正确" "false"
fi

# 检查端口号
if [[ "$NEXT_PUBLIC_APP_PORT" =~ ^[0-9]+$ ]] && [ "$NEXT_PUBLIC_APP_PORT" -ge 1024 ] && [ "$NEXT_PUBLIC_APP_PORT" -le 65535 ]; then
    validate_check "NEXT_PUBLIC_APP_PORT 端口号有效" "true"
else
    validate_check "NEXT_PUBLIC_APP_PORT 端口号有效" "false"
fi

# 统一API配置检查
echo ""
echo -e "${BLUE}🔄 检查统一API配置${NC}"
echo "Checking unified API configuration"
echo "----------------------------------"

validate_check "使用统一的API配置" "[ ! -z '$NEXT_PUBLIC_API_BASE_URL' ]"

# 显示当前配置
echo ""
echo -e "${BLUE}📊 当前环境变量配置${NC}"
echo "Current environment variables configuration"
echo "-------------------------------------------"

echo "NEXT_PUBLIC_API_BASE_URL: ${NEXT_PUBLIC_API_BASE_URL:-未设置}"
echo "NEXT_PUBLIC_APP_PORT: ${NEXT_PUBLIC_APP_PORT:-未设置}"
echo "NODE_ENV: ${NODE_ENV:-未设置}"
echo "NEXT_TELEMETRY_DISABLED: ${NEXT_TELEMETRY_DISABLED:-未设置}"
echo "NEXT_PUBLIC_DEBUG_API: ${NEXT_PUBLIC_DEBUG_API:-未设置}"
echo "NEXT_PUBLIC_API_TIMEOUT: ${NEXT_PUBLIC_API_TIMEOUT:-未设置}"
echo "NEXT_PUBLIC_API_RETRY_COUNT: ${NEXT_PUBLIC_API_RETRY_COUNT:-未设置}"

# 检查代码中的硬编码地址
echo ""
echo -e "${BLUE}🔍 检查代码中的硬编码地址${NC}"
echo "Checking for hardcoded URLs in code"
echo "------------------------------------"

# 搜索可能的硬编码地址
HARDCODED_FOUND=0

# 检查真正的硬编码地址（排除注释、默认值和环境变量使用）
HARDCODED_PATTERNS=$(grep -r "http://localhost" src/ --exclude-dir=node_modules 2>/dev/null | \
    grep -v "process.env" | \
    grep -v "||" | \
    grep -v "* @default" | \
    grep -v "//" | \
    grep -v "const.*=" | \
    grep -v "fetch.*\${" | \
    head -5)

if [ ! -z "$HARDCODED_PATTERNS" ]; then
    echo -e "${RED}⚠️  发现可能的硬编码地址:${NC}"
    echo "$HARDCODED_PATTERNS"
    HARDCODED_FOUND=1
else
    echo -e "${GREEN}✅ 未发现硬编码地址${NC}"
fi

if [ $HARDCODED_FOUND -eq 0 ]; then
    validate_check "代码中无硬编码地址" "true"
else
    validate_check "代码中无硬编码地址" "false"
fi

# 检查API连接
echo ""
echo -e "${BLUE}🌐 测试API连接${NC}"
echo "Testing API connectivity"
echo "------------------------"

if command -v curl >/dev/null 2>&1; then
    # 测试API连接
    if curl -s --connect-timeout 5 "$NEXT_PUBLIC_API_BASE_URL/api/v3/info" >/dev/null 2>&1; then
        validate_check "API服务连接正常" "true"
    else
        validate_check "API服务连接正常" "false"
        echo -e "${YELLOW}💡 提示: 请确保后端API服务正在运行${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  curl 未安装，跳过API连接测试${NC}"
fi

# 检查Docker配置
echo ""
echo -e "${BLUE}🐳 检查Docker配置${NC}"
echo "Checking Docker configuration"
echo "-----------------------------"

validate_check "docker-compose.yml 文件存在" "[ -f 'docker-compose.yml' ]"

if [ -f "docker-compose.yml" ]; then
    # 检查docker-compose.yml中是否使用了环境变量
    if grep -q "\${" docker-compose.yml; then
        validate_check "docker-compose.yml 使用环境变量" "true"
    else
        validate_check "docker-compose.yml 使用环境变量" "false"
    fi
fi

# 生成验证报告
echo ""
echo -e "${BLUE}📊 验证结果总结${NC}"
echo "Validation Results Summary"
echo "=========================="

echo "总检查项: $TOTAL_CHECKS"
echo "通过: $PASSED_CHECKS"
echo "失败: $FAILED_CHECKS"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有检查项都通过了！${NC}"
    echo -e "${GREEN}✅ 环境变量配置正确${NC}"
    exit 0
else
    echo -e "${RED}❌ 有 $FAILED_CHECKS 项检查失败${NC}"
    echo -e "${YELLOW}💡 请根据上述提示修复配置问题${NC}"
    exit 1
fi
