# 环境变量使用指南

## 📋 概述

本文档说明 30_machine_status (Next.js) 项目中环境变量的正确使用方法，特别是 API 配置相关的环境变量。

## 🔑 关键环境变量

### 1. `NEXT_PUBLIC_API_BASE_URL`
- **作用**: API 服务基础 URL
- **适用**: Next.js 项目（客户端和服务器端都可访问）
- **格式**: `http://host:port`
- **示例**: 
  - 本地开发: `http://localhost:9005`
  - Docker 环境: `http://mdc_server_api:9005`

### 2. `NEXT_PUBLIC_APP_PORT`
- **作用**: 前端应用端口
- **默认值**: `9100`
- **示例**: `9100`

### 3. `NODE_ENV`
- **作用**: 运行环境标识
- **可选值**: `development`, `production`, `test`
- **默认值**: `development`

## ❌ 常见误区

### `REACT_APP_API_BASE_URL` 无效
```bash
# ❌ 错误：Next.js 不识别 REACT_APP_ 前缀
REACT_APP_API_BASE_URL=http://localhost:9005

# ✅ 正确：使用 NEXT_PUBLIC_ 前缀
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
```

**原因**:
- `REACT_APP_` 是 Create React App 的约定
- `NEXT_PUBLIC_` 是 Next.js 的约定
- 两者不兼容，不能混用

## 🐳 Docker 环境配置

### 容器间通信原理
```yaml
# docker-compose.yml
services:
  mdc_dashboard:
    environment:
      # 容器间通信使用容器名称
      NEXT_PUBLIC_API_BASE_URL: http://mdc_server_api:9005
    networks:
      - mdc_full_mdc_network

  mdc_server_api:
    networks:
      - mdc_full_mdc_network
```

### 智能 URL 处理
代码会自动处理不同环境：

```typescript
// src/config/api.ts
export const getApiBaseUrl = (): string => {
  if (typeof window !== 'undefined') {
    // 浏览器环境：转换为 localhost
    const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    if (apiUrl.includes('mdc_server_api')) {
      return apiUrl.replace('mdc_server_api', 'localhost');
    }
    return apiUrl;
  } else {
    // 服务器端：使用容器名称
    return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
  }
};
```

## 🛠️ 配置方法

### 方法 1: 环境变量文件
```bash
# .env.local
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
NEXT_PUBLIC_APP_PORT=9100
NODE_ENV=development
```

### 方法 2: Docker Compose
```yaml
# docker-compose.yml
services:
  mdc_dashboard:
    environment:
      NEXT_PUBLIC_API_BASE_URL: http://mdc_server_api:9005
      NEXT_PUBLIC_APP_PORT: 9100
      NODE_ENV: production
```

### 方法 3: 运行时设置
```bash
# 临时设置
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005 npm run dev

# 导出设置
export NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
npm run dev
```

## 🔍 验证配置

### 1. 检查环境变量
```bash
# 在项目根目录运行
./validate_env_config.sh
```

### 2. 浏览器控制台
```javascript
// 在浏览器控制台中检查
console.log('API URL:', process.env.NEXT_PUBLIC_API_BASE_URL);
```

### 3. 测试页面
访问测试页面验证配置：
```
http://localhost:9100/api-test
```

## 🚨 故障排除

### 问题 1: API 请求失败
**可能原因**:
- 环境变量未设置或设置错误
- 容器间网络不通
- API 服务未启动

**解决方案**:
```bash
# 检查环境变量
echo $NEXT_PUBLIC_API_BASE_URL

# 测试容器连接
./test_docker_connection.sh

# 检查 API 服务状态
docker ps | grep mdc_server_api
```

### 问题 2: 浏览器无法访问容器名称
**症状**: 浏览器显示 "无法解析 mdc_server_api"

**原因**: 浏览器运行在宿主机，无法解析 Docker 内部名称

**解决方案**: 代码已自动处理，无需手动修改

### 问题 3: 环境变量不生效
**可能原因**:
- 使用了错误的前缀 (`REACT_APP_` 而不是 `NEXT_PUBLIC_`)
- 修改后未重启服务
- 文件权限问题

**解决方案**:
```bash
# 确保使用正确前缀
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005

# 重启服务
npm run dev

# 检查文件权限
ls -la .env*
```

## 📚 最佳实践

1. **统一使用 `NEXT_PUBLIC_` 前缀**
2. **不同环境使用不同配置文件**
3. **敏感信息不要放在客户端可访问的环境变量中**
4. **修改环境变量后重启服务**
5. **使用验证脚本检查配置**

## 🔗 相关文档

- [Next.js 环境变量文档](https://nextjs.org/docs/basic-features/environment-variables)
- [Docker Compose 环境变量](https://docs.docker.com/compose/environment-variables/)
- [项目 Docker 通信指南](./DOCKER_COMMUNICATION_GUIDE.md)
