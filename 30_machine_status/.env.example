# ================================
# 30_machine_status 环境变量配置示例
# ================================
# 请复制此文件为 .env 并根据实际情况修改配置

# 后台API基础URL配置
# 统一的后端API接口地址，用于所有前后端交互
# 选择要连接的后端服务：
# - Python版本 (31_machine_backend): http://localhost:9980
# - Go版本 (12_server_api): http://localhost:9005
# - 生产环境: https://your-api-domain.com

# 统一的API基础URL（前后端共用）
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005

# ================================
# 应用程序配置
# ================================

# 前端应用端口配置
# 默认端口为9100，避免与其他服务冲突
NEXT_PUBLIC_APP_PORT=9100

# 应用程序环境
# 可选值: development, testing, production
NODE_ENV=development

# 禁用Next.js遥测数据收集
NEXT_TELEMETRY_DISABLED=1

# ================================
# 快速切换配置
# ================================

# 快速切换到不同后端服务：
# 1. Go后端 (推荐):
#    NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
#
# 2. Python后端:
#    NEXT_PUBLIC_API_BASE_URL=http://localhost:9980
#
# 3. 生产环境:
#    NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com

# ================================
# 开发调试配置
# ================================

# 启用API调试日志
NEXT_PUBLIC_DEBUG_API=false

# API请求超时时间（毫秒）
NEXT_PUBLIC_API_TIMEOUT=10000

# API重试次数
NEXT_PUBLIC_API_RETRY_COUNT=3

# ================================
# Docker 部署配置示例
# ================================

# Docker 环境中的容器间通信配置:
# 当 30_machine_status 和 12_server_api 都运行在 Docker 容器中时，
# 需要使用容器名称进行通信，而不是 localhost

# Docker Compose 环境变量示例:
# services:
#   mdc_dashboard:
#     environment:
#       - NEXT_PUBLIC_API_BASE_URL=http://mdc_server_api:9005
#       - NODE_ENV=production
#       - NEXT_TELEMETRY_DISABLED=1
#     networks:
#       - mdc_full_mdc_network

# 重要说明：
# 1. 容器间通信：使用 http://mdc_server_api:9005
# 2. 浏览器访问：仍然使用 http://localhost:9005 (通过端口映射)
# 3. 网络配置：确保两个容器在同一个 Docker 网络中

# ================================
# 生产环境配置示例
# ================================

# 生产环境配置:
# NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
# NODE_ENV=production
# NEXT_PUBLIC_DEBUG_API=false
