# Docker 容器间通信配置指南

## 问题描述

30_machine_status (Next.js 项目) 和 12_server_api (Go API 服务) 运行在不同的 Docker 容器中，需要正确配置容器间通信。

## 解决方案概述

### 1. 网络配置
两个服务都使用 `mdc_full_mdc_network` 外部网络：

```yaml
networks:
  mdc_full_mdc_network:
    external: true
```

### 2. API URL 配置
- **容器间通信**: 使用容器名称 `http://mdc_server_api:9005`
- **浏览器访问**: 使用宿主机地址 `http://localhost:9005`

## 详细配置步骤

### 步骤 1: 确保网络存在
```bash
# 检查网络是否存在
docker network ls | grep mdc_full_mdc_network

# 如果不存在，创建网络
docker network create mdc_full_mdc_network
```

### 步骤 2: 配置环境变量
在 `30_machine_status/docker-compose.yml` 中：

```yaml
services:
  mdc_dashboard:
    environment:
      # 使用容器名称进行服务间通信
      NEXT_PUBLIC_API_BASE_URL: http://mdc_server_api:9005
    networks:
      - mdc_full_mdc_network
```

### 步骤 3: 更新 API 配置
`src/config/api.ts` 已更新为智能处理不同环境：

- **服务器端渲染**: 使用容器名称 `mdc_server_api:9005`
- **客户端请求**: 自动转换为 `localhost:9005`

### 步骤 4: 启动服务

#### 方法 1: 使用启动脚本（推荐）
```bash
./docker_start.sh
```

#### 方法 2: 手动启动
```bash
# 1. 启动 12_server_api
cd ../12_server_api
docker-compose up -d

# 2. 启动 30_machine_status
cd ../30_machine_status
docker-compose --env-file .env.docker up -d
```

## 验证连接

### 1. 检查容器状态
```bash
docker ps | grep -E "(mdc_dashboard|mdc_server_api)"
```

### 2. 测试网络连接
```bash
# 从 30_machine_status 容器测试连接到 12_server_api
docker exec mdc_dashboard curl http://mdc_server_api:9005/health
```

### 3. 检查网络配置
```bash
# 查看网络详情
docker network inspect mdc_full_mdc_network

# 查看容器网络连接
docker inspect mdc_dashboard | grep -A 10 "Networks"
docker inspect mdc_server_api | grep -A 10 "Networks"
```

## 访问地址

- **前端应用**: http://localhost:9100
- **API 服务**: http://localhost:9005
- **容器间通信**: http://mdc_server_api:9005

## 故障排除

### 问题 1: 无法连接到 API 服务
**症状**: 前端显示 API 连接错误

**解决方案**:
1. 检查 12_server_api 容器是否运行
2. 验证网络配置
3. 检查环境变量设置

```bash
# 检查容器日志
docker logs mdc_server_api
docker logs mdc_dashboard

# 测试网络连接
docker exec mdc_dashboard ping mdc_server_api
```

### 问题 2: 网络不存在
**症状**: 启动时报错 "network mdc_full_mdc_network not found"

**解决方案**:
```bash
docker network create mdc_full_mdc_network
```

### 问题 3: 端口冲突
**症状**: 端口已被占用

**解决方案**:
```bash
# 查看端口占用
lsof -i :9100
lsof -i :9005

# 停止冲突的容器
docker stop $(docker ps -q --filter "publish=9100")
```

## 配置文件说明

### 主要配置文件
- `docker-compose.yml`: Docker 服务配置
- `.env.docker`: Docker 环境变量
- `src/config/api.ts`: API 配置逻辑

### 环境变量
- `NEXT_PUBLIC_API_BASE_URL`: API 基础 URL
- `NEXT_PUBLIC_APP_PORT`: 应用端口
- `NODE_ENV`: 运行环境

## 最佳实践

1. **使用外部网络**: 便于多个项目共享网络
2. **环境变量配置**: 便于不同环境部署
3. **智能 URL 处理**: 自动适配服务器端和客户端
4. **健康检查**: 定期验证服务连接状态

## 相关文档

- [Docker Compose 网络配置](https://docs.docker.com/compose/networking/)
- [Next.js 环境变量](https://nextjs.org/docs/basic-features/environment-variables)
- [12_server_api 配置文档](../12_server_api/README.md)
