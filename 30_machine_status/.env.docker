# ================================
# 30_machine_status Docker 环境配置
# ================================
# 此文件专门用于 Docker 容器环境的配置

# ================================
# Docker 容器间通信配置
# ================================

# API 服务基础URL - 使用容器名称进行通信
# 在 Docker 网络中，容器可以通过服务名称相互访问
# Next.js 只识别 NEXT_PUBLIC_ 前缀的环境变量
NEXT_PUBLIC_API_BASE_URL=http://mdc_server_api:9005

# 应用端口配置
NEXT_PUBLIC_APP_PORT=9100

# 生产环境配置
NODE_ENV=production

# 禁用 Next.js 遥测
NEXT_TELEMETRY_DISABLED=1

# ================================
# API 调试和性能配置
# ================================

# 生产环境关闭调试
NEXT_PUBLIC_DEBUG_API=false

# API 请求超时时间（毫秒）
NEXT_PUBLIC_API_TIMEOUT=10000

# API 重试次数
NEXT_PUBLIC_API_RETRY_COUNT=3

# ================================
# Docker 网络说明
# ================================

# 网络配置要求：
# 1. 两个服务必须在同一个 Docker 网络中
# 2. 网络名称：mdc_full_mdc_network
# 3. 网络类型：external (外部网络)

# 容器通信流程：
# 1. 30_machine_status (mdc_dashboard) 容器
#    ↓ 通过 Docker 内部网络
# 2. 12_server_api (mdc_server_api) 容器

# 端口映射：
# - 30_machine_status: 宿主机 9100 → 容器 9100
# - 12_server_api: 宿主机 9005 → 容器 9005

# ================================
# 使用方法
# ================================

# 1. 确保 12_server_api 容器正在运行：
#    cd ../12_server_api
#    docker-compose up -d

# 2. 使用此配置启动 30_machine_status：
#    docker-compose --env-file .env.docker up -d

# 3. 验证容器间通信：
#    docker exec -it mdc_dashboard curl http://mdc_server_api:9005/health

# ================================
# 故障排除
# ================================

# 如果无法连接到 API 服务，请检查：
# 1. 网络连接：
#    docker network ls | grep mdc_full_mdc_network
#    docker network inspect mdc_full_mdc_network

# 2. 容器状态：
#    docker ps | grep mdc_server_api
#    docker ps | grep mdc_dashboard

# 3. 容器日志：
#    docker logs mdc_server_api
#    docker logs mdc_dashboard

# 4. 网络连通性测试：
#    docker exec -it mdc_dashboard ping mdc_server_api
#    docker exec -it mdc_dashboard curl http://mdc_server_api:9005/health
