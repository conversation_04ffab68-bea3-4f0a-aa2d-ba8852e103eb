'use client';

import { SidebarMode } from '@/components/layout/Sidebar';

const STORAGE_KEY = 'sidebar-mode';

// 全局变量，用于在页面之间保持状态
let currentMode: SidebarMode | null = null;

/**
 * 获取侧边栏模式
 * 优先使用全局变量，然后从localStorage中获取保存的模式，如果没有则返回默认值
 */
export function getSidebarMode(): SidebarMode {
  console.log('获取侧边栏模式，当前全局变量:', currentMode);

  // 服务器端渲染时返回默认值
  if (typeof window === 'undefined') {
    console.log('服务器端渲染，使用默认值:', SidebarMode.HIDDEN);
    return SidebarMode.HIDDEN; // 默认值
  }

  // 从localStorage读取
  const savedMode = localStorage.getItem(STORAGE_KEY);
  console.log('从localStorage读取:', savedMode);

  // 如果localStorage中有值，使用它
  if (savedMode && Object.values(SidebarMode).includes(savedMode as SidebarMode)) {
    // 保存到全局变量
    currentMode = savedMode as SidebarMode;
    console.log('使用localStorage值并更新全局变量:', currentMode);
    return currentMode;
  }

  // 如果全局变量已设置，使用它
  if (currentMode !== null) {
    console.log('使用全局变量:', currentMode);
    return currentMode;
  }

  // 设置默认值
  currentMode = SidebarMode.HIDDEN;
  console.log('使用默认值并更新全局变量:', currentMode);
  return currentMode;
}

/**
 * 保存侧边栏模式
 * 将当前模式保存到localStorage中和全局变量
 */
export function saveSidebarMode(mode: SidebarMode): void {
  console.log('保存侧边栏模式:', mode);

  // 更新全局变量
  currentMode = mode;
  console.log('全局变量已更新:', currentMode);

  // 保存到localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem(STORAGE_KEY, mode);
    console.log('localStorage已更新:', localStorage.getItem(STORAGE_KEY));
  }
}

/**
 * 切换侧边栏模式
 * 在两种模式之间切换：隐藏 <-> 折叠
 */
export function getNextSidebarMode(currentMode: SidebarMode): SidebarMode {
  switch (currentMode) {
    case SidebarMode.HIDDEN:
      return SidebarMode.COLLAPSED;
    case SidebarMode.COLLAPSED:
      return SidebarMode.HIDDEN;
    default:
      return SidebarMode.HIDDEN;
  }
}
