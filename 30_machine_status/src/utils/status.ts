import { MachineStatus, StatusCount, Machine } from '@/types';

/**
 * 获取设备状态的中文显示文本
 *
 * @param status - 设备状态枚举值
 * @returns 对应的中文状态文本
 *
 * 该函数将英文状态代码转换为用户界面显示的中文文本
 */
export function getStatusText(status: MachineStatus): string {
  switch (status) {
    case 'production':
      return '生产'; // 设备正在生产中
    case 'idle':
      return '空闲'; // 设备处于空闲状态
    case 'fault':
      return '故障'; // 设备发生故障
    case 'adjusting':
      return '调机'; // 设备正在调整中
    case 'shutdown':
      return '关机'; // 设备已关闭
    case 'disconnected':
      return '未连接'; // 设备与系统断开连接
    case 'maintenance':
      return '保养'; // 设备正在保养中
    case 'debugging':
      return '调试'; // 设备正在调试中
    default:
      return '未知'; // 未知状态，用于处理异常情况
  }
}

/**
 * 获取设备状态对应的背景颜色CSS类
 *
 * @param status - 设备状态枚举值
 * @returns 对应的Tailwind CSS背景颜色类名
 *
 * 该函数返回适用于Tailwind CSS的背景颜色类名，用于在UI中直观地区分不同状态
 * - 生产中: 绿色
 * - 空闲: 琥珀色
 * - 故障: 红色
 * - 调机: 蓝色
 * - 关机: 灰色
 * - 未连接: 浅灰色
 * - 保养: 紫色
 * - 调试: 青色
 */
export function getStatusColor(status: MachineStatus): string {
  switch (status) {
    case 'production':
      return 'bg-green-500'; // 绿色表示正常生产
    case 'idle':
      return 'bg-amber-500'; // 琥珀色表示空闲状态
    case 'fault':
      return 'bg-red-500'; // 红色表示故障警告
    case 'adjusting':
      return 'bg-blue-500'; // 蓝色表示调机状态
    case 'shutdown':
      return 'bg-gray-500'; // 灰色表示关机状态
    case 'disconnected':
      return 'bg-gray-400'; // 浅灰色表示未连接
    case 'maintenance':
      return 'bg-purple-500'; // 紫色表示保养中
    case 'debugging':
      return 'bg-cyan-500'; // 青色表示调试中
    default:
      return 'bg-gray-300'; // 浅灰色用于未知状态
  }
}

/**
 * 获取设备状态对应的边框颜色CSS类
 *
 * @param status - 设备状态枚举值
 * @returns 对应的Tailwind CSS边框颜色类名
 *
 * 该函数返回适用于Tailwind CSS的边框颜色类名，用于在UI中为设备卡片添加状态相关的边框样式
 * 颜色方案与getStatusColor保持一致，但应用于边框而非背景
 */
export function getStatusBorderColor(status: MachineStatus): string {
  switch (status) {
    case 'production':
      return 'border-green-500'; // 绿色边框表示正常生产
    case 'idle':
      return 'border-amber-500'; // 琥珀色边框表示空闲状态
    case 'fault':
      return 'border-red-500'; // 红色边框表示故障警告
    case 'adjusting':
      return 'border-blue-500'; // 蓝色边框表示调机状态
    case 'shutdown':
      return 'border-gray-500'; // 灰色边框表示关机状态
    case 'disconnected':
      return 'border-dashed border-gray-400'; // 虚线灰色边框表示未连接
    default:
      return 'border-gray-300'; // 浅灰色边框用于未知状态
  }
}

/**
 * 计算各种状态的设备数量
 *
 * @param machines - 设备对象数组
 * @returns 包含各状态计数的对象
 *
 * 该函数遍历所有设备并统计每种状态的设备数量，用于状态摘要显示
 * 返回的对象包含每种可能状态的计数器
 */
export function calculateStatusCounts(machines: Machine[]): StatusCount {
  // 初始化所有状态的计数为0
  const counts: StatusCount = {
    production: 0,   // 生产中的设备数量
    idle: 0,         // 空闲设备数量
    fault: 0,        // 故障设备数量
    adjusting: 0,    // 调机中的设备数量
    shutdown: 0,     // 已关机的设备数量
    disconnected: 0, // 未连接的设备数量
    maintenance: 0,  // 保养中的设备数量
    debugging: 0     // 调试中的设备数量
  };

  // 遍历所有设备并增加相应状态的计数
  machines.forEach(machine => {
    // 确保状态是有效的计数键
    if (machine.status in counts) {
      counts[machine.status]++;
    }
  });

  return counts;
}

/**
 * 比较两个设备列表，找出状态发生变化的设备索引
 *
 * @param oldMachines - 旧的设备列表
 * @param newMachines - 新的设备列表
 * @returns 状态发生变化的设备索引数组
 *
 * 该函数用于在数据更新时识别哪些设备的状态发生了变化，
 * 以便UI可以对这些设备应用视觉效果（如闪烁动画）来提示用户
 */
export function findChangedMachines(oldMachines: Machine[], newMachines: Machine[]): number[] {
  // 存储状态变化的设备索引
  const changedIndices: number[] = [];

  // 取两个数组的最小长度，避免越界
  const minLength = Math.min(oldMachines.length, newMachines.length);

  // 遍历并比较每个设备的状态
  for (let i = 0; i < minLength; i++) {
    // 如果状态不同，记录该索引
    if (oldMachines[i].status !== newMachines[i].status) {
      changedIndices.push(i);
    }
  }

  return changedIndices;
}
