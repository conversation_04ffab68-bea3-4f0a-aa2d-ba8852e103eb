/**
 * API配置文件
 *
 * 用于管理API相关的配置，包括基础URL等
 * 统一使用 NEXT_PUBLIC_API_BASE_URL 环境变量
 * 支持Docker容器间通信和本地开发环境
 */

/**
 * 检测当前运行环境
 * @returns 是否在Docker容器环境中运行
 */
const isDockerEnvironment = (): boolean => {
  // 检查是否在服务器端且有Docker相关的环境变量
  if (typeof window === 'undefined') {
    return process.env.NODE_ENV === 'production' ||
      (process.env.NEXT_PUBLIC_API_BASE_URL?.includes('mdc_server_api') ?? false);
  }
  return false;
};

/**
 * 获取统一的API基础URL
 * 支持多环境部署：本地开发、Docker容器、生产环境
 */
export const getApiBaseUrl = (): string => {
  const configuredUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

  // 检查是否在浏览器环境中
  if (typeof window !== 'undefined') {
    // 客户端环境处理

    // 如果配置的是相对路径，直接返回（适用于生产环境反向代理）
    if (configuredUrl.startsWith('/')) {
      return configuredUrl;
    }

    // 如果配置的是容器名称，需要转换为宿主机访问地址
    if (configuredUrl.includes('mdc_server_api')) {
      // 将容器名称替换为当前主机地址
      const currentHost = window.location.host;
      const protocol = window.location.protocol;

      // 如果是开发环境（localhost），使用 9005 端口
      if (currentHost.includes('localhost') || currentHost.includes('127.0.0.1')) {
        return `${protocol}//${currentHost.replace(/:\d+$/, '')}:9005`;
      }

      // 生产环境，假设 API 在同一主机的不同端口或通过反向代理
      return `/api`;
    }

    return configuredUrl;
  } else {
    // 服务器端环境处理

    // 如果配置的是相对路径，在服务器端需要转换为完整URL
    if (configuredUrl.startsWith('/')) {
      // 在Docker环境中，尝试使用容器名称
      if (process.env.NODE_ENV === 'production') {
        return 'http://mdc_server_api:9005';
      }
      // 开发环境回退到localhost
      return 'http://localhost:9005';
    }

    return configuredUrl;
  }
};

/**
 * 获取后端API的直接URL（用于服务器端调用后端的情况）
 * 在Docker环境中使用容器名称进行通信
 */
export const getBackendApiUrl = (): string => {
  const configuredUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

  // 如果配置的是相对路径，在服务器端需要转换为完整URL
  if (configuredUrl.startsWith('/')) {
    // 在Docker环境中，尝试使用容器名称
    if (process.env.NODE_ENV === 'production') {
      return 'http://mdc_server_api:9005';
    }
    // 开发环境回退到localhost
    return 'http://localhost:9005';
  }

  return configuredUrl;
};

// API端点配置
export const API_ENDPOINTS = {
  machines: '/api/machines',
  companyInfo: '/api/company-info',
  settings: '/api/settings',
  settingsDisplay: '/api/settings/display',
  settingsRefresh: '/api/settings/refresh',
  productionHistory: '/api/production-history',
} as const;

// 构建完整的API URL
export const buildApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  return baseUrl ? `${baseUrl}${endpoint}` : endpoint;
};
