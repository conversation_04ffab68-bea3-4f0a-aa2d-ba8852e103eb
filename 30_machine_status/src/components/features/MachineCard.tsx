'use client'; // 声明这是一个客户端组件

/**
 * 设备卡片组件
 *
 * 该组件显示单个设备的信息卡片，包括设备名称、型号、位置和状态
 * 当设备状态变化时，会显示闪烁动画以提示用户
 */
import { Machine, MachineStatus } from '@/types';
import { getStatusBorderColor } from '@/utils/status';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * MachineCard组件的属性接口
 */
interface MachineCardProps {
  machine: Machine; // 设备数据对象
  index: number;    // 设备在列表中的索引（用于显示序号）
  apiError?: boolean; // API请求是否失败
  cardContent?: {
    showCode: boolean;      // 是否显示设备编码
    showName: boolean;      // 是否显示设备名称
    showLocation: boolean;  // 是否显示设备位置
    showModel: boolean;     // 是否显示设备型号
    showQuantity: boolean;  // 是否显示当前产量数量
    showPlan: boolean;      // 是否显示计划生产数量
    fontSize: number;       // 字体大小（像素）
    textAlign: string;      // 文本对齐方式（"left", "center", "right"）
    layoutRatio: number;    // 左右布局比例（左侧占比，如60表示左侧占60%）
  };
  cardStyle?: {
    cardWidth: number;      // 卡片宽度（像素）
    cardHeight: number;     // 卡片高度（像素）
    borderRadius: number;   // 边框圆角（像素）
    borderWidth: number;    // 边框宽度（像素）
    contentPadding: number; // 内容区域内边距（像素）
  };
  statusColors?: {
    [key in MachineStatus]?: string; // 各状态对应的颜色
  };
}

export default function MachineCard({
  machine,
  index,
  apiError = false,
  cardContent = {
    showCode: true,
    showName: true,
    showLocation: true,
    showModel: true,
    showQuantity: true,
    showPlan: true,
    fontSize: 14,
    textAlign: "center",
    layoutRatio: 60
  },
  cardStyle = {
    cardWidth: 200,  // 这个值现在只作为最小宽度参考，实际宽度由容器决定
    cardHeight: 120,
    borderRadius: 4,
    borderWidth: 2,
    contentPadding: 8
  },
  statusColors = {
    production: "#22c55e",
    idle: "#f59e0b",
    fault: "#ef4444",
    adjusting: "#3b82f6",
    shutdown: "#6b7280",
    disconnected: "#9ca3af",
    maintenance: "#8b5cf6",
    debugging: "#06b6d4"
  }
}: MachineCardProps) {
  // 路由器，用于页面导航
  const router = useRouter();
  // 状态管理
  // 跟踪上一个状态，用于检测状态变化并触发动画
  const [prevStatus, setPrevStatus] = useState<MachineStatus | undefined>(undefined);
  // 控制闪烁动画的状态
  const [isFlashing, setIsFlashing] = useState(false);

  /**
   * 状态变化时添加闪烁动画效果
   * 当设备状态发生变化时，卡片会闪烁1秒钟以提示用户
   */
  useEffect(() => {
    // 初始化时，只设置前一状态，不触发动画
    if (prevStatus === undefined) {
      setPrevStatus(machine.status);
      return;
    }

    // 只有当状态发生变化时才触发动画
    if (prevStatus !== machine.status) {
      // 开始闪烁动画
      setIsFlashing(true);
      // 设置定时器，1秒后停止闪烁
      const timer = setTimeout(() => {
        setIsFlashing(false);
        // 更新前一状态为当前状态
        setPrevStatus(machine.status);
      }, 1000);
      // 清理函数，防止内存泄漏
      return () => clearTimeout(timer);
    }
  }, [machine.status, prevStatus]); // 依赖项：当前状态和前一状态

  /**
   * 根据设备状态获取背景颜色样式
   * 未连接的设备使用透明背景，其他状态使用深灰色背景
   *
   * @param status - 设备状态
   * @returns 对应的CSS类名
   */
  const getStatusBgColor = (status: MachineStatus) => {
    switch (status) {
      case 'disconnected':
        return 'bg-transparent'; // 未连接设备使用透明背景
      default:
        return 'bg-gray-900'; // 其他状态使用深灰色背景
    }
  };

  /**
   * 获取默认状态颜色
   * 根据设备状态返回默认的颜色代码
   *
   * @param status - 设备状态
   * @returns 对应的默认颜色代码
   */
  const getDefaultStatusColor = (status: MachineStatus): string => {
    switch (status) {
      case 'production':
        return '#22c55e'; // 绿色
      case 'idle':
        return '#f59e0b'; // 琥珀色
      case 'fault':
        return '#ef4444'; // 红色
      case 'adjusting':
        return '#3b82f6'; // 蓝色
      case 'shutdown':
        return '#6b7280'; // 灰色
      case 'disconnected':
        return '#9ca3af'; // 浅灰色
      case 'maintenance':
        return '#8b5cf6'; // 紫色
      case 'debugging':
        return '#06b6d4'; // 青色
      default:
        return '#6b7280'; // 默认灰色
    }
  };

  /**
   * 获取状态颜色
   * 根据设备状态和自定义颜色设置返回对应的颜色
   *
   * @param status - 设备状态
   * @returns 对应的颜色代码
   */
  const getStatusColor = (status: MachineStatus): string => {
    if (apiError) {
      return '#d1d5db'; // 浅灰色，API错误时使用
    }

    // 使用自定义颜色设置
    return statusColors?.[status] || getDefaultStatusColor(status);
  };

  // 格式化索引为两位数，例如：01, 02, ...
  const indexFormatted = index.toString().padStart(2, '0');

  /**
   * 处理卡片点击事件
   * 点击卡片时跳转到设备状态历史页面
   */
  const handleCardClick = () => {
    // 导航到设备状态历史页面
    router.push(`/device_status_history/${machine.id}`);
  };

  /**
   * 渲染设备卡片
   * 卡片由两部分组成：
   * 1. 顶部的序号条，颜色根据设备状态变化
   * 2. 主体部分显示设备信息，边框颜色根据状态变化
   */
  return (
    <div className="relative w-full">
      {/* 设备卡片主体 - 包含状态颜色栏和内容 */}
      <div
        style={{
          height: `${cardStyle?.cardHeight ?? 120}px`, // 使用固定高度而不是最小高度
          borderWidth: `${cardStyle?.borderWidth ?? 2}px`,
          borderColor: apiError ? '#d1d5db' : getStatusColor(machine.status),
          borderRadius: `${cardStyle?.borderRadius ?? 4}px`,
          position: 'relative', // 使用相对定位，让内部元素可以绝对定位
          overflow: 'hidden', // 防止内容溢出
          cursor: 'pointer' // 鼠标指针变为手型，表示可点击
        }}
        className={`
          w-full
          ${getStatusBgColor(machine.status)}
          transition-all duration-300 ease-in-out
          ${isFlashing ? 'animate-pulse shadow-lg shadow-current' : ''}
          hover:shadow-md hover:brightness-110
        `}
        onClick={handleCardClick} // 添加点击事件处理函数
      >
        {/* 顶部序号条 - 颜色根据设备状态变化，API错误时显示浅灰色 */}
        <div
          style={{
            backgroundColor: getStatusColor(machine.status),
            borderTopLeftRadius: `${(cardStyle?.borderRadius ?? 4) - 1}px`,
            borderTopRightRadius: `${(cardStyle?.borderRadius ?? 4) - 1}px`
          }}
          className="w-full text-xs px-2 py-1 transition-colors duration-300 ease-in-out"
        >
          {/* 显示格式化的序号 */}
          {indexFormatted}
        </div>

        {/* 内容区域 - 应用内边距和对齐方式，使用flex布局垂直居中 */}
        <div
          style={{
            padding: `${cardStyle?.contentPadding ?? 8}px`,
            fontSize: `${cardContent?.fontSize ?? 14}px`,
            height: 'calc(100% - 28px)' // 减去状态栏高度(28px)
          }}
          className="flex items-center justify-center overflow-hidden"
        >
          {/* 根据连接状态显示不同内容 */}
          {machine.status === 'disconnected' ? (
            // 未连接状态显示短横线
            <span className="text-gray-400 transition-opacity duration-300 w-full text-center">-</span>
          ) : (
            <>
              {/* 左侧信息区域 */}
              <div
                className="flex flex-col justify-center overflow-hidden"
                style={{
                  width: `${(!(cardContent?.showQuantity ?? true) && !(cardContent?.showPlan ?? true)) ? '100%' : `${cardContent?.layoutRatio ?? 60}%`}`,
                  textAlign: (cardContent?.textAlign ?? "center") as any,
                }}
              >
                {/* 设备品牌 - 根据设置显示 */}
                {(cardContent?.showCode ?? true) && (
                  <span className="text-gray-400 text-xs transition-all duration-300 truncate">{machine.brand}</span>
                )}

                {/* 设备名称 - 根据设置显示 */}
                {(cardContent?.showName ?? true) && (
                  <span className="text-white font-bold transition-all duration-300 truncate">{machine.name}</span>
                )}

                {/* 设备型号 - 根据设置显示 */}
                {(cardContent?.showModel ?? true) && (
                  <span className="text-gray-400 text-xs transition-all duration-300 truncate">{machine.model}</span>
                )}

                {/* 设备位置 - 根据设置显示 */}
                {(cardContent?.showLocation ?? true) && (
                  <span className="text-gray-400 text-xs transition-all duration-300 truncate">{machine.location}</span>
                )}
              </div>

              {/* 右侧数量区域 - 只有在至少一个数量字段显示时才显示 */}
              {((cardContent?.showQuantity ?? true) || (cardContent?.showPlan ?? true)) && (
                <div
                  className="flex flex-col justify-center ml-2 border-l border-gray-700 pl-2"
                  style={{
                    width: `${100 - (cardContent?.layoutRatio ?? 60)}%`,
                  }}
                >
                  {/* 产量和计划数据区域 - 居中显示 */}
                  <div className="flex flex-col items-center justify-center w-full">
                    {/* 当前产量 - 根据设置显示 */}
                    {(cardContent?.showQuantity ?? true) && (
                      <div className="text-xs">
                        <span className={`${machine.status === 'production' ? 'text-green-400' : 'text-gray-300'} font-medium`}>
                          {machine.quantity}
                        </span>
                      </div>
                    )}

                    {/* 分隔线 - 当产量和计划都显示时显示 */}
                    {(cardContent?.showQuantity ?? true) && (cardContent?.showPlan ?? true) && (
                      <div className="w-3/4 border-t border-gray-600 my-1"></div>
                    )}

                    {/* 计划数量 - 根据设置显示 */}
                    {(cardContent?.showPlan ?? true) && (
                      <div className="text-xs">
                        <span className="text-blue-400 font-medium">{machine.plan}</span>
                      </div>
                    )}

                    {/* 进度条和百分比 - 当产量和计划都显示且计划不为0时显示 */}
                    {(cardContent?.showQuantity ?? true) && (cardContent?.showPlan ?? true) && machine.plan > 0 && (
                      <>
                        <div className="mt-2 w-full bg-gray-700 rounded-full h-1.5">
                          <div
                            className="bg-blue-500 h-1.5 rounded-full"
                            style={{
                              width: `${Math.min(100, (machine.quantity / machine.plan) * 100)}%`,
                              backgroundColor: machine.status === 'production' ? getStatusColor(machine.status) : '#3b82f6'
                            }}
                          ></div>
                        </div>
                        {/* 百分比显示 */}
                        <div className="text-xs text-center mt-1 text-gray-400">
                          {Math.round((machine.quantity / machine.plan) * 100)}%
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
