'use client'; // 声明这是一个客户端组件

/**
 * 刷新指示器组件
 *
 * 该组件显示上次数据更新的时间信息，并提供手动刷新按钮
 * 包括：
 * 1. 刷新按钮（加载中时显示旋转动画）
 * 2. 相对时间显示（几秒前更新）
 * 3. 精确时间显示（时:分:秒.毫秒）
 */
import { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react'; // 导入刷新图标

/**
 * RefreshIndicator组件的属性接口
 */
interface RefreshIndicatorProps {
  lastUpdated: Date | null; // 上次更新时间
  isLoading: boolean;       // 是否正在加载数据
  onRefresh: () => void;    // 刷新按钮点击处理函数
}

export default function RefreshIndicator({
  lastUpdated,
  isLoading,
  onRefresh
}: RefreshIndicatorProps) {
  // 状态管理
  const [timeAgo, setTimeAgo] = useState<string>(''); // 相对时间文本（几秒前更新）
  const [updateTime, setUpdateTime] = useState<string>(''); // 精确时间文本（时:分:秒.毫秒）

  /**
   * 格式化显示更新时间，精确到毫秒
   * 将Date对象转换为格式化的时间字符串：HH:MM:SS.mmm
   */
  useEffect(() => {
    // 如果没有更新时间，清空显示
    if (!lastUpdated) {
      setUpdateTime('');
      return;
    }

    // 格式化时间，所有部分都补零到固定位数
    const hours = lastUpdated.getHours().toString().padStart(2, '0');       // 两位小时
    const minutes = lastUpdated.getMinutes().toString().padStart(2, '0');   // 两位分钟
    const seconds = lastUpdated.getSeconds().toString().padStart(2, '0');   // 两位秒
    const milliseconds = lastUpdated.getMilliseconds().toString().padStart(3, '0'); // 三位毫秒

    // 组合成完整时间字符串
    setUpdateTime(`${hours}:${minutes}:${seconds}.${milliseconds}`);
  }, [lastUpdated]); // 依赖项：上次更新时间

  /**
   * 计算并显示相对时间（多久前更新）
   * 根据时间差动态显示不同的时间单位（毫秒/秒/分钟/小时）
   * 每秒更新一次，使显示保持实时性
   */
  useEffect(() => {
    // 如果没有更新时间，清空显示
    if (!lastUpdated) {
      setTimeAgo('');
      return;
    }

    /**
     * 计算并格式化相对时间文本
     * 根据时间差的大小选择合适的时间单位
     */
    const updateTimeAgo = () => {
      const now = new Date();
      // 计算时间差（秒和毫秒）
      const diffSeconds = Math.floor((now.getTime() - lastUpdated.getTime()) / 1000);
      const diffMilliseconds = now.getTime() - lastUpdated.getTime();

      // 根据时间差大小选择不同的显示格式
      if (diffMilliseconds < 1000) {
        // 不到1秒，显示毫秒
        setTimeAgo(`${diffMilliseconds}毫秒前更新`);
      } else if (diffSeconds < 10) {
        // 不到10秒，显示秒数
        setTimeAgo(`${diffSeconds}秒前更新`);
      } else if (diffSeconds < 60) {
        // 不到1分钟，显示秒数
        setTimeAgo(`${diffSeconds}秒前更新`);
      } else if (diffSeconds < 3600) {
        // 不到1小时，显示分钟数
        setTimeAgo(`${Math.floor(diffSeconds / 60)}分钟前更新`);
      } else {
        // 超过1小时，显示小时数
        setTimeAgo(`${Math.floor(diffSeconds / 3600)}小时前更新`);
      }
    };

    // 立即执行一次计算
    updateTimeAgo();

    // 设置定时器，每秒更新一次相对时间
    const timer = setInterval(updateTimeAgo, 1000);

    // 清理函数，组件卸载时清除定时器
    return () => clearInterval(timer);
  }, [lastUpdated]); // 依赖项：上次更新时间

  /**
   * 渲染刷新指示器组件
   * 包含刷新按钮和时间信息显示
   */
  return (
    <div className="flex items-center space-x-2 text-sm">
      {/* 刷新按钮 - 加载中时显示旋转动画 */}
      <button
        onClick={onRefresh} // 点击触发刷新
        disabled={isLoading} // 加载中时禁用按钮
        // 加载中时显示旋转动画和蓝色
        className={`p-1 rounded-full hover:bg-gray-700 transition-all ${isLoading ? 'animate-spin text-blue-400' : 'text-gray-300'
          }`}
      >
        <RefreshCw size={16} /> {/* 刷新图标 */}
      </button>

      {/* 时间信息显示区域 */}
      <div className="flex flex-col">
        {/* 相对时间显示（几秒前更新） - 使用固定宽度防止抖动 */}
        <span className="text-gray-400 inline-block w-[140px]">{timeAgo || '点击刷新数据'}</span>

        {/* 精确时间显示（时:分:秒.毫秒） - 使用等宽字体和固定宽度防止抖动 */}
        {updateTime && (
          <span className="text-xs text-gray-500 font-mono inline-block w-[160px]">
            更新时间: {updateTime}
          </span>
        )}
      </div>
    </div>
  );
}
