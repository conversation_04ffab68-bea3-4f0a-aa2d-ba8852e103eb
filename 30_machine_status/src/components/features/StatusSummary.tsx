'use client'; // 声明这是一个客户端组件

/**
 * 状态摘要组件
 *
 * 该组件显示所有设备状态的汇总信息，包括各种状态的设备数量
 * 当状态计数变化时，会显示平滑过渡动画
 */
import { useState, useEffect } from 'react';
import { StatusCount } from '@/types';
import StatusIndicator from './StatusIndicator';

/**
 * StatusSummary组件的属性接口
 */
interface StatusSummaryProps {
  counts: StatusCount; // 各状态的设备计数对象
  digitsCount?: number; // 状态计数显示的位数
  padChar?: string; // 状态计数前置填充字符
}

export default function StatusSummary({ counts, digitsCount = 2, padChar = "0" }: StatusSummaryProps) {
  // 状态管理
  // 跟踪上一次的计数，用于检测变化和平滑过渡
  const [prevCounts, setPrevCounts] = useState<StatusCount>(counts);
  // 控制背景动画效果的状态
  const [isAnimating, setIsAnimating] = useState(false);

  /**
   * 检测计数变化并添加动画效果
   * 当任何状态的计数发生变化时，添加背景透明度变化效果
   */
  useEffect(() => {
    // 检查是否有任何状态的计数发生变化
    const hasChanges = Object.keys(counts).some(
      (key) => counts[key as keyof StatusCount] !== prevCounts[key as keyof StatusCount]
    );

    // 如果有变化，触发动画效果
    if (hasChanges) {
      // 开始动画
      setIsAnimating(true);
      // 设置定时器，500毫秒后结束动画并更新前一状态
      const timer = setTimeout(() => {
        setIsAnimating(false);
        setPrevCounts(counts); // 更新前一状态为当前状态
      }, 500);
      // 清理函数，防止内存泄漏
      return () => clearTimeout(timer);
    }
  }, [counts, prevCounts]); // 依赖项：当前计数和前一计数

  /**
   * 渲染状态摘要组件
   * 显示一个包含所有状态指示器的水平容器
   * 当计数变化时，背景透明度会短暂变化以提示用户
   */
  return (
    <div className={`flex flex-wrap gap-4 py-3 px-4 bg-gray-800 rounded-md transition-all duration-300 ${isAnimating ? 'bg-opacity-80' : ''}`}>
      {/* 生产中状态指示器 */}
      <StatusIndicator
        status="production"
        count={counts.production}
        isChanged={counts.production !== prevCounts.production} // 检测计数变化
        digitsCount={digitsCount}
        padChar={padChar}
      />

      {/* 空闲状态指示器 */}
      <StatusIndicator
        status="idle"
        count={counts.idle}
        isChanged={counts.idle !== prevCounts.idle} // 检测计数变化
        digitsCount={digitsCount}
        padChar={padChar}
      />

      {/* 故障状态指示器 */}
      <StatusIndicator
        status="fault"
        count={counts.fault}
        isChanged={counts.fault !== prevCounts.fault} // 检测计数变化
        digitsCount={digitsCount}
        padChar={padChar}
      />

      {/* 关机状态指示器 */}
      <StatusIndicator
        status="shutdown"
        count={counts.shutdown}
        isChanged={counts.shutdown !== prevCounts.shutdown} // 检测计数变化
        digitsCount={digitsCount}
        padChar={padChar}
      />
    </div>
  );
}
