'use client'; // 声明这是一个客户端组件

/**
 * 状态指示器组件
 *
 * 该组件显示特定状态的设备数量，包括状态颜色指示点、状态名称和数量
 * 当数量变化时，会显示闪烁和放大动画以提示用户
 */
import { MachineStatus } from '@/types';
import { getStatusColor, getStatusText } from '@/utils/status';
import { useState, useEffect } from 'react';

/**
 * StatusIndicator组件的属性接口
 */
interface StatusIndicatorProps {
  status: MachineStatus; // 设备状态类型
  count: number;         // 该状态的设备数量
  isChanged?: boolean;   // 数量是否发生变化（用于触发动画）
  digitsCount?: number;  // 状态计数显示的位数
  padChar?: string;      // 状态计数前置填充字符
}

export default function StatusIndicator({
  status,
  count,
  isChanged = false,
  digitsCount = 2,
  padChar = "0"
}: StatusIndicatorProps) {
  // 状态管理
  // 控制闪烁动画的状态
  const [isFlashing, setIsFlashing] = useState(false);

  /**
   * 当计数变化时添加闪烁和放大效果
   * 通过CSS scale和颜色变化实现视觉提示
   */
  useEffect(() => {
    // 只有当isChanged为true时才触发动画
    if (isChanged) {
      // 开始闪烁动画
      setIsFlashing(true);
      // 设置定时器，1秒后停止闪烁
      const timer = setTimeout(() => {
        setIsFlashing(false);
      }, 1000);
      // 清理函数，防止内存泄漏
      return () => clearTimeout(timer);
    }
  }, [count, isChanged]); // 依赖项：计数和变化标志

  // 根据状态获取文本颜色类
  const getTextColorClass = (status: MachineStatus): string => {
    switch (status) {
      case 'production':
        return 'text-green-500';
      case 'idle':
        return 'text-amber-500';
      case 'fault':
        return 'text-red-500';
      case 'adjusting':
        return 'text-blue-500';
      case 'shutdown':
        return 'text-gray-500';
      case 'disconnected':
        return 'text-gray-400';
      case 'maintenance':
        return 'text-purple-500';
      case 'debugging':
        return 'text-cyan-500';
      default:
        return 'text-gray-300';
    }
  };

  const textColorClass = getTextColorClass(status);

  return (
    <div className={`flex items-center transition-all duration-300 ${isFlashing ? 'scale-110' : ''}`}>
      {/* 状态颜色指示点 - 确保所有状态都有颜色指示 */}
      <div className={`w-4 h-4 rounded-full mr-2 transition-all duration-300 ${getStatusColor(status)}`}></div>

      {/* 状态名称文本 - 使用与状态相匹配的颜色 */}
      <span className={`text-sm font-medium transition-all duration-300 ${textColorClass}`}>
        {getStatusText(status)}
      </span>

      {/* 状态计数，变化时高亮显示，固定位数显示 */}
      <span
        className={`ml-2 text-sm font-bold transition-all duration-300 ${isFlashing ? 'text-white' : textColorClass}`}
        style={{ minWidth: `${digitsCount * 0.6}rem`, display: 'inline-block', textAlign: 'right' }}
      >
        {count.toString().padStart(digitsCount, padChar)}
      </span>
    </div>
  );
}
