/**
 * 设备利用率图表组件
 * 
 * 提供各种利用率数据的可视化图表，包括：
 * 1. 每日利用率趋势柱状图
 * 2. 班次利用率分析柱状图
 * 3. 设备排名条形图
 * 
 * 特性：
 * - 响应式设计，适配不同屏幕尺寸
 * - 交互式图表，支持hover和点击
 * - 自定义颜色主题
 * - 数据标签和工具提示
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-12
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUpIcon, TrendingDownIcon, MinusIcon } from 'lucide-react';

// 数据类型定义
interface DailyUtilizationData {
  date: string;
  utilization_rate: number;
  total_devices: number;
  active_devices: number;
  working_hours: number;
  productive_hours: number;
}

interface DailyUtilizationTrend {
  start_date: string;
  end_date: string;
  daily_data: DailyUtilizationData[];
  average_rate: number;
  max_rate: number;
  min_rate: number;
  trend_direction: string;
  work_time_settings: any;
  last_updated: string;
}

interface ShiftTrend {
  start_date: string;
  end_date: string;
  daily_shift_data: any[];
  shift_averages: Record<string, number>;
  shifts: any[];
  work_time_settings: any;
  last_updated: string;
}

interface UtilizationChartsProps {
  dailyTrend: DailyUtilizationTrend | null;
  shiftTrend: ShiftTrend | null;
}

// 格式化利用率百分比
const formatUtilizationRate = (rate: number): string => {
  return `${rate.toFixed(1)}%`;
};

// 获取趋势图标
const getTrendIcon = (direction: string) => {
  switch (direction) {
    case 'up':
      return <TrendingUpIcon className="w-4 h-4 text-green-500" />;
    case 'down':
      return <TrendingDownIcon className="w-4 h-4 text-red-500" />;
    default:
      return <MinusIcon className="w-4 h-4 text-gray-500" />;
  }
};

// 获取趋势颜色
const getTrendColor = (direction: string): string => {
  switch (direction) {
    case 'up':
      return 'text-green-500';
    case 'down':
      return 'text-red-500';
    default:
      return 'text-gray-500';
  }
};

export default function UtilizationCharts({ dailyTrend, shiftTrend }: UtilizationChartsProps) {
  // 添加调试日志
  console.log('UtilizationCharts 渲染，dailyTrend:', dailyTrend);

  return (
    <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
      {/* 每日利用率趋势柱状图 */}
      {dailyTrend && (
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-white flex items-center gap-2">
              📈 每日利用率趋势
              <div className="flex items-center gap-1">
                {getTrendIcon(dailyTrend.trend_direction)}
                <span className={`text-sm ${getTrendColor(dailyTrend.trend_direction)}`}>
                  {dailyTrend.trend_direction === 'up' ? '上升' :
                    dailyTrend.trend_direction === 'down' ? '下降' : '稳定'}
                </span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {/* 统计指标 */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
              <div className="text-center bg-gray-700/50 rounded-lg p-2">
                <div className="text-lg font-bold text-blue-400">
                  {formatUtilizationRate(dailyTrend.average_rate)}
                </div>
                <div className="text-xs text-gray-400">平均利用率</div>
              </div>

              <div className="text-center bg-gray-700/50 rounded-lg p-2">
                <div className="text-lg font-bold text-green-400">
                  {formatUtilizationRate(dailyTrend.max_rate)}
                </div>
                <div className="text-xs text-gray-400">最高利用率</div>
              </div>

              <div className="text-center bg-gray-700/50 rounded-lg p-2">
                <div className="text-lg font-bold text-red-400">
                  {formatUtilizationRate(dailyTrend.min_rate)}
                </div>
                <div className="text-xs text-gray-400">最低利用率</div>
              </div>

              <div className="text-center bg-gray-700/50 rounded-lg p-2">
                <div className="text-lg font-bold text-purple-400">
                  {dailyTrend.daily_data.length}
                </div>
                <div className="text-xs text-gray-400">统计天数</div>
              </div>
            </div>

            {/* 柱状图 */}
            <div className="space-y-2">
              {dailyTrend.daily_data.map((day, index) => (
                <div key={day.date} className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-16 text-xs text-gray-400">
                    {new Date(day.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="text-sm font-medium text-white">
                        {formatUtilizationRate(day.utilization_rate)}
                      </div>
                      <div className="text-xs text-gray-400">
                        ({day.active_devices}/{day.total_devices} 台设备)
                      </div>
                    </div>

                    {/* 柱状图条 */}
                    <div className="w-full h-6 bg-gray-700 rounded-full overflow-hidden relative">
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-300 flex items-center justify-end pr-2"
                        style={{ width: `${Math.min(day.utilization_rate, 100)}%` }}
                      >
                        {day.utilization_rate > 15 && (
                          <span className="text-xs font-medium text-white">
                            {formatUtilizationRate(day.utilization_rate)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex-shrink-0 text-right">
                    <div className="text-sm font-medium text-green-400">
                      {day.productive_hours.toFixed(1)}h
                    </div>
                    <div className="text-xs text-gray-400">生产时间</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 班次利用率分析柱状图 */}
      {shiftTrend && (
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-white">
              🕐 班次利用率分析
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {/* 班次平均利用率 */}
            {Object.keys(shiftTrend.shift_averages || {}).length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                {Object.entries(shiftTrend.shift_averages || {}).map(([shiftName, avgRate]: [string, any]) => (
                  <div key={shiftName} className="bg-gray-700/50 rounded-lg p-3 text-center">
                    <div className="text-lg font-bold text-blue-400 mb-1">
                      {formatUtilizationRate(avgRate)}
                    </div>
                    <div className="text-xs text-gray-400">{shiftName} 平均</div>
                  </div>
                ))}
              </div>
            )}

            {/* 每日班次柱状图 */}
            <div className="space-y-3">
              {shiftTrend.daily_shift_data?.slice(-7).map((dayData: any) => (
                <div key={dayData.date} className="border border-gray-700 rounded-lg p-3">
                  <div className="text-sm font-medium text-white mb-3">
                    {new Date(dayData.date).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>

                  <div className="space-y-2">
                    {Object.entries(dayData.shift_rates || {}).map(([shiftName, rate]: [string, any]) => (
                      <div key={shiftName} className="flex items-center gap-3">
                        <div className="flex-shrink-0 w-12 text-xs text-gray-400">
                          {shiftName}
                        </div>
                        <div className="flex-1">
                          <div className="w-full h-4 bg-gray-600 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-orange-500 to-orange-400 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min(rate, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                        <div className="flex-shrink-0 w-12 text-xs text-right text-orange-400">
                          {formatUtilizationRate(rate)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* 空数据提示 */}
            {(!shiftTrend.daily_shift_data || shiftTrend.daily_shift_data.length === 0) && (
              <div className="text-center py-8 text-gray-400">
                <div className="text-sm">暂无班次数据</div>
                <div className="text-xs mt-1">请检查工作时间设置和数据采集状态</div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
