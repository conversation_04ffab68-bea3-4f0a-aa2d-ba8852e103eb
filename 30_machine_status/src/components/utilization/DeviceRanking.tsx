/**
 * 设备利用率排名组件
 * 
 * 显示设备利用率排名列表，包括：
 * 1. 设备基本信息（名称、位置、品牌等）
 * 2. 利用率百分比和排名
 * 3. 状态构成可视化条形图
 * 4. 生产时间统计
 * 5. 状态图例说明
 * 
 * 特性：
 * - 响应式布局，适配不同屏幕尺寸
 * - 状态颜色编码，直观显示设备状态
 * - 悬停提示，显示详细信息
 * - 紧凑布局，适合全屏显示
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-12
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useRouter } from 'next/navigation';

// 数据类型定义
interface StatusDetail {
  duration: number | null | undefined; // 允许null和undefined值
  percentage: number | null | undefined; // 允许null和undefined值
  color: string;
}

interface StatusComposition {
  production: StatusDetail;
  idle: StatusDetail;
  fault: StatusDetail;
  adjusting: StatusDetail;
  shutdown: StatusDetail;
  disconnected: StatusDetail;
}

interface DeviceUtilization {
  device_id: string;
  device_name: string;
  device_code: string;
  location: string;
  brand: string;
  model: string;
  utilization_rate: number | null | undefined; // 允许null和undefined值
  status_composition: StatusComposition;
  total_working_time: number | null | undefined; // 允许null和undefined值
  productive_time: number | null | undefined; // 允许null和undefined值
  idle_time: number | null | undefined; // 允许null和undefined值
  fault_time: number | null | undefined; // 允许null和undefined值
  rank: number;
}

interface DeviceRankingData {
  devices: DeviceUtilization[];
  status_legend: Array<{
    status: string;
    label: string;
    color: string;
  }>;
  work_time_settings: any;
  last_updated: string;
}

interface DeviceRankingProps {
  deviceRanking: DeviceRankingData | null;
  isRealtime?: boolean; // 是否为实时计算模式
  displayLimit?: number; // 显示数量限制，-1表示不限制
  sortOrder?: "asc" | "desc"; // 排序方式：asc(正序) 或 desc(倒序)
  title?: string; // 自定义标题
  showCard?: boolean; // 是否显示卡片容器，默认true
  loading?: boolean; // 是否正在加载数据
  queryDate?: string; // 查询日期，用于设备详细页面跳转
}

// 格式化利用率百分比
const formatUtilizationRate = (rate: number | null | undefined): string => {
  // 处理 null、undefined 或无效值的情况
  if (rate === null || rate === undefined || isNaN(rate)) {
    return '0.0%';
  }

  // 确保数值在合理范围内（0-100%）
  const validRate = Math.max(0, Math.min(100, rate));
  return `${validRate.toFixed(1)}%`;
};

// 格式化时间显示（小时转换为小时分钟格式）
const formatTimeDisplay = (hours: number | null | undefined): string => {
  // 处理 null、undefined 或无效值的情况
  if (hours === null || hours === undefined || isNaN(hours)) {
    return '0h';
  }

  // 确保数值为非负数
  const validHours = Math.max(0, hours);
  const wholeHours = Math.floor(validHours);
  const minutes = Math.round((validHours - wholeHours) * 60);

  if (minutes === 0) {
    return `${wholeHours}h`;
  } else {
    return `${wholeHours}h${minutes}m`;
  }
};

// 状态英文转中文映射
const statusTranslation: { [key: string]: string } = {
  production: '生产',
  idle: '待机',
  fault: '报警',
  shutdown: '停机',
};

// 格式化秒数为时:分:秒格式
const formatSecondsToHMS = (seconds: number | null | undefined): string => {
  // 处理 null、undefined 或无效值的情况
  if (seconds === null || seconds === undefined || isNaN(seconds)) {
    return '0:00:00';
  }

  // 确保数值为非负数
  const validSeconds = Math.max(0, Math.floor(seconds));

  const hours = Math.floor(validSeconds / 3600);
  const minutes = Math.floor((validSeconds % 3600) / 60);
  const remainingSeconds = validSeconds % 60;

  // 格式化为 时:分:秒，分钟和秒数补零
  return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export default function DeviceRanking({
  deviceRanking,
  isRealtime = false,
  displayLimit = 20,
  sortOrder = "desc",
  title,
  showCard = true,
  loading = false,
  queryDate
}: DeviceRankingProps) {
  // 根据是否实时计算决定标题，如果传入了自定义标题则使用自定义标题
  const displayTitle = title || (isRealtime ? "🏆 今日设备实时利用率排名" : "🏆 各设备利用率排名");

  // 路由导航
  const router = useRouter();

  /**
   * 处理状态条点击事件
   * 在新标签页中打开设备状态详细页面
   */
  const handleStatusBarClick = (deviceId: string) => {
    // 使用传入的查询日期，如果没有则使用当前日期
    const targetDate = queryDate || new Date().toISOString().split('T')[0];
    const detailUrl = `/device_status_detail_v2/${deviceId}?date=${targetDate}`;
    console.log(`🔗 在新标签页打开设备状态详细页面: ${detailUrl}`);
    window.open(detailUrl, '_blank');
  };

  // 渲染设备列表内容
  const renderDeviceList = () => {
    // 显示加载状态
    if (loading) {
      return (
        <div className="text-center py-8 text-gray-400">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <div className="text-sm">正在加载设备排名数据...</div>
          <div className="text-xs text-gray-500 mt-1">请稍候，正在获取设备利用率排名</div>
        </div>
      );
    }

    // 显示无数据状态
    if (!deviceRanking) {
      return (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">暂无设备排名数据</div>
          <div className="text-xs text-gray-500 mt-1">请检查时间范围设置或稍后重试</div>
        </div>
      );
    }

    return (
      <>
        {/* 设备排名列表 */}
        <div className="space-y-1">
          {(() => {
            // 根据排序方式处理设备列表
            let sortedDevices = [...deviceRanking.devices];
            if (sortOrder === "asc") {
              // 正序：利用率从低到高
              sortedDevices.sort((a, b) => {
                const rateA = a.utilization_rate ?? 0;
                const rateB = b.utilization_rate ?? 0;
                return rateA - rateB;
              });
            } else {
              // 倒序：利用率从高到低（默认）
              sortedDevices.sort((a, b) => {
                const rateA = a.utilization_rate ?? 0;
                const rateB = b.utilization_rate ?? 0;
                return rateB - rateA;
              });
            }

            // 应用显示数量限制
            const limitedDevices = displayLimit === -1 ? sortedDevices : sortedDevices.slice(0, displayLimit);

            return limitedDevices.map((device: DeviceUtilization, index: number) => {
              // 计算正确的排名编号
              let rankNumber;
              if (sortOrder === "asc") {
                // 正序：利用率最低的应该从总数倒数
                rankNumber = sortedDevices.length - index;
              } else {
                // 倒序：利用率最高的从1开始
                rankNumber = index + 1;
              }

              return (
                <div key={device.device_id} className="flex items-center gap-2 bg-gray-700/30 rounded p-1.5">
                  {/* 排名 */}
                  <div className="flex-shrink-0 w-5 h-5 rounded-full bg-gray-600 flex items-center justify-center text-xs font-bold">
                    {rankNumber}
                  </div>

                  {/* 设备信息 - 减少宽度为设备时间腾出空间 */}
                  <div className="flex-shrink-0 min-w-0 w-24">
                    <div className="font-medium text-white truncate text-xs" title={`${device.device_name} - ${device.location}`}>
                      {device.device_name}
                    </div>
                  </div>

                  {/* 利用率显示 */}
                  <div className="flex-shrink-0 w-10 text-center">
                    <div className="text-xs font-medium text-white">
                      {formatUtilizationRate(device.utilization_rate)}
                    </div>
                  </div>

                  {/* 状态构成条形图 - 添加点击导航功能 */}
                  <div className="flex-1 min-w-0">
                    <div
                      className="w-full h-4 bg-gray-600 rounded-full overflow-hidden flex cursor-pointer hover:ring-2 hover:ring-blue-400 hover:ring-opacity-50 transition-all duration-200"
                      onClick={() => handleStatusBarClick(device.device_id)}
                      title="点击查看设备状态详细分析"
                    >
                      {Object.entries(device.status_composition).map(([status, detail]) => {
                        const percentage = detail.percentage ?? 0;
                        const duration = detail.duration ?? 0;
                        if (percentage === 0) return null;
                        return (
                          <div
                            key={status}
                            className="h-full flex items-center justify-center text-xs font-medium text-white hover:brightness-110 transition-all duration-200"
                            style={{
                              width: `${percentage}%`,
                              backgroundColor: detail.color,
                              minWidth: percentage > 5 ? 'auto' : '0'
                            }}
                            title={`${statusTranslation[status] || status}: ${percentage.toFixed(1)}% (${formatSecondsToHMS(duration)}) - 点击查看详细分析`}
                          >
                            {percentage > 8 && `${percentage.toFixed(1)}%`}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 生产时间 - 增加宽度以容纳新的时间格式 */}
                  <div className="flex-shrink-0 text-right w-20">
                    <div className="text-xs font-medium text-green-400">
                      {formatTimeDisplay(device.productive_time)}/{formatTimeDisplay(device.total_working_time)}
                    </div>
                  </div>
                </div>
              );
            });
          })()}
        </div>

        {/* 显示更多设备提示 */}
        {(() => {
          const totalDevices = deviceRanking.devices.length;
          const actualDisplayLimit = displayLimit === -1 ? totalDevices : displayLimit;
          const isLimited = totalDevices > actualDisplayLimit;

          if (isLimited) {
            return (
              <div className="mt-2 text-center">
                <div className="text-xs text-gray-500">
                  显示{sortOrder === "asc" ? "利用率最低" : "利用率最高"}的{actualDisplayLimit}台设备，共{totalDevices}台设备
                </div>
              </div>
            );
          }
          return null;
        })()}
      </>
    );
  };

  // 如果不显示卡片，直接返回内容
  if (!showCard) {
    return <div>{renderDeviceList()}</div>;
  }

  // 卡片模式渲染
  return (
    <Card className="bg-gray-800 border-gray-700">
      {displayTitle && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-white">
              {displayTitle}
            </CardTitle>
            {/* 状态标识条 - 移到标题同一行，靠右对齐，移除断线和调机 */}
            <div className="flex items-center gap-2 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-gray-300">生产</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-gray-300">空闲</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-gray-300">故障</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                <span className="text-gray-300">停机</span>
              </div>
            </div>
          </div>
        </CardHeader>
      )}
      <CardContent className={displayTitle ? "pt-0" : "pt-3"}>
        {renderDeviceList()}
      </CardContent>
    </Card>
  );
}
