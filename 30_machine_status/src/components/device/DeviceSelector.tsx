'use client';

/**
 * 设备选择器组件
 * 
 * 允许用户搜索和选择设备
 */
import { useState, useEffect, useRef } from 'react';
import { Search, X, ChevronDown, ChevronUp } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Device {
  id: string;
  name: string;
  brand?: string;
  location?: string;
  model?: string;
  status?: string;
  quantity?: number;
  plan?: number;
}

interface DeviceSelectorProps {
  currentDeviceId: string;
  onDeviceChange: (deviceId: string) => void;
  selectedDate?: Date; // 添加当前选择的日期
}

export default function DeviceSelector({ currentDeviceId, onDeviceChange, selectedDate }: DeviceSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [devices, setDevices] = useState<Device[]>([]);
  const [filteredDevices, setFilteredDevices] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentDevice, setCurrentDevice] = useState<Device | null>(null);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // 获取设备列表
  useEffect(() => {
    const fetchDevices = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // 直接调用后端API，使用环境变量配置的地址
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
        const response = await fetch(`${API_BASE_URL}/api/machines`, {
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
          },
        });

        if (!response.ok) {
          throw new Error(`获取设备列表失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.machines && Array.isArray(data.machines)) {
          setDevices(data.machines);
          setFilteredDevices(data.machines);

          // 设置当前设备
          const current = data.machines.find((device: Device) => device.id === currentDeviceId);
          if (current) {
            setCurrentDevice(current);
          }
        } else {
          throw new Error('获取设备列表格式错误');
        }
      } catch (error: any) {
        console.error('获取设备列表失败:', error);
        setError(error.message || '获取设备列表失败');

        // 如果API失败，至少设置当前设备的基本信息
        setCurrentDevice({
          id: currentDeviceId,
          name: `设备 ${currentDeviceId}`,
          brand: '未知品牌'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDevices();
  }, [currentDeviceId]);



  // 处理搜索输入变化
  const handleSearchChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.trim() === '') {
      setFilteredDevices(devices);
    } else {
      // 直接调用后端API进行搜索
      try {
        setIsLoading(true);
        // 直接调用后端API，使用环境变量配置的地址
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

        // 后端搜索API使用 /api/machines 接口，然后在前端过滤
        const response = await fetch(`${API_BASE_URL}/api/machines`, {
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
          },
        });

        if (!response.ok) {
          throw new Error(`搜索失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.machines && Array.isArray(data.machines)) {
          // 在前端进行搜索过滤
          const filtered = data.machines.filter((device: Device) => {
            const searchLower = value.toLowerCase();
            return (
              device.name?.toLowerCase().includes(searchLower) ||
              device.brand?.toLowerCase().includes(searchLower) ||
              device.model?.toLowerCase().includes(searchLower) ||
              device.location?.toLowerCase().includes(searchLower) ||
              device.id?.toLowerCase().includes(searchLower)
            );
          });
          setFilteredDevices(filtered);
        } else {
          setFilteredDevices([]);
        }
      } catch (error: any) {
        console.error('搜索设备失败:', error);
        setError(error.message || '搜索失败');
        setFilteredDevices([]);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // 处理设备选择
  const handleDeviceSelect = (device: Device) => {
    setCurrentDevice(device);
    setIsOpen(false);
    onDeviceChange(device.id);

    // 更新URL，保持当前选择的日期
    const dateParam = selectedDate ? `?date=${selectedDate.toISOString().split('T')[0]}` : '';
    router.push(`/device_status_history/${device.id}${dateParam}`);
  };

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 清除搜索
  const clearSearch = () => {
    setSearchTerm('');
    setFilteredDevices(devices);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* 当前设备显示 */}
      <button
        className="flex items-center justify-between w-64 bg-gray-800 border border-gray-700 rounded-md px-3 py-2 text-sm"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center">
          {currentDevice ? (
            <span className="truncate">{currentDevice.name} ({currentDevice.brand || '未知品牌'})</span>
          ) : (
            <span className="text-gray-400">选择设备</span>
          )}
        </div>
        {isOpen ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute z-10 mt-1 w-80 bg-gray-800 border border-gray-700 rounded-md shadow-lg">
          {/* 搜索框 */}
          <div className="p-2 border-b border-gray-700">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索设备..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full bg-gray-900 border border-gray-700 rounded-md pl-8 pr-8 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              {searchTerm && (
                <button
                  onClick={clearSearch}
                  className="absolute right-2 top-2.5"
                >
                  <X className="h-4 w-4 text-gray-400 hover:text-white" />
                </button>
              )}
            </div>
          </div>

          {/* 设备列表 */}
          <div className="max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-400">加载设备列表中...</div>
            ) : error ? (
              <div className="p-4 text-center text-red-400">{error}</div>
            ) : filteredDevices.length === 0 ? (
              <div className="p-4 text-center text-gray-400">未找到匹配的设备</div>
            ) : (
              filteredDevices.map(device => (
                <button
                  key={device.id}
                  className={`w-full text-left px-4 py-2 hover:bg-gray-700 ${device.id === currentDeviceId ? 'bg-blue-900/30 text-blue-300' : ''}`}
                  onClick={() => handleDeviceSelect(device)}
                >
                  <div className="font-medium">{device.name}</div>
                  <div className="text-xs text-gray-400 flex justify-between">
                    <span>{device.brand || '未知品牌'}</span>
                    {device.location && <span>{device.location}</span>}
                  </div>
                  {device.model && (
                    <div className="text-xs text-gray-500">型号: {device.model}</div>
                  )}

                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
