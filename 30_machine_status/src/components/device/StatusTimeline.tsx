'use client';

/**
 * 状态时间线组件 - 正确设计版本
 *
 * 核心原则：
 * 1. 每个状态记录只在其开始时间所属的时间段中显示
 * 2. 状态条可以延伸到时间段边界外，但只在一个时间段中渲染
 * 3. tooltip始终显示完整的原始时间范围
 * 4. 简单直接的实现，确保一个状态一个色块
 */
import { useMemo } from 'react';

interface TimeSlot {
    label: string;
    start: string;
    end: string;
}

interface StatusRecord {
    id: string;
    deviceId: string;
    status: string;
    startTime: string;
    endTime: string;
    duration: number;
}

interface StatusTimelineProps {
    timeSlot: TimeSlot;
    statusHistory: StatusRecord[];
    statusColors: Record<string, string>;
    selectedDate: Date;
    timeSlotWidth?: number;
    timelineHeight?: number;
}

export default function StatusTimeline({
    timeSlot,
    statusHistory,
    statusColors,
    selectedDate,
    timeSlotWidth = 120,
    timelineHeight = 40
}: StatusTimelineProps) {

    // 将时间字符串转换为秒数（从当天00:00:00开始）
    const timeToSeconds = (timeStr: string): number => {
        const [hours, minutes] = timeStr.split(':').map(Number);
        return hours * 3600 + minutes * 60;
    };

    // 将Date对象转换为秒数（从当天00:00:00开始）
    // 支持跨日期工作时间，正确处理次日的时间戳
    const dateToSeconds = (date: Date, baseDate: Date): number => {
        const hours = date.getHours();
        const minutes = date.getMinutes();
        const seconds = date.getSeconds();

        // 计算日期差异（支持跨日期）
        const baseDateStr = baseDate.toISOString().split('T')[0]; // YYYY-MM-DD
        const currentDateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD

        // 计算天数偏移
        let dayOffset = 0;
        if (currentDateStr > baseDateStr) {
            // 次日的时间，添加24小时偏移
            dayOffset = 24 * 3600;
        } else if (currentDateStr < baseDateStr) {
            // 前一天的时间，减去24小时偏移（理论上不应该出现）
            dayOffset = -24 * 3600;
        }

        const totalSeconds = hours * 3600 + minutes * 60 + seconds + dayOffset;

        // 调试日志
        if (dayOffset !== 0) {
            console.log(`🕐 跨日期时间转换: ${date.toISOString()} -> ${totalSeconds}秒 (基准日期: ${baseDateStr}, 偏移: ${dayOffset / 3600}小时)`);
        }

        return totalSeconds;
    };

    // 工具函数 - 需要在useMemo之前定义
    const formatTime = (date: Date): string => {
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    };

    // 计算时间段范围（秒）
    const slotStartSeconds = timeToSeconds(timeSlot.start);
    const slotEndSeconds = timeToSeconds(timeSlot.end) + (timeToSeconds(timeSlot.end) < slotStartSeconds ? 24 * 3600 : 0);
    const slotTotalSeconds = slotEndSeconds - slotStartSeconds;

    // 处理状态记录 - 使用简化的重叠检测逻辑
    const visibleStatusRecords = useMemo(() => {
        if (!statusHistory?.length) return [];

        console.log(`🔍 时间段 ${timeSlot.label}: ${slotStartSeconds}-${slotEndSeconds}秒`);

        const records: any[] = [];

        statusHistory.forEach(record => {
            const recordStart = new Date(record.startTime);
            const recordEnd = new Date(record.endTime);

            // 将时间转换为当日的秒数，正确处理UTC时间转换
            // UTC时间会自动转换为本地时间（北京时间）
            const recordStartSeconds = recordStart.getHours() * 3600 + recordStart.getMinutes() * 60 + recordStart.getSeconds();
            const recordEndSeconds = recordEnd.getHours() * 3600 + recordEnd.getMinutes() * 60 + recordEnd.getSeconds();

            // 调试日志：显示时间转换结果
            console.log(`  🕐 记录时间: ${record.startTime} -> 本地${recordStart.getHours()}:${recordStart.getMinutes()}:${recordStart.getSeconds()} (${recordStartSeconds}秒)`);
            console.log(`    📊 完整状态: ${record.status} ${formatTime(recordStart)}-${formatTime(recordEnd)}`);

            // 简单的重叠检测：只处理同一天内的状态
            let hasOverlap = false;
            let effectiveStartSeconds = recordStartSeconds;
            let effectiveEndSeconds = recordEndSeconds;

            // 检查是否跨日期
            if (recordEndSeconds < recordStartSeconds) {
                // 跨日期状态：只在开始时间所在的时间段显示
                if (recordStartSeconds >= slotStartSeconds && recordStartSeconds < slotEndSeconds) {
                    hasOverlap = true;
                    effectiveStartSeconds = Math.max(recordStartSeconds, slotStartSeconds);
                    effectiveEndSeconds = Math.min(24 * 3600, slotEndSeconds); // 到当日结束
                }
            } else {
                // 正常状态：检查是否与当前时间段重叠
                hasOverlap = recordStartSeconds < slotEndSeconds && recordEndSeconds > slotStartSeconds;
                if (hasOverlap) {
                    effectiveStartSeconds = Math.max(recordStartSeconds, slotStartSeconds);
                    effectiveEndSeconds = Math.min(recordEndSeconds, slotEndSeconds);

                    // 特殊处理：如果状态在工作时间开始之前就开始了，只在第一个时间段显示
                    // 检查是否是第一个时间段（通过时间段标签判断）
                    const isFirstTimeSlot = timeSlot.label.startsWith('09:00');
                    if (recordStartSeconds < 9 * 3600 && !isFirstTimeSlot) {
                        // 状态在09:00之前开始，但当前不是第一个时间段，跳过
                        hasOverlap = false;
                    }
                }
            }

            if (!hasOverlap) return;

            // 记录状态类型用于调试
            if (recordStartSeconds >= slotStartSeconds && recordEndSeconds <= slotEndSeconds) {
                console.log(`  📊 完整状态: ${record.status} ${formatTime(recordStart)}-${formatTime(recordEnd)}`);
            } else {
                console.log(`  📊 跨时间段状态: ${record.status} ${formatTime(recordStart)}-${formatTime(recordEnd)}`);
            }

            // 计算在时间段内的位置（百分比）
            const position = ((effectiveStartSeconds - slotStartSeconds) / slotTotalSeconds) * 100;

            // 计算宽度
            const segmentDurationSeconds = effectiveEndSeconds - effectiveStartSeconds;
            const width = (segmentDurationSeconds / slotTotalSeconds) * 100;

            if (width > 0) {
                records.push({
                    ...record,
                    position: Math.max(0, position),
                    width: Math.max(0.1, width),
                    originalStart: recordStart,
                    originalEnd: recordEnd,
                    segmentKey: `${record.id || record.startTime}-${timeSlot.label}-${effectiveStartSeconds}`
                });
            }
        });

        return records;
    }, [statusHistory, timeSlot.label, slotStartSeconds, slotEndSeconds, slotTotalSeconds, selectedDate]);

    // 其他工具函数
    const calculateDuration = (start: Date, end: Date): number => {
        return Math.round((end.getTime() - start.getTime()) / 1000); // 返回秒数
    };

    const formatDuration = (totalSeconds: number): string => {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        const parts = [];
        if (hours > 0) parts.push(`${hours}时`);
        if (minutes > 0) parts.push(`${minutes}分`);
        if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`);

        return parts.join('');
    };

    const getStatusColor = (status: string): string => {
        const colors = {
            production: '#22c55e',
            idle: '#f59e0b',
            fault: '#ef4444',
            adjusting: '#3b82f6',
            shutdown: '#6b7280',
            disconnected: '#9ca3af',
            maintenance: '#8b5cf6',
            debugging: '#06b6d4'
        };
        return statusColors?.[status] || (colors as any)[status] || '#6b7280';
    };

    const getStatusName = (status: string): string => {
        const names = {
            production: '生产中',
            idle: '空闲',
            fault: '故障',
            adjusting: '调机',
            shutdown: '关机',
            disconnected: '未连接',
            maintenance: '维护',
            debugging: '调试'
        };
        return (names as any)[status] || status;
    };

    const getTooltip = (record: any): string => {
        // 优先使用API返回的tooltip（包含北京时间）
        if (record.tooltip) {
            return record.tooltip;
        }

        // 如果没有API tooltip，则使用原来的逻辑作为备用
        const statusName = getStatusName(record.status);
        const startTime = formatTime(record.originalStart);
        const endTime = formatTime(record.originalEnd);
        const duration = calculateDuration(record.originalStart, record.originalEnd);
        const formattedDuration = formatDuration(duration);

        return `${statusName}: ${startTime} - ${endTime} (${formattedDuration})`;
    };

    console.log(`🎯 时间段 ${timeSlot.label} 显示 ${visibleStatusRecords.length} 个状态条`);

    // 调试：检查前几个状态条的样式属性和时间段标签
    console.log(`🏷️ 当前时间段标签: "${timeSlot.label}" (类型: ${typeof timeSlot.label})`);
    console.log(`🔍 标签包含09:00? ${timeSlot.label.includes('09:00')}`);
    console.log(`🔍 标签等于'09:00-11:00'? ${timeSlot.label === '09:00-11:00'}`);

    if (timeSlot.label.includes('09:00') && visibleStatusRecords.length > 0) {
        console.log('🔍 09:00-11:00时间段前5个状态条样式:');
        visibleStatusRecords.slice(0, 5).forEach((record, index) => {
            console.log(`  状态条 ${index + 1}: left=${record.position}%, width=${record.width}%, color=${getStatusColor(record.status)}, status=${record.status}, currentStatus=${record.currentStatus}`);
            console.log(`    原始数据:`, record);
        });

        // 添加测试状态条用于验证渲染
        console.log('🎯 添加测试状态条用于验证渲染');
    }

    // 特别检查09:00时间段
    if (timeSlot.label.includes('09:00')) {
        console.log('🚨 这是09:00时间段，应该显示测试状态条！');
    }

    return (
        <div className="border-b border-gray-800">
            <div className="flex items-stretch">
                {/* 时间段标签 */}
                <div
                    className="bg-gray-950 text-xs font-medium flex items-center justify-end pr-2 flex-shrink-0 whitespace-nowrap"
                    style={{ width: `${timeSlotWidth}px` }}
                >
                    {timeSlot.label}
                </div>

                {/* 状态时间线 */}
                <div
                    className="flex-1 relative overflow-hidden bg-gray-700"
                    style={{ height: `${timelineHeight}px` }}
                >
                    {visibleStatusRecords.map((record, index) => (
                        <div
                            key={`${record.segmentKey || `${record.id}-${record.startTime}`}-${timeSlot.label}`}
                            className="absolute top-0 h-full cursor-pointer hover:brightness-110 transition-all duration-200"
                            style={{
                                left: `${record.position}%`,
                                width: `${Math.max(record.width, 0.5)}%`, // 确保最小宽度为0.5%
                                backgroundColor: getStatusColor(record.status),
                                minWidth: '8px', // 进一步增加最小宽度
                                zIndex: 1,
                                // 强化可见性
                                border: '1px solid rgba(0,0,0,0.2)', // 使用深色边框增加对比度
                                minHeight: '36px',
                                opacity: 1, // 完全不透明
                                boxShadow: '0 1px 2px rgba(0,0,0,0.1)' // 添加阴影增加立体感
                            }}
                            title={getTooltip(record)}
                        >
                            {/* 移除状态条上的文字显示，只保留颜色和tooltip */}
                        </div>
                    ))}

                    {/* 测试状态条：在所有时间段显示，用于验证渲染 */}
                    {true && (
                        <div
                            className="absolute top-0 h-full"
                            title="🔴 测试状态条 - 验证渲染正常"
                            style={{
                                left: '0%',
                                width: '10%',
                                backgroundColor: '#ff0000', // 红色，非常明显
                                zIndex: 100,
                                border: '3px solid #000000',
                                minHeight: '36px',
                                opacity: 0.8
                            }}
                        />
                    )}

                    {visibleStatusRecords.length === 0 && (
                        <div className="h-full flex items-center justify-center text-xs text-gray-400">
                            无记录
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}