'use client';

/**
 * 多选设备选择器组件
 *
 * 允许用户从下拉列表中选择多个设备
 */
import { useState, useEffect, useRef } from 'react';
import { Check, ChevronDown, X, Search } from 'lucide-react';
import { Machine } from '@/types';

interface MultiDeviceSelectorProps {
  machines: Machine[];
  selectedDeviceIds: string[];
  onDeviceSelectionChange: (deviceIds: string[]) => void;
  className?: string;
}

export default function MultiDeviceSelector({
  machines,
  selectedDeviceIds,
  onDeviceSelectionChange,
  className = ''
}: MultiDeviceSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 过滤设备列表
  const filteredMachines = machines.filter(machine => {
    const term = searchTerm.toLowerCase();
    return (
      machine.id.toLowerCase().includes(term) ||
      machine.brand.toLowerCase().includes(term) ||
      machine.name.toLowerCase().includes(term) ||
      machine.location.toLowerCase().includes(term) ||
      machine.model.toLowerCase().includes(term)
    );
  });

  // 切换设备选择状态
  const toggleDeviceSelection = (deviceId: string) => {
    if (selectedDeviceIds.includes(deviceId)) {
      // 如果已选中，则移除
      const newSelection = selectedDeviceIds.filter(id => id !== deviceId);
      onDeviceSelectionChange(newSelection);
    } else {
      // 如果未选中，则添加
      const newSelection = [...selectedDeviceIds, deviceId];
      onDeviceSelectionChange(newSelection);
    }
  };

  // 清除所有选择
  const clearSelection = () => {
    onDeviceSelectionChange([]);
  };

  // 选择所有设备
  const selectAllDevices = () => {
    const allDeviceIds = machines.map(machine => machine.id);
    onDeviceSelectionChange(allDeviceIds);
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    // 添加 ESC 键关闭下拉框
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    // 只有在下拉框打开时才添加事件监听器
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  // 获取选中设备的显示文本
  const getSelectionDisplayText = () => {
    if (selectedDeviceIds.length === 0) {
      return '选择设备...';
    } else if (selectedDeviceIds.length === machines.length) {
      return '所有设备';
    } else if (selectedDeviceIds.length === 1) {
      const selectedMachine = machines.find(m => m.id === selectedDeviceIds[0]);
      return selectedMachine ? selectedMachine.name : '1 台设备';
    } else {
      return `${selectedDeviceIds.length} 台设备`;
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* 选择器按钮 */}
      <button
        className="flex items-center justify-between w-full bg-gray-800 text-white px-3 py-2 rounded-md border border-gray-700 hover:bg-gray-700 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="truncate">{getSelectionDisplayText()}</span>
        <div className="flex items-center">
          {selectedDeviceIds.length > 0 && (
            <button
              className="p-1 hover:bg-gray-600 rounded-full mr-1"
              onClick={(e) => {
                e.stopPropagation();
                clearSelection();
              }}
              title="清除选择"
            >
              <X className="h-3 w-3" />
            </button>
          )}
          <ChevronDown className="h-4 w-4 ml-1" />
        </div>
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute z-[9999] mt-1 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-96 overflow-auto">
          {/* 搜索框 */}
          <div className="p-2 border-b border-gray-700 sticky top-0 bg-gray-800 z-50">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                className="w-full bg-gray-700 text-white pl-8 pr-4 py-1 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="搜索设备..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>

          {/* 全选/清除按钮 */}
          <div className="flex justify-between p-2 border-b border-gray-700 sticky top-10 bg-gray-800 z-50">
            <button
              className="text-xs text-blue-400 hover:text-blue-300"
              onClick={(e) => {
                e.stopPropagation();
                selectAllDevices();
              }}
            >
              全选
            </button>
            <button
              className="text-xs text-blue-400 hover:text-blue-300"
              onClick={(e) => {
                e.stopPropagation();
                clearSelection();
              }}
            >
              清除
            </button>
          </div>

          {/* 设备列表 */}
          <div className="py-1 bg-gray-800">
            {filteredMachines.length === 0 ? (
              <div className="px-4 py-2 text-sm text-gray-400 text-center">
                没有找到匹配的设备
              </div>
            ) : (
              filteredMachines.map(machine => (
                <div
                  key={machine.id}
                  className={`flex items-center px-4 py-2 hover:bg-gray-700 cursor-pointer ${selectedDeviceIds.includes(machine.id) ? 'bg-gray-700' : 'bg-gray-800'
                    }`}
                  onClick={() => toggleDeviceSelection(machine.id)}
                >
                  <div className={`w-4 h-4 border rounded mr-2 flex items-center justify-center ${selectedDeviceIds.includes(machine.id) ? 'bg-blue-500 border-blue-500' : 'border-gray-500'
                    }`}>
                    {selectedDeviceIds.includes(machine.id) && (
                      <Check className="h-3 w-3 text-white" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{machine.name}</div>
                    <div className="text-xs text-gray-400 truncate">
                      {machine.id} | {machine.location}
                    </div>
                  </div>
                  <div className={`ml-2 w-2 h-2 rounded-full ${machine.status === 'production' ? 'bg-green-500' :
                    machine.status === 'idle' ? 'bg-amber-500' :
                      machine.status === 'fault' ? 'bg-red-500' :
                        machine.status === 'adjusting' ? 'bg-blue-500' :
                          'bg-gray-500'
                    }`}></div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
