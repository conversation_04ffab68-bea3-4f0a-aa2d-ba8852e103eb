'use client'; // 声明这是一个客户端组件

/**
 * 设备网格布局组件
 *
 * 该组件负责以网格形式展示所有设备卡片
 * 实现了响应式布局，在不同屏幕尺寸下显示不同列数的卡片
 * 当设备数据更新时，添加平滑过渡动画
 */
import { Machine } from '@/types';
import MachineCard from '@/components/features/MachineCard';
import { useState, useEffect, useRef } from 'react';
import { useWindowSize } from '@/hooks/useWindowSize';

/**
 * MachineGrid组件的属性接口
 */
interface MachineGridProps {
  machines: Machine[];     // 设备数据数组
  gridGap?: number;        // 设备网格间隔（像素）
  rowGap?: number;         // 设备行间隔（像素）
  columnGap?: number;      // 设备列间隔（像素）
  apiError?: boolean;      // API请求是否失败
  displaySettings?: any;   // 显示设置
}

export default function MachineGrid({
  machines,
  gridGap = 12,
  rowGap,
  columnGap,
  apiError = false,
  displaySettings
}: MachineGridProps) {
  // 状态管理
  // 使用状态跟踪是否正在动画过渡中
  const [isAnimating, setIsAnimating] = useState(false);
  // 获取窗口大小
  const windowSize = useWindowSize();
  // 网格容器引用，用于测量容器宽度
  const gridRef = useRef<HTMLDivElement>(null);
  // 动态计算的列数
  const [columnsCount, setColumnsCount] = useState(5);

  /**
   * 当设备列表变化时，添加平滑过渡效果
   * 通过短暂降低透明度来实现视觉上的平滑过渡
   */
  useEffect(() => {
    // 只有当有设备数据时才应用动画
    if (machines.length > 0) {
      // 开始动画
      setIsAnimating(true);
      // 设置定时器，300毫秒后结束动画
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 300);
      // 清理函数，防止内存泄漏
      return () => clearTimeout(timer);
    }
  }, [machines]); // 依赖项：设备数组

  /**
   * 根据容器宽度和卡片宽度动态计算列数
   * 使用ResizeObserver监听容器宽度变化
   */
  useEffect(() => {
    if (!gridRef.current) return;

    // 创建一个函数来计算列数
    const calculateColumns = () => {
      if (!gridRef.current) return;

      // 获取容器宽度
      const containerWidth = gridRef.current.clientWidth;
      // 设置最小卡片宽度（像素）- 使用displaySettings中的值或默认值
      const minCardWidth = displaySettings?.cardStyle?.cardWidth || 160;
      // 计算可以放置的列数（考虑间隔）
      const actualColumnGap = columnGap !== undefined ? columnGap : gridGap;

      // 计算公式：(容器宽度 + 间隔) / (卡片宽度 + 间隔)
      // 这样可以确保最后一个卡片后面不需要间隔
      const calculatedColumns = Math.floor((containerWidth + actualColumnGap) / (minCardWidth + actualColumnGap));

      // 确保至少有1列，最多有10列
      const newColumnsCount = Math.max(1, Math.min(10, calculatedColumns));

      // 打印调试信息
      console.log(`容器宽度: ${containerWidth}px, 卡片宽度: ${minCardWidth}px, 间隔: ${actualColumnGap}px, 计算列数: ${calculatedColumns}, 最终列数: ${newColumnsCount}`);

      // 只有当列数变化时才更新状态
      if (newColumnsCount !== columnsCount) {
        setColumnsCount(newColumnsCount);
      }
    };

    // 初始计算
    calculateColumns();

    // 创建ResizeObserver来监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      calculateColumns();
    });

    // 开始观察容器
    resizeObserver.observe(gridRef.current);

    // 清理函数
    return () => {
      if (gridRef.current) {
        resizeObserver.unobserve(gridRef.current);
      }
      resizeObserver.disconnect();
    };
  }, [gridGap, columnGap, displaySettings, columnsCount]); // 依赖间隔值和显示设置

  // 计算实际使用的间隔值
  const actualRowGap = rowGap !== undefined ? rowGap : gridGap;
  const actualColumnGap = columnGap !== undefined ? columnGap : gridGap;

  return (
    <div
      ref={gridRef}
      className={`
        grid p-0 w-full
        transition-opacity duration-300 ease-in-out
        ${isAnimating && machines.length > 0 ? 'opacity-95' : 'opacity-100'}
      `}
      style={{
        gap: `${gridGap}px`,
        rowGap: `${actualRowGap}px`,
        columnGap: `${actualColumnGap}px`,
        gridTemplateColumns: `repeat(${columnsCount}, minmax(0, 1fr))` // 动态设置列数
      }}
    >
      {/*
        自适应网格布局:
        - 根据容器宽度和卡片最小宽度动态计算列数
        - 确保卡片不会重叠或变形
        - 在大屏幕上自动增加列数，小屏幕上减少列数

        动画效果:
        - 数据更新时降低透明度实现平滑过渡
      */}

      {/* 遍历所有设备并渲染设备卡片 */}
      {machines.map((machine, index) => (
        <div
          key={machine.id} // 使用设备ID作为唯一键
          className="transition-all duration-300 ease-in-out w-full"
        >
          {/* 渲染单个设备卡片组件 */}
          <MachineCard
            machine={machine} // 传递设备数据
            index={index + 1}  // 传递1-based索引作为显示序号
            apiError={apiError} // 传递API错误状态
            cardContent={displaySettings?.cardContent}
            cardStyle={displaySettings?.cardStyle}
            statusColors={displaySettings?.statusColors}
          />
        </div>
      ))}
    </div>
  );
}
