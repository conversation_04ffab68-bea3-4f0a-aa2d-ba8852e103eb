'use client'; // 声明这是一个客户端组件

/**
 * 页面顶部标题栏组件
 *
 * 该组件显示在页面顶部，包含公司信息、日期时间和操作按钮
 * 提供应用程序的基本信息和全局操作入口
 */
import { CompanyInfo } from '@/types';
import { Settings, Maximize, Menu } from 'lucide-react'; // 导入图标

/**
 * Header组件的属性接口
 */
interface HeaderProps {
  companyInfo: CompanyInfo; // 公司信息对象，包含名称和日期时间
  isMaximized?: boolean; // 是否处于最大化状态
  toggleMaximize?: () => void; // 切换最大化状态的回调函数
  toggleSidebar: () => void; // 切换侧边栏显示模式的回调函数
  sidebarMode?: any; // 侧边栏模式（可选，用于兼容性）
}

export default function Header({
  companyInfo,
  isMaximized = false,
  toggleMaximize,
  toggleSidebar,
  sidebarMode
}: HeaderProps) {
  // 添加调试日志
  console.log('Header组件渲染');
  return (
    <header className="w-full bg-gray-900 text-white shadow-md px-6 py-3">
      <div className="flex items-center justify-between">
        {/* 左侧区域：侧边栏切换按钮和公司名称 */}
        <div className="flex items-center">
          {/* 侧边栏切换按钮 */}
          <button
            className="p-2 mr-3 rounded-full hover:bg-gray-700"
            onClick={toggleSidebar}
            aria-label="切换侧边栏"
            title="切换侧边栏"
          >
            {/* 侧边栏切换图标 */}
            <Menu className="h-5 w-5" />
          </button>

          {/* 公司名称 */}
          <h1 className="text-xl font-bold">{companyInfo.name}</h1>
        </div>

        {/* 右侧区域：分为两行 */}
        <div className="flex flex-col items-end">
          {/* 日期时间 - 上方一行，使用等宽字体和固定宽度防止抖动 */}
          <span className="text-xs text-gray-400 mb-1 font-mono w-[160px] text-right inline-block">{companyInfo.dateTime}</span>

          {/* 技术支持信息和操作按钮 - 下方一行 */}
          <div className="flex items-center space-x-3">
            {/* 技术支持信息 */}
            <div className="flex items-center space-x-1">
              <span className="text-sm">{companyInfo.support || ''}</span>
              <span className="text-xs text-gray-400">{companyInfo.support_info || ''}</span>
            </div>

            {/* 操作按钮组 */}
            <div className="flex space-x-2 ml-2">
              {/* 设置按钮 */}
              <button className="p-1.5 rounded-full hover:bg-gray-700" aria-label="设置">
                <Settings className="h-4 w-4" />
              </button>
              {/* 最大化/恢复按钮 */}
              <button
                className="p-1.5 rounded-full hover:bg-gray-700"
                aria-label={isMaximized ? "退出全屏" : "全屏显示"}
                onClick={toggleMaximize}
                title={isMaximized ? "退出全屏" : "全屏显示"}
              >
                <Maximize className={`h-4 w-4 ${isMaximized ? 'rotate-45' : ''} transition-transform`} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
