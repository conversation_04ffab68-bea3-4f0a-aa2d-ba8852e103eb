'use client'; // 声明这是一个客户端组件

/**
 * 侧边栏导航组件
 *
 * 该组件提供应用程序的主导航菜单，可以展开和折叠
 * 包含应用程序的各个主要功能区域的入口
 */
import { Home, Gauge, Settings, Database, BarChart3, History, LineChart, Activity, TrendingUp, Clock, PieChart } from 'lucide-react'; // 导入各种图标
import { useRouter, usePathname } from 'next/navigation'; // 导入路由相关钩子

/**
 * 侧边栏显示模式枚举
 */
export enum SidebarMode {
  HIDDEN = 'hidden',   // 隐藏
  COLLAPSED = 'collapsed' // 折叠（只显示图标）
}

/**
 * Sidebar组件的属性接口
 */
interface SidebarProps {
  mode: SidebarMode; // 侧边栏显示模式
  activePage?: string; // 当前激活的页面（可选）
}

export default function Sidebar({ mode, activePage }: SidebarProps) {
  // 添加调试日志
  console.log('Sidebar组件渲染，模式:', mode, '页面:', activePage);
  const router = useRouter(); // 获取路由器实例
  const pathname = usePathname(); // 获取当前路径

  /**
   * 菜单项配置数组
   * 每个菜单项包含图标、标签文本、路径和ID
   */
  const menuItems = [
    {
      id: 'home',
      icon: <Home size={20} />,
      label: '首页',
      path: '/'
    },
    {
      id: 'status',
      icon: <Gauge size={20} />,
      label: '设备状态',
      path: '/status'
    },
    {
      id: 'device_status_list',
      icon: <History size={20} />,
      label: '状态历史',
      path: '/device_status_list'
    },
    // 隐藏的导航项 - 根据用户要求暂时隐藏
    // {
    //   id: 'device_production_history',
    //   icon: <LineChart size={20} />,
    //   label: '产量历史',
    //   path: '/device_production_history'
    // },
    // {
    //   id: 'production_progress',
    //   icon: <Activity size={20} />,
    //   label: '生产进度',
    //   path: '/production_progress'
    // },
    // {
    //   id: 'production_progress_v2',
    //   icon: <TrendingUp size={20} />,
    //   label: '生产进度V2',
    //   path: '/production_progress_v2'
    // },
    // {
    //   id: 'data',
    //   icon: <Database size={20} />,
    //   label: '数据管理',
    //   path: '/data'
    // },
    // {
    //   id: 'device_usage_statistics',
    //   icon: <Clock size={20} />,
    //   label: '设备统计',
    //   path: '/device_usage_statistics'
    // },
    {
      id: 'device_utilization_dashboard',
      icon: <PieChart size={20} />,
      label: '利用率仪表盘',
      path: '/device_utilization_dashboard_v2'
    },
    // 隐藏的导航项 - 根据用户要求暂时隐藏
    // {
    //   id: 'analytics',
    //   icon: <BarChart3 size={20} />,
    //   label: '统计分析',
    //   path: '/analytics'
    // },
    // {
    //   id: 'settings',
    //   icon: <Settings size={20} />,
    //   label: '系统设置',
    //   path: '/settings'
    // },
  ];

  /**
   * 判断菜单项是否激活
   *
   * @param item - 菜单项
   * @returns 是否激活
   */
  const isActive = (item: typeof menuItems[0]) => {
    // 如果提供了activePage属性，则使用它来确定激活状态
    if (activePage) {
      return item.id === activePage;
    }

    // 如果pathname为null，返回false
    if (!pathname) {
      return false;
    }

    // 否则，根据当前路径确定激活状态
    if (item.path === '/' && pathname === '/') {
      return true;
    }

    // 精确匹配路径，避免 /production_progress 匹配 /production_progress_v2
    if (item.path !== '/') {
      // 完全匹配或者以路径+/开头（子路径）
      return pathname === item.path || pathname.startsWith(item.path + '/');
    }

    return false;
  };

  /**
   * 渲染侧边栏组件
   * 根据mode属性动态调整宽度和内容显示
   */
  // 如果模式是隐藏，则不渲染任何内容
  if (mode === SidebarMode.HIDDEN) {
    return null;
  }

  // 折叠状态下固定宽度为16px
  return (
    <div className="w-16 transition-all duration-300 h-screen bg-gray-800 flex flex-col">
      {/* 顶部标志区域 */}
      <div className="p-4 flex items-center justify-center border-b border-gray-700 h-16">
        {/* 折叠状态只显示一个字 */}
        <span className="text-lg font-bold">智</span>
      </div>

      {/* 菜单项列表区域 */}
      <div className="flex-1 py-4">
        {/* 遍历渲染所有菜单项 */}
        {menuItems.map((item) => (
          <div
            key={item.id} // 使用菜单项ID作为键
            // 点击时导航到对应路径，但不改变侧边栏状态
            onClick={async (e) => {
              e.preventDefault(); // 阻止默认行为

              try {
                // 立即保存当前侧边栏模式
                const { saveSidebarMode } = await import('@/utils/sidebarState');

                // 保存当前侧边栏模式，确保导航后状态不变
                console.log('菜单点击，保存侧边栏模式:', mode);
                saveSidebarMode(mode);

                // 等待一小段时间确保状态已保存
                await new Promise(resolve => setTimeout(resolve, 50));

                // 保存后再导航，确保状态已保存
                console.log('导航到:', item.path);
                router.push(item.path);
              } catch (error) {
                console.error('菜单点击处理失败:', error);
                // 出错时仍然导航
                router.push(item.path);
              }
            }}
            // 激活项使用蓝色背景，非激活项鼠标悬停时变色
            // 折叠状态下居中显示图标
            className={`
              flex items-center px-4 py-3 cursor-pointer justify-center
              ${isActive(item) ? 'bg-blue-600' : 'hover:bg-gray-700'}
            `}
          >
            {/* 菜单图标 - 折叠状态下只显示图标 */}
            <div className="text-white">
              {item.icon}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
