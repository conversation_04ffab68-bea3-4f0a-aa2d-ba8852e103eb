'use client';

/**
 * 日期选择器组件
 *
 * 允许用户选择日期
 */
import { useState, useRef, useEffect } from 'react';
import { Calendar } from 'lucide-react';
import { Locale } from 'date-fns';

interface DatePickerProps {
  selectedDate: Date | undefined;
  onChange: (date: Date) => void;
  className?: string;
  locale?: Locale;
  dateFormat?: string;
  calendarClassName?: string;
}

export default function DatePicker({
  selectedDate,
  onChange,
  className = '',
  locale,
  dateFormat,
  calendarClassName
}: DatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState<Date>(() => selectedDate ? new Date(selectedDate) : new Date());
  const calendarRef = useRef<HTMLDivElement>(null);

  // 格式化日期为 YYYY-MM-DD
  const formatDate = (date: Date | undefined): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  // 格式化日期为显示格式
  const formatDisplayDate = (date: Date | undefined): string => {
    if (!date) return '';
    return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
  };

  // 生成日历网格
  const generateCalendarGrid = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    // 获取当月第一天
    const firstDayOfMonth = new Date(year, month, 1);

    // 获取当月最后一天
    const lastDayOfMonth = new Date(year, month + 1, 0);

    // 获取当月第一天是星期几（0-6，0表示星期日）
    const firstDayOfWeek = firstDayOfMonth.getDay();

    // 获取当月天数
    const daysInMonth = lastDayOfMonth.getDate();

    // 生成日历网格
    const calendarDays = [];

    // 添加上个月的日期
    for (let i = 0; i < firstDayOfWeek; i++) {
      const prevMonthDay = new Date(year, month, -firstDayOfWeek + i + 1);
      calendarDays.push({
        date: prevMonthDay,
        isCurrentMonth: false,
        isToday: formatDate(prevMonthDay) === formatDate(new Date()),
        isSelected: selectedDate ? formatDate(prevMonthDay) === formatDate(selectedDate) : false
      });
    }

    // 添加当月的日期
    for (let i = 1; i <= daysInMonth; i++) {
      const currentDate = new Date(year, month, i);
      calendarDays.push({
        date: currentDate,
        isCurrentMonth: true,
        isToday: formatDate(currentDate) === formatDate(new Date()),
        isSelected: selectedDate ? formatDate(currentDate) === formatDate(selectedDate) : false
      });
    }

    // 添加下个月的日期，使日历网格填满 6 行
    const remainingDays = 42 - calendarDays.length;
    for (let i = 1; i <= remainingDays; i++) {
      const nextMonthDay = new Date(year, month + 1, i);
      calendarDays.push({
        date: nextMonthDay,
        isCurrentMonth: false,
        isToday: formatDate(nextMonthDay) === formatDate(new Date()),
        isSelected: selectedDate ? formatDate(nextMonthDay) === formatDate(selectedDate) : false
      });
    }

    return calendarDays;
  };

  // 切换到上个月
  const goToPreviousMonth = () => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() - 1);
      return newDate;
    });
  };

  // 切换到下个月
  const goToNextMonth = () => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + 1);
      return newDate;
    });
  };

  // 选择日期
  const selectDate = (date: Date) => {
    onChange(date);
    setIsOpen(false);
  };

  // 点击外部关闭日历
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 当选中的日期变化时，更新当前月份
  useEffect(() => {
    if (selectedDate) {
      setCurrentMonth(new Date(selectedDate));
    }
  }, [selectedDate]);

  return (
    <div className="relative">
      <button
        className={`flex items-center ${className}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <Calendar className="h-4 w-4 mr-2" />
        <span>{selectedDate ? formatDisplayDate(selectedDate) : '选择日期'}</span>
      </button>

      {isOpen && (
        <div
          ref={calendarRef}
          className="absolute z-10 mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg p-3 w-64 right-0"
        >
          <div className="flex items-center justify-between mb-2">
            <button
              className="p-1 rounded-full hover:bg-gray-700"
              onClick={goToPreviousMonth}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </button>
            <div className="text-sm font-medium">
              {currentMonth.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })}
            </div>
            <button
              className="p-1 rounded-full hover:bg-gray-700"
              onClick={goToNextMonth}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-7 gap-1 text-center text-xs">
            <div className="text-gray-400">日</div>
            <div className="text-gray-400">一</div>
            <div className="text-gray-400">二</div>
            <div className="text-gray-400">三</div>
            <div className="text-gray-400">四</div>
            <div className="text-gray-400">五</div>
            <div className="text-gray-400">六</div>

            {generateCalendarGrid().map((day, index) => (
              <button
                key={index}
                className={`p-1 rounded-full text-xs ${day.isSelected
                  ? 'bg-blue-600 text-white'
                  : day.isToday
                    ? 'bg-blue-900/30 text-blue-300'
                    : day.isCurrentMonth
                      ? 'hover:bg-gray-700'
                      : 'text-gray-500 hover:bg-gray-700'
                  }`}
                onClick={() => selectDate(day.date)}
              >
                {day.date.getDate()}
              </button>
            ))}
          </div>

          <div className="mt-2 text-center">
            <button
              className="text-xs text-blue-400 hover:text-blue-300"
              onClick={() => selectDate(new Date())}
            >
              今天
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
