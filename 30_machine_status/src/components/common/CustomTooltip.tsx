'use client';

import React, { useState, useRef, useEffect } from 'react';

interface TooltipProps {
  content: string;
  children: React.ReactNode;
  className?: string;
  delay?: number;
}

/**
 * 自定义Tooltip组件
 * 提供比原生HTML title属性更好的用户体验
 * 
 * @param content - tooltip显示的内容
 * @param children - 触发tooltip的子元素
 * @param className - 额外的CSS类名
 * @param delay - 显示延迟（毫秒），默认500ms
 */
export default function CustomTooltip({
  content,
  children,
  className = '',
  delay = 500
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理鼠标进入事件
  const handleMouseEnter = (event: React.MouseEvent) => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 计算tooltip位置
    const rect = event.currentTarget.getBoundingClientRect();
    const tooltipX = rect.left + rect.width / 2;
    const tooltipY = rect.top - 10; // 在元素上方显示

    setPosition({ x: tooltipX, y: tooltipY });

    // 延迟显示tooltip
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    // 清除定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // 立即隐藏tooltip
    setIsVisible(false);
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      className={className}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ position: 'relative' }}
    >
      {children}

      {/* Tooltip弹窗 */}
      {isVisible && (
        <div
          className="fixed z-50 pointer-events-none bg-gray-900 text-white text-xs px-3 py-2 rounded-lg shadow-lg border border-gray-700 max-w-xs whitespace-pre-line"
          style={{
            left: position.x,
            top: position.y,
            transform: 'translateX(-50%) translateY(-100%)'
          }}
        >
          {content}
          {/* 小箭头 */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2">
            <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        </div>
      )}
    </div>
  );
}
