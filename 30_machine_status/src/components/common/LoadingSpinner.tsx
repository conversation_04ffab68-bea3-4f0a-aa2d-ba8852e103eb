'use client';

/**
 * 加载中旋转器组件
 * 
 * 显示加载中状态
 */
interface LoadingSpinnerProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function LoadingSpinner({ message = '加载中...', size = 'md' }: LoadingSpinnerProps) {
  // 根据尺寸设置样式
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-8 w-8 border-4',
    lg: 'h-12 w-12 border-4'
  };
  
  const textClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };
  
  return (
    <div className="flex flex-col items-center justify-center p-8">
      <div 
        className={`${sizeClasses[size]} animate-spin rounded-full border-blue-500 border-t-transparent`}
      ></div>
      {message && (
        <p className={`mt-4 ${textClasses[size]} text-gray-300`}>{message}</p>
      )}
    </div>
  );
}
