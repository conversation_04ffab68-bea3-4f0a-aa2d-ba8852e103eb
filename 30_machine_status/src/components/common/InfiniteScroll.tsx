import React, { useRef, useEffect, useState, useCallback } from 'react';
import './InfiniteScroll.css';

interface InfiniteScrollProps {
  children: React.ReactNode;
  scrollSpeed?: number; // 像素/秒
  className?: string;
  pauseOnHover?: boolean;
  repeatCount?: number; // 重复次数，默认为3
}

export default function InfiniteScroll({
  children,
  scrollSpeed = 30,
  className = '',
  pauseOnHover = true,
  repeatCount = 3
}: InfiniteScrollProps) {
  // 容器引用
  const containerRef = useRef<HTMLDivElement>(null);
  // 内容引用
  const contentRef = useRef<HTMLDivElement>(null);
  // 滚动位置引用
  const scrollPositionRef = useRef(0);
  // 动画帧引用
  const animationFrameRef = useRef<number | null>(null);
  // 上次时间戳引用
  const lastTimestampRef = useRef<number | null>(null);
  // 是否暂停滚动
  const [isPaused, setIsPaused] = useState(false);
  // 内容高度
  const [contentHeight, setContentHeight] = useState(0);
  // 是否已初始化
  const [isInitialized, setIsInitialized] = useState(false);
  // 重复的内容数组
  const [repeatedChildren, setRepeatedChildren] = useState<React.ReactNode[]>([]);

  // 创建重复的子元素数组
  useEffect(() => {
    const childrenArray = React.Children.toArray(children);
    if (childrenArray.length === 0) return;

    const repeated: React.ReactNode[] = [];
    for (let i = 0; i < repeatCount; i++) {
      repeated.push(...childrenArray);
    }
    setRepeatedChildren(repeated);
  }, [children, repeatCount]);

  // 初始化和监听内容高度变化
  useEffect(() => {
    if (!containerRef.current || !contentRef.current) return;

    // 测量内容高度
    const updateHeight = () => {
      if (!contentRef.current) return;

      // 获取单个内容块的高度（不是整个重复内容的高度）
      const singleContentHeight = contentRef.current.children[0]?.clientHeight || 0;
      if (singleContentHeight > 0) {
        setContentHeight(singleContentHeight);
        setIsInitialized(true);
      }
    };

    // 初始更新
    updateHeight();

    // 创建 ResizeObserver 监听内容高度变化
    const resizeObserver = new ResizeObserver(() => {
      updateHeight();
    });

    // 开始观察内容容器
    if (contentRef.current.children[0]) {
      resizeObserver.observe(contentRef.current.children[0] as Element);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [repeatedChildren]);

  // 更新滚动位置
  const updateScrollPosition = useCallback(() => {
    if (!contentRef.current || !containerRef.current) return;

    // 应用滚动位置
    contentRef.current.style.transform = `translateY(-${scrollPositionRef.current}px)`;

    // 检查是否需要重置滚动位置
    if (contentHeight > 0 && scrollPositionRef.current >= contentHeight) {
      // 重置到相对位置，保持无缝滚动
      scrollPositionRef.current = scrollPositionRef.current % contentHeight;
      contentRef.current.style.transform = `translateY(-${scrollPositionRef.current}px)`;
    }
  }, [contentHeight]);

  // 滚动动画
  const animate = useCallback((timestamp: number) => {
    if (isPaused) {
      animationFrameRef.current = requestAnimationFrame(animate);
      return;
    }

    if (lastTimestampRef.current === null) {
      lastTimestampRef.current = timestamp;
      animationFrameRef.current = requestAnimationFrame(animate);
      return;
    }

    // 计算时间差（毫秒）
    const deltaTime = timestamp - lastTimestampRef.current;
    
    // 限制最大时间差，防止大跳跃
    const effectiveDeltaTime = Math.min(deltaTime, 100);
    
    // 更新时间戳
    lastTimestampRef.current = timestamp;

    // 计算滚动增量
    const scrollIncrement = (scrollSpeed * effectiveDeltaTime) / 1000;

    // 更新滚动位置
    scrollPositionRef.current += scrollIncrement;

    // 更新DOM
    updateScrollPosition();

    // 继续动画
    animationFrameRef.current = requestAnimationFrame(animate);
  }, [isPaused, scrollSpeed, updateScrollPosition]);

  // 启动滚动动画
  useEffect(() => {
    if (!isInitialized || contentHeight <= 0) return;

    // 开始动画
    animationFrameRef.current = requestAnimationFrame(animate);

    // 清理函数
    return () => {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      lastTimestampRef.current = null;
    };
  }, [isInitialized, contentHeight, animate]);

  // 处理鼠标悬停
  const handleMouseEnter = useCallback(() => {
    if (pauseOnHover) {
      setIsPaused(true);
    }
  }, [pauseOnHover]);

  const handleMouseLeave = useCallback(() => {
    setIsPaused(false);
    lastTimestampRef.current = null; // 重置时间戳，避免大跳跃
  }, []);

  // 检查children是否为空
  const hasChildren = React.Children.count(children) > 0;

  return (
    <div
      ref={containerRef}
      className={`infinite-scroll-container ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        ref={contentRef}
        className="infinite-scroll-content-wrapper"
        style={{
          transform: 'translateY(0px)',
          transition: 'none',
          willChange: 'transform'
        }}
      >
        {hasChildren ? (
          // 渲染重复的内容
          repeatedChildren.map((child, index) => (
            <div key={index} className="infinite-scroll-item">
              {child}
            </div>
          ))
        ) : (
          <div className="infinite-scroll-item">
            <div className="p-4 text-center text-gray-400">
              无内容可显示 - InfiniteScroll组件
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
