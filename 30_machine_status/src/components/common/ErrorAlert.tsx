'use client';

/**
 * 错误提示组件
 * 
 * 显示错误信息
 */
import { AlertCircle } from 'lucide-react';

interface ErrorAlertProps {
  message: string;
  onRetry?: () => void;
}

export default function ErrorAlert({ message, onRetry }: ErrorAlertProps) {
  return (
    <div className="bg-red-900/50 text-red-300 px-6 py-4 rounded-md flex items-start">
      <AlertCircle className="h-5 w-5 mr-3 mt-0.5 flex-shrink-0" />
      <div>
        <p className="font-medium">{message}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="mt-2 text-sm bg-red-800 hover:bg-red-700 px-3 py-1 rounded-md transition-colors"
          >
            重试
          </button>
        )}
      </div>
    </div>
  );
}
