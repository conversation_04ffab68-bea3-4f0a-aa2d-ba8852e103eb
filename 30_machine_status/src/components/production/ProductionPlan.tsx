'use client';

/**
 * 生产计划组件
 * 
 * 显示单个生产计划的信息和进度条
 */
import { useMemo } from 'react';

interface PlanProps {
  plan: any; // 生产计划数据
  startHour: number; // 时间轴开始小时
  selectedDate: Date; // 选中的日期
  getProductBackgroundColor: (index: number) => string; // 获取产品背景色函数
  getProductLightBackgroundColor: (index: number) => string; // 获取产品浅色背景函数
}

export default function ProductionPlan({ 
  plan, 
  startHour, 
  selectedDate,
  getProductBackgroundColor,
  getProductLightBackgroundColor
}: PlanProps) {
  // 计算进度条位置和宽度
  const { startPercent, widthPercent, completionPercent, theoreticalPercent } = useMemo(() => {
    const startTime = new Date(plan.plannedStartTime);
    const endTime = new Date(plan.plannedEndTime);
    const now = new Date();

    // 计算计划开始时间相对于时间轴起始时间的位置（百分比）
    const dayStart = new Date(selectedDate);
    dayStart.setHours(startHour, 0, 0, 0);

    const dayEnd = new Date(selectedDate);
    dayEnd.setHours(startHour + 24, 0, 0, 0);

    const totalDayMinutes = (dayEnd.getTime() - dayStart.getTime()) / (60 * 1000);

    // 计算计划开始和结束时间相对于一天的位置
    const startMinutes = Math.max(0, (startTime.getTime() - dayStart.getTime()) / (60 * 1000));
    const endMinutes = Math.min(totalDayMinutes, (endTime.getTime() - dayStart.getTime()) / (60 * 1000));

    // 转换为百分比
    const startPercent = (startMinutes / totalDayMinutes) * 100;
    const widthPercent = ((endMinutes - startMinutes) / totalDayMinutes) * 100;

    // 计算当前完成进度
    const completionPercent = (plan.completedQuantity / plan.plannedQuantity) * 100;

    // 计算理论进度（基于当前时间）
    let theoreticalPercent = 0;
    if (now >= startTime && now <= endTime) {
      const elapsedMinutes = (now.getTime() - startTime.getTime()) / (60 * 1000);
      const totalPlanMinutes = (endTime.getTime() - startTime.getTime()) / (60 * 1000);
      theoreticalPercent = (elapsedMinutes / totalPlanMinutes) * 100;
    } else if (now > endTime) {
      theoreticalPercent = 100;
    }

    return {
      startPercent,
      widthPercent,
      completionPercent: Math.min(completionPercent, 100),
      theoreticalPercent: Math.min(theoreticalPercent, 100)
    };
  }, [plan, startHour, selectedDate]);

  // 获取背景色和浅色背景
  const bgColor = getProductBackgroundColor(plan.productIndex);
  const lightBgColor = getProductLightBackgroundColor(plan.productIndex);

  return (
    <div
      className="absolute top-0 h-full flex flex-col z-20"
      style={{
        left: `${startPercent}%`,
        width: `${widthPercent}%`,
      }}
    >
      {/* 背景 - 计划时间范围 */}
      <div
        className="absolute inset-0 opacity-30"
        style={{ backgroundColor: lightBgColor }}
      ></div>

      {/* 垂直排列的内容区域 */}
      <div className="relative h-full flex flex-col justify-between px-0 pt-0.5 pb-0 text-xs overflow-hidden">
        {/* 文字容器的背景 - 使用与产品相匹配的颜色 */}
        <div
          className="absolute top-0 left-0 right-0 h-12 z-0"
          style={{ backgroundColor: bgColor, opacity: 0.7 }}
        ></div>

        {/* 1. 物料信息 - 使用单行显示并添加工具提示 */}
        <div className="mb-1 px-1 z-10 relative">
          <div className="truncate whitespace-nowrap py-0.5 px-1" title={`编号: ${plan.materialCode} 名称: ${plan.materialName} 工单: ${plan.workOrder}`}>
            <span className="text-gray-400">编号:</span> <span className="font-medium">{plan.materialCode}</span>
            <span className="text-gray-400 ml-2">名称:</span> {plan.materialName}
            <span className="text-gray-400 ml-2">工单:</span> {plan.workOrder}
          </div>
        </div>

        {/* 2. 计划信息 - 使用单行显示并添加工具提示 */}
        <div className="mb-2 px-1 z-10 relative">
          <div className="flex justify-between whitespace-nowrap py-0.5 px-1" title={`产量: ${plan.completedQuantity}/${plan.plannedQuantity} (${plan.completionPercentage}%) 操作员: ${plan.operator}`}>
            <span className="truncate">产量: {plan.completedQuantity}/{plan.plannedQuantity}</span>
            <span className="truncate ml-2">操作员: {plan.operator}</span>
          </div>
        </div>

        {/* 3. 进度条组 - 紧接在计划信息下方 */}
        <div className="flex flex-col space-y-1 mb-0">
          {/* 进度条容器 - 占满整个区域 */}
          <div className="space-y-0 mx-[-1px]">
            {/* 3.1 计划理论进度条 */}
            <div className="h-3 w-full relative bg-gray-800/70 rounded-none overflow-hidden z-[5]">
              <div
                className="absolute top-0 left-0 h-full bg-white opacity-50 z-0"
                style={{ width: `${theoreticalPercent}%` }}
              ></div>
              <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                计划:{Math.round(theoreticalPercent)}%
              </div>
            </div>

            {/* 3.2 实际生产进度条 */}
            <div className="h-3 w-full relative bg-gray-900/70 rounded-none overflow-hidden mt-[-1px] z-[5]">
              <div
                className="absolute top-0 left-0 h-full z-0"
                style={{
                  width: `${completionPercent}%`,
                  backgroundColor: bgColor,
                  boxShadow: 'none'
                }}
              ></div>
              <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                产量:{Math.round(completionPercent)}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
