'use client';

/**
 * 可滚动设备列表组件
 *
 * 实现设备列表的连续滚动效果
 * 当设备数量多时，自动向上滚动，第一条记录接续最后一条记录
 * 优化滚动性能，使用GPU加速和requestAnimationFrame实现丝滑滚动
 */
import { useState, useEffect, useRef, useCallback } from 'react';
import DeviceRow from './DeviceRow';

interface ScrollableDeviceListProps {
  devices: any[]; // 设备数据数组
  statusHistory: Record<string, any[]>; // 设备状态历史
  selectedDate: Date; // 选中的日期
  startHour: number; // 时间轴开始小时
  scrollSpeed?: number; // 滚动速度（像素/秒）
  smoothScroll?: boolean; // 是否启用平滑滚动
  getProductBackgroundColor: (index: number) => string; // 获取产品背景色函数
  getProductLightBackgroundColor: (index: number) => string; // 获取产品浅色背景函数
  getStatusColor: (status: string) => string; // 获取状态颜色函数
  getStatusName: (status: string) => string; // 获取状态名称函数
}

export default function ScrollableDeviceList({
  devices,
  statusHistory,
  selectedDate,
  startHour,
  scrollSpeed = 30, // 默认滚动速度
  smoothScroll = true, // 默认启用平滑滚动
  getProductBackgroundColor,
  getProductLightBackgroundColor,
  getStatusColor,
  getStatusName
}: ScrollableDeviceListProps) {
  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  // 设备行容器引用
  const deviceRowsRef = useRef<HTMLDivElement>(null);
  // 滚动位置引用 - 使用ref而不是state以避免重渲染
  const scrollPositionRef = useRef(0);
  // 是否应该滚动的状态
  const [shouldScroll, setShouldScroll] = useState(true);
  // 设备行高度引用
  const rowHeightRef = useRef(0);
  // 容器高度引用
  const containerHeightRef = useRef(0);
  // 动画帧ID
  const animationFrameRef = useRef<number | null>(null);
  // 上次时间戳
  const lastTimestampRef = useRef<number | null>(null);
  // 总内容高度引用
  const totalHeightRef = useRef(0);
  // 强制更新状态 - 用于触发重渲染
  const [, forceUpdate] = useState({});

  // 渲染时间网格 - 与TimeAxis组件保持一致
  const renderTimeGrid = (zIndex: number) => {
    // 生成时间轴刻度
    const hours = [];
    for (let i = 0; i < 24; i++) {
      const hour = (startHour + i) % 24;
      hours.push(hour);
    }

    // 创建与时间轴完全相同的网格布局
    return (
      <div className="absolute inset-0 right-4 pointer-events-none">
        <div className="h-full w-full grid" style={{ gridTemplateColumns: `repeat(${hours.length}, 1fr)`, zIndex }}>
          {hours.map((hour, index) => {
            // 每4小时（整点）的线条更明显
            const isMainHour = hour % 4 === 0;
            return (
              <div
                key={index}
                className={`${isMainHour ? '' : ''}`}
                style={{
                  borderRight: isMainHour ? '1px solid rgba(156, 163, 175, 0.5)' : '1px solid rgba(156, 163, 175, 0.3)',
                  height: '100%'
                }}
              ></div>
            );
          })}
        </div>
      </div>
    );
  };

  // 计算行高和容器高度 - 使用ResizeObserver实现响应式
  useEffect(() => {
    if (!deviceRowsRef.current || !scrollContainerRef.current || devices.length === 0) return;

    // 创建ResizeObserver监听容器大小变化
    const containerResizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        // 更新容器高度
        containerHeightRef.current = entry.contentRect.height;

        // 重新计算总高度
        if (rowHeightRef.current > 0) {
          totalHeightRef.current = rowHeightRef.current * devices.length;
        }
      }
    });

    // 创建ResizeObserver监听行高变化
    const rowResizeObserver = new ResizeObserver((entries) => {
      if (entries.length > 0 && entries[0].target) {
        // 更新行高
        rowHeightRef.current = entries[0].contentRect.height;

        // 重新计算总高度
        totalHeightRef.current = rowHeightRef.current * devices.length;
      }
    });

    // 开始观察容器
    containerResizeObserver.observe(scrollContainerRef.current);

    // 获取第一个设备行并观察
    const firstRow = deviceRowsRef.current.firstChild as HTMLElement;
    if (firstRow) {
      rowResizeObserver.observe(firstRow);
    }

    // 初始化测量
    if (firstRow) {
      rowHeightRef.current = firstRow.offsetHeight;
    }
    containerHeightRef.current = scrollContainerRef.current.offsetHeight;
    totalHeightRef.current = rowHeightRef.current * devices.length;

    // 清理函数
    return () => {
      containerResizeObserver.disconnect();
      rowResizeObserver.disconnect();
    };
  }, [devices]);

  // 更新DOM元素的transform属性 - 使用直接DOM操作避免React重渲染
  const updateTransform = useCallback(() => {
    if (deviceRowsRef.current) {
      // 使用硬件加速的transform属性
      if (smoothScroll) {
        // 平滑滚动 - 使用CSS过渡
        deviceRowsRef.current.style.transition = 'transform 0.1s linear';
        deviceRowsRef.current.style.transform = `translate3d(0, -${scrollPositionRef.current}px, 0)`;
      } else {
        // 无平滑滚动 - 直接设置位置
        deviceRowsRef.current.style.transition = 'none';
        deviceRowsRef.current.style.transform = `translate3d(0, -${Math.round(scrollPositionRef.current)}px, 0)`;
      }
    }
  }, [smoothScroll]);

  // 处理滚动动画 - 优化的滚动算法
  useEffect(() => {
    if (!shouldScroll || !deviceRowsRef.current || !scrollContainerRef.current || devices.length === 0 || rowHeightRef.current === 0) {
      return;
    }

    // 如果内容高度小于容器高度，不需要滚动
    if (totalHeightRef.current <= containerHeightRef.current) {
      setShouldScroll(false);
      return;
    }

    // 使用Temporal Dead Reckoning算法实现平滑滚动
    // 这种算法基于时间而非帧率，可以在不同性能设备上保持一致的滚动速度
    const animate = (timestamp: number) => {
      // 初始化时间戳
      if (lastTimestampRef.current === null) {
        lastTimestampRef.current = timestamp;
        animationFrameRef.current = requestAnimationFrame(animate);
        return;
      }

      // 计算时间差（毫秒）
      const deltaTime = timestamp - lastTimestampRef.current;

      // 如果帧率过低（deltaTime > 100ms），限制最大时间差以避免大跳跃
      const effectiveDeltaTime = Math.min(deltaTime, 100);

      // 更新时间戳
      lastTimestampRef.current = timestamp;

      // 计算滚动增量（像素）- 使用亚像素精度
      const scrollIncrement = (scrollSpeed * effectiveDeltaTime) / 1000;

      // 更新滚动位置
      scrollPositionRef.current += scrollIncrement;

      // 如果滚动到底部，重置到顶部（无缝循环）
      if (scrollPositionRef.current >= totalHeightRef.current) {
        scrollPositionRef.current = 0;
      }

      // 直接更新DOM，避免React状态更新和重渲染
      updateTransform();

      // 继续动画循环
      animationFrameRef.current = requestAnimationFrame(animate);
    };

    // 开始动画
    animationFrameRef.current = requestAnimationFrame(animate);

    // 清理函数
    return () => {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      lastTimestampRef.current = null;
    };
  }, [shouldScroll, devices.length, scrollSpeed, updateTransform]);

  // 鼠标悬停时暂停滚动
  const handleMouseEnter = useCallback(() => {
    setShouldScroll(false);
  }, []);

  // 鼠标离开时恢复滚动
  const handleMouseLeave = useCallback(() => {
    setShouldScroll(true);
    lastTimestampRef.current = null; // 重置时间戳
  }, []);

  // 触摸事件处理 - 移动设备上暂停滚动
  const handleTouchStart = useCallback(() => {
    setShouldScroll(false);
  }, []);

  // 触摸结束时恢复滚动
  const handleTouchEnd = useCallback(() => {
    setShouldScroll(true);
    lastTimestampRef.current = null; // 重置时间戳
  }, []);

  // 复制设备数组以实现无缝滚动 - 使用3倍数据确保在任何滚动位置都有足够的内容
  const renderDevices = [...devices, ...devices, ...devices];

  // 使用memo优化设备行渲染
  const deviceRows = useCallback(() => {
    return renderDevices.map((device, index) => (
      <DeviceRow
        key={`${device.deviceId}-${index}`}
        device={device}
        statusHistory={statusHistory}
        selectedDate={selectedDate}
        startHour={startHour}
        getProductBackgroundColor={getProductBackgroundColor}
        getProductLightBackgroundColor={getProductLightBackgroundColor}
        getStatusColor={getStatusColor}
        getStatusName={getStatusName}
        renderTimeGrid={renderTimeGrid}
      />
    ));
  }, [devices, statusHistory, selectedDate, startHour, getProductBackgroundColor,
    getProductLightBackgroundColor, getStatusColor, getStatusName, renderTimeGrid]);

  return (
    <div
      ref={scrollContainerRef}
      className="flex-1 overflow-hidden"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      <div
        ref={deviceRowsRef}
        className="transform will-change-transform"
        style={{
          transform: 'translate3d(0, 0, 0)', // 初始值，将通过updateTransform直接更新
          backfaceVisibility: 'hidden', // 提高GPU渲染性能
          WebkitBackfaceVisibility: 'hidden',
          perspective: 1000,
          WebkitPerspective: 1000
        }}
      >
        {deviceRows()}
      </div>
    </div>
  );
}
