'use client';

/**
 * 设备状态条组件
 *
 * 显示设备状态历史
 */
import { useMemo } from 'react';

interface DeviceStatusBarProps {
  deviceId: string;
  statusHistory: Record<string, any[]>;
  selectedDate: Date;
  startHour: number;
  getStatusColor: (status: string) => string;
  getStatusName: (status: string) => string;
  renderTimeGrid: (zIndex: number) => JSX.Element;
}

export default function DeviceStatusBar({
  deviceId,
  statusHistory,
  selectedDate,
  startHour,
  getStatusColor,
  getStatusName,
  renderTimeGrid
}: DeviceStatusBarProps) {
  // 计算状态条位置和宽度
  const statusRecords = useMemo(() => {
    if (!statusHistory[deviceId]) return [];

    return statusHistory[deviceId].map((statusRecord, idx) => {
      // 计算状态记录的开始和结束时间
      const startTime = new Date(statusRecord.startTime);
      const endTime = new Date(statusRecord.endTime);

      // 计算一天的开始时间（从timeAxisStartHour开始）
      const dayStart = new Date(selectedDate);
      dayStart.setHours(startHour, 0, 0, 0);

      // 计算一天的结束时间（到第二天的timeAxisStartHour）
      const dayEnd = new Date(selectedDate);
      dayEnd.setHours(startHour + 24, 0, 0, 0);

      // 计算总分钟数
      const totalMinutes = (dayEnd.getTime() - dayStart.getTime()) / (60 * 1000);

      // 计算状态开始和结束时间相对于一天开始的分钟数
      const startMinutes = Math.max(0, (startTime.getTime() - dayStart.getTime()) / (60 * 1000));
      const endMinutes = Math.min(totalMinutes, (endTime.getTime() - dayStart.getTime()) / (60 * 1000));

      // 计算位置和宽度（百分比）
      const left = (startMinutes / totalMinutes) * 100;
      const width = ((endMinutes - startMinutes) / totalMinutes) * 100;

      return {
        ...statusRecord,
        left,
        width,
        startTime,
        endTime
      };
    }).filter(record => record.width > 0); // 过滤掉宽度为0的记录
  }, [deviceId, statusHistory, selectedDate, startHour]);

  return (
    <div className="flex mt-0 py-1">
      {/* 设备信息 - 空白区域 */}
      <div className="w-48 min-w-[12rem] bg-gray-900 border-r border-gray-700">
      </div>

      {/* 设备状态变化条 - 贯穿整个24小时 */}
      <div className="flex-1 pr-4 relative">
        {/* 添加竖线 - 使用绝对定位，确保与时间轴对齐 */}
        {renderTimeGrid(1)}

        {/* 使用相对定位容器 */}
        <div className="relative h-6 bg-gray-800/70 z-10">
          {/* 设备状态历史数据 - 使用相对定位的容器 */}
          <div className="relative h-full z-20">
            {statusRecords.map((statusRecord, idx) => (
              <div
                key={idx}
                className="absolute top-0 h-full z-20"
                style={{
                  left: `${statusRecord.left}%`,
                  width: `${statusRecord.width}%`,
                  backgroundColor: getStatusColor(statusRecord.status),
                  transition: 'left 0.3s ease, width 0.3s ease',
                  maxWidth: 'calc(100% - 2px)' // 确保状态条不会超出容器
                }}
                title={`${getStatusName(statusRecord.status)}: ${statusRecord.startTime.toLocaleTimeString()} - ${statusRecord.endTime.toLocaleTimeString()}`}
              >
                {statusRecord.width > 5 && (
                  <div className="absolute inset-0 flex items-center justify-center text-[8px] text-white font-medium overflow-hidden">
                    {getStatusName(statusRecord.status)}
                  </div>
                )}
              </div>
            ))}
          </div>

          {(!statusHistory[deviceId] || statusHistory[deviceId]?.length === 0) && (
            <div className="flex items-center justify-center text-[9px] text-gray-400 z-20 h-full">
              无状态记录
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
