'use client';

/**
 * 时间轴组件
 *
 * 显示从早上8点到第二天早上7点的时间轴
 * 固定在顶部，不随内容滚动
 */
import { useMemo } from 'react';

interface TimeAxisProps {
  startHour?: number; // 开始小时，默认为8（上午8点）
  hoursCount?: number; // 显示的小时数，默认为24
  className?: string;
}

export default function TimeAxis({
  startHour = 8,
  hoursCount = 24,
  className = ''
}: TimeAxisProps) {
  // 生成时间轴刻度
  const timeAxis = useMemo(() => {
    const hours = [];
    for (let i = 0; i < hoursCount; i++) {
      const hour = (startHour + i) % 24;
      hours.push(hour);
    }
    return hours;
  }, [startHour, hoursCount]);

  return (
    <div className={`sticky top-0 z-20 bg-gray-900 border-b border-gray-700 flex ${className}`}>
      {/* 左侧设备标题区域 */}
      <div className="w-48 min-w-[12rem] bg-gray-900 border-r border-gray-700 flex items-center justify-center text-xs text-gray-400">
        机器/时间
      </div>

      {/* 时间轴 */}
      <div className="flex-1 pr-4 relative">
        {/* 使用与其他区域完全相同的网格布局 */}
        <div className="grid" style={{ gridTemplateColumns: `repeat(${timeAxis.length}, 1fr)` }}>
          {timeAxis.map((hour, index) => {
            // 每4小时（整点）的线条更明显
            const isMainHour = hour % 4 === 0;
            return (
              <div
                key={index}
                className={`text-center py-2 text-xs text-gray-400 border-r ${isMainHour ? 'border-gray-600 border-r-[1.5px]' : 'border-gray-700'}`}
              >
                {hour.toString().padStart(2, '0')}:00
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
