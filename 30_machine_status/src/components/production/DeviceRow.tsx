'use client';

/**
 * 设备行组件
 * 
 * 显示单个设备的信息、计划和状态
 */
import ProductionPlan from './ProductionPlan';
import DeviceStatusBar from './DeviceStatusBar';

interface DeviceRowProps {
  device: any; // 设备数据
  statusHistory: Record<string, any[]>; // 设备状态历史
  selectedDate: Date; // 选中的日期
  startHour: number; // 时间轴开始小时
  getProductBackgroundColor: (index: number) => string; // 获取产品背景色函数
  getProductLightBackgroundColor: (index: number) => string; // 获取产品浅色背景函数
  getStatusColor: (status: string) => string; // 获取状态颜色函数
  getStatusName: (status: string) => string; // 获取状态名称函数
  renderTimeGrid: (zIndex: number) => JSX.Element; // 渲染时间网格函数
}

export default function DeviceRow({
  device,
  statusHistory,
  selectedDate,
  startHour,
  getProductBackgroundColor,
  getProductLightBackgroundColor,
  getStatusColor,
  getStatusName,
  renderTimeGrid
}: DeviceRowProps) {
  return (
    <div className="flex flex-col border-b border-gray-800">
      {/* 第一行：设备信息和计划信息组 */}
      <div className="flex">
        {/* 设备信息 - 居中对齐 */}
        <div className="w-48 min-w-[12rem] p-2 bg-gray-900 border-r border-gray-700 flex items-center justify-center z-10">
          <div className="text-center w-full">
            <div className="font-medium">{device.deviceName}</div>
            <div className="text-xs text-gray-400">{device.deviceCode}</div>
          </div>
        </div>

        {/* 计划信息组（顶部） */}
        <div className="flex-1 relative h-20 bg-gray-950/80 pr-4 z-10">
          {/* 设备行专用时间网格 */}
          <div className="absolute inset-0 z-[1] pointer-events-none">
            {renderTimeGrid(1)}
          </div>

          {device.plans && device.plans.length > 0 ? (
            // 有计划数据时显示
            device.plans.map((plan: any, index: number) => (
              <ProductionPlan
                key={index}
                plan={plan}
                startHour={startHour}
                selectedDate={selectedDate}
                getProductBackgroundColor={getProductBackgroundColor}
                getProductLightBackgroundColor={getProductLightBackgroundColor}
              />
            ))
          ) : (
            // 没有计划数据时显示空的进度条
            <div className="h-full flex flex-col justify-between px-0 pt-0.5 pb-0">
              {/* 文字容器的背景 - 使用默认蓝色 */}
              <div className="absolute top-0 left-0 right-0 bg-blue-600/70 h-12 z-0"></div>

              {/* 添加一个不透明的背景容器 */}
              <div className="mb-1 px-1 z-10 relative">
                <div className="py-0.5 px-1 text-center">
                  无计划数据
                </div>
              </div>

              <div className="flex flex-col space-y-1 mb-0 z-10 relative">
                {/* 进度条容器 - 占满整个区域 */}
                <div className="space-y-0 mx-[-2px]">
                  {/* 空的计划理论进度条 */}
                  <div className="h-3 w-full relative bg-gray-800/70 rounded-none overflow-hidden z-[5]">
                    <div className="absolute top-0 left-0 h-full bg-white opacity-50 z-0" style={{ width: '0%' }}></div>
                    <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                      计划:0%
                    </div>
                  </div>

                  {/* 空的实际生产进度条 */}
                  <div className="h-3 w-full relative bg-gray-900/70 rounded-none overflow-hidden mt-[-1px] z-[5]">
                    <div className="absolute top-0 left-0 h-full bg-blue-600 z-0" style={{ width: '0%', boxShadow: 'none' }}></div>
                    <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                      产量:0%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 设备状态条 */}
      <DeviceStatusBar
        deviceId={device.deviceId}
        statusHistory={statusHistory}
        selectedDate={selectedDate}
        startHour={startHour}
        getStatusColor={getStatusColor}
        getStatusName={getStatusName}
        renderTimeGrid={renderTimeGrid}
      />
    </div>
  );
}
