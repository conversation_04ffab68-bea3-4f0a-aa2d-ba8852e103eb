'use client';

/**
 * 状态颜色设置组件
 * 
 * 功能：
 * - 各种设备状态的颜色配置
 * - 颜色预览功能
 * - 预设颜色方案
 */
import { useState } from 'react';
import { Save, AlertCircle, RotateCcw } from 'lucide-react';
import {
  saveDisplaySettings,
  DisplaySettings as DisplaySettingsType
} from '@/services/settingsService';

interface StatusColorSettingsProps {
  settings: DisplaySettingsType;
  onSettingsChange: (settings: DisplaySettingsType) => void;
  isLoading: boolean;
}

export default function StatusColorSettings({
  settings,
  onSettingsChange,
  isLoading
}: StatusColorSettingsProps) {
  // 保存状态
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(false);

  // 默认颜色配置
  const defaultColors = {
    production: "#22c55e",    // 绿色 - 生产中
    idle: "#f59e0b",          // 橙色 - 空闲
    fault: "#ef4444",         // 红色 - 故障
    adjusting: "#3b82f6",     // 蓝色 - 调试
    shutdown: "#6b7280",      // 灰色 - 关机
    disconnected: "#9ca3af",  // 浅灰色 - 断线
    maintenance: "#8b5cf6",   // 紫色 - 维护
    debugging: "#06b6d4"      // 青色 - 调试
  };

  // 状态名称映射
  const statusNames = {
    production: "生产中",
    idle: "空闲",
    fault: "故障",
    adjusting: "调试",
    shutdown: "关机",
    disconnected: "断线",
    maintenance: "维护",
    debugging: "调试"
  };

  // 保存状态颜色设置到API
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSaveSuccess(false);
      setSaveError(false);

      await saveDisplaySettings(settings);

      setSaveSuccess(true);
      // 3秒后清除成功提示
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      console.error('保存状态颜色设置失败:', error);
      setSaveError(true);
      // 3秒后清除错误提示
      setTimeout(() => setSaveError(false), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理状态颜色变化
  const handleStatusColorChange = (status: string, color: string) => {
    onSettingsChange({
      ...settings,
      statusColors: {
        ...settings.statusColors,
        [status]: color
      }
    });
  };

  // 重置为默认颜色
  const resetToDefaultColors = () => {
    onSettingsChange({
      ...settings,
      statusColors: { ...defaultColors }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">状态颜色设置</h3>

        <div className="flex space-x-2">
          {/* 重置按钮 */}
          <button
            onClick={resetToDefaultColors}
            disabled={isSaving || isLoading}
            className="flex items-center px-3 py-2 rounded-md text-sm font-medium bg-gray-600 hover:bg-gray-500 transition-colors"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            重置默认
          </button>

          {/* 保存按钮 */}
          <button
            onClick={handleSaveSettings}
            disabled={isSaving || isLoading}
            className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
              ${isSaving || isLoading ? 'bg-gray-600 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
          >
            {isSaving ? (
              <>
                <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存设置
              </>
            )}
          </button>
        </div>
      </div>

      {/* 保存状态提示 */}
      {saveSuccess && (
        <div className="bg-green-900/50 text-green-300 px-4 py-2 rounded-md flex items-center">
          <div className="bg-green-500 rounded-full p-1 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          设置已成功保存
        </div>
      )}

      {saveError && (
        <div className="bg-red-900/50 text-red-300 px-4 py-2 rounded-md flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          保存设置失败，请重试
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="ml-3">加载设置中...</span>
        </div>
      ) : (
        <div className="space-y-6">
          <p className="text-sm text-gray-400">
            配置不同设备状态对应的颜色，这些颜色将在设备卡片、状态条和图表中使用。
          </p>

          {/* 状态颜色配置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(statusNames).map(([status, name]) => (
              <div key={status} className="bg-gray-800 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div
                      className="w-6 h-6 rounded-full mr-3 border-2 border-gray-600"
                      style={{ backgroundColor: (settings.statusColors as any)[status] }}
                    ></div>
                    <span className="font-medium">{name}</span>
                  </div>
                  <span className="text-xs text-gray-400 font-mono">
                    {(settings.statusColors as any)[status]}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    value={(settings.statusColors as any)[status]}
                    onChange={(e) => handleStatusColorChange(status, e.target.value)}
                    className="w-12 h-8 rounded border border-gray-600 bg-gray-700 cursor-pointer"
                  />
                  <input
                    type="text"
                    value={(settings.statusColors as any)[status]}
                    onChange={(e) => handleStatusColorChange(status, e.target.value)}
                    className="flex-1 px-3 py-1 bg-gray-700 border border-gray-600 rounded text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="#000000"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* 颜色预览 */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h4 className="text-md font-medium mb-3">颜色预览</h4>
            <div className="flex flex-wrap gap-2">
              {Object.entries(statusNames).map(([status, name]) => (
                <div
                  key={status}
                  className="flex items-center px-3 py-2 rounded-md text-sm"
                  style={{
                    backgroundColor: (settings.statusColors as any)[status],
                    color: '#ffffff'
                  }}
                >
                  {name}
                </div>
              ))}
            </div>
          </div>

          {/* 预设颜色方案 */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h4 className="text-md font-medium mb-3">预设颜色方案</h4>
            <div className="space-y-2">
              <button
                onClick={resetToDefaultColors}
                className="w-full text-left px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-md transition-colors"
              >
                <div className="flex items-center justify-between">
                  <span>默认方案</span>
                  <div className="flex space-x-1">
                    {Object.values(defaultColors).slice(0, 6).map((color, index) => (
                      <div
                        key={index}
                        className="w-4 h-4 rounded-full border border-gray-500"
                        style={{ backgroundColor: color }}
                      ></div>
                    ))}
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="bg-blue-900/20 border border-blue-700 p-4 rounded-lg">
            <h4 className="text-blue-400 font-medium mb-2">使用说明</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• 点击颜色块可以使用颜色选择器</li>
              <li>• 也可以直接输入十六进制颜色代码</li>
              <li>• 建议使用对比度较高的颜色以便区分</li>
              <li>• 颜色变更会立即在预览区域显示</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
