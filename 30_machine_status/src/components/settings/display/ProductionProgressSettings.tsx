'use client'; // 声明这是一个客户端组件

/**
 * 生产进度设置组件
 *
 * 该组件提供生产进度页面显示相关的配置选项
 */
import { useState } from 'react';
import { DisplaySettings, saveDisplaySettings } from '@/services/settingsService';
import { Clock, Save, RotateCcw } from 'lucide-react';

interface ProductionProgressSettingsProps {
  settings: DisplaySettings;
  onSettingsChange: (settings: DisplaySettings) => void;
  isLoading: boolean;
}

export default function ProductionProgressSettings({
  settings,
  onSettingsChange,
  isLoading
}: ProductionProgressSettingsProps) {
  // 保存状态
  const [isSaving, setIsSaving] = useState(false);

  // 处理保存设置
  const handleSave = async () => {
    setIsSaving(true);
    try {
      console.log('保存生产进度设置:', settings.productionProgress);

      // 调用API保存设置
      await saveDisplaySettings(settings);

      console.log('生产进度设置保存成功');
    } catch (error) {
      console.error('保存生产进度设置失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 重置为默认值
  const handleReset = () => {
    onSettingsChange({
      ...settings,
      productionProgress: {
        timeAxisStartHour: 8
      }
    });
  };

  // 生成时间轴预览
  const generateTimeAxisPreview = () => {
    const startHour = settings.productionProgress?.timeAxisStartHour || 8;
    const hours = [];

    for (let i = 0; i < 24; i++) {
      const hour = (startHour + i) % 24;
      hours.push(hour);
    }

    return hours;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
        <span className="ml-3">加载设置中...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Clock className="h-5 w-5 mr-2 text-blue-400" />
          <h3 className="text-lg font-medium">生产进度设置</h3>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleReset}
            className="flex items-center px-3 py-1.5 text-sm bg-gray-600 hover:bg-gray-500 rounded-md transition-colors"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center px-3 py-1.5 text-sm bg-blue-600 hover:bg-blue-500 disabled:bg-blue-400 rounded-md transition-colors"
          >
            <Save className="h-4 w-4 mr-1" />
            {isSaving ? '保存中...' : '保存'}
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* 时间轴配置 */}
        <div className="space-y-4">
          <h4 className="font-medium text-blue-400">时间轴配置</h4>

          <div className="space-y-2">
            <label className="font-medium">时间轴起始小时</label>
            <p className="text-sm text-gray-400">设置生产进度页面时间轴的起始小时（0-23）</p>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min="0"
                max="23"
                step="1"
                value={settings.productionProgress?.timeAxisStartHour || 8}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  onSettingsChange({
                    ...settings,
                    productionProgress: {
                      ...settings.productionProgress,
                      timeAxisStartHour: value
                    }
                  });
                }}
                className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="23"
                  value={settings.productionProgress?.timeAxisStartHour || 8}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (value >= 0 && value <= 23) {
                      onSettingsChange({
                        ...settings,
                        productionProgress: {
                          ...settings.productionProgress,
                          timeAxisStartHour: value
                        }
                      });
                    }
                  }}
                  className="w-16 bg-gray-700 border border-gray-600 rounded-md px-2 py-1 text-sm text-center"
                />
                <span className="text-sm text-gray-400">点</span>
              </div>
            </div>
            <div className="flex justify-between text-xs text-gray-400">
              <span>0点 (午夜)</span>
              <span>当前: {settings.productionProgress?.timeAxisStartHour || 8}点</span>
              <span>23点 (晚11点)</span>
            </div>
            <p className="text-xs text-blue-400 italic">
              注意：修改此设置后，生产进度页面的时间轴将从设定的小时开始显示24小时
            </p>
          </div>
        </div>

        {/* 时间轴预览 */}
        <div className="space-y-4">
          <h4 className="font-medium text-blue-400">时间轴预览</h4>

          <div className="bg-gray-700 rounded-lg p-4">
            <div className="text-sm text-gray-300 mb-3">
              时间轴显示顺序（从 {settings.productionProgress?.timeAxisStartHour || 8}点 开始）：
            </div>

            <div className="grid grid-cols-12 gap-1 text-xs">
              {generateTimeAxisPreview().slice(0, 12).map((hour, index) => (
                <div
                  key={index}
                  className={`text-center p-2 rounded ${index === 0
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-600 text-gray-300'
                    }`}
                >
                  {hour}点
                </div>
              ))}
            </div>

            <div className="grid grid-cols-12 gap-1 text-xs mt-1">
              {generateTimeAxisPreview().slice(12, 24).map((hour, index) => (
                <div
                  key={index + 12}
                  className="text-center p-2 rounded bg-gray-600 text-gray-300"
                >
                  {hour}点
                </div>
              ))}
            </div>

            <div className="text-xs text-gray-400 mt-3">
              <span className="inline-block w-3 h-3 bg-blue-600 rounded mr-2"></span>
              起始时间点
            </div>
          </div>
        </div>

        {/* 常用时间设置 */}
        <div className="space-y-4">
          <h4 className="font-medium text-blue-400">常用时间设置</h4>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {[
              { label: '早班 (6点)', value: 6 },
              { label: '白班 (8点)', value: 8 },
              { label: '中班 (14点)', value: 14 },
              { label: '夜班 (22点)', value: 22 }
            ].map((preset) => (
              <button
                key={preset.value}
                onClick={() => {
                  onSettingsChange({
                    ...settings,
                    productionProgress: {
                      ...settings.productionProgress,
                      timeAxisStartHour: preset.value
                    }
                  });
                }}
                className={`p-3 rounded-md text-sm transition-colors ${(settings.productionProgress?.timeAxisStartHour || 8) === preset.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
                  }`}
              >
                {preset.label}
              </button>
            ))}
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="bg-gray-700 rounded-lg p-4">
          <h4 className="font-medium text-gray-300 mb-3">设置说明</h4>
          <div className="space-y-2 text-sm text-gray-400">
            <div>
              <span className="text-white font-medium">时间轴起始小时：</span>
              设置生产进度页面时间轴的起始时间，时间轴将从此时间开始显示连续24小时。
            </div>
            <div>
              <span className="text-white font-medium">应用场景：</span>
              根据工厂的实际工作时间安排，可以设置为早班、白班、中班或夜班的开始时间。
            </div>
            <div>
              <span className="text-white font-medium">显示效果：</span>
              时间轴将按照设定的起始时间顺序显示，便于查看完整的工作周期。
            </div>
            <div>
              <span className="text-white font-medium">注意事项：</span>
              修改此设置会影响生产进度页面的时间轴显示，建议根据实际生产安排进行配置。
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
