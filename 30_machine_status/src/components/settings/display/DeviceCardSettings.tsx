'use client';

/**
 * 设备卡片设置组件
 * 
 * 功能：
 * - 设备卡片内容显示设置
 * - 设备卡片样式设置
 * - 字体大小和对齐方式设置
 * - 布局比例设置
 */
import { useState } from 'react';
import { Save, AlertCircle } from 'lucide-react';
import {
  saveDisplaySettings,
  DisplaySettings as DisplaySettingsType
} from '@/services/settingsService';

interface DeviceCardSettingsProps {
  settings: DisplaySettingsType;
  onSettingsChange: (settings: DisplaySettingsType) => void;
  isLoading: boolean;
}

export default function DeviceCardSettings({
  settings,
  onSettingsChange,
  isLoading
}: DeviceCardSettingsProps) {
  // 保存状态
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(false);

  // 保存设备卡片设置到API
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSaveSuccess(false);
      setSaveError(false);

      await saveDisplaySettings(settings);

      setSaveSuccess(true);
      // 3秒后清除成功提示
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      console.error('保存设备卡片设置失败:', error);
      setSaveError(true);
      // 3秒后清除错误提示
      setTimeout(() => setSaveError(false), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理卡片内容设置变化
  const handleCardContentChange = (key: string, value: any) => {
    onSettingsChange({
      ...settings,
      cardContent: {
        ...settings.cardContent,
        [key]: value
      }
    });
  };

  // 处理卡片样式设置变化
  const handleCardStyleChange = (key: string, value: any) => {
    onSettingsChange({
      ...settings,
      cardStyle: {
        ...settings.cardStyle,
        [key]: value
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">设备卡片设置</h3>

        {/* 保存按钮 */}
        <button
          onClick={handleSaveSettings}
          disabled={isSaving || isLoading}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
            ${isSaving || isLoading ? 'bg-gray-600 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
        >
          {isSaving ? (
            <>
              <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
              保存中...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              保存设置
            </>
          )}
        </button>
      </div>

      {/* 保存状态提示 */}
      {saveSuccess && (
        <div className="bg-green-900/50 text-green-300 px-4 py-2 rounded-md flex items-center">
          <div className="bg-green-500 rounded-full p-1 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          设置已成功保存
        </div>
      )}

      {saveError && (
        <div className="bg-red-900/50 text-red-300 px-4 py-2 rounded-md flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          保存设置失败，请重试
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="ml-3">加载设置中...</span>
        </div>
      ) : (
        <div className="space-y-6">
          <h4 className="text-md font-medium text-blue-400">卡片内容显示</h4>

          {/* 显示内容开关 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <label className="font-medium">显示设备编号</label>
              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.cardContent.showCode}
                  onChange={(e) => handleCardContentChange('showCode', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <label className="font-medium">显示设备名称</label>
              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.cardContent.showName}
                  onChange={(e) => handleCardContentChange('showName', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <label className="font-medium">显示设备位置</label>
              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.cardContent.showLocation}
                  onChange={(e) => handleCardContentChange('showLocation', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <label className="font-medium">显示设备型号</label>
              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.cardContent.showModel}
                  onChange={(e) => handleCardContentChange('showModel', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <label className="font-medium">显示生产数量</label>
              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.cardContent.showQuantity}
                  onChange={(e) => handleCardContentChange('showQuantity', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <label className="font-medium">显示生产计划</label>
              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.cardContent.showPlan}
                  onChange={(e) => handleCardContentChange('showPlan', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
              </label>
            </div>
          </div>

          <h4 className="text-md font-medium text-blue-400 mt-6">文字设置</h4>

          {/* 字体大小设置 */}
          <div className="space-y-2">
            <label className="font-medium">字体大小 (像素)</label>
            <input
              type="range"
              min="10"
              max="20"
              step="1"
              value={settings.cardContent.fontSize}
              onChange={(e) => handleCardContentChange('fontSize', parseInt(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-400">
              <span>10px</span>
              <span>{settings.cardContent.fontSize}px</span>
              <span>20px</span>
            </div>
          </div>

          {/* 文字对齐方式 */}
          <div className="space-y-2">
            <label className="font-medium">文字对齐方式</label>
            <div className="flex space-x-4 mt-2">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="textAlign"
                  checked={settings.cardContent.textAlign === "left"}
                  onChange={() => handleCardContentChange('textAlign', 'left')}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-600"
                />
                <span>左对齐</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="textAlign"
                  checked={settings.cardContent.textAlign === "center"}
                  onChange={() => handleCardContentChange('textAlign', 'center')}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-600"
                />
                <span>居中对齐</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="textAlign"
                  checked={settings.cardContent.textAlign === "right"}
                  onChange={() => handleCardContentChange('textAlign', 'right')}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-600"
                />
                <span>右对齐</span>
              </label>
            </div>
          </div>

          {/* 布局比例设置 */}
          <div className="space-y-2">
            <label className="font-medium">布局比例 (%)</label>
            <p className="text-sm text-gray-400">设置状态指示器与文字内容的比例</p>
            <input
              type="range"
              min="40"
              max="80"
              step="5"
              value={settings.cardContent.layoutRatio}
              onChange={(e) => handleCardContentChange('layoutRatio', parseInt(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-400">
              <span>40%</span>
              <span>{settings.cardContent.layoutRatio}%</span>
              <span>80%</span>
            </div>
          </div>


        </div>
      )}
    </div>
  );
}
