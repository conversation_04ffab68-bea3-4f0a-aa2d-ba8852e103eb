'use client'; // 声明这是一个客户端组件

/**
 * 利用率仪表板设置组件
 *
 * 该组件提供利用率仪表板布局和显示相关的配置选项
 */
import { useState } from 'react';
import { DisplaySettings, saveDisplaySettings } from '@/services/settingsService';
import { BarChart3, Save, RotateCcw } from 'lucide-react';

interface UtilizationDashboardSettingsProps {
  settings: DisplaySettings;
  onSettingsChange: (settings: DisplaySettings) => void;
  isLoading: boolean;
}

export default function UtilizationDashboardSettings({
  settings,
  onSettingsChange,
  isLoading
}: UtilizationDashboardSettingsProps) {
  // 保存状态
  const [isSaving, setIsSaving] = useState(false);

  // 处理保存设置
  const handleSave = async () => {
    setIsSaving(true);
    try {
      console.log('保存利用率仪表板设置:', settings.utilizationDashboard);

      // 调用API保存设置
      await saveDisplaySettings(settings);

      console.log('利用率仪表板设置保存成功');
    } catch (error) {
      console.error('保存利用率仪表板设置失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 重置为默认值
  const handleReset = () => {
    onSettingsChange({
      ...settings,
      utilizationDashboard: {
        layoutMode: "split",
        rankingDisplayLimit: 20
      }
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
        <span className="ml-3">加载设置中...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <BarChart3 className="h-5 w-5 mr-2 text-blue-400" />
          <h3 className="text-lg font-medium">利用率仪表板设置</h3>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleReset}
            className="flex items-center px-3 py-1.5 text-sm bg-gray-600 hover:bg-gray-500 rounded-md transition-colors"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center px-3 py-1.5 text-sm bg-blue-600 hover:bg-blue-500 disabled:bg-blue-400 rounded-md transition-colors"
          >
            <Save className="h-4 w-4 mr-1" />
            {isSaving ? '保存中...' : '保存'}
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* 布局模式设置 */}
        <div className="space-y-4">
          <h4 className="font-medium text-blue-400">布局配置</h4>

          <div className="space-y-2">
            <label className="font-medium">布局模式</label>
            <p className="text-sm text-gray-400">选择利用率仪表板的布局方式</p>
            <select
              value={settings.utilizationDashboard?.layoutMode ?? "split"}
              onChange={(e) => {
                onSettingsChange({
                  ...settings,
                  utilizationDashboard: {
                    ...settings.utilizationDashboard,
                    layoutMode: e.target.value as "split" | "triple",
                    rankingDisplayLimit: settings.utilizationDashboard?.rankingDisplayLimit || 10
                  }
                });
              }}
              className="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-sm"
            >
              <option value="split">左右分屏布局</option>
              <option value="triple">三分布局</option>
            </select>
            <div className="text-xs text-gray-500">
              {settings.utilizationDashboard?.layoutMode === "split"
                ? "左侧：概览+趋势，右侧：排名"
                : "左侧1/3：概览+趋势，右侧2/3：左右分区显示排名(倒序/正序)"}
            </div>
          </div>
        </div>

        {/* 排名显示设置 */}
        <div className="space-y-4">
          <h4 className="font-medium text-blue-400">排名显示配置</h4>

          <div className="space-y-2">
            <label className="font-medium">排名显示限制</label>
            <p className="text-sm text-gray-400">设置设备排名列表显示的最大设备数量</p>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                min="-1"
                max="100"
                value={settings.utilizationDashboard?.rankingDisplayLimit ?? 20}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  onSettingsChange({
                    ...settings,
                    utilizationDashboard: {
                      ...settings.utilizationDashboard,
                      rankingDisplayLimit: value,
                      layoutMode: settings.utilizationDashboard?.layoutMode || "split"
                    }
                  });
                }}
                className="w-20 bg-gray-700 border border-gray-600 rounded-md px-2 py-1 text-sm"
              />
              <span className="text-sm text-gray-400">个设备</span>
            </div>
            <div className="text-xs text-gray-500">
              设置为 -1 表示不限制显示数量，显示所有设备
            </div>
          </div>
        </div>

        {/* 预览区域 */}
        <div className="space-y-4">
          <h4 className="font-medium text-blue-400">布局预览</h4>

          <div className="bg-gray-700 rounded-lg p-4">
            <div className="text-sm text-gray-300 mb-3">当前配置预览：</div>

            {settings.utilizationDashboard?.layoutMode === "split" ? (
              <div className="grid grid-cols-2 gap-4 h-32">
                <div className="bg-gray-600 rounded p-3 flex flex-col justify-center items-center">
                  <div className="text-xs text-gray-300 mb-1">左侧区域</div>
                  <div className="text-xs text-gray-400">概览统计</div>
                  <div className="text-xs text-gray-400">趋势图表</div>
                </div>
                <div className="bg-gray-600 rounded p-3 flex flex-col justify-center items-center">
                  <div className="text-xs text-gray-300 mb-1">右侧区域</div>
                  <div className="text-xs text-gray-400">设备排名</div>
                  <div className="text-xs text-gray-400">
                    (显示前{settings.utilizationDashboard?.rankingDisplayLimit ?? 20}台设备)
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-3 gap-4 h-32">
                <div className="bg-gray-600 rounded p-3 flex flex-col justify-center items-center">
                  <div className="text-xs text-gray-300 mb-1">左侧1/3</div>
                  <div className="text-xs text-gray-400">概览统计</div>
                  <div className="text-xs text-gray-400">趋势图表</div>
                </div>
                <div className="bg-gray-600 rounded p-3 flex flex-col justify-center items-center">
                  <div className="text-xs text-gray-300 mb-1">中间1/3</div>
                  <div className="text-xs text-gray-400">排名(倒序)</div>
                  <div className="text-xs text-gray-400">低→高利用率</div>
                </div>
                <div className="bg-gray-600 rounded p-3 flex flex-col justify-center items-center">
                  <div className="text-xs text-gray-300 mb-1">右侧1/3</div>
                  <div className="text-xs text-gray-400">排名(正序)</div>
                  <div className="text-xs text-gray-400">高→低利用率</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="bg-gray-700 rounded-lg p-4">
          <h4 className="font-medium text-gray-300 mb-3">设置说明</h4>
          <div className="space-y-2 text-sm text-gray-400">
            <div>
              <span className="text-white font-medium">布局模式：</span>
              选择仪表板的整体布局方式，影响各个区域的排列和大小比例。
            </div>
            <div>
              <span className="text-white font-medium">排名显示限制：</span>
              控制设备排名列表显示的设备数量，避免列表过长影响性能。
            </div>
            <div>
              <span className="text-white font-medium">三分布局：</span>
              适合大屏幕显示，可以同时查看概览、低利用率设备和高利用率设备。
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
