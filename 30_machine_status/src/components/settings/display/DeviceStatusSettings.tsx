'use client';

/**
 * 设备状态设置组件
 * 
 * 功能：
 * - 过滤信号时长设置
 * - 状态历史相关配置
 * - 设备状态显示选项
 */
import { useState } from 'react';
import { Save, AlertCircle } from 'lucide-react';
import {
  saveDisplaySettings,
  DisplaySettings as DisplaySettingsType
} from '@/services/settingsService';

interface DeviceStatusSettingsProps {
  settings: DisplaySettingsType;
  onSettingsChange: (settings: DisplaySettingsType) => void;
  isLoading: boolean;
}

export default function DeviceStatusSettings({
  settings,
  onSettingsChange,
  isLoading
}: DeviceStatusSettingsProps) {
  // 保存状态
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(false);

  // 保存设备状态设置到API
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSaveSuccess(false);
      setSaveError(false);

      await saveDisplaySettings(settings);

      setSaveSuccess(true);
      // 3秒后清除成功提示
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      console.error('保存设备状态设置失败:', error);
      setSaveError(true);
      // 3秒后清除错误提示
      setTimeout(() => setSaveError(false), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理过滤信号时长变化
  const handleFilterSignalDurationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    onSettingsChange({
      ...settings,
      filterSignalDuration: value
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">设备状态设置</h3>

        {/* 保存按钮 */}
        <button
          onClick={handleSaveSettings}
          disabled={isSaving || isLoading}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
            ${isSaving || isLoading ? 'bg-gray-600 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
        >
          {isSaving ? (
            <>
              <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
              保存中...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              保存设置
            </>
          )}
        </button>
      </div>

      {/* 保存状态提示 */}
      {saveSuccess && (
        <div className="bg-green-900/50 text-green-300 px-4 py-2 rounded-md flex items-center">
          <div className="bg-green-500 rounded-full p-1 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          设置已成功保存
        </div>
      )}

      {saveError && (
        <div className="bg-red-900/50 text-red-300 px-4 py-2 rounded-md flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          保存设置失败，请重试
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="ml-3">加载设置中...</span>
        </div>
      ) : (
        <div className="space-y-6">
          {/* 过滤信号时长设置 */}
          <div className="space-y-2">
            <label className="font-medium">过滤信号时长 (秒)</label>
            <p className="text-sm text-gray-400">过滤掉持续时间小于此值的状态信号，减少噪声干扰</p>
            <input
              type="range"
              min="0"
              max="10"
              step="1"
              value={settings.filterSignalDuration}
              onChange={handleFilterSignalDurationChange}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-400">
              <span>0秒</span>
              <span>{settings.filterSignalDuration}秒</span>
              <span>10秒</span>
            </div>
          </div>

          {/* 状态历史时间轴设置 */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-300">状态历史显示</h4>
            
            {/* 时间槽宽度设置 */}
            <div className="space-y-2">
              <label className="font-medium">时间槽宽度 (像素)</label>
              <p className="text-sm text-gray-400">设置状态历史时间轴中每个时间槽的宽度</p>
              <input
                type="range"
                min="80"
                max="200"
                step="10"
                value={settings.statusHistory.timeSlotWidth}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  onSettingsChange({
                    ...settings,
                    statusHistory: {
                      ...settings.statusHistory,
                      timeSlotWidth: value
                    }
                  });
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-400">
                <span>80px</span>
                <span>{settings.statusHistory.timeSlotWidth}px</span>
                <span>200px</span>
              </div>
            </div>

            {/* 时间轴高度设置 */}
            <div className="space-y-2">
              <label className="font-medium">时间轴高度 (像素)</label>
              <p className="text-sm text-gray-400">设置状态历史时间轴的高度</p>
              <input
                type="range"
                min="20"
                max="60"
                step="5"
                value={settings.statusHistory.timelineHeight}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  onSettingsChange({
                    ...settings,
                    statusHistory: {
                      ...settings.statusHistory,
                      timelineHeight: value
                    }
                  });
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-400">
                <span>20px</span>
                <span>{settings.statusHistory.timelineHeight}px</span>
                <span>60px</span>
              </div>
            </div>

            {/* 时间轴间隔设置 */}
            <div className="space-y-2">
              <label className="font-medium">时间轴间隔 (像素)</label>
              <p className="text-sm text-gray-400">设置多个时间轴之间的间隔</p>
              <input
                type="range"
                min="4"
                max="16"
                step="2"
                value={settings.statusHistory.timelineGap}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  onSettingsChange({
                    ...settings,
                    statusHistory: {
                      ...settings.statusHistory,
                      timelineGap: value
                    }
                  });
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-400">
                <span>4px</span>
                <span>{settings.statusHistory.timelineGap}px</span>
                <span>16px</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
