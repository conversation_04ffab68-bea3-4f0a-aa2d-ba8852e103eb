'use client';

import { useState, useEffect, useCallback } from 'react';
import { SidebarMode } from '@/components/layout/Sidebar';
import { getSidebarMode, saveSidebarMode, getNextSidebarMode } from '@/utils/sidebarState';

/**
 * 侧边栏状态钩子
 * 
 * 管理侧边栏的显示状态，包括从localStorage加载和保存状态
 * 
 * @returns {Object} 包含侧边栏模式和切换函数
 */
export function useSidebar() {
  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);

  // 在客户端初始化时从localStorage加载侧边栏状态
  useEffect(() => {
    // 导入是动态的，因为这些函数使用了浏览器API
    const loadSidebarMode = () => {
      try {
        const mode = getSidebarMode();
        console.log('加载侧边栏状态:', mode);
        setSidebarMode(mode);
      } catch (error) {
        console.error('加载侧边栏状态失败:', error);
      }
    };

    // 立即执行
    loadSidebarMode();
  }, []);

  /**
   * 切换侧边栏显示模式
   * 在两种模式之间切换：隐藏 <-> 折叠
   * 并保存到localStorage
   */
  const toggleSidebar = useCallback(() => {
    try {
      // 获取下一个模式
      const nextMode = getNextSidebarMode(sidebarMode);
      console.log('切换侧边栏状态:', sidebarMode, '->', nextMode);

      // 保存到localStorage和全局变量
      saveSidebarMode(nextMode);

      // 更新组件状态
      setSidebarMode(nextMode);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  return { sidebarMode, toggleSidebar };
}
