'use client';

import { useState, useEffect } from 'react';

/**
 * 窗口大小钩子
 * 
 * 该钩子用于监听窗口大小变化，并返回当前窗口的宽度和高度
 * 在组件中使用此钩子可以实现响应式布局
 * 
 * @returns {Object} 包含窗口宽度和高度的对象
 */
export function useWindowSize() {
  // 初始化状态为undefined，避免服务器端渲染时的不匹配问题
  const [windowSize, setWindowSize] = useState<{
    width: number | undefined;
    height: number | undefined;
  }>({
    width: undefined,
    height: undefined,
  });

  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') {
      return;
    }

    // 处理窗口大小变化的函数
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // 添加事件监听器
    window.addEventListener('resize', handleResize);
    
    // 立即调用一次以设置初始大小
    handleResize();
    
    // 清理函数：移除事件监听器
    return () => window.removeEventListener('resize', handleResize);
  }, []); // 空依赖数组确保效果只运行一次

  return windowSize;
}
