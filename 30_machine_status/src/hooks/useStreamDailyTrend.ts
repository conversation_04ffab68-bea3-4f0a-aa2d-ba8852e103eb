/**
 * 流式每日利用率趋势Hook
 * 
 * 功能：处理流式每日利用率数据，实现无感渲染
 * 特性：
 * 1. 支持Server-Sent Events (SSE)
 * 2. 逐日接收数据并更新状态
 * 3. 自动处理连接管理
 * 4. 提供进度信息
 */

import { useState, useEffect, useCallback, useRef } from 'react';

// 数据类型定义
interface DailyUtilizationData {
  date: string;
  utilization_rate: number;
  total_devices: number;
  active_devices: number;
  working_hours: number;
  productive_hours: number;
}

interface DailyTrendSummary {
  start_date: string;
  end_date: string;
  daily_data: DailyUtilizationData[];
  average_rate: number;
  max_rate: number;
  min_rate: number;
  trend_direction: string;
  last_updated: string;
}

interface StreamProgress {
  current: number;
  total: number;
  percentage: number;
}

interface UseStreamDailyTrendResult {
  dailyTrend: DailyTrendSummary | null;
  loading: boolean;
  error: string | null;
  progress: StreamProgress | null;
  fetchStreamDailyTrend: (startDate: string, endDate: string) => void;
  cancelStream: () => void;
}

export function useStreamDailyTrend(): UseStreamDailyTrendResult {
  const [dailyTrend, setDailyTrend] = useState<DailyTrendSummary | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<StreamProgress | null>(null);
  
  const eventSourceRef = useRef<EventSource | null>(null);
  const accumulatedDataRef = useRef<DailyUtilizationData[]>([]);

  // 取消流式请求
  const cancelStream = useCallback(() => {
    if (eventSourceRef.current) {
      console.log('🛑 [Stream Hook] 取消流式请求');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setLoading(false);
    setProgress(null);
  }, []);

  // 清理函数
  useEffect(() => {
    return () => {
      cancelStream();
    };
  }, [cancelStream]);

  // 获取流式每日利用率趋势
  const fetchStreamDailyTrend = useCallback((startDate: string, endDate: string) => {
    // 取消之前的请求
    cancelStream();
    
    setLoading(true);
    setError(null);
    setProgress(null);
    accumulatedDataRef.current = [];

    console.log('🚀 [Stream Hook] 开始流式获取数据:', { startDate, endDate });

    // 构建URL
    const params = new URLSearchParams();
    params.append('start_date', startDate);
    params.append('end_date', endDate);
    const url = `/api/utilization/stream-daily-trend?${params.toString()}`;

    // 创建EventSource
    const eventSource = new EventSource(url);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      console.log('✅ [Stream Hook] SSE连接已建立');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case 'init':
            console.log('📋 [Stream Hook] 初始化信息:', data);
            setProgress({
              current: 0,
              total: data.total_dates,
              percentage: 0
            });
            break;

          case 'daily_data':
            console.log(`📊 [Stream Hook] 接收到 ${data.data.date} 数据:`, data.data);
            
            // 累积数据
            accumulatedDataRef.current.push(data.data);
            
            // 更新进度
            setProgress(data.progress);
            
            // 实时更新趋势数据（无感渲染）
            const currentData = [...accumulatedDataRef.current];
            const utilizationRates = currentData.map(d => d.utilization_rate);
            const averageRate = utilizationRates.length > 0 ? utilizationRates.reduce((a, b) => a + b, 0) / utilizationRates.length : 0;
            const maxRate = utilizationRates.length > 0 ? Math.max(...utilizationRates) : 0;
            const minRate = utilizationRates.length > 0 ? Math.min(...utilizationRates) : 0;

            // 计算趋势方向
            let trendDirection = 'stable';
            if (currentData.length >= 2) {
              const firstRate = currentData[currentData.length - 1].utilization_rate;
              const lastRate = currentData[0].utilization_rate;
              const diff = lastRate - firstRate;
              if (diff > 2) {
                trendDirection = 'up';
              } else if (diff < -2) {
                trendDirection = 'down';
              }
            }

            setDailyTrend({
              start_date: startDate,
              end_date: endDate,
              daily_data: currentData,
              average_rate: averageRate,
              max_rate: maxRate,
              min_rate: minRate,
              trend_direction: trendDirection,
              last_updated: new Date().toISOString()
            });
            break;

          case 'complete':
            console.log('🎉 [Stream Hook] 流式传输完成:', data.summary);
            setDailyTrend(data.summary);
            setLoading(false);
            setProgress(null);
            eventSource.close();
            eventSourceRef.current = null;
            break;

          case 'error':
            console.error('❌ [Stream Hook] 服务器错误:', data.message);
            setError(data.message);
            setLoading(false);
            setProgress(null);
            eventSource.close();
            eventSourceRef.current = null;
            break;

          default:
            console.warn('⚠️ [Stream Hook] 未知消息类型:', data.type);
        }
      } catch (err) {
        console.error('❌ [Stream Hook] 解析消息失败:', err);
        setError('数据解析失败');
      }
    };

    eventSource.onerror = (event) => {
      console.error('❌ [Stream Hook] SSE连接错误:', event);
      setError('连接失败，请稍后重试');
      setLoading(false);
      setProgress(null);
      eventSource.close();
      eventSourceRef.current = null;
    };

  }, [cancelStream]);

  return {
    dailyTrend,
    loading,
    error,
    progress,
    fetchStreamDailyTrend,
    cancelStream
  };
}
