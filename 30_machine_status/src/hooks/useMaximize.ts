'use client';

import { useState, useCallback, useEffect } from 'react';

/**
 * 最大化状态钩子
 * 
 * 管理应用的最大化状态，包括进入/退出全屏模式
 * 
 * @returns {Object} 包含最大化状态和切换函数
 */
export function useMaximize() {
  // 最大化状态
  const [isMaximized, setIsMaximized] = useState(false);

  /**
   * 切换最大化状态
   * 当点击最大化按钮时调用
   * 同时控制浏览器全屏模式
   */
  const toggleMaximize = useCallback(() => {
    // 切换最大化状态
    setIsMaximized(prev => {
      const newState = !prev;

      // 根据新状态切换全屏模式
      if (newState) {
        // 进入全屏模式
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        // 退出全屏模式
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }

      return newState;
    });
  }, []);

  // 监听全屏变化事件，同步状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      // 如果浏览器退出了全屏模式，同步更新我们的状态
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };

    // 添加全屏变化事件监听
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    // 清理函数
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  return { isMaximized, toggleMaximize };
}
