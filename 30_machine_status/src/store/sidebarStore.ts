'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { SidebarMode } from '@/components/layout/Sidebar';

// 侧边栏状态存储接口
interface SidebarState {
  mode: SidebarMode;
  setMode: (mode: SidebarMode) => void;
  toggleMode: () => void;
}

// 创建侧边栏状态存储
export const useSidebarStore = create<SidebarState>()(
  persist(
    (set) => ({
      // 默认为隐藏状态
      mode: SidebarMode.HIDDEN,

      // 设置侧边栏模式
      setMode: (mode: SidebarMode) => set({ mode }),

      // 切换侧边栏模式
      toggleMode: () => set((state) => {
        switch (state.mode) {
          case SidebarMode.HIDDEN:
            return { mode: SidebarMode.COLLAPSED };
          case SidebarMode.COLLAPSED:
            return { mode: SidebarMode.HIDDEN };
          default:
            return { mode: SidebarMode.HIDDEN };
        }
      }),
    }),
    {
      name: 'sidebar-storage', // 本地存储的键名
    }
  )
);
