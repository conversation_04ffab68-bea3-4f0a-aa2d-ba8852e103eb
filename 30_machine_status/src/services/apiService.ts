/**
 * 统一API服务配置
 *
 * 提供统一的后端API调用接口，避免硬编码API地址
 * 支持环境变量配置，便于不同环境部署
 * 支持Docker容器间通信和本地开发环境
 */

import { getApiBaseUrl, getBackendApiUrl } from '../config/api';

/**
 * API配置类
 *
 * 功能：
 * - 统一管理API基础URL配置
 * - 支持环境变量和默认值
 * - 支持Docker容器间通信
 * - 提供类型安全的API调用方法
 */
class APIConfig {
  private baseURL: string;

  constructor() {
    // 使用统一的API配置逻辑
    // 这会自动处理Docker环境和本地开发环境的差异
    this.baseURL = getApiBaseUrl();

    // 确保URL不以斜杠结尾
    this.baseURL = this.baseURL.replace(/\/$/, '');
  }

  /**
   * 获取API基础URL
   * 根据运行环境自动选择合适的URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }

  /**
   * 获取后端API的直接URL
   * 用于服务器端调用
   */
  getBackendURL(): string {
    return getBackendApiUrl().replace(/\/$/, '');
  }

  /**
   * 构建完整的API URL
   * @param endpoint API端点路径（以/开头）
   * @returns 完整的API URL
   */
  buildURL(endpoint: string): string {
    // 确保端点以斜杠开头
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${this.baseURL}${cleanEndpoint}`;
  }

  /**
   * 构建后端API URL（用于服务器端调用）
   * @param endpoint API端点路径（以/开头）
   * @returns 完整的后端API URL
   */
  buildBackendURL(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${this.getBackendURL()}${cleanEndpoint}`;
  }
}

// 创建全局API配置实例
const apiConfig = new APIConfig();

/**
 * 统一的API调用服务
 * 
 * 功能：
 * - 提供标准化的HTTP请求方法
 * - 统一错误处理和超时控制
 * - 自动添加通用请求头
 */
export class APIService {
  private config: APIConfig;
  private defaultTimeout: number = 10000; // 10秒默认超时

  constructor() {
    this.config = apiConfig;
  }

  /**
   * 通用的fetch请求方法
   * @param endpoint API端点
   * @param options fetch选项
   * @param timeout 自定义超时时间（毫秒），如果为0则不设置超时
   * @returns Promise<Response>
   */
  private async request(endpoint: string, options: RequestInit = {}, timeout?: number): Promise<Response> {
    const url = this.config.buildURL(endpoint);

    // 设置超时控制
    const controller = new AbortController();
    const actualTimeout = timeout !== undefined ? timeout : this.defaultTimeout;
    let timeoutId: NodeJS.Timeout | null = null;

    if (actualTimeout > 0) {
      timeoutId = setTimeout(() => controller.abort(), actualTimeout);
    }

    try {
      // 合并默认选项和用户选项
      const requestOptions: RequestInit = {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          ...options.headers,
        },
      };

      console.log(`📡 API请求: ${options.method || 'GET'} ${url}`);

      const response = await fetch(url, requestOptions);

      // 清除超时
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      return response;
    } catch (error: any) {
      // 清除超时
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }

      console.error(`❌ API请求失败: ${url}`, error);
      throw error;
    }
  }

  /**
   * GET请求
   * @param endpoint API端点
   * @param timeout 自定义超时时间（毫秒），如果为0则不设置超时
   * @returns Promise<any>
   */
  async get(endpoint: string, timeout?: number): Promise<any> {
    const response = await this.request(endpoint, { method: 'GET' }, timeout);
    return response.json();
  }

  /**
   * POST请求
   * @param endpoint API端点
   * @param data 请求数据
   * @returns Promise<any>
   */
  async post(endpoint: string, data?: any): Promise<any> {
    const response = await this.request(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
    return response.json();
  }

  /**
   * PUT请求
   * @param endpoint API端点
   * @param data 请求数据
   * @returns Promise<any>
   */
  async put(endpoint: string, data?: any): Promise<any> {
    const response = await this.request(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
    return response.json();
  }

  /**
   * DELETE请求
   * @param endpoint API端点
   * @returns Promise<any>
   */
  async delete(endpoint: string): Promise<any> {
    const response = await this.request(endpoint, { method: 'DELETE' });
    return response.json();
  }

  /**
   * 获取API基础URL（用于调试）
   */
  getBaseURL(): string {
    return this.config.getBaseURL();
  }
}

// 创建全局API服务实例
export const apiService = new APIService();

/**
 * 设备相关API接口
 */
export const deviceAPI = {
  /**
   * 获取所有设备状态
   * 直接调用后端API，使用环境变量统一后台连接
   * @param date 可选的日期参数
   * @param devices 可选的设备ID列表
   */
  getMachines: async (date?: string, devices?: string[]) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);
    if (devices && devices.length > 0) params.append('devices', devices.join(','));

    // 直接调用后端API，使用环境变量配置
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    const url = `${backendUrl}/api/machines${params.toString() ? `?${params.toString()}` : ''}`;

    console.log(`📡 [Device API] 直接调用后端: GET ${url}`);

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      });

      if (!response.ok) {
        throw new Error(`后端API请求失败: ${response.status} ${response.statusText}`);
      }

      return response.json();
    } catch (error) {
      console.error(`❌ [Device API] 获取设备列表失败:`, error);
      throw error;
    }
  },

  /**
   * 获取单个设备状态
   * 直接调用后端API，使用环境变量统一后台连接
   * @param deviceId 设备ID
   * @param date 可选的日期参数
   */
  getMachine: async (deviceId: string, date?: string) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);

    // 直接调用后端API，使用环境变量配置
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    const url = `${backendUrl}/api/machines/${deviceId}${params.toString() ? `?${params.toString()}` : ''}`;

    console.log(`📡 [Device API] 直接调用后端: GET ${url}`);

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      });

      if (!response.ok) {
        throw new Error(`后端API请求失败: ${response.status} ${response.statusText}`);
      }

      return response.json();
    } catch (error) {
      console.error(`❌ [Device API] 获取设备 ${deviceId} 失败:`, error);
      throw error;
    }
  },

  /**
   * 从MongoDB获取单台设备的详细配置信息
   *
   * 功能：获取设备的基本信息，如名称、型号、位置、品牌等
   * 这个API专门用于获取设备的配置信息，不包含状态数据
   *
   * @param {string} deviceId - 设备ID
   * @returns {Promise<Object>} 设备配置信息对象
   *
   * @example
   * const deviceInfo = await deviceAPI.getMachineInfo('test-37');
   * console.log(deviceInfo.name, deviceInfo.location, deviceInfo.brand);
   */
  getMachineInfo: (deviceId: string) => {
    console.log(`🔍 [API Service] 获取设备配置信息: ${deviceId}`);
    const endpoint = `/api/machine/${deviceId}`;
    return apiService.get(endpoint);
  },

  /**
   * 获取设备状态历史
   * @param deviceId 设备ID
   * @param date 可选的日期参数
   */
  getDeviceStatusHistory: (deviceId: string, date?: string) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);

    const endpoint = `/api/devices/${deviceId}/status-history${params.toString() ? `?${params.toString()}` : ''}`;
    return apiService.get(endpoint);
  },

  /**
   * 获取设备状态历史（V2版本，支持后端预处理）
   * @param deviceId 设备ID
   * @param date 可选的日期参数
   */
  getDeviceStatusHistoryV2: (deviceId: string, date?: string) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);

    const endpoint = `/api/devices/${deviceId}/status-history${params.toString() ? `?${params.toString()}` : ''}`;
    return apiService.get(endpoint);
  },

  /**
   * 获取生产历史数据
   * @param date 可选的日期参数
   * @param devices 可选的设备ID列表
   */
  getProductionHistory: (date?: string, devices?: string[]) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);
    if (devices && devices.length > 0) params.append('devices', devices.join(','));

    const endpoint = `/api/production-history${params.toString() ? `?${params.toString()}` : ''}`;
    return apiService.get(endpoint);
  },

  /**
   * 获取生产进度数据
   * @param date 可选的日期参数
   * @param devices 可选的设备ID列表
   */
  getProductionProgress: (date?: string, devices?: string[]) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);
    if (devices && devices.length > 0) params.append('devices', devices.join(','));

    const endpoint = `/api/production/progress${params.toString() ? `?${params.toString()}` : ''}`;
    return apiService.get(endpoint);
  },
};

/**
 * 利用率相关API接口
 */
export const utilizationAPI = {
  /**
   * 获取利用率概览
   * @param date 可选的日期参数
   * @param timeout 自定义超时时间（毫秒），如果为0则不设置超时
   */
  getOverview: (date?: string, timeout?: number) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);

    const endpoint = `/api/utilization/overview${params.toString() ? `?${params.toString()}` : ''}`;
    return apiService.get(endpoint, timeout);
  },

  /**
   * 获取设备利用率排名
   * @param date 可选的日期参数
   * @param timeout 自定义超时时间（毫秒），如果为0则不设置超时
   */
  getDeviceRanking: (date?: string, timeout?: number) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);

    const endpoint = `/api/utilization/devices${params.toString() ? `?${params.toString()}` : ''}`;
    return apiService.get(endpoint, timeout);
  },

  /**
   * 获取每日利用率趋势（使用准确算法，与device_utilization_machine页面一致）
   * @param startDate 开始日期（YYYY-MM-DD）
   * @param endDate 结束日期（YYYY-MM-DD）
   * @param timeout 自定义超时时间（毫秒），如果为0则不设置超时
   */
  getDailyTrend: async (startDate: string, endDate: string, timeout?: number) => {
    const params = new URLSearchParams();
    params.append('start_date', startDate);
    params.append('end_date', endDate);

    // 直接调用前端准确算法API，确保与单日详细页面数据一致
    const url = `/api/utilization/accurate-daily-trend?${params.toString()}`;

    console.log(`📡 API请求: GET ${url}`);

    // 设置超时控制
    const controller = new AbortController();
    const actualTimeout = timeout !== undefined ? timeout : 10000;
    let timeoutId: NodeJS.Timeout | null = null;

    if (actualTimeout > 0) {
      timeoutId = setTimeout(() => controller.abort(), actualTimeout);
    }

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
        signal: controller.signal,
      });

      // 清除超时
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      return response.json();
    } catch (error: any) {
      // 清除超时
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }

      console.error(`❌ API请求失败: ${url}`, error);
      throw error;
    }
  },

  /**
   * 获取准确的每日利用率趋势（使用device_utilization_machine的算法）
   * @param startDate 开始日期（YYYY-MM-DD）
   * @param endDate 结束日期（YYYY-MM-DD）
   * @param timeout 自定义超时时间（毫秒），如果为0则不设置超时
   */
  getAccurateDailyTrend: (startDate: string, endDate: string, timeout?: number) => {
    const params = new URLSearchParams();
    params.append('start_date', startDate);
    params.append('end_date', endDate);

    const endpoint = `/api/utilization/accurate-daily-trend?${params.toString()}`;
    return apiService.get(endpoint, timeout);
  },
};

/**
 * 统计相关API接口
 */
export const statisticsAPI = {
  /**
   * 获取工作时间配置
   * 直接调用后端API
   */
  getWorkTimeSettings: () => apiService.get('/api/statistics/worktime'),

  /**
   * 保存工作时间配置
   * @param settings 工作时间配置数据
   * 直接调用后端API
   */
  saveWorkTimeSettings: (settings: any) => apiService.post('/api/statistics/worktime', settings),
};

/**
 * 设置相关API接口
 */
export const settingsAPI = {
  /**
   * 获取显示设置
   */
  getDisplaySettings: () => apiService.get('/api/settings/display'),

  /**
   * 保存显示设置
   * @param settings 显示设置数据
   */
  saveDisplaySettings: (settings: any) => apiService.post('/api/settings/display', settings),

  /**
   * 获取刷新设置
   */
  getRefreshSettings: () => apiService.get('/api/settings/refresh'),

  /**
   * 保存刷新设置
   * @param settings 刷新设置数据
   */
  saveRefreshSettings: (settings: any) => apiService.post('/api/settings/refresh', settings),
};

// 导出默认实例
export default apiService;
