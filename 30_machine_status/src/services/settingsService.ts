/**
 * 设置服务
 *
 * 提供与设置相关的API交互功能
 * 包括获取和保存系统设置
 */

// 刷新设置接口
export interface RefreshSettings {
  autoRefresh: boolean;
  refreshInterval: number;
  showRefreshIndicator: boolean;
}

// 设备卡片内容设置接口
export interface CardContentSettings {
  showCode: boolean;      // 是否显示设备编码
  showName: boolean;      // 是否显示设备名称
  showLocation: boolean;  // 是否显示设备位置
  showModel: boolean;     // 是否显示设备型号
  showQuantity: boolean;  // 是否显示当前产量数量
  showPlan: boolean;      // 是否显示计划生产数量
  fontSize: number;       // 字体大小（像素）
  textAlign: string;      // 文本对齐方式（"left", "center", "right"）
  layoutRatio: number;    // 左右布局比例（左侧占比，如60表示左侧占60%）
}

// 设备卡片样式设置接口
export interface CardStyleSettings {
  cardWidth: number;      // 卡片宽度（像素）
  cardHeight: number;     // 卡片高度（像素）
  borderRadius: number;   // 边框圆角（像素）
  borderWidth: number;    // 边框宽度（像素）
  contentPadding: number; // 内容区域内边距（像素）
}

// 状态颜色设置接口
export interface StatusColorSettings {
  production: string;     // 生产中状态颜色
  idle: string;           // 空闲状态颜色
  fault: string;          // 故障状态颜色
  adjusting: string;      // 调机状态颜色
  shutdown: string;       // 关机状态颜色
  disconnected: string;   // 未连接状态颜色
  maintenance: string;    // 保养状态颜色
  debugging: string;      // 调试状态颜色
}

// 设备状态历史设置接口
export interface StatusHistorySettings {
  timeSlotWidth: number;  // 时间段宽度（像素）
  timelineHeight: number; // 时间条高度（像素）
  timelineGap: number;    // 时间条之间的间隔（像素）
}

// 生产进度设置接口
export interface ProductionProgressSettings {
  timeAxisStartHour: number; // 时间轴起始小时（0-23）
}

// 利用率仪表板设置接口
export interface UtilizationDashboardSettings {
  layoutMode: "split" | "triple"; // 布局模式：split(左右分屏) 或 triple(三分布局)
  rankingDisplayLimit: number;    // 排名显示数量限制，-1表示不限制
}

// 显示设置接口
export interface DisplaySettings {
  deviceGridGap: number;  // 设备网格间隔（像素）
  deviceRowGap: number;   // 设备行间隔（像素）
  deviceColumnGap: number; // 设备列间隔（像素）
  statusDeviceGap: number; // 状态栏与设备列表间隔（像素）
  statusCountDigits: number; // 状态计数显示的位数
  statusCountPadChar: string; // 状态计数前置填充字符（"0"或" "空格）
  filterSignalDuration: number; // 过滤信号时长（秒），默认1秒
  cardContent: CardContentSettings; // 设备卡片内容设置
  cardStyle: CardStyleSettings;     // 设备卡片样式设置
  statusColors: StatusColorSettings; // 状态颜色设置
  statusHistory: StatusHistorySettings; // 设备状态历史设置
  productionProgress?: ProductionProgressSettings; // 生产进度设置
  utilizationDashboard?: UtilizationDashboardSettings; // 利用率仪表板设置
}

// 系统设置接口
export interface SystemSettings {
  refresh: RefreshSettings;
  display: DisplaySettings;
  data?: any;
  notification?: any;
  security?: any;
}

/**
 * 获取系统所有设置
 *
 * @returns 系统设置对象
 */
export async function getSystemSettings(): Promise<SystemSettings> {
  try {
    const response = await fetch('/api/settings');
    if (!response.ok) {
      throw new Error('获取系统设置失败');
    }
    return await response.json();
  } catch (error) {
    console.error('获取系统设置出错:', error);
    // 返回默认设置
    return {
      refresh: {
        autoRefresh: true,
        refreshInterval: 1000,
        showRefreshIndicator: true
      },
      display: {
        deviceGridGap: 12,
        deviceRowGap: 12,
        deviceColumnGap: 12,
        statusDeviceGap: 12,
        statusCountDigits: 2,
        statusCountPadChar: "0",
        filterSignalDuration: 1,
        cardContent: {
          showCode: true,
          showName: true,
          showLocation: true,
          showModel: true,
          showQuantity: true,
          showPlan: true,
          fontSize: 14,
          textAlign: "center",
          layoutRatio: 60
        },
        cardStyle: {
          cardWidth: 200,
          cardHeight: 120,
          borderRadius: 4,
          borderWidth: 2,
          contentPadding: 8
        },
        statusColors: {
          production: "#22c55e",
          idle: "#f59e0b",
          fault: "#ef4444",
          adjusting: "#3b82f6",
          shutdown: "#6b7280",
          disconnected: "#9ca3af",
          maintenance: "#8b5cf6",
          debugging: "#06b6d4"
        },
        statusHistory: {
          timeSlotWidth: 120,
          timelineHeight: 40,
          timelineGap: 8
        }
      }
    };
  }
}

/**
 * 获取刷新设置
 *
 * @returns 刷新设置对象
 */
export async function getRefreshSettings(): Promise<RefreshSettings> {
  try {
    // 使用统一API服务
    const { settingsAPI } = await import('./apiService');
    return await settingsAPI.getRefreshSettings();
  } catch (error) {
    console.error('获取刷新设置出错:', error);
    // 返回默认设置而不是抛出错误
    return {
      autoRefresh: true,
      refreshInterval: 5000, // 5秒，与后端缓存时间一致
      showRefreshIndicator: true
    };
  }
}

/**
 * 保存刷新设置
 *
 * @param settings 要保存的刷新设置
 * @returns 保存后的刷新设置对象
 */
export async function saveRefreshSettings(settings: RefreshSettings): Promise<RefreshSettings> {
  try {
    // 使用统一API服务
    const { settingsAPI } = await import('./apiService');
    return await settingsAPI.saveRefreshSettings(settings);
  } catch (error) {
    console.error('保存刷新设置出错:', error);
    throw error;
  }
}

/**
 * 获取显示设置
 *
 * @returns 显示设置对象
 */
export async function getDisplaySettings(): Promise<DisplaySettings> {
  // 默认设置
  const defaultSettings = {
    deviceGridGap: 12,
    deviceRowGap: 12,
    deviceColumnGap: 12,
    statusDeviceGap: 12,
    statusCountDigits: 2,
    statusCountPadChar: "0",
    filterSignalDuration: 1, // 默认过滤1秒以下的信号
    cardContent: {
      showCode: true,
      showName: true,
      showLocation: true,
      showModel: true,
      showQuantity: true,
      showPlan: true,
      fontSize: 14,
      textAlign: "center",
      layoutRatio: 60
    },
    cardStyle: {
      cardWidth: 200,
      cardHeight: 120,
      borderRadius: 4,
      borderWidth: 2,
      contentPadding: 8
    },
    statusColors: {
      production: "#22c55e",
      idle: "#f59e0b",
      fault: "#ef4444",
      adjusting: "#3b82f6",
      shutdown: "#6b7280",
      disconnected: "#9ca3af",
      maintenance: "#8b5cf6",
      debugging: "#06b6d4"
    },
    statusHistory: {
      timeSlotWidth: 120,
      timelineHeight: 40,
      timelineGap: 8
    },
    productionProgress: {
      timeAxisStartHour: 8
    },
    utilizationDashboard: {
      layoutMode: "split" as "split" | "triple",
      rankingDisplayLimit: 20
    }
  };

  // 从后端API获取设置
  try {
    // 使用统一API服务
    const { settingsAPI } = await import('./apiService');
    const apiSettings = await settingsAPI.getDisplaySettings();

    // 合并默认设置和API设置，确保新字段有默认值
    const mergedSettings = {
      ...defaultSettings,
      ...apiSettings,
      utilizationDashboard: {
        ...defaultSettings.utilizationDashboard,
        ...(apiSettings.utilizationDashboard || {})
      }
    };

    console.log('✅ 从后端API加载显示设置');
    return mergedSettings;
  } catch (error) {
    console.error('获取显示设置出错:', error);
    console.log('⚠️ 使用默认显示设置');
    return defaultSettings;
  }
}

/**
 * 保存显示设置
 *
 * @param settings 要保存的显示设置
 * @returns 保存后的显示设置对象
 */
export async function saveDisplaySettings(settings: DisplaySettings): Promise<DisplaySettings> {
  try {
    // 使用统一API服务
    const { settingsAPI } = await import('./apiService');
    return await settingsAPI.saveDisplaySettings(settings);
  } catch (error) {
    console.error('保存显示设置出错:', error);
    throw error;
  }
}
