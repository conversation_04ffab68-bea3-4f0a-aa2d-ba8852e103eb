/**
 * 设备服务
 *
 * 提供设备相关的API调用
 */

/**
 * 获取设备状态历史
 * @param deviceId 设备ID
 * @param date 日期（YYYY-MM-DD格式，可选）
 * @returns 设备状态历史数据
 */
export async function getDeviceStatusHistory(deviceId: string, date?: string) {
  try {
    // 使用统一API服务
    const { deviceAPI } = await import('./apiService');
    console.log(`📡 请求设备状态历史: ${deviceId}, date: ${date || 'auto'}`);
    return await deviceAPI.getDeviceStatusHistory(deviceId, date);
  } catch (error: any) {
    console.error('获取设备状态历史失败:', error);
    throw error;
  }
}

/**
 * 获取设备状态历史（V2版本，支持后端预处理）
 * @param deviceId 设备ID
 * @param date 日期（YYYY-MM-DD格式，可选）
 * @returns 预处理的设备状态历史数据，按时间段分组
 */
export async function getDeviceStatusHistoryV2(deviceId: string, date?: string) {
  try {
    // 使用统一API服务
    const { deviceAPI } = await import('./apiService');
    console.log(`📡 请求设备状态历史 V2: ${deviceId}, date: ${date || 'auto'}`);
    return await deviceAPI.getDeviceStatusHistoryV2(deviceId, date);
  } catch (error: any) {
    console.error('获取设备状态历史 V2 失败:', error);
    throw error;
  }
}

/**
 * 获取设备详情
 * @param deviceId 设备ID
 * @returns 设备详情
 */
export async function getDeviceDetails(deviceId: string) {
  try {
    // 使用统一API服务
    const { deviceAPI } = await import('./apiService');
    return await deviceAPI.getMachine(deviceId);
  } catch (error: any) {
    console.error('获取设备详情失败:', error);
    throw error;
  }
}

/**
 * 获取设备配置信息
 *
 * 功能：从MongoDB获取设备的基本配置信息，如名称、型号、位置等
 * 这个方法专门用于获取设备的静态配置信息，不包含动态状态数据
 *
 * @param {string} deviceId - 设备ID
 * @returns {Promise<Object>} 设备配置信息对象
 *
 * @example
 * const deviceInfo = await getDeviceInfo('test-37');
 * console.log(deviceInfo.name, deviceInfo.location, deviceInfo.brand);
 */
export async function getDeviceInfo(deviceId: string) {
  try {
    console.log(`🔍 [Device Service] 获取设备配置信息: ${deviceId}`);

    // 使用统一API服务的新方法
    const { deviceAPI } = await import('./apiService');
    const deviceInfo = await deviceAPI.getMachineInfo(deviceId);

    console.log(`✅ [Device Service] 成功获取设备配置信息:`, {
      device_id: deviceInfo.device_id,
      name: deviceInfo.name,
      location: deviceInfo.location,
      brand: deviceInfo.brand,
      model: deviceInfo.model
    });

    return deviceInfo;
  } catch (error: any) {
    console.error(`❌ [Device Service] 获取设备配置信息失败 (${deviceId}):`, error);

    // 如果获取失败，返回默认信息
    console.log(`📝 [Device Service] 使用默认设备信息: ${deviceId}`);
    return {
      id: deviceId,
      device_id: deviceId,
      name: deviceId,
      location: 'unknown',
      brand: 'unknown',
      model: 'unknown',
      data_type: 'unknown',
      description: `设备 ${deviceId}`
    };
  }
}
