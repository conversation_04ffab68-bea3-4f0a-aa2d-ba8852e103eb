/**
 * 产量历史服务
 *
 * 提供与产量历史相关的API交互功能
 * 包括获取设备产量历史数据
 */
import { ProductionHistoryResponse } from '@/types';

/**
 * 获取产量历史数据
 *
 * @param date 可选的日期参数，格式为YYYY-MM-DD
 * @returns 产量历史响应对象
 */
export async function getProductionHistory(date?: string): Promise<ProductionHistoryResponse> {
  try {
    // 构建URL，如果提供了日期则添加查询参数
    let url = '/api/production-history';
    if (date) {
      url += `?date=${date}`;
    }

    console.log('请求产量历史数据:', url);

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 清除超时计时器
    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API响应错误:', response.status, errorText);
      throw new Error(`获取产量历史数据失败: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error: any) {
    console.error('获取产量历史数据出错:', error);

    // 处理超时错误
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请稍后重试');
    }

    // 在错误情况下，重新抛出错误，让调用者处理
    throw error;
  }
}
