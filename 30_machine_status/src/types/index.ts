/**
 * 定义机器的可能状态
 * MachineStatus - 设备状态的联合类型
 */
export type MachineStatus =
  | 'production'   // 生产中 - 设备正在生产产品
  | 'idle'         // 空闲 - 设备已开机但未生产
  | 'fault'        // 故障 - 设备发生故障需要维修
  | 'adjusting'    // 调机 - 设备正在进行调整或设置
  | 'shutdown'     // 关机 - 设备已关闭
  | 'disconnected' // 未连接 - 设备与系统断开连接
  | 'maintenance'  // 保养 - 设备正在进行维护保养
  | 'debugging';   // 调试 - 设备正在进行调试

/**
 * 机器信息接口
 * Machine - 表示单个生产设备的数据结构
 */
export interface Machine {
  id: string;      // 唯一标识符（字符串类型，支持非数字ID）
  brand: string;   // 设备品牌
  name: string;    // 设备名称
  location: string; // 设备位置/区域
  model: string;   // 设备型号
  status: MachineStatus; // 当前设备状态
  quantity: number; // 当前产量数量
  plan: number;    // 计划生产数量
}

/**
 * 状态计数接口
 * StatusCount - 用于统计各种状态的设备数量
 */
export interface StatusCount {
  production: number;   // 生产中的设备数量
  idle: number;         // 空闲设备数量
  fault: number;        // 故障设备数量
  adjusting: number;    // 调机中的设备数量（保留用于兼容性，但不在UI中显示）
  shutdown: number;     // 已关机的设备数量
  disconnected: number; // 未连接的设备数量（保留用于兼容性，但不在UI中显示）
  maintenance: number;  // 保养中的设备数量
  debugging: number;    // 调试中的设备数量
}

/**
 * 公司信息接口
 * CompanyInfo - 显示在界面上的公司信息
 */
export interface CompanyInfo {
  name: string;           // 公司名称
  dateTime: string;       // 日期时间信息
  logo?: string;          // 公司logo（可选）
  supportContact?: string; // 技术支持联系信息（可选）
  support?: string;       // 技术支持公司名称（从API获取）
  support_info?: string;  // 技术支持详细信息（从API获取）
}

/**
 * 产品信息接口
 * Product - 表示生产的产品信息
 */
export interface Product {
  id: string;         // 产品唯一标识符
  code: string;       // 产品编码
  name: string;       // 产品名称
  processCode: string; // 工序编码
  processName: string; // 工序名称
  program: string;     // 工序程序
}

/**
 * 产量记录接口
 * ProductionRecord - 表示设备的产量记录
 */
export interface ProductionRecord {
  id: string;         // 记录唯一标识符
  deviceId: string;   // 设备ID
  productId: string;  // 产品ID
  date: string;       // 记录日期 (YYYY-MM-DD)
  quantity: number;   // 产量数量
  plan: number;       // 计划数量
  startTime: string;  // 开始时间
  endTime?: string;   // 结束时间或当前时间（可选）
}

/**
 * 产量历史响应接口
 * ProductionHistoryResponse - API响应的产量历史数据
 */
export interface ProductionHistoryResponse {
  machines: Machine[];           // 设备列表
  products: Product[];           // 产品列表
  productionRecords: ProductionRecord[]; // 产量记录列表
  companyInfo: CompanyInfo;      // 公司信息
}

/**
 * 刷新设置接口
 * RefreshSettings - 控制数据刷新的设置
 */
export interface RefreshSettings {
  autoRefresh: boolean;         // 是否自动刷新
  refreshInterval: number;      // 刷新间隔（毫秒）
  showRefreshIndicator: boolean; // 是否显示刷新指示器
}

/**
 * 卡片内容设置接口
 * CardContentSettings - 控制设备卡片内容显示的设置
 */
export interface CardContentSettings {
  showCode: boolean;            // 是否显示设备品牌
  showName: boolean;            // 是否显示设备名称
  showLocation: boolean;        // 是否显示设备位置
  showModel: boolean;           // 是否显示设备型号
  showQuantity: boolean;        // 是否显示产量
  showPlan: boolean;            // 是否显示计划
  fontSize: number;             // 字体大小
  textAlign: string;            // 文本对齐方式
  layoutRatio: number;          // 布局比例
}

/**
 * 卡片样式设置接口
 * CardStyleSettings - 控制设备卡片样式的设置
 */
export interface CardStyleSettings {
  cardWidth: number;            // 卡片宽度
  cardHeight: number;           // 卡片高度
  borderRadius: number;         // 边框圆角
  borderWidth: number;          // 边框宽度
  contentPadding: number;       // 内容内边距
}

/**
 * 状态历史设置接口
 * StatusHistorySettings - 控制状态历史显示的设置
 */
export interface StatusHistorySettings {
  timeSlotWidth: number;        // 时间槽宽度
  timelineHeight: number;       // 时间线高度
  timelineGap: number;          // 时间线间隔
}

/**
 * 生产进度设置接口
 * ProductionProgressSettings - 控制生产进度显示的设置
 */
export interface ProductionProgressSettings {
  timeAxisStartHour: number;    // 时间轴起始小时（0-23）
}

/**
 * 显示设置接口
 * DisplaySettings - 控制界面显示的设置
 */
export interface DisplaySettings {
  deviceGridGap: number;        // 设备网格间隔
  deviceRowGap: number;         // 设备行间隔
  deviceColumnGap: number;      // 设备列间隔
  statusDeviceGap: number;      // 状态栏与设备列表间隔
  statusCountDigits: number;    // 状态计数位数
  statusCountPadChar: string;   // 状态计数填充字符
  cardContent: CardContentSettings; // 卡片内容设置
  cardStyle: CardStyleSettings; // 卡片样式设置
  statusColors: Record<string, string>; // 状态颜色设置
  statusHistory: StatusHistorySettings; // 状态历史设置
  productionProgress?: ProductionProgressSettings; // 生产进度设置
}
