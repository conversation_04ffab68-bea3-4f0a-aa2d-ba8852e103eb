@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --production-color: #22c55e;
  --idle-color: #f59e0b;
  --fault-color: #ef4444;
  --adjusting-color: #3b82f6;
  --shutdown-color: #6b7280;
}

@layer base {
  body {
    @apply bg-gray-900 text-white;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold;
  }
}

@layer components {
  .status-indicator {
    @apply inline-block w-3 h-3 rounded-full;
  }

  .machine-card {
    @apply border-2 rounded-md bg-gray-800 overflow-hidden;
  }
}

@layer utilities {
  .status-production {
    @apply border-green-500 text-green-500;
  }

  .status-idle {
    @apply border-amber-500 text-amber-500;
  }

  .status-fault {
    @apply border-red-500 text-red-500;
  }

  .status-adjusting {
    @apply border-blue-500 text-blue-500;
  }

  .status-shutdown {
    @apply border-gray-500 text-gray-500;
  }

  .status-disconnected {
    @apply border-dashed border-gray-400 text-gray-400;
  }

  /* 隐藏滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
  }
}