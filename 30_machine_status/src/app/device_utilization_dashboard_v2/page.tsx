'use client';

/**
 * 设备利用率仪表盘V2页面
 *
 * 功能概述：
 * 本页面是制造业数据采集系统的设备利用率分析核心界面V2版本，提供全面的设备利用率
 * 可视化分析功能，帮助管理者深入了解设备运行效率和生产状况
 *
 * V2版本特性：
 * - 后端统一数据处理：API直接返回页面所需的统计后的数据，前端不需要再进行计算
 * - 单一API调用：使用 /api/utilization/dashboard-v2 获取所有页面数据
 * - 简化前端逻辑：移除前端数据处理和计算逻辑，专注于数据展示
 * - 优化性能：减少API调用次数，提高页面加载速度
 *
 * 核心功能模块：
 * 1. 设备利用率概览 - 总体利用率统计和分区间分析
 * 2. 各设备利用率排名 - 水平堆叠条形图展示设备对比
 * 3. 每日利用率趋势 - 柱状图显示日趋势变化
 *
 * 技术特性：
 * - 实时数据更新：自动刷新，当天数据来自InfluxDB→Redis，历史数据来自MongoDB
 * - 响应式设计：适配桌面、平板、手机、智能电视等不同设备
 * - 交互式图表：支持hover、点击、缩放等交互操作
 * - 智能计算：支持OP1和OP2两种利用率计算模式（后端处理）
 * - 时间范围：支持自定义时间范围查询，默认当天至过往7天
 *
 * 数据来源：
 * - 统一API：/api/utilization/dashboard-v2 返回所有页面所需数据
 * - 当天数据：InfluxDB sensor_data → Redis缓存 → 12_server_api
 * - 历史数据：MongoDB device_statistics → 12_server_api
 * - 工作时间配置：MongoDB work_time_settings
 *
 * 设计风格：
 * - 现代科技感：深色主题，高对比度，专业的数据可视化
 * - 简洁高效：信息密度适中，重点突出，易于理解
 * - 视觉流畅：合理的布局层次，清晰的视觉引导
 * - 全屏优化：在全屏下能显示所有内容，自适应布局
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-22
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, RefreshCwIcon, PieChart, ChevronDown, ChevronUp } from 'lucide-react';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import DeviceRanking from '@/components/utilization/DeviceRanking';
import { getDisplaySettings, DisplaySettings } from '@/services/settingsService';
import { getApiBaseUrl } from '@/config/api';

// V3版本数据类型定义 - 使用新的API数据结构
interface RealtimeUtilizationData {
  last_updated: string;
  date: string;
  user_worktime: boolean;
  overall_utilization: number;
  total_running_time: number;
  total_status_time: number;
  working_time: number;
  total_status_summary: {
    fault: number;
    idle: number;
    production: number;
    shutdown: number;
    unknown: number;
  };
  device_count: number;
  device_utilizations: Array<{
    use_work_time: boolean;
    work_time: number;
    utilization: number;
    running_time: number;
    total_time: number;
    status_summary: {
      fault: number;
      idle: number;
      production: number;
      shutdown: number;
      unknown: number;
    };
    device: {
      id: string;
      device_id: string;
      name: string;
      data_type: string;
      location: string;
      status: string;
      sort_order: number;
      brand: string;
      model: string;
      ip: string;
      port: number;
      enabled: boolean;
      auto_start: boolean;
      collect_interval: number;
      timeout: number;
      retry_count: number;
      retry_delay: number;
      description: string;
      created_at: string;
      updated_at: string;
    };
  }>;
  utilization_zones: Array<{
    name: string;
    min_rate: number;
    max_rate: number;
    device_count: number;
    percentage: number;
    color: string;
  }>;
  work_time_settings: {
    id: string;
    day_start_time: string;
    shifts: Array<{
      name: string;
      start_time: string;
      end_time: string;
      enabled: boolean;
    }>;
    rest_periods: Array<{
      name: string;
      start_time: string;
      end_time: string;
      enabled: boolean;
    }>;
    utilization_mode: string;
    created_at: string;
    updated_at: string;
  };
}

interface DailyUtilizationData {
  last_updated: string;
  date: string;
  user_worktime: boolean;
  overall_utilization: number;
  total_running_time: number;
  total_status_time: number;
  working_time: number;
  date_range?: string[]; // 添加可选的date_range字段
  total_status_summary: {
    fault: number;
    idle: number;
    production: number;
    shutdown: number;
    unknown: number;
  };
  device_count: number;
  device_utilizations: Array<{
    use_work_time: boolean;
    work_time: number;
    utilization: number;
    running_time: number;
    total_time: number;
    status_summary: {
      fault: number;
      idle: number;
      production: number;
      shutdown: number;
      unknown: number;
    };
    device: {
      id: string;
      device_id: string;
      name: string;
      data_type: string;
      location: string;
      status: string;
      sort_order: number;
      brand: string;
      model: string;
      ip: string;
      port: number;
      enabled: boolean;
      auto_start: boolean;
      collect_interval: number;
      timeout: number;
      retry_count: number;
      retry_delay: number;
      description: string;
      created_at: string;
      updated_at: string;
    };
  }>;
  utilization_zones: Array<{
    name: string;
    min_rate: number;
    max_rate: number;
    device_count: number;
    percentage: number;
    color: string;
  }>;
  work_time_settings: {
    id: string;
    day_start_time: string;
    shifts: Array<{
      name: string;
      start_time: string;
      end_time: string;
      enabled: boolean;
    }>;
    rest_periods: Array<{
      name: string;
      start_time: string;
      end_time: string;
      enabled: boolean;
    }>;
    utilization_mode: string;
    created_at: string;
    updated_at: string;
  };
}

// V3版本仪表板数据结构
interface DashboardV3Data {
  // 实时利用率数据
  realtime: RealtimeUtilizationData | null;

  // 每日趋势数据
  daily: DailyUtilizationData[] | null;

  // 元数据
  last_updated: string;
}

export default function DeviceUtilizationDashboardV2() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // APP布局状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);
  const [isMaximized, setIsMaximized] = useState(false);
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '',
    dateTime: '',
    support: '',
    support_info: ''
  });

  // 控制栏折叠状态 - 默认收起
  const [isControlsCollapsed, setIsControlsCollapsed] = useState(true);

  // V3版本数据状态管理 - 分离的数据源
  const [dashboardData, setDashboardData] = useState<DashboardV3Data | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);
  const [refreshInterval, setRefreshInterval] = useState<number>(300); // 5分钟自动刷新
  const [isSilentRefreshing, setIsSilentRefreshing] = useState<boolean>(false); // 无感刷新状态

  // 时间范围状态 - 从URL参数初始化，默认当天至过往7天
  const [endDate, setEndDate] = useState<string>(() => {
    const urlEndDate = searchParams?.get('end_date');
    return urlEndDate || new Date().toISOString().split('T')[0];
  });
  const [startDate, setStartDate] = useState<string>(() => {
    const urlStartDate = searchParams?.get('start_date');
    if (urlStartDate) return urlStartDate;
    const date = new Date();
    date.setDate(date.getDate() - 7);
    return date.toISOString().split('T')[0];
  });

  // 显示设置状态
  const [displaySettings, setDisplaySettings] = useState<DisplaySettings | null>(null);

  // URL参数同步函数
  const updateUrlParams = useCallback((start: string, end: string) => {
    const params = new URLSearchParams();
    params.set('start_date', start);
    params.set('end_date', end);
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  }, []);

  // 时间范围变化处理函数
  const handleStartDateChange = useCallback((newStartDate: string) => {
    setStartDate(newStartDate);
    updateUrlParams(newStartDate, endDate);
  }, [endDate, updateUrlParams]);

  const handleEndDateChange = useCallback((newEndDate: string) => {
    setEndDate(newEndDate);
    updateUrlParams(startDate, newEndDate);
  }, [startDate, updateUrlParams]);

  /**
   * 获取站点信息
   * 从API获取站点基本信息（名称、技术支持等）
   */
  const fetchSiteInfo = useCallback(async () => {
    try {
      console.log('🌐 开始获取站点信息...');
      const API_HOST = getApiBaseUrl();
      const response = await fetch(`${API_HOST}/api/v3/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`站点信息API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 站点信息API响应:', result);

      if (result.success && result.data) {
        // 更新公司信息，保留现有的dateTime
        setCompanyInfo(prev => ({
          ...prev,
          name: result.data.name || '智能制造系统',
          support: result.data.support || '',
          support_info: result.data.support_info || ''
        }));
        console.log('✅ 站点信息更新成功:', {
          name: result.data.name,
          support: result.data.support,
          support_info: result.data.support_info
        });
      } else {
        console.warn('⚠️ 站点信息API返回数据格式异常:', result);
      }
    } catch (error) {
      console.error('❌ 获取站点信息失败:', error);
      // 设置默认值
      setCompanyInfo(prev => ({
        ...prev,
        name: '智能制造系统',
        support: '',
        support_info: ''
      }));
    }
  }, []);

  // V3版本：获取仪表板数据 - 分别调用两个API
  const fetchDashboardData = async (start: string, end: string, silent: boolean = false, isInitialLoad: boolean = false) => {
    if (!silent) {
      setLoading(true);
    }

    try {
      const API_HOST = getApiBaseUrl();

      // 设置超时控制
      const controller = new AbortController();
      const actualTimeout = isInitialLoad ? 0 : 60000; // 初始加载无超时限制，其他请求60秒超时
      let timeoutId: NodeJS.Timeout | null = null;

      if (actualTimeout > 0) {
        timeoutId = setTimeout(() => controller.abort(), actualTimeout);
      }

      // 并行请求两个API
      const [realtimeResponse, dailyResponse] = await Promise.all([
        // 获取实时利用率数据
        fetch(`${API_HOST}/api/v3/utilization/realtime`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
          signal: controller.signal,
        }),
        // 获取每日利用率趋势数据
        fetch(`${API_HOST}/api/v3/utilization/daily`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
          signal: controller.signal,
        })
      ]);

      // 清除超时
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      if (!realtimeResponse.ok) {
        throw new Error(`实时数据API请求失败: ${realtimeResponse.status} ${realtimeResponse.statusText}`);
      }

      if (!dailyResponse.ok) {
        throw new Error(`每日数据API请求失败: ${dailyResponse.status} ${dailyResponse.statusText}`);
      }

      const realtimeData = await realtimeResponse.json();
      const dailyData = await dailyResponse.json();

      console.log('🚀 V3版本实时数据加载成功:', realtimeData);
      console.log('📈 V3版本每日数据加载成功:', dailyData);

      // 组合数据
      const combinedData: DashboardV3Data = {
        realtime: realtimeData.success ? realtimeData.data : null,
        daily: dailyData.success ? dailyData.data : null,
        last_updated: new Date().toISOString()
      };

      setDashboardData(combinedData);
      setLastUpdated(new Date().toLocaleString('zh-CN'));
      setError(null);

    } catch (err: any) {
      console.error('❌ V3版本数据加载失败:', err);
      if (!silent) {
        setError(err.message || '获取数据失败');
      }
    } finally {
      if (!silent) {
        setLoading(false);
      }
    }
  };

  // 刷新所有数据（带加载状态，用于初始加载和手动刷新）
  const refreshAllData = useCallback(async (isInitialLoad: boolean = false) => {
    await fetchDashboardData(startDate, endDate, false, isInitialLoad);
  }, [startDate, endDate]);

  // 无感刷新所有数据（后台刷新，不显示加载状态）
  const silentRefreshAllData = useCallback(async () => {
    setIsSilentRefreshing(true);
    await fetchDashboardData(startDate, endDate, true);
    setIsSilentRefreshing(false);
  }, [startDate, endDate]);

  // 在客户端初始化时从localStorage加载侧边栏状态和显示设置
  useEffect(() => {
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        setSidebarMode(mode);
      } catch (error) {
        console.error('加载侧边栏状态失败:', error);
      }
    };

    const loadDisplaySettings = async () => {
      try {
        const settings = await getDisplaySettings();
        setDisplaySettings(settings);
      } catch (error) {
        console.error('加载显示设置失败:', error);
      }
    };

    loadSidebarMode();
    loadDisplaySettings();
  }, []);

  // 切换侧边栏显示模式
  const toggleSidebar = useCallback(async () => {
    try {
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');
      const nextMode = getNextSidebarMode(sidebarMode);
      saveSidebarMode(nextMode);
      setSidebarMode(nextMode);
      setTimeout(() => {
        setCompanyInfo(prev => ({ ...prev }));
      }, 50);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  // 切换最大化状态
  const toggleMaximize = useCallback(() => {
    setIsMaximized(prev => {
      const newState = !prev;
      if (newState) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }
      return newState;
    });
  }, []);

  // 监听全屏变化事件
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  // 在客户端更新时间
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };
    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 组件挂载时加载数据（初始加载，无超时限制）
  useEffect(() => {
    fetchSiteInfo(); // 获取站点信息
    refreshAllData(true); // 设置为初始加载模式
  }, [fetchSiteInfo, refreshAllData]);

  // 监听时间范围变化，强制刷新数据（无超时限制）
  useEffect(() => {
    console.log('时间范围变化，刷新数据:', { startDate, endDate });
    // 清空现有数据，设置加载状态
    setDashboardData(null);
    setLoading(true);
    // 延迟刷新，确保状态清空
    setTimeout(() => {
      refreshAllData(true); // 设置为初始加载模式，无超时限制
    }, 100);
  }, [startDate, endDate, refreshAllData]);

  // 自动刷新功能（使用无感刷新）
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      silentRefreshAllData(); // 使用无感刷新，不显示加载状态
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, silentRefreshAllData]);

  // 格式化利用率百分比
  const formatUtilizationRate = (rate: number): string => {
    return `${rate.toFixed(1)}%`;
  };

  // 将V3 API数据转换为DeviceRanking组件期望的格式
  const transformRealtimeDataForDeviceRanking = (realtimeData: RealtimeUtilizationData | null) => {
    if (!realtimeData) return null;

    // 状态颜色映射
    const statusColors = {
      production: '#22c55e', // 绿色
      idle: '#f59e0b',       // 黄色
      fault: '#ef4444',      // 红色
      shutdown: '#6b7280',   // 灰色
      adjusting: '#8b5cf6',  // 紫色
      disconnected: '#374151' // 深灰色
    };

    // 转换设备数据
    const devices = realtimeData.device_utilizations.map((device, index) => {
      // 计算各状态的百分比
      const totalTime = device.total_time || 1; // 避免除零
      const productionPercentage = (device.status_summary.production / totalTime) * 100;
      const idlePercentage = (device.status_summary.idle / totalTime) * 100;
      const faultPercentage = (device.status_summary.fault / totalTime) * 100;
      const shutdownPercentage = (device.status_summary.shutdown / totalTime) * 100;

      return {
        device_id: device.device.device_id,
        device_name: device.device.name,
        device_code: device.device.device_id,
        location: device.device.location,
        brand: device.device.brand,
        model: device.device.model,
        utilization_rate: device.utilization,
        status_composition: {
          production: {
            duration: device.status_summary.production / 3600, // 转换为小时
            percentage: productionPercentage,
            color: statusColors.production
          },
          idle: {
            duration: device.status_summary.idle / 3600,
            percentage: idlePercentage,
            color: statusColors.idle
          },
          fault: {
            duration: device.status_summary.fault / 3600,
            percentage: faultPercentage,
            color: statusColors.fault
          },
          adjusting: {
            duration: 0,
            percentage: 0,
            color: statusColors.adjusting
          },
          shutdown: {
            duration: device.status_summary.shutdown / 3600,
            percentage: shutdownPercentage,
            color: statusColors.shutdown
          },
          disconnected: {
            duration: 0,
            percentage: 0,
            color: statusColors.disconnected
          }
        },
        total_working_time: device.work_time / 3600, // 转换为小时
        productive_time: device.running_time / 3600, // 转换为小时
        idle_time: device.status_summary.idle / 3600,
        fault_time: device.status_summary.fault / 3600,
        rank: index + 1
      };
    });

    // 状态图例
    const status_legend = [
      { status: 'production', label: '生产', color: statusColors.production },
      { status: 'idle', label: '空闲', color: statusColors.idle },
      { status: 'fault', label: '故障', color: statusColors.fault },
      { status: 'shutdown', label: '停机', color: statusColors.shutdown }
    ];

    return {
      devices,
      status_legend,
      work_time_settings: realtimeData.work_time_settings,
      last_updated: realtimeData.last_updated
    };
  };

  // 处理每日利用率趋势条点击事件（新标签页打开）
  const handleDailyTrendClick = useCallback((date: string) => {
    console.log(`🔗 [Dashboard V2] 在新标签页打开设备利用率机器页面，日期: ${date}`);
    window.open(`/device_utilization_machine_v2?date=${date}`, '_blank');
  }, []);

  // 渲染设备利用率概览组件 - 使用V3实时数据
  const renderUtilizationOverview = () => {
    const realtimeData = dashboardData?.realtime;

    return (
      <Card className="bg-gray-800 border-gray-700 h-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg text-white flex items-center gap-2">
            📊 今日设备实时利用率概览
            {loading && (
              <Badge variant="secondary" className="bg-yellow-600 text-white text-xs">
                加载中...
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {/* 加载状态显示 */}
          {loading && (
            <div className="flex flex-col items-center justify-center h-32 text-gray-400">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mb-2"></div>
              <p className="text-sm">正在加载概览数据...</p>
            </div>
          )}

          {/* 数据显示 */}
          {!loading && realtimeData && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {/* 总体利用率 */}
              <div className="text-center bg-gray-700/50 rounded-lg p-3">
                <div className="text-2xl font-bold text-blue-400 mb-1">
                  {formatUtilizationRate(realtimeData.overall_utilization)}
                </div>
                <div className="text-xs text-gray-400">总体利用率</div>
                <div className="text-xs text-gray-500 mt-1">
                  {realtimeData.device_count} 台设备
                </div>
              </div>

              {/* 利用率区间统计 */}
              {realtimeData.utilization_zones.map((zone, index) => {
                // 根据区间名称显示固定的范围说明
                let rangeText = '';
                if (zone.name === '高利用率') {
                  rangeText = '>80%';
                } else if (zone.name === '中利用率') {
                  rangeText = '50%-80%';
                } else if (zone.name === '低利用率') {
                  rangeText = '<50%';
                }

                return (
                  <div key={index} className="text-center bg-gray-700/50 rounded-lg p-3">
                    <div
                      className="text-xl font-bold mb-1"
                      style={{ color: zone.color }}
                    >
                      {zone.device_count}
                    </div>
                    <div className="text-xs text-gray-400">{zone.name}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {rangeText}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* 无数据状态 */}
          {!loading && !realtimeData && (
            <div className="flex flex-col items-center justify-center h-32 text-gray-400">
              <p className="text-sm">暂无概览数据</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // 格式化时间显示（小时转换为小时分钟格式）
  const formatTimeDisplay = (hours: number): string => {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);

    if (minutes === 0) {
      return `${wholeHours}h`;
    } else {
      return `${wholeHours}h${minutes}m`;
    }
  };

  // 渲染每日利用率趋势组件 - 使用V3每日数据
  const renderDailyTrend = () => {
    const dailyData = dashboardData?.daily;

    // 从API返回数据中获取天数和日期范围
    const getDaysCountAndDateRange = () => {
      if (!dailyData || dailyData.length === 0) {
        return {
          daysCount: 0,
          dateRangeText: `${startDate} ~ ${endDate}` // 使用默认的状态变量作为fallback
        };
      }

      // 获取第一个元素的date_range
      const firstItem = dailyData[0];
      if (firstItem && firstItem.date_range && Array.isArray(firstItem.date_range)) {
        const dateRange = firstItem.date_range;
        const daysCount = dateRange.length;

        // 获取日期范围的最小值和最大值
        const minDate = Math.min(...dateRange.map((d: string) => new Date(d).getTime()));
        const maxDate = Math.max(...dateRange.map((d: string) => new Date(d).getTime()));
        const minDateStr = new Date(minDate).toISOString().split('T')[0];
        const maxDateStr = new Date(maxDate).toISOString().split('T')[0];

        return {
          daysCount,
          dateRangeText: `${minDateStr} ~ ${maxDateStr}`
        };
      }

      // 如果没有date_range字段，使用dailyData.length作为fallback
      return {
        daysCount: dailyData.length,
        dateRangeText: `${startDate} ~ ${endDate}`
      };
    };

    const { daysCount, dateRangeText } = getDaysCountAndDateRange();

    return (
      <Card className="bg-gray-800 border-gray-700 h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-white flex items-center gap-2">
              📈 每日利用率趋势
              {dailyData && daysCount > 0 && (
                <Badge variant="secondary" className="bg-blue-600 text-white text-xs">
                  {daysCount} 天
                </Badge>
              )}
              {loading && (
                <Badge variant="secondary" className="bg-yellow-600 text-white text-xs">
                  加载中...
                </Badge>
              )}
            </CardTitle>
            <div className="text-xs text-gray-400">
              {dateRangeText}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0 h-full">
          {/* 加载状态显示 */}
          {loading && (
            <div className="flex flex-col items-center justify-center h-full min-h-[200px] text-gray-400">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
              <p className="text-sm">正在加载数据...</p>
              <p className="text-xs text-gray-500 mt-1">请稍候，正在获取每日利用率趋势数据</p>
            </div>
          )}

          {/* 数据显示 */}
          {dailyData && !loading && (
            <div className="space-y-2 overflow-y-auto scrollbar-hide" style={{ maxHeight: 'calc(100vh - 300px)' }}>
              {/* 按日期倒序排列数据（最新日期在前） */}
              {[...dailyData].sort((a, b) => {
                return new Date(b.date).getTime() - new Date(a.date).getTime();
              }).map((day) => (
                <div key={day.date} className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-16 text-xs text-gray-400">
                    {new Date(day.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="text-sm font-medium text-white">
                        {day.overall_utilization.toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-400">
                        ({day.device_count} 台设备)
                      </div>
                    </div>
                    <div
                      className="w-full h-6 bg-gray-700 rounded-full overflow-hidden relative cursor-pointer hover:bg-gray-600 transition-colors duration-200"
                      onClick={() => handleDailyTrendClick(day.date)}
                      title={`点击查看 ${day.date} 的详细设备利用率信息`}
                    >
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-300 flex items-center justify-end pr-2 hover:from-blue-400 hover:to-blue-300"
                        style={{ width: `${Math.min(day.overall_utilization, 100)}%` }}
                      >
                        {day.overall_utilization > 15 && (
                          <span className="text-xs font-medium text-white">
                            {day.overall_utilization.toFixed(1)}%
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex-shrink-0 text-right">
                    <div className="text-sm font-medium text-green-400">
                      {formatTimeDisplay(day.total_running_time / 3600)} {/* 转换为小时 */}
                    </div>
                    <div className="text-xs text-gray-400">生产时间</div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 无数据状态显示 */}
          {!dailyData && !loading && (
            <div className="flex flex-col items-center justify-center h-full min-h-[200px] text-gray-400">
              <p className="text-sm">暂无数据</p>
              <p className="text-xs text-gray-500 mt-1">请检查时间范围设置或稍后重试</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // 渲染主要内容布局 - 与原版本保持一致
  const renderMainContent = () => {
    const layoutMode = displaySettings?.utilizationDashboard?.layoutMode || "split";
    const baseRankingLimit = displaySettings?.utilizationDashboard?.rankingDisplayLimit || 20;

    // 每个分区都显示完整的设备数量
    const rankingLimit = baseRankingLimit;

    if (layoutMode === "triple") {
      // 三分布局：左侧1/3，右侧2/3分为左右两部分
      return (
        <div className="flex-1 flex overflow-hidden">
          {/* 左侧：设备利用率概览和每日趋势 */}
          <div className="w-1/3 flex flex-col border-r border-gray-700">
            {/* 设备利用率概览 - 上方 */}
            <div className="p-4 border-b border-gray-700">
              {renderUtilizationOverview()}
            </div>

            {/* 每日利用率趋势 - 下方，底部对齐，可滚动 */}
            <div className="flex-1 p-4 overflow-hidden">
              <div className="h-full overflow-y-auto scrollbar-hide">
                {renderDailyTrend()}
              </div>
            </div>
          </div>

          {/* 右侧：设备排名，分为左右两部分，共用标题 */}
          <div className="w-2/3 flex flex-col">
            {/* 共用标题和状态图例 */}
            <div className="p-4 pb-2 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-white flex items-center gap-2">
                  🏆 今日设备实时利用率排名
                </h3>
                {/* 状态图例 */}
                <div className="flex items-center gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <span className="text-gray-300">生产</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <span className="text-gray-300">空闲</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <span className="text-gray-300">故障</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                    <span className="text-gray-300">停机</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 排名内容区域：左右分为两部分 */}
            <div className="flex-1 flex overflow-hidden">
              {/* 左半部分：设备排名（倒序） */}
              <div className="w-1/2 p-4 border-r border-gray-700 overflow-hidden">
                <div className="h-full overflow-y-auto">
                  <div className="bg-gray-800 border-gray-700 rounded-lg">
                    <div className="p-3 border-b border-gray-700">
                      <h4 className="text-sm font-medium text-blue-400">📈 利用率最高</h4>
                    </div>
                    <div className="p-3">
                      <DeviceRanking
                        deviceRanking={transformRealtimeDataForDeviceRanking(dashboardData?.realtime || null)}
                        isRealtime={true}
                        displayLimit={rankingLimit}
                        sortOrder="desc"
                        title=""
                        showCard={false}
                        loading={loading}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* 右半部分：设备排名（正序） */}
              <div className="w-1/2 p-4 overflow-hidden">
                <div className="h-full overflow-y-auto">
                  <div className="bg-gray-800 border-gray-700 rounded-lg">
                    <div className="p-3 border-b border-gray-700">
                      <h4 className="text-sm font-medium text-red-400">📉 利用率最低</h4>
                    </div>
                    <div className="p-3">
                      <DeviceRanking
                        deviceRanking={transformRealtimeDataForDeviceRanking(dashboardData?.realtime || null)}
                        isRealtime={true}
                        displayLimit={rankingLimit}
                        sortOrder="asc"
                        title=""
                        showCard={false}
                        loading={loading}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // 默认左右分屏布局
      return (
        <div className="flex-1 flex overflow-hidden">
          {/* 左侧：设备利用率概览和每日趋势 */}
          <div className="w-1/2 flex flex-col border-r border-gray-700">
            {/* 设备利用率概览 - 上方 */}
            <div className="p-4 border-b border-gray-700">
              {renderUtilizationOverview()}
            </div>

            {/* 每日利用率趋势 - 下方，底部对齐，可滚动 */}
            <div className="flex-1 p-4 overflow-hidden">
              <div className="h-full overflow-y-auto scrollbar-hide">
                {renderDailyTrend()}
              </div>
            </div>
          </div>

          {/* 右侧：今日各设备利用率排名 */}
          <div className="w-1/2 flex flex-col">
            <div className="flex-1 p-4 border-b border-gray-700 overflow-hidden">
              <div className="h-full overflow-y-auto">
                <DeviceRanking
                  deviceRanking={transformRealtimeDataForDeviceRanking(dashboardData?.realtime || null)}
                  isRealtime={true}
                  displayLimit={rankingLimit}
                  loading={loading}
                />
              </div>
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="flex">
      {/* 侧边导航栏 - 在非最大化状态下显示 */}
      {!isMaximized && <Sidebar mode={sidebarMode} activePage="device_utilization_dashboard" />}

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-h-screen">
        {/* 顶部标题栏 */}
        <Header
          companyInfo={companyInfo}
          isMaximized={isMaximized}
          toggleMaximize={toggleMaximize}
          toggleSidebar={toggleSidebar}
        />

        {/* 页面标题和控制栏 - 可折叠 */}
        <div className="relative">
          {/* 标题栏 - 始终显示 */}
          <div className="bg-blue-900/20 border-b border-gray-700 flex items-center justify-between py-2 px-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <PieChart className="mr-2 h-5 w-5" />
              设备利用率仪表盘
              {dashboardData?.realtime && (
                <Badge variant="secondary" className="bg-blue-600 text-white text-xs ml-2">
                  {(() => {
                    const mode = dashboardData.realtime.work_time_settings.utilization_mode || 'OP1';
                    if (mode === 'OP1') {
                      return '24小时';
                    } else if (mode === 'OP2') {
                      return '实际工作时长';
                    }
                    return mode;
                  })()}
                </Badge>
              )}
              {dashboardData?.realtime?.work_time_settings && dashboardData.realtime.work_time_settings.utilization_mode === 'OP2' && (
                <span className="text-sm text-gray-300 ml-2">
                  ({dashboardData.realtime.work_time_settings.day_start_time}起)
                </span>
              )}
            </h2>

            {/* 控制信息和折叠按钮 */}
            <div className="flex items-center gap-4">
              {/* 关键信息显示 */}
              <div className="flex items-center gap-6 text-sm text-gray-300">
                {lastUpdated && (
                  <span>
                    最后更新: {lastUpdated}
                  </span>
                )}
                {autoRefresh && (
                  <span>
                    自动刷新: {refreshInterval >= 60 ? `${refreshInterval / 60}分钟` : `${refreshInterval}秒`}
                  </span>
                )}
              </div>

              {/* 折叠按钮 */}
              <Button
                onClick={() => setIsControlsCollapsed(!isControlsCollapsed)}
                variant="outline"
                size="sm"
                className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
              >
                {isControlsCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          {/* 控制栏 - 遮罩式展开 */}
          {!isControlsCollapsed && (
            <div className="absolute top-full left-0 right-0 z-50 bg-gray-800/95 backdrop-blur-sm border-b border-gray-700 shadow-lg">
              <div className="px-6 py-4">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  {/* 左侧：时间范围选择 */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-400">时间范围:</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="date"
                        value={startDate}
                        onChange={(e) => handleStartDateChange(e.target.value)}
                        className="bg-gray-700 border border-gray-600 rounded-md px-3 py-1.5 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span className="text-gray-400">至</span>
                      <input
                        type="date"
                        value={endDate}
                        onChange={(e) => handleEndDateChange(e.target.value)}
                        className="bg-gray-700 border border-gray-600 rounded-md px-3 py-1.5 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* 右侧：控制按钮 */}
                  <div className="flex items-center gap-4">
                    {/* 自动刷新控制 */}
                    <div className="flex items-center gap-2">
                      <label className="flex items-center gap-2 text-sm text-gray-400">
                        <input
                          type="checkbox"
                          checked={autoRefresh}
                          onChange={(e) => setAutoRefresh(e.target.checked)}
                          className="rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500"
                        />
                        自动刷新
                      </label>
                      {autoRefresh && (
                        <select
                          value={refreshInterval}
                          onChange={(e) => setRefreshInterval(Number(e.target.value))}
                          className="bg-gray-700 border border-gray-600 rounded-md px-2 py-1 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value={60}>1分钟</option>
                          <option value={300}>5分钟</option>
                          <option value={600}>10分钟</option>
                          <option value={900}>15分钟</option>
                          <option value={1800}>30分钟</option>
                          <option value={3600}>60分钟</option>
                        </select>
                      )}
                    </div>

                    {/* 手动刷新按钮 */}
                    <Button
                      onClick={() => refreshAllData(false)}
                      variant="outline"
                      size="sm"
                      disabled={loading}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <RefreshCwIcon className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                      刷新
                    </Button>
                  </div>
                </div>

                {/* 状态信息 */}
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-700 text-xs text-gray-500">
                  <div>
                    {lastUpdated && `详细更新时间：${lastUpdated}`}
                  </div>
                  <div className="flex items-center gap-4">
                    {loading && (
                      <span className="flex items-center gap-1">
                        <RefreshCwIcon className="w-3 h-3 animate-spin" />
                        正在加载...
                      </span>
                    )}
                    {isSilentRefreshing && !loading && (
                      <span className="flex items-center gap-1 text-blue-400">
                        <RefreshCwIcon className="w-3 h-3 animate-spin" />
                        后台更新中...
                      </span>
                    )}
                    {error && (
                      <span className="text-red-400">
                        错误：{error}
                      </span>
                    )}
                    {autoRefresh && !loading && !isSilentRefreshing && (
                      <span className="text-green-400">
                        自动刷新已启用
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 主要内容区域 - 根据设置动态布局 */}
        {renderMainContent()}
      </main>
    </div>
  );
}
