'use client'; // 声明这是一个客户端组件

/**
 * 历史记录页面
 *
 * 该页面提供历史记录查询功能（开发中）
 */
import { useState, useEffect, useCallback } from 'react';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { History } from 'lucide-react';

export default function HistoryPage() {
  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);

  // 在客户端初始化时从localStorage加载侧边栏状态
  useEffect(() => {
    // 导入是动态的，因为这些函数使用了浏览器API
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        console.log('历史页面加载侧边栏状态:', mode);
        setSidebarMode(mode);
      } catch (error) {
        console.error('加载侧边栏状态失败:', error);
      }
    };

    // 立即执行
    loadSidebarMode();
  }, []);

  // 最大化状态
  const [isMaximized, setIsMaximized] = useState(false);

  // 公司信息状态
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '江苏恒力阿萨德有限公司',
    dateTime: ''  // 初始为空字符串，避免水合错误
  });

  /**
   * 切换侧边栏显示模式
   * 在两种模式之间切换：隐藏 <-> 折叠
   * 并保存到localStorage
   */
  const toggleSidebar = useCallback(async () => {
    try {
      // 导入工具函数
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');

      // 获取下一个模式
      const nextMode = getNextSidebarMode(sidebarMode);
      console.log('历史页面切换侧边栏状态:', sidebarMode, '->', nextMode);
      console.log('历史页面当前DOM元素:', document.body.innerHTML.substring(0, 100));

      // 保存到localStorage和全局变量
      saveSidebarMode(nextMode);

      // 更新组件状态
      setSidebarMode(nextMode);

      // 强制重新渲染
      setTimeout(() => {
        console.log('历史页面强制重新渲染，当前侧边栏模式:', nextMode);
        // 触发一个无害的状态更新，强制组件重新渲染
        setCompanyInfo(prev => ({ ...prev }));
      }, 50);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  /**
   * 切换最大化状态
   * 当点击最大化按钮时调用
   * 同时控制浏览器全屏模式
   */
  const toggleMaximize = useCallback(() => {
    // 切换最大化状态
    setIsMaximized(prev => {
      const newState = !prev;

      // 根据新状态切换全屏模式
      if (newState) {
        // 进入全屏模式
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        // 退出全屏模式
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }

      return newState;
    });
  }, []);

  // 监听全屏变化事件，同步状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      // 如果浏览器退出了全屏模式，同步更新我们的状态
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };

    // 添加全屏变化事件监听
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    // 清理函数
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  // 在客户端更新时间
  useEffect(() => {
    // 更新当前时间
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    // 立即更新一次
    updateTime();

    // 每秒更新一次
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="flex">
      {/* 侧边导航栏 - 在非最大化状态下显示 */}
      {!isMaximized && <Sidebar mode={sidebarMode} activePage="history" />}

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-h-screen">
        {/* 顶部标题栏 */}
        <Header
          companyInfo={companyInfo}
          isMaximized={isMaximized}
          toggleMaximize={toggleMaximize}
          toggleSidebar={toggleSidebar}
        />

        {/* 页面标题 */}
        <div className="bg-blue-900/20 py-2 px-6">
          <h2 className="text-xl font-bold text-white flex items-center">
            <History className="mr-2 h-5 w-5" />
            历史记录
          </h2>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-6 flex items-center justify-center">
          <div className="text-center p-8 bg-gray-800 rounded-lg shadow-lg max-w-md">
            <History className="mx-auto h-16 w-16 text-blue-500 mb-4" />
            <h3 className="text-xl font-bold mb-2">历史记录功能</h3>
            <p className="text-gray-400 mb-4">
              此功能正在开发中，敬请期待！
            </p>
            <div className="text-sm text-gray-500">
              预计功能：状态变更历史、操作日志、故障记录等
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
