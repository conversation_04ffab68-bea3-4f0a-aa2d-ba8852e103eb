'use client'

import Link from 'next/link'
import { useState } from 'react'

/**
 * 导航页面组件
 * 
 * 功能：显示所有可用的页面链接，方便用户导航
 * 包含页面分类、搜索功能和页面描述
 */
export default function NavigationPage() {
  const [searchTerm, setSearchTerm] = useState('')

  // 页面配置数据
  const pages = [
    // 主要功能页面
    {
      category: '主要功能',
      items: [
        { path: '/status', name: '设备状态监控', description: '实时监控所有设备的运行状态' },
        { path: '/device_utilization_dashboard_v2', name: '设备利用率仪表板 V2', description: '新版设备利用率仪表板' },
        { path: '/device_utilization_machine_v2', name: '设备利用率机器视图 V2', description: '新版单机设备利用率详情' },
      ]
    },
    // 数据分析页面
    {
      category: '数据分析',
      items: [
        { path: '/analytics', name: '数据分析', description: '综合数据分析和报表' },
        { path: '/data', name: '数据管理', description: '数据查看和管理' },
        { path: '/device_usage_statistics', name: '设备使用统计', description: '设备使用情况统计分析' },
      ]
    },
    // 历史记录页面
    {
      category: '历史记录',
      items: [
        { path: '/history', name: '历史记录', description: '查看历史数据和记录' },
        { path: '/device_status_history', name: '设备状态历史', description: '设备状态变化历史记录' },
        { path: '/device_production_history', name: '设备生产历史', description: '设备生产数据历史记录' },
      ]
    },
    // 生产管理页面
    {
      category: '生产管理',
      items: [
        { path: '/production_progress', name: '生产进度', description: '实时生产进度监控' },
        { path: '/production_progress_v2', name: '生产进度 V2', description: '新版生产进度监控' },
        { path: '/device_status_list', name: '设备状态列表', description: '设备状态列表视图' },
      ]
    },
    // 系统设置页面
    {
      category: '系统设置',
      items: [
        { path: '/settings', name: '系统设置', description: '系统配置和参数设置' },
        { path: '/settings/display', name: '显示设置', description: '界面显示相关设置' },
        { path: '/settings/refresh', name: '刷新设置', description: '数据刷新相关设置' },
        { path: '/settings/worktime', name: '工作时间设置', description: '工作时间配置' },
      ]
    },
    // 测试页面
    {
      category: '测试功能',
      items: [
        { path: '/test', name: '测试页面', description: '功能测试和调试' },
      ]
    }
  ]

  // 过滤页面
  const filteredPages = pages.map(category => ({
    ...category,
    items: category.items.filter(page =>
      page.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.path.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.items.length > 0)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">系统导航</h1>
          <p className="text-gray-600">选择要访问的功能页面</p>
        </div>

        {/* 搜索框 */}
        <div className="mb-8">
          <div className="max-w-md mx-auto">
            <input
              type="text"
              placeholder="搜索页面..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* 页面分类列表 */}
        <div className="space-y-8">
          {filteredPages.map((category) => (
            <div key={category.category} className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 border-b pb-2">
                {category.category}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {category.items.map((page) => (
                  <Link
                    key={page.path}
                    href={page.path}
                    className="block p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all duration-200 group"
                  >
                    <div className="flex flex-col h-full">
                      <h3 className="font-medium text-gray-900 group-hover:text-blue-600 mb-2">
                        {page.name}
                      </h3>
                      <p className="text-sm text-gray-600 flex-1">
                        {page.description}
                      </p>
                      <div className="mt-2 text-xs text-gray-400 font-mono">
                        {page.path}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 无搜索结果 */}
        {filteredPages.length === 0 && searchTerm && (
          <div className="text-center py-12">
            <p className="text-gray-500">没有找到匹配的页面</p>
            <button
              onClick={() => setSearchTerm('')}
              className="mt-2 text-blue-600 hover:text-blue-800"
            >
              清除搜索
            </button>
          </div>
        )}

        {/* 返回首页 */}
        <div className="text-center mt-8">
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回首页
          </Link>
        </div>
      </div>
    </div>
  )
}
