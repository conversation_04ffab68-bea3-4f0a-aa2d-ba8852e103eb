/**
 * 工作时间设置API路由
 *
 * 代理到 12_server_api 获取工作时间设置
 * 支持多环境部署和智能API地址解析
 */
import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

// 请求超时时间（毫秒）
const TIMEOUT_MS = 5000;

/**
 * 处理 GET 请求
 * 将请求代理到后端 API 并返回响应
 */
export async function GET(request: NextRequest) {
  try {
    // 构建后端 API URL，使用智能API配置
    const apiBaseUrl = getApiBaseUrl();
    const apiUrl = `${apiBaseUrl}/api/admin/work-time-settings`;

    console.log(`📡 代理工作时间设置请求到: ${apiUrl}`);

    // 设置超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

    // 调用后端API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      console.error(`❌ 后端API响应错误: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `后端API响应错误: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`✅ 工作时间设置获取成功:`, data);

    return NextResponse.json(data);

  } catch (error: any) {
    console.error('❌ 获取工作时间设置失败:', error);

    if (error.name === 'AbortError') {
      return NextResponse.json(
        { error: '请求超时' },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { error: '获取工作时间设置失败' },
      { status: 500 }
    );
  }
}
