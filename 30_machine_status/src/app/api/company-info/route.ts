/**
 * 公司信息API路由
 *
 * 该文件实现了公司信息的API端点，作为后端API的代理
 * 将请求转发到后端FastAPI服务
 */
import { NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

// 后端API基础URL，从环境变量获取（统一使用NEXT_PUBLIC_API_BASE_URL）
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

/**
 * GET请求处理函数
 *
 * 处理对/api/company-info端点的GET请求
 * 将请求转发到后端FastAPI服务
 *
 * @returns NextResponse包含从后端获取的公司信息数据
 */
export async function GET() {
  try {
    // 从后端API获取数据
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

    const response = await fetch(`${API_BASE_URL}/api/machines`, {
      signal: controller.signal,
      // 添加缓存控制头，防止浏览器缓存
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    }).finally(() => clearTimeout(timeoutId));

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status}`);
    }

    // 获取后端返回的数据
    const data = await response.json();

    // 提取公司信息部分
    if (data && data.companyInfo) {
      // 将数据返回给前端
      return NextResponse.json(data.companyInfo);
    } else {
      // 如果后端没有返回公司信息，则返回默认值
      return NextResponse.json({
        name: '',
        dateTime: new Date().toISOString()
      });
    }
  } catch (error: any) {
    console.error('获取公司信息出错:', error);
    // 区分超时错误和其他错误
    const errorMessage = error.name === 'AbortError'
      ? '获取公司信息超时'
      : '获取公司信息失败';

    // 返回默认公司信息
    return NextResponse.json({
      name: '',
      dateTime: new Date().toISOString(),
      error: errorMessage
    });
  }
}
