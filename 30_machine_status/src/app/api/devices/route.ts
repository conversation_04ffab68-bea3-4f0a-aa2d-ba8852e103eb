/**
 * 设备列表API路由
 *
 * 从后端API获取真实的设备列表数据
 */
import { NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../config/api';

// 后端 API 基础 URL（统一使用NEXT_PUBLIC_getApiBaseUrl()）
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

// 请求超时时间（毫秒）
const TIMEOUT_MS = 5000;

export async function GET() {
  try {
    console.log('📡 获取设备列表，代理到后端API...');

    // 设置超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

    try {
      // 调用后端 API 获取设备列表
      const response = await fetch(`${getApiBaseUrl()}/api/machines`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        signal: controller.signal,
      });

      // 清除超时
      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`❌ 后端API请求失败: ${response.status} ${response.statusText}`);
        throw new Error(`后端API请求失败: ${response.status}`);
      }

      // 解析响应数据
      const data = await response.json();

      console.log(`✅ 成功获取设备列表，共 ${data.machines?.length || 0} 个设备`);

      return NextResponse.json(data);

    } catch (fetchError: any) {
      // 清除超时
      clearTimeout(timeoutId);

      if (fetchError.name === 'AbortError') {
        console.error('❌ 请求超时');
        throw new Error('请求超时');
      }

      console.error('❌ 网络请求失败:', fetchError);
      throw fetchError;
    }

  } catch (error: any) {
    console.error('❌ 获取设备列表失败:', error);
    return NextResponse.json(
      {
        error: '获取设备列表失败',
        details: error.message || '未知错误',
        machines: [] // 返回空数组作为后备
      },
      { status: 500 }
    );
  }
}
