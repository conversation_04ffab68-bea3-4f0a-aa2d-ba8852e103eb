/**
 * 设备状态历史API路由
 *
 * 代理到 12_server_api 获取真实的设备状态历史数据
 * 支持根据工作时间设置自动计算查询时间范围，包括跨天查询
 *
 * 工作时间逻辑：
 *   - 如果设置08:00开始，date=2025-06-11表示从2025-06-11 08:00到2025-06-12 08:00
 *   - 如果设置00:00开始，date=2025-06-11表示从2025-06-11 00:00到2025-06-12 00:00
 *   - 无date参数时，根据当前时间和工作开始时间自动推算应该查询的日期
 */
import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

// 后端 API 基础 URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

// 请求超时时间（毫秒）
const TIMEOUT_MS = 30000;

/**
 * 根据当前时间和工作开始时间推算查询日期
 *
 * @param dayStartTime 工作开始时间，格式HH:MM
 * @returns 应该查询的日期，格式YYYY-MM-DD
 */
function calculateQueryDate(dayStartTime: string): string {
  const now = new Date();
  const [startHour, startMinute] = dayStartTime.split(':').map(Number);

  // 创建今天的工作开始时间
  const todayWorkStart = new Date(now);
  todayWorkStart.setHours(startHour, startMinute, 0, 0);

  // 如果当前时间在今天的工作开始时间之前，说明应该查询昨天的数据
  if (now < todayWorkStart) {
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday.toISOString().split('T')[0];
  } else {
    // 否则查询今天的数据
    return now.toISOString().split('T')[0];
  }
}

/**
 * 获取工作时间设置
 */
async function getWorkTimeSettings(): Promise<{ day_start_time: string } | null> {
  try {
    const response = await fetch(`${getApiBaseUrl()}/api/admin/work-time-settings`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.ok) {
      const data = await response.json();
      return data;
    }
  } catch (error) {
    console.warn('⚠️ 获取工作时间设置失败，使用默认值:', error);
  }

  // 返回默认设置
  return { day_start_time: '08:00' };
}

/**
 * 处理 GET 请求
 * 将请求代理到后端 API 并返回响应
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 获取设备ID
    const deviceId = params.id;

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    let date = searchParams.get('date');

    // 如果没有提供日期参数，根据工作时间设置自动推算
    if (!date) {
      console.log('📅 未提供日期参数，根据工作时间设置自动推算...');

      const workTimeSettings = await getWorkTimeSettings();
      if (workTimeSettings) {
        date = calculateQueryDate(workTimeSettings.day_start_time);
        console.log(`📅 根据工作开始时间 ${workTimeSettings.day_start_time} 推算查询日期: ${date}`);
      } else {
        date = new Date().toISOString().split('T')[0];
        console.log(`📅 使用默认日期: ${date}`);
      }
    }

    // 构建后端 API URL，添加工作时间参数
    const apiUrl = `${getApiBaseUrl()}/api/devices/${deviceId}/status-history?date=${date}&use_work_time=true`;

    console.log(`📡 代理请求到: ${apiUrl}`);

    // 设置超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

    try {
      // 调用后端 API
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      // 清除超时
      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`❌ 后端API请求失败: ${response.status} ${response.statusText}`);

        // 如果后端API失败，返回空的状态历史数据
        return NextResponse.json({
          deviceInfo: {
            id: deviceId,
            code: deviceId,
            name: `设备${deviceId}`,
            model: 'HXM-000',
            location: '未知区域'
          },
          statusHistory: [],
          date,
          total: 0,
          error: `后端API请求失败: ${response.status}`
        });
      }

      // 解析响应数据
      const data = await response.json();

      console.log(`✅ 成功获取设备 ${deviceId} 的状态历史数据，共 ${data.total || 0} 条记录`);

      return NextResponse.json(data);

    } catch (fetchError: any) {
      // 清除超时
      clearTimeout(timeoutId);

      if (fetchError.name === 'AbortError') {
        console.error('❌ 请求超时');
        return NextResponse.json({
          deviceInfo: {
            id: deviceId,
            code: deviceId,
            name: `设备${deviceId}`,
            model: 'HXM-000',
            location: '未知区域'
          },
          statusHistory: [],
          date,
          total: 0,
          error: '请求超时'
        });
      }

      console.error('❌ 网络请求失败:', fetchError);
      return NextResponse.json({
        deviceInfo: {
          id: deviceId,
          code: deviceId,
          name: `设备${deviceId}`,
          model: 'HXM-000',
          location: '未知区域'
        },
        statusHistory: [],
        date,
        total: 0,
        error: '网络请求失败'
      });
    }

  } catch (error) {
    console.error('❌ 获取设备状态历史失败:', error);
    return NextResponse.json(
      {
        error: '获取设备状态历史失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
