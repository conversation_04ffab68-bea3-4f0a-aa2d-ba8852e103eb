import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

/**
 * 设备状态详细数据API - V1版本
 *
 * 功能：
 * - 获取指定设备在指定日期的详细状态记录
 * - 计算累计时间和利用率统计
 * - 支持状态变化的完整时间线
 *
 * 注意：这是V1版本，前端需要进行部分数据处理
 * V2版本请使用 /api/devices/[id]/status-detail-v2
 */

// 状态映射
const STATUS_MAPPING = {
  'production': 'production',
  'running': 'production',
  'idle': 'idle',
  'fault': 'fault',
  'error': 'fault',
  'maintenance': 'maintenance',
  'shutdown': 'shutdown',
  'offline': 'maintenance'
};

interface StatusRecord {
  id: number;
  status: string;
  startTime: string;
  endTime: string;
  duration: number;
  productionCumulative: number;
  idleCumulative: number;
  faultCumulative: number;
  maintenanceCumulative: number;
  shutdownCumulative: number;
}

interface UtilizationStats {
  totalTime: number;
  productionTime: number;
  idleTime: number;
  faultTime: number;
  maintenanceTime: number;
  shutdownTime: number;
  utilizationRate: number;
}

/**
 * 处理 GET 请求 - 获取设备状态详细数据
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const deviceId = params.id;
    const searchParams = request.nextUrl.searchParams;
    let date = searchParams.get('date');

    // 如果没有提供日期，使用当前日期
    if (!date) {
      date = new Date().toISOString().split('T')[0];
    }

    console.log(`📊 获取设备状态详细数据: 设备=${deviceId}, 日期=${date}`);

    // 构建后端API URL
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    const apiUrl = `${backendUrl}/api/devices/${deviceId}/status-history?date=${date}&use_work_time=true`;

    console.log(`🔗 调用后端API: ${apiUrl}`);

    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

    try {
      // 调用后端API获取状态历史数据
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`❌ 后端API请求失败: ${response.status} ${response.statusText}`);
        throw new Error(`后端API请求失败: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ 成功获取设备状态历史数据，共 ${data.statusHistory?.length || 0} 条记录`);

      // 处理状态历史数据
      const statusHistory = data.statusHistory || [];

      // 转换为详细记录格式
      const statusRecords = processStatusHistory(statusHistory, date);

      // 获取工作时间配置
      const workTimeConfig = await getWorkTimeConfig();

      // 计算利用率统计
      const utilizationStats = await calculateUtilizationStats(statusRecords, date, workTimeConfig || undefined);

      return NextResponse.json({
        success: true,
        deviceId,
        date,
        statusRecords,
        utilizationStats,
        total: statusRecords.length
      });

    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('❌ 请求超时');
        throw new Error('请求超时，请稍后重试');
      }

      throw fetchError;
    }

  } catch (error) {
    console.error('❌ 获取设备状态详细数据失败:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取设备状态详细数据失败',
      deviceId: params.id,
      date: request.nextUrl.searchParams.get('date'),
      statusRecords: [],
      utilizationStats: null,
      total: 0
    }, { status: 500 });
  }
}

/**
 * 处理状态历史数据，转换为详细记录格式
 */
function processStatusHistory(statusHistory: any[], date: string): StatusRecord[] {
  const records: StatusRecord[] = [];

  // 累计时间计数器
  let productionCumulative = 0;
  let idleCumulative = 0;
  let faultCumulative = 0;
  let maintenanceCumulative = 0;
  let shutdownCumulative = 0;

  statusHistory.forEach((item, index) => {
    // 🔍 调试：打印完整的记录结构
    if (index === 0) {
      console.log(`🔍 后端返回的数据结构示例:`, JSON.stringify(item, null, 2));
      console.log(`🔍 可用字段:`, Object.keys(item));
    }

    // 🔧 修复：查找状态字段，适配后端API返回的数据格式
    const statusField = item.currentStatus || item.status || item.state || item.device_status || 'unknown';
    const normalizedStatus = STATUS_MAPPING[statusField as keyof typeof STATUS_MAPPING] || statusField;

    // 计算持续时间（秒）
    const duration = item.duration || 0;

    // 根据状态更新累计时间
    switch (normalizedStatus) {
      case 'production':
        productionCumulative += duration;
        break;
      case 'idle':
        idleCumulative += duration;
        break;
      case 'fault':
        faultCumulative += duration;
        break;
      case 'maintenance':
        maintenanceCumulative += duration;
        break;
      case 'shutdown':
        shutdownCumulative += duration;
        break;
    }

    // 🔧 修复：安全处理时间数据，适配后端API返回的数据格式
    // 后端API可能返回的字段名：startTime, timestamp, start_time, time
    const startTimeStr = item.startTime || item.timestamp || item.start_time || item.time;

    console.log(`🔍 处理记录 ${index + 1}: status=${statusField}->${normalizedStatus}, startTime=${item.startTime}, timestamp=${item.timestamp}, start_time=${item.start_time}, time=${item.time}, duration=${duration}`);

    // 验证开始时间是否有效
    if (!startTimeStr) {
      console.warn(`⚠️ 记录 ${index + 1} 缺少时间字段，跳过处理`);
      return; // 跳过这条无效记录
    }

    // 🔧 修复：处理只有时间部分的字符串（如"00:00:00"）
    let startTime: Date;
    if (startTimeStr.includes('T') || startTimeStr.includes(' ')) {
      // 完整的日期时间字符串
      startTime = new Date(startTimeStr);
    } else {
      // 只有时间部分，需要与日期组合
      // 使用查询的日期作为基准日期
      const baseDate = new Date(date + 'T' + startTimeStr + 'Z'); // 假设是UTC时间
      startTime = baseDate;
    }

    if (isNaN(startTime.getTime())) {
      console.warn(`⚠️ 无效的开始时间: ${startTimeStr}, 跳过记录 ${index + 1}`);
      return; // 跳过这条无效记录
    }

    // 计算结束时间
    let endTimeStr: string;
    const endTimeField = item.endTime || item.end_time;
    if (endTimeField) {
      const endTime = new Date(endTimeField);
      if (isNaN(endTime.getTime())) {
        // 如果endTime无效，使用startTime + duration计算
        endTimeStr = new Date(startTime.getTime() + duration * 1000).toISOString();
      } else {
        endTimeStr = endTime.toISOString();
      }
    } else {
      // 没有endTime，使用startTime + duration计算
      endTimeStr = new Date(startTime.getTime() + duration * 1000).toISOString();
    }

    // 创建记录
    const record: StatusRecord = {
      id: index + 1,
      status: normalizedStatus,
      startTime: startTime.toISOString(),
      endTime: endTimeStr,
      duration,
      productionCumulative,
      idleCumulative,
      faultCumulative,
      maintenanceCumulative,
      shutdownCumulative
    };

    records.push(record);
  });

  // 按开始时间排序
  records.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());

  return records;
}

/**
 * 工作时间配置接口
 */
interface WorkTimeConfig {
  utilization_mode: string;
  day_start_time: string;
  rest_periods: Array<{
    name: string;
    start_time: string;
    end_time: string;
    enabled: boolean;
  }>;
}

/**
 * 获取工作时间配置
 */
async function getWorkTimeConfig(): Promise<WorkTimeConfig | null> {
  try {
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    const apiUrl = `${backendUrl}/api/statistics/worktime`;

    console.log(`📋 [Work Time Config] 获取工作时间配置: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ [Work Time Config] 成功获取工作时间配置:`, {
        utilization_mode: data.utilization_mode,
        day_start_time: data.day_start_time,
        rest_periods_count: data.rest_periods?.length || 0
      });

      return {
        utilization_mode: data.utilization_mode || 'OP1',
        day_start_time: data.day_start_time || '08:00',
        rest_periods: data.rest_periods || []
      };
    } else {
      console.warn(`⚠️ [Work Time Config] 获取工作时间配置失败: ${response.status}`);
      return null;
    }
  } catch (error) {
    console.error('❌ [Work Time Config] 获取工作时间配置异常:', error);
    return null;
  }
}

/**
 * 计算利用率统计
 */
async function calculateUtilizationStats(
  records: StatusRecord[],
  date: string,
  workTimeConfig?: WorkTimeConfig
): Promise<UtilizationStats> {
  let productionTime = 0;
  let idleTime = 0;
  let faultTime = 0;
  let maintenanceTime = 0;
  let shutdownTime = 0;

  // 计算各状态时间（与统计服务保持一致的状态映射）
  records.forEach(record => {
    switch (record.status) {
      case 'production':
      case 'running':  // 🔧 修复：添加running状态，与统计服务保持一致
        productionTime += record.duration;
        break;
      case 'idle':
        idleTime += record.duration;
        break;
      case 'fault':
      case 'error':  // 🔧 修复：添加error状态，与统计服务保持一致
        faultTime += record.duration;
        break;
      case 'maintenance':
        maintenanceTime += record.duration;
        break;
      case 'shutdown':
        shutdownTime += record.duration;
        break;
    }
  });

  // 计算总工作时间
  const totalTime = await calculateTotalWorkTime(date, workTimeConfig);

  // 计算利用率
  const utilizationRate = totalTime > 0 ? (productionTime / totalTime) * 100 : 0;

  return {
    totalTime,
    productionTime,
    idleTime,
    faultTime,
    maintenanceTime,
    shutdownTime,
    utilizationRate
  };
}

/**
 * 计算总工作时间
 */
async function calculateTotalWorkTime(date: string, workTimeConfig?: WorkTimeConfig): Promise<number> {
  // 如果没有工作时间配置，使用默认值
  if (!workTimeConfig) {
    console.warn('⚠️ [Total Work Time] 没有工作时间配置，使用默认24小时');
    return 24 * 60 * 60; // 24小时的秒数
  }

  const { utilization_mode, day_start_time, rest_periods } = workTimeConfig;

  // OP1模式：固定24小时
  if (utilization_mode === 'OP1') {
    console.log('📊 [Total Work Time] OP1模式，总工作时间: 23:59:59');
    return 24 * 60 * 60; // 23:59:59的秒数 24 * 60 * 60 - 1;
  }

  // OP2模式：需要考虑休息时间
  const today = new Date().toISOString().split('T')[0];
  const isToday = date === today;

  if (isToday) {
    // 当天：动态计算
    return calculateCurrentDayWorkTime(day_start_time, rest_periods);
  } else {
    // 非当天：23:59:59 - 休息时间的和
    return calculateNonCurrentDayWorkTime(rest_periods);
  }
}

/**
 * 计算当天的工作时间
 */
function calculateCurrentDayWorkTime(
  dayStartTime: string,
  restPeriods: Array<{ name: string; start_time: string; end_time: string; enabled: boolean }>
): number {
  const now = new Date();
  const currentTime = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds();

  // 解析每天开始时间
  const [startHour, startMinute] = dayStartTime.split(':').map(Number);
  const dayStartSeconds = startHour * 3600 + startMinute * 60;

  // 基础工作时间：当前时间 - 每天开始时间
  let baseWorkTime = currentTime - dayStartSeconds;
  if (baseWorkTime < 0) {
    baseWorkTime += 24 * 3600; // 跨天处理
  }

  // 计算需要扣除的休息时间
  let restTimeToDeduct = 0;

  for (const restPeriod of restPeriods) {
    if (!restPeriod.enabled) continue;

    const [restStartHour, restStartMinute] = restPeriod.start_time.split(':').map(Number);
    const [restEndHour, restEndMinute] = restPeriod.end_time.split(':').map(Number);

    const restStartSeconds = restStartHour * 3600 + restStartMinute * 60;
    const restEndSeconds = restEndHour * 3600 + restEndMinute * 60;

    if (currentTime >= restStartSeconds && currentTime < restEndSeconds) {
      // 当前时间在休息时间内但未超过
      restTimeToDeduct += currentTime - restStartSeconds;
      console.log(`📊 [Current Day] 当前在${restPeriod.name}时间内，扣除时间: ${currentTime - restStartSeconds}秒`);
    } else if (currentTime >= restEndSeconds) {
      // 当前时间已超过休息时间
      restTimeToDeduct += restEndSeconds - restStartSeconds;
      console.log(`📊 [Current Day] 已超过${restPeriod.name}时间，扣除时间: ${restEndSeconds - restStartSeconds}秒`);
    }
    // 如果当前时间还未到休息时间，不扣除
  }

  const totalWorkTime = baseWorkTime - restTimeToDeduct;
  console.log(`📊 [Current Day] 当天工作时间计算: ${baseWorkTime}秒 - ${restTimeToDeduct}秒 = ${totalWorkTime}秒`);

  return Math.max(0, totalWorkTime);
}

/**
 * 计算非当天的工作时间
 */
function calculateNonCurrentDayWorkTime(
  restPeriods: Array<{ name: string; start_time: string; end_time: string; enabled: boolean }>
): number {
  const fullDaySeconds = 24 * 60 * 60; // 23:59:59 24 * 60 * 60 - 1;

  // 计算所有启用的休息时间总和
  let totalRestTime = 0;

  for (const restPeriod of restPeriods) {
    if (!restPeriod.enabled) continue;

    const [startHour, startMinute] = restPeriod.start_time.split(':').map(Number);
    const [endHour, endMinute] = restPeriod.end_time.split(':').map(Number);

    const startSeconds = startHour * 3600 + startMinute * 60;
    const endSeconds = endHour * 3600 + endMinute * 60;

    const restDuration = endSeconds - startSeconds;
    totalRestTime += restDuration;

    console.log(`📊 [Non-Current Day] ${restPeriod.name}: ${restDuration}秒`);
  }

  const totalWorkTime = fullDaySeconds - totalRestTime;
  console.log(`📊 [Non-Current Day] 非当天工作时间: ${fullDaySeconds}秒 - ${totalRestTime}秒 = ${totalWorkTime}秒`);

  return Math.max(0, totalWorkTime);
}
