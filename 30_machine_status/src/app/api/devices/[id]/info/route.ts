import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../../config/api';

/**
 * 设备信息API
 * 
 * 功能：
 * - 获取指定设备的基本信息
 * - 包含设备名称、型号、位置等
 * - 获取工作时间配置信息
 */

interface DeviceInfo {
  id: string;
  name: string;
  model: string;
  location: string;
  utilizationMode: string;
  dayStartTime: string;
}

/**
 * 处理 GET 请求 - 获取设备基本信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const deviceId = params.id;

    console.log(`📋 获取设备信息: 设备=${deviceId}`);

    // 构建后端API URL
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    try {
      // 并行获取设备信息和工作时间配置
      const [deviceResponse, workTimeResponse] = await Promise.all([
        // 获取设备基本信息
        fetch(`${backendUrl}/api/machines/${deviceId}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: controller.signal,
        }).catch(() => null), // 如果失败则返回null

        // 获取工作时间配置
        fetch(`${backendUrl}/api/statistics/worktime`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: controller.signal,
        }).catch(() => null) // 如果失败则返回null
      ]);

      clearTimeout(timeoutId);

      // 处理设备信息
      let deviceData = null;
      if (deviceResponse && deviceResponse.ok) {
        deviceData = await deviceResponse.json();
        console.log(`✅ 成功获取设备基本信息: ${deviceData.name || deviceId}`);
      } else {
        console.log(`⚠️ 无法获取设备基本信息，使用默认信息`);
      }

      // 处理工作时间配置
      let workTimeData = null;
      if (workTimeResponse && workTimeResponse.ok) {
        workTimeData = await workTimeResponse.json();
        console.log(`✅ 成功获取工作时间配置: ${workTimeData.day_start_time || '08:00'}`);
      } else {
        console.log(`⚠️ 无法获取工作时间配置，使用默认配置`);
      }

      // 构建设备信息响应
      const deviceInfo: DeviceInfo = {
        id: deviceId,
        name: deviceData?.name || deviceData?.device_name || `设备 ${deviceId}`,
        model: deviceData?.model || deviceData?.device_model || 'Unknown',
        location: deviceData?.location || deviceData?.workshop || '未知位置',
        utilizationMode: workTimeData?.utilization_mode || 'OP1',
        dayStartTime: workTimeData?.day_start_time || '08:00'
      };

      return NextResponse.json(deviceInfo);

    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('❌ 请求超时');
        throw new Error('请求超时，请稍后重试');
      }

      throw fetchError;
    }

  } catch (error) {
    console.error('❌ 获取设备信息失败:', error);

    // 返回默认设备信息
    const defaultDeviceInfo: DeviceInfo = {
      id: params.id,
      name: `设备 ${params.id}`,
      model: 'Unknown',
      location: '未知位置',
      utilizationMode: 'OP1',
      dayStartTime: '08:00'
    };

    return NextResponse.json(defaultDeviceInfo);
  }
}
