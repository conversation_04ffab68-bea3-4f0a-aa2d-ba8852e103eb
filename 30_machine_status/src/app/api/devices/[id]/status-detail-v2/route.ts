import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

/**
 * 设备状态详细数据API - V2版本
 *
 * 功能：
 * - 获取指定设备在指定日期的详细状态记录
 * - 在后端完成所有数据处理和计算
 * - 返回前端直接可用的渲染数据
 * - 包括格式化的时间、持续时间、累计统计等
 *
 * V2版本特点：
 * - 前端无需进行任何数据处理或计算
 * - 所有格式化和统计在后端完成
 * - 返回完全可渲染的数据结构
 */

// 状态映射 - 与V1版本保持一致
const STATUS_MAPPING = {
  'production': 'production',
  'running': 'production',
  'idle': 'idle',
  'fault': 'fault',
  'error': 'fault',
  'maintenance': 'maintenance',
  'shutdown': 'shutdown',
  'offline': 'maintenance'
};

// 状态中文名称映射
const STATUS_NAMES = {
  production: '生产',
  idle: '空闲',
  fault: '故障',
  shutdown: '关机',
  maintenance: '维护',
  offline: '离线'
};

// 状态颜色映射
const STATUS_COLORS = {
  production: '#22c55e', // 绿色 - 生产
  idle: '#f59e0b',       // 橙色 - 空闲
  fault: '#ef4444',      // 红色 - 故障
  shutdown: '#6b7280',   // 灰色 - 关机
  maintenance: '#8b5cf6', // 紫色 - 维护
  offline: '#6b7280'     // 灰色 - 离线
};

/**
 * V2版本的状态记录接口 - 包含所有格式化数据
 */
interface StatusRecordV2 {
  id: number;
  status: string;
  statusName: string;           // 中文状态名称
  statusColor: string;          // 状态颜色
  startTime: string;            // ISO格式原始时间
  endTime: string;              // ISO格式原始时间
  startTimeFormatted: string;   // 格式化的开始时间
  endTimeFormatted: string;     // 格式化的结束时间
  duration: number;             // 持续时间（秒）
  durationFormatted: string;    // 格式化的持续时间
  productionCumulative: number;
  productionCumulativeFormatted: string;
  idleCumulative: number;
  idleCumulativeFormatted: string;
  faultCumulative: number;
  faultCumulativeFormatted: string;
  maintenanceCumulative: number;
  maintenanceCumulativeFormatted: string;
  shutdownCumulative: number;
  shutdownCumulativeFormatted: string;
}

/**
 * V2版本的利用率统计接口 - 包含所有格式化数据
 */
interface UtilizationStatsV2 {
  totalTime: number;
  totalTimeFormatted: string;
  productionTime: number;
  productionTimeFormatted: string;
  productionPercentage: number;
  productionPercentageFormatted: string;
  idleTime: number;
  idleTimeFormatted: string;
  idlePercentage: number;
  idlePercentageFormatted: string;
  faultTime: number;
  faultTimeFormatted: string;
  faultPercentage: number;
  faultPercentageFormatted: string;
  maintenanceTime: number;
  maintenanceTimeFormatted: string;
  maintenancePercentage: number;
  maintenancePercentageFormatted: string;
  shutdownTime: number;
  shutdownTimeFormatted: string;
  shutdownPercentage: number;
  shutdownPercentageFormatted: string;
  utilizationRate: number;
  utilizationRateFormatted: string;
  // 状态分布数据（用于渲染进度条）
  statusDistribution: Array<{
    status: string;
    statusName: string;
    color: string;
    percentage: number;
    percentageFormatted: string;
    time: number;
    timeFormatted: string;
  }>;
}

/**
 * V2版本的设备信息接口 - 包含所有格式化数据
 */
interface DeviceInfoV2 {
  id: string;
  name: string;
  model: string;
  location: string;
  dayStartTime: string;
  dayStartTimeFormatted: string;
}

/**
 * V2版本的工作时间配置接口 - 包含所有格式化数据
 */
interface WorkTimeConfigV2 {
  utilizationMode: string;
  utilizationModeFormatted: string;
  dayStartTime: string;
  dayStartTimeFormatted: string;
  restPeriods: Array<{
    name: string;
    startTime: string;
    endTime: string;
    enabled: boolean;
    duration: number;
    durationFormatted: string;
    timeRange: string; // 如："12:00-13:00 (1小时)"
  }>;
  restPeriodsEnabled: Array<{
    name: string;
    timeRange: string;
  }>;
}

/**
 * V2版本的完整响应接口
 */
interface StatusDetailResponseV2 {
  success: boolean;
  deviceId: string;
  date: string;
  dateFormatted: string;
  isToday: boolean;
  deviceInfo: DeviceInfoV2;
  workTimeConfig: WorkTimeConfigV2;
  statusRecords: StatusRecordV2[];
  utilizationStats: UtilizationStatsV2;
  summary: {
    totalRecords: number;
    totalRecordsFormatted: string;
    dateRange: string;
    calculationMode: string;
    calculationModeDescription: string;
  };
  error?: string;
}

/**
 * 处理 GET 请求 - 获取设备状态详细数据 V2版本
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const deviceId = params.id;
    const searchParams = request.nextUrl.searchParams;
    let date = searchParams.get('date');

    // 如果没有提供日期，使用当前日期
    if (!date) {
      date = new Date().toISOString().split('T')[0];
    }

    const isToday = date === new Date().toISOString().split('T')[0];

    console.log(`📊 [V2] 获取设备状态详细数据: 设备=${deviceId}, 日期=${date}, 是否当天=${isToday}`);

    // 构建后端API URL
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    const apiUrl = `${backendUrl}/api/devices/${deviceId}/status-history?date=${date}&use_work_time=true`;

    console.log(`🔗 [V2] 调用后端API: ${apiUrl}`);

    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

    try {
      // 调用后端API获取状态历史数据
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`❌ [V2] 后端API请求失败: ${response.status} ${response.statusText}`);
        throw new Error(`后端API请求失败: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ [V2] 成功获取设备状态历史数据，共 ${data.statusHistory?.length || 0} 条记录`);

      // 获取工作时间配置
      const workTimeConfig = await getWorkTimeConfig();

      // 获取设备信息
      const deviceInfo = await getDeviceInfo(deviceId);

      // 处理状态历史数据 - V2版本包含完整格式化
      const statusRecords = await processStatusHistoryV2(data.statusHistory || [], date);

      // 计算利用率统计 - V2版本包含完整格式化
      const utilizationStats = await calculateUtilizationStatsV2(statusRecords, date, workTimeConfig);

      // 构建V2版本的完整响应
      const responseData: StatusDetailResponseV2 = {
        success: true,
        deviceId,
        date,
        dateFormatted: formatDate(date),
        isToday,
        deviceInfo: formatDeviceInfoV2(deviceInfo, deviceId),
        workTimeConfig: formatWorkTimeConfigV2(workTimeConfig),
        statusRecords,
        utilizationStats,
        summary: {
          totalRecords: statusRecords.length,
          totalRecordsFormatted: `${statusRecords.length} 条记录`,
          dateRange: isToday ? '当天实时数据' : '历史数据',
          calculationMode: workTimeConfig?.utilization_mode || 'OP1',
          calculationModeDescription: (workTimeConfig?.utilization_mode === 'OP2')
            ? '实际工作时长模式（扣除休息时间）'
            : '24小时固定模式'
        }
      };

      console.log(`✅ [V2] 数据处理完成，返回 ${statusRecords.length} 条格式化记录`);
      return NextResponse.json(responseData);

    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('❌ [V2] 请求超时');
        throw new Error('请求超时，请稍后重试');
      }

      throw fetchError;
    }

  } catch (error) {
    console.error('❌ [V2] 获取设备状态详细数据失败:', error);

    const errorResponse: StatusDetailResponseV2 = {
      success: false,
      deviceId: params.id,
      date: request.nextUrl.searchParams.get('date') || new Date().toISOString().split('T')[0],
      dateFormatted: formatDate(request.nextUrl.searchParams.get('date') || new Date().toISOString().split('T')[0]),
      isToday: false,
      deviceInfo: formatDeviceInfoV2(null, params.id),
      workTimeConfig: formatWorkTimeConfigV2(null),
      statusRecords: [],
      utilizationStats: getEmptyUtilizationStatsV2(),
      summary: {
        totalRecords: 0,
        totalRecordsFormatted: '0 条记录',
        dateRange: '无数据',
        calculationMode: 'OP1',
        calculationModeDescription: '24小时固定模式'
      },
      error: error instanceof Error ? error.message : '获取设备状态详细数据失败'
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * 格式化时长（秒转换为 HH:MM:SS 格式）
 */
function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 格式化日期时间（ISO字符串转换为本地化格式）
 */
function formatDateTime(dateTimeStr: string): string {
  return new Date(dateTimeStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 格式化日期（YYYY-MM-DD 转换为本地化格式）
 */
function formatDate(dateStr: string): string {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

/**
 * 格式化百分比
 */
function formatPercentage(value: number): string {
  return `${value.toFixed(1)}%`;
}

/**
 * V2版本：处理状态历史数据，返回完全格式化的记录
 */
async function processStatusHistoryV2(statusHistory: any[], date: string): Promise<StatusRecordV2[]> {
  const records: StatusRecordV2[] = [];

  // 累计时间计数器
  let productionCumulative = 0;
  let idleCumulative = 0;
  let faultCumulative = 0;
  let maintenanceCumulative = 0;
  let shutdownCumulative = 0;

  statusHistory.forEach((item, index) => {
    // 调试：打印完整的记录结构
    if (index === 0) {
      console.log(`🔍 [V2] 后端返回的数据结构示例:`, JSON.stringify(item, null, 2));
      console.log(`🔍 [V2] 可用字段:`, Object.keys(item));
    }

    // 查找状态字段，适配后端API返回的数据格式
    const statusField = item.currentStatus || item.status || item.state || item.device_status || 'unknown';
    const normalizedStatus = STATUS_MAPPING[statusField as keyof typeof STATUS_MAPPING] || statusField;

    // 计算持续时间（秒）
    const duration = item.duration || 0;

    // 根据状态更新累计时间
    switch (normalizedStatus) {
      case 'production':
        productionCumulative += duration;
        break;
      case 'idle':
        idleCumulative += duration;
        break;
      case 'fault':
        faultCumulative += duration;
        break;
      case 'maintenance':
        maintenanceCumulative += duration;
        break;
      case 'shutdown':
        shutdownCumulative += duration;
        break;
    }

    // 安全处理时间数据，适配后端API返回的数据格式
    const startTimeStr = item.startTime || item.timestamp || item.start_time || item.time;

    console.log(`🔍 [V2] 处理记录 ${index + 1}: status=${statusField}->${normalizedStatus}, startTime=${item.startTime}, duration=${duration}`);

    // 验证开始时间是否有效
    if (!startTimeStr) {
      console.warn(`⚠️ [V2] 记录 ${index + 1} 缺少时间字段，跳过处理`);
      return; // 跳过这条无效记录
    }

    // 处理只有时间部分的字符串（如"00:00:00"）
    let startTime: Date;
    if (startTimeStr.includes('T') || startTimeStr.includes(' ')) {
      // 完整的日期时间字符串
      startTime = new Date(startTimeStr);
    } else {
      // 只有时间部分，需要与日期组合
      const baseDate = new Date(date + 'T' + startTimeStr + 'Z'); // 假设是UTC时间
      startTime = baseDate;
    }

    if (isNaN(startTime.getTime())) {
      console.warn(`⚠️ [V2] 无效的开始时间: ${startTimeStr}, 跳过记录 ${index + 1}`);
      return; // 跳过这条无效记录
    }

    // 计算结束时间
    let endTimeStr: string;
    const endTimeField = item.endTime || item.end_time;
    if (endTimeField) {
      const endTime = new Date(endTimeField);
      if (isNaN(endTime.getTime())) {
        // 如果endTime无效，使用startTime + duration计算
        endTimeStr = new Date(startTime.getTime() + duration * 1000).toISOString();
      } else {
        endTimeStr = endTime.toISOString();
      }
    } else {
      // 没有endTime，使用startTime + duration计算
      endTimeStr = new Date(startTime.getTime() + duration * 1000).toISOString();
    }

    // 创建V2版本的完全格式化记录
    const record: StatusRecordV2 = {
      id: index + 1,
      status: normalizedStatus,
      statusName: STATUS_NAMES[normalizedStatus as keyof typeof STATUS_NAMES] || normalizedStatus,
      statusColor: STATUS_COLORS[normalizedStatus as keyof typeof STATUS_COLORS] || '#6b7280',
      startTime: startTime.toISOString(),
      endTime: endTimeStr,
      startTimeFormatted: formatDateTime(startTime.toISOString()),
      endTimeFormatted: formatDateTime(endTimeStr),
      duration,
      durationFormatted: formatDuration(duration),
      productionCumulative,
      productionCumulativeFormatted: formatDuration(productionCumulative),
      idleCumulative,
      idleCumulativeFormatted: formatDuration(idleCumulative),
      faultCumulative,
      faultCumulativeFormatted: formatDuration(faultCumulative),
      maintenanceCumulative,
      maintenanceCumulativeFormatted: formatDuration(maintenanceCumulative),
      shutdownCumulative,
      shutdownCumulativeFormatted: formatDuration(shutdownCumulative)
    };

    records.push(record);
  });

  // 按开始时间排序
  records.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());

  console.log(`✅ [V2] 处理完成，共 ${records.length} 条格式化记录`);
  return records;
}

/**
 * V2版本：计算利用率统计，返回完全格式化的数据
 */
async function calculateUtilizationStatsV2(
  records: StatusRecordV2[],
  date: string,
  workTimeConfig?: any
): Promise<UtilizationStatsV2> {
  let productionTime = 0;
  let idleTime = 0;
  let faultTime = 0;
  let maintenanceTime = 0;
  let shutdownTime = 0;

  // 计算各状态时间
  records.forEach(record => {
    switch (record.status) {
      case 'production':
      case 'running':
        productionTime += record.duration;
        break;
      case 'idle':
        idleTime += record.duration;
        break;
      case 'fault':
      case 'error':
        faultTime += record.duration;
        break;
      case 'maintenance':
        maintenanceTime += record.duration;
        break;
      case 'shutdown':
        shutdownTime += record.duration;
        break;
    }
  });

  // 计算总工作时间
  const totalTime = await calculateTotalWorkTimeV2(date, workTimeConfig);

  // 计算利用率和百分比
  const utilizationRate = totalTime > 0 ? (productionTime / totalTime) * 100 : 0;
  const productionPercentage = totalTime > 0 ? (productionTime / totalTime) * 100 : 0;
  const idlePercentage = totalTime > 0 ? (idleTime / totalTime) * 100 : 0;
  const faultPercentage = totalTime > 0 ? (faultTime / totalTime) * 100 : 0;
  const maintenancePercentage = totalTime > 0 ? (maintenanceTime / totalTime) * 100 : 0;
  const shutdownPercentage = totalTime > 0 ? (shutdownTime / totalTime) * 100 : 0;

  // 构建状态分布数据（用于渲染进度条）
  const statusDistribution = [];

  if (productionTime > 0) {
    statusDistribution.push({
      status: 'production',
      statusName: '生产',
      color: STATUS_COLORS.production,
      percentage: productionPercentage,
      percentageFormatted: formatPercentage(productionPercentage),
      time: productionTime,
      timeFormatted: formatDuration(productionTime)
    });
  }

  if (idleTime > 0) {
    statusDistribution.push({
      status: 'idle',
      statusName: '空闲',
      color: STATUS_COLORS.idle,
      percentage: idlePercentage,
      percentageFormatted: formatPercentage(idlePercentage),
      time: idleTime,
      timeFormatted: formatDuration(idleTime)
    });
  }

  if (faultTime > 0) {
    statusDistribution.push({
      status: 'fault',
      statusName: '故障',
      color: STATUS_COLORS.fault,
      percentage: faultPercentage,
      percentageFormatted: formatPercentage(faultPercentage),
      time: faultTime,
      timeFormatted: formatDuration(faultTime)
    });
  }

  if (maintenanceTime > 0) {
    statusDistribution.push({
      status: 'maintenance',
      statusName: '维护',
      color: STATUS_COLORS.maintenance,
      percentage: maintenancePercentage,
      percentageFormatted: formatPercentage(maintenancePercentage),
      time: maintenanceTime,
      timeFormatted: formatDuration(maintenanceTime)
    });
  }

  if (shutdownTime > 0) {
    statusDistribution.push({
      status: 'shutdown',
      statusName: '关机',
      color: STATUS_COLORS.shutdown,
      percentage: shutdownPercentage,
      percentageFormatted: formatPercentage(shutdownPercentage),
      time: shutdownTime,
      timeFormatted: formatDuration(shutdownTime)
    });
  }

  return {
    totalTime,
    totalTimeFormatted: formatDuration(totalTime),
    productionTime,
    productionTimeFormatted: formatDuration(productionTime),
    productionPercentage,
    productionPercentageFormatted: formatPercentage(productionPercentage),
    idleTime,
    idleTimeFormatted: formatDuration(idleTime),
    idlePercentage,
    idlePercentageFormatted: formatPercentage(idlePercentage),
    faultTime,
    faultTimeFormatted: formatDuration(faultTime),
    faultPercentage,
    faultPercentageFormatted: formatPercentage(faultPercentage),
    maintenanceTime,
    maintenanceTimeFormatted: formatDuration(maintenanceTime),
    maintenancePercentage,
    maintenancePercentageFormatted: formatPercentage(maintenancePercentage),
    shutdownTime,
    shutdownTimeFormatted: formatDuration(shutdownTime),
    shutdownPercentage,
    shutdownPercentageFormatted: formatPercentage(shutdownPercentage),
    utilizationRate,
    utilizationRateFormatted: formatPercentage(utilizationRate),
    statusDistribution
  };
}

/**
 * V2版本：计算总工作时间
 */
async function calculateTotalWorkTimeV2(date: string, workTimeConfig?: any): Promise<number> {
  // 如果没有工作时间配置，使用默认值
  if (!workTimeConfig) {
    console.warn('⚠️ [V2 Total Work Time] 没有工作时间配置，使用默认24小时');
    return 24 * 60 * 60; // 24小时的秒数
  }

  const { utilization_mode, day_start_time, rest_periods } = workTimeConfig;

  // OP1模式：固定24小时
  if (utilization_mode === 'OP1') {
    console.log('📊 [V2 Total Work Time] OP1模式，总工作时间: 24小时');
    return 24 * 60 * 60;
  }

  // OP2模式：需要考虑休息时间
  const today = new Date().toISOString().split('T')[0];
  const isToday = date === today;

  if (isToday) {
    // 当天：动态计算
    return calculateCurrentDayWorkTimeV2(day_start_time, rest_periods);
  } else {
    // 非当天：24小时 - 休息时间的和
    return calculateNonCurrentDayWorkTimeV2(rest_periods);
  }
}

/**
 * V2版本：计算当天的工作时间
 */
function calculateCurrentDayWorkTimeV2(
  dayStartTime: string,
  restPeriods: Array<{ name: string; start_time: string; end_time: string; enabled: boolean }>
): number {
  const now = new Date();
  const currentTime = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds();

  // 解析每天开始时间
  const [startHour, startMinute] = dayStartTime.split(':').map(Number);
  const dayStartSeconds = startHour * 3600 + startMinute * 60;

  // 基础工作时间：当前时间 - 每天开始时间
  let baseWorkTime = currentTime - dayStartSeconds;
  if (baseWorkTime < 0) {
    baseWorkTime += 24 * 3600; // 跨天处理
  }

  // 计算需要扣除的休息时间
  let restTimeToDeduct = 0;

  for (const restPeriod of restPeriods) {
    if (!restPeriod.enabled) continue;

    const [restStartHour, restStartMinute] = restPeriod.start_time.split(':').map(Number);
    const [restEndHour, restEndMinute] = restPeriod.end_time.split(':').map(Number);

    const restStartSeconds = restStartHour * 3600 + restStartMinute * 60;
    const restEndSeconds = restEndHour * 3600 + restEndMinute * 60;

    if (currentTime >= restStartSeconds && currentTime < restEndSeconds) {
      // 当前时间在休息时间内但未超过
      restTimeToDeduct += currentTime - restStartSeconds;
      console.log(`📊 [V2 Current Day] 当前在${restPeriod.name}时间内，扣除时间: ${currentTime - restStartSeconds}秒`);
    } else if (currentTime >= restEndSeconds) {
      // 当前时间已超过休息时间
      restTimeToDeduct += restEndSeconds - restStartSeconds;
      console.log(`📊 [V2 Current Day] 已超过${restPeriod.name}时间，扣除时间: ${restEndSeconds - restStartSeconds}秒`);
    }
    // 如果当前时间还未到休息时间，不扣除
  }

  const totalWorkTime = baseWorkTime - restTimeToDeduct;
  console.log(`📊 [V2 Current Day] 当天工作时间计算: ${baseWorkTime}秒 - ${restTimeToDeduct}秒 = ${totalWorkTime}秒`);

  return Math.max(0, totalWorkTime);
}

/**
 * V2版本：计算非当天的工作时间
 */
function calculateNonCurrentDayWorkTimeV2(
  restPeriods: Array<{ name: string; start_time: string; end_time: string; enabled: boolean }>
): number {
  const fullDaySeconds = 24 * 60 * 60;

  // 计算所有启用的休息时间总和
  let totalRestTime = 0;

  for (const restPeriod of restPeriods) {
    if (!restPeriod.enabled) continue;

    const [startHour, startMinute] = restPeriod.start_time.split(':').map(Number);
    const [endHour, endMinute] = restPeriod.end_time.split(':').map(Number);

    const startSeconds = startHour * 3600 + startMinute * 60;
    const endSeconds = endHour * 3600 + endMinute * 60;

    const restDuration = endSeconds - startSeconds;
    totalRestTime += restDuration;

    console.log(`📊 [V2 Non-Current Day] ${restPeriod.name}: ${restDuration}秒`);
  }

  const totalWorkTime = fullDaySeconds - totalRestTime;
  console.log(`📊 [V2 Non-Current Day] 非当天工作时间: ${fullDaySeconds}秒 - ${totalRestTime}秒 = ${totalWorkTime}秒`);

  return Math.max(0, totalWorkTime);
}

/**
 * 获取工作时间配置
 */
async function getWorkTimeConfig(): Promise<any> {
  try {
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    const apiUrl = `${backendUrl}/api/statistics/worktime`;

    console.log(`📋 [V2 Work Time Config] 获取工作时间配置: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ [V2 Work Time Config] 成功获取工作时间配置:`, {
        utilization_mode: data.utilization_mode,
        day_start_time: data.day_start_time,
        rest_periods_count: data.rest_periods?.length || 0
      });

      return {
        utilization_mode: data.utilization_mode || 'OP1',
        day_start_time: data.day_start_time || '08:00',
        rest_periods: data.rest_periods || []
      };
    } else {
      console.warn(`⚠️ [V2 Work Time Config] 获取工作时间配置失败: ${response.status}`);
      return null;
    }
  } catch (error) {
    console.error('❌ [V2 Work Time Config] 获取工作时间配置异常:', error);
    return null;
  }
}

/**
 * 获取设备信息
 */
async function getDeviceInfo(deviceId: string): Promise<any> {
  try {
    // 这里可以调用设备信息API，暂时返回null让格式化函数处理
    console.log(`📡 [V2 Device Info] 获取设备信息: ${deviceId}`);
    return null;
  } catch (error) {
    console.error('❌ [V2 Device Info] 获取设备信息失败:', error);
    return null;
  }
}

/**
 * V2版本：格式化设备信息
 */
function formatDeviceInfoV2(deviceInfo: any, deviceId: string): DeviceInfoV2 {
  const defaultInfo = {
    id: deviceId,
    name: `设备 ${deviceId}`,
    model: 'Unknown',
    location: '未知位置',
    dayStartTime: '08:00',
    dayStartTimeFormatted: '08:00'
  };

  if (!deviceInfo) {
    return defaultInfo;
  }

  return {
    id: deviceInfo.device_id || deviceInfo.id || deviceId,
    name: deviceInfo.name || `设备 ${deviceId}`,
    model: deviceInfo.model || deviceInfo.brand || 'Unknown',
    location: deviceInfo.location || '未知位置',
    dayStartTime: deviceInfo.dayStartTime || '08:00',
    dayStartTimeFormatted: deviceInfo.dayStartTime || '08:00'
  };
}

/**
 * V2版本：格式化工作时间配置
 */
function formatWorkTimeConfigV2(workTimeConfig: any): WorkTimeConfigV2 {
  const defaultConfig: WorkTimeConfigV2 = {
    utilizationMode: 'OP1',
    utilizationModeFormatted: '24小时固定模式',
    dayStartTime: '08:00',
    dayStartTimeFormatted: '08:00',
    restPeriods: [],
    restPeriodsEnabled: []
  };

  if (!workTimeConfig) {
    return defaultConfig;
  }

  const restPeriods = (workTimeConfig.rest_periods || []).map((period: any) => {
    const startTime = period.start_time || '12:00';
    const endTime = period.end_time || '13:00';

    // 计算休息时间长度
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    const startSeconds = startHour * 3600 + startMinute * 60;
    const endSeconds = endHour * 3600 + endMinute * 60;
    const duration = endSeconds - startSeconds;

    const durationHours = Math.floor(duration / 3600);
    const durationMinutes = Math.floor((duration % 3600) / 60);

    let durationText = '';
    if (durationHours > 0) {
      durationText = durationMinutes > 0 ? `${durationHours}小时${durationMinutes}分钟` : `${durationHours}小时`;
    } else {
      durationText = `${durationMinutes}分钟`;
    }

    return {
      name: period.name || '休息时间',
      startTime,
      endTime,
      enabled: period.enabled || false,
      duration,
      durationFormatted: formatDuration(duration),
      timeRange: `${startTime}-${endTime} (${durationText})`
    };
  });

  const restPeriodsEnabled = restPeriods
    .filter((period: any) => period.enabled)
    .map((period: any) => ({
      name: period.name,
      timeRange: period.timeRange
    }));

  return {
    utilizationMode: workTimeConfig.utilization_mode || 'OP1',
    utilizationModeFormatted: (workTimeConfig.utilization_mode === 'OP2')
      ? '实际工作时长模式'
      : '24小时固定模式',
    dayStartTime: workTimeConfig.day_start_time || '08:00',
    dayStartTimeFormatted: workTimeConfig.day_start_time || '08:00',
    restPeriods,
    restPeriodsEnabled
  };
}

/**
 * 获取空的利用率统计数据
 */
function getEmptyUtilizationStatsV2(): UtilizationStatsV2 {
  return {
    totalTime: 0,
    totalTimeFormatted: '0:00:00',
    productionTime: 0,
    productionTimeFormatted: '0:00:00',
    productionPercentage: 0,
    productionPercentageFormatted: '0.0%',
    idleTime: 0,
    idleTimeFormatted: '0:00:00',
    idlePercentage: 0,
    idlePercentageFormatted: '0.0%',
    faultTime: 0,
    faultTimeFormatted: '0:00:00',
    faultPercentage: 0,
    faultPercentageFormatted: '0.0%',
    maintenanceTime: 0,
    maintenanceTimeFormatted: '0:00:00',
    maintenancePercentage: 0,
    maintenancePercentageFormatted: '0.0%',
    shutdownTime: 0,
    shutdownTimeFormatted: '0:00:00',
    shutdownPercentage: 0,
    shutdownPercentageFormatted: '0.0%',
    utilizationRate: 0,
    utilizationRateFormatted: '0.0%',
    statusDistribution: []
  };
}
