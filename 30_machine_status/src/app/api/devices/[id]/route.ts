/**
 * 设备详情API路由
 *
 * 从后端API获取特定设备的详细信息
 */
import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 后端 API 基础 URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

// 请求超时时间（毫秒）
const TIMEOUT_MS = 5000;

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // 获取设备ID
  const deviceId = params.id;

  try {

    console.log(`📡 获取设备详情: ${deviceId}，代理到后端API...`);

    // 设置超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

    try {
      // 调用后端 API 获取设备详情
      const response = await fetch(`${getApiBaseUrl()}/api/machines/${deviceId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        signal: controller.signal,
      });

      // 清除超时
      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`❌ 后端API请求失败: ${response.status} ${response.statusText}`);
        throw new Error(`后端API请求失败: ${response.status}`);
      }

      // 解析响应数据
      const data = await response.json();

      console.log(`✅ 成功获取设备详情: ${deviceId}`);

      return NextResponse.json(data);

    } catch (fetchError: any) {
      // 清除超时
      clearTimeout(timeoutId);

      if (fetchError.name === 'AbortError') {
        console.error('❌ 请求超时');
        throw new Error('请求超时');
      }

      console.error('❌ 网络请求失败:', fetchError);
      throw fetchError;
    }

  } catch (error: any) {
    console.error(`❌ 获取设备详情失败: ${deviceId}`, error);
    return NextResponse.json(
      {
        error: '获取设备详情失败',
        details: error.message || '未知错误'
      },
      { status: 500 }
    );
  }
}
