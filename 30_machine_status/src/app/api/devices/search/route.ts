import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();
const TIMEOUT_MS = 10000; // 10秒超时

/**
 * 设备搜索API
 *
 * 使用与设备状态历史页面相同的API接口 (/api/machines)
 * 支持根据name, brand, model, location字段进行搜索
 */
export async function GET(request: NextRequest) {
  console.log('🔍 设备搜索API调用');

  try {
    // 获取搜索参数
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';

    console.log(`🔍 搜索关键词: "${search}"`);

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

    try {
      // 调用后端搜索API
      const response = await fetch(`${getApiBaseUrl()}/api/machines`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        signal: controller.signal,
      });

      // 清除超时
      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`❌ 后端API请求失败: ${response.status} ${response.statusText}`);
        throw new Error(`后端API请求失败: ${response.status}`);
      }

      // 解析响应数据
      const data = await response.json();

      if (!data.machines || !Array.isArray(data.machines)) {
        console.error('❌ 后端API返回数据格式错误');
        throw new Error('后端API返回数据格式错误');
      }

      // 在前端进行搜索过滤
      let filteredMachines = data.machines;

      if (search.trim() !== '') {
        const searchLower = search.toLowerCase();
        filteredMachines = data.machines.filter((machine: any) => {
          return (
            // 搜索设备名称
            (machine.name && machine.name.toLowerCase().includes(searchLower)) ||
            // 搜索品牌
            (machine.brand && machine.brand.toLowerCase().includes(searchLower)) ||
            // 搜索型号
            (machine.model && machine.model.toLowerCase().includes(searchLower)) ||
            // 搜索位置
            (machine.location && machine.location.toLowerCase().includes(searchLower))
          );
        });
      }

      console.log(`✅ 搜索完成，找到 ${filteredMachines.length} 个匹配的设备`);

      // 返回过滤后的结果
      return NextResponse.json({
        machines: filteredMachines,
        total: filteredMachines.length,
        search: search
      });

    } catch (fetchError: any) {
      // 清除超时
      clearTimeout(timeoutId);

      if (fetchError.name === 'AbortError') {
        console.error('❌ 请求超时');
        throw new Error('请求超时');
      }

      console.error('❌ 网络请求失败:', fetchError);
      throw fetchError;
    }

  } catch (error: any) {
    console.error('❌ 设备搜索失败:', error);
    return NextResponse.json(
      {
        error: '设备搜索失败',
        details: error.message || '未知错误',
        machines: [],
        total: 0
      },
      { status: 500 }
    );
  }
}
