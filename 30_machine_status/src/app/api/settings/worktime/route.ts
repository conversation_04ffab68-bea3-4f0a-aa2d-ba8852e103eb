import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

/**
 * 工作时间设置API
 *
 * 功能：
 * - 获取工作时间配置信息
 * - 包含班次设置、休息时间、利用率计算模式等
 * - 用于设备状态详细页面显示休息时间信息
 * - 支持多环境部署和智能API地址解析
 */

interface RestPeriod {
  name: string;
  start_time: string;
  end_time: string;
  enabled: boolean;
}

interface ShiftSetting {
  name: string;
  start_time: string;
  end_time: string;
  enabled: boolean;
}

interface WorkTimeSettings {
  id?: string;
  day_start_time: string;
  shifts: ShiftSetting[];
  rest_periods: RestPeriod[];
  utilization_mode: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * 处理 GET 请求 - 获取工作时间设置
 */
export async function GET(request: NextRequest) {
  try {
    console.log(`📋 [Settings API] 获取工作时间配置`);

    // 构建后端API URL，使用智能API配置
    const backendUrl = getBackendApiUrl();
    const apiUrl = `${backendUrl}/api/statistics/worktime`;

    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    try {
      // 调用后端API获取工作时间设置
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ [Settings API] 成功获取工作时间配置:`, {
          utilization_mode: data.utilization_mode,
          day_start_time: data.day_start_time,
          rest_periods_count: data.rest_periods?.length || 0,
          shifts_count: data.shifts?.length || 0
        });

        // 确保数据格式正确
        const workTimeSettings: WorkTimeSettings = {
          id: data.id,
          day_start_time: data.day_start_time || '08:00',
          shifts: data.shifts || [],
          rest_periods: data.rest_periods || [],
          utilization_mode: data.utilization_mode || 'OP1',
          created_at: data.created_at,
          updated_at: data.updated_at
        };

        return NextResponse.json(workTimeSettings);
      } else {
        console.warn(`⚠️ [Settings API] 后端API返回错误状态: ${response.status}`);
        throw new Error(`后端API返回错误: ${response.status}`);
      }

    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('❌ [Settings API] 请求超时');
        throw new Error('请求超时，请稍后重试');
      }

      throw fetchError;
    }

  } catch (error) {
    console.error('❌ [Settings API] 获取工作时间配置失败:', error);

    // 返回错误响应，不提供默认配置
    return NextResponse.json(
      {
        error: '获取工作时间配置失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
