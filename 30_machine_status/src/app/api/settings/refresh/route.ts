/**
 * 刷新设置API路由
 *
 * 该文件实现了刷新设置的API端点，作为后端API的代理
 * 将请求转发到后端FastAPI服务
 * 支持多环境部署和智能API地址解析
 */
import { NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

/**
 * GET请求处理函数
 *
 * 处理对/api/settings/refresh端点的GET请求
 * 将请求转发到后端FastAPI服务
 *
 * @returns NextResponse包含从后端获取的刷新设置数据
 */
export async function GET() {
  try {
    // 从后端API获取数据，使用智能API配置
    const apiBaseUrl = getApiBaseUrl();
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

    const response = await fetch(`${apiBaseUrl}/api/settings/refresh`, {
      signal: controller.signal
    }).finally(() => clearTimeout(timeoutId));

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status}`);
    }

    // 获取后端返回的数据
    const data = await response.json();

    // 将数据返回给前端
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('获取刷新设置出错:', error);
    // 区分超时错误和其他错误
    const errorMessage = error.name === 'AbortError'
      ? '获取刷新设置超时'
      : '获取刷新设置失败';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * POST请求处理函数
 *
 * 处理对/api/settings/refresh端点的POST请求
 * 将请求转发到后端FastAPI服务
 *
 * @returns NextResponse包含从后端获取的更新后的刷新设置数据
 */
export async function POST(request: Request) {
  try {
    // 获取请求体
    const body = await request.json();

    // 转发到后端API，使用智能API配置
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/settings/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status}`);
    }

    // 获取后端返回的数据
    const data = await response.json();

    // 将数据返回给前端
    return NextResponse.json(data);
  } catch (error) {
    console.error('更新刷新设置出错:', error);
    return NextResponse.json(
      { error: '更新刷新设置失败' },
      { status: 500 }
    );
  }
}
