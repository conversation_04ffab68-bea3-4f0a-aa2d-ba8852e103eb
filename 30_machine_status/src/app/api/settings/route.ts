/**
 * 系统设置API路由
 *
 * 该文件实现了系统设置的API端点，作为后端API的代理
 * 将请求转发到后端FastAPI服务
 * 支持多环境部署和智能API地址解析
 */
import { NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../config/api';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

/**
 * GET请求处理函数
 *
 * 处理对/api/settings端点的GET请求
 * 将请求转发到后端FastAPI服务
 *
 * @returns NextResponse包含从后端获取的系统设置数据
 */
export async function GET() {
  try {
    // 从后端API获取数据，使用智能API配置
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/settings`);

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status}`);
    }

    // 获取后端返回的数据
    const data = await response.json();

    // 将数据返回给前端
    return NextResponse.json(data);
  } catch (error) {
    console.error('获取系统设置出错:', error);
    return NextResponse.json(
      { error: '获取系统设置失败' },
      { status: 500 }
    );
  }
}
