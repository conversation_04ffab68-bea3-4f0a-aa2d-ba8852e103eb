/**
 * 生产进度 API 路由
 *
 * 提供生产进度数据
 */
import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';


// 生成随机生产计划
function generateProductionPlans(date: string) {
  const plans = [];
  const dayStart = new Date(`${date}T08:00:00`);

  // 决定这台设备有多少个计划 (1-3个)
  const numPlans = Math.floor(Math.random() * 3) + 1;

  // 将一天的时间分成几个不重叠的时间段
  // 一天从8:00到次日7:00，共23小时
  const totalHours = 23;
  const hoursPerPlan = Math.floor(totalHours / numPlans);

  for (let i = 0; i < numPlans; i++) {
    // 计算每个计划的开始时间，确保它们按顺序排列且不重叠
    // 每个计划在其时间段内随机开始，但确保有足够时间完成
    const segmentStart = 8 + (i * hoursPerPlan); // 该时间段的开始小时
    const segmentEnd = 8 + ((i + 1) * hoursPerPlan); // 该时间段的结束小时

    // 在时间段内随机选择开始时间，但确保至少有2小时的持续时间
    const maxStartHour = segmentEnd - 2;
    const startHour = segmentStart + Math.floor(Math.random() * (maxStartHour - segmentStart + 1));

    // 计算可能的最大持续时间
    const maxDuration = segmentEnd - startHour;
    // 持续时间在2小时到最大可能持续时间之间
    const duration = Math.max(2, Math.min(Math.floor(Math.random() * 4) + 2, maxDuration));

    const startTime = new Date(dayStart);
    startTime.setHours(startHour);

    const endTime = new Date(startTime);
    endTime.setHours(startTime.getHours() + duration);

    // 生成随机的计划数量和完成数量
    const plannedQuantity = Math.floor(Math.random() * 1000) + 500;
    const completedQuantity = Math.floor(Math.random() * plannedQuantity);

    // 生成随机的物料编码和名称
    const materialCode = `M${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    const materialName = `零件-${String.fromCharCode(65 + i)}`;

    // 生成随机的工单号
    const workOrder = `WO-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    plans.push({
      id: `P${Date.now()}${i}`,
      materialCode,
      materialName,
      workOrder,
      plannedStartTime: startTime.toISOString(),
      plannedEndTime: endTime.toISOString(),
      plannedQuantity,
      completedQuantity,
      completionPercentage: Math.round((completedQuantity / plannedQuantity) * 100),
      operator: `操作员-${Math.floor(Math.random() * 10) + 1}`,
      productIndex: i
    });
  }

  // 按开始时间排序，确保计划按时间顺序排列
  plans.sort((a, b) => new Date(a.plannedStartTime).getTime() - new Date(b.plannedStartTime).getTime());

  return plans;
}

// 生成设备生产进度数据
async function generateProgressData(date: string, deviceIds: string[] = []) {
  console.log('生成进度数据 - 设备IDs:', deviceIds);

  try {
    // 从后端API获取设备列表
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';
    const response = await fetch(`${backendUrl}/api/machines`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      console.error('获取设备列表失败，使用默认数据');
      return [];
    }

    const data = await response.json();
    const devices = data.machines || [];

    console.log('可用设备列表:', devices.map((d: any) => d.id));

    const filteredDevices = deviceIds.length > 0
      ? devices.filter((device: any) => deviceIds.includes(device.id))
      : devices;

    console.log('筛选后的设备:', filteredDevices.map((d: any) => d.id));

    return filteredDevices.map((device: any) => ({
      deviceId: device.id,
      deviceName: device.name || device.device_name || `设备 ${device.id}`,
      location: device.location || '未知位置',
      plans: generateProductionPlans(date)
    }));
  } catch (error) {
    console.error('获取设备数据失败:', error);
    return [];
  }
}

/**
 * 处理 GET 请求
 *
 * @param request 请求对象
 * @returns 响应对象
 */
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];
    const devicesParam = searchParams.get('devices');

    // 解析设备ID列表
    const deviceIds = devicesParam ? devicesParam.split(',') : [];

    console.log('API收到请求 - 日期:', date, '设备IDs:', deviceIds);

    // 生成生产进度数据
    const progressData = await generateProgressData(date, deviceIds);

    console.log('API生成的数据 - 设备数量:', progressData.length);

    // 返回结果
    return NextResponse.json({ progressData });
  } catch (error) {
    console.error('生成生产进度数据失败:', error);
    return NextResponse.json(
      { error: '生成生产进度数据失败' },
      { status: 500 }
    );
  }
}
