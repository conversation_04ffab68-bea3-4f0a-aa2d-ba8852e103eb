/**
 * 每日利用率趋势API代理路由
 * 
 * 功能：将前端请求代理到后端12_server_api的每日利用率趋势接口
 * 路径：/api/utilization/daily-trend
 * 方法：GET
 * 
 * 查询参数：
 * - start_date: 开始日期 (YYYY-MM-DD)，可选
 * - end_date: 结束日期 (YYYY-MM-DD)，可选
 * - days: 天数（从今天往前推），可选，优先级高于start_date/end_date
 * 
 * 返回数据：
 * - start_date: 开始日期
 * - end_date: 结束日期
 * - daily_data: 每日数据
 * - average_rate: 平均利用率
 * - max_rate: 最高利用率
 * - min_rate: 最低利用率
 * - trend_direction: 趋势方向 up/down/stable
 * - work_time_settings: 工作时间设置
 * - last_updated: 最后更新时间
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const days = searchParams.get('days'); // 不设置默认值，只有前端明确传递时才使用

    // 构建查询字符串
    const queryParams = new URLSearchParams();
    if (startDate) queryParams.append('start_date', startDate);
    if (endDate) queryParams.append('end_date', endDate);
    if (days) queryParams.append('days', days); // 只有当前端明确传递days参数时才添加

    // 构建后端API URL
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();
    const backendUrl = `${getApiBaseUrl()}/api/utilization/daily-trend?${queryParams.toString()}`;

    // 添加调试日志
    console.log('🔍 每日趋势API代理请求:', {
      startDate,
      endDate,
      days,
      backendUrl
    });

    // 发送请求到后端API
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
    });

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status} ${response.statusText}`);
    }

    // 获取响应数据
    const data = await response.json();

    // 添加响应调试日志
    console.log('📊 每日趋势API代理响应:', {
      start_date: data.start_date,
      end_date: data.end_date,
      daily_data_count: data.daily_data?.length || 0,
      first_date: data.daily_data?.[0]?.date,
      last_date: data.daily_data?.[data.daily_data?.length - 1]?.date
    });

    // 返回数据给前端
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Last-Modified': new Date().toUTCString(),
        'ETag': `"${Date.now()}"`,
      },
    });

  } catch (error) {
    console.error('❌ 每日利用率趋势API代理错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: '获取每日利用率趋势失败',
        error: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}
