/**
 * 设备班次利用率排名API代理路由
 * 
 * 功能：将前端请求代理到后端12_server_api的班次利用率排名接口
 * 路径：/api/utilization/shifts
 * 方法：GET
 * 
 * 查询参数：
 * - date: 日期 (YYYY-MM-DD)，可选，默认为当前日期
 * 
 * 返回数据：
 * - devices: 设备班次利用率列表
 * - shifts: 班次信息
 * - status_legend: 状态图例
 * - work_time_settings: 工作时间设置
 * - last_updated: 最后更新时间
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];

    // 构建后端API URL
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();
    const backendUrl = `${getApiBaseUrl()}/api/utilization/shifts?date=${date}`;

    // 发送请求到后端API
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
    });

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status} ${response.statusText}`);
    }

    // 获取响应数据
    const data = await response.json();

    // 返回数据给前端
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('❌ 班次利用率排名API代理错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: '获取班次利用率排名失败',
        error: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}
