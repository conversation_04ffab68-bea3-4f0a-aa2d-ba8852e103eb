/**
 * 流式每日利用率趋势API
 * 
 * 功能：支持流式返回每日利用率数据，实现无感渲染
 * 路径：/api/utilization/stream-daily-trend
 * 方法：GET
 * 
 * 特性：
 * 1. 从最新日期开始计算
 * 2. 逐日返回数据，支持前端无感渲染
 * 3. 使用Server-Sent Events (SSE)
 * 4. 复刻device_utilization_machine的准确算法
 * 
 * 查询参数：
 * - start_date: 开始日期 (YYYY-MM-DD)
 * - end_date: 结束日期 (YYYY-MM-DD)
 * - days: 天数（从今天往前推），可选
 */

import { NextRequest } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 数据类型定义
interface DailyUtilizationData {
  date: string;
  utilization_rate: number;
  total_devices: number;
  active_devices: number;
  working_hours: number;
  productive_hours: number;
}

interface DeviceUtilizationStats {
  utilizationRate: number;
  productionTime: number;
  totalTime: number;
}

export async function GET(request: NextRequest) {
  console.log('🚀 [Stream Daily Trend] 开始流式处理请求');

  // 获取查询参数
  const { searchParams } = new URL(request.url);
  const startDateParam = searchParams.get('start_date');
  const endDateParam = searchParams.get('end_date');
  const daysParam = searchParams.get('days');

  // 计算日期范围
  let startDate: string;
  let endDate: string;

  if (daysParam) {
    // 使用days参数
    const days = parseInt(daysParam, 10);
    const today = new Date();
    endDate = today.toISOString().split('T')[0];
    const start = new Date(today);
    start.setDate(start.getDate() - days + 1);
    startDate = start.toISOString().split('T')[0];
  } else {
    // 使用start_date和end_date参数
    endDate = endDateParam || new Date().toISOString().split('T')[0];
    const defaultStart = new Date();
    defaultStart.setDate(defaultStart.getDate() - 6);
    startDate = startDateParam || defaultStart.toISOString().split('T')[0];
  }

  console.log(`📅 [Stream Daily Trend] 查询日期范围: ${startDate} 至 ${endDate}`);

  // 生成日期列表（从大到小，最新日期在前）
  const dates = generateDateRange(startDate, endDate).reverse();
  console.log(`📋 [Stream Daily Trend] 生成日期列表: ${dates.length} 天`);

  // 创建流式响应
  const encoder = new TextEncoder();

  const stream = new ReadableStream({
    async start(controller) {
      try {
        // 获取所有设备列表，添加重试机制
        const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:9005';
        let devices: any[] = [];
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            console.log(`🔄 [Stream Daily Trend] 获取设备列表 (尝试 ${retryCount + 1}/${maxRetries})`);
            const devicesResponse = await fetch(`${API_HOST}/api/public/devices/configs`, {
              headers: {
                'Cache-Control': 'no-cache',
              },
            });

            if (!devicesResponse.ok) {
              throw new Error(`设备列表API响应错误: ${devicesResponse.status}`);
            }

            const devicesData = await devicesResponse.json();
            devices = devicesData.data || devicesData.devices || [];

            if (devices.length > 0) {
              console.log(`🏭 [Stream Daily Trend] 获取到 ${devices.length} 台设备`);
              break;
            } else {
              throw new Error('设备列表为空');
            }
          } catch (error) {
            retryCount++;
            console.warn(`⚠️ [Stream Daily Trend] 获取设备列表失败 (尝试 ${retryCount}/${maxRetries}):`, error);

            if (retryCount >= maxRetries) {
              throw new Error(`获取设备列表失败，已重试 ${maxRetries} 次`);
            }

            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          }
        }

        // 发送初始化信息
        const initData = {
          type: 'init',
          start_date: startDate,
          end_date: endDate,
          total_dates: dates.length,
          total_devices: devices.length
        };
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(initData)}\n\n`));

        // 逐日计算并发送数据，添加智能失败处理
        const validDailyData: DailyUtilizationData[] = [];
        let consecutiveFailures = 0;
        const maxConsecutiveFailures = 3; // 连续失败3次后停止

        for (let i = 0; i < dates.length; i++) {
          const date = dates[i];
          try {
            console.log(`📊 [Stream Daily Trend] 计算第 ${i + 1}/${dates.length} 天: ${date}`);

            const dailyData = await calculateDailyUtilization(date, devices);
            if (dailyData) {
              validDailyData.push(dailyData);
              consecutiveFailures = 0; // 重置连续失败计数

              // 发送单日数据
              const dayData = {
                type: 'daily_data',
                data: dailyData,
                progress: {
                  current: i + 1,
                  total: dates.length,
                  percentage: Math.round(((i + 1) / dates.length) * 100),
                  successful: validDailyData.length,
                  failed: i + 1 - validDailyData.length
                }
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(dayData)}\n\n`));

              console.log(`✅ [Stream Daily Trend] 发送 ${date} 数据: 利用率 ${dailyData.utilization_rate.toFixed(1)}%`);
            } else {
              consecutiveFailures++;
              console.warn(`⚠️ [Stream Daily Trend] ${date} 数据计算失败，跳过 (连续失败: ${consecutiveFailures})`);

              // 如果连续失败次数过多，考虑提前终止
              if (consecutiveFailures >= maxConsecutiveFailures && i > 2) {
                console.warn(`🛑 [Stream Daily Trend] 连续失败 ${consecutiveFailures} 次，提前终止处理`);
                break;
              }
            }
          } catch (error) {
            consecutiveFailures++;
            console.warn(`⚠️ [Stream Daily Trend] ${date} 计算异常 (连续失败: ${consecutiveFailures}):`, error);

            // 如果连续失败次数过多，考虑提前终止
            if (consecutiveFailures >= maxConsecutiveFailures && i > 2) {
              console.warn(`🛑 [Stream Daily Trend] 连续异常 ${consecutiveFailures} 次，提前终止处理`);
              break;
            }
          }
        }

        // 计算统计指标并发送完成信息
        const utilizationRates = validDailyData.map(d => d.utilization_rate);
        const averageRate = utilizationRates.length > 0 ? utilizationRates.reduce((a, b) => a + b, 0) / utilizationRates.length : 0;
        const maxRate = utilizationRates.length > 0 ? Math.max(...utilizationRates) : 0;
        const minRate = utilizationRates.length > 0 ? Math.min(...utilizationRates) : 0;

        // 计算趋势方向
        let trendDirection = 'stable';
        if (validDailyData.length >= 2) {
          const firstRate = validDailyData[validDailyData.length - 1].utilization_rate;
          const lastRate = validDailyData[0].utilization_rate;
          const diff = lastRate - firstRate;
          if (diff > 2) {
            trendDirection = 'up';
          } else if (diff < -2) {
            trendDirection = 'down';
          }
        }

        const completeData = {
          type: 'complete',
          summary: {
            start_date: startDate,
            end_date: endDate,
            daily_data: validDailyData,
            average_rate: averageRate,
            max_rate: maxRate,
            min_rate: minRate,
            trend_direction: trendDirection,
            last_updated: new Date().toISOString()
          }
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(completeData)}\n\n`));
        console.log(`🎉 [Stream Daily Trend] 流式传输完成: ${validDailyData.length} 天数据`);

      } catch (error) {
        console.error('❌ [Stream Daily Trend] 流式处理错误:', error);
        const errorData = {
          type: 'error',
          message: error instanceof Error ? error.message : '未知错误'
        };
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
      } finally {
        controller.close();
      }
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

/**
 * 计算单日利用率（复刻device_utilization_machine的算法）
 * 添加超时控制和错误处理优化
 */
async function calculateDailyUtilization(date: string, devices: any[]): Promise<DailyUtilizationData | null> {
  try {
    console.log(`📊 [${date}] 开始计算利用率，设备数量: ${devices.length}`);

    // 创建超时控制器
    const timeoutMs = 25000; // 25秒超时，比设备API的30秒稍短

    // 并行获取所有设备的状态详细数据，添加超时控制
    const devicePromises = devices.map(async (device: any) => {
      const deviceId = device.id || device.device_id;
      try {
        // 创建AbortController用于超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/devices/${deviceId}/status-detail?date=${date}`, {
          headers: {
            'Cache-Control': 'no-cache',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          console.warn(`⚠️ [${date}] 设备 ${deviceId} API响应错误: ${response.status}`);
          return null;
        }

        const data = await response.json();
        return {
          deviceId,
          stats: data.utilizationStats as DeviceUtilizationStats
        };
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          console.warn(`⏰ [${date}] 设备 ${deviceId} 请求超时 (${timeoutMs}ms)`);
        } else {
          console.warn(`⚠️ [${date}] 设备 ${deviceId} 请求失败:`, error);
        }
        return null;
      }
    });

    // 等待所有设备请求完成，设置总体超时
    const deviceResults = await Promise.race([
      Promise.all(devicePromises),
      new Promise<null[]>((_, reject) =>
        setTimeout(() => reject(new Error('总体请求超时')), timeoutMs + 5000)
      )
    ]);

    const validDevices = deviceResults.filter((result): result is NonNullable<typeof result> => result !== null && !!result?.stats);

    console.log(`📊 [${date}] 有效设备数量: ${validDevices.length}/${devices.length}`);

    if (validDevices.length === 0) {
      console.warn(`⚠️ [${date}] 没有有效的设备数据`);
      return null;
    }

    // 计算总体利用率（使用加权平均算法，与device_utilization_machine一致）
    const totalWorkingTime = validDevices.reduce((sum, device) => {
      return sum + (device!.stats.totalTime / 3600); // 转换为小时
    }, 0);

    const totalProductiveTime = validDevices.reduce((sum, device) => {
      return sum + (device!.stats.productionTime / 3600); // 转换为小时
    }, 0);

    const overallUtilization = totalWorkingTime > 0 ? (totalProductiveTime / totalWorkingTime) * 100 : 0;

    const result = {
      date,
      utilization_rate: overallUtilization,
      total_devices: devices.length,
      active_devices: validDevices.length,
      working_hours: totalWorkingTime,
      productive_hours: totalProductiveTime
    };

    console.log(`✅ [${date}] 计算完成: 利用率 ${overallUtilization.toFixed(1)}%, 有效设备 ${validDevices.length}/${devices.length}`);

    return result;
  } catch (error) {
    console.error(`❌ [${date}] 计算利用率失败:`, error);
    return null;
  }
}

/**
 * 生成日期范围
 */
function generateDateRange(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const current = new Date(start);
  while (current <= end) {
    dates.push(current.toISOString().split('T')[0]);
    current.setDate(current.getDate() + 1);
  }

  return dates;
}
