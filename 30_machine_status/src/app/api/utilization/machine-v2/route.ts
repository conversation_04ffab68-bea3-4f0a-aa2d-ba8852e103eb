/**
 * 机器页面V2 API代理路由
 * 
 * 功能：将前端请求代理到后端12_server_api的机器页面V2接口
 * 路径：/api/utilization/machine-v2
 * 方法：GET
 * 
 * 查询参数：
 * - date: 日期 (YYYY-MM-DD)，可选，默认为当前日期
 * 
 * 返回数据：
 * - overview: 利用率概览数据
 * - device_ranking: 设备利用率排名数据
 * - work_time_settings: 工作时间设置
 * - last_updated: 最后更新时间
 * 
 * 特点：
 * - 后端统一处理所有数据计算
 * - 前端只需一次API调用获取全部数据
 * - 减少前端数据处理逻辑
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];

    console.log(`📡 [Machine V2 API] 代理请求: date=${date}`);

    // 构建后端API URL
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();
    const backendUrl = `${getApiBaseUrl()}/api/utilization/machine-v2?date=${date}`;

    // 发送请求到后端API
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
    });

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status} ${response.statusText}`);
    }

    // 获取响应数据
    const data = await response.json();

    console.log(`✅ [Machine V2 API] 数据获取成功: 总体利用率=${data.overview?.overall_utilization?.toFixed(1)}%, 设备数量=${data.device_ranking?.devices?.length}`);

    // 返回数据给前端
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('❌ [Machine V2 API] 请求失败:', error);

    return NextResponse.json(
      {
        success: false,
        message: '获取机器页面数据失败',
        error: error instanceof Error ? error.message : '未知错误',
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    );
  }
}
