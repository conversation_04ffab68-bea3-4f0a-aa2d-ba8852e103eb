/**
 * 准确的每日利用率趋势API
 * 
 * 功能：复刻device_utilization_machine页面的准确算法，为每日利用率趋势提供精确数据
 * 路径：/api/utilization/accurate-daily-trend
 * 方法：GET
 * 
 * 算法特点：
 * 1. 获取所有设备列表
 * 2. 并行调用每台设备的状态详情API
 * 3. 前端聚合计算每日总体利用率
 * 4. 支持多日期范围查询
 * 
 * 查询参数：
 * - start_date: 开始日期 (YYYY-MM-DD)
 * - end_date: 结束日期 (YYYY-MM-DD)
 * - days: 天数（从今天往前推），可选
 * 
 * 返回数据：
 * - start_date: 开始日期
 * - end_date: 结束日期
 * - daily_data: 每日数据（使用准确算法计算）
 * - average_rate: 平均利用率
 * - max_rate: 最高利用率
 * - min_rate: 最低利用率
 * - trend_direction: 趋势方向
 * - last_updated: 最后更新时间
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

// 数据类型定义
interface DailyUtilizationData {
  date: string;
  utilization_rate: number;
  total_devices: number;
  active_devices: number;
  working_hours: number;
  productive_hours: number;
}

interface DeviceUtilizationStats {
  utilizationRate: number;
  productionTime: number;
  totalTime: number;
}

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 [Accurate Daily Trend] 开始处理请求');

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get('start_date');
    const endDateParam = searchParams.get('end_date');
    const daysParam = searchParams.get('days');

    // 计算日期范围
    let startDate: string;
    let endDate: string;

    if (daysParam) {
      // 使用days参数
      const days = parseInt(daysParam, 10);
      const today = new Date();
      endDate = today.toISOString().split('T')[0];
      const start = new Date(today);
      start.setDate(start.getDate() - days + 1);
      startDate = start.toISOString().split('T')[0];
    } else {
      // 使用start_date和end_date参数
      endDate = endDateParam || new Date().toISOString().split('T')[0];
      const defaultStart = new Date();
      defaultStart.setDate(defaultStart.getDate() - 6);
      startDate = startDateParam || defaultStart.toISOString().split('T')[0];
    }

    console.log(`📅 [Accurate Daily Trend] 查询日期范围: ${startDate} 至 ${endDate}`);

    // 生成日期列表（从大到小，最新日期在前）
    const dates = generateDateRange(startDate, endDate).reverse();
    console.log(`📋 [Accurate Daily Trend] 生成日期列表: ${dates.length} 天`);

    // 获取所有设备列表
    const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:9005';
    const devicesResponse = await fetch(`${API_HOST}/api/public/devices/configs`);
    if (!devicesResponse.ok) {
      throw new Error('获取设备列表失败');
    }
    const devicesData = await devicesResponse.json();
    const devices = devicesData.data || devicesData.devices || [];

    console.log(`🏭 [Accurate Daily Trend] 获取到 ${devices.length} 台设备`);

    // 串行计算每日利用率（从最新日期开始，支持无感渲染）
    const validDailyData: DailyUtilizationData[] = [];

    for (const date of dates) {
      try {
        const dailyData = await calculateDailyUtilization(date, devices);
        if (dailyData) {
          validDailyData.push(dailyData);
        }
      } catch (error) {
        console.warn(`⚠️ [${date}] 计算失败，跳过:`, error);
      }
    }

    console.log(`✅ [Accurate Daily Trend] 成功计算 ${validDailyData.length} 天的利用率数据`);

    // 计算统计指标
    const utilizationRates = validDailyData.map(d => d.utilization_rate);
    const averageRate = utilizationRates.length > 0 ? utilizationRates.reduce((a, b) => a + b, 0) / utilizationRates.length : 0;
    const maxRate = utilizationRates.length > 0 ? Math.max(...utilizationRates) : 0;
    const minRate = utilizationRates.length > 0 ? Math.min(...utilizationRates) : 0;

    // 计算趋势方向
    let trendDirection = 'stable';
    if (validDailyData.length >= 2) {
      const firstRate = validDailyData[validDailyData.length - 1].utilization_rate;
      const lastRate = validDailyData[0].utilization_rate;
      const diff = lastRate - firstRate;
      if (diff > 2) {
        trendDirection = 'up';
      } else if (diff < -2) {
        trendDirection = 'down';
      }
    }

    const result = {
      start_date: startDate,
      end_date: endDate,
      daily_data: validDailyData,
      average_rate: averageRate,
      max_rate: maxRate,
      min_rate: minRate,
      trend_direction: trendDirection,
      last_updated: new Date().toISOString()
    };

    console.log(`📊 [Accurate Daily Trend] 返回结果: 平均利用率 ${averageRate.toFixed(1)}%`);

    return NextResponse.json(result, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('❌ [Accurate Daily Trend] API错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: '获取准确每日利用率趋势失败',
        error: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

/**
 * 计算单日利用率（复刻device_utilization_machine的算法）
 */
async function calculateDailyUtilization(date: string, devices: any[]): Promise<DailyUtilizationData | null> {
  try {
    console.log(`📊 [${date}] 开始计算利用率`);

    // 并行获取所有设备的状态详细数据
    const devicePromises = devices.map(async (device: any) => {
      try {
        const deviceId = device.id || device.device_id;
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/devices/${deviceId}/status-detail?date=${date}`, {
          headers: {
            'Cache-Control': 'no-cache',
          },
        });

        if (!response.ok) {
          console.warn(`⚠️ [${date}] 获取设备 ${deviceId} 数据失败: ${response.status}`);
          return null;
        }

        const data = await response.json();
        return {
          deviceId,
          stats: data.utilizationStats as DeviceUtilizationStats
        };
      } catch (error) {
        const deviceId = device.id || device.device_id;
        console.warn(`⚠️ [${date}] 获取设备 ${deviceId} 数据异常:`, error);
        return null;
      }
    });

    const deviceResults = await Promise.all(devicePromises);
    const validDevices = deviceResults.filter((result): result is NonNullable<typeof result> => result !== null && !!result?.stats);

    if (validDevices.length === 0) {
      console.warn(`⚠️ [${date}] 没有有效的设备数据`);
      return null;
    }

    // 计算总体利用率（使用加权平均算法，与device_utilization_machine一致）
    const totalWorkingTime = validDevices.reduce((sum, device) => {
      return sum + (device.stats.totalTime / 3600); // 转换为小时
    }, 0);

    const totalProductiveTime = validDevices.reduce((sum, device) => {
      return sum + (device.stats.productionTime / 3600); // 转换为小时
    }, 0);

    const overallUtilization = totalWorkingTime > 0 ? (totalProductiveTime / totalWorkingTime) * 100 : 0;

    const result: DailyUtilizationData = {
      date,
      utilization_rate: overallUtilization,
      total_devices: devices.length,
      active_devices: validDevices.length,
      working_hours: totalWorkingTime,
      productive_hours: totalProductiveTime
    };

    console.log(`✅ [${date}] 计算完成: 利用率 ${overallUtilization.toFixed(1)}%, 有效设备 ${validDevices.length}/${devices.length}`);

    return result;
  } catch (error) {
    console.error(`❌ [${date}] 计算利用率失败:`, error);
    return null;
  }
}

/**
 * 生成日期范围
 */
function generateDateRange(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const current = new Date(start);
  while (current <= end) {
    dates.push(current.toISOString().split('T')[0]);
    current.setDate(current.getDate() + 1);
  }

  return dates;
}
