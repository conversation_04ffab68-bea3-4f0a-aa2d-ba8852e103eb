/**
 * 单台设备信息API路由
 *
 * 该文件实现了单台设备详细信息的API端点
 * 从MongoDB获取设备的基本配置信息，如名称、型号、位置等
 * 
 * 功能：
 * - 获取指定设备ID的详细配置信息
 * - 支持多种设备ID匹配方式（id、device_id、code、name）
 * - 提供完整的设备信息，包括品牌、型号、位置、IP地址等
 * - 支持错误处理和容错机制
 * 
 * API端点：GET /api/machine/[id]
 * 
 * 请求参数：
 * - id (路径参数): 设备ID，支持多种格式
 * 
 * 响应格式：
 * {
 *   "id": "设备MongoDB ID",
 *   "device_id": "设备业务ID", 
 *   "name": "设备名称",
 *   "brand": "设备品牌",
 *   "model": "设备型号",
 *   "location": "设备位置",
 *   "data_type": "数据类型",
 *   "ip": "设备IP地址",
 *   "port": 设备端口,
 *   "description": "设备描述",
 *   "created_at": "创建时间",
 *   "updated_at": "更新时间"
 * }
 * 
 * 状态码：
 * - 200: 查询成功
 * - 404: 设备不存在
 * - 500: 服务器内部错误
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

// 后端API基础URL，从环境变量获取（统一使用NEXT_PUBLIC_getApiBaseUrl()）
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

// 调试信息：打印环境变量值（开发环境）
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Machine API - API_BASE_URL 环境变量值:', process.env.NEXT_PUBLIC_API_BASE_URL);
  console.log('🔧 Machine API - 实际使用的 getApiBaseUrl():', getApiBaseUrl());
}

/**
 * GET请求处理函数 - 获取单台设备信息
 *
 * 处理对/api/machine/[id]端点的GET请求
 * 从后端MongoDB API获取指定设备的详细配置信息
 *
 * @param {NextRequest} request - Next.js请求对象
 * @param {Object} params - 路由参数对象
 * @param {Object} params.params - 包含路由参数的对象
 * @param {string} params.params.id - 设备ID参数
 * @returns {NextResponse} 包含设备信息的响应对象
 * 
 * @example
 * GET /api/machine/test-37
 * 返回test-37设备的详细信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const startTime = Date.now();

  try {
    // 获取设备ID参数
    const deviceId = params.id;

    console.log(`🔍 [Machine API] 获取设备信息 - 设备ID: ${deviceId}`);

    // 构建后端API请求URL - 使用新的MongoDB API端点
    const requestUrl = `${getApiBaseUrl()}/api/machine/${deviceId}`;

    console.log(`📡 [Machine API] 请求后端API: ${requestUrl}`);

    // 向后端API发送请求
    const response = await fetch(requestUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // 添加缓存控制头，确保获取最新数据
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'If-None-Match': '*',
        'If-Modified-Since': 'Thu, 01 Jan 1970 00:00:00 GMT'
      },
      // 强制禁用缓存
      cache: 'no-store'
    });

    const duration = Date.now() - startTime;
    console.log(`⏱️ [Machine API] 后端API响应时间: ${duration}ms, 状态码: ${response.status}`);

    // 检查响应状态
    if (!response.ok) {
      if (response.status === 404) {
        console.warn(`⚠️ [Machine API] 设备未找到: ${deviceId}`);
        return NextResponse.json(
          {
            success: false,
            message: '设备不存在',
            error: `Device ID ${deviceId} not found`
          },
          { status: 404 }
        );
      }

      console.error(`❌ [Machine API] 后端API请求失败: ${response.status} ${response.statusText}`);
      throw new Error(`后端API请求失败: ${response.status} ${response.statusText}`);
    }

    // 获取后端返回的数据
    const data = await response.json();

    // 开发环境调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ [Machine API] 成功获取设备信息:`, {
        device_id: data.device_id,
        name: data.name,
        location: data.location,
        brand: data.brand,
        model: data.model,
        data_type: data.data_type
      });
    }

    // 返回设备信息
    return NextResponse.json(data, {
      headers: {
        'Content-Type': 'application/json',
        // 添加缓存控制头
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [Machine API] 获取设备信息出错 (${duration}ms):`, error);

    // 返回错误响应
    return NextResponse.json(
      {
        success: false,
        message: '获取设备信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
