/**
 * 产量历史数据 API 代理
 *
 * 将前端的产量历史数据请求代理到后端 API
 */
import { NextRequest, NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

// 后端 API 基础 URL（统一使用NEXT_PUBLIC_getApiBaseUrl()）
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9005';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

// 请求超时时间（毫秒）
const TIMEOUT_MS = 3000;

/**
 * 处理 GET 请求
 * 将请求代理到后端 API 并返回响应
 */
export async function GET(request: NextRequest) {
  // 获取查询参数
  const searchParams = request.nextUrl.searchParams;
  const date = searchParams.get('date');

  // 构建后端 API URL
  let apiUrl = `${getApiBaseUrl()}/api/production-history`;
  if (date) {
    apiUrl += `?date=${date}`;
  }

  try {
    // 创建一个 AbortController 用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

    // 发送请求到后端 API
    const response = await fetch(apiUrl, {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 清除超时计时器
    clearTimeout(timeoutId);

    // 检查响应状态
    if (!response.ok) {
      // 如果后端返回错误，将错误传递给前端
      return NextResponse.json(
        { error: `后端 API 错误: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    // 获取响应数据
    const data = await response.json();

    // 返回响应给前端
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('产量历史数据 API 代理错误:', error);

    // 处理超时错误
    if (error.name === 'AbortError') {
      return NextResponse.json(
        { error: '请求超时，请稍后重试' },
        { status: 504 }
      );
    }

    // 处理其他错误
    return NextResponse.json(
      { error: `请求失败: ${error.message}` },
      { status: 500 }
    );
  }
}
