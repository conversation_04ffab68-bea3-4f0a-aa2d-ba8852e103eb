/**
 * 设备状态API路由
 *
 * 该文件实现了设备数据的API端点，作为后端API的代理
 * 将请求转发到后端FastAPI服务
 * 支持多环境部署和智能API地址解析
 */
import { NextResponse } from 'next/server';
import { getBackendApiUrl } from '../../../config/api';

// 强制动态渲染，避免静态生成错误
export const dynamic = 'force-dynamic';

// 使用智能API配置，支持多环境部署
const getApiBaseUrl = () => getBackendApiUrl();

// 调试信息：打印环境变量值（开发环境）
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 API_BASE_URL 环境变量值:', process.env.NEXT_PUBLIC_API_BASE_URL);
  console.log('🔧 实际使用的 API_BASE_URL:', getApiBaseUrl());
}
import { Machine } from '@/types';

/**
 * GET请求处理函数
 *
 * 处理对/api/machines端点的GET请求
 * 将请求转发到后端FastAPI服务
 *
 * @returns NextResponse包含从后端获取的数据
 */
export async function GET() {
  try {
    // 从后端API获取数据，使用智能API配置
    const apiBaseUrl = getApiBaseUrl();
    const requestUrl = `${apiBaseUrl}/api/machines`;

    const response = await fetch(requestUrl, {
      // 添加缓存控制头，防止浏览器和Node.js缓存
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'If-None-Match': '*',
        'If-Modified-Since': 'Thu, 01 Jan 1970 00:00:00 GMT'
      },
      // 强制禁用缓存
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error(`后端API请求失败: ${response.status}`);
    }

    // 获取后端返回的数据
    const data = await response.json();

    // 开发环境调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('后端API返回的设备数量:', data.machines?.length || 0);
      if (data.machines && data.machines.length > 0) {
        console.log('第一个设备:', {
          id: data.machines[0].id,
          name: data.machines[0].name,
          brand: data.machines[0].brand,
          status: data.machines[0].status
        });
      }
    }

    // 确保数据中的每个machine都有正确的字段和值
    if (data.machines && Array.isArray(data.machines)) {
      // 直接使用后端返回的数据，不做任何修改
      // 这样可以保留原始的id、quantity和plan值
    }

    // 将数据返回给前端，使用原始数据
    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('获取设备数据出错:', error);
    return NextResponse.json(
      { error: '获取设备数据失败' },
      { status: 500 }
    );
  }
}