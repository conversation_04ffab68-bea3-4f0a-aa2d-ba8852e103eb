'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { History, Calendar, ChevronLeft, ChevronRight, ArrowLeftRight, Clock } from 'lucide-react';
import { getDeviceStatusHistoryV2 } from '@/services/deviceService';
import { getDisplaySettings } from '@/services/settingsService';
import DatePicker from '@/components/common/DatePicker';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorAlert from '@/components/common/ErrorAlert';
import DeviceSelector from '@/components/device/DeviceSelector';

// 时间段状态记录接口
interface TimeSlotStatusRecord {
  id: string;
  device_id: string;
  status: string;
  start_time: string;
  end_time: string;
  duration: number;
  position: number;
  width: number;
  tooltip: string;
  original_start: string;
  original_end: string;
  original_timestamp: string; // InfluxDB原始时间戳
  is_segmented: boolean;
}

// 时间段数据接口
interface TimeSlotData {
  label: string;
  start: string;
  end: string;
  records: TimeSlotStatusRecord[];
}

// 设备状态历史响应接口
interface DeviceStatusHistoryResponse {
  device_info: {
    id: string;
    code: string;
    name: string;
    brand: string;
    model: string;
    location: string;
  };
  time_slots: TimeSlotData[];
  date: string;
  total: number;
}

export default function DeviceStatusHistoryPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();

  // 安全地获取设备ID，添加空值检查
  const deviceId = params?.id as string;
  const dateParam = searchParams?.get('date');

  // 如果没有设备ID，显示错误
  if (!deviceId) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">错误</h1>
          <p className="text-gray-600">设备ID参数缺失</p>
        </div>
      </div>
    );
  }

  // 状态管理
  const [deviceInfo, setDeviceInfo] = useState<any>({});
  const [timeSlots, setTimeSlots] = useState<TimeSlotData[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date>(
    dateParam ? new Date(dateParam) : new Date()
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);

  // 状态显示模式：'count'（次数）或'duration'（时长）
  const [statusDisplayMode, setStatusDisplayMode] = useState<'count' | 'duration'>('duration');
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造监控系统',
    dateTime: ''
  });

  // 状态颜色映射（参考图片样式）
  const statusColors = {
    production: '#22c55e', // 绿色 - 生产中
    idle: '#f59e0b',       // 橙色 - 空闲
    fault: '#ef4444',      // 红色 - 故障
    shutdown: '#6b7280'    // 灰色 - 关机
  };

  // 状态名称映射（只包含需要的状态）
  const statusNames = {
    production: '生产中',
    idle: '空闲',
    fault: '故障',
    shutdown: '关机'
  };

  // 获取设备状态历史数据
  const loadStatusHistory = async () => {
    if (!deviceId) return;

    setLoading(true);
    setError(null);

    try {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      console.log(`📡 加载设备 ${deviceId} 在 ${formattedDate} 的状态历史数据`);

      const data: DeviceStatusHistoryResponse = await getDeviceStatusHistoryV2(deviceId, formattedDate);

      // 更新设备信息和时间段数据
      setDeviceInfo(data.device_info || {});
      setTimeSlots(data.time_slots || []);

      console.log(`✅ 成功获取 ${data.time_slots?.length || 0} 个时间段，总记录数: ${data.total}`);

      // 计算总记录数用于调试
      const totalRecords = data.time_slots?.reduce((sum, slot) => sum + slot.records.length, 0) || 0;
      console.log(`📊 时间段记录分布: ${totalRecords} 条记录分布在 ${data.time_slots?.length || 0} 个时间段中`);

    } catch (err) {
      console.error('❌ 获取状态历史失败:', err);
      setError(err instanceof Error ? err.message : '获取数据失败');
      setTimeSlots([]);
      setDeviceInfo({});
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadStatusHistory();
  }, [deviceId, selectedDate.toISOString().split('T')[0]]); // 使用日期字符串避免无限重渲染

  // 在客户端更新时间
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 日期变化处理
  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
    const formattedDate = date.toISOString().split('T')[0];
    const newUrl = `/device_status_history/${deviceId}?date=${formattedDate}`;
    router.push(newUrl);
  };

  // 日期导航
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    handleDateChange(newDate);
  };

  // 返回设备列表
  const goBackToDeviceList = () => {
    router.push('/');
  };

  // 返回设备选择页面
  const goBackToDeviceSelection = () => {
    router.push('/device_status_history');
  };

  // 处理设备切换
  const handleDeviceChange = (newDeviceId: string) => {
    const formattedDate = selectedDate.toISOString().split('T')[0];
    const newUrl = `/device_status_history/${newDeviceId}?date=${formattedDate}`;
    router.push(newUrl);
  };

  // 切换状态显示模式
  const toggleStatusDisplayMode = () => {
    setStatusDisplayMode(prevMode => prevMode === 'count' ? 'duration' : 'count');
    console.log(`切换状态显示模式: ${statusDisplayMode === 'count' ? 'duration' : 'count'}`);
  };

  // 计算各状态的数量（只包含需要的状态）
  const calculateStatusCounts = (timeSlots: TimeSlotData[]) => {
    const counts: Record<string, number> = {
      'production': 0,
      'idle': 0,
      'fault': 0,
      'shutdown': 0
    };

    // 遍历时间段和记录，统计各状态的数量
    timeSlots.forEach(slot => {
      slot.records.forEach(record => {
        if (counts[record.status] !== undefined) {
          counts[record.status]++;
        }
      });
    });

    return counts;
  };

  // 计算各状态的累计时长（秒）
  const calculateStatusDurations = (timeSlots: TimeSlotData[]) => {
    const durations: Record<string, number> = {
      'production': 0,
      'idle': 0,
      'fault': 0,
      'shutdown': 0
    };

    // 遍历时间段和记录，计算各状态的累计时长
    timeSlots.forEach(slot => {
      slot.records.forEach(record => {
        if (durations[record.status] !== undefined) {
          durations[record.status] += record.duration || 0;
        }
      });
    });

    return durations;
  };

  // 格式化时长为可读格式（秒级精度）
  const formatDuration = (totalSeconds: number): string => {
    // 确保输入是整数秒
    const roundedSeconds = Math.round(totalSeconds);

    const hours = Math.floor(roundedSeconds / 3600);
    const minutes = Math.floor((roundedSeconds % 3600) / 60);
    const seconds = roundedSeconds % 60;

    const parts = [];
    if (hours > 0) parts.push(`${hours}时`);
    if (minutes > 0) parts.push(`${minutes}分`);
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`);

    return parts.join('');
  };

  // 获取状态的中文名称（只包含需要的状态）
  const getStatusName = (status: string): string => {
    const statusMap: Record<string, string> = {
      'production': '生产',
      'idle': '空闲',
      'fault': '故障',
      'shutdown': '关机'
    };

    return statusMap[status] || status;
  };



  // 渲染时间段
  const renderTimeSlot = (timeSlot: TimeSlotData, index: number) => {

    return (
      <div key={index}>
        <div className="flex items-stretch">
          {/* 时间段标签 */}
          <div
            className="bg-gray-950 text-xs font-medium flex items-center justify-end pr-2 flex-shrink-0 whitespace-nowrap"
            style={{ width: '120px' }}
          >
            {timeSlot.label}
          </div>

          {/* 状态时间线 */}
          <div
            className="flex-1 relative overflow-hidden bg-gray-700"
            style={{ height: '40px' }}
          >
            {timeSlot.records && timeSlot.records.map((record, recordIndex) => {
              // 秒级精度计算：确保位置和宽度基于精确的秒数计算
              const precisePosition = Math.max(0, Math.min(100, record.position));
              const preciseWidth = Math.max(0.05, Math.min(100 - precisePosition, record.width)); // 最小宽度0.05%

              return (
                <div
                  key={recordIndex}
                  className="absolute top-0 h-full cursor-pointer hover:brightness-110 transition-all duration-200"
                  style={{
                    left: `${precisePosition.toFixed(3)}%`, // 保留3位小数，提供秒级精度
                    width: `${preciseWidth.toFixed(3)}%`,   // 保留3位小数，提供秒级精度
                    backgroundColor: statusColors[record.status as keyof typeof statusColors] || '#6b7280',
                    minWidth: '1px', // 最小像素宽度1px，确保可见
                    zIndex: 1
                  }}
                  title={record.tooltip || `${record.start_time}-${record.end_time} (${formatDuration(record.duration)})`}
                />
              );
            })}

            {(!timeSlot.records || timeSlot.records.length === 0) && (
              <div className="h-full flex items-center justify-center text-xs text-gray-400">
                无记录
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white flex">
      <Sidebar mode={sidebarMode} />

      <div className="flex-1 flex flex-col">
        <Header
          companyInfo={companyInfo}
          toggleSidebar={() => setSidebarMode(sidebarMode === SidebarMode.COLLAPSED ? SidebarMode.HIDDEN : SidebarMode.COLLAPSED)}
        />

        <main className="flex-1 px-3 py-2">
          {/* 页面标题和导航 */}
          <div className="bg-blue-900/20 py-1 px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {/* 导航按钮和设备选择器在同一行 */}
                <div className="flex items-center">
                  <div className="flex space-x-1 mr-2">
                    <button
                      onClick={goBackToDeviceList}
                      className="p-1 rounded-full hover:bg-gray-700"
                      title="返回设备列表"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </button>
                    <button
                      onClick={goBackToDeviceSelection}
                      className="p-1 rounded-full hover:bg-gray-700 flex items-center justify-center"
                      title="返回设备选择"
                    >
                      <History className="h-4 w-4" />
                    </button>
                  </div>

                  <DeviceSelector
                    currentDeviceId={deviceId}
                    onDeviceChange={handleDeviceChange}
                    selectedDate={selectedDate}
                  />

                  {/* 设备名称和车间信息 */}
                  {deviceInfo && (
                    <div className="text-gray-300 text-sm ml-3">
                      <span className="font-medium">{deviceInfo.name}</span>
                      {deviceInfo.location && (
                        <>
                          <span className="mx-2 text-gray-500">|</span>
                          <span>{deviceInfo.location}</span>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* 日期选择器 */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => navigateDate('prev')}
                  className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>

                <DatePicker
                  selectedDate={selectedDate}
                  onChange={handleDateChange}
                  className="bg-gray-800 border-gray-700"
                />

                <button
                  onClick={() => navigateDate('next')}
                  className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>



          {/* 加载状态 */}
          {loading && (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          )}

          {/* 错误状态 */}
          {error && (
            <ErrorAlert message={error} onRetry={loadStatusHistory} />
          )}

          {/* 状态时间线 */}
          {!loading && !error && (
            <div className="bg-gray-950 shadow-md overflow-hidden">
              <div className="p-0">
                {/* 标题和状态颜色标识条在同一行 */}
                <div className="flex items-center justify-between p-2 bg-gray-900">
                  <h3 className="text-sm font-medium">
                    {selectedDate.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' })} 状态历史
                  </h3>

                  {/* 切换按钮 */}
                  <button
                    onClick={toggleStatusDisplayMode}
                    className="p-1 hover:bg-gray-800 transition-colors flex items-center text-xs"
                    title={statusDisplayMode === 'count' ? "切换到时长统计" : "切换到次数统计"}
                  >
                    <ArrowLeftRight className="h-3 w-3 mr-1" />
                    {statusDisplayMode === 'count' ? "次数" : "时长"}
                  </button>
                </div>

                {/* 状态颜色标识条 - 更紧凑的布局 */}
                <div className="flex items-center flex-wrap gap-2 p-2 bg-gray-900 border-t border-b border-gray-800">
                  {statusDisplayMode === 'count' ? (
                    // 显示状态次数
                    Object.entries(calculateStatusCounts(timeSlots)).map(([status, count]) =>
                      count > 0 ? (
                        <div key={status} className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-1"
                            style={{ backgroundColor: statusColors[status as keyof typeof statusColors] || '#6b7280' }}
                          ></div>
                          <span className="text-xs font-medium">{getStatusName(status)}</span>
                          <span className="ml-1 text-xs bg-gray-800 px-1.5 py-0.5 rounded-full">{count}</span>
                        </div>
                      ) : null
                    )
                  ) : (
                    // 显示状态时长
                    Object.entries(calculateStatusDurations(timeSlots)).map(([status, duration]) =>
                      duration > 0 ? (
                        <div key={status} className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-1"
                            style={{ backgroundColor: statusColors[status as keyof typeof statusColors] || '#6b7280' }}
                          ></div>
                          <span className="text-xs font-medium">{getStatusName(status)}</span>
                          <span className="ml-1 text-xs bg-gray-800 px-1.5 py-0.5 rounded-full flex items-center">
                            <Clock className="h-2.5 w-2.5 mr-0.5" />
                            {formatDuration(duration)}
                          </span>
                        </div>
                      ) : null
                    )
                  )}
                </div>

                {/* 状态时间线 */}
                <div
                  className="mt-2"
                  style={{
                    gap: '8px',
                    display: 'flex',
                    flexDirection: 'column'
                  }}
                >
                  {timeSlots.map((timeSlot, index) => renderTimeSlot(timeSlot, index))}
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
