'use client';

/**
 * 设备状态历史 - 设备选择页面
 *
 * 功能特性：
 * - 设备列表展示
 * - 设备搜索和筛选
 * - 快速跳转到设备状态历史
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { getApiBaseUrl } from '@/config/api';
import { History, Search, X, Filter, ChevronRight } from 'lucide-react';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorAlert from '@/components/common/ErrorAlert';

// 设备接口定义
interface Device {
  id: string;
  name: string;
  brand?: string;
  location?: string;
  model?: string;
  status?: string;
  quantity?: number;
  plan?: number;
}

export default function DeviceStatusHistorySelectPage() {
  const router = useRouter();

  // 状态管理
  const [devices, setDevices] = useState<Device[]>([]);
  const [filteredDevices, setFilteredDevices] = useState<Device[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造监控系统',
    dateTime: ''
  });

  // 状态颜色映射
  const statusColors = {
    production: 'bg-green-500',
    idle: 'bg-yellow-500',
    fault: 'bg-red-500',
    adjusting: 'bg-blue-500',
    shutdown: 'bg-gray-500',
    disconnected: 'bg-gray-400',
    maintenance: 'bg-purple-500',
    debugging: 'bg-cyan-500'
  };

  // 状态名称映射
  const statusNames = {
    production: '生产中',
    idle: '空闲',
    fault: '故障',
    adjusting: '调机',
    shutdown: '关机',
    disconnected: '未连接',
    maintenance: '维护',
    debugging: '调试'
  };

  // 获取设备列表
  const loadDevices = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('📡 获取设备列表...');

      // 使用统一的环境变量直接调用后端API
      const backendUrl = getApiBaseUrl();
      const apiUrl = `${backendUrl}/api/machines`;

      console.log(`🔗 [Device Status History] 直接调用后端API: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      });

      if (!response.ok) {
        throw new Error(`获取设备列表失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.machines && Array.isArray(data.machines)) {
        console.log(`✅ 成功获取 ${data.machines.length} 个设备`);
        setDevices(data.machines);
        setFilteredDevices(data.machines);
      } else {
        throw new Error('获取设备列表格式错误');
      }
    } catch (err) {
      console.error('❌ 获取设备列表失败:', err);
      setError(err instanceof Error ? err.message : '获取设备列表失败');
      setDevices([]);
      setFilteredDevices([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadDevices();
  }, []);

  // 在客户端更新时间
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);

    if (value.trim() === '') {
      setFilteredDevices(devices);
    } else {
      const filtered = devices.filter(device =>
        device.name.toLowerCase().includes(value.toLowerCase()) ||
        (device.brand && device.brand.toLowerCase().includes(value.toLowerCase())) ||
        (device.location && device.location.toLowerCase().includes(value.toLowerCase())) ||
        (device.model && device.model.toLowerCase().includes(value.toLowerCase()))
      );
      setFilteredDevices(filtered);
    }
  };

  // 清除搜索
  const clearSearch = () => {
    setSearchTerm('');
    setFilteredDevices(devices);
  };

  // 跳转到设备状态历史
  const goToDeviceHistory = (deviceId: string) => {
    const today = new Date().toISOString().split('T')[0];
    router.push(`/device_status_history/${deviceId}?date=${today}`);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white flex">
      <Sidebar mode={sidebarMode} />

      <div className="flex-1 flex flex-col">
        <Header
          companyInfo={companyInfo}
          toggleSidebar={() => setSidebarMode(sidebarMode === SidebarMode.COLLAPSED ? SidebarMode.HIDDEN : SidebarMode.COLLAPSED)}
        />

        <main className="flex-1 p-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <History className="w-8 h-8 text-blue-400" />
              <div>
                <h1 className="text-2xl font-bold">设备状态历史</h1>
                <p className="text-gray-400">选择设备查看状态历史</p>
              </div>
            </div>

            {/* 搜索框 */}
            <div className="relative w-80">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索设备名称、品牌、位置..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg pl-10 pr-10 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {searchTerm && (
                <button
                  onClick={clearSearch}
                  className="absolute right-3 top-3"
                >
                  <X className="h-4 w-4 text-gray-400 hover:text-white" />
                </button>
              )}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-400">
                显示 {filteredDevices.length} / {devices.length} 个设备
                {searchTerm && ` (搜索: "${searchTerm}")`}
              </div>
              <div className="flex items-center space-x-4 text-sm">
                {Object.entries(statusNames).map(([status, name]) => {
                  const count = filteredDevices.filter(d => d.status === status).length;
                  if (count === 0) return null;
                  return (
                    <div key={status} className="flex items-center space-x-1">
                      <div className={`w-3 h-3 rounded-full ${statusColors[status as keyof typeof statusColors]}`} />
                      <span>{name}: {count}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* 加载状态 */}
          {loading && (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          )}

          {/* 错误状态 */}
          {error && (
            <ErrorAlert message={error} onRetry={loadDevices} />
          )}

          {/* 设备列表 */}
          {!loading && !error && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredDevices.map(device => (
                <div
                  key={device.id}
                  className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors cursor-pointer border border-gray-700 hover:border-blue-500"
                  onClick={() => goToDeviceHistory(device.id)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-lg truncate">{device.name}</h3>
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">品牌:</span>
                      <span>{device.brand || '未知'}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">位置:</span>
                      <span>{device.location || '未知'}</span>
                    </div>
                    {device.model && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">型号:</span>
                        <span>{device.model}</span>
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">状态:</span>
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${statusColors[device.status as keyof typeof statusColors] || 'bg-gray-500'}`} />
                        <span>{statusNames[device.status as keyof typeof statusNames] || '未知'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 无数据状态 */}
          {!loading && !error && filteredDevices.length === 0 && (
            <div className="text-center py-12">
              <History className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">
                {searchTerm ? '未找到匹配的设备' : '暂无设备数据'}
              </h3>
              <p className="text-gray-500">
                {searchTerm ? '请尝试其他搜索关键词' : '请检查设备连接状态'}
              </p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
