'use client';

/**
 * 设备产量历史页面
 *
 * 显示所有设备的产量历史信息
 * 支持按设备和产品筛选，以及日期选择
 */
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Calendar as CalendarIcon, Search, Filter, Loader2, ChevronLeft, ChevronRight } from 'lucide-react';
import DatePicker from '@/components/common/DatePicker';

import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { Machine, Product, ProductionRecord, CompanyInfo } from '@/types';
import { getProductionHistory } from '@/services/productionHistoryService';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorAlert from '@/components/common/ErrorAlert';
import MultiDeviceSelector from '@/components/device/MultiDeviceSelector';

export default function DeviceProductionHistoryPage() {
  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);

  // 最大化状态
  const [isMaximized, setIsMaximized] = useState(false);

  // 公司信息状态
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '',
    dateTime: ''
  });

  // 数据状态
  const [machines, setMachines] = useState<Machine[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [productionRecords, setProductionRecords] = useState<ProductionRecord[]>([]);

  // 筛选状态
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [deviceFilter, setDeviceFilter] = useState('');
  const [productFilter, setProductFilter] = useState('');
  const [selectedDeviceIds, setSelectedDeviceIds] = useState<string[]>([]);

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 路由
  const router = useRouter();

  // 在客户端初始化时从localStorage加载侧边栏状态
  useEffect(() => {
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        setSidebarMode(mode);
      } catch (error) {
        console.error('加载侧边栏状态失败:', error);
      }
    };

    loadSidebarMode();
  }, []);

  // 切换最大化状态
  const toggleMaximize = useCallback(() => {
    setIsMaximized(prev => {
      const newState = !prev;

      if (newState) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }

      return newState;
    });
  }, []);

  // 监听全屏变化事件，同步状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  // 切换侧边栏显示模式
  const toggleSidebar = useCallback(async () => {
    try {
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');

      const nextMode = getNextSidebarMode(sidebarMode);
      saveSidebarMode(nextMode);

      setSidebarMode(nextMode);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  // 加载产量历史数据
  const loadProductionHistory = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 格式化日期为YYYY-MM-DD
      const dateStr = format(selectedDate, 'yyyy-MM-dd');

      // 获取产量历史数据
      const data = await getProductionHistory(dateStr);

      // 更新状态
      setMachines(data.machines);
      setProducts(data.products);
      setProductionRecords(data.productionRecords);
      setCompanyInfo(data.companyInfo);
    } catch (error) {
      console.error('加载产量历史数据失败:', error);
      setError('加载产量历史数据失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  }, [selectedDate]);

  // 初始加载数据
  useEffect(() => {
    loadProductionHistory();
  }, [loadProductionHistory]);

  // 在客户端更新时间
  useEffect(() => {
    // 更新当前时间
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    // 立即更新一次
    updateTime();

    // 每秒更新一次
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 处理日期变化
  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
  };

  // 切换到前一天
  const goToPreviousDay = () => {
    const prevDay = new Date(selectedDate);
    prevDay.setDate(prevDay.getDate() - 1);
    setSelectedDate(prevDay);
  };

  // 切换到后一天
  const goToNextDay = () => {
    const nextDay = new Date(selectedDate);
    nextDay.setDate(nextDay.getDate() + 1);
    setSelectedDate(nextDay);
  };

  // 处理设备筛选变化
  const handleDeviceFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDeviceFilter(e.target.value);
  };

  // 处理产品筛选变化
  const handleProductFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProductFilter(e.target.value);
  };

  // 处理设备选择变化
  const handleDeviceSelectionChange = (deviceIds: string[]) => {
    setSelectedDeviceIds(deviceIds);
  };

  // 筛选设备（添加安全检查）
  const filteredMachines = (machines || []).filter(machine => {
    // 如果有选中的设备，只显示选中的设备
    if (selectedDeviceIds.length > 0 && !selectedDeviceIds.includes(machine.id)) {
      return false;
    }

    // 应用文本筛选
    const searchTerm = deviceFilter.toLowerCase();
    return (
      machine.id.toLowerCase().includes(searchTerm) ||
      machine.brand.toLowerCase().includes(searchTerm) ||
      machine.name.toLowerCase().includes(searchTerm) ||
      machine.location.toLowerCase().includes(searchTerm) ||
      machine.model.toLowerCase().includes(searchTerm)
    );
  });

  // 筛选产品（添加安全检查）
  const filteredProducts = (products || []).filter(product => {
    const searchTerm = productFilter.toLowerCase();
    return (
      product.id.toLowerCase().includes(searchTerm) ||
      product.code.toLowerCase().includes(searchTerm) ||
      product.name.toLowerCase().includes(searchTerm) ||
      product.processCode.toLowerCase().includes(searchTerm) ||
      product.processName.toLowerCase().includes(searchTerm) ||
      product.program.toLowerCase().includes(searchTerm)
    );
  });

  // 获取设备的产量记录（添加安全检查）
  const getDeviceProductionRecords = (deviceId: string) => {
    return (productionRecords || []).filter(record => record.deviceId === deviceId);
  };

  // 获取产品信息（添加安全检查）
  const getProductById = (productId: string) => {
    return (products || []).find(product => product.id === productId);
  };

  // 计算进度百分比
  const calculateProgress = (quantity: number, plan: number) => {
    if (plan <= 0) return 0;
    return Math.min(100, Math.round((quantity / plan) * 100));
  };

  return (
    <div className="flex h-screen bg-gray-950 text-white">
      {/* 侧边栏 */}
      <Sidebar mode={sidebarMode} />

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部导航栏 */}
        <Header
          companyInfo={companyInfo}
          toggleSidebar={toggleSidebar}
          toggleMaximize={toggleMaximize}
          isMaximized={isMaximized}
        />

        {/* 内容区域 */}
        <div className="flex-1 overflow-auto p-4">
          {/* 页面标题和筛选区域 */}
          <div className="mb-4">
            <h1 className="text-xl font-bold mb-4">设备产量历史</h1>

            {/* 筛选和日期选择 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 设备多选选择器 */}
                <MultiDeviceSelector
                  machines={machines || []}
                  selectedDeviceIds={selectedDeviceIds}
                  onDeviceSelectionChange={handleDeviceSelectionChange}
                  className="w-full"
                />

                {/* 产品筛选 */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Filter className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="筛选产品 (编号, 名称, 工序, 程序...)"
                    value={productFilter}
                    onChange={handleProductFilterChange}
                    className="bg-gray-800 text-white pl-10 pr-4 py-2 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 设备文本筛选 */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="筛选设备 (编号, 名称, 位置...)"
                    value={deviceFilter}
                    onChange={handleDeviceFilterChange}
                    className="bg-gray-800 text-white pl-10 pr-4 py-2 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 日期选择 */}
                <div className="flex items-center justify-end bg-gray-800 rounded-md px-3 py-2">
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={goToPreviousDay}
                      className="p-1 rounded-full hover:bg-gray-700"
                      title="前一天"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </button>

                    <DatePicker
                      selectedDate={selectedDate}
                      onChange={handleDateChange}
                      className="bg-gray-800 border border-gray-700 rounded-md px-2 py-0.5 text-xs"
                    />

                    <button
                      onClick={goToNextDay}
                      className="p-1 rounded-full hover:bg-gray-700"
                      title="后一天"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 内容主体 */}
          {isLoading ? (
            <LoadingSpinner message="加载设备产量历史中..." />
          ) : error ? (
            <ErrorAlert message={error} onRetry={loadProductionHistory} />
          ) : (
            <div className="grid grid-cols-1 gap-6">
              {filteredMachines.length === 0 ? (
                <div className="bg-gray-800 rounded-md p-4 text-center text-gray-400">
                  没有找到匹配的设备
                </div>
              ) : (
                filteredMachines.map(machine => {
                  // 获取该设备的产量记录
                  const deviceRecords = getDeviceProductionRecords(machine.id);

                  // 筛选产品后的记录
                  const filteredRecords = deviceRecords.filter(record => {
                    if (!productFilter) return true;
                    const product = getProductById(record.productId);
                    if (!product) return false;

                    const searchTerm = productFilter.toLowerCase();
                    return (
                      product.id.toLowerCase().includes(searchTerm) ||
                      product.code.toLowerCase().includes(searchTerm) ||
                      product.name.toLowerCase().includes(searchTerm) ||
                      product.processCode.toLowerCase().includes(searchTerm) ||
                      product.processName.toLowerCase().includes(searchTerm) ||
                      product.program.toLowerCase().includes(searchTerm)
                    );
                  });

                  // 如果没有匹配的记录，不显示该设备
                  if (filteredRecords.length === 0) return null;

                  return (
                    <div key={machine.id} className="bg-gray-800 rounded-md overflow-hidden">
                      {/* 设备信息头部 */}
                      <div className="bg-gray-700 p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-bold">{machine.name}</h3>
                            <div className="text-sm text-gray-400">
                              <span className="mr-4">编号: {machine.id}</span>
                              <span className="mr-4">型号: {machine.model}</span>
                              <span>位置: {machine.location}</span>
                            </div>
                          </div>
                          <div className="text-sm">
                            <span className={`px-2 py-1 rounded-full ${machine.status === 'production' ? 'bg-green-900 text-green-300' :
                              machine.status === 'idle' ? 'bg-amber-900 text-amber-300' :
                                machine.status === 'fault' ? 'bg-red-900 text-red-300' :
                                  machine.status === 'adjusting' ? 'bg-blue-900 text-blue-300' :
                                    'bg-gray-700 text-gray-300'
                              }`}>
                              {machine.status === 'production' ? '生产中' :
                                machine.status === 'idle' ? '空闲' :
                                  machine.status === 'fault' ? '故障' :
                                    machine.status === 'adjusting' ? '调机' :
                                      machine.status === 'shutdown' ? '关机' :
                                        machine.status === 'disconnected' ? '未连接' :
                                          machine.status === 'maintenance' ? '保养' :
                                            machine.status === 'debugging' ? '调试' :
                                              machine.status}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* 产品列表 */}
                      <div className="p-3">
                        <div className="grid grid-cols-1 gap-3">
                          {filteredRecords.map(record => {
                            const product = getProductById(record.productId);
                            if (!product) return null;

                            const progress = calculateProgress(record.quantity, record.plan);

                            return (
                              <div key={record.id} className="bg-gray-900 p-3 rounded-md">
                                <div className="flex justify-between items-start mb-2">
                                  <div>
                                    <h4 className="font-medium">{product.name}</h4>
                                    <div className="text-sm text-gray-400">
                                      <span className="mr-4">产品编号: {product.code}</span>
                                      <span className="mr-4">工序: {product.processName} ({product.processCode})</span>
                                      <span>程序: {product.program}</span>
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-sm">
                                      <span className="text-gray-400">开始时间: </span>
                                      <span>{new Date(record.startTime).toLocaleTimeString()}</span>
                                    </div>
                                    {record.endTime && (
                                      <div className="text-sm">
                                        <span className="text-gray-400">结束时间: </span>
                                        <span>{new Date(record.endTime).toLocaleTimeString()}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>

                                {/* 产量和进度条 */}
                                <div className="mt-2">
                                  <div className="flex justify-between items-center mb-1">
                                    <span className="text-sm">
                                      产量: <span className="text-green-400">{record.quantity}</span> /
                                      <span className="text-blue-400">{record.plan}</span>
                                    </span>
                                    <span className="text-sm text-gray-400">{progress}%</span>
                                  </div>
                                  <div className="w-full bg-gray-700 rounded-full h-2">
                                    <div
                                      className="bg-green-500 h-2 rounded-full"
                                      style={{ width: `${progress}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  );
                }).filter(Boolean) // 过滤掉null值
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
