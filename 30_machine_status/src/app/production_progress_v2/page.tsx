'use client';

/**
 * 生产进度页面 V2
 *
 * 显示设备生产进度，支持设备数据向上连续滚动
 * 时间轴固定在顶部，不随内容滚动
 * 支持通过URL参数设置滚动速度
 */
import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';
import { useMaximize } from '@/hooks/useMaximize';
import Sidebar from '@/components/layout/Sidebar';
import { useSidebar } from '@/hooks/useSidebar';
import Header from '@/components/layout/Header';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorAlert from '@/components/common/ErrorAlert';
import TimeAxis from '@/components/production/TimeAxis';
import DeviceRow from '@/components/production/DeviceRow';
import InfiniteScroll from '@/components/common/InfiniteScroll';

// 产品颜色数组
const PRODUCT_COLORS = [
  { bg: 'rgb(59, 130, 246)', lightBg: 'rgb(191, 219, 254)' }, // 蓝色
  { bg: 'rgb(16, 185, 129)', lightBg: 'rgb(167, 243, 208)' }, // 绿色
  // { bg: 'rgb(249, 115, 22)', lightBg: 'rgb(254, 215, 170)' }, // 橙色
  // { bg: 'rgb(236, 72, 153)', lightBg: 'rgb(251, 207, 232)' }, // 粉色
  // { bg: 'rgb(139, 92, 246)', lightBg: 'rgb(221, 214, 254)' }, // 紫色
  // { bg: 'rgb(234, 179, 8)', lightBg: 'rgb(254, 240, 138)' },  // 黄色
];

// 状态颜色映射
const STATUS_COLORS = {
  production: 'rgb(22, 163, 74)',   // 生产中 - 绿色
  idle: 'rgb(245, 158, 11)',         // 空闲 - 琥珀色
  fault: 'rgb(220, 38, 38)',         // 故障 - 红色
  adjusting: 'rgb(59, 130, 246)',    // 调机 - 蓝色
  shutdown: 'rgb(75, 85, 99)',       // 关机 - 灰色
  disconnected: 'rgb(107, 114, 128)', // 未连接 - 灰色
  maintenance: 'rgb(124, 58, 237)',  // 保养 - 紫色
  debugging: 'rgb(236, 72, 153)',    // 调试 - 粉色
};

// 状态名称映射
const STATUS_NAMES = {
  production: '生产中',
  idle: '空闲',
  fault: '故障',
  adjusting: '调机',
  shutdown: '关机',
  disconnected: '未连接',
  maintenance: '保养',
  debugging: '调试',
};

export default function ProductionProgressV2Page() {
  // 获取URL参数
  const searchParams = useSearchParams();
  const scrollSpeedParam = searchParams?.get('scrollSpeed');
  const smoothScrollParam = searchParams?.get('smoothScroll');


  // 侧边栏和最大化状态
  const { isMaximized, toggleMaximize } = useMaximize();
  const { sidebarMode, toggleSidebar } = useSidebar();
  // 状态管理
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progressData, setProgressData] = useState<any[]>([]);
  const [companyInfo, setCompanyInfo] = useState<any>({
    name: '江苏恒力阀门有限公司',
    logo: '/logo.png',
    supportContact: '技术支持: 400-123-4567',
    dateTime: ''
  });
  const [deviceStatusHistory, setDeviceStatusHistory] = useState<Record<string, any[]>>({});
  const [scrollSpeed, setScrollSpeed] = useState(30); // 默认滚动速度（像素/秒）
  const [smoothScroll, setSmoothScroll] = useState(true); // 默认启用平滑滚动
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null); // 最后更新时间
  const [isRefreshing, setIsRefreshing] = useState(false); // 是否正在刷新
  const [hasApiError, setHasApiError] = useState(false); // API请求是否出错
  const [selectedDeviceIds, setSelectedDeviceIds] = useState<string[]>([]); // 选中的设备ID

  // 处理URL参数
  useEffect(() => {
    // 处理滚动速度参数
    if (scrollSpeedParam) {
      const speed = parseInt(scrollSpeedParam, 10);
      if (!isNaN(speed) && speed > 0) {
        setScrollSpeed(speed);
      }
    }

    // 处理平滑滚动参数
    if (smoothScrollParam !== null && smoothScrollParam !== undefined) {
      const smooth = smoothScrollParam.toLowerCase();
      if (smooth === 'false' || smooth === '0' || smooth === 'off') {
        setSmoothScroll(false);
      } else if (smooth === 'true' || smooth === '1' || smooth === 'on') {
        setSmoothScroll(true);
      }
    }
  }, [scrollSpeedParam, smoothScrollParam]);


  // 加载设备状态历史数据 - 一次性获取所有设备数据
  const loadDeviceStatusHistory = useCallback(async (deviceIds: string[] = [], silent: boolean = false) => {
    try {
      // 格式化日期为 YYYY-MM-DD
      const formattedDate = format(selectedDate, 'yyyy-MM-dd');

      // 构建请求URL，如果有指定设备则只获取这些设备的数据
      let url = `/api/devices/status-history?date=${formattedDate}`;
      if (deviceIds.length > 0) {
        url += `&devices=${deviceIds.join(',')}`;
      }

      console.log('加载设备状态历史数据，URL:', url);
      console.log('设备ID列表:', deviceIds);

      const response = await fetch(url);

      if (response.ok) {
        const data = await response.json();
        // 预期API返回格式: { statusHistory: { deviceId1: [...records], deviceId2: [...records] } }
        if (!silent) {
          console.log('设备状态历史数据:', data.statusHistory);
        }

        // 确保每个设备都有状态历史数据
        if (data.statusHistory && deviceIds.length > 0) {
          const updatedStatusHistory = { ...data.statusHistory };

          // 检查每个设备是否有状态历史数据
          deviceIds.forEach(deviceId => {
            if (!updatedStatusHistory[deviceId]) {
              if (!silent) {
                console.log(`设备 ${deviceId} 没有状态历史数据，创建空数组`);
              }
              updatedStatusHistory[deviceId] = [];
            }
          });

          setDeviceStatusHistory(updatedStatusHistory);
        } else {
          setDeviceStatusHistory(data.statusHistory || {});
        }
      } else {
        console.error('获取设备状态历史失败:', response.status);
        setDeviceStatusHistory({});
        // 设置API错误状态为true
        setHasApiError(true);
      }
    } catch (error) {
      console.error('获取设备状态历史失败:', error);
      setDeviceStatusHistory({});
      // 设置API错误状态为true
      setHasApiError(true);
    }
  }, [selectedDate]);

  // 加载生产进度数据
  const loadProductionProgress = useCallback(async (silent: boolean = false) => {
    if (!silent) {
      setIsLoading(true);
    }
    setError(null);

    try {
      // 标记开始刷新
      if (silent) {
        setIsRefreshing(true);
      }

      // 格式化日期为 YYYY-MM-DD
      const formattedDate = format(selectedDate, 'yyyy-MM-dd');

      // 构建请求URL，包含设备筛选
      let url = `/api/production/progress?date=${formattedDate}`;
      if (selectedDeviceIds.length > 0) {
        url += `&devices=${selectedDeviceIds.join(',')}`;
      }

      console.log('发送API请求:', url);
      console.log('选中的设备IDs:', selectedDeviceIds);

      const response = await fetch(url);
      if (!response.ok) {
        console.error('API请求失败:', response.status, response.statusText);
        // 设置API错误状态为true
        setHasApiError(true);
        throw new Error(`获取生产进度数据失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('API响应数据:', data);

      // API请求成功，设置错误状态为false
      setHasApiError(false);

      // 检查响应数据是否有效
      if (!data || !data.progressData) {
        console.warn('API返回的数据无效或为空');
        setProgressData([]);
      } else {
        // 只在调试模式下输出日志，减少控制台输出
        if (!silent) {
          console.log('生产进度数据:', data.progressData);
        }

        console.log('设置 progressData:', data.progressData);
        setProgressData(data.progressData || []);

        // 如果API返回了公司信息，则更新公司信息
        if (data.companyInfo && data.companyInfo.name) {
          setCompanyInfo((prevInfo: any) => ({
            ...prevInfo,
            name: data.companyInfo.name,
            logo: data.companyInfo.logo || prevInfo.logo,
            supportContact: data.companyInfo.supportContact || prevInfo.supportContact
          }));
        }
      }

      // 从生产进度数据中提取设备ID
      const deviceIds = data.progressData?.map((item: any) => item.deviceId) || [];

      if (!silent) {
        console.log('从生产进度数据中提取的设备ID:', deviceIds);
      }

      // 加载设备状态历史数据 - 使用从生产进度数据中提取的设备ID
      if (deviceIds.length > 0) {
        await loadDeviceStatusHistory(deviceIds, silent);
      } else {
        // 如果没有设备ID，则清空设备状态历史数据
        setDeviceStatusHistory({});
      }

      // 更新最后刷新时间
      setLastRefreshTime(new Date());
    } catch (error: any) {
      console.error('获取生产进度数据失败:', error);
      // 设置API错误状态为true
      setHasApiError(true);
      if (!silent) {
        setError(error.message || '获取生产进度数据失败');
      }
      // 即使出错也更新最后刷新时间，以便显示橙色时间戳
      setLastRefreshTime(new Date());
    } finally {
      if (!silent) {
        setIsLoading(false);
      }
      // 标记结束刷新
      if (silent) {
        setIsRefreshing(false);
      }
    }
  }, [selectedDate, selectedDeviceIds, loadDeviceStatusHistory]);

  // 初始加载和日期/设备选择变更时加载数据
  useEffect(() => {
    console.log('加载生产进度数据，设备选择:', selectedDeviceIds);
    loadProductionProgress();
  }, [loadProductionProgress, selectedDeviceIds]);

  // 添加调试日志
  useEffect(() => {
    console.log('progressData 更新:', progressData);
  }, [progressData]);

  // 自动刷新数据 - 每秒无感刷新
  useEffect(() => {
    // 只有在显示当前日期的数据时才自动刷新
    const isCurrentDate = format(selectedDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');

    if (!isCurrentDate) {
      return; // 如果不是当前日期，不自动刷新
    }

    // 创建自动刷新定时器
    const refreshTimer = setInterval(() => {
      // 无感刷新数据（静默模式）
      loadProductionProgress(true);
    }, 1000); // 每秒刷新一次

    // 清理函数
    return () => {
      clearInterval(refreshTimer);
    };
  }, [loadProductionProgress, selectedDate]);

  // 在客户端更新时间
  useEffect(() => {
    // 更新当前时间
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      setCompanyInfo((prev: any) => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    // 立即更新一次
    updateTime();

    // 每秒更新一次
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 获取产品背景色
  const getProductBackgroundColor = (index: number) => {
    return PRODUCT_COLORS[index % PRODUCT_COLORS.length].bg;
  };

  // 获取产品浅色背景
  const getProductLightBackgroundColor = (index: number) => {
    return PRODUCT_COLORS[index % PRODUCT_COLORS.length].lightBg;
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'rgb(75, 85, 99)';
  };

  // 获取状态名称
  const getStatusName = (status: string) => {
    return STATUS_NAMES[status as keyof typeof STATUS_NAMES] || status;
  };

  // 创建renderTimeGrid函数
  const createRenderTimeGrid = (startHour: number) => {
    return (zIndex: number) => (
      <div className="absolute inset-0 right-4 pointer-events-none">
        <div className="h-full w-full grid" style={{ gridTemplateColumns: `repeat(24, 1fr)`, zIndex }}>
          {Array.from({ length: 24 }).map((_, index) => {
            const hour = (startHour + index) % 24;
            const isMainHour = hour % 4 === 0;
            return (
              <div
                key={index}
                style={{
                  borderRight: isMainHour ? '1px solid rgba(156, 163, 175, 0.5)' : '1px solid rgba(156, 163, 175, 0.3)',
                  height: '100%'
                }}
              ></div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-screen bg-gray-950 text-white">
      {/* 侧边栏 */}
      <Sidebar mode={sidebarMode} />

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部导航栏 */}
        <Header
          companyInfo={companyInfo}
          toggleSidebar={toggleSidebar}
          toggleMaximize={toggleMaximize}
          isMaximized={isMaximized}
        />

        {/* 内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 页面标题和状态颜色标识 */}
          <div className="p-4 pb-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <h1 className="text-xl font-bold">生产进度</h1>
                {lastRefreshTime && (
                  <span className={`ml-2 text-xs flex items-center ${hasApiError ? 'text-amber-500' : 'text-gray-400'}`}>
                    最后更新: <span className="font-mono w-[4.5rem] inline-block text-right">{format(lastRefreshTime, 'HH:mm:ss')}</span>
                    {isRefreshing && (
                      <span
                        className={`ml-1 inline-block w-2 h-2 rounded-full animate-pulse ${hasApiError ? 'bg-amber-500' : 'bg-green-500'}`}
                        title={hasApiError ? "数据刷新失败" : "数据刷新中"}
                      ></span>
                    )}
                  </span>
                )}
              </div>

              {/* 设备状态颜色标识 - 右对齐 */}
              <div className="flex items-center space-x-4 text-xs">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-600 mr-1"></div>
                  <span>生产中</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-amber-500 mr-1"></div>
                  <span>空闲</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-red-600 mr-1"></div>
                  <span>故障</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
                  <span>调机</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-gray-500 mr-1"></div>
                  <span>关机</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-1"></div>
                  <span>保养</span>
                </div>
              </div>
            </div>
          </div>

          {/* 内容主体 */}
          {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <LoadingSpinner message="加载生产进度数据中..." />
            </div>
          ) : error ? (
            <div className="flex-1 flex items-center justify-center">
              <ErrorAlert message={error} onRetry={loadProductionProgress} />
            </div>
          ) : (
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* 时间轴 - 固定在顶部 */}
              <TimeAxis startHour={8} hoursCount={24} />

              {/* 设备列表 - 使用新的无缝滚动组件 */}
              <InfiniteScroll
                scrollSpeed={scrollSpeed}
                pauseOnHover={true}
                repeatCount={5} // 增加重复次数，确保有足够的内容进行滚动
                className="flex-1"
              >
                <div className="device-rows" style={{ position: 'relative', minHeight: '100px', width: '100%' }}>
                  {/* 添加调试信息 */}
                  {progressData.length === 0 && (
                    <div className="p-4 text-center text-gray-400">
                      没有设备数据可显示
                    </div>
                  )}
                  {progressData.map((device) => (
                    <DeviceRow
                      key={device.deviceId}
                      device={device}
                      statusHistory={deviceStatusHistory}
                      selectedDate={selectedDate}
                      startHour={8}
                      getProductBackgroundColor={getProductBackgroundColor}
                      getProductLightBackgroundColor={getProductLightBackgroundColor}
                      getStatusColor={getStatusColor}
                      getStatusName={getStatusName}
                      renderTimeGrid={createRenderTimeGrid(8)}
                    />
                  ))}
                </div>
              </InfiniteScroll>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
