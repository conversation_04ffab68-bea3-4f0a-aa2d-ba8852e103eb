'use client'; // 声明这是一个客户端组件

import { getApiBaseUrl } from '@/config/api';

/**
 * 设备状态页面组件
 *
 * 该组件负责：
 * 1. 显示设备状态监控界面
 * 2. 模拟设备状态实时变化
 * 3. 显示设备状态统计和网格视图
 * 4. 支持自动刷新和手动刷新
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import Header from '@/components/layout/Header'; // 顶部标题栏组件
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar'; // 侧边导航栏组件和模式枚举
import StatusSummary from '@/components/features/StatusSummary'; // 状态摘要组件
import MachineGrid from '@/components/layout/MachineGrid'; // 设备网格组件
import RefreshIndicator from '@/components/features/RefreshIndicator'; // 刷新指示器组件
import { Machine, StatusCount, CompanyInfo, MachineStatus } from '@/types'; // 类型定义
import { calculateStatusCounts } from '@/utils/status'; // 工具函数
import { getRefreshSettings, getDisplaySettings, DisplaySettings } from '@/services/settingsService'; // 设置服务
import { deviceAPI } from '@/services/apiService'; // 统一API服务

/**
 * 设备状态页面组件
 *
 * 该组件是设备状态监控页面，负责：
 * 1. 显示设备状态监控界面
 * 2. 模拟设备状态实时变化
 * 3. 显示设备状态统计和网格视图
 * 4. 支持自动刷新和手动刷新
 */
export default function StatusPage() {
  console.log('📊 设备状态页面组件开始渲染, 环境:', typeof window !== 'undefined' ? '客户端' : '服务端');

  // ===== 状态管理 =====

  /**
   * 侧边栏状态
   * 控制侧边导航栏的显示模式
   */
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);

  /**
   * 最大化状态
   * 控制页面是否处于最大化状态
   */
  const [isMaximized, setIsMaximized] = useState(false);

  /**
   * 数据状态
   * 存储和管理应用程序的核心数据
   */
  // 公司信息（名称和日期时间）
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '',
    dateTime: '',
    support: '',
    support_info: ''
  });
  // 设备数据数组
  const [machines, setMachines] = useState<Machine[]>([]);
  // 各状态的设备计数
  const [statusCounts, setStatusCounts] = useState<StatusCount>({
    production: 0,   // 生产中
    idle: 0,         // 空闲
    fault: 0,        // 故障
    adjusting: 0,    // 调机
    shutdown: 0,     // 关机
    disconnected: 0, // 未连接
    maintenance: 0,  // 保养
    debugging: 0     // 调试
  });

  /**
   * 刷新状态
   * 控制数据刷新的相关状态
   */
  const [isLoading, setIsLoading] = useState(false);           // 是否正在加载数据
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null); // 上次更新时间
  const [autoRefresh, setAutoRefresh] = useState(true);        // 是否自动刷新
  const [refreshInterval, setRefreshInterval] = useState(5000); // 刷新间隔（毫秒）
  const [updateCount, setUpdateCount] = useState(0);           // 更新计数器
  const [showRefreshIndicator, setShowRefreshIndicator] = useState(true); // 是否显示刷新指示器
  const [apiError, setApiError] = useState(false);             // API请求是否失败
  const [settingsLoaded, setSettingsLoaded] = useState(false); // Settings 是否已加载完成

  /**
   * 显示设置
   * 控制设备网格的显示样式
   */
  const [displaySettings, setDisplaySettings] = useState<DisplaySettings>({
    deviceGridGap: 12,
    deviceRowGap: 12,
    deviceColumnGap: 12,
    statusDeviceGap: 12,
    statusCountDigits: 2,
    statusCountPadChar: "0",
    filterSignalDuration: 1, // 默认过滤1秒以下的信号
    cardContent: {
      showCode: true,
      showName: true,
      showLocation: true,
      showModel: true,
      showQuantity: true,
      showPlan: true,
      fontSize: 14,
      textAlign: "center",
      layoutRatio: 60
    },
    cardStyle: {
      cardWidth: 200,
      cardHeight: 120,
      borderRadius: 4,
      borderWidth: 2,
      contentPadding: 8
    },
    statusColors: {
      production: "#22c55e",
      idle: "#f59e0b",
      fault: "#ef4444",
      adjusting: "#3b82f6",
      shutdown: "#6b7280",
      disconnected: "#9ca3af",
      maintenance: "#8b5cf6",
      debugging: "#06b6d4"
    },
    statusHistory: {
      timeSlotWidth: 120,
      timelineHeight: 40,
      timelineGap: 8
    }
  });

  /**
   * Refs
   * 用于存储不触发重新渲染的数据
   */
  // 存储原始设备数据，避免不必要的重新渲染
  const machinesRef = useRef<Machine[]>([]);
  // 跟踪上次更新时间戳，确保更新间隔
  const lastUpdateTimeRef = useRef<number>(0);

  // 在客户端初始化时从localStorage加载侧边栏状态
  useEffect(() => {
    console.log('🔧 开始加载侧边栏状态');
    // 导入是动态的，因为这些函数使用了浏览器API
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        console.log('✅ 加载侧边栏状态成功:', mode);
        setSidebarMode(mode);
      } catch (error) {
        console.error('❌ 加载侧边栏状态失败:', error);
      }
    };

    // 立即执行
    loadSidebarMode();
  }, []);

  /**
   * 从API获取设置
   */
  useEffect(() => {
    console.log('🔧 开始获取设置');
    const fetchSettings = async () => {
      try {
        // 获取刷新设置，使用默认值作为回退
        try {
          const refreshSettings = await getRefreshSettings();
          setAutoRefresh(refreshSettings.autoRefresh);
          setRefreshInterval(refreshSettings.refreshInterval);
          setShowRefreshIndicator(refreshSettings.showRefreshIndicator);
          console.log('✅ 刷新设置加载成功:', refreshSettings);
        } catch (refreshError) {
          console.warn('⚠️ 刷新设置加载失败，使用默认值:', refreshError);
          // 使用默认的刷新设置
          setAutoRefresh(true);
          setRefreshInterval(5000); // 5秒，与后端缓存时间一致
          setShowRefreshIndicator(true);
        }

        // 获取显示设置，使用默认值作为回退
        try {
          const displayData = await getDisplaySettings();
          setDisplaySettings(displayData);
          console.log('✅ 显示设置加载成功:', displayData);
        } catch (displayError) {
          console.warn('⚠️ 显示设置加载失败，使用默认值:', displayError);
          // 保持现有的默认显示设置，不需要额外操作
        }
      } catch (error) {
        console.error('❌ 设置加载过程中发生未知错误:', error);
        // 确保即使设置加载失败，组件也能正常工作
        setAutoRefresh(true);
        setRefreshInterval(5000);
        setShowRefreshIndicator(true);
      } finally {
        // 无论成功失败，都标记 settings 已加载完成
        setSettingsLoaded(true);
        console.log('✅ Settings 加载流程完成');
      }
    };

    fetchSettings();
  }, []);

  /**
   * 切换最大化状态
   * 当点击最大化按钮时调用
   * 同时控制浏览器全屏模式
   */
  const toggleMaximize = useCallback(() => {
    // 切换最大化状态
    setIsMaximized(prev => {
      const newState = !prev;

      // 根据新状态切换全屏模式
      if (newState) {
        // 进入全屏模式
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        // 退出全屏模式
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }

      return newState;
    });
  }, []);

  // 监听全屏变化事件，同步状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      // 如果浏览器退出了全屏模式，同步更新我们的状态
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };

    // 添加全屏变化事件监听
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    // 清理函数
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  /**
   * 切换侧边栏显示模式
   * 在两种模式之间切换：隐藏 <-> 折叠
   * 并保存到localStorage
   */
  const toggleSidebar = useCallback(async () => {
    try {
      // 导入工具函数
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');

      // 获取下一个模式
      const nextMode = getNextSidebarMode(sidebarMode);
      console.log('切换侧边栏状态:', sidebarMode, '->', nextMode);

      // 保存到localStorage和全局变量
      saveSidebarMode(nextMode);

      // 更新组件状态
      setSidebarMode(nextMode);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  /**
   * 智能更新设备数据
   *
   * 该函数只更新发生变化的设备状态，而不是整个列表
   * 这样可以实现平滑的视觉过渡，避免整个列表重新渲染
   *
   * @param newMachines - 新的设备数据数组
   */
  const updateMachines = useCallback((newMachines: Machine[]) => {
    // 使用 ref 获取当前设备列表，避免依赖 machines 状态
    const currentMachines = machinesRef.current;

    // 如果是初始加载，直接设置整个列表
    if (currentMachines.length === 0) {
      setMachines(newMachines);
      machinesRef.current = newMachines;
      return;
    }

    // 有设备状态变化时，进行平滑更新
    const updatedMachines = [...currentMachines]; // 创建当前设备列表的副本
    let hasChanges = false; // 跟踪是否有任何变化

    // 遍历新设备数据，查找状态或数量变化
    newMachines.forEach((newMachine, index) => {
      // 检查索引是否有效且状态或数量是否变化
      if (index < updatedMachines.length &&
        (updatedMachines[index].status !== newMachine.status ||
          updatedMachines[index].quantity !== newMachine.quantity)) {
        // 更新状态和数量属性，保留其他属性不变
        updatedMachines[index] = {
          ...updatedMachines[index],
          status: newMachine.status,
          quantity: newMachine.quantity
        };
        hasChanges = true; // 标记有变化
      }
    });

    // 只有在有变化时才更新状态
    if (hasChanges) {
      // 更新ref中存储的原始数据
      machinesRef.current = newMachines;
      // 更新React状态，触发重新渲染
      setMachines(updatedMachines);

      // 重新计算并更新状态计数
      setStatusCounts(calculateStatusCounts(updatedMachines));
    }
  }, []); // 空依赖项：避免死循环，使用 ref 来访问最新状态

  /**
   * 获取站点信息
   * 从API获取站点基本信息（名称、技术支持等）
   */
  const fetchSiteInfo = useCallback(async () => {
    try {
      console.log('🌐 开始获取站点信息...');
      const API_HOST = getApiBaseUrl();
      const response = await fetch(`${API_HOST}/api/v3/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`站点信息API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 站点信息API响应:', result);

      if (result.success && result.data) {
        // 更新公司信息，保留现有的dateTime
        setCompanyInfo(prev => ({
          ...prev,
          name: result.data.name || '智能制造系统',
          support: result.data.support || '',
          support_info: result.data.support_info || ''
        }));
        console.log('✅ 站点信息更新成功:', {
          name: result.data.name,
          support: result.data.support,
          support_info: result.data.support_info
        });
      } else {
        console.warn('⚠️ 站点信息API返回数据格式异常:', result);
      }
    } catch (error) {
      console.error('❌ 获取站点信息失败:', error);
      // 设置默认值
      setCompanyInfo(prev => ({
        ...prev,
        name: '智能制造系统',
        support: '',
        support_info: ''
      }));
    }
  }, []);

  /**
   * 从API加载数据并模拟随机状态变化
   *
   * 该函数负责：
   * 1. 首次加载时从API获取初始数据
   * 2. 后续更新时模拟随机设备状态变化
   * 3. 控制更新频率和加载状态
   */
  const fetchMachinesData = useCallback(async () => {
    // 防止重复加载：如果已经在加载中，直接返回
    if (isLoading) return;

    // 控制更新频率：确保至少经过设定的间隔时间后才更新数据
    const now = Date.now();
    if (now - lastUpdateTimeRef.current < refreshInterval) {
      return;
    }

    // 开始加载，设置加载状态
    setIsLoading(true);
    try {
      // 使用统一API服务获取设备数据
      const data = await deviceAPI.getMachines();

      // API请求成功，重置错误状态
      setApiError(false);

      // 首次加载：直接使用API返回的数据
      if (machinesRef.current.length === 0) {
        // 设置设备数据
        console.log('首次加载，设置设备数据');
        updateMachines(data.machines);
        // 设置状态计数
        setStatusCounts(data.statusCounts);

        // 设置公司信息 - 只更新dateTime，保留从站点信息API获取的其他字段
        setCompanyInfo(prev => ({
          ...prev,
          dateTime: data.companyInfo?.dateTime || prev.dateTime
        }));
      } else {
        // 后续更新：使用API返回的真实数据
        updateMachines(data.machines);
        setStatusCounts(data.statusCounts);
        // 只更新dateTime，保留从站点信息API获取的其他字段
        setCompanyInfo(prev => ({
          ...prev,
          dateTime: data.companyInfo?.dateTime || prev.dateTime
        }));
      }

      // 更新刷新相关状态
      const currentTime = new Date(); // 当前时间
      setLastUpdated(currentTime); // 设置上次更新时间
      lastUpdateTimeRef.current = now; // 更新时间戳引用
      setUpdateCount(prev => prev + 1); // 增加更新计数

      // 记录更新日志
      console.log(`数据已更新(${updateCount}): ${currentTime.toLocaleTimeString()}.${currentTime.getMilliseconds()}`);
    } catch (error) {
      // 详细的错误处理
      console.error('❌ 获取机器数据失败:', error);

      // 设置API错误状态为true
      setApiError(true);

      // 如果是首次加载失败，设置一些默认数据以防止页面完全空白
      if (machinesRef.current.length === 0) {
        console.warn('⚠️ 首次加载失败，设置默认公司信息');
        setCompanyInfo({
          name: '智能制造监控系统',
          dateTime: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }).replace(/\//g, '.').replace(',', '')
        });
      }
    } finally {
      // 无论成功失败，都结束加载状态
      setIsLoading(false);
    }
  }, [refreshInterval]); // 依赖项（移除 isLoading 避免死循环）

  /**
   * 手动刷新数据
   *
   * 用于响应用户点击刷新按钮的操作
   * 重置上次更新时间，强制立即刷新数据
   */
  const handleRefresh = useCallback(() => {
    // 重置上次更新时间戳为0，绕过时间间隔检查
    lastUpdateTimeRef.current = 0;
    // 调用数据加载函数
    fetchMachinesData();
  }, []); // 空依赖项：避免死循环

  /**
   * 初始加载数据
   *
   * 组件首次渲染时加载初始数据
   * 等待 settings 加载完成后再开始数据加载，确保界面使用正确的设置
   */
  useEffect(() => {
    // 只有在 settings 加载完成后才开始数据加载
    if (settingsLoaded) {
      console.log('🔧 Settings 已加载完成，开始初始数据加载');
      fetchSiteInfo(); // 获取站点信息
      fetchMachinesData(); // 调用数据加载函数
    }
  }, [settingsLoaded, fetchSiteInfo]); // 依赖项：settings 加载状态和站点信息获取函数

  // 创建 fetchMachinesData 的 ref，避免闭包问题
  const fetchMachinesDataRef = useRef(fetchMachinesData);
  fetchMachinesDataRef.current = fetchMachinesData;

  /**
   * 自动刷新数据
   *
   * 根据autoRefresh状态定期刷新数据
   * 严格控制为每秒更新一次
   * 等待 settings 加载完成后再开始自动刷新
   */
  useEffect(() => {
    // 如果 settings 还没加载完成，不设置定时器
    if (!settingsLoaded) return;

    // 如果自动刷新关闭，不设置定时器
    if (!autoRefresh) return;

    console.log('🔧 启动自动刷新定时器，间隔:', refreshInterval, 'ms');

    // 设置定时器，按照refreshInterval间隔刷新数据
    const intervalId = setInterval(() => {
      // 使用 ref 调用最新的函数，避免闭包问题
      fetchMachinesDataRef.current();
    }, refreshInterval);

    // 清理函数：组件卸载或依赖项变化时清除定时器
    return () => {
      console.log('🔧 清除自动刷新定时器');
      clearInterval(intervalId);
    };
  }, [settingsLoaded, autoRefresh, refreshInterval]); // 依赖项：settings 加载状态、自动刷新状态、刷新间隔

  /**
   * 更新当前时间
   *
   * 每秒更新一次界面上显示的当前时间
   */
  useEffect(() => {
    /**
     * 格式化并更新当前时间
     * 将当前时间格式化为"YYYY.MM.DD HH:MM:SS"格式
     */
    const updateTime = () => {
      const now = new Date();
      // 格式化日期部分：YYYY.MM.DD
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      // 格式化时间部分：HH:MM:SS
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      // 更新公司信息中的日期时间，保留其他属性不变
      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    // 设置定时器，每秒更新一次时间
    const timer = setInterval(updateTime, 1000);
    // 清理函数：组件卸载时清除定时器
    return () => clearInterval(timer);
  }, []); // 空依赖数组：只在组件挂载时运行一次

  /**
   * 渲染设备状态页面组件
   *
   * 包含以下主要部分：
   * 1. 侧边导航栏
   * 2. 主内容区域（标题栏、控制栏、状态摘要、设备网格）
   */

  // 如果 settings 还没有加载完成，显示加载界面
  if (!settingsLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-lg">正在加载系统设置...</p>
          <p className="text-sm text-gray-400 mt-2">请稍候，系统正在应用您的个性化设置</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex">
      {/* 侧边导航栏 - 在非最大化状态下显示 */}
      {!isMaximized && <Sidebar mode={sidebarMode} activePage="status" />}

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-h-screen">
        {/* 顶部标题栏 - 传递最大化状态和切换函数 */}
        <Header
          companyInfo={companyInfo}
          isMaximized={isMaximized}
          toggleMaximize={toggleMaximize}
          toggleSidebar={toggleSidebar}
        />

        {/* 控制栏：包含标题和刷新控制 - 根据设置决定是否显示 */}
        {showRefreshIndicator && (
          <div className="bg-blue-900/20 py-2 px-6 flex justify-between items-center">
            {/* 页面标题 */}
            <h2 className="text-xl font-bold text-white">设备实时状态</h2>

            {/* 刷新控制区域 */}
            <div className="flex items-center">
              {/* 自动刷新开关 */}
              <div className="mr-4">
                <label className="inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={() => setAutoRefresh(!autoRefresh)}
                    className="sr-only peer"
                  />
                  {/* 开关样式 */}
                  <div className="relative w-11 h-6 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                  <span className="ml-2 text-sm font-medium text-gray-300">自动刷新</span>
                </label>
              </div>

              {/* 更新计数器 */}
              <div className="mr-4 text-sm text-gray-300" hidden>
                <span>更新次数: </span>
                <span className="font-bold">{updateCount}</span>
              </div>

              {/* 刷新指示器组件 */}
              <RefreshIndicator
                lastUpdated={lastUpdated} // 上次更新时间
                isLoading={isLoading}     // 是否正在加载
                onRefresh={handleRefresh}  // 刷新处理函数
              />
            </div>
          </div>
        )}

        {/* 状态摘要区域 */}
        <div className="px-6 pt-3 pb-0">
          {apiError && (
            <div className="bg-red-900/20 text-red-300 px-4 py-2 mb-3 rounded-md flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span>无法连接到服务器，设备状态可能不是最新的</span>
            </div>
          )}
          <StatusSummary
            counts={statusCounts}
            digitsCount={displaySettings.statusCountDigits}
            padChar={displaySettings.statusCountPadChar}
          />
        </div>

        {/* 设备网格区域 - 可滚动 - 应用状态栏与设备列表间隔 */}
        <div
          className="flex-1 px-6 pb-6 overflow-auto"
          style={{ paddingTop: `${displaySettings.statusDeviceGap}px` }}
        >
          <MachineGrid
            machines={machines}
            gridGap={displaySettings.deviceGridGap}
            rowGap={displaySettings.deviceRowGap}
            columnGap={displaySettings.deviceColumnGap}
            apiError={apiError}
            displaySettings={displaySettings}
          />
        </div>
      </main>
    </div>
  );
}
