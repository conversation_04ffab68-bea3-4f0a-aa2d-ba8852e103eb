'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { ArrowLeft, Download, Filter, Clock, BarChart3, Settings, ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import Header from '@/components/layout/Header';
import * as XLSX from 'xlsx';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { getApiBaseUrl } from '@/config/api';

/**
 * 设备状态详细页面 - V2版本
 * 
 * V2版本特点：
 * - 前端不进行任何数据处理或计算
 * - 所有数据由后端API预处理并格式化
 * - 前端只负责调用API和渲染数据
 * - 保持与V1版本相同的UI界面和功能
 * 
 * 功能：
 * - 显示单个设备的详细状态信息
 * - 设备基本信息和配置
 * - 利用率统计图表
 * - 详细状态变化记录表格
 * - 状态筛选和数据导出
 */

/**
 * V2版本的状态记录接口 - 从API直接获取格式化数据
 */
interface StatusRecordV2 {
  id: number;
  status: string;
  statusName: string;           // 中文状态名称
  statusColor: string;          // 状态颜色
  startTime: string;            // ISO格式原始时间
  endTime: string;              // ISO格式原始时间
  startTimeFormatted: string;   // 格式化的开始时间
  endTimeFormatted: string;     // 格式化的结束时间
  duration: number;             // 持续时间（秒）
  durationFormatted: string;    // 格式化的持续时间
  productionCumulative: number;
  productionCumulativeFormatted: string;
  idleCumulative: number;
  idleCumulativeFormatted: string;
  faultCumulative: number;
  faultCumulativeFormatted: string;
  maintenanceCumulative: number;
  maintenanceCumulativeFormatted: string;
  shutdownCumulative: number;
  shutdownCumulativeFormatted: string;
}

/**
 * V2版本的利用率统计接口 - 从API直接获取格式化数据
 */
interface UtilizationStatsV2 {
  totalTime: number;
  totalTimeFormatted: string;
  productionTime: number;
  productionTimeFormatted: string;
  productionPercentage: number;
  productionPercentageFormatted: string;
  idleTime: number;
  idleTimeFormatted: string;
  idlePercentage: number;
  idlePercentageFormatted: string;
  faultTime: number;
  faultTimeFormatted: string;
  faultPercentage: number;
  faultPercentageFormatted: string;
  maintenanceTime: number;
  maintenanceTimeFormatted: string;
  maintenancePercentage: number;
  maintenancePercentageFormatted: string;
  shutdownTime: number;
  shutdownTimeFormatted: string;
  shutdownPercentage: number;
  shutdownPercentageFormatted: string;
  utilizationRate: number;
  utilizationRateFormatted: string;
  // 状态分布数据（用于渲染进度条）
  statusDistribution: Array<{
    status: string;
    statusName: string;
    color: string;
    percentage: number;
    percentageFormatted: string;
    time: number;
    timeFormatted: string;
  }>;
}

/**
 * V2版本的设备信息接口 - 从API直接获取格式化数据
 */
interface DeviceInfoV2 {
  id: string;
  name: string;
  model: string;
  location: string;
  dayStartTime: string;
  dayStartTimeFormatted: string;
}

/**
 * V2版本的工作时间配置接口 - 从API直接获取格式化数据
 */
interface WorkTimeConfigV2 {
  utilizationMode: string;
  utilizationModeFormatted: string;
  dayStartTime: string;
  dayStartTimeFormatted: string;
  restPeriods: Array<{
    name: string;
    startTime: string;
    endTime: string;
    enabled: boolean;
    duration: number;
    durationFormatted: string;
    timeRange: string; // 如："12:00-13:00 (1小时)"
  }>;
  restPeriodsEnabled: Array<{
    name: string;
    timeRange: string;
  }>;
}

/**
 * V2版本的完整API响应接口
 */
interface StatusDetailResponseV2 {
  success: boolean;
  deviceId: string;
  date: string;
  dateFormatted: string;
  isToday: boolean;
  deviceInfo: DeviceInfoV2;
  workTimeConfig: WorkTimeConfigV2;
  statusRecords: StatusRecordV2[];
  utilizationStats: UtilizationStatsV2;
  summary: {
    totalRecords: number;
    totalRecordsFormatted: string;
    dateRange: string;
    calculationMode: string;
    calculationModeDescription: string;
  };
  error?: string;
}

export default function DeviceStatusDetailPageV2() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();

  // 安全地获取设备ID，添加空值检查
  const deviceId = params?.device_id as string;
  const date = searchParams?.get('date') || new Date().toISOString().split('T')[0];

  // 如果没有设备ID，显示错误
  if (!deviceId) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">错误</h1>
          <p className="text-gray-600">设备ID参数缺失</p>
        </div>
      </div>
    );
  }

  // 状态管理 - V2版本简化，只存储API返回的数据
  const [apiData, setApiData] = useState<StatusDetailResponseV2 | null>(null);
  const [filteredRecords, setFilteredRecords] = useState<StatusRecordV2[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isHelpExpanded, setIsHelpExpanded] = useState<boolean>(false); // 帮助信息展开状态

  // 系统框架状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '',
    dateTime: '',
    support: '',
    support_info: ''
  });

  /**
   * 获取站点信息
   * 从API获取站点基本信息（名称、技术支持等）
   */
  const fetchSiteInfo = async () => {
    try {
      console.log('🌐 开始获取站点信息...');
      const API_HOST = getApiBaseUrl();
      const response = await fetch(`${API_HOST}/api/v3/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`站点信息API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 站点信息API响应:', result);

      if (result.success && result.data) {
        // 更新公司信息，保留现有的dateTime
        setCompanyInfo(prev => ({
          ...prev,
          name: result.data.name || '智能制造系统',
          support: result.data.support || '',
          support_info: result.data.support_info || ''
        }));
        console.log('✅ 站点信息更新成功:', {
          name: result.data.name,
          support: result.data.support,
          support_info: result.data.support_info
        });
      } else {
        console.warn('⚠️ 站点信息API返回数据格式异常:', result);
      }
    } catch (error) {
      console.error('❌ 获取站点信息失败:', error);
      // 设置默认值
      setCompanyInfo(prev => ({
        ...prev,
        name: '智能制造系统',
        support: '',
        support_info: ''
      }));
    }
  };

  // V2版本：使用新的统一API获取设备状态详细数据
  const fetchDeviceStatusDetailV2 = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`📊 [V2 Frontend] 使用新的统一API获取设备状态详细数据: 设备=${deviceId}, 日期=${date}`);

      // 后端API URL
      const backendUrl = getApiBaseUrl();

      // 使用新的统一API - 一次调用获取所有需要的数据
      const response = await fetch(`${backendUrl}/api/v3/device/${deviceId}/status_history?date=${date}&use_work_time=true`);

      // 处理统一API响应
      if (!response.ok) {
        throw new Error(`获取设备状态详细数据失败: ${response.status}`);
      }

      const unifiedData = await response.json();
      console.log(`✅ [V2 Frontend] 成功获取统一API数据，共 ${unifiedData.status_history?.length || 0} 条状态记录`);

      // 从统一API响应中提取各部分数据
      const statusHistoryData = {
        statusHistory: unifiedData.status_history || [],
        time_slots: unifiedData.time_slots || []
      };

      const deviceInfo = unifiedData.device_info || null;

      // 从新API结构中提取工作时间配置
      const workTimeConfig = {
        utilization_mode: unifiedData.worktime_config?.utilization_mode || 'OP1',
        day_start_time: unifiedData.worktime_config?.day_start_time || '08:00',
        rest_periods: unifiedData.worktime_config?.rest_periods || []
      };

      // 从新API结构中提取统计数据，适配字段名称差异
      const statisticsData = {
        utilization_rate: unifiedData.utilization?.utilization || 0, // API字段名：utilization -> 前端期望：utilization_rate
        work_time: unifiedData.utilization?.work_time || 0,          // 总工作时间（有效工作时间）
        running_time: unifiedData.utilization?.running_time || 0,
        total_time: unifiedData.utilization?.total_time || 0,        // 所有状态时间之和
        status_summary: unifiedData.utilization?.status_summary || {}
      };

      console.log(`✅ [V2 Frontend] 数据解析完成: 设备=${deviceInfo?.name || deviceId}, 模式=${workTimeConfig?.utilization_mode || 'OP1'}, 利用率=${statisticsData.utilization_rate?.toFixed(1)}%`);

      // 在前端进行数据处理和格式化（V2版本的核心逻辑，使用统一API数据）
      const processedData = await processDataV2(statusHistoryData, deviceInfo, workTimeConfig, statisticsData, deviceId, date);

      console.log(`✅ [V2 Frontend] 数据处理完成，共 ${processedData.statusRecords.length} 条记录`);

      setApiData(processedData);
      setError(null);
    } catch (err) {
      console.error('❌ [V2 Frontend] 获取设备状态详细数据失败:', err);
      setError(err instanceof Error ? err.message : '获取数据失败');
      setApiData(null);
    } finally {
      setLoading(false);
    }
  };

  // 在客户端更新时间
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchSiteInfo(); // 获取站点信息
    if (deviceId) {
      fetchDeviceStatusDetailV2();
    }
  }, [deviceId, date]);

  // 状态筛选 - V2版本：直接使用API返回的数据
  useEffect(() => {
    if (!apiData) {
      setFilteredRecords([]);
      return;
    }

    if (statusFilter === 'all') {
      setFilteredRecords(apiData.statusRecords);
    } else {
      setFilteredRecords(apiData.statusRecords.filter(record => record.status === statusFilter));
    }
  }, [apiData, statusFilter]);

  // V2版本：导出Excel - 使用API返回的格式化数据
  const exportToExcelV2 = () => {
    if (!apiData) {
      alert('没有数据可导出');
      return;
    }

    try {
      // 准备导出数据 - 直接使用API返回的格式化数据
      const exportData = filteredRecords.map((record, index) => ({
        '序号': index + 1,
        '状态': record.statusName,
        '开始时间': record.startTimeFormatted,
        '结束时间': record.endTimeFormatted,
        '时长': record.durationFormatted,
        '运行累计': record.status === 'production' ? record.productionCumulativeFormatted : '',
        '待机累计': record.status === 'idle' ? record.idleCumulativeFormatted : '',
        '报警累计': record.status === 'fault' ? record.faultCumulativeFormatted : '',
        '关机累计': record.status === 'shutdown' ? record.shutdownCumulativeFormatted : ''
      }));

      // 添加统计信息 - 使用API返回的格式化数据
      const stats = apiData.utilizationStats;
      exportData.push({
        '序号': 0,
        '状态': '--- 统计汇总 ---',
        '开始时间': '',
        '结束时间': '',
        '时长': '',
        '运行累计': '',
        '待机累计': '',
        '报警累计': '',
        '关机累计': ''
      } as any);

      exportData.push({
        '序号': 0,
        '状态': '总工作时间',
        '开始时间': stats.totalTimeFormatted,
        '结束时间': '',
        '时长': '',
        '运行累计': '',
        '待机累计': '',
        '报警累计': '',
        '关机累计': ''
      } as any);

      exportData.push({
        '序号': 0,
        '状态': '运行合计',
        '开始时间': stats.productionTimeFormatted,
        '结束时间': '',
        '时长': '',
        '运行累计': '',
        '待机累计': '',
        '报警累计': '',
        '关机累计': ''
      } as any);

      exportData.push({
        '序号': 0,
        '状态': '待机合计',
        '开始时间': stats.idleTimeFormatted,
        '结束时间': '',
        '时长': '',
        '运行累计': '',
        '待机累计': '',
        '报警累计': '',
        '关机累计': ''
      } as any);

      exportData.push({
        '序号': 0,
        '状态': '报警合计',
        '开始时间': stats.faultTimeFormatted,
        '结束时间': '',
        '时长': '',
        '运行累计': '',
        '待机累计': '',
        '报警累计': '',
        '关机累计': ''
      } as any);

      exportData.push({
        '序号': 0,
        '状态': '关机合计',
        '开始时间': stats.shutdownTimeFormatted,
        '结束时间': '',
        '时长': '',
        '运行累计': '',
        '待机累计': '',
        '报警累计': '',
        '关机累计': ''
      } as any);

      exportData.push({
        '序号': 0,
        '状态': '设备利用率',
        '开始时间': stats.utilizationRateFormatted,
        '结束时间': '',
        '时长': '',
        '运行累计': '',
        '待机累计': '',
        '报警累计': '',
        '关机累计': ''
      } as any);

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 8 },  // 序号
        { wch: 16 }, // 状态
        { wch: 20 }, // 开始时间
        { wch: 20 }, // 结束时间
        { wch: 12 }, // 时长
        { wch: 12 }, // 运行累计
        { wch: 12 }, // 待机累计
        { wch: 12 }, // 报警累计
        { wch: 12 }  // 关机累计
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '设备状态详细记录');

      // 生成文件名
      const fileName = `设备状态详细记录_V2_${apiData.deviceInfo.name}_${date}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      console.log(`✅ [V2 Frontend] Excel导出成功: ${fileName}`);
    } catch (error) {
      console.error('❌ [V2 Frontend] Excel导出失败:', error);
      alert('导出Excel失败，请重试');
    }
  };

  // 返回仪表板
  const goBackToDashboard = () => {
    router.push('/device_utilization_dashboard_v2');
  };

  // V2版本：数据处理函数 - 将后端原始数据处理成格式化数据
  const processDataV2 = async (
    statusHistoryData: any,
    deviceInfo: any,
    workTimeConfig: any,
    statisticsData: any,
    deviceId: string,
    date: string
  ): Promise<StatusDetailResponseV2> => {
    const isToday = date === new Date().toISOString().split('T')[0];

    // 状态映射
    const STATUS_MAPPING = {
      'production': 'production',
      'running': 'production',
      'idle': 'idle',
      'fault': 'fault',
      'error': 'fault',
      'maintenance': 'maintenance',
      'shutdown': 'shutdown',
      'unknown': 'shutdown',
      'disconnected': 'shutdown',
      'offline': 'maintenance'
    };

    // 状态中文名称映射
    const STATUS_NAMES = {
      production: '生产',
      idle: '空闲',
      fault: '故障',
      shutdown: '关机',
      maintenance: '维护',
      offline: '离线'
    };

    // 状态颜色映射
    const STATUS_COLORS = {
      production: '#22c55e', // 绿色 - 生产
      idle: '#f59e0b',       // 橙色 - 空闲
      fault: '#ef4444',      // 红色 - 故障
      shutdown: '#6b7280',   // 灰色 - 关机
      maintenance: '#8b5cf6', // 紫色 - 维护
      offline: '#6b7280'     // 灰色 - 离线
    };

    // 格式化时长（秒转换为 HH:MM:SS 格式）
    const formatDuration = (seconds: number): string => {
      // 确保输入是整数秒，避免小数秒导致的格式问题
      const totalSeconds = Math.floor(seconds);
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const secs = totalSeconds % 60;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    // 格式化日期时间（ISO字符串转换为本地化格式）
    const formatDateTime = (dateTimeStr: string): string => {
      return new Date(dateTimeStr).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    };

    // 格式化日期（YYYY-MM-DD 转换为本地化格式）
    const formatDate = (dateStr: string): string => {
      return new Date(dateStr).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    };

    // 格式化百分比
    const formatPercentage = (value: number): string => {
      return `${value.toFixed(1)}%`;
    };

    // 处理状态历史数据
    const statusRecords: StatusRecordV2[] = [];
    let productionCumulative = 0;
    let idleCumulative = 0;
    let faultCumulative = 0;
    let maintenanceCumulative = 0;
    let shutdownCumulative = 0;

    const statusHistory = statusHistoryData?.statusHistory || [];
    statusHistory.forEach((item: any, index: number) => {
      const statusField = item.currentStatus || item.status || item.state || item.device_status || 'shutdown';
      const normalizedStatus = STATUS_MAPPING[statusField as keyof typeof STATUS_MAPPING] || statusField;
      const duration = item.duration || 0;

      // 根据状态更新累计时间
      switch (normalizedStatus) {
        case 'production':
          productionCumulative += duration;
          break;
        case 'idle':
          idleCumulative += duration;
          break;
        case 'fault':
          faultCumulative += duration;
          break;
        case 'maintenance':
          maintenanceCumulative += duration;
          break;
        case 'shutdown':
          shutdownCumulative += duration;
          break;
      }

      // 处理时间数据
      const startTimeStr = item.startTime || item.timestamp || item.start_time || item.time;
      if (!startTimeStr) {
        console.warn(`⚠️ [V2 Frontend] 记录 ${index + 1} 缺少时间字段，跳过处理`);
        return;
      }

      let startTime: Date;
      if (startTimeStr.includes('T') || startTimeStr.includes(' ')) {
        startTime = new Date(startTimeStr);
      } else {
        const baseDate = new Date(date + 'T' + startTimeStr + 'Z');
        startTime = baseDate;
      }

      if (isNaN(startTime.getTime())) {
        console.warn(`⚠️ [V2 Frontend] 无效的开始时间: ${startTimeStr}, 跳过记录 ${index + 1}`);
        return;
      }

      // 计算结束时间
      let endTimeStr: string;
      const endTimeField = item.endTime || item.end_time;
      if (endTimeField) {
        const endTime = new Date(endTimeField);
        if (isNaN(endTime.getTime())) {
          endTimeStr = new Date(startTime.getTime() + duration * 1000).toISOString();
        } else {
          endTimeStr = endTime.toISOString();
        }
      } else {
        endTimeStr = new Date(startTime.getTime() + duration * 1000).toISOString();
      }

      // 创建格式化记录
      const record: StatusRecordV2 = {
        id: index + 1,
        status: normalizedStatus,
        statusName: STATUS_NAMES[normalizedStatus as keyof typeof STATUS_NAMES] || normalizedStatus,
        statusColor: STATUS_COLORS[normalizedStatus as keyof typeof STATUS_COLORS] || '#6b7280',
        startTime: startTime.toISOString(),
        endTime: endTimeStr,
        startTimeFormatted: formatDateTime(startTime.toISOString()),
        endTimeFormatted: formatDateTime(endTimeStr),
        duration,
        durationFormatted: formatDuration(duration),
        productionCumulative,
        productionCumulativeFormatted: formatDuration(productionCumulative),
        idleCumulative,
        idleCumulativeFormatted: formatDuration(idleCumulative),
        faultCumulative,
        faultCumulativeFormatted: formatDuration(faultCumulative),
        maintenanceCumulative,
        maintenanceCumulativeFormatted: formatDuration(maintenanceCumulative),
        shutdownCumulative,
        shutdownCumulativeFormatted: formatDuration(shutdownCumulative)
      };

      statusRecords.push(record);
    });

    // 按开始时间排序
    statusRecords.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());

    return {
      success: true,
      deviceId,
      date,
      dateFormatted: formatDate(date),
      isToday,
      deviceInfo: {
        id: deviceId,
        name: deviceInfo?.name || `设备 ${deviceId}`,
        model: deviceInfo?.model || deviceInfo?.brand || 'Unknown',
        location: deviceInfo?.location || '未知位置',
        dayStartTime: workTimeConfig?.day_start_time || '08:00',
        dayStartTimeFormatted: workTimeConfig?.day_start_time || '08:00'
      },
      workTimeConfig: {
        utilizationMode: workTimeConfig?.utilization_mode || 'OP1',
        utilizationModeFormatted: (workTimeConfig?.utilization_mode === 'OP2')
          ? '实际工作时长模式'
          : '24小时固定模式',
        dayStartTime: workTimeConfig?.day_start_time || '08:00',
        dayStartTimeFormatted: workTimeConfig?.day_start_time || '08:00',
        restPeriods: (workTimeConfig?.rest_periods || []).map((period: any) => {
          const startTime = period.start_time || '12:00';
          const endTime = period.end_time || '13:00';
          const [startHour, startMinute] = startTime.split(':').map(Number);
          const [endHour, endMinute] = endTime.split(':').map(Number);
          const startSeconds = startHour * 3600 + startMinute * 60;
          const endSeconds = endHour * 3600 + endMinute * 60;
          const duration = endSeconds - startSeconds;
          const durationHours = Math.floor(duration / 3600);
          const durationMinutes = Math.floor((duration % 3600) / 60);
          let durationText = '';
          if (durationHours > 0) {
            durationText = durationMinutes > 0 ? `${durationHours}小时${durationMinutes}分钟` : `${durationHours}小时`;
          } else {
            durationText = `${durationMinutes}分钟`;
          }
          return {
            name: period.name || '休息时间',
            startTime,
            endTime,
            enabled: period.enabled || false,
            duration,
            durationFormatted: formatDuration(duration),
            timeRange: `${startTime}-${endTime} (${durationText})`
          };
        }),
        restPeriodsEnabled: (workTimeConfig?.rest_periods || [])
          .filter((period: any) => period.enabled)
          .map((period: any) => {
            const startTime = period.start_time || '12:00';
            const endTime = period.end_time || '13:00';
            const [startHour, startMinute] = startTime.split(':').map(Number);
            const [endHour, endMinute] = endTime.split(':').map(Number);
            const startSeconds = startHour * 3600 + startMinute * 60;
            const endSeconds = endHour * 3600 + endMinute * 60;
            const duration = endSeconds - startSeconds;
            const durationMinutes = Math.floor(duration / 60);
            return {
              name: period.name || '休息时间',
              timeRange: `${startTime}-${endTime} (${durationMinutes}分钟)`
            };
          })
      },
      statusRecords,
      utilizationStats: await calculateUtilizationStatsV2(statusRecords, workTimeConfig, statisticsData, isToday),
      summary: {
        totalRecords: statusRecords.length,
        totalRecordsFormatted: `${statusRecords.length} 条记录`,
        dateRange: isToday ? '当天实时数据' : '历史数据',
        calculationMode: workTimeConfig?.utilization_mode || 'OP1',
        calculationModeDescription: (workTimeConfig?.utilization_mode === 'OP2')
          ? '实际工作时长模式（扣除休息时间）'
          : '24小时固定模式'
      }
    };
  };

  // V2版本：使用统计服务数据（完全依赖后端计算，无前端计算）
  const calculateUtilizationStatsV2 = async (
    records: StatusRecordV2[],
    workTimeConfig: any,
    statisticsData: any,
    isToday: boolean
  ): Promise<UtilizationStatsV2> => {
    console.log(`📊 [V2 Frontend] 处理利用率统计: 记录数=${records.length}, 是否今天=${isToday}`);

    // 完全依赖后端统计服务的计算结果，不进行任何前端计算
    if (statisticsData && statisticsData.work_time && statisticsData.utilization_rate !== undefined) {
      console.log(`📊 [V2 Frontend] 使用统计服务数据，无需前端计算`);

      // 直接使用后端计算的结果，适配新API字段名称
      // 注意：totalTime 使用 work_time（有效工作时间），而不是 total_time（所有状态时间之和）
      const totalTime = statisticsData.work_time;           // 总工作时间 = API的work_time
      const utilizationRate = statisticsData.utilization_rate;
      const productionTime = statisticsData.running_time || 0;

      // 从status_summary中提取各状态时间
      const statusSummary = statisticsData.status_summary || {};
      const idleTime = statusSummary.idle || 0;
      const faultTime = statusSummary.fault || 0;
      const maintenanceTime = statusSummary.maintenance || 0;
      const shutdownTime = statusSummary.shutdown || 0;

      // 计算百分比（基于后端数据）
      const productionPercentage = totalTime > 0 ? (productionTime / totalTime) * 100 : 0;
      const idlePercentage = totalTime > 0 ? (idleTime / totalTime) * 100 : 0;
      const faultPercentage = totalTime > 0 ? (faultTime / totalTime) * 100 : 0;
      const maintenancePercentage = totalTime > 0 ? (maintenanceTime / totalTime) * 100 : 0;
      const shutdownPercentage = totalTime > 0 ? (shutdownTime / totalTime) * 100 : 0;

      return buildUtilizationStatsResponse(
        totalTime, utilizationRate, productionTime, idleTime, faultTime,
        maintenanceTime, shutdownTime, productionPercentage, idlePercentage,
        faultPercentage, maintenancePercentage, shutdownPercentage
      );
    } else {
      // 统计服务数据不可用时的错误处理
      console.log(`❌ [V2 Frontend] 统计服务数据不可用，无法计算利用率`);
      return getEmptyUtilizationStatsV2();
    }
  };

  // 构建利用率统计响应的辅助函数
  const buildUtilizationStatsResponse = (
    totalTime: number, utilizationRate: number, productionTime: number,
    idleTime: number, faultTime: number, maintenanceTime: number, shutdownTime: number,
    productionPercentage: number, idlePercentage: number, faultPercentage: number,
    maintenancePercentage: number, shutdownPercentage: number
  ): UtilizationStatsV2 => {

    // 格式化函数（仅用于显示格式化）
    const formatDuration = (seconds: number): string => {
      // 确保输入是整数秒，避免小数秒导致的格式问题
      const totalSeconds = Math.floor(seconds);
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const secs = totalSeconds % 60;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const formatPercentage = (value: number): string => {
      return `${value.toFixed(1)}%`;
    };

    // 状态颜色映射
    const STATUS_COLORS = {
      production: '#22c55e',
      idle: '#f59e0b',
      fault: '#ef4444',
      shutdown: '#6b7280',
      maintenance: '#8b5cf6'
    };

    // 构建状态分布数据（仅用于显示）
    const statusDistribution = [];

    if (productionTime > 0) {
      statusDistribution.push({
        status: 'production',
        statusName: '生产',
        color: STATUS_COLORS.production,
        percentage: productionPercentage,
        percentageFormatted: formatPercentage(productionPercentage),
        time: productionTime,
        timeFormatted: formatDuration(productionTime)
      });
    }

    if (idleTime > 0) {
      statusDistribution.push({
        status: 'idle',
        statusName: '空闲',
        color: STATUS_COLORS.idle,
        percentage: idlePercentage,
        percentageFormatted: formatPercentage(idlePercentage),
        time: idleTime,
        timeFormatted: formatDuration(idleTime)
      });
    }

    if (faultTime > 0) {
      statusDistribution.push({
        status: 'fault',
        statusName: '故障',
        color: STATUS_COLORS.fault,
        percentage: faultPercentage,
        percentageFormatted: formatPercentage(faultPercentage),
        time: faultTime,
        timeFormatted: formatDuration(faultTime)
      });
    }

    if (maintenanceTime > 0) {
      statusDistribution.push({
        status: 'maintenance',
        statusName: '维护',
        color: STATUS_COLORS.maintenance,
        percentage: maintenancePercentage,
        percentageFormatted: formatPercentage(maintenancePercentage),
        time: maintenanceTime,
        timeFormatted: formatDuration(maintenanceTime)
      });
    }

    if (shutdownTime > 0) {
      statusDistribution.push({
        status: 'shutdown',
        statusName: '关机',
        color: STATUS_COLORS.shutdown,
        percentage: shutdownPercentage,
        percentageFormatted: formatPercentage(shutdownPercentage),
        time: shutdownTime,
        timeFormatted: formatDuration(shutdownTime)
      });
    }

    return {
      totalTime,
      totalTimeFormatted: formatDuration(totalTime),
      productionTime,
      productionTimeFormatted: formatDuration(productionTime),
      productionPercentage,
      productionPercentageFormatted: formatPercentage(productionPercentage),
      idleTime,
      idleTimeFormatted: formatDuration(idleTime),
      idlePercentage,
      idlePercentageFormatted: formatPercentage(idlePercentage),
      faultTime,
      faultTimeFormatted: formatDuration(faultTime),
      faultPercentage,
      faultPercentageFormatted: formatPercentage(faultPercentage),
      maintenanceTime,
      maintenanceTimeFormatted: formatDuration(maintenanceTime),
      maintenancePercentage,
      maintenancePercentageFormatted: formatPercentage(maintenancePercentage),
      shutdownTime,
      shutdownTimeFormatted: formatDuration(shutdownTime),
      shutdownPercentage,
      shutdownPercentageFormatted: formatPercentage(shutdownPercentage),
      utilizationRate,
      utilizationRateFormatted: formatPercentage(utilizationRate),
      statusDistribution
    };
  };

  // 获取空的利用率统计数据（错误处理用）
  const getEmptyUtilizationStatsV2 = (): UtilizationStatsV2 => {
    return {
      totalTime: 0,
      totalTimeFormatted: '0:00:00',
      productionTime: 0,
      productionTimeFormatted: '0:00:00',
      productionPercentage: 0,
      productionPercentageFormatted: '0.0%',
      idleTime: 0,
      idleTimeFormatted: '0:00:00',
      idlePercentage: 0,
      idlePercentageFormatted: '0.0%',
      faultTime: 0,
      faultTimeFormatted: '0:00:00',
      faultPercentage: 0,
      faultPercentageFormatted: '0.0%',
      maintenanceTime: 0,
      maintenanceTimeFormatted: '0:00:00',
      maintenancePercentage: 0,
      maintenancePercentageFormatted: '0.0%',
      shutdownTime: 0,
      shutdownTimeFormatted: '0:00:00',
      shutdownPercentage: 0,
      shutdownPercentageFormatted: '0.0%',
      utilizationRate: 0,
      utilizationRateFormatted: '0.0%',
      statusDistribution: []
    };
  };

  // V2版本：加载状态渲染
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex">
        <Sidebar mode={sidebarMode} />
        <div className="flex-1 flex flex-col">
          <Header
            companyInfo={companyInfo}
            toggleSidebar={() => setSidebarMode(sidebarMode === SidebarMode.COLLAPSED ? SidebarMode.HIDDEN : SidebarMode.COLLAPSED)}
          />
          <main className="flex-1 px-3 py-2">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-400">正在加载设备状态详细数据 (V2版本)...</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  // V2版本：错误状态渲染
  if (error || !apiData) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex">
        <Sidebar mode={sidebarMode} />
        <div className="flex-1 flex flex-col">
          <Header
            companyInfo={companyInfo}
            toggleSidebar={() => setSidebarMode(sidebarMode === SidebarMode.COLLAPSED ? SidebarMode.HIDDEN : SidebarMode.COLLAPSED)}
          />
          <main className="flex-1 px-3 py-2">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-red-400 mb-4">
                  <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p className="text-lg font-medium">加载失败</p>
                  <p className="text-sm text-gray-400 mt-2">{error || '获取数据失败'}</p>
                </div>
                <Button
                  onClick={fetchDeviceStatusDetailV2}
                  variant="outline"
                  className="text-gray-300 border-gray-600 hover:bg-gray-800"
                >
                  重新加载
                </Button>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  // V2版本：主要内容渲染 - 使用API返回的格式化数据
  const { deviceInfo, workTimeConfig, utilizationStats, summary } = apiData;

  return (
    <div className="min-h-screen bg-gray-900 text-white flex">
      <Sidebar mode={sidebarMode} />

      <div className="flex-1 flex flex-col">
        <Header
          companyInfo={companyInfo}
          toggleSidebar={() => setSidebarMode(sidebarMode === SidebarMode.COLLAPSED ? SidebarMode.HIDDEN : SidebarMode.COLLAPSED)}
        />

        <main className="flex-1 px-3 py-2">
          {/* 页面标题和导航 */}
          <div className="bg-blue-900/20 py-1 px-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={goBackToDashboard}
                  className="p-1 rounded-full hover:bg-gray-700 flex items-center"
                  title="返回仪表板"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  <span className="text-sm">返回仪表板</span>
                </button>
                <div>
                  <h1 className="text-xl font-bold text-white">设备状态详细数据</h1>
                  <p className="text-gray-400 text-sm">
                    设备: {deviceInfo.name} | 日期: {apiData.dateFormatted}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  onClick={exportToExcelV2}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  size="sm"
                >
                  <Download className="w-4 h-4 mr-2" />
                  导出Excel
                </Button>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {/* 设备基本信息和利用率统计 - 左右分布 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* 设备基本信息 */}
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white flex items-center text-sm">
                    <Settings className="w-4 h-4 mr-2" />
                    设备基本信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                    <div className="flex items-center">
                      <p className="text-xs text-gray-400 mr-2">设备名称:</p>
                      <p className="text-white font-medium text-sm">{deviceInfo.name}</p>
                    </div>
                    <div className="flex items-center">
                      <p className="text-xs text-gray-400 mr-2">设备型号:</p>
                      <p className="text-white font-medium text-sm">{deviceInfo.model}</p>
                    </div>
                    <div className="flex items-center">
                      <p className="text-xs text-gray-400 mr-2">设备位置:</p>
                      <p className="text-white font-medium text-sm">{deviceInfo.location}</p>
                    </div>
                    <div className="flex items-center">
                      <p className="text-xs text-gray-400 mr-2">计算模式:</p>
                      <Badge variant="outline" className="text-blue-400 border-blue-400 text-xs py-0 px-2 h-5">
                        {workTimeConfig.utilizationModeFormatted}
                      </Badge>
                    </div>
                    <div className="flex items-center sm:col-span-2 lg:col-span-2">
                      <p className="text-xs text-gray-400 mr-2">开始时间:</p>
                      <p className="text-white font-medium text-sm flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {deviceInfo.dayStartTimeFormatted}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 设备利用率统计 */}
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white flex items-center text-sm">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    设备利用率统计
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="space-y-2">
                    {/* 利用率和状态分布 - 一行显示 */}
                    <div className="flex items-center gap-4">
                      {/* 设备利用率 - 占用小 */}
                      <div className="flex-shrink-0">
                        <div className="text-2xl font-bold text-blue-400">
                          {utilizationStats.utilizationRateFormatted}
                        </div>
                        <p className="text-xs text-gray-400">设备利用率</p>
                      </div>

                      {/* 状态分布 - 占用大 */}
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-400">状态分布</span>
                          <span className="text-xs text-gray-400">
                            总工作时间: {utilizationStats.totalTimeFormatted}
                          </span>
                        </div>

                        <div className="w-full bg-gray-700 rounded-lg h-6 flex overflow-hidden">
                          {utilizationStats.statusDistribution.map((statusItem, index) => (
                            <div
                              key={statusItem.status}
                              className="h-full flex items-center justify-center text-xs text-white font-medium"
                              style={{
                                backgroundColor: statusItem.color,
                                width: `${statusItem.percentage}%`
                              }}
                            >
                              {statusItem.percentageFormatted}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* 状态图例 - 一行显示 */}
                    <div className="flex items-center gap-4 text-xs">
                      {utilizationStats.statusDistribution.map((statusItem) => (
                        <div key={statusItem.status} className="flex items-center">
                          <div
                            className="w-2 h-2 rounded mr-1.5"
                            style={{ backgroundColor: statusItem.color }}
                          ></div>
                          <span className="text-gray-300">
                            {statusItem.statusName}: {statusItem.timeFormatted}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 状态筛选和数据表格 */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center">
                    <Filter className="w-5 h-5 mr-2" />
                    设备状态详细记录
                  </CardTitle>

                  <div className="flex items-center">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="w-36 h-10 rounded-md border border-gray-600 bg-gray-700 px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all" className="text-white bg-gray-700">全部状态</option>
                      <option value="production" className="text-white bg-gray-700">生产</option>
                      <option value="idle" className="text-white bg-gray-700">空闲</option>
                      <option value="fault" className="text-white bg-gray-700">故障</option>
                      <option value="shutdown" className="text-white bg-gray-700">关机</option>
                      {/* <option value="maintenance" className="text-white bg-gray-700">维护</option> */}
                    </select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {filteredRecords.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-400">暂无状态记录数据</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-700">
                          <th className="text-left py-3 px-4 text-gray-300">序号</th>
                          <th className="text-left py-3 px-4 text-gray-300">状态</th>
                          <th className="text-left py-3 px-4 text-gray-300">开始时间</th>
                          <th className="text-left py-3 px-4 text-gray-300">结束时间</th>
                          <th className="text-left py-3 px-4 text-gray-300">时长</th>
                          <th className="text-left py-3 px-4 text-gray-300">运行累计</th>
                          <th className="text-left py-3 px-4 text-gray-300">待机累计</th>
                          <th className="text-left py-3 px-4 text-gray-300">报警累计</th>
                          <th className="text-left py-3 px-4 text-gray-300">关机累计</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredRecords.map((record, index) => (
                          <tr
                            key={record.id}
                            className="border-b border-gray-700 hover:bg-gray-750"
                            style={{
                              backgroundColor: `${record.statusColor}15`
                            }}
                          >
                            <td className="py-3 px-4 text-gray-300">{index + 1}</td>
                            <td className="py-3 px-4">
                              <div
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                                style={{
                                  backgroundColor: record.statusColor
                                }}
                              >
                                {record.statusName}
                              </div>
                            </td>
                            <td className="py-3 px-4 text-gray-300">{record.startTimeFormatted}</td>
                            <td className="py-3 px-4 text-gray-300">{record.endTimeFormatted}</td>
                            <td className="py-3 px-4 text-gray-300">{record.durationFormatted}</td>
                            <td className="py-3 px-4 text-gray-300">
                              {record.status === 'production' ? record.productionCumulativeFormatted : ''}
                            </td>
                            <td className="py-3 px-4 text-gray-300">
                              {record.status === 'idle' ? record.idleCumulativeFormatted : ''}
                            </td>
                            <td className="py-3 px-4 text-gray-300">
                              {record.status === 'fault' ? record.faultCumulativeFormatted : ''}
                            </td>
                            <td className="py-3 px-4 text-gray-300">
                              {record.status === 'shutdown' ? record.shutdownCumulativeFormatted : ''}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>

                    {/* 合计行 - V2版本使用API返回的格式化数据 */}
                    <div className="mt-6 p-4 bg-gray-750 rounded-lg">
                      <div className="flex items-center justify-end mb-3">
                        <button
                          onClick={() => setIsHelpExpanded(!isHelpExpanded)}
                          className="text-gray-400 hover:text-white transition-colors p-1 rounded"
                          title={isHelpExpanded ? "收起帮助信息" : "展开帮助信息"}
                        >
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        </button>
                      </div>

                      {/* 帮助信息内容 - 默认折叠 */}
                      {isHelpExpanded && (
                        <div className="space-y-4">
                          <h4 className="text-white font-medium">状态合计与利用率计算</h4>
                          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                            <div>
                              <p className="text-gray-400">总工作时间</p>
                              <p className="text-blue-400 font-medium">{utilizationStats.totalTimeFormatted}</p>
                            </div>
                            <div>
                              <p className="text-gray-400">运行合计</p>
                              <p className="text-green-400 font-medium">{utilizationStats.productionTimeFormatted}</p>
                            </div>
                            <div>
                              <p className="text-gray-400">待机合计</p>
                              <p className="text-yellow-400 font-medium">{utilizationStats.idleTimeFormatted}</p>
                            </div>
                            <div>
                              <p className="text-gray-400">报警合计</p>
                              <p className="text-red-400 font-medium">{utilizationStats.faultTimeFormatted}</p>
                            </div>
                            <div>
                              <p className="text-gray-400">关机合计</p>
                              <p className="text-gray-400 font-medium">{utilizationStats.shutdownTimeFormatted}</p>
                            </div>
                          </div>

                          {/* 休息时间显示 - 仅OP2模式 */}
                          {workTimeConfig.utilizationMode === 'OP2' && workTimeConfig.restPeriodsEnabled.length > 0 && (
                            <div className="mt-4 pt-4 border-t border-gray-600">
                              <div className="text-gray-400 text-sm">
                                <p className="mb-2">休息时间:</p>
                                <div className="space-y-1 text-xs">
                                  {workTimeConfig.restPeriodsEnabled.map((period, index) => (
                                    <p key={index}>
                                      • {period.name}: {period.timeRange}
                                    </p>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}

                          {/* 利用率计算模式说明 */}
                          <div className="mt-4 pt-4 border-t border-gray-600">
                            <div className="text-gray-400 text-sm space-y-2">
                              <p className="font-medium">利用率计算模式:</p>
                              <div className="text-xs space-y-1 pl-2">
                                <p>• 模式: {summary.calculationMode} ({workTimeConfig.utilizationModeFormatted})</p>
                                <p>• 说明: 根据实际工作时间计算，扣除配置的休息时间</p>
                                {apiData.isToday ? (
                                  <p>• 当天计算: 当前时间 - 开始时间 - 已过的休息时间</p>
                                ) : (
                                  <p>• 历史计算: 24小时 - 全部休息时间</p>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* 利用率计算公式 */}
                          <div className="mt-4 pt-4 border-t border-gray-600">
                            <div className="text-gray-400 text-sm space-y-1">
                              <p>利用率计算:</p>
                              <p>• 总运行时间 ÷ 总工作时间 = 利用率</p>
                              <p>• <span>
                                {utilizationStats.productionTimeFormatted} ÷ {utilizationStats.totalTimeFormatted} =
                                <span className="text-blue-400 font-medium ml-1">
                                  {utilizationStats.utilizationRateFormatted}
                                </span>
                              </span>
                              </p>
                              <p>• <span>
                                {utilizationStats.productionTime}秒 ÷ {utilizationStats.totalTime}秒 =
                                <span className="text-blue-400 font-medium ml-1">
                                  {utilizationStats.utilizationRateFormatted}
                                </span>
                              </span>
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
