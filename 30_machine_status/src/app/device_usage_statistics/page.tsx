/**
 * 设备使用情况统计页面
 *
 * 功能描述：
 * - 提供设备利用率、产量达成率等关键指标的统计分析
 * - 支持单设备和多设备汇总统计
 * - 包含当日统计、横向对比、班次统计三种视图模式
 * - 集成数据验证功能，确保统计结果的准确性
 *
 * 技术特性：
 * - 响应式设计，适配不同屏幕尺寸
 * - 实时数据刷新和手动统计任务触发
 * - 与主框架完全集成（侧边栏、头部、全屏功能）
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-05
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, BarChart3, TrendingUp, Clock, Settings, RefreshCw } from 'lucide-react';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { getApiBaseUrl } from '@/config/api';

/**
 * ===== 数据类型定义 =====
 * 定义了页面中使用的所有数据结构和接口
 */

/**
 * 设备统计数据接口
 *
 * 描述：单个设备的完整统计信息，包含利用率、产量、运行时间等核心指标
 *
 * @interface DeviceStatistics
 */
interface DeviceStatistics {
  /** 设备唯一标识符 */
  device_id: string;

  /** 设备显示名称 */
  device_name: string;

  /** 统计日期，格式：YYYY-MM-DD */
  date: string;

  /** 设备利用率，范围：0-100，单位：百分比 */
  utilization_rate: number;

  /** 产量达成率，范围：0-100+，单位：百分比 */
  output_rate: number;

  /** 设备运行时间，单位：秒 */
  running_time: number;

  /** 设备总时间（通常为24小时），单位：秒 */
  total_time: number;

  /** 实际产量，单位：件 */
  actual_output: number;

  /** 计划产量，单位：件 */
  planned_output: number;

  /** 班次统计详细数据 */
  shift_statistics: ShiftStatistics[];

  /** 数据创建时间，ISO格式 */
  created_at: string;

  /** 数据更新时间，ISO格式 */
  updated_at: string;
}

/**
 * 班次统计数据接口
 *
 * 描述：单个班次的详细统计信息，用于班次级别的数据分析
 *
 * @interface ShiftStatistics
 */
interface ShiftStatistics {
  /** 班次名称，如：白班、夜班 */
  shift_name: string;

  /** 班次开始时间，格式：HH:MM */
  start_time: string;

  /** 班次结束时间，格式：HH:MM */
  end_time: string;

  /** 班次设备利用率，范围：0-100，单位：百分比 */
  utilization_rate: number;

  /** 班次产量达成率，范围：0-100+，单位：百分比 */
  output_rate: number;

  /** 班次运行时间，单位：秒 */
  running_time: number;

  /** 班次总时间，单位：秒 */
  total_time: number;

  /** 班次实际产量，单位：件 */
  actual_output: number;

  /** 班次计划产量，单位：件 */
  planned_output: number;
}

/**
 * 日统计汇总数据接口
 *
 * 描述：多设备的汇总统计信息，提供整体运营状况的概览
 * 注意：支持两种API字段格式以保证兼容性
 *
 * @interface DailyStatisticsSummary
 */
interface DailyStatisticsSummary {
  /** 统计日期，格式：YYYY-MM-DD */
  date: string;

  /** 参与统计的设备总数 */
  total_devices: number;

  /** 平均设备利用率（新版API字段），范围：0-100，单位：百分比 */
  average_utilization_rate?: number;

  /** 平均产量达成率（新版API字段），范围：0-100+，单位：百分比 */
  average_output_rate?: number;

  /** 平均设备利用率（旧版API字段），范围：0-100，单位：百分比 */
  avg_utilization_rate?: number;

  /** 平均产量达成率（旧版API字段），范围：0-100+，单位：百分比 */
  avg_output_rate?: number;

  /** 所有设备运行时间总和，单位：秒 */
  total_running_time: number;

  /** 所有设备总时间（设备数×24小时），单位：秒 */
  total_time: number;

  /** 所有设备实际产量总和，单位：件 */
  total_actual_output: number;

  /** 所有设备计划产量总和，单位：件 */
  total_planned_output: number;
}

/**
 * 工作时间设置接口
 *
 * 描述：定义工厂的工作时间配置，包括班次设置、休息时间和利用率计算模式
 * 用途：用于显示当前的工作时间配置，帮助用户理解统计数据的计算基础
 *
 * @interface WorkTimeSettings
 */
interface WorkTimeSettings {
  /** 每日工作开始时间，格式：HH:MM */
  day_start_time: string;

  /** 班次配置列表 */
  shifts: Array<{
    /** 班次名称，如：白班、夜班 */
    name: string;
    /** 班次开始时间，格式：HH:MM */
    start_time: string;
    /** 班次结束时间，格式：HH:MM */
    end_time: string;
    /** 是否启用该班次 */
    enabled: boolean;
  }>;

  /** 休息时间配置列表 */
  rest_periods: Array<{
    /** 休息时间名称，如：午休、晚休 */
    name: string;
    /** 休息开始时间，格式：HH:MM */
    start_time: string;
    /** 休息结束时间，格式：HH:MM */
    end_time: string;
    /** 是否启用该休息时间 */
    enabled: boolean;
  }>;

  /** 利用率计算模式：OP1(24小时) 或 OP2(24小时-休息时间) */
  utilization_mode: string;
}

/**
 * 设备使用情况统计页面主组件
 *
 * 功能：
 * - 管理页面的所有状态和数据
 * - 处理用户交互和数据获取
 * - 渲染统计数据的可视化界面
 *
 * @returns {JSX.Element} 设备统计页面的完整UI
 */
const DeviceUsageStatisticsPage: React.FC = () => {

  /**
   * ===== 界面状态管理 =====
   * 管理页面布局和用户界面相关的状态
   */

  /** 侧边栏显示模式状态 */
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);

  /** 页面全屏/最大化状态 */
  const [isMaximized, setIsMaximized] = useState(false);

  /** 公司信息状态，包含公司名称和当前时间 */
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '江苏恒力阿萨德有限公司',
    dateTime: ''  // 初始为空字符串，避免SSR水合错误
  });

  /**
   * ===== 数据筛选状态 =====
   * 管理用户的筛选条件和查询参数
   */

  /** 当前选择的统计日期，格式：YYYY-MM-DD */
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));

  /** 当前选择的设备ID，'all'表示所有设备 */
  const [selectedDevice, setSelectedDevice] = useState<string>('all');

  /** 当前视图模式：daily(当日统计) | comparison(横向对比) | shifts(班次统计) */
  const [viewMode, setViewMode] = useState<'daily' | 'comparison' | 'shifts'>('daily');

  /** 日期范围选择，用于横向对比模式 */
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: format(subDays(new Date(), 7), 'yyyy-MM-dd'),  // 默认最近7天
    end: format(new Date(), 'yyyy-MM-dd')                 // 到今天
  });

  /**
   * ===== 数据状态管理 =====
   * 管理从API获取的各种数据
   */

  /** 设备统计数据列表，用于单设备或多设备详细统计 */
  const [deviceStatistics, setDeviceStatistics] = useState<DeviceStatistics[]>([]);

  /** 日汇总统计数据，用于所有设备的汇总显示 */
  const [dailySummary, setDailySummary] = useState<DailyStatisticsSummary | null>(null);

  /** 工作时间设置数据，用于显示当前的工作时间配置 */
  const [workTimeSettings, setWorkTimeSettings] = useState<WorkTimeSettings | null>(null);

  /** 可选设备列表，用于设备选择下拉框 */
  const [devices, setDevices] = useState<Array<{ id: string; name: string }>>([]);

  /** 数据加载状态，控制加载动画的显示 */
  const [loading, setLoading] = useState(false);

  /** 错误信息状态，用于显示API调用失败等错误 */
  const [error, setError] = useState<string | null>(null);

  /**
   * API服务器配置
   * 从环境变量读取API主机地址，支持开发和生产环境的灵活配置
   *
   * @constant {string} API_HOST - API服务器基础URL
   * @default 从环境变量 NEXT_PUBLIC_API_BASE_URL 读取，如未设置则使用默认值
   */
  const API_HOST = getApiBaseUrl();

  /**
   * ===== 生命周期管理 =====
   * 管理组件的初始化、状态同步和清理工作
   */

  /**
   * 初始化侧边栏状态
   *
   * 功能：从localStorage加载用户上次设置的侧边栏显示模式
   * 时机：组件首次挂载时执行
   *
   * 注意：使用动态导入避免SSR问题，因为localStorage只在客户端可用
   */
  useEffect(() => {
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        setSidebarMode(mode);
      } catch (error) {
        console.error('加载侧边栏状态失败:', error);
      }
    };
    loadSidebarMode();
  }, []);

  /**
   * ===== 用户交互处理函数 =====
   * 处理用户的各种操作和界面交互
   */

  /**
   * 切换侧边栏显示模式
   *
   * 功能：在隐藏、图标、完整三种模式间循环切换
   * 持久化：将新状态保存到localStorage
   *
   * 实现细节：
   * - 使用useCallback优化性能，避免不必要的重渲染
   * - 延迟50ms更新公司信息，确保布局变化完成后再触发重渲染
   *
   * @param {void} - 无参数
   * @returns {Promise<void>} - 异步操作，无返回值
   */
  const toggleSidebar = useCallback(async () => {
    try {
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');
      const nextMode = getNextSidebarMode(sidebarMode);
      saveSidebarMode(nextMode);
      setSidebarMode(nextMode);
      // 延迟更新，确保布局变化完成
      setTimeout(() => {
        setCompanyInfo(prev => ({ ...prev }));
      }, 50);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  // 切换最大化状态
  const toggleMaximize = useCallback(() => {
    setIsMaximized(prev => {
      const newState = !prev;
      if (newState) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }
      return newState;
    });
  }, []);

  // 监听全屏变化事件
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  // 在客户端更新时间
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };
    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  /**
   * ===== 数据获取函数 =====
   * 负责从API获取各种数据并更新组件状态
   */

  /**
   * 获取设备列表
   *
   * 功能：从API获取所有可用设备的列表，用于设备选择下拉框
   * API端点：GET /api/machines
   *
   * 数据处理：
   * - 提取machines数组中的设备信息
   * - 只保留id和name字段，简化数据结构
   * - 设置空数组作为默认值，防止undefined错误
   *
   * 错误处理：
   * - 网络错误或API错误会被捕获并记录到控制台
   * - 不会影响页面的正常显示，只是设备选择功能不可用
   *
   * @returns {Promise<void>} - 异步操作，无返回值
   */
  const fetchDevices = async () => {
    try {
      // 使用统一的环境变量直接调用后端API
      console.log(`🔗 [Device Usage Statistics] 直接调用后端API: ${API_HOST}/api/machines`);

      const response = await fetch(`${API_HOST}/api/machines`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      });

      if (!response.ok) throw new Error('获取设备列表失败');
      const data = await response.json();
      // 提取设备基本信息，确保数据结构一致性
      setDevices(data.machines?.map((device: any) => ({
        id: device.id,
        name: device.name
      })) || []);
    } catch (err) {
      console.error('获取设备列表失败:', err);
      // 设置空数组，确保UI不会因为数据缺失而崩溃
      setDevices([]);
    }
  };

  /**
   * 获取工作时间设置
   *
   * 功能：获取当前工厂的工作时间配置，包括班次、休息时间和利用率计算模式
   * API端点：GET /api/statistics/worktime
   *
   * 用途：
   * - 在页面上显示当前的工作时间配置
   * - 帮助用户理解统计数据的计算基础
   * - 为数据验证提供参考标准
   *
   * 数据内容：
   * - 每日工作开始时间
   * - 班次配置（白班、夜班等）
   * - 休息时间设置
   * - 利用率计算模式（OP1/OP2）
   *
   * 错误处理：
   * - 获取失败时不影响统计功能，只是不显示工作时间配置
   *
   * @returns {Promise<void>} - 异步操作，无返回值
   */
  const fetchWorkTimeSettings = async () => {
    try {
      const response = await fetch(`${API_HOST}/api/statistics/worktime`);
      if (!response.ok) throw new Error('获取工作时间设置失败');
      const data = await response.json();
      setWorkTimeSettings(data);
    } catch (err) {
      console.error('获取工作时间设置失败:', err);
      // 设置为null，UI会自动隐藏工作时间设置卡片
      setWorkTimeSettings(null);
    }
  };

  /**
   * 获取设备统计数据（当日统计模式）
   *
   * 功能：根据用户选择的设备和日期，获取相应的统计数据
   *
   * 业务逻辑：
   * - 当selectedDevice为'all'时：获取所有设备的汇总统计
   * - 当selectedDevice为具体设备ID时：获取单个设备的详细统计
   *
   * API端点：
   * - 汇总统计：GET /api/statistics/summary?start_date={date}&end_date={date}
   * - 单设备统计：GET /api/statistics/devices/{deviceId}?date={date}
   *
   * 状态管理：
   * - 开始时设置loading=true，显示加载动画
   * - 清除之前的错误信息
   * - 根据数据类型设置相应的状态变量
   * - 完成后设置loading=false
   *
   * 数据处理：
   * - 汇总模式：设置dailySummary，清空deviceStatistics
   * - 单设备模式：设置deviceStatistics数组，清空dailySummary
   *
   * 错误处理：
   * - 网络错误、API错误会设置error状态
   * - 错误信息会在UI中显示给用户
   *
   * @returns {Promise<void>} - 异步操作，无返回值
   */
  const fetchDeviceStatistics = async () => {
    setLoading(true);
    setError(null);

    try {
      if (selectedDevice === 'all') {
        // 获取所有设备的汇总统计数据
        const response = await fetch(
          `${API_HOST}/api/statistics/summary?start_date=${selectedDate}&end_date=${selectedDate}`
        );
        if (!response.ok) throw new Error('获取统计数据失败');
        const data = await response.json();
        setDailySummary(data);
        setDeviceStatistics([]); // 清空单设备数据
      } else {
        // 获取单个设备的详细统计数据
        const response = await fetch(
          `${API_HOST}/api/statistics/devices/${selectedDevice}?date=${selectedDate}`
        );
        if (!response.ok) throw new Error('获取设备统计失败');
        const data = await response.json();
        setDeviceStatistics([data]); // 包装成数组以保持数据结构一致
        setDailySummary(null); // 清空汇总数据
      }
    } catch (err) {
      // 设置用户友好的错误信息
      setError(err instanceof Error ? err.message : '获取数据失败');
    } finally {
      // 无论成功失败都要停止加载状态
      setLoading(false);
    }
  };

  // 获取多设备对比数据
  const fetchComparisonData = async () => {
    setLoading(true);
    setError(null);

    try {
      const deviceIds = selectedDevice === 'all' ? devices.map(d => d.id) : [selectedDevice];
      const response = await fetch(`${API_HOST}/api/statistics/devices/multiple`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          device_ids: deviceIds,
          start_date: dateRange.start,
          end_date: dateRange.end,
          group_by: 'device'
        })
      });

      if (!response.ok) throw new Error('获取对比数据失败');
      const data = await response.json();
      setDeviceStatistics(data.devices || []);
      setDailySummary(data.summary);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取对比数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchDevices();
    fetchWorkTimeSettings();
  }, []);

  // 当选择变化时重新获取数据
  useEffect(() => {
    if (viewMode === 'daily') {
      fetchDeviceStatistics();
    } else if (viewMode === 'comparison') {
      fetchComparisonData();
    }
  }, [selectedDate, selectedDevice, viewMode, dateRange]);

  // 手动刷新数据
  const handleRefresh = () => {
    if (viewMode === 'daily') {
      fetchDeviceStatistics();
    } else if (viewMode === 'comparison') {
      fetchComparisonData();
    }
  };

  // 手动运行统计任务
  const handleRunStatistics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_HOST}/api/statistics/scheduler/run?date=${selectedDate}`, {
        method: 'POST'
      });
      if (!response.ok) throw new Error('运行统计任务失败');

      // 等待一下再刷新数据
      setTimeout(() => {
        handleRefresh();
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '运行统计任务失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * ===== 数据格式化和显示工具函数 =====
   * 负责将原始数据转换为用户友好的显示格式
   */

  /**
   * 格式化百分比数值
   *
   * 功能：将数值转换为带百分号的字符串，保留一位小数
   *
   * 参数处理：
   * - undefined/null/NaN：返回 "0.0%"
   * - 正常数值：保留一位小数并添加百分号
   *
   * 使用场景：
   * - 设备利用率显示
   * - 产量达成率显示
   *
   * @param {number | undefined | null} value - 要格式化的数值
   * @returns {string} 格式化后的百分比字符串，如 "85.5%"
   *
   * @example
   * formatPercentage(85.567) // "85.6%"
   * formatPercentage(null)   // "0.0%"
   */
  const formatPercentage = (value: number | undefined | null) => {
    if (value === undefined || value === null || isNaN(value)) return '0.0%';
    return `${value.toFixed(1)}%`;
  };

  /**
   * 格式化时间显示（秒转小时分钟）
   *
   * 功能：将秒数转换为中文的小时分钟格式
   *
   * 转换规则：
   * - 输入单位：秒
   * - 输出格式：X小时Y分钟 或 X小时（当分钟为0时）
   *
   * 参数处理：
   * - undefined/null/NaN：返回 "0小时"
   * - 分钟为0：只显示小时，如 "8小时"
   * - 分钟不为0：显示完整格式，如 "8小时30分钟"
   *
   * 计算逻辑：
   * - 小时 = Math.floor(秒数 / 3600)
   * - 分钟 = Math.floor((秒数 % 3600) / 60)
   *
   * @param {number | undefined | null} seconds - 要格式化的秒数
   * @returns {string} 格式化后的时间字符串
   *
   * @example
   * formatTime(3600)  // "1小时"
   * formatTime(3900)  // "1小时5分钟"
   * formatTime(null)  // "0小时"
   */
  const formatTime = (seconds: number | undefined | null) => {
    if (seconds === undefined || seconds === null || isNaN(seconds)) return '0小时';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (minutes === 0) {
      return `${hours}小时`;
    } else {
      return `${hours}小时${minutes}分钟`;
    }
  };

  /**
   * 获取设备利用率对应的颜色样式
   *
   * 功能：根据利用率数值返回相应的Tailwind CSS颜色类名
   *
   * 颜色规则：
   * - >= 80%：绿色（优秀）
   * - >= 60%：黄色（良好）
   * - < 60%：红色（需要改进）
   * - 无效值：灰色（无数据）
   *
   * @param {number | undefined | null} rate - 利用率数值（0-100）
   * @returns {string} Tailwind CSS颜色类名
   *
   * @example
   * getUtilizationColor(85)   // "text-green-600"
   * getUtilizationColor(65)   // "text-yellow-600"
   * getUtilizationColor(45)   // "text-red-600"
   * getUtilizationColor(null) // "text-gray-600"
   */
  const getUtilizationColor = (rate: number | undefined | null) => {
    if (rate === undefined || rate === null || isNaN(rate)) return 'text-gray-600';
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * 获取产量达成率对应的颜色样式
   *
   * 功能：根据产量达成率数值返回相应的Tailwind CSS颜色类名
   *
   * 颜色规则：
   * - >= 100%：绿色（达成或超额完成）
   * - >= 80%：黄色（接近达成）
   * - < 80%：红色（未达成）
   * - 无效值：灰色（无数据）
   *
   * @param {number | undefined | null} rate - 产量达成率数值（0-100+）
   * @returns {string} Tailwind CSS颜色类名
   *
   * @example
   * getOutputColor(105)  // "text-green-600"
   * getOutputColor(85)   // "text-yellow-600"
   * getOutputColor(65)   // "text-red-600"
   * getOutputColor(null) // "text-gray-600"
   */
  const getOutputColor = (rate: number | undefined | null) => {
    if (rate === undefined || rate === null || isNaN(rate)) return 'text-gray-600';
    if (rate >= 100) return 'text-green-600';
    if (rate >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * ===== 数据质量验证函数 =====
   * 检查统计数据的合理性，发现潜在的数据质量问题
   */

  /**
   * 验证统计数据的合理性
   *
   * 功能：对统计数据进行多维度的合理性检查，识别数据异常
   *
   * 验证维度：
   * 1. 时间逻辑验证：运行时间不能超过总时间
   * 2. 物理限制验证：单日时间不能超过24小时
   * 3. 一致性验证：利用率与运行时间的匹配度
   *
   * 验证类型：
   * - error：严重错误，数据明显异常
   * - warning：警告，数据可疑但可能合理
   *
   * 支持的数据类型：
   * - DeviceStatistics：单设备统计数据
   * - DailyStatisticsSummary：多设备汇总数据
   *
   * @param {DeviceStatistics | DailyStatisticsSummary} stats - 要验证的统计数据
   * @returns {Array<{type: string, message: string}>} 验证结果数组
   *
   * @example
   * const validations = validateStatistics(deviceStats);
   * if (validations.length > 0) {
   *   // 显示验证警告
   * }
   */
  const validateStatistics = (stats: DeviceStatistics | DailyStatisticsSummary) => {
    const validations: Array<{ type: string, message: string }> = [];

    // 单设备数据验证
    if ('running_time' in stats && 'total_time' in stats) {
      const maxDailyTime = 24 * 3600; // 24小时的秒数

      // 验证1：运行时间不能超过总时间
      if (stats.running_time > stats.total_time) {
        validations.push({
          type: 'error',
          message: `运行时间(${formatTime(stats.running_time)})超过总时间(${formatTime(stats.total_time)})`
        });
      }

      // 验证2：总时间不应超过24小时（单日统计）
      if (stats.total_time > maxDailyTime) {
        validations.push({
          type: 'warning',
          message: `总时间(${formatTime(stats.total_time)})超过24小时，可能包含多天数据`
        });
      }

      // 验证3：运行时间不应超过24小时
      if (stats.running_time > maxDailyTime) {
        validations.push({
          type: 'error',
          message: `运行时间(${formatTime(stats.running_time)})超过24小时，数据异常`
        });
      }
    }

    // 汇总数据验证
    if ('total_running_time' in stats && 'total_devices' in stats) {
      const maxDailyTimePerDevice = 24 * 3600;
      const avgRunningTime = stats.total_running_time / stats.total_devices;

      // 验证4：平均运行时间不应超过24小时
      if (avgRunningTime > maxDailyTimePerDevice) {
        validations.push({
          type: 'error',
          message: `平均运行时间(${formatTime(avgRunningTime)})超过24小时，数据异常`
        });
      }

      // 验证5：利用率与运行时间的一致性检查
      const utilizationRate = stats.avg_utilization_rate || stats.average_utilization_rate || 0;
      const expectedRunningTime = (stats.total_time / stats.total_devices) * (utilizationRate / 100);
      const actualAvgRunningTime = stats.total_running_time / stats.total_devices;
      const timeDiff = Math.abs(expectedRunningTime - actualAvgRunningTime);

      // 如果预期运行时间与实际运行时间差异超过1小时，发出警告
      if (timeDiff > 3600) {
        validations.push({
          type: 'warning',
          message: `利用率与运行时间不匹配，预期运行时间${formatTime(expectedRunningTime)}，实际${formatTime(actualAvgRunningTime)}`
        });
      }
    }

    return validations;
  };

  return (
    <div className="flex">
      {/* 侧边导航栏 - 在非最大化状态下显示 */}
      {!isMaximized && <Sidebar mode={sidebarMode} activePage="device_usage_statistics" />}

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-h-screen">
        {/* 顶部标题栏 */}
        <Header
          companyInfo={companyInfo}
          isMaximized={isMaximized}
          toggleMaximize={toggleMaximize}
          toggleSidebar={toggleSidebar}
        />

        {/* 页面标题 */}
        <div className="bg-blue-900/20 py-2 px-6">
          <h2 className="text-xl font-bold text-white flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            设备使用情况统计
          </h2>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-6 space-y-6">
          {/* 控制面板 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* 设备选择 */}
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700">设备选择:</label>
                    <select
                      value={selectedDevice}
                      onChange={(e) => setSelectedDevice(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">全部设备</option>
                      {devices.map((device) => (
                        <option key={device.id} value={device.id}>
                          {device.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* 日期选择 */}
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700">统计日期:</label>
                    <Input
                      type="date"
                      value={selectedDate}
                      onChange={(e) => setSelectedDate(e.target.value)}
                      className="w-40"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button onClick={handleRefresh} disabled={loading} variant="outline" size="sm">
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    刷新
                  </Button>
                  <Button onClick={handleRunStatistics} disabled={loading} variant="outline" size="sm">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    运行统计
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 错误提示 */}
          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <p className="text-red-600">{error}</p>
              </CardContent>
            </Card>
          )}



          {/* 工作时间设置显示 */}
          {workTimeSettings && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  当前工作时间设置
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">每天开始时间</p>
                    <p className="font-medium">{workTimeSettings.day_start_time}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">利用率计算模式</p>
                    <Badge variant={workTimeSettings.utilization_mode === 'OP1' ? 'default' : 'secondary'}>
                      {workTimeSettings.utilization_mode}
                      {workTimeSettings.utilization_mode === 'OP1' ? ' (24小时)' : ' (24小时-休息时间)'}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">班次设置</p>
                    <div className="flex flex-wrap gap-1">
                      {workTimeSettings.shifts.filter(s => s.enabled).map((shift, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {shift.name} ({shift.start_time}-{shift.end_time})
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 主要内容区域 */}
          <Tabs value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="daily">当日统计</TabsTrigger>
              <TabsTrigger value="comparison">横向对比</TabsTrigger>
              <TabsTrigger value="shifts">班次统计</TabsTrigger>
            </TabsList>

            {/* 当日统计 */}
            <TabsContent value="daily" className="space-y-6">
              {loading ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                      <span className="ml-2 text-gray-600">加载中...</span>
                    </div>
                  </CardContent>
                </Card>
              ) : dailySummary ? (
                <>
                  {/* 数据验证结果 */}
                  {(() => {
                    const validations = validateStatistics(dailySummary);
                    return validations.length > 0 ? (
                      <Card className="border-yellow-200 bg-yellow-50">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-yellow-800 flex items-center">
                            <TrendingUp className="h-4 w-4 mr-2" />
                            数据验证结果
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {validations.map((validation, index) => (
                              <div key={index} className={`text-sm ${validation.type === 'error' ? 'text-red-600' : 'text-yellow-600'
                                }`}>
                                • {validation.message}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })()}

                  {/* 汇总统计显示 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">设备总数</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{dailySummary.total_devices || 0}</div>
                        <p className="text-xs text-gray-600 mt-1">台设备</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">平均利用率</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className={`text-2xl font-bold ${getUtilizationColor(dailySummary.average_utilization_rate || dailySummary.avg_utilization_rate)}`}>
                          {formatPercentage(dailySummary.average_utilization_rate || dailySummary.avg_utilization_rate)}
                        </div>
                        <p className="text-xs text-gray-600 mt-1">设备利用率</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">平均产量达成率</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className={`text-2xl font-bold ${getOutputColor(dailySummary.average_output_rate || dailySummary.avg_output_rate)}`}>
                          {formatPercentage(dailySummary.average_output_rate || dailySummary.avg_output_rate)}
                        </div>
                        <p className="text-xs text-gray-600 mt-1">产量达成率</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">运行时间统计</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div>
                            <div className="text-lg font-bold">总计: {formatTime(dailySummary.total_running_time)}</div>
                            <div className="text-sm text-gray-600">
                              平均: {formatTime(Math.round(dailySummary.total_running_time / dailySummary.total_devices))} / 台
                            </div>
                          </div>
                          <div className="text-xs text-gray-500">
                            {dailySummary.total_devices}台设备，总时长 {formatTime(dailySummary.total_time)}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </>
              ) : deviceStatistics.length > 0 ? (
                <>
                  {/* 单设备数据验证结果 */}
                  {deviceStatistics.map((stats) => {
                    const validations = validateStatistics(stats);
                    return validations.length > 0 ? (
                      <Card key={`validation-${stats.device_id}`} className="border-yellow-200 bg-yellow-50">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-yellow-800 flex items-center">
                            <TrendingUp className="h-4 w-4 mr-2" />
                            {stats.device_name} - 数据验证结果
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {validations.map((validation, index) => (
                              <div key={index} className={`text-sm ${validation.type === 'error' ? 'text-red-600' : 'text-yellow-600'
                                }`}>
                                • {validation.message}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })}

                  {/* 单设备统计显示 */}
                  {deviceStatistics.map((stats) => (
                    <Card key={stats.device_id}>
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <span>{stats.device_name}</span>
                          <Badge variant="outline">{stats.date}</Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="text-center">
                            <p className="text-sm text-gray-600">设备利用率</p>
                            <p className={`text-2xl font-bold ${getUtilizationColor(stats.utilization_rate)}`}>
                              {formatPercentage(stats.utilization_rate)}
                            </p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">产量达成率</p>
                            <p className={`text-2xl font-bold ${getOutputColor(stats.output_rate)}`}>
                              {formatPercentage(stats.output_rate)}
                            </p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">运行时间</p>
                            <p className="text-xl font-bold">{formatTime(stats.running_time)}</p>
                            <p className="text-xs text-gray-600">/ {formatTime(stats.total_time)}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600">产量</p>
                            <p className="text-xl font-bold">{stats.actual_output || 0}</p>
                            <p className="text-xs text-gray-600">/ {stats.planned_output || 0}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </>
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">暂无统计数据</p>
                      <p className="text-sm text-gray-500 mt-2">
                        请选择其他日期或点击"运行统计"生成数据
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* 横向对比 */}
            <TabsContent value="comparison" className="space-y-6">
              {loading ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                      <span className="ml-2 text-gray-600">加载中...</span>
                    </div>
                  </CardContent>
                </Card>
              ) : deviceStatistics.length > 0 ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2" />
                      设备对比统计 ({dateRange.start} 至 {dateRange.end})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">设备名称</th>
                            <th className="text-center p-2">利用率</th>
                            <th className="text-center p-2">产量达成率</th>
                            <th className="text-center p-2">运行时间</th>
                            <th className="text-center p-2">产量</th>
                          </tr>
                        </thead>
                        <tbody>
                          {deviceStatistics.map((stats) => (
                            <tr key={stats.device_id} className="border-b hover:bg-gray-50">
                              <td className="p-2 font-medium">{stats.device_name}</td>
                              <td className={`text-center p-2 font-bold ${getUtilizationColor(stats.utilization_rate)}`}>
                                {formatPercentage(stats.utilization_rate)}
                              </td>
                              <td className={`text-center p-2 font-bold ${getOutputColor(stats.output_rate)}`}>
                                {formatPercentage(stats.output_rate)}
                              </td>
                              <td className="text-center p-2">
                                {formatTime(stats.running_time)} / {formatTime(stats.total_time)}
                              </td>
                              <td className="text-center p-2">
                                {stats.actual_output || 0} / {stats.planned_output || 0}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">暂无对比数据</p>
                      <p className="text-sm text-gray-500 mt-2">
                        请调整日期范围或设备选择
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* 班次统计 */}
            <TabsContent value="shifts" className="space-y-6">
              {loading ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                      <span className="ml-2 text-gray-600">加载中...</span>
                    </div>
                  </CardContent>
                </Card>
              ) : deviceStatistics.length > 0 && deviceStatistics[0].shift_statistics ? (
                deviceStatistics.map((stats) => (
                  <Card key={stats.device_id}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>{stats.device_name} - 班次统计</span>
                        <Badge variant="outline">{stats.date}</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {stats.shift_statistics.map((shift, index) => (
                          <Card key={index} className="border border-gray-200">
                            <CardHeader className="pb-2">
                              <CardTitle className="text-lg">{shift.shift_name}</CardTitle>
                              <p className="text-sm text-gray-600">
                                {shift.start_time} - {shift.end_time}
                              </p>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">利用率:</span>
                                  <span className={`font-bold ${getUtilizationColor(shift.utilization_rate)}`}>
                                    {formatPercentage(shift.utilization_rate)}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">产量达成率:</span>
                                  <span className={`font-bold ${getOutputColor(shift.output_rate)}`}>
                                    {formatPercentage(shift.output_rate)}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">运行时间:</span>
                                  <span className="font-medium">{formatTime(shift.running_time)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">产量:</span>
                                  <span className="font-medium">{shift.actual_output || 0} / {shift.planned_output || 0}</span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">暂无班次统计数据</p>
                      <p className="text-sm text-gray-500 mt-2">
                        请选择具体设备查看班次统计
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
};

export default DeviceUsageStatisticsPage;
