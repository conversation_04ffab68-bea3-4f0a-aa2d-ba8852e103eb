'use client'; // 声明这是一个客户端组件

/**
 * 主页组件
 *
 * 该组件负责自动跳转到设备状态页面
 */
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * 主页组件
 *
 * 该组件负责自动跳转到设备状态页面
 */
export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // 自动跳转到设备状态页面
    router.replace('/status');
  }, [router]);

  // 显示跳转中的加载界面
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-900 text-white">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-lg">正在跳转到设备状态页面...</p>
        <p className="text-sm text-gray-400 mt-2">请稍候</p>
      </div>
    </div>
  );
}

