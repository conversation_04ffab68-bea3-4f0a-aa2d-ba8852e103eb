'use client';

import { getApiBaseUrl } from '@/config/api';

import { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { ChevronLeft, ChevronRight, Search } from 'lucide-react';

import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import DatePicker from '@/components/common/DatePicker';
import MultiDeviceSelector from '@/components/device/MultiDeviceSelector';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorAlert from '@/components/common/ErrorAlert';

import { useMaximize } from '@/hooks/useMaximize';
import { useSidebar } from '@/hooks/useSidebar';

import { Machine } from '@/types';

// 生产计划接口定义
interface ProductionPlan {
    plannedStartTime: string;
    plannedEndTime: string;
    productIndex: number;
    [key: string]: any; // 允许其他属性
}

export default function ProductionProgressPage() {
    // 状态
    const [machines, setMachines] = useState<Machine[]>([]);
    const [filteredMachines, setFilteredMachines] = useState<Machine[]>([]);
    const [selectedDeviceIds, setSelectedDeviceIds] = useState<string[]>([]);
    const [deviceFilter, setDeviceFilter] = useState('');
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [progressData, setProgressData] = useState<any[]>([]);
    const [deviceStatusHistory, setDeviceStatusHistory] = useState<Record<string, any[]>>({});
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [companyInfo, setCompanyInfo] = useState({ name: '', logo: '', supportContact: '', dateTime: '2023-01-01T00:00:00.000Z' });
    const [isRefreshing, setIsRefreshing] = useState(false); // 数据刷新状态
    const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null); // 最后刷新时间
    const [hasApiError, setHasApiError] = useState(false); // API获取数据失败状态

    // 侧边栏和最大化状态
    const { isMaximized, toggleMaximize } = useMaximize();
    const { sidebarMode, toggleSidebar } = useSidebar();

    // 时间轴起始小时（从早上8点开始，到第二天早上7点结束）
    const timeAxisStartHour = 8;

    // 加载设备列表
    useEffect(() => {
        const fetchMachines = async () => {
            try {
                // 使用统一的环境变量直接调用后端API
                const backendUrl = getApiBaseUrl();
                const apiUrl = `${backendUrl}/api/machines`;

                console.log(`🔗 [Production Progress] 直接调用后端API: ${apiUrl}`);

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    setMachines(data.machines || []);
                    setFilteredMachines(data.machines || []);
                }
            } catch (error) {
                console.error('获取设备列表失败:', error);
            }
        };

        const fetchCompanyInfo = async () => {
            try {
                // 使用统一的环境变量直接调用后端API
                const backendUrl = getApiBaseUrl();
                const apiUrl = `${backendUrl}/api/v3/info`;

                console.log(`🔗 [Production Progress] 直接调用后端API: ${apiUrl}`);

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('获取到公司信息:', data);
                    if (data && data.success && data.data && data.data.name) {
                        setCompanyInfo(prevInfo => ({
                            ...prevInfo,
                            name: data.data.name,
                            logo: data.data.logo || prevInfo.logo,
                            supportContact: data.data.supportContact || prevInfo.supportContact
                        }));
                    }
                }
            } catch (error) {
                console.error('获取公司信息失败:', error);
            }
        };

        fetchMachines();
        fetchCompanyInfo();
    }, []);

    // 获取设备状态颜色
    const getStatusColor = (status: string) => {
        const colorMap: Record<string, string> = {
            'producing': '#22c55e', // 生产中 - 绿色
            'idle': '#f59e0b',      // 空闲 - 琥珀色
            'fault': '#ef4444',     // 故障 - 红色
            'adjusting': '#3b82f6', // 调机 - 蓝色
            'shutdown': '#6b7280',  // 关机 - 灰色
            'disconnected': '#9ca3af', // 未连接 - 浅灰色
            'maintenance': '#8b5cf6', // 维护 - 紫色
            'debugging': '#ec4899'  // 调试 - 粉色
        };

        console.log('状态颜色映射:', status, colorMap[status]);
        return colorMap[status] || '#9ca3af';
    };

    // 获取状态名称
    const getStatusName = (status: string) => {
        const statusMap: Record<string, string> = {
            'producing': '生产中',
            'idle': '空闲',
            'fault': '故障',
            'adjusting': '调机',
            'shutdown': '关机',
            'disconnected': '未连接',
            'maintenance': '维护',
            'debugging': '调试'
        };

        return statusMap[status] || status;
    };

    // 加载设备状态历史数据 - 一次性获取所有设备数据
    const loadDeviceStatusHistory = useCallback(async (deviceIds: string[] = [], silent: boolean = false) => {
        try {
            // 格式化日期为 YYYY-MM-DD
            const formattedDate = format(selectedDate, 'yyyy-MM-dd');

            // 使用统一的环境变量直接调用后端API
            const backendUrl = getApiBaseUrl();
            let url = `${backendUrl}/api/devices/status-history?date=${formattedDate}`;
            if (deviceIds.length > 0) {
                url += `&devices=${deviceIds.join(',')}`;
            }

            console.log(`🔗 [Production Progress] 直接调用后端API: ${url}`);
            console.log('设备ID列表:', deviceIds);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                },
            });

            if (response.ok) {
                const data = await response.json();
                // 预期API返回格式: { statusHistory: { deviceId1: [...records], deviceId2: [...records] } }
                if (!silent) {
                    console.log('设备状态历史数据:', data.statusHistory);
                }

                // 确保每个设备都有状态历史数据
                if (data.statusHistory && deviceIds.length > 0) {
                    const updatedStatusHistory = { ...data.statusHistory };

                    // 检查每个设备是否有状态历史数据
                    deviceIds.forEach(deviceId => {
                        if (!updatedStatusHistory[deviceId]) {
                            if (!silent) {
                                console.log(`设备 ${deviceId} 没有状态历史数据，创建空数组`);
                            }
                            updatedStatusHistory[deviceId] = [];
                        }
                    });

                    setDeviceStatusHistory(updatedStatusHistory);
                } else {
                    setDeviceStatusHistory(data.statusHistory || {});
                }
            } else {
                console.error('获取设备状态历史失败:', response.status);
                setDeviceStatusHistory({});
                // 设置API错误状态为true
                setHasApiError(true);
            }
        } catch (error) {
            console.error('获取设备状态历史失败:', error);
            setDeviceStatusHistory({});
            // 设置API错误状态为true
            setHasApiError(true);
        }
    }, [selectedDate]);

    // 加载生产进度数据
    const loadProductionProgress = useCallback(async (silent: boolean = false) => {
        if (!silent) {
            setIsLoading(true);
        }
        setError(null);

        try {
            // 标记开始刷新
            if (silent) {
                setIsRefreshing(true);
            }

            // 格式化日期为 YYYY-MM-DD
            const formattedDate = format(selectedDate, 'yyyy-MM-dd');

            // 使用统一的环境变量直接调用后端API
            const backendUrl = getApiBaseUrl();
            let url = `${backendUrl}/api/production/progress?date=${formattedDate}`;
            if (selectedDeviceIds.length > 0) {
                url += `&devices=${selectedDeviceIds.join(',')}`;
            }

            console.log(`🔗 [Production Progress] 直接调用后端API: ${url}`);
            console.log('选中的设备IDs:', selectedDeviceIds);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                },
            });
            if (!response.ok) {
                console.error('API请求失败:', response.status, response.statusText);
                // 设置API错误状态为true
                setHasApiError(true);
                throw new Error(`获取生产进度数据失败: ${response.status}`);
            }

            const data = await response.json();
            console.log('API响应数据:', data);

            // API请求成功，设置错误状态为false
            setHasApiError(false);

            // 检查响应数据是否有效
            if (!data || !data.progressData) {
                console.warn('API返回的数据无效或为空');
                setProgressData([]);
            } else {
                // 只在调试模式下输出日志，减少控制台输出
                if (!silent) {
                    console.log('生产进度数据:', data.progressData);
                }

                setProgressData(data.progressData);

                // 如果API返回了公司信息，则更新公司信息
                if (data.companyInfo && data.companyInfo.name) {
                    setCompanyInfo(prevInfo => ({
                        ...prevInfo,
                        name: data.companyInfo.name,
                        logo: data.companyInfo.logo || prevInfo.logo,
                        supportContact: data.companyInfo.supportContact || prevInfo.supportContact
                    }));
                }
            }

            // 从生产进度数据中提取设备ID
            const deviceIds = data.progressData?.map((item: any) => item.deviceId) || [];

            if (!silent) {
                console.log('从生产进度数据中提取的设备ID:', deviceIds);
            }

            // 加载设备状态历史数据 - 使用从生产进度数据中提取的设备ID
            if (deviceIds.length > 0) {
                await loadDeviceStatusHistory(deviceIds, silent);
            } else {
                // 如果没有设备ID，则清空设备状态历史数据
                setDeviceStatusHistory({});
            }

            // 更新最后刷新时间
            setLastRefreshTime(new Date());
        } catch (error: any) {
            console.error('获取生产进度数据失败:', error);
            // 设置API错误状态为true
            setHasApiError(true);
            if (!silent) {
                setError(error.message || '获取生产进度数据失败');
            }
            // 即使出错也更新最后刷新时间，以便显示橙色时间戳
            setLastRefreshTime(new Date());
        } finally {
            if (!silent) {
                setIsLoading(false);
            }
            // 标记结束刷新
            if (silent) {
                setIsRefreshing(false);
            }
        }
    }, [selectedDate, selectedDeviceIds, loadDeviceStatusHistory]);

    // 初始加载和日期/设备选择变更时加载数据
    useEffect(() => {
        console.log('加载生产进度数据，设备选择:', selectedDeviceIds);
        loadProductionProgress();
    }, [loadProductionProgress, selectedDeviceIds]);

    // 自动刷新数据 - 每秒无感刷新
    useEffect(() => {
        // 只有在显示当前日期的数据时才自动刷新
        const isCurrentDate = format(selectedDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');

        if (!isCurrentDate) {
            return; // 如果不是当前日期，不自动刷新
        }

        // 创建自动刷新定时器
        const refreshTimer = setInterval(() => {
            // 无感刷新数据（静默模式）
            loadProductionProgress(true);
        }, 1000); // 每秒刷新一次

        // 清理函数
        return () => {
            clearInterval(refreshTimer);
        };
    }, [loadProductionProgress, selectedDate]);

    // 在客户端更新时间
    useEffect(() => {
        // 更新当前时间
        const updateTime = () => {
            const now = new Date();
            const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
            const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

            setCompanyInfo(prev => ({
                ...prev,
                dateTime: `${formattedDate} ${formattedTime}`
            }));
        };

        // 立即更新一次
        updateTime();

        // 每秒更新一次
        const timer = setInterval(updateTime, 1000);
        return () => clearInterval(timer);
    }, []);

    // 处理日期变化
    const handleDateChange = (date: Date) => {
        setSelectedDate(date);
    };

    // 切换到前一天
    const goToPreviousDay = () => {
        const prevDay = new Date(selectedDate);
        prevDay.setDate(prevDay.getDate() - 1);
        setSelectedDate(prevDay);
    };

    // 切换到后一天
    const goToNextDay = () => {
        const nextDay = new Date(selectedDate);
        nextDay.setDate(nextDay.getDate() + 1);
        setSelectedDate(nextDay);
    };

    // 处理设备筛选变化
    const handleDeviceFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setDeviceFilter(e.target.value);
    };

    // 处理设备选择变化
    const handleDeviceSelectionChange = (deviceIds: string[]) => {
        console.log('设备选择变更为:', deviceIds);
        setSelectedDeviceIds(deviceIds);
    };

    // 筛选设备
    useEffect(() => {
        let filtered = machines;

        // 应用文本筛选
        if (deviceFilter) {
            const searchTerm = deviceFilter.toLowerCase();
            filtered = filtered.filter(machine => (
                machine.id.toLowerCase().includes(searchTerm) ||
                machine.brand.toLowerCase().includes(searchTerm) ||
                machine.name.toLowerCase().includes(searchTerm) ||
                machine.location.toLowerCase().includes(searchTerm) ||
                machine.model.toLowerCase().includes(searchTerm)
            ));
        }

        setFilteredMachines(filtered);
    }, [machines, deviceFilter]);

    // 生成时间轴刻度，从08:00到07:00，确保07:00占满一格
    const generateTimeAxis = () => {
        const hours = [];
        for (let i = 0; i <= 23; i++) { // 生成24个小时的刻度，从08:00到07:00
            const hour = (timeAxisStartHour + i) % 24;
            hours.push(hour);
        }
        return hours;
    };

    const timeAxis = generateTimeAxis();

    // 获取产品背景色
    const getProductBackgroundColor = (productIndex: number) => {
        // 只使用两种颜色：蓝色和绿色
        const colors = ['#3b82f6', '#10b981'];
        return colors[productIndex % colors.length];
    };

    // 获取产品背景浅色版本
    const getProductLightBackgroundColor = (productIndex: number) => {
        // 浅色版本的蓝色和绿色
        const lightColors = ['#93c5fd', '#86efac'];
        return lightColors[productIndex % lightColors.length];
    };

    // 生成时间网格线 - 用于所有区域，确保与时间轴完全一致
    const renderTimeGrid = (zIndex = 1) => {
        // 创建与时间轴完全相同的网格布局
        return (
            <div className="absolute inset-0 right-4 pointer-events-none">
                <div className="h-full w-full grid" style={{ gridTemplateColumns: `repeat(${timeAxis.length}, 1fr)`, zIndex }}>
                    {timeAxis.map((_, index) => {
                        // 每4小时（整点）的线条更明显
                        const isMainHour = (index + 1) % 4 === 0;
                        return (
                            <div
                                key={index}
                                className={`${isMainHour ? '' : ''}`}
                                style={{
                                    borderRight: isMainHour ? '1px solid rgba(156, 163, 175, 0.5)' : '1px solid rgba(156, 163, 175, 0.3)',
                                    height: '100%'
                                }}
                            ></div>
                        );
                    })}
                </div>
            </div>
        );
    };



    // 计算进度条位置和宽度
    const calculateProgressBar = (plan: ProductionPlan) => {
        const startTime = new Date(plan.plannedStartTime);
        const endTime = new Date(plan.plannedEndTime);
        const now = new Date();

        // 计算计划开始时间相对于时间轴起始时间的位置（百分比）
        const dayStart = new Date(selectedDate);
        dayStart.setHours(timeAxisStartHour, 0, 0, 0);

        const dayEnd = new Date(selectedDate);
        dayEnd.setHours(timeAxisStartHour + 24, 0, 0, 0);

        const totalDayMinutes = (dayEnd.getTime() - dayStart.getTime()) / (60 * 1000);

        // 计算计划开始和结束时间相对于一天的位置
        const startMinutes = Math.max(0, (startTime.getTime() - dayStart.getTime()) / (60 * 1000));
        const endMinutes = Math.min(totalDayMinutes, (endTime.getTime() - dayStart.getTime()) / (60 * 1000));

        // 转换为百分比
        const startPercent = (startMinutes / totalDayMinutes) * 100;
        const widthPercent = ((endMinutes - startMinutes) / totalDayMinutes) * 100;

        // 计算当前完成进度
        const completionPercent = (plan.completedQuantity / plan.plannedQuantity) * 100;

        // 计算理论进度（基于当前时间）
        let theoreticalPercent = 0;
        if (now >= startTime && now <= endTime) {
            const elapsedMinutes = (now.getTime() - startTime.getTime()) / (60 * 1000);
            const totalPlanMinutes = (endTime.getTime() - startTime.getTime()) / (60 * 1000);
            theoreticalPercent = (elapsedMinutes / totalPlanMinutes) * 100;
        } else if (now > endTime) {
            theoreticalPercent = 100;
        }

        return {
            startPercent,
            widthPercent,
            completionPercent: Math.min(completionPercent, 100),
            theoreticalPercent: Math.min(theoreticalPercent, 100)
        };
    };

    return (
        <div className="flex">
            {/* 侧边导航栏 - 在非最大化状态下显示 */}
            {!isMaximized && <Sidebar mode={sidebarMode} activePage="production_progress" />}

            {/* 主内容区域 */}
            <main className="flex-1 flex flex-col min-h-screen">
                {/* 顶部标题栏 */}
                <Header
                    companyInfo={companyInfo}
                    isMaximized={isMaximized}
                    toggleMaximize={toggleMaximize}
                    toggleSidebar={toggleSidebar}
                />

                {/* 页面标题和筛选区域 */}
                <div className="bg-blue-900/20 py-2 px-6">
                    <h2 className="text-xl font-bold text-white flex items-center mb-3">
                        生产进度
                        {lastRefreshTime && (
                            <span className={`ml-2 text-xs flex items-center ${hasApiError ? 'text-amber-500' : 'text-gray-400'}`}>
                                最后更新: <span className="font-mono w-[4.5rem] inline-block text-right">{format(lastRefreshTime, 'HH:mm:ss')}</span>
                                {isRefreshing && (
                                    <span
                                        className={`ml-1 inline-block w-2 h-2 rounded-full animate-pulse ${hasApiError ? 'bg-amber-500' : 'bg-green-500'}`}
                                        title={hasApiError ? "数据刷新失败" : "数据刷新中"}
                                    ></span>
                                )}
                            </span>
                        )}
                    </h2>

                    {/* 筛选和日期选择 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* 设备多选选择器 */}
                            <MultiDeviceSelector
                                machines={machines}
                                selectedDeviceIds={selectedDeviceIds}
                                onDeviceSelectionChange={handleDeviceSelectionChange}
                                className="w-full"
                            />

                            {/* 设备文本筛选 */}
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <Search className="h-4 w-4 text-gray-400" />
                                </div>
                                <input
                                    type="text"
                                    placeholder="筛选设备 (编号, 名称, 位置...)"
                                    value={deviceFilter}
                                    onChange={handleDeviceFilterChange}
                                    className="bg-gray-800 text-white pl-10 pr-4 py-2 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        {/* 日期选择 */}
                        <div className="flex items-center justify-end bg-gray-800 rounded-md px-3 py-2">
                            <div className="flex items-center space-x-1">
                                <button
                                    onClick={goToPreviousDay}
                                    className="p-1 rounded-full hover:bg-gray-700"
                                    title="前一天"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </button>

                                <DatePicker
                                    selectedDate={selectedDate}
                                    onChange={handleDateChange}
                                    className="bg-gray-800 border border-gray-700 rounded-md px-2 py-0.5 text-xs"
                                />

                                <button
                                    onClick={goToNextDay}
                                    className="p-1 rounded-full hover:bg-gray-700"
                                    title="后一天"
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 状态标识栏 */}
                <div className="bg-gray-900 px-6 py-2 border-b border-gray-800">
                    <div className="flex flex-wrap gap-3">
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('producing') }}></div>
                            <span className="text-xs ml-1">生产中</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('idle') }}></div>
                            <span className="text-xs ml-1">空闲</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('fault') }}></div>
                            <span className="text-xs ml-1">故障</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('adjusting') }}></div>
                            <span className="text-xs ml-1">调机</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('shutdown') }}></div>
                            <span className="text-xs ml-1">关机</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('disconnected') }}></div>
                            <span className="text-xs ml-1">未连接</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('maintenance') }}></div>
                            <span className="text-xs ml-1">维护</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getStatusColor('debugging') }}></div>
                            <span className="text-xs ml-1">调试</span>
                        </div>
                    </div>
                </div>

                {/* 主内容区域 */}
                <div className="flex-1 overflow-auto">
                    {isLoading ? (
                        <LoadingSpinner message="加载生产进度数据中..." />
                    ) : error ? (
                        <ErrorAlert message={error} onRetry={loadProductionProgress} />
                    ) : (
                        <div className="h-full">
                            {/* 时间轴和生产进度表格 */}
                            <div className="relative">
                                {/* 全局时间网格背景 - 应用到整个内容区域，作为背景 */}
                                <div className="absolute top-0 left-0 right-0 bottom-0 z-[1] pointer-events-none">
                                    <div className="absolute top-0 left-48 right-0 bottom-0">
                                        {renderTimeGrid(1)}
                                    </div>
                                </div>

                                {/* 时间轴 */}
                                <div className="sticky top-0 z-20 bg-gray-900 border-b border-gray-700 flex">
                                    <div className="w-48 min-w-[12rem] bg-gray-900 border-r border-gray-700 flex items-center justify-center text-xs text-gray-400">
                                        机器/时间
                                    </div>
                                    <div className="flex-1 pr-4 relative">
                                        {/* 使用与其他区域完全相同的网格布局 */}
                                        <div className="grid" style={{ gridTemplateColumns: `repeat(${timeAxis.length}, 1fr)` }}>
                                            {timeAxis.map((hour, index) => {
                                                // 每4小时（整点）的线条更明显
                                                const isMainHour = (index + 1) % 4 === 0;
                                                return (
                                                    <div
                                                        key={index}
                                                        className={`text-center py-2 text-xs text-gray-400 border-r ${isMainHour ? 'border-gray-600 border-r-[1.5px]' : 'border-gray-700'}`}
                                                    >
                                                        {hour.toString().padStart(2, '0')}:00
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                </div>

                                {/* 设备和生产进度 */}
                                <div className="divide-y divide-gray-800">
                                    {progressData.length === 0 ? (
                                        <div className="py-8 text-center text-gray-400 relative z-20">
                                            没有找到生产进度数据
                                        </div>
                                    ) : (
                                        progressData.map((item) => (
                                            <div key={item.deviceId} className="flex flex-col">
                                                {/* 第一行：设备信息和计划信息组 */}
                                                <div className="flex">
                                                    {/* 设备信息 - 居中对齐 */}
                                                    <div className="w-48 min-w-[12rem] p-2 bg-gray-900 border-r border-gray-700 flex items-center justify-center z-10">
                                                        <div className="text-center w-full">
                                                            <div className="font-medium">{item.deviceName}</div>
                                                            <div className="text-xs text-gray-400">{item.deviceCode}</div>
                                                        </div>
                                                    </div>

                                                    {/* 计划信息组（顶部） */}
                                                    <div className="flex-1 relative h-20 bg-gray-950/80 pr-4 z-10">
                                                        {/* 设备行专用时间网格 */}
                                                        <div className="absolute inset-0 z-[1] pointer-events-none">
                                                            {renderTimeGrid(1)}
                                                        </div>


                                                        {item.plans && item.plans.length > 0 ? (
                                                            // 有计划数据时显示
                                                            item.plans.map((plan: ProductionPlan, index: number) => {
                                                                const { startPercent, widthPercent, completionPercent, theoreticalPercent } = calculateProgressBar(plan);
                                                                const bgColor = getProductBackgroundColor(plan.productIndex);
                                                                const lightBgColor = getProductLightBackgroundColor(plan.productIndex);

                                                                return (
                                                                    <div
                                                                        key={index}
                                                                        className="absolute top-0 h-full flex flex-col z-20"
                                                                        style={{
                                                                            left: `${startPercent}%`,
                                                                            width: `${widthPercent}%`,
                                                                        }}
                                                                    >
                                                                        {/* 背景 - 计划时间范围 */}
                                                                        <div
                                                                            className="absolute inset-0 opacity-30"
                                                                            style={{ backgroundColor: lightBgColor }}
                                                                        ></div>

                                                                        {/* 垂直排列的内容区域 */}
                                                                        <div className="relative h-full flex flex-col justify-between px-0 pt-0.5 pb-0 text-xs overflow-hidden">
                                                                            {/* 文字容器的背景 - 使用与产品相匹配的颜色 */}
                                                                            <div
                                                                                className="absolute top-0 left-0 right-0 h-12 z-0"
                                                                                style={{ backgroundColor: bgColor, opacity: 0.7 }}
                                                                            ></div>

                                                                            {/* 1. 物料信息 - 使用单行显示并添加工具提示 */}
                                                                            <div className="mb-1 px-1 z-10 relative">
                                                                                <div className="truncate whitespace-nowrap py-0.5 px-1" title={`编号: ${plan.materialCode} 名称: ${plan.materialName} 工单: ${plan.workOrder}`}>
                                                                                    <span className="text-gray-400">编号:</span> <span className="font-medium">{plan.materialCode}</span>
                                                                                    <span className="text-gray-400 ml-2">名称:</span> {plan.materialName}
                                                                                    <span className="text-gray-400 ml-2">工单:</span> {plan.workOrder}
                                                                                </div>
                                                                            </div>

                                                                            {/* 2. 计划信息 - 使用单行显示并添加工具提示 */}
                                                                            <div className="mb-2 px-1 z-10 relative">
                                                                                <div className="flex justify-between whitespace-nowrap py-0.5 px-1" title={`产量: ${plan.completedQuantity}/${plan.plannedQuantity} (${plan.completionPercentage}%) 操作员: ${plan.operator}`}>
                                                                                    <span className="truncate">产量: {plan.completedQuantity}/{plan.plannedQuantity}</span>
                                                                                    <span className="truncate ml-2">操作员: {plan.operator}</span>
                                                                                </div>
                                                                            </div>

                                                                            {/* 3. 进度条组 - 紧接在计划信息下方 */}
                                                                            <div className="flex flex-col space-y-1 mb-0">
                                                                                {/* 进度条容器 - 占满整个区域 */}
                                                                                <div className="space-y-0 mx-[-1px]">
                                                                                    {/* 3.1 计划理论进度条 */}
                                                                                    <div className="h-3 w-full relative bg-gray-800/70 rounded-none overflow-hidden z-[5]">
                                                                                        <div
                                                                                            className="absolute top-0 left-0 h-full bg-white opacity-50 z-0"
                                                                                            style={{ width: `${theoreticalPercent}%` }}
                                                                                        ></div>
                                                                                        <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                                                                                            计划:{Math.round(theoreticalPercent)}%
                                                                                        </div>
                                                                                    </div>

                                                                                    {/* 3.2 实际生产进度条 */}
                                                                                    <div className="h-3 w-full relative bg-gray-900/70 rounded-none overflow-hidden mt-[-1px] z-[5]">
                                                                                        <div
                                                                                            className="absolute top-0 left-0 h-full z-0"
                                                                                            style={{
                                                                                                width: `${completionPercent}%`,
                                                                                                backgroundColor: bgColor,
                                                                                                boxShadow: 'none'
                                                                                            }}
                                                                                        ></div>
                                                                                        <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                                                                                            产量:{Math.round(completionPercent)}%
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })
                                                        ) : (
                                                            // 没有计划数据时显示空的进度条
                                                            <div className="h-full flex flex-col justify-between px-0 pt-0.5 pb-0">
                                                                {/* 文字容器的背景 - 使用默认蓝色 */}
                                                                <div className="absolute top-0 left-0 right-0 bg-blue-600/70 h-12 z-0"></div>

                                                                {/* 添加一个不透明的背景容器 */}
                                                                <div className="mb-1 px-1 z-10 relative">
                                                                    <div className="py-0.5 px-1 text-center">
                                                                        无计划数据
                                                                    </div>
                                                                </div>

                                                                <div className="flex flex-col space-y-1 mb-0 z-10 relative">
                                                                    {/* 进度条容器 - 占满整个区域 */}
                                                                    <div className="space-y-0 mx-[-2px]">
                                                                        {/* 空的计划理论进度条 */}
                                                                        <div className="h-3 w-full relative bg-gray-800/70 rounded-none overflow-hidden z-[5]">
                                                                            <div className="absolute top-0 left-0 h-full bg-white opacity-50 z-0" style={{ width: '0%' }}></div>
                                                                            <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                                                                                计划:0%
                                                                            </div>
                                                                        </div>

                                                                        {/* 空的实际生产进度条 */}
                                                                        <div className="h-3 w-full relative bg-gray-900/70 rounded-none overflow-hidden mt-[-1px] z-[5]">
                                                                            <div className="absolute top-0 left-0 h-full bg-blue-600 z-0" style={{ width: '0%', boxShadow: 'none' }}></div>
                                                                            <div className="absolute inset-0 flex items-center justify-center text-[10px] z-10">
                                                                                产量:0%
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* 设备状态条组（底部） - 贯穿整个24小时时间线 */}
                                                <div className="flex mt-0 py-1">
                                                    {/* 设备信息 - 空白区域 */}
                                                    <div className="w-48 min-w-[12rem] bg-gray-900 border-r border-gray-700 z-10">
                                                    </div>

                                                    {/* 设备状态变化条 - 贯穿整个24小时 */}
                                                    <div className="flex-1 pr-4 z-10">
                                                        {/* 使用相对定位容器 */}
                                                        <div className="relative h-6 bg-gray-800/70 z-10">
                                                            {/* 添加竖线 - 使用绝对定位 */}
                                                            <div className="absolute inset-0 grid" style={{ gridTemplateColumns: `repeat(${timeAxis.length}, 1fr)`, zIndex: 1 }}>
                                                                {timeAxis.map((_, index) => {
                                                                    const isMainHour = (index + 1) % 4 === 0;
                                                                    return (
                                                                        <div
                                                                            key={index}
                                                                            className={`border-r h-full ${isMainHour ? 'border-gray-600' : 'border-gray-700'}`}
                                                                        ></div>
                                                                    );
                                                                })}
                                                            </div>


                                                            {/* 设备状态历史数据 - 使用相对定位的容器 */}
                                                            <div className="relative col-span-full h-full">
                                                                {deviceStatusHistory[item.deviceId]?.map((statusRecord, idx) => {
                                                                    // 计算状态记录的开始和结束时间
                                                                    const startTime = new Date(statusRecord.startTime);
                                                                    const endTime = new Date(statusRecord.endTime);

                                                                    // 计算一天的开始时间（从timeAxisStartHour开始）
                                                                    const dayStart = new Date(selectedDate);
                                                                    dayStart.setHours(timeAxisStartHour, 0, 0, 0);

                                                                    // 计算一天的结束时间（到第二天的timeAxisStartHour）
                                                                    const dayEnd = new Date(selectedDate);
                                                                    dayEnd.setHours(timeAxisStartHour + 24, 0, 0, 0);

                                                                    // 计算总分钟数
                                                                    const totalMinutes = (dayEnd.getTime() - dayStart.getTime()) / (60 * 1000);

                                                                    // 计算状态开始和结束时间相对于一天开始的分钟数
                                                                    const startMinutes = Math.max(0, (startTime.getTime() - dayStart.getTime()) / (60 * 1000));
                                                                    const endMinutes = Math.min(totalMinutes, (endTime.getTime() - dayStart.getTime()) / (60 * 1000));

                                                                    // 计算位置和宽度（百分比）
                                                                    const left = (startMinutes / totalMinutes) * 100;
                                                                    const width = ((endMinutes - startMinutes) / totalMinutes) * 100;

                                                                    // 如果宽度太小，不显示
                                                                    if (width <= 0) return null;

                                                                    return (
                                                                        <div
                                                                            key={idx}
                                                                            className="absolute top-0 h-full z-20"
                                                                            style={{
                                                                                left: `${left}%`,
                                                                                width: `${width}%`,
                                                                                backgroundColor: getStatusColor(statusRecord.status),
                                                                                transition: 'left 0.3s ease, width 0.3s ease',
                                                                                maxWidth: 'calc(100% - 2px)' // 确保状态条不会超出容器
                                                                            }}
                                                                            title={`${getStatusName(statusRecord.status)}: ${startTime.toLocaleTimeString()} - ${endTime.toLocaleTimeString()}`}
                                                                        >
                                                                            {width > 5 && (
                                                                                <div className="absolute inset-0 flex items-center justify-center text-[8px] text-white font-medium overflow-hidden">
                                                                                    {getStatusName(statusRecord.status)}
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>

                                                            {(!deviceStatusHistory[item.deviceId] || deviceStatusHistory[item.deviceId]?.length === 0) && (
                                                                <div className="col-span-full flex items-center justify-center text-[9px] text-gray-400 z-20 h-full">
                                                                    无状态记录
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </main>
        </div>
    );
}
