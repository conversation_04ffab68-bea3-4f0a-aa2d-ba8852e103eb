/**
 * 根布局组件
 *
 * 该文件定义了应用程序的根布局结构
 * 包含全局样式、元数据和基本HTML结构
 */
import './globals.css'; // 导入全局CSS样式
import type { Metadata } from 'next';

/**
 * 应用程序元数据
 * 定义页面标题、描述等SEO相关信息
 */
export const metadata: Metadata = {
  title: '智能制造系统', // 页面标题
  description: '生产设备实时状态监控系统', // 页面描述
};

/**
 * 根布局组件
 *
 * @param children - 子组件，将被包裹在根布局中
 * @returns 完整的HTML结构
 *
 * 该组件为整个应用提供基础HTML结构和全局样式
 * 所有页面内容都将作为children渲染在此结构内
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  {/* 设置语言为中文 */ }
  return (
    <html lang="zh">
      <body className="bg-gray-900 text-white min-h-screen">
        {/* 渲染子组件（页面内容） */}
        {children}
      </body>
    </html>
  );
}
