'use client';

import { getApiBaseUrl } from '@/config/api';

/**
 * 设备利用率机器页面 V2
 *
 * 功能特点：
 * - 后端统一数据处理：所有计算逻辑在后端完成
 * - 单一API调用：一次请求获取页面所需全部数据
 * - 前端专注渲染：移除复杂的数据处理逻辑
 * - 性能优化：减少前端计算负担和API调用次数
 * - 界面布局：与原版本完全一致的布局结构
 *
 * 数据流程：
 * 1. 前端发起单个API请求到 /api/utilization/machine-v2
 * 2. 后端返回完整的页面数据（概览+排名+设置）
 * 3. 前端直接渲染数据，无需额外计算
 *
 * 与V1版本的区别：
 * - V1：前端多次API调用 + 复杂数据处理 + 计算概览统计
 * - V2：前端单次API调用 + 直接数据渲染 + 后端预处理
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, ArrowLeftIcon, PieChart, RefreshCw } from 'lucide-react';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import DeviceRanking from '@/components/utilization/DeviceRanking';

// 定义数据类型接口 - 适配新API结构
interface DeviceUtilizationV3 {
  utilization: number;           // 利用率百分比
  running_time: number;          // 运行时间（秒）
  work_time: number;             // 工作时间（秒）
  total_time: number;            // 总时间（秒）
  status_summary: {              // 状态汇总
    production: number;
    fault: number;
    idle: number;
    shutdown: number;
    unknown?: number;
  };
  device: {                      // 设备信息
    name: string;
    device_id: string;
    location: string;
    brand: string;
    model: string;
    data_type: string;
    // ... 其他设备字段
  };
}

interface MachinePageV2Data {
  success: boolean;
  message: string;
  data: {
    date: string;
    overall_utilization: number;
    device_count: number;
    device_utilizations: DeviceUtilizationV3[];
    utilization_zones: Array<{
      name: string;
      min_rate: number;
      max_rate: number;
      device_count: number;
      percentage: number;
      color: string;
    }>;
    work_time_settings: any;
    last_updated: string;
    working_time: number;
    total_running_time: number;
    total_status_summary: any;
  };
}

export default function DeviceUtilizationMachineV2() {
  // 路由和查询参数
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryDate = searchParams?.get('date') || new Date().toISOString().split('T')[0];

  // APP布局状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);
  const [isMaximized, setIsMaximized] = useState(false);
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '',
    dateTime: '',
    support: '',
    support_info: ''
  });

  // 数据状态管理
  const [data, setData] = useState<MachinePageV2Data | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCalculationExpanded, setIsCalculationExpanded] = useState(false);

  // 在客户端初始化时从localStorage加载侧边栏状态
  useEffect(() => {
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        setSidebarMode(mode);
      } catch (error) {
        console.error('加载侧边栏状态失败:', error);
      }
    };
    loadSidebarMode();
  }, []);

  /**
   * 获取站点信息
   * 从API获取站点基本信息（名称、技术支持等）
   */
  const fetchSiteInfo = useCallback(async () => {
    try {
      console.log('🌐 开始获取站点信息...');
      const API_HOST = getApiBaseUrl();
      const response = await fetch(`${API_HOST}/api/v3/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`站点信息API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 站点信息API响应:', result);

      if (result.success && result.data) {
        // 更新公司信息，保留现有的dateTime
        setCompanyInfo(prev => ({
          ...prev,
          name: result.data.name || '智能制造系统',
          support: result.data.support || '',
          support_info: result.data.support_info || ''
        }));
        console.log('✅ 站点信息更新成功:', {
          name: result.data.name,
          support: result.data.support,
          support_info: result.data.support_info
        });
      } else {
        console.warn('⚠️ 站点信息API返回数据格式异常:', result);
      }
    } catch (error) {
      console.error('❌ 获取站点信息失败:', error);
      // 设置默认值
      setCompanyInfo(prev => ({
        ...prev,
        name: '智能制造系统',
        support: '',
        support_info: ''
      }));
    }
  }, []);

  // 切换侧边栏显示模式
  const toggleSidebar = useCallback(async () => {
    try {
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');
      const nextMode = getNextSidebarMode(sidebarMode);
      saveSidebarMode(nextMode);
      setSidebarMode(nextMode);
      setTimeout(() => {
        setCompanyInfo(prev => ({ ...prev }));
      }, 50);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  // 切换最大化状态
  const toggleMaximize = useCallback(() => {
    setIsMaximized(prev => {
      const newState = !prev;
      if (newState) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }
      return newState;
    });
  }, []);

  // 监听全屏变化事件
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  // 在客户端更新时间
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };
    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 格式化日期显示
  const formatDateDisplay = (dateStr: string): string => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  };

  // 格式化利用率百分比
  const formatUtilizationRate = (rate: number): string => {
    return `${rate.toFixed(1)}%`;
  };

  /**
   * 将新API数据转换为DeviceRanking组件期望的格式
   */
  const convertToDeviceRankingFormat = (apiData: any) => {
    if (!apiData?.device_utilizations) return null;

    // 转换设备数据格式
    const devices = apiData.device_utilizations.map((item: DeviceUtilizationV3, index: number) => ({
      device_id: item.device.device_id,
      device_name: item.device.name,
      device_code: item.device.device_id, // 使用device_id作为code
      location: item.device.location,
      brand: item.device.brand || item.device.data_type,
      model: item.device.model,
      utilization_rate: item.utilization,
      status_composition: {
        production: {
          duration: item.status_summary.production,
          percentage: (item.status_summary.production / item.total_time) * 100,
          color: '#22c55e'
        },
        idle: {
          duration: item.status_summary.idle,
          percentage: (item.status_summary.idle / item.total_time) * 100,
          color: '#f59e0b'
        },
        fault: {
          duration: item.status_summary.fault,
          percentage: (item.status_summary.fault / item.total_time) * 100,
          color: '#ef4444'
        },
        shutdown: {
          duration: item.status_summary.shutdown + (item.status_summary.unknown || 0),
          percentage: ((item.status_summary.shutdown + (item.status_summary.unknown || 0)) / item.total_time) * 100,
          color: '#6b7280'
        }
      },
      total_working_time: item.work_time / 3600, // 转换为小时
      productive_time: item.running_time / 3600,  // 转换为小时
      idle_time: item.status_summary.idle / 3600,
      fault_time: item.status_summary.fault / 3600,
      rank: index + 1
    }));

    // 状态图例
    const status_legend = [
      { status: 'production', label: '生产', color: '#22c55e' },
      { status: 'idle', label: '空闲', color: '#f59e0b' },
      { status: 'fault', label: '故障', color: '#ef4444' },
      { status: 'shutdown', label: '停机', color: '#6b7280' },
    ];

    return {
      date: apiData.date,
      devices,
      status_legend,
      work_time_settings: apiData.work_time_settings,
      last_updated: apiData.last_updated
    };
  };

  /**
   * 获取机器页面V2数据 - 使用新的V3 API
   * 单一API调用获取所有页面数据
   */
  const fetchMachinePageData = useCallback(async (date: string, silent = false) => {
    if (!silent) {
      setLoading(true);
    }
    setError(null);

    try {
      console.log(`🚀 [Machine V2] 开始获取数据，日期: ${date}`);

      // 使用新的V3 API
      const API_BASE_URL = getApiBaseUrl();
      const response = await fetch(`${API_BASE_URL}/api/v3/utilization/machines?date=${date}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();

      console.log(`✅ [Machine V2] 数据获取成功:`, {
        总体利用率: responseData.data?.overall_utilization?.toFixed(1) + '%',
        设备数量: responseData.data?.device_count,
        计算模式: responseData.data?.work_time_settings?.utilization_mode,
        工作时长: (responseData.data?.working_time / 3600)?.toFixed(1) + '小时'
      });

      setData(responseData);

    } catch (err) {
      console.error('❌ [Machine V2] 数据获取失败:', err);
      setError(err instanceof Error ? err.message : '获取数据失败');
    } finally {
      if (!silent) {
        setLoading(false);
      }
    }
  }, []);

  /**
   * 刷新数据
   */
  const handleRefresh = useCallback(() => {
    fetchMachinePageData(queryDate);
  }, [queryDate, fetchMachinePageData]);

  /**
   * 无感刷新数据
   */
  const silentRefresh = useCallback(() => {
    fetchMachinePageData(queryDate, true);
  }, [queryDate, fetchMachinePageData]);

  // 组件挂载时加载数据
  useEffect(() => {
    fetchSiteInfo(); // 获取站点信息
    fetchMachinePageData(queryDate);
  }, [queryDate, fetchMachinePageData, fetchSiteInfo]);

  // 定期无感刷新（每30秒）
  useEffect(() => {
    const interval = setInterval(silentRefresh, 30000);
    return () => clearInterval(interval);
  }, [silentRefresh]);

  /**
   * 返回仪表板（新标签页打开）
   */
  const handleBackToDashboard = useCallback(() => {
    window.open('/device_utilization_dashboard_v2', '_blank');
  }, []);



  // 渲染利用率统计图表 - 适配新API数据结构
  const renderUtilizationChart = () => {
    const apiData = data?.data;

    return (
      <Card className="bg-gray-800 border-gray-700 mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg text-white flex items-center gap-2">
            📊 利用率统计图表
            {apiData && (
              <Badge variant="secondary" className="bg-blue-600 text-white text-xs">
                {formatUtilizationRate(apiData.overall_utilization)}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {loading && (
            <div className="flex flex-col items-center justify-center h-32 text-gray-400">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mb-2"></div>
              <p className="text-sm">正在加载统计数据...</p>
            </div>
          )}

          {!loading && apiData && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {/* 总体利用率 */}
              <div className="text-center bg-gray-700/50 rounded-lg p-3">
                <div className="text-2xl font-bold text-blue-400 mb-1">
                  {formatUtilizationRate(apiData.overall_utilization)}
                </div>
                <div className="text-xs text-gray-400">总体利用率</div>
                <div className="text-xs text-gray-500 mt-1">
                  {apiData.device_count} 台设备
                </div>
              </div>

              {/* 利用率区间统计 */}
              {apiData.utilization_zones.map((zone, index) => {
                // 根据区间名称显示固定的范围说明
                let rangeText = '';
                if (zone.name === '高利用率') {
                  rangeText = '>80%';
                } else if (zone.name === '中利用率') {
                  rangeText = '50%-80%';
                } else if (zone.name === '低利用率') {
                  rangeText = '<50%';
                }

                return (
                  <div key={index} className="text-center bg-gray-700/50 rounded-lg p-3">
                    <div
                      className="text-xl font-bold mb-1"
                      style={{ color: zone.color }}
                    >
                      {zone.device_count}
                    </div>
                    <div className="text-xs text-gray-400">{zone.name}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {rangeText}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {!loading && !apiData && (
            <div className="flex flex-col items-center justify-center h-32 text-gray-400">
              <p className="text-sm">暂无统计数据</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // 渲染主要内容
  return (
    <div className="flex">
      {/* 侧边导航栏 - 在非最大化状态下显示 */}
      {!isMaximized && <Sidebar mode={sidebarMode} activePage="device_utilization_dashboard_v2" />}

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-h-screen">
        {/* 顶部标题栏 */}
        <Header
          companyInfo={companyInfo}
          isMaximized={isMaximized}
          toggleMaximize={toggleMaximize}
          toggleSidebar={toggleSidebar}
        />

        {/* 页面标题栏 */}
        <div className="bg-blue-900/20 border-b border-gray-700 flex items-center justify-between py-3 px-6">
          <div className="flex items-center gap-4">
            {/* 返回按钮 */}
            <Button
              onClick={handleBackToDashboard}
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              返回仪表板
            </Button>

            {/* 页面标题 */}
            <h2 className="text-xl font-bold text-white flex items-center">
              <PieChart className="mr-2 h-5 w-5" />
              设备利用率详细分析
              <Badge variant="secondary" className="bg-green-600 text-white text-xs ml-2">
                {formatDateDisplay(queryDate)}
              </Badge>
            </h2>
          </div>

          {/* 页面信息 */}
          <div className="flex items-center gap-4 text-sm text-gray-300">
            <div className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4" />
              <span>查询日期: {queryDate}</span>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 p-6">
          {/* 利用率统计图表 */}
          {renderUtilizationChart()}

          {/* 设备排名表 - 左右分布 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 利用率最高设备 */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-white flex items-center gap-2">
                  📈 利用率最高设备
                  {data?.data && (
                    <Badge variant="secondary" className="bg-blue-600 text-white text-xs">
                      排名
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {loading && (
                  <div className="flex flex-col items-center justify-center h-32 text-gray-400">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mb-2"></div>
                    <p className="text-sm">正在加载排名数据...</p>
                  </div>
                )}

                {!loading && data?.data && (() => {
                  const deviceRanking = convertToDeviceRankingFormat(data.data);
                  return deviceRanking && (
                    <DeviceRanking
                      deviceRanking={deviceRanking}
                      isRealtime={false}
                      displayLimit={-1}
                      sortOrder="desc"
                      title=""
                      showCard={false}
                      loading={false}
                      queryDate={queryDate}
                    />
                  );
                })()}

                {!loading && !data?.data && (
                  <div className="flex flex-col items-center justify-center h-32 text-gray-400">
                    <p className="text-sm">暂无排名数据</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 利用率最低设备 */}
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-white flex items-center gap-2">
                  📉 利用率最低设备
                  {data?.data && (
                    <Badge variant="secondary" className="bg-red-600 text-white text-xs">
                      排名
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {loading && (
                  <div className="flex flex-col items-center justify-center h-32 text-gray-400">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mb-2"></div>
                    <p className="text-sm">正在加载排名数据...</p>
                  </div>
                )}

                {!loading && data?.data && (() => {
                  const deviceRanking = convertToDeviceRankingFormat(data.data);
                  return deviceRanking && (
                    <DeviceRanking
                      deviceRanking={deviceRanking}
                      isRealtime={false}
                      displayLimit={-1}
                      sortOrder="asc"
                      title=""
                      showCard={false}
                      loading={false}
                      queryDate={queryDate}
                    />
                  );
                })()}

                {!loading && !data?.data && (
                  <div className="flex flex-col items-center justify-center h-32 text-gray-400">
                    <p className="text-sm">暂无排名数据</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 帮助按钮 - 总体利用率计算过程 */}
          {data?.data && (
            <div className="mt-6 p-4 bg-gray-750 rounded-lg">
              <div className="flex items-center justify-end mb-3">
                <button
                  onClick={() => setIsCalculationExpanded(!isCalculationExpanded)}
                  className="text-gray-400 hover:text-white transition-colors p-1 rounded"
                  title={isCalculationExpanded ? "收起帮助信息" : "展开帮助信息"}
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </button>
              </div>

              {/* 总体利用率计算过程显示 - 可折叠 */}
              {isCalculationExpanded && (
                <div className="space-y-4">
                  <h4 className="text-white font-medium">总体利用率计算</h4>
                  {/* 设备数量和总体利用率 */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-gray-750 p-4 rounded-lg">
                      <p className="text-gray-400 text-sm">设备总数</p>
                      <p className="text-blue-400 font-bold text-xl">{data.data.device_count} 台</p>
                    </div>
                    <div className="bg-gray-750 p-4 rounded-lg">
                      <p className="text-gray-400 text-sm">总体利用率</p>
                      <p className="text-green-400 font-bold text-xl">{formatUtilizationRate(data.data.overall_utilization)}</p>
                    </div>
                    <div className="bg-gray-750 p-4 rounded-lg">
                      <p className="text-gray-400 text-sm">计算模式</p>
                      <p className="text-yellow-400 font-medium">
                        {data.data.work_time_settings.utilization_mode}
                        {data.data.work_time_settings.utilization_mode === 'OP1' ? ' (24小时)' : ' (实际工作时长)'}
                      </p>
                    </div>
                    <div className="bg-gray-750 p-4 rounded-lg">
                      <p className="text-gray-400 text-sm">工作开始时间</p>
                      <p className="text-blue-400 font-medium">{data.data.work_time_settings.day_start_time}</p>
                    </div>
                  </div>

                  {/* 休息时间显示 - 仅OP2模式 */}
                  {data.data.work_time_settings.utilization_mode === 'OP2' && data.data.work_time_settings.rest_periods && data.data.work_time_settings.rest_periods.length > 0 && (
                    <div className="mb-6 p-4 bg-gray-750 rounded-lg">
                      <div className="text-gray-400 text-sm">
                        <p className="mb-2 font-medium">休息时间配置:</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 text-xs">
                          {(data.data.work_time_settings.rest_periods || [])
                            .filter((period: any) => period.enabled)
                            .map((period: any, index: number) => {
                              // 计算休息时间长度
                              const startTime = new Date(`2000-01-01T${period.start_time}:00`);
                              const endTime = new Date(`2000-01-01T${period.end_time}:00`);
                              const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

                              return (
                                <div key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                                  <span className="text-gray-300">• {period.name}:</span>
                                  <span className="text-yellow-400 font-medium">
                                    {period.start_time}-{period.end_time} ({durationMinutes}分钟)
                                  </span>
                                </div>
                              );
                            })}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 计算公式说明 */}
                  <div className="bg-gray-750 p-4 rounded-lg">
                    <div className="text-gray-400 text-sm space-y-3">
                      <p className="font-medium text-white">总体利用率计算公式:</p>
                      <div className="text-xs space-y-2 pl-4 border-l-2 border-blue-500">
                        <p>• <span className="text-blue-400">总体利用率</span> = <span className="text-green-400">所有设备总运行时间</span> ÷ <span className="text-yellow-400">所有设备总工作时间</span> × 100%</p>
                        <p>• <span className="text-blue-400">单设备利用率</span> = <span className="text-green-400">生产时间</span> ÷ <span className="text-yellow-400">工作时间</span> × 100%</p>
                        {data.data.work_time_settings.utilization_mode === 'OP2' ? (
                          <>
                            <p>• <span className="text-yellow-400">工作时间</span> = 24小时 - 休息时间</p>
                            <p>• 当前配置下每日工作时间 = 24:00:00 - {
                              (data.data.work_time_settings.rest_periods || [])
                                .filter((period: any) => period.enabled)
                                .reduce((total: number, period: any) => {
                                  const startTime = new Date(`2000-01-01T${period.start_time}:00`);
                                  const endTime = new Date(`2000-01-01T${period.end_time}:00`);
                                  const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
                                  return total + durationMinutes;
                                }, 0)
                            }分钟</p>
                          </>
                        ) : (
                          <p>• <span className="text-yellow-400">工作时间</span> = 24小时（固定）</p>
                        )}
                      </div>

                      <div className="mt-4 pt-4 border-t border-gray-600">
                        <p className="font-medium text-white mb-2">当前计算结果:</p>
                        <div className="text-xs space-y-1 pl-4">
                          <p>• 参与计算设备: <span className="text-blue-400 font-medium">{data.data.device_count}台</span></p>
                          <p>• 所有设备总工作时间: <span className="text-yellow-400 font-medium">
                            {(data.data.device_utilizations.reduce((sum, device) => sum + device.work_time, 0) / 3600).toFixed(1)}小时
                          </span></p>
                          <p>• 所有设备总运行时间: <span className="text-green-400 font-medium">
                            {(data.data.total_running_time / 3600).toFixed(1)}小时
                          </span></p>

                          <p>• 总体利用率: <span className="text-green-400 font-medium">
                            {(data.data.total_running_time / 3600).toFixed(1)} ÷ {(data.data.device_utilizations.reduce((sum, device) => sum + device.work_time, 0) / 3600).toFixed(1)} = {formatUtilizationRate(data.data.overall_utilization)}
                          </span></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 错误状态显示 */}
          {error && (
            <Card className="bg-red-900/20 border-red-700 mt-6">
              <CardContent className="pt-6">
                <div className="text-center text-red-400">
                  <p className="text-lg">数据加载失败</p>
                  <p className="text-sm mt-2">{error}</p>
                  <Button
                    onClick={() => fetchMachinePageData(queryDate)}
                    variant="outline"
                    size="sm"
                    className="border-red-600 text-red-400 hover:bg-red-700/20 mt-4"
                  >
                    重新加载
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  );
}
