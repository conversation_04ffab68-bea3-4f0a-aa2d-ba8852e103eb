'use client'; // 声明这是一个客户端组件

/**
 * 通知设置页面
 *
 * 该页面提供状态变化通知和警报相关的配置选项
 */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { Bell, ArrowLeft } from 'lucide-react';

export default function NotificationSettingsPage() {
  const router = useRouter();

  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);

  // 公司信息
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造系统',
    dateTime: '',
    logo: '/logo.png'
  });

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 模拟加载
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // 返回设置主页
  const goBack = () => {
    router.push('/settings');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header
        companyInfo={companyInfo}
        toggleSidebar={() => setSidebarMode(SidebarMode.COLLAPSED)}
      />

      <div className="flex">
        <Sidebar mode={sidebarMode} />

        <main className={`flex-1 transition-all duration-300 ml-16`}>
          <div className="p-6">
            {/* 页面头部 */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <button
                  onClick={goBack}
                  className="flex items-center text-gray-400 hover:text-white mr-4 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 mr-1" />
                  返回
                </button>
                <Bell className="h-6 w-6 mr-2" />
                <h1 className="text-2xl font-bold">通知设置</h1>
              </div>
              <p className="text-gray-400">配置状态变化通知和警报</p>
            </div>

            {/* 设置内容 */}
            <div className="max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">通知设置</h3>
                  </div>

                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                      <span className="ml-3">加载设置中...</span>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* 开发中提示 */}
                      <div className="bg-yellow-900/50 text-yellow-300 px-4 py-6 rounded-md text-center">
                        <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <h4 className="text-lg font-medium mb-2">通知设置功能正在开发中</h4>
                        <p className="text-sm">
                          此功能将包括设备状态变化通知、故障警报、邮件通知等选项。
                          <br />
                          敬请期待后续版本更新。
                        </p>
                      </div>

                      {/* 预览功能列表 */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-gray-300">计划功能</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">状态变化通知</h5>
                            <p className="text-sm text-gray-400">设备状态变化时的实时通知</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">故障警报</h5>
                            <p className="text-sm text-gray-400">设备故障时的紧急警报通知</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">邮件通知</h5>
                            <p className="text-sm text-gray-400">通过邮件发送重要事件通知</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">短信通知</h5>
                            <p className="text-sm text-gray-400">紧急情况下的短信通知</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">浏览器通知</h5>
                            <p className="text-sm text-gray-400">浏览器桌面通知设置</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">通知规则</h5>
                            <p className="text-sm text-gray-400">自定义通知触发条件和规则</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 帮助信息 */}
            <div className="mt-6 max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-medium mb-4">功能说明</h3>
                <div className="space-y-3 text-sm text-gray-400">
                  <div>
                    <h4 className="text-white font-medium">状态变化通知</h4>
                    <p>当设备状态发生变化时，系统会根据配置发送相应的通知，帮助用户及时了解设备状态。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">故障警报</h4>
                    <p>设备发生故障时，系统会立即发送紧急警报，确保问题能够得到及时处理。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">多渠道通知</h4>
                    <p>支持邮件、短信、浏览器通知等多种通知方式，确保重要信息能够及时传达。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">通知规则</h4>
                    <p>可以自定义通知的触发条件、频率限制和接收人员，避免通知过载。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
