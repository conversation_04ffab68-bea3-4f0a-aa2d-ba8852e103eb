'use client'; // 声明这是一个客户端组件

/**
 * 系统设置主页面
 *
 * 该页面提供系统各项设置的导航入口
 * 包括刷新设置、显示设置、工作时间设置
 */
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { getApiBaseUrl } from '@/config/api';
import { Settings, RefreshCw, Monitor, Clock, Database, Bell, Shield } from 'lucide-react';

export default function SettingsPage() {
  const router = useRouter();

  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);

  // 最大化状态
  const [isMaximized, setIsMaximized] = useState(false);

  // 公司信息
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '',
    dateTime: '',
    support: '',
    support_info: ''
  });

  // 设置分类 - 包含所有6个设置分类
  const settingCategories = [
    {
      id: 'refresh',
      name: '设备实时状态页面设置',
      icon: <RefreshCw className="h-5 w-5" />,
      description: '设备实时状态页面配置数据刷新频率和自动刷新选项',
      url: '/settings/refresh'
    },
    {
      id: 'display',
      name: '显示设置',
      icon: <Monitor className="h-5 w-5" />,
      description: '配置界面显示选项和布局',
      url: '/settings/display'
    },
    {
      id: 'worktime',
      name: '工作时间设置',
      icon: <Clock className="h-5 w-5" />,
      description: '配置每天开始时间、班次、休息时间和利用率计算模式',
      url: '/settings/worktime'
    },
    {
      id: 'data',
      name: '数据设置',
      icon: <Database className="h-5 w-5" />,
      description: '配置数据源和数据处理选项',
      url: '/settings/data'
    },
    {
      id: 'notification',
      name: '通知设置',
      icon: <Bell className="h-5 w-5" />,
      description: '配置状态变化通知和警报',
      url: '/settings/notification'
    },
    {
      id: 'security',
      name: '安全设置',
      icon: <Shield className="h-5 w-5" />,
      description: '配置访问权限和安全选项',
      url: '/settings/security'
    }
  ];

  // 在客户端初始化时从localStorage加载侧边栏状态
  useEffect(() => {
    console.log('🔧 开始加载侧边栏状态');
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        console.log('✅ 加载侧边栏状态成功:', mode);
        setSidebarMode(mode);
      } catch (error) {
        console.error('❌ 加载侧边栏状态失败:', error);
      }
    };
    loadSidebarMode();
  }, []);

  /**
   * 获取站点信息
   * 从API获取站点基本信息（名称、技术支持等）
   */
  const fetchSiteInfo = useCallback(async () => {
    try {
      console.log('🌐 开始获取站点信息...');
      const API_HOST = getApiBaseUrl();
      const response = await fetch(`${API_HOST}/api/v3/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`站点信息API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 站点信息API响应:', result);

      if (result.success && result.data) {
        // 更新公司信息，保留现有的dateTime
        setCompanyInfo(prev => ({
          ...prev,
          name: result.data.name || '智能制造系统',
          support: result.data.support || '',
          support_info: result.data.support_info || ''
        }));
        console.log('✅ 站点信息更新成功:', {
          name: result.data.name,
          support: result.data.support,
          support_info: result.data.support_info
        });
      } else {
        console.warn('⚠️ 站点信息API返回数据格式异常:', result);
      }
    } catch (error) {
      console.error('❌ 获取站点信息失败:', error);
      // 设置默认值
      setCompanyInfo(prev => ({
        ...prev,
        name: '智能制造系统',
        support: '',
        support_info: ''
      }));
    }
  }, []);

  // 更新当前时间
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 组件挂载时获取站点信息
  useEffect(() => {
    fetchSiteInfo();
  }, [fetchSiteInfo]);

  // 切换最大化状态
  const toggleMaximize = useCallback(() => {
    setIsMaximized(prev => {
      const newState = !prev;
      if (newState) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }
      return newState;
    });
  }, []);

  // 监听全屏变化事件
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  // 切换侧边栏显示模式
  const toggleSidebar = useCallback(async () => {
    try {
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');
      const nextMode = getNextSidebarMode(sidebarMode);
      console.log('切换侧边栏状态:', sidebarMode, '->', nextMode);
      saveSidebarMode(nextMode);
      setSidebarMode(nextMode);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  // 导航到设置页面
  const navigateToSetting = (url: string) => {
    router.push(url);
  };

  return (
    <div className="flex">
      {/* 侧边导航栏 - 在非最大化状态下显示 */}
      {!isMaximized && <Sidebar mode={sidebarMode} activePage="settings" />}

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-h-screen bg-gray-900 text-white">
        {/* 顶部标题栏 */}
        <Header
          companyInfo={companyInfo}
          isMaximized={isMaximized}
          toggleMaximize={toggleMaximize}
          toggleSidebar={toggleSidebar}
        />

        {/* 设置内容区域 */}
        <div className="flex-1 p-6">
          <div className="mb-6">
            <div className="flex items-center mb-2">
              <Settings className="h-6 w-6 mr-2" />
              <h1 className="text-2xl font-bold">系统设置</h1>
            </div>
            <p className="text-gray-400">配置系统的各项参数和选项</p>
          </div>

          {/* 设置分类网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {settingCategories.map((category) => (
              <div
                key={category.id}
                onClick={() => navigateToSetting(category.url)}
                className="bg-gray-800 rounded-lg p-6 cursor-pointer hover:bg-gray-700 transition-colors border border-gray-700 hover:border-blue-500"
              >
                <div className="flex items-center mb-4">
                  <div className="p-3 bg-blue-600 rounded-lg mr-4">
                    {category.icon}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{category.name}</h3>
                  </div>
                </div>
                <p className="text-gray-400 text-sm">{category.description}</p>
                <div className="mt-4 flex items-center text-blue-400 text-sm">
                  <span>进入设置</span>
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            ))}
          </div>

          {/* 快速操作提示 */}
          <div className="mt-8 bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium mb-4">快速操作指南</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-400">
              <div>
                <h4 className="text-white font-medium mb-2">刷新设置</h4>
                <p>调整数据自动刷新频率，优化系统性能和用户体验。</p>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">显示设置</h4>
                <p>自定义界面布局、设备卡片样式和状态颜色配置。</p>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">工作时间设置</h4>
                <p>配置班次时间、休息时间和利用率计算模式。</p>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">数据设置</h4>
                <p>配置数据源连接、同步策略和数据处理规则。</p>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">通知设置</h4>
                <p>设置设备状态变化通知和故障警报机制。</p>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">安全设置</h4>
                <p>管理用户权限、访问控制和系统安全策略。</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
