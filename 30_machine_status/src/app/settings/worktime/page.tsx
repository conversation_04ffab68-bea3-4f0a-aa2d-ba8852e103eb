'use client'; // 声明这是一个客户端组件

/**
 * 工作时间设置页面
 *
 * 该页面提供工作时间相关的配置选项
 */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { getApiBaseUrl } from '@/config/api';
import { Clock, ArrowLeft } from 'lucide-react';
import { Save, AlertCircle, Plus, Trash2 } from 'lucide-react';

export default function WorkTimeSettingsPage() {
  const router = useRouter();

  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);

  // 公司信息
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造系统',
    dateTime: '',
    logo: '/logo.png'
  });

  // 工作时间设置类型定义
  interface WorkTimeSettings {
    id?: string;
    day_start_time: string;
    shifts: Array<{
      name: string;
      start_time: string;
      end_time: string;
      enabled: boolean;
    }>;
    rest_periods: Array<{
      name: string;
      start_time: string;
      end_time: string;
      enabled: boolean;
    }>;
    utilization_mode: string;
    created_at?: string;
    updated_at?: string;
  }

  // 工作时间设置
  const [workTimeSettings, setWorkTimeSettings] = useState<WorkTimeSettings>({
    day_start_time: '08:00',
    shifts: [
      { name: '早班', start_time: '08:00', end_time: '16:00', enabled: true },
      { name: '中班', start_time: '16:00', end_time: '00:00', enabled: true },
      { name: '晚班', start_time: '00:00', end_time: '08:00', enabled: true }
    ],
    rest_periods: [
      { name: '午休', start_time: '12:00', end_time: '13:00', enabled: true },
      { name: '晚餐', start_time: '18:00', end_time: '19:00', enabled: false }
    ],
    utilization_mode: 'OP1'
  });

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 保存状态
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(false);

  // 从API获取设置
  useEffect(() => {
    let isMounted = true;

    const fetchSettings = async () => {
      try {
        if (!isMounted) return;

        setIsLoading(true);

        // 获取工作时间设置
        try {
          const API_HOST = getApiBaseUrl();
          const workTimeResponse = await fetch(`${API_HOST}/api/statistics/worktime`);
          if (workTimeResponse.ok) {
            const workTimeData = await workTimeResponse.json();
            if (!isMounted) return;
            setWorkTimeSettings(workTimeData);
          }
        } catch (workTimeError) {
          console.error('获取工作时间设置失败:', workTimeError);
        }

        console.log('工作时间设置加载成功完成');
      } catch (error) {
        console.error('获取工作时间设置失败:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    const timer = setTimeout(fetchSettings, 0);

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, []);

  // 保存工作时间设置到API
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSaveSuccess(false);
      setSaveError(false);

      const API_HOST = getApiBaseUrl();
      const response = await fetch(`${API_HOST}/api/statistics/worktime`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workTimeSettings),
      });

      if (!response.ok) {
        throw new Error('保存工作时间设置失败');
      }

      setSaveSuccess(true);
      // 3秒后清除成功提示
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      console.error('保存工作时间设置失败:', error);
      setSaveError(true);
      // 3秒后清除错误提示
      setTimeout(() => setSaveError(false), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理工作时间设置变化
  const handleWorkTimeSettingsChange = (key: string, value: any) => {
    setWorkTimeSettings({
      ...workTimeSettings,
      [key]: value
    });
  };

  // 处理班次设置变化
  const handleShiftChange = (index: number, key: string, value: any) => {
    setWorkTimeSettings({
      ...workTimeSettings,
      shifts: workTimeSettings.shifts.map((shift, i) =>
        i === index ? { ...shift, [key]: value } : shift
      )
    });
  };

  // 添加新班次
  const handleAddShift = () => {
    setWorkTimeSettings({
      ...workTimeSettings,
      shifts: [...workTimeSettings.shifts, { name: '新班次', start_time: '08:00', end_time: '16:00', enabled: true }]
    });
  };

  // 删除班次
  const handleRemoveShift = (index: number) => {
    setWorkTimeSettings({
      ...workTimeSettings,
      shifts: workTimeSettings.shifts.filter((_, i) => i !== index)
    });
  };

  // 处理休息时间设置变化
  const handleRestPeriodChange = (index: number, key: string, value: any) => {
    setWorkTimeSettings({
      ...workTimeSettings,
      rest_periods: workTimeSettings.rest_periods.map((period, i) =>
        i === index ? { ...period, [key]: value } : period
      )
    });
  };

  // 添加新休息时间
  const handleAddRestPeriod = () => {
    setWorkTimeSettings({
      ...workTimeSettings,
      rest_periods: [...workTimeSettings.rest_periods, { name: '新休息时间', start_time: '12:00', end_time: '13:00', enabled: true }]
    });
  };

  // 删除休息时间
  const handleRemoveRestPeriod = (index: number) => {
    setWorkTimeSettings({
      ...workTimeSettings,
      rest_periods: workTimeSettings.rest_periods.filter((_, i) => i !== index)
    });
  };

  // 返回设置主页
  const goBack = () => {
    router.push('/settings');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header
        companyInfo={companyInfo}
        toggleSidebar={() => setSidebarMode(SidebarMode.COLLAPSED)}
      />

      <div className="flex">
        <Sidebar mode={sidebarMode} />

        <main className={`flex-1 transition-all duration-300 ml-16`}>
          <div className="p-6">
            {/* 页面头部 */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <button
                  onClick={goBack}
                  className="flex items-center text-gray-400 hover:text-white mr-4 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 mr-1" />
                  返回
                </button>
                <Clock className="h-6 w-6 mr-2" />
                <h1 className="text-2xl font-bold">工作时间设置</h1>
              </div>
              <p className="text-gray-400">配置每天开始时间、班次、休息时间和利用率计算模式</p>
            </div>

            {/* 设置内容 */}
            <div className="max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">工作时间设置</h3>

                    {/* 保存按钮 */}
                    <button
                      onClick={handleSaveSettings}
                      disabled={isSaving || isLoading}
                      className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
                        ${isSaving || isLoading ? 'bg-gray-600 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
                    >
                      {isSaving ? (
                        <>
                          <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          保存设置
                        </>
                      )}
                    </button>
                  </div>

                  {/* 保存状态提示 */}
                  {saveSuccess && (
                    <div className="bg-green-900/50 text-green-300 px-4 py-2 rounded-md flex items-center">
                      <div className="bg-green-500 rounded-full p-1 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      设置已成功保存
                    </div>
                  )}

                  {saveError && (
                    <div className="bg-red-900/50 text-red-300 px-4 py-2 rounded-md flex items-center">
                      <AlertCircle className="h-5 w-5 mr-2" />
                      保存设置失败，请重试
                    </div>
                  )}

                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                      <span className="ml-3">加载设置中...</span>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* 每天开始时间设置 */}
                      <div className="space-y-2">
                        <label className="font-medium">每天开始时间</label>
                        <p className="text-sm text-gray-400">设置每天的开始时间，用于计算日期边界</p>
                        <input
                          type="time"
                          value={workTimeSettings.day_start_time}
                          onChange={(e) => handleWorkTimeSettingsChange('day_start_time', e.target.value)}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      {/* 利用率计算模式设置 */}
                      <div className="space-y-2">
                        <label className="font-medium">利用率计算模式</label>
                        <p className="text-sm text-gray-400">选择设备利用率的计算方式</p>
                        <div className="flex space-x-4 mt-2">
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              name="utilizationMode"
                              checked={workTimeSettings.utilization_mode === 'OP1'}
                              onChange={() => handleWorkTimeSettingsChange('utilization_mode', 'OP1')}
                              className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-600"
                            />
                            <span>OP1: 24小时</span>
                          </label>
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              name="utilizationMode"
                              checked={workTimeSettings.utilization_mode === 'OP2'}
                              onChange={() => handleWorkTimeSettingsChange('utilization_mode', 'OP2')}
                              className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-600"
                            />
                            <span>OP2: 24小时 - 休息时间</span>
                          </label>
                        </div>
                      </div>

                      {/* 班次设置 */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="text-md font-medium text-blue-400">班次设置</h4>
                          <button
                            onClick={handleAddShift}
                            className="flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 rounded-md text-sm transition-colors"
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            添加班次
                          </button>
                        </div>

                        {workTimeSettings.shifts.map((shift, index) => (
                          <div key={index} className="bg-gray-800 p-4 rounded-md space-y-3">
                            <div className="flex items-center justify-between">
                              <input
                                type="text"
                                value={shift.name}
                                onChange={(e) => handleShiftChange(index, 'name', e.target.value)}
                                className="bg-gray-700 border border-gray-600 rounded-md px-3 py-1 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="班次名称"
                              />
                              <div className="flex items-center space-x-2">
                                <label className="inline-flex items-center cursor-pointer">
                                  <input
                                    type="checkbox"
                                    checked={shift.enabled}
                                    onChange={(e) => handleShiftChange(index, 'enabled', e.target.checked)}
                                    className="sr-only peer"
                                  />
                                  <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
                                </label>
                                <button
                                  onClick={() => handleRemoveShift(index)}
                                  className="p-1 text-red-400 hover:text-red-300 transition-colors"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <label className="block text-sm text-gray-400 mb-1">开始时间</label>
                                <input
                                  type="time"
                                  value={shift.start_time}
                                  onChange={(e) => handleShiftChange(index, 'start_time', e.target.value)}
                                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                              </div>
                              <div>
                                <label className="block text-sm text-gray-400 mb-1">结束时间</label>
                                <input
                                  type="time"
                                  value={shift.end_time}
                                  onChange={(e) => handleShiftChange(index, 'end_time', e.target.value)}
                                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* 休息时间设置 */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="text-md font-medium text-blue-400">休息时间设置</h4>
                          <button
                            onClick={handleAddRestPeriod}
                            className="flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 rounded-md text-sm transition-colors"
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            添加休息时间
                          </button>
                        </div>

                        {workTimeSettings.rest_periods.map((period, index) => (
                          <div key={index} className="bg-gray-800 p-4 rounded-md space-y-3">
                            <div className="flex items-center justify-between">
                              <input
                                type="text"
                                value={period.name}
                                onChange={(e) => handleRestPeriodChange(index, 'name', e.target.value)}
                                className="bg-gray-700 border border-gray-600 rounded-md px-3 py-1 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="休息时间名称"
                              />
                              <div className="flex items-center space-x-2">
                                <label className="inline-flex items-center cursor-pointer">
                                  <input
                                    type="checkbox"
                                    checked={period.enabled}
                                    onChange={(e) => handleRestPeriodChange(index, 'enabled', e.target.checked)}
                                    className="sr-only peer"
                                  />
                                  <div className="relative w-9 h-5 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all"></div>
                                </label>
                                <button
                                  onClick={() => handleRemoveRestPeriod(index)}
                                  className="p-1 text-red-400 hover:text-red-300 transition-colors"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <label className="block text-sm text-gray-400 mb-1">开始时间</label>
                                <input
                                  type="time"
                                  value={period.start_time}
                                  onChange={(e) => handleRestPeriodChange(index, 'start_time', e.target.value)}
                                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                              </div>
                              <div>
                                <label className="block text-sm text-gray-400 mb-1">结束时间</label>
                                <input
                                  type="time"
                                  value={period.end_time}
                                  onChange={(e) => handleRestPeriodChange(index, 'end_time', e.target.value)}
                                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 帮助信息 */}
            <div className="mt-6 max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-medium mb-4">设置说明</h3>
                <div className="space-y-4 text-sm text-gray-400">
                  <div>
                    <h4 className="text-white font-medium">每天开始时间</h4>
                    <p>设置每天的开始时间，用于计算日期边界。例如设置为08:00，则当天从08:00开始到次日07:59结束。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">利用率计算模式</h4>
                    <ul className="list-disc list-inside space-y-1 mt-2">
                      <li><strong>OP1模式（24小时）</strong>：基于24小时计算利用率，使用24小时作为分母</li>
                      <li><strong>OP2模式（24小时 - 休息时间）</strong>：基于实际工作时间计算利用率，从24小时中减去休息时间</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">班次管理</h4>
                    <p>可以添加、删除和编辑班次。每个班次可以单独启用或禁用。班次时间支持跨日设置（如夜班从00:00到08:00）。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">休息时间管理</h4>
                    <p>在OP2模式下，休息时间会从实际工作时间中扣除。可以设置多个休息时间段，如午休、晚餐等。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
