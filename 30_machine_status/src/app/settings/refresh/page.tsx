'use client'; // 声明这是一个客户端组件

/**
 * 刷新设置页面
 *
 * 该页面提供数据刷新相关的配置选项
 */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { RefreshCw, ArrowLeft } from 'lucide-react';
import {
  getRefreshSettings,
  saveRefreshSettings,
  RefreshSettings as RefreshSettingsType
} from '@/services/settingsService';

export default function RefreshSettingsPage() {
  const router = useRouter();

  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);

  // 公司信息
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造系统',
    dateTime: '',
    logo: '/logo.png'
  });

  // 刷新设置
  const [refreshSettings, setRefreshSettings] = useState<RefreshSettingsType>({
    autoRefresh: true,
    refreshInterval: 1000,
    showRefreshIndicator: true
  });

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 保存状态
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(false);

  // 从API获取设置
  useEffect(() => {
    let isMounted = true;

    const fetchSettings = async () => {
      try {
        if (!isMounted) return;

        setIsLoading(true);

        // 获取刷新设置
        const refreshData = await getRefreshSettings();
        if (!isMounted) return;
        setRefreshSettings(refreshData);

        console.log('刷新设置加载成功完成');
      } catch (error) {
        console.error('获取刷新设置失败:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    const timer = setTimeout(fetchSettings, 0);

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, []);

  // 保存刷新设置到API
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSaveSuccess(false);
      setSaveError(false);

      await saveRefreshSettings(refreshSettings);

      setSaveSuccess(true);
      // 3秒后清除成功提示
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      console.error('保存刷新设置失败:', error);
      setSaveError(true);
      // 3秒后清除错误提示
      setTimeout(() => setSaveError(false), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理刷新间隔变化
  const handleRefreshIntervalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setRefreshSettings({
      ...refreshSettings,
      refreshInterval: value
    });
  };

  // 处理自动刷新开关变化
  const handleAutoRefreshChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRefreshSettings({
      ...refreshSettings,
      autoRefresh: e.target.checked
    });
  };

  // 处理显示刷新指示器开关变化
  const handleShowRefreshIndicatorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRefreshSettings({
      ...refreshSettings,
      showRefreshIndicator: e.target.checked
    });
  };

  // 返回设置主页
  const goBack = () => {
    router.push('/settings');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header
        companyInfo={companyInfo}
        toggleSidebar={() => setSidebarMode(SidebarMode.COLLAPSED)}
      />

      <div className="flex">
        <Sidebar mode={sidebarMode} />

        <main className={`flex-1 transition-all duration-300 ml-16`}>
          <div className="p-6">
            {/* 页面头部 */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <button
                  onClick={goBack}
                  className="flex items-center text-gray-400 hover:text-white mr-4 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 mr-1" />
                  返回
                </button>
                <RefreshCw className="h-6 w-6 mr-2" />
                <h1 className="text-2xl font-bold">设备实时状态-刷新设置</h1>
              </div>
              <p className="text-gray-400">配置数据刷新频率和自动刷新选项</p>
            </div>

            {/* 设置内容 */}
            <div className="max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">刷新设置</h3>

                    {/* 保存按钮 */}
                    <button
                      onClick={handleSaveSettings}
                      disabled={isSaving || isLoading}
                      className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors
                        ${isSaving || isLoading ? 'bg-gray-600 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
                    >
                      {isSaving ? (
                        <>
                          <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                          保存中...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          保存设置
                        </>
                      )}
                    </button>
                  </div>

                  {/* 保存状态提示 */}
                  {saveSuccess && (
                    <div className="bg-green-900/50 text-green-300 px-4 py-2 rounded-md flex items-center">
                      <div className="bg-green-500 rounded-full p-1 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      设置已成功保存
                    </div>
                  )}

                  {saveError && (
                    <div className="bg-red-900/50 text-red-300 px-4 py-2 rounded-md flex items-center">
                      <RefreshCw className="h-5 w-5 mr-2" />
                      保存设置失败，请重试
                    </div>
                  )}

                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                      <span className="ml-3">加载设置中...</span>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <label className="font-medium">自动刷新</label>
                          <p className="text-sm text-gray-400">启用后，系统将自动定期刷新数据</p>
                        </div>
                        <label className="inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={refreshSettings.autoRefresh}
                            onChange={handleAutoRefreshChange}
                            className="sr-only peer"
                          />
                          <div className="relative w-11 h-6 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                        </label>
                      </div>

                      <div className="space-y-2">
                        <label className="font-medium">刷新间隔 (毫秒)</label>
                        <p className="text-sm text-gray-400">设置数据自动刷新的时间间隔</p>
                        <input
                          type="range"
                          min="500"
                          max="60000"
                          step="500"
                          value={refreshSettings.refreshInterval}
                          onChange={handleRefreshIntervalChange}
                          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="flex justify-between text-xs text-gray-400">
                          <span>500毫秒</span>
                          <span>{refreshSettings.refreshInterval / 1000} 秒</span>
                          <span>60秒</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <label className="font-medium">显示刷新指示器</label>
                          <p className="text-sm text-gray-400">在界面上显示最近刷新时间和刷新按钮</p>
                        </div>
                        <label className="inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={refreshSettings.showRefreshIndicator}
                            onChange={handleShowRefreshIndicatorChange}
                            className="sr-only peer"
                          />
                          <div className="relative w-11 h-6 bg-gray-700 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 帮助信息 */}
            <div className="mt-6 max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-medium mb-4">设置说明</h3>
                <div className="space-y-3 text-sm text-gray-400">
                  <div>
                    <h4 className="text-white font-medium">自动刷新</h4>
                    <p>启用后，系统将根据设定的时间间隔自动刷新数据，确保显示的信息是最新的。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">刷新间隔</h4>
                    <p>设置数据自动刷新的时间间隔，建议根据数据更新频率和系统性能来调整。较短的间隔可以获得更实时的数据，但会增加系统负载。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">显示刷新指示器</h4>
                    <p>在界面上显示最近刷新时间和手动刷新按钮，方便用户了解数据的新鲜度。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
