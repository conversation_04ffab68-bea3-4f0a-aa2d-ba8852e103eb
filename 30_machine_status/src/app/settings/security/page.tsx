'use client'; // 声明这是一个客户端组件

/**
 * 安全设置页面
 *
 * 该页面提供访问权限和安全相关的配置选项
 */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { Shield, ArrowLeft } from 'lucide-react';

export default function SecuritySettingsPage() {
  const router = useRouter();

  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);

  // 公司信息
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造系统',
    dateTime: '',
    logo: '/logo.png'
  });

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 模拟加载
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // 返回设置主页
  const goBack = () => {
    router.push('/settings');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header
        companyInfo={companyInfo}
        toggleSidebar={() => setSidebarMode(SidebarMode.COLLAPSED)}
      />

      <div className="flex">
        <Sidebar mode={sidebarMode} />

        <main className={`flex-1 transition-all duration-300 ml-16`}>
          <div className="p-6">
            {/* 页面头部 */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <button
                  onClick={goBack}
                  className="flex items-center text-gray-400 hover:text-white mr-4 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 mr-1" />
                  返回
                </button>
                <Shield className="h-6 w-6 mr-2" />
                <h1 className="text-2xl font-bold">安全设置</h1>
              </div>
              <p className="text-gray-400">配置访问权限和安全选项</p>
            </div>

            {/* 设置内容 */}
            <div className="max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">安全设置</h3>
                  </div>

                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                      <span className="ml-3">加载设置中...</span>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* 开发中提示 */}
                      <div className="bg-yellow-900/50 text-yellow-300 px-4 py-6 rounded-md text-center">
                        <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <h4 className="text-lg font-medium mb-2">安全设置功能正在开发中</h4>
                        <p className="text-sm">
                          此功能将包括用户权限管理、访问控制、安全审计等选项。
                          <br />
                          敬请期待后续版本更新。
                        </p>
                      </div>

                      {/* 预览功能列表 */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-gray-300">计划功能</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">用户权限管理</h5>
                            <p className="text-sm text-gray-400">管理用户角色和访问权限</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">访问控制</h5>
                            <p className="text-sm text-gray-400">设置页面和功能的访问限制</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">登录安全</h5>
                            <p className="text-sm text-gray-400">配置登录验证和会话管理</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">安全审计</h5>
                            <p className="text-sm text-gray-400">记录和监控系统访问日志</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">数据加密</h5>
                            <p className="text-sm text-gray-400">配置数据传输和存储加密</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">安全策略</h5>
                            <p className="text-sm text-gray-400">设置密码策略和安全规则</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 帮助信息 */}
            <div className="mt-6 max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-medium mb-4">功能说明</h3>
                <div className="space-y-3 text-sm text-gray-400">
                  <div>
                    <h4 className="text-white font-medium">用户权限管理</h4>
                    <p>管理系统用户的角色和权限，确保不同用户只能访问其被授权的功能和数据。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">访问控制</h4>
                    <p>设置细粒度的访问控制策略，保护敏感功能和数据不被未授权访问。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">安全审计</h4>
                    <p>记录所有用户操作和系统访问日志，便于安全审计和问题追踪。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">数据保护</h4>
                    <p>通过加密和安全策略保护系统数据，确保数据的机密性和完整性。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
