'use client'; // 声明这是一个客户端组件

/**
 * 数据设置页面
 *
 * 该页面提供数据源和数据处理相关的配置选项
 */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { Database, ArrowLeft } from 'lucide-react';

export default function DataSettingsPage() {
  const router = useRouter();

  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);

  // 公司信息
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造系统',
    dateTime: '',
    logo: '/logo.png'
  });

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 模拟加载
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // 返回设置主页
  const goBack = () => {
    router.push('/settings');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header
        companyInfo={companyInfo}
        toggleSidebar={() => setSidebarMode(sidebarMode === SidebarMode.COLLAPSED ? SidebarMode.COLLAPSED : SidebarMode.COLLAPSED)}
      />

      <div className="flex">
        <Sidebar mode={sidebarMode} />

        <main className={`flex-1 transition-all duration-300 ml-16`}>
          <div className="p-6">
            {/* 页面头部 */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <button
                  onClick={goBack}
                  className="flex items-center text-gray-400 hover:text-white mr-4 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 mr-1" />
                  返回
                </button>
                <Database className="h-6 w-6 mr-2" />
                <h1 className="text-2xl font-bold">数据设置</h1>
              </div>
              <p className="text-gray-400">配置数据源和数据处理选项</p>
            </div>

            {/* 设置内容 */}
            <div className="max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">数据设置</h3>
                  </div>

                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                      <span className="ml-3">加载设置中...</span>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* 开发中提示 */}
                      <div className="bg-yellow-900/50 text-yellow-300 px-4 py-6 rounded-md text-center">
                        <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <h4 className="text-lg font-medium mb-2">数据设置功能正在开发中</h4>
                        <p className="text-sm">
                          此功能将包括数据源配置、数据同步设置、数据清理规则等选项。
                          <br />
                          敬请期待后续版本更新。
                        </p>
                      </div>

                      {/* 预览功能列表 */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-gray-300">计划功能</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">数据源配置</h5>
                            <p className="text-sm text-gray-400">配置设备数据源、API接口和数据库连接</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">数据同步设置</h5>
                            <p className="text-sm text-gray-400">设置数据同步频率和同步策略</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">数据清理规则</h5>
                            <p className="text-sm text-gray-400">配置历史数据保留期限和清理策略</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded-md opacity-50">
                            <h5 className="font-medium mb-2">数据备份设置</h5>
                            <p className="text-sm text-gray-400">配置数据备份策略和恢复选项</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 帮助信息 */}
            <div className="mt-6 max-w-4xl">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-medium mb-4">功能说明</h3>
                <div className="space-y-3 text-sm text-gray-400">
                  <div>
                    <h4 className="text-white font-medium">数据源配置</h4>
                    <p>配置系统连接的各种数据源，包括设备API、数据库连接、文件数据源等。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">数据同步设置</h4>
                    <p>设置数据同步的频率、策略和错误处理机制，确保数据的实时性和一致性。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">数据清理规则</h4>
                    <p>配置历史数据的保留策略，自动清理过期数据，优化系统性能。</p>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">数据备份设置</h4>
                    <p>配置数据备份的策略和恢复机制，保障数据安全。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
