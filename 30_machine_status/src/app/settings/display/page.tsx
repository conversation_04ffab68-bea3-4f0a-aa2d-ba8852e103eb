'use client'; // 声明这是一个客户端组件

/**
 * 显示设置页面
 *
 * 该页面提供界面显示相关的配置选项
 * 包含多个子TAB：设备布局、设备卡片、状态颜色
 */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { Monitor, ArrowLeft, Layout, CreditCard, Palette, Activity, BarChart3, Clock } from 'lucide-react';
import {
  getDisplaySettings,
  DisplaySettings as DisplaySettingsType
} from '@/services/settingsService';

// 导入显示设置子组件
import DeviceLayoutSettings from '@/components/settings/display/DeviceLayoutSettings';
import DeviceCardSettings from '@/components/settings/display/DeviceCardSettings';
import StatusColorSettings from '@/components/settings/display/StatusColorSettings';
import UtilizationDashboardSettings from '@/components/settings/display/UtilizationDashboardSettings';
import ProductionProgressSettings from '@/components/settings/display/ProductionProgressSettings';

export default function DisplaySettingsPage() {
  const router = useRouter();

  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.COLLAPSED);

  // 公司信息
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '智能制造系统',
    dateTime: '',
    logo: '/logo.png'
  });

  // 显示设置子TAB
  const displayTabs = [
    {
      id: 'layout',
      name: '设备看板布局',
      icon: <Layout className="h-4 w-4" />,
      description: '设备网格间隔、状态计数、状态过滤和历史显示设置'
    },
    {
      id: 'card',
      name: '设备卡片',
      icon: <CreditCard className="h-4 w-4" />,
      description: '设备卡片内容和样式设置'
    },
    {
      id: 'color',
      name: '状态颜色',
      icon: <Palette className="h-4 w-4" />,
      description: '各种设备状态的颜色配置'
    },
    {
      id: 'utilization',
      name: '利用率仪表板',
      icon: <BarChart3 className="h-4 w-4" />,
      description: '利用率仪表板布局和显示配置'
    },
    {
      id: 'production',
      name: '生产进度',
      icon: <Clock className="h-4 w-4" />,
      description: '生产进度页面时间轴配置'
    }
  ];

  // 当前选中的子TAB
  const [activeTab, setActiveTab] = useState('layout');

  // 显示设置
  const [displaySettings, setDisplaySettings] = useState<DisplaySettingsType>({
    deviceGridGap: 12,
    deviceRowGap: 12,
    deviceColumnGap: 12,
    statusDeviceGap: 12,
    statusCountDigits: 2,
    statusCountPadChar: "0",
    filterSignalDuration: 1, // 默认过滤1秒以下的信号
    cardContent: {
      showCode: true,
      showName: true,
      showLocation: true,
      showModel: true,
      showQuantity: true,
      showPlan: true,
      fontSize: 14,
      textAlign: "center",
      layoutRatio: 60
    },
    cardStyle: {
      cardWidth: 200,
      cardHeight: 120,
      borderRadius: 4,
      borderWidth: 2,
      contentPadding: 8
    },
    statusColors: {
      production: "#22c55e",
      idle: "#f59e0b",
      fault: "#ef4444",
      adjusting: "#3b82f6",
      shutdown: "#6b7280",
      disconnected: "#9ca3af",
      maintenance: "#8b5cf6",
      debugging: "#06b6d4"
    },
    statusHistory: {
      timeSlotWidth: 120,
      timelineHeight: 40,
      timelineGap: 8
    },
    productionProgress: {
      timeAxisStartHour: 8
    },
    utilizationDashboard: {
      layoutMode: "split",
      rankingDisplayLimit: 20
    }
  });

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 从API获取设置
  useEffect(() => {
    let isMounted = true;

    const fetchSettings = async () => {
      try {
        if (!isMounted) return;

        setIsLoading(true);

        // 获取显示设置
        const displayData = await getDisplaySettings();
        if (!isMounted) return;
        setDisplaySettings({
          ...displayData,
          cardContent: { ...displayData.cardContent },
          cardStyle: { ...displayData.cardStyle },
          statusColors: { ...displayData.statusColors },
          statusHistory: { ...displayData.statusHistory },
          productionProgress: displayData.productionProgress ? { ...displayData.productionProgress } : { timeAxisStartHour: 8 }
        });

        console.log('显示设置加载成功完成');
      } catch (error) {
        console.error('获取显示设置失败:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    const timer = setTimeout(fetchSettings, 0);

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, []);

  // 返回设置主页
  const goBack = () => {
    router.push('/settings');
  };

  // 渲染当前选中的TAB内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'layout':
        return (
          <DeviceLayoutSettings
            settings={displaySettings}
            onSettingsChange={setDisplaySettings}
            isLoading={isLoading}
          />
        );
      case 'card':
        return (
          <DeviceCardSettings
            settings={displaySettings}
            onSettingsChange={setDisplaySettings}
            isLoading={isLoading}
          />
        );
      case 'color':
        return (
          <StatusColorSettings
            settings={displaySettings}
            onSettingsChange={setDisplaySettings}
            isLoading={isLoading}
          />
        );
      case 'utilization':
        return (
          <UtilizationDashboardSettings
            settings={displaySettings}
            onSettingsChange={setDisplaySettings}
            isLoading={isLoading}
          />
        );
      case 'production':
        return (
          <ProductionProgressSettings
            settings={displaySettings}
            onSettingsChange={setDisplaySettings}
            isLoading={isLoading}
          />
        );
      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-400">请选择一个设置分类</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header
        companyInfo={companyInfo}
        toggleSidebar={() => setSidebarMode(SidebarMode.COLLAPSED)}
      />

      <div className="flex">
        <Sidebar mode={sidebarMode} />

        <main className={`flex-1 transition-all duration-300 ml-16`}>
          <div className="p-6">
            {/* 页面头部 */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <button
                  onClick={goBack}
                  className="flex items-center text-gray-400 hover:text-white mr-4 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 mr-1" />
                  返回
                </button>
                <Monitor className="h-6 w-6 mr-2" />
                <h1 className="text-2xl font-bold">显示设置</h1>
              </div>
              <p className="text-gray-400">配置界面显示选项和布局</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* 左侧子TAB导航 */}
              <div className="lg:col-span-1">
                <div className="bg-gray-800 rounded-lg p-4">
                  <h3 className="text-lg font-medium mb-4">设置分类</h3>
                  <div className="space-y-2">
                    {displayTabs.map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full text-left p-3 rounded-md transition-colors ${activeTab === tab.id
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                          }`}
                      >
                        <div className="flex items-center mb-1">
                          {tab.icon}
                          <span className="ml-2 font-medium">{tab.name}</span>
                        </div>
                        <p className="text-xs text-gray-400">{tab.description}</p>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* 右侧设置内容 */}
              <div className="lg:col-span-3">
                <div className="bg-gray-800 rounded-lg p-6">
                  {renderTabContent()}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
