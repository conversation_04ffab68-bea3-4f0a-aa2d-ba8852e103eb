'use client'; // 声明这是一个客户端组件

/**
 * 404 页面 - 未找到页面
 * 
 * 当用户访问不存在的路由时显示此页面
 */
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';

export default function NotFound() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col items-center justify-center text-white p-4">
      <div className="text-center max-w-md">
        <h1 className="text-6xl font-bold mb-4">404</h1>
        <h2 className="text-2xl font-semibold mb-6">页面未找到</h2>
        <p className="text-gray-400 mb-8">
          您访问的页面不存在或正在开发中。请返回首页或尝试其他功能。
        </p>
        <button
          onClick={() => router.push('/')}
          className="flex items-center justify-center mx-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
        >
          <ArrowLeft className="mr-2 h-5 w-5" />
          返回首页
        </button>
      </div>
    </div>
  );
}
