'use client';

/**
 * 设备状态历史索引页面
 *
 * 显示设备列表，允许用户选择设备查看其状态历史
 */
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/layout/Header';
import Sidebar, { SidebarMode } from '@/components/layout/Sidebar';
import { CompanyInfo } from '@/types';
import { getApiBaseUrl } from '@/config/api';
import { History, Search, X, ArrowRight, ChevronLeft, ExternalLink } from 'lucide-react';
import { getDisplaySettings } from '@/services/settingsService';
import { deviceAPI } from '@/services/apiService';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorAlert from '@/components/common/ErrorAlert';

interface Device {
  id: string;
  code: string;
  name: string;
  location?: string;
  model?: string;
  brand?: string;
  status?: string;
}

export default function DeviceStatusHistoryIndexPage() {
  // 侧边栏状态
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>(SidebarMode.HIDDEN);

  // 最大化状态
  const [isMaximized, setIsMaximized] = useState(false);

  // 公司信息状态
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: '',
    dateTime: '',  // 初始为空字符串，避免水合错误
    support: '',
    support_info: ''
  });

  // 设备列表
  const [devices, setDevices] = useState<Device[]>([]);
  const [filteredDevices, setFilteredDevices] = useState<Device[]>([]);

  // 搜索词
  const [searchTerm, setSearchTerm] = useState('');

  // 显示设置（包含状态颜色）
  const [displaySettings, setDisplaySettings] = useState<any>(null);

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 错误状态
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();

  // 在客户端初始化时从localStorage加载侧边栏状态
  useEffect(() => {
    // 导入是动态的，因为这些函数使用了浏览器API
    const loadSidebarMode = async () => {
      try {
        const { getSidebarMode } = await import('@/utils/sidebarState');
        const mode = getSidebarMode();
        console.log('设备状态历史索引页面加载侧边栏状态:', mode);
        setSidebarMode(mode);
      } catch (error) {
        console.error('加载侧边栏状态失败:', error);
      }
    };

    // 立即执行
    loadSidebarMode();
  }, []);

  /**
   * 切换侧边栏显示模式
   * 在两种模式之间切换：隐藏 <-> 折叠
   * 并保存到localStorage
   */
  const toggleSidebar = useCallback(async () => {
    try {
      // 导入工具函数
      const { getNextSidebarMode, saveSidebarMode } = await import('@/utils/sidebarState');

      // 获取下一个模式
      const nextMode = getNextSidebarMode(sidebarMode);
      console.log('设备状态历史索引页面切换侧边栏状态:', sidebarMode, '->', nextMode);

      // 保存到localStorage和全局变量
      saveSidebarMode(nextMode);

      // 更新组件状态
      setSidebarMode(nextMode);
    } catch (error) {
      console.error('切换侧边栏状态失败:', error);
    }
  }, [sidebarMode]);

  /**
   * 获取站点信息
   * 从API获取站点基本信息（名称、技术支持等）
   */
  const fetchSiteInfo = useCallback(async () => {
    try {
      console.log('🌐 开始获取站点信息...');
      const API_HOST = getApiBaseUrl();
      const response = await fetch(`${API_HOST}/api/v3/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`站点信息API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 站点信息API响应:', result);

      if (result.success && result.data) {
        // 更新公司信息，保留现有的dateTime
        setCompanyInfo(prev => ({
          ...prev,
          name: result.data.name || '智能制造系统',
          support: result.data.support || '',
          support_info: result.data.support_info || ''
        }));
        console.log('✅ 站点信息更新成功:', {
          name: result.data.name,
          support: result.data.support,
          support_info: result.data.support_info
        });
      } else {
        console.warn('⚠️ 站点信息API返回数据格式异常:', result);
      }
    } catch (error) {
      console.error('❌ 获取站点信息失败:', error);
      // 设置默认值
      setCompanyInfo(prev => ({
        ...prev,
        name: '智能制造系统',
        support: '',
        support_info: ''
      }));
    }
  }, []);

  /**
   * 切换最大化状态
   * 当点击最大化按钮时调用
   * 同时控制浏览器全屏模式
   */
  const toggleMaximize = useCallback(() => {
    // 切换最大化状态
    setIsMaximized(prev => {
      const newState = !prev;

      // 根据新状态切换全屏模式
      if (newState) {
        // 进入全屏模式
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(err => {
            console.error(`全屏请求失败: ${err.message}`);
          });
        }
      } else {
        // 退出全屏模式
        if (document.fullscreenElement && document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`退出全屏失败: ${err.message}`);
          });
        }
      }

      return newState;
    });
  }, []);

  // 监听全屏变化事件，同步状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      // 如果浏览器退出了全屏模式，同步更新我们的状态
      if (!document.fullscreenElement && isMaximized) {
        setIsMaximized(false);
      }
    };

    // 添加全屏变化事件监听
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    // 清理函数
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isMaximized]);

  // 在客户端更新时间
  useEffect(() => {
    // 更新当前时间
    const updateTime = () => {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
      const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      setCompanyInfo(prev => ({
        ...prev,
        dateTime: `${formattedDate} ${formattedTime}`
      }));
    };

    // 立即更新一次
    updateTime();

    // 每秒更新一次
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // 加载显示设置
  useEffect(() => {
    const loadDisplaySettings = async () => {
      try {
        const settings = await getDisplaySettings();

        // 确保所有必要的嵌套对象都存在
        const completeSettings = {
          ...settings,
          statusColors: {
            ...(settings.statusColors || {}),
            production: "#22c55e",
            idle: "#f59e0b",
            fault: "#ef4444",
            adjusting: "#3b82f6",
            shutdown: "#6b7280",
            disconnected: "#9ca3af",
            maintenance: "#8b5cf6",
            debugging: "#06b6d4"
          },
          statusHistory: {
            ...(settings.statusHistory || {}),
            timeSlotWidth: 120,
            timelineHeight: 40,
            timelineGap: 8
          }
        };

        setDisplaySettings(completeSettings);
      } catch (error) {
        console.error('加载显示设置失败:', error);

        // 使用默认设置，确保UI可用
        setDisplaySettings({
          statusColors: {
            production: "#22c55e",
            idle: "#f59e0b",
            fault: "#ef4444",
            adjusting: "#3b82f6",
            shutdown: "#6b7280",
            disconnected: "#9ca3af",
            maintenance: "#8b5cf6",
            debugging: "#06b6d4"
          },
          statusHistory: {
            timeSlotWidth: 120,
            timelineHeight: 40,
            timelineGap: 8
          }
        });
      }
    };

    loadDisplaySettings();
  }, []);

  // 加载设备列表和站点信息
  useEffect(() => {
    const fetchDevices = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // 并行获取站点信息和设备列表
        await Promise.all([
          fetchSiteInfo(), // 获取站点信息
          (async () => {
            // 使用统一API服务获取设备列表
            const data = await deviceAPI.getMachines();

            if (data.machines && Array.isArray(data.machines)) {
              setDevices(data.machines);
              setFilteredDevices(data.machines);
              console.log(`✅ 成功获取设备列表，共 ${data.machines.length} 台设备`);
            } else {
              throw new Error('获取设备列表格式错误');
            }
          })()
        ]);
      } catch (error: any) {
        console.error('获取设备列表失败:', error);
        setError(error.message || '获取设备列表失败');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDevices();
  }, [fetchSiteInfo]);

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.trim() === '') {
      setFilteredDevices(devices);
    } else {
      const filtered = devices.filter(device =>
        device.name.toLowerCase().includes(value.toLowerCase()) ||
        (device.brand && device.brand.toLowerCase().includes(value.toLowerCase())) ||
        (device.model && device.model.toLowerCase().includes(value.toLowerCase())) ||
        (device.location && device.location.toLowerCase().includes(value.toLowerCase()))
      );
      setFilteredDevices(filtered);
    }
  };

  // 清除搜索
  const clearSearch = () => {
    setSearchTerm('');
    setFilteredDevices(devices);
  };

  // 返回设备列表
  const goBackToDeviceList = () => {
    router.push('/');
  };

  // 获取状态的颜色
  const getStatusColor = (status?: string): string => {
    if (!status) return '#6b7280'; // 无状态时返回默认灰色

    // 使用默认颜色映射
    const defaultColors: Record<string, string> = {
      'production': '#22c55e', // 绿色
      'idle': '#f59e0b',       // 琥珀色
      'fault': '#ef4444',      // 红色
      'adjusting': '#3b82f6',  // 蓝色
      'shutdown': '#6b7280',   // 灰色
      'disconnected': '#9ca3af', // 浅灰色
      'maintenance': '#8b5cf6', // 紫色
      'debugging': '#06b6d4'   // 青色
    };

    // 优先使用设置中的颜色，如果没有则使用默认颜色
    return displaySettings?.statusColors?.[status] || defaultColors[status] || '#6b7280';
  };

  // 获取状态的中文名称
  const getStatusName = (status?: string): string => {
    if (!status) return '未知';

    const statusMap: Record<string, string> = {
      'production': '生产中',
      'idle': '空闲',
      'fault': '故障',
      'adjusting': '调机',
      'shutdown': '关机',
      'disconnected': '未连接',
      'maintenance': '维护',
      'debugging': '调试'
    };

    return statusMap[status] || status;
  };

  return (
    <div className="flex">
      {/* 侧边导航栏 - 在非最大化状态下显示 */}
      {!isMaximized && <Sidebar mode={sidebarMode} activePage="device_status_list" />}

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-h-screen">
        {/* 顶部标题栏 */}
        <Header
          companyInfo={companyInfo}
          isMaximized={isMaximized}
          toggleMaximize={toggleMaximize}
          toggleSidebar={toggleSidebar}
        />

        {/* 页面标题 */}
        <div className="bg-blue-900/20 py-2 px-6">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <button
                onClick={goBackToDeviceList}
                className="mr-3 p-1 rounded-full hover:bg-gray-700"
                title="返回设备列表"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <h2 className="text-xl font-bold text-white flex items-center">
                <History className="mr-2 h-5 w-5" />
                设备状态历史
              </h2>
            </div>
          </div>

          {/* 搜索框 */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索设备..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full bg-gray-800 border border-gray-700 rounded-md pl-10 pr-10 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            {searchTerm && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-2.5"
              >
                <X className="h-4 w-4 text-gray-400 hover:text-white" />
              </button>
            )}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-6 overflow-auto">
          {isLoading ? (
            <LoadingSpinner message="加载设备列表中..." />
          ) : error ? (
            <ErrorAlert message={error} />
          ) : (
            <div className="bg-gray-800 rounded-md shadow-md overflow-hidden">
              <div className="p-4">
                <h3 className="text-lg font-medium mb-4">选择设备查看状态历史</h3>

                {/* 设备列表 */}
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                  {filteredDevices.length === 0 ? (
                    <div className="col-span-full text-center py-8 text-gray-400">
                      未找到匹配的设备
                    </div>
                  ) : (
                    filteredDevices.map(device => (
                      <Link
                        key={device.id}
                        href={`/device_status_history/${device.id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-gray-900 rounded-md overflow-hidden hover:bg-gray-800 transition-colors cursor-pointer block"
                      >
                        <div className="p-3">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-base truncate" title={device.name}>{device.name}</h4>
                              <div className="text-xs text-gray-400 mt-1 space-y-0.5">
                                {device.brand && <div className="truncate" title={`品牌: ${device.brand}`}>品牌: {device.brand}</div>}
                                {device.model && <div className="truncate" title={`型号: ${device.model}`}>型号: {device.model}</div>}
                                {device.location && <div className="truncate" title={`位置: ${device.location}`}>位置: {device.location}</div>}
                              </div>
                            </div>
                            <div className="flex items-center space-x-1 ml-2 flex-shrink-0">
                              <ExternalLink className="h-3 w-3 text-blue-400" />
                              <ArrowRight className="h-4 w-4 text-blue-400" />
                            </div>
                          </div>


                        </div>
                      </Link>
                    ))
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
