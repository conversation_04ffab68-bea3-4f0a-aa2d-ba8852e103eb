# 部署指南

## 🎯 概述

本指南提供了在不同环境中部署 30_machine_status 的完整解决方案，解决了在不同服务器上自动适配 API 地址的问题。

## 🔧 解决方案

### 核心思路
1. **使用相对路径** - 前端通过 `/api` 访问后端
2. **反向代理** - Nginx 将 `/api` 请求转发到后端服务
3. **环境自适应** - 根据部署环境自动选择合适的配置

## 🚀 快速部署

### 方法 1: 使用部署脚本（推荐）

```bash
# 本地开发环境
./deploy.sh local

# Docker 环境
./deploy.sh docker --build

# 生产环境
./deploy.sh production --api-url /api
```

### 方法 2: 手动配置

#### 本地开发
```bash
# 使用开发环境配置
cp .env.example .env.local
npm run dev
```

#### Docker 环境
```bash
# 使用 Docker 配置
docker-compose --env-file .env.docker up -d
```

#### 生产环境
```bash
# 使用生产环境配置
docker-compose -f docker-compose.production.yml up -d
```

## 📋 环境配置

### 1. 本地开发环境
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
NODE_ENV=development
```

### 2. Docker 容器环境
```env
NEXT_PUBLIC_API_BASE_URL=http://mdc_server_api:9005
NODE_ENV=production
```

### 3. 生产环境（推荐）
```env
NEXT_PUBLIC_API_BASE_URL=/api
NODE_ENV=production
```

## 🌐 Nginx 配置

### 基础配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端应用
    location / {
        proxy_pass http://localhost:9100;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API 代理
    location /api/ {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://localhost:9005;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 完整配置
参考 `nginx/nginx.conf` 文件，包含：
- 安全头设置
- 静态资源缓存
- CORS 支持
- 健康检查
- 错误处理

## 🐳 Docker 部署

### 开发环境
```bash
docker-compose up -d
```

### 生产环境
```bash
docker-compose -f docker-compose.production.yml up -d
```

## 🔄 自动适配逻辑

### API URL 解析流程
1. **检查配置值**
   - 相对路径 (`/api`) → 直接使用
   - 容器名称 (`mdc_server_api:9005`) → 转换处理
   - 完整URL → 直接使用

2. **环境判断**
   - 浏览器环境 → 使用当前主机地址
   - 服务器环境 → 使用容器间通信

3. **智能转换**
   - 开发环境 → `localhost:9005`
   - 生产环境 → `/api` (通过反向代理)

## 📊 部署架构

### 架构 1: 直接访问（开发环境）
```
浏览器 → http://localhost:9100 (前端)
浏览器 → http://localhost:9005 (API)
```

### 架构 2: 反向代理（生产环境）
```
浏览器 → http://your-domain.com/ → Nginx → localhost:9100 (前端)
浏览器 → http://your-domain.com/api → Nginx → localhost:9005 (API)
```

### 架构 3: Docker 容器间通信
```
mdc_dashboard → http://mdc_server_api:9005 (容器间)
浏览器 → http://localhost:9100 (端口映射)
```

## 🛠️ 故障排除

### 问题 1: API 连接失败
**症状**: 前端无法访问 API

**解决方案**:
1. 检查 API 服务是否运行
2. 验证网络连接
3. 确认环境变量配置

```bash
# 检查服务状态
curl http://localhost:9005/health

# 检查容器网络
docker network inspect mdc_full_mdc_network
```

### 问题 2: 跨域错误
**症状**: CORS 错误

**解决方案**:
1. 使用反向代理统一域名
2. 配置 CORS 头
3. 检查 Nginx 配置

### 问题 3: 静态资源加载失败
**症状**: CSS/JS 文件 404

**解决方案**:
1. 检查 Nginx 静态资源配置
2. 验证文件路径
3. 确认缓存设置

## 📈 性能优化

### 1. 启用 Gzip 压缩
```nginx
gzip on;
gzip_types text/plain text/css application/javascript;
```

### 2. 设置缓存头
```nginx
location ~* \.(js|css|png|jpg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 启用 HTTP/2
```nginx
listen 443 ssl http2;
```

## 🔒 安全配置

### 1. SSL/TLS
```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512;
```

### 2. 安全头
```nginx
add_header X-Frame-Options "SAMEORIGIN";
add_header X-Content-Type-Options "nosniff";
add_header X-XSS-Protection "1; mode=block";
```

### 3. 访问控制
```nginx
# 限制访问频率
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req zone=api burst=20 nodelay;
```

## 📝 检查清单

部署前请确认：

- [ ] 环境变量配置正确
- [ ] 网络连接正常
- [ ] 端口映射配置
- [ ] 防火墙设置
- [ ] SSL 证书（如果使用 HTTPS）
- [ ] 域名解析
- [ ] 健康检查配置
- [ ] 日志配置
- [ ] 备份策略

## 🆘 获取帮助

如果遇到问题：

1. 查看日志文件
2. 运行健康检查
3. 验证网络连接
4. 检查配置文件

```bash
# 查看容器日志
docker logs mdc_dashboard
docker logs mdc_nginx

# 测试连接
./test_docker_connection.sh

# 验证配置
nginx -t
```
