#!/bin/bash

# 机器状态前端启动脚本
# 解决Node.js环境配置问题并启动30_machine_status前端服务

echo "🚀 启动 机器状态前端"
echo "=================================="

# 检查并修复Node.js环境
echo "🔧 检查Node.js环境..."

# 设置正确的Node.js环境变量
export PATH=/opt/homebrew/bin:$PATH

# 验证Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ 错误: Node.js未安装或不在PATH中"
    echo "请确保Node.js已正确安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm未安装或不在PATH中"
    echo "请确保npm已正确安装"
    exit 1
fi

# 显示Node.js版本信息
echo "✅ Node.js版本: $(node --version)"
echo "✅ npm版本: $(npm --version)"

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    echo "请确保在30_machine_status目录中运行此脚本"
    exit 1
fi

echo "✅ 项目文件检查通过"

# 从环境变量读取端口配置，如果没有则使用默认值
PORT=${NEXT_PUBLIC_APP_PORT:-9100}

# 检查端口是否被占用
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 $PORT 已被占用"
    echo "正在尝试停止占用端口的进程..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 检查node_modules是否存在
if [ ! -d "node_modules" ]; then
    echo "📦 安装npm依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 错误: npm依赖安装失败"
        exit 1
    fi
else
    echo "✅ npm依赖已存在"
fi

# 设置环境变量
echo "🔧 设置环境变量..."

# 从 .env 文件读取环境变量（如果存在）
if [ -f ".env" ]; then
    echo "📄 加载 .env 文件..."
    export $(grep -v '^#' .env | xargs)
else
    echo "⚠️  未找到 .env 文件，使用默认配置"
fi

# 设置关键环境变量，使用 .env 中的值或默认值
export PORT=${NEXT_PUBLIC_APP_PORT:-$PORT}
export NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-"http://localhost:9005"}
export NODE_ENV=${NODE_ENV:-"development"}
export NEXT_TELEMETRY_DISABLED=${NEXT_TELEMETRY_DISABLED:-1}

echo "✅ 环境变量设置完成"
echo "  - PORT: $PORT"
echo "  - NEXT_PUBLIC_API_BASE_URL: $NEXT_PUBLIC_API_BASE_URL"
echo "  - NODE_ENV: $NODE_ENV"

echo ""
echo "🌐 启动机器状态前端..."
echo "端口: $PORT"
echo "访问地址: http://localhost:$PORT"
echo "API后端: $NEXT_PUBLIC_API_BASE_URL"
echo ""
echo "功能特性:"
echo "  - 机器状态实时监控"
echo "  - 设备运行状态展示"
echo "  - 生产数据统计"
echo "  - 报警信息显示"
echo "  - 响应式设计"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 启动Next.js开发服务器
npm run dev
