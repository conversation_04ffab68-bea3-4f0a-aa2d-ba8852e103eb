# 制造业生产设备状态监控系统

基于Next.js和Tailwind CSS构建的实时设备状态监控仪表板。

## 功能特点

- 实时监控生产设备状态
- 设备状态分类显示（生产中、空闲、故障、调机、关机、未连接）
- 动态更新设备状态和数量统计
- 响应式布局，适应不同屏幕尺寸

## 技术栈

- Next.js 14
- React 18
- TypeScript
- Tailwind CSS
- Lucide React (图标)

## 开始使用

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
npm start
```

## 项目结构

```
src/
├── app/                # 应用程序路由和页面
├── components/         # UI组件
│   ├── features/       # 功能组件
│   ├── layout/         # 布局组件
│   └── shared/         # 共享组件
├── types/              # TypeScript类型定义
└── utils/              # 工具函数
```
