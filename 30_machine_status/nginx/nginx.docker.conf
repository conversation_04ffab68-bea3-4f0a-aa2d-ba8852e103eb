# ================================
# Docker 环境专用 Nginx 配置
# ================================
# 此配置专门用于 Docker 容器环境，使用容器名称进行通信

upstream frontend {
    # 前端服务 - 使用 Docker 容器名称
    server mdc_dashboard:9100;
}

upstream backend {
    # 后端API服务 - 使用 Docker 容器名称
    server mdc_server_api:9005;
}

# 主服务器配置
server {
    listen 80;
    
    # server_name 配置说明：
    # - localhost: 适用于本地测试
    # - _: 匹配所有域名（通配符）
    # - your-domain.com: 替换为你的实际域名
    server_name _;  # 匹配所有域名，适用于 Docker 环境
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 日志配置
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # 前端应用代理
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API 代理
    location /api/ {
        # 移除 /api 前缀，转发到后端
        rewrite ^/api/(.*)$ /$1 break;
        
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API 特定设置
        proxy_buffering off;
        proxy_request_buffering off;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://backend/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        access_log off;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# ================================
# 配置说明
# ================================

# 1. upstream 配置：
#    - frontend: mdc_dashboard:9100 (前端容器)
#    - backend: mdc_server_api:9005 (后端容器)
#    这些是 Docker 容器名称，用于容器间通信

# 2. server_name 配置：
#    - localhost: 本地访问
#    - _: 匹配所有域名（推荐用于 Docker）
#    - 具体域名: 生产环境使用

# 3. 访问方式：
#    - 前端: http://localhost/ (通过宿主机端口访问)
#    - API: http://localhost/api/ (通过 Nginx 代理)

# 4. Docker 网络要求：
#    - 所有容器必须在同一个 Docker 网络中
#    - 容器名称必须与 upstream 配置一致
