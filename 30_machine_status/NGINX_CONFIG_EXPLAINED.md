# Nginx 配置详解

## 🔍 关键概念澄清

### `server_name` vs `upstream`

很多人容易混淆这两个配置的作用：

#### ❌ **错误理解**
```nginx
server {
    server_name mdc_server_api;  # 这不是用来访问后端API的！
}
```

#### ✅ **正确理解**
```nginx
# upstream 用于定义后端服务地址
upstream backend {
    server mdc_server_api:9005;  # 这里才是后端服务地址
}

server {
    server_name localhost;  # 这是匹配请求域名的
    
    location /api/ {
        proxy_pass http://backend;  # 转发到 upstream
    }
}
```

## 📋 配置详解

### 1. `server_name` 的作用

`server_name` 用于匹配 HTTP 请求头中的 `Host` 字段：

```nginx
server {
    listen 80;
    server_name example.com;  # 只处理 Host: example.com 的请求
}

server {
    listen 80;
    server_name _;  # 匹配所有域名（通配符）
}
```

**请求流程**：
```
浏览器发送: GET / HTTP/1.1
           Host: example.com
           ↓
Nginx 根据 Host 头选择对应的 server 块
```

### 2. `upstream` 的作用

`upstream` 定义后端服务的实际地址：

```nginx
upstream backend {
    server mdc_server_api:9005;  # Docker 容器名称
    # 或者
    # server *************:9005;  # IP 地址
    # 或者
    # server localhost:9005;       # 本地服务
}
```

### 3. 完整的请求流程

```
1. 浏览器请求: http://localhost/api/machines
   ↓
2. Nginx 接收请求，Host: localhost
   ↓
3. 匹配 server_name localhost 的 server 块
   ↓
4. 匹配 location /api/ 规则
   ↓
5. 转发到 upstream backend (mdc_server_api:9005)
   ↓
6. 实际请求: http://mdc_server_api:9005/machines
```

## 🐳 Docker 环境配置

### 网络架构
```
外部访问 → Nginx容器 → 前端容器 + 后端容器
   ↓           ↓           ↓
  :80      :80→:9100   :9005
```

### 容器间通信
```nginx
upstream frontend {
    server mdc_dashboard:9100;    # 容器名称:端口
}

upstream backend {
    server mdc_server_api:9005;   # 容器名称:端口
}
```

**重要**：所有容器必须在同一个 Docker 网络中！

## 🔧 不同环境的配置

### 1. 本地开发（无 Docker）
```nginx
upstream backend {
    server localhost:9005;  # 本地服务
}

server {
    server_name localhost;
}
```

### 2. Docker 环境
```nginx
upstream backend {
    server mdc_server_api:9005;  # 容器名称
}

server {
    server_name _;  # 匹配所有域名
}
```

### 3. 生产环境
```nginx
upstream backend {
    server api.internal:9005;  # 内部服务地址
}

server {
    server_name your-domain.com;  # 实际域名
}
```

## 🚀 实际部署示例

### 场景：部署到云服务器

假设你的服务器 IP 是 `*************`：

#### 1. Docker Compose 配置
```yaml
services:
  nginx:
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.docker.conf:/etc/nginx/conf.d/default.conf
```

#### 2. Nginx 配置
```nginx
upstream backend {
    server mdc_server_api:9005;  # 容器名称
}

server {
    server_name _;  # 匹配所有域名
    # 或者指定具体域名：
    # server_name ************* your-domain.com;
}
```

#### 3. 访问方式
```
前端: http://*************/
API:  http://*************/api/
```

## 🛠️ 常见问题

### Q1: 为什么不能直接设置 `server_name mdc_server_api`？

**A**: 因为 `server_name` 是用来匹配请求域名的，而 `mdc_server_api` 是容器名称，不是域名。

浏览器发送的请求是：
```
GET / HTTP/1.1
Host: localhost  # 或者你的域名
```

而不是：
```
GET / HTTP/1.1
Host: mdc_server_api  # 这是错误的
```

### Q2: 如何验证配置是否正确？

**A**: 
```bash
# 1. 检查 Nginx 配置语法
docker exec mdc_nginx nginx -t

# 2. 查看 upstream 状态
docker exec mdc_nginx nginx -T | grep upstream

# 3. 测试连接
curl http://localhost/api/health
```

### Q3: 容器间无法通信怎么办？

**A**: 确保所有容器在同一网络中：
```bash
# 检查网络
docker network inspect mdc_production_network

# 确认容器都在网络中
docker ps --format "table {{.Names}}\t{{.Networks}}"
```

## 📝 配置模板

### 基础模板
```nginx
upstream backend {
    server mdc_server_api:9005;
}

server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://mdc_dashboard:9100;
    }
    
    location /api/ {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://backend;
    }
}
```

### 生产环境模板
```nginx
upstream backend {
    server mdc_server_api:9005;
    # 可以添加多个后端实现负载均衡
    # server mdc_server_api_2:9005;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    
    # 其他配置...
}
```

## 🎯 总结

- `server_name` = 匹配请求域名
- `upstream` = 定义后端服务地址
- Docker 环境使用容器名称进行通信
- 所有容器必须在同一网络中
- 通过 Nginx 实现统一域名访问
