# ================================
# 生产环境配置
# ================================
# 此文件用于生产环境部署

# ================================
# API 配置 - 生产环境
# ================================

# 使用相对路径，通过反向代理访问 API
# 这样可以自动适配部署的主机地址
NEXT_PUBLIC_API_BASE_URL=/api

# 应用端口
NEXT_PUBLIC_APP_PORT=9100

# 生产环境标识
NODE_ENV=production

# 禁用遥测
NEXT_TELEMETRY_DISABLED=1

# ================================
# 性能优化配置
# ================================

# API 请求超时时间（生产环境可以设置更长）
NEXT_PUBLIC_API_TIMEOUT=15000

# API 重试次数
NEXT_PUBLIC_API_RETRY_COUNT=3

# 关闭调试模式
NEXT_PUBLIC_DEBUG_API=false

# ================================
# 部署说明
# ================================

# 生产环境部署时，需要配置反向代理：
# 
# Nginx 配置示例：
# server {
#     listen 80;
#     server_name your-domain.com;
#     
#     # 前端静态文件
#     location / {
#         proxy_pass http://localhost:9100;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#     }
#     
#     # API 代理
#     location /api/ {
#         proxy_pass http://localhost:9005/;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }
# }

# ================================
# Docker Compose 生产环境启动
# ================================

# 使用此配置启动：
# docker-compose --env-file .env.production up -d
