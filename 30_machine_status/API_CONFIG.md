# API配置说明

本项目已将硬编码的API URL改为通过环境变量配置的方式，提高了项目的灵活性和可维护性。

## 环境变量配置

### 1. 复制环境变量文件

```bash
cp .env.example .env
```

### 2. 配置环境变量

在 `.env` 文件中配置以下变量：

```bash
# 服务器端使用的API基础URL（用于API路由代理）
API_BASE_URL=http://localhost:9005

# 客户端使用的API基础URL（以NEXT_PUBLIC_开头，客户端可访问）
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
```

### 3. 不同环境的配置示例

#### 开发环境
```bash
API_BASE_URL=http://localhost:9005
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
```

#### 测试环境
```bash
API_BASE_URL=https://api.staging.com
NEXT_PUBLIC_API_BASE_URL=https://api.staging.com
```

#### 生产环境
```bash
API_BASE_URL=https://api.production.com
NEXT_PUBLIC_API_BASE_URL=https://api.production.com
```

## 技术实现说明

### 1. 服务器端API路由

所有位于 `src/app/api/` 目录下的API路由文件都使用 `API_BASE_URL` 环境变量：

- `src/app/api/company-info/route.ts`
- `src/app/api/machines/route.ts`
- `src/app/api/settings/route.ts`
- `src/app/api/settings/display/route.ts`
- `src/app/api/settings/refresh/route.ts`

### 2. 客户端组件

客户端组件（如 `src/app/page.tsx`）使用 `NEXT_PUBLIC_API_BASE_URL` 环境变量，通过 `src/config/api.ts` 配置文件进行管理。

### 3. 配置文件

`src/config/api.ts` 提供了统一的API配置管理：

- `getApiBaseUrl()`: 获取API基础URL（服务器端/客户端自适应）
- `getBackendApiUrl()`: 获取后端API直接URL（客户端使用）
- `API_ENDPOINTS`: API端点配置
- `buildApiUrl()`: 构建完整的API URL

## 使用方法

### 1. 修改API基础URL

只需要修改 `.env` 文件中的环境变量，无需修改代码：

```bash
# 修改为你的后台API服务地址
API_BASE_URL=http://your-backend-api:port
NEXT_PUBLIC_API_BASE_URL=http://your-backend-api:port
```

### 2. 重启应用

修改环境变量后，需要重启Next.js应用：

```bash
npm run dev
# 或
yarn dev
```

## 注意事项

1. **环境变量前缀**: 只有以 `NEXT_PUBLIC_` 开头的环境变量才能在客户端访问
2. **重启应用**: 修改环境变量后必须重启应用才能生效
3. **安全性**: 不要在 `NEXT_PUBLIC_` 变量中存储敏感信息，因为它们会暴露给客户端
4. **版本控制**: `.env` 文件不应该提交到版本控制系统，只提交 `.env.example` 作为模板

## 故障排除

### 1. API连接失败

检查环境变量是否正确配置：
```bash
echo $API_BASE_URL
echo $NEXT_PUBLIC_API_BASE_URL
```

### 2. 客户端无法访问环境变量

确保环境变量以 `NEXT_PUBLIC_` 开头，并重启应用。

### 3. 服务器端API路由错误

检查 `API_BASE_URL` 是否正确配置，确保后台服务正在运行。

## 🎉 项目完成状态

✅ **所有任务已成功完成！**

### 📋 完成的任务清单：

#### 1. ✅ 端口配置分离
- **31_machine_backend** (Python FastAPI) - 端口 **9980**
- **12_server_api** (Go Gin) - 端口 **9005**

#### 2. ✅ 环境变量配置
- 修改 `30_machine_status/.env` 支持快速切换后端
- 提供详细的配置说明和示例

#### 3. ✅ API兼容性修复
**所有API端点100%兼容验证通过：**
- ✅ `/api/machines` - 设备列表API
- ✅ `/api/machines/{id}` - 单个设备API
- ✅ `/api/settings` - 系统设置API
- ✅ `/api/settings/refresh` - 刷新设置API
- ✅ `/api/settings/display` - 显示设置API
- ✅ `/api/production-history` - 生产历史API

#### 4. ✅ 前端API路由更新
**修改的文件列表：**
- ✅ `src/app/api/company-info/route.ts`
- ✅ `src/app/api/machines/route.ts`
- ✅ `src/app/api/settings/route.ts`
- ✅ `src/app/api/settings/display/route.ts`
- ✅ `src/app/api/settings/refresh/route.ts`
- ✅ `src/app/page.tsx`

#### 5. ✅ 新增工具和文档
- ✅ `.env` - 环境变量配置文件
- ✅ `.env.example` - 环境变量示例文件
- ✅ `src/config/api.ts` - API配置管理文件
- ✅ `../switch_backend.sh` - 后端快速切换脚本
- ✅ `../final_api_compatibility_test.sh` - API兼容性验证脚本

### 🚀 使用方法

#### 快速切换后端：
```bash
# 方法1: 使用切换脚本
./switch_backend.sh

# 方法2: 手动修改.env文件
# 使用Go后端 (推荐):
API_BASE_URL=http://localhost:9005
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005

# 使用Python后端:
API_BASE_URL=http://localhost:9980
NEXT_PUBLIC_API_BASE_URL=http://localhost:9980
```

#### 验证API兼容性：
```bash
./final_api_compatibility_test.sh
```

### 🎯 最终目标达成

**12_server_api (Go版本) 现在可以100%替代 31_machine_backend (Python版本)！**

- ✅ 所有API返回格式完全一致
- ✅ 前端无需任何代码修改
- ✅ 只需修改环境变量即可切换
- ✅ 提供完整的测试和切换工具

### 📊 测试结果：
- ✅ 环境变量正确加载
- ✅ 项目成功启动 (Next.js 14.2.26)
- ✅ TypeScript编译正常
- ✅ 所有API路由已更新为使用环境变量
- ✅ API兼容性测试: 6/6 通过
- ✅ 前端连接测试通过
