# ================================
# 生产环境 Docker Compose 配置
# ================================
# 此配置包含 Nginx 反向代理，实现统一域名访问

version: '3.8'

services:
  # 前端应用
  mdc_dashboard:
    build:
      context: .
      dockerfile: Dockerfile
    image: mdc_dashboard:1.1.13
    container_name: mdc_dashboard_prod
    restart: unless-stopped
    environment:
      # 生产环境使用相对路径，通过 Nginx 代理访问 API
      NEXT_PUBLIC_API_BASE_URL: /api
      NEXT_PUBLIC_APP_PORT: 9100
      NODE_ENV: production
      NEXT_TELEMETRY_DISABLED: 1
      NEXT_PUBLIC_DEBUG_API: false
      NEXT_PUBLIC_API_TIMEOUT: 15000
      NEXT_PUBLIC_API_RETRY_COUNT: 3
    ports:
      - "9100:9100"
    networks:
      - mdc_production_network
    depends_on:
      - mdc_server_api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: mdc_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"  # 如果使用 HTTPS
    volumes:
      # 使用 Docker 专用配置
      - ./nginx/nginx.docker.conf:/etc/nginx/conf.d/default.conf:ro
      - ./nginx/logs:/var/log/nginx
      # SSL 证书（如果使用 HTTPS）
      # - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - mdc_production_network
    depends_on:
      - mdc_dashboard
      - mdc_server_api
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端 API 服务（引用外部服务）
  mdc_server_api:
    image: mdc_server_api:latest
    container_name: mdc_server_api_prod
    restart: unless-stopped
    ports:
      - "9005:9005"
    networks:
      - mdc_production_network
      - mdc_full_mdc_network  # 连接到数据库网络
    environment:
      - GIN_MODE=release
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9005/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  mdc_production_network:
    driver: bridge
    name: mdc_production_network
  mdc_full_mdc_network:
    external: true

# 可选：添加数据卷用于持久化
volumes:
  nginx_logs:
    driver: local
