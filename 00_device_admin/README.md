设备管理
分前后端, 前端在 /ui 文件夹, 后端在 /api 文件夹

# 系统
- ubuntu
- arm v7

# ui 技术
- react 18.x
- nextjs
- shadcn
- URL 无 # 符号
- URL 格式 : host:port/[function]?[params]
- docker 打包

# api
为前端ui提供数据,功能
- golang 1.21.x
- goframe 2.x
- .env 配置文件
- docker 打包


# 功能
- 登录
用户通过输入账号和密码进入设备管理系统，进行身份验证后才能访问后续功能。

- 改密码
用户可以修改自己的登录密码，以增强账户安全性。

- 查询设备唯一ID号
支持查询和显示设备的唯一标识符（如序列号、UUID等），用于设备识别和管理。

- 设置远程设备服务器
可以配置设备连接的远程服务器地址，实现设备与服务器之间的数据通信。

- 查看/同步设备清单
查看当前管理的所有设备列表，并支持与服务器同步，确保设备清单的最新状态。
设备清单由远程设备服务器统一管理, 设备清单从远程服务器获取

- 查看设备状态
将设备的状态信息主动推送到远程服务器，便于远程监控和管理。
显示成功或失败

- 推送状态
将设备的状态信息主动推送到远程服务器，便于远程监控和管理。
显示成功或失败

- 采集状态
将设备的状态信息主动推送到远程服务器，便于远程监控和管理。

- 重启设备
单击重启按钮可以重启设备

- 设置 IP

- 连接 WIFI 设备

-
