# Docker构建忽略文件
# 排除不需要复制到Docker镜像中的文件和目录

# Git相关
.git/
.gitignore
.gitattributes

# 构建产物
influxdb-test
*.exe
*.dll
*.so
*.dylib

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/
data/

# 临时文件
tmp/
temp/
.tmp/

# 测试文件
*_test.go
test/
tests/

# 文档
README.md
docs/
*.md

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 脚本文件
*.sh
run.sh
docker_build_linux_x86.sh

# 配置文件备份
*.bak
*.backup
config.*.yml

# 操作系统文件
.DS_Store
Thumbs.db
desktop.ini

# 编辑器配置
.editorconfig

# 依赖缓存
vendor/
node_modules/

# 环境变量文件
.env
.env.local
.env.*.local

# 证书文件
*.pem
*.key
*.crt
*.p12

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 压缩文件
*.zip
*.tar.gz
*.rar

# IDE生成的文件
*.iml
.project
.classpath
.settings/

# 覆盖率报告
coverage.out
coverage.html

# 性能分析文件
*.prof
*.pprof

# 二进制文件
bin/
build/
dist/
