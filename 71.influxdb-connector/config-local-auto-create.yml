# InfluxDB本地自动创建测试配置文件
# 用于在本地环境测试自动创建组织和存储桶功能

# 服务配置
service:
  name: "influxdb-local-auto-create-test"
  debug: true
  timeout: 30s

# InfluxDB配置 - 本地环境
influxdb:
  url: "http://localhost:8086"                # 本地InfluxDB地址
  token: "1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw=="
  
  # 使用新的组织和存储桶名称（将被自动创建）
  organization: "test-auto-org"             # 新组织名称
  bucket: "test-auto-bucket"                # 新存储桶名称
  measurement: "auto_test_data"
  batch_size: 100
  flush_interval: 5s
  timeout: 15s
  retry_count: 3
  retry_delay: 2s
  
  # 自动创建功能 - 启用
  auto_create_org: true                     # 启用自动创建组织
  auto_create_bucket: true                  # 启用自动创建存储桶
  bucket_retention_days: 7                  # 测试保留7天
  organization_description: "自动创建的测试组织 - 本地环境测试"
  bucket_description: "自动创建的测试存储桶 - 本地环境测试数据"

# 测试配置
test:
  write_count: 20                           # 少量测试数据
  query_limit: 10
  test_device_id: "local-auto-test-device"
  test_interval: 1s

# 日志配置
logging:
  enable_file: true
  log_dir: "./logs"
  log_file: "influxdb-local-auto-create-test.log"
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true
  level: "info"
  format: "text"
