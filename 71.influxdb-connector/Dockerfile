# InfluxDB测试程序 Docker镜像
# 基于Alpine Linux的多阶段构建，优化镜像大小

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具和依赖
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

# 设置Go环境变量
ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct \
    GOSUMDB=off \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用程序
# 添加构建信息和版本标识
ARG BUILD_TIME
ARG GIT_COMMIT
ARG VERSION=1.0.0

RUN go build \
    -ldflags "-s -w -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT} -X main.Version=${VERSION}" \
    -o influxdb-test \
    .

# 运行阶段
FROM alpine:3.18

# 安装运行时依赖和网络工具
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    curl \
    netcat-openbsd \
    bind-tools

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/influxdb-test .

# 复制配置文件
COPY --from=builder /app/*.yml ./

# 创建数据目录
RUN mkdir -p /app/data && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 暴露端口（如果需要HTTP服务）
EXPOSE 8080

# 设置入口点
ENTRYPOINT ["./influxdb-test"]

# 默认命令参数
CMD ["config.yml"]

# 添加标签信息
LABEL maintainer="MDC System Team" \
      version="${VERSION}" \
      description="InfluxDB测试程序 - 用于测试InfluxDB连接、写入和查询功能" \
      org.opencontainers.image.title="InfluxDB Test Tool" \
      org.opencontainers.image.description="企业级InfluxDB连接和性能测试工具" \
      org.opencontainers.image.version="${VERSION}" \
      org.opencontainers.image.created="${BUILD_TIME}" \
      org.opencontainers.image.revision="${GIT_COMMIT}" \
      org.opencontainers.image.source="https://github.com/mdc-system/influxdb-test"
