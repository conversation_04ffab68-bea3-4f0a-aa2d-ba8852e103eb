#!/bin/bash

# InfluxDB测试程序 Linux x86 Docker构建脚本
# 功能：构建、标记和推送Docker镜像到仓库

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="influxdb-test"
IMAGE_NAME="mdc-system/influxdb-test"
DOCKERFILE="Dockerfile"

# 默认配置
DEFAULT_VERSION="1.0.0"
DEFAULT_REGISTRY=""
DEFAULT_PLATFORM="linux/amd64"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "InfluxDB测试程序 Docker构建脚本"
    echo ""
    echo "用法: $0 [选项] [命令]"
    echo ""
    echo "命令:"
    echo "  build        构建Docker镜像（默认）"
    echo "  push         推送镜像到仓库"
    echo "  build-push   构建并推送镜像"
    echo "  clean        清理本地镜像"
    echo "  run          运行容器"
    echo "  stop         停止容器"
    echo "  logs         查看容器日志"
    echo "  shell        进入容器shell"
    echo "  help         显示帮助信息"
    echo ""
    echo "选项:"
    echo "  -v, --version VERSION    镜像版本标签（默认: $DEFAULT_VERSION）"
    echo "  -r, --registry REGISTRY  Docker仓库地址"
    echo "  -p, --platform PLATFORM  目标平台（默认: $DEFAULT_PLATFORM）"
    echo "  -t, --tag TAG           额外的镜像标签"
    echo "  -f, --file DOCKERFILE   Dockerfile路径（默认: $DOCKERFILE）"
    echo "  --no-cache              构建时不使用缓存"
    echo "  --push-latest           同时推送latest标签"
    echo "  --verbose               详细输出"
    echo "  -h, --help              显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY         Docker仓库地址"
    echo "  IMAGE_VERSION           镜像版本"
    echo "  BUILD_ARGS              额外的构建参数"
    echo ""
    echo "示例:"
    echo "  $0 build                           # 构建默认版本镜像"
    echo "  $0 build -v 1.2.0                 # 构建指定版本镜像"
    echo "  $0 build-push -v 1.2.0 --push-latest  # 构建并推送镜像"
    echo "  $0 run -v 1.2.0                   # 运行指定版本容器"
    echo "  $0 clean                          # 清理本地镜像"
}

# 获取构建信息
get_build_info() {
    # 构建时间
    BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    
    # Git信息
    if git rev-parse --git-dir > /dev/null 2>&1; then
        GIT_COMMIT=$(git rev-parse --short HEAD)
        GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
        GIT_TAG=$(git describe --tags --exact-match 2>/dev/null || echo "")
    else
        GIT_COMMIT="unknown"
        GIT_BRANCH="unknown"
        GIT_TAG=""
    fi
    
    log_debug "构建信息:"
    log_debug "  构建时间: $BUILD_TIME"
    log_debug "  Git提交: $GIT_COMMIT"
    log_debug "  Git分支: $GIT_BRANCH"
    [ -n "$GIT_TAG" ] && log_debug "  Git标签: $GIT_TAG"
}

# 构建镜像标签
build_image_tags() {
    local version="$1"
    local registry="$2"
    
    # 基础镜像名
    local base_name="$IMAGE_NAME"
    if [ -n "$registry" ]; then
        base_name="$registry/$IMAGE_NAME"
    fi
    
    # 主要标签
    IMAGE_TAGS=("$base_name:$version")
    
    # 添加额外标签
    if [ -n "$EXTRA_TAG" ]; then
        IMAGE_TAGS+=("$base_name:$EXTRA_TAG")
    fi
    
    # 如果是主分支且版本不是latest，添加latest标签
    if [ "$PUSH_LATEST" = true ] && [ "$version" != "latest" ]; then
        IMAGE_TAGS+=("$base_name:latest")
    fi
    
    # 如果有Git标签，添加Git标签
    if [ -n "$GIT_TAG" ] && [ "$GIT_TAG" != "$version" ]; then
        IMAGE_TAGS+=("$base_name:$GIT_TAG")
    fi
    
    log_debug "镜像标签:"
    for tag in "${IMAGE_TAGS[@]}"; do
        log_debug "  $tag"
    done
}

# 构建Docker镜像
build_image() {
    local version="$1"
    local registry="$2"
    local platform="$3"
    local dockerfile="$4"
    local no_cache="$5"
    
    log_step "开始构建Docker镜像..."
    
    # 检查Dockerfile
    if [ ! -f "$dockerfile" ]; then
        log_error "Dockerfile不存在: $dockerfile"
        return 1
    fi
    
    # 获取构建信息
    get_build_info
    
    # 构建镜像标签
    build_image_tags "$version" "$registry"
    
    # 构建参数
    local build_args=(
        "--platform=$platform"
        "--file=$dockerfile"
        "--build-arg=BUILD_TIME=$BUILD_TIME"
        "--build-arg=GIT_COMMIT=$GIT_COMMIT"
        "--build-arg=VERSION=$version"
    )
    
    # 添加额外构建参数
    if [ -n "$BUILD_ARGS" ]; then
        IFS=' ' read -ra EXTRA_ARGS <<< "$BUILD_ARGS"
        for arg in "${EXTRA_ARGS[@]}"; do
            build_args+=("--build-arg=$arg")
        done
    fi
    
    # 不使用缓存
    if [ "$no_cache" = true ]; then
        build_args+=("--no-cache")
    fi
    
    # 添加所有标签
    for tag in "${IMAGE_TAGS[@]}"; do
        build_args+=("--tag=$tag")
    done
    
    # 构建上下文
    build_args+=(".")
    
    log_info "执行Docker构建命令..."
    if [ "$VERBOSE" = true ]; then
        log_debug "docker build ${build_args[*]}"
    fi
    
    # 执行构建
    if docker build "${build_args[@]}"; then
        log_success "Docker镜像构建成功"
        
        # 显示镜像信息
        log_info "构建的镜像:"
        for tag in "${IMAGE_TAGS[@]}"; do
            log_info "  $tag"
        done
        
        # 显示镜像大小
        local main_tag="${IMAGE_TAGS[0]}"
        local image_size=$(docker images --format "table {{.Size}}" "$main_tag" | tail -n 1)
        log_info "镜像大小: $image_size"
        
        return 0
    else
        log_error "Docker镜像构建失败"
        return 1
    fi
}

# 推送镜像
push_image() {
    local registry="$1"
    
    if [ -z "$registry" ]; then
        log_error "未指定Docker仓库地址"
        return 1
    fi
    
    log_step "开始推送Docker镜像..."
    
    # 推送所有标签
    for tag in "${IMAGE_TAGS[@]}"; do
        log_info "推送镜像: $tag"
        if docker push "$tag"; then
            log_success "推送成功: $tag"
        else
            log_error "推送失败: $tag"
            return 1
        fi
    done
    
    log_success "所有镜像推送完成"
}

# 运行容器
run_container() {
    local version="$1"
    local registry="$2"
    
    local image_name="$IMAGE_NAME:$version"
    if [ -n "$registry" ]; then
        image_name="$registry/$IMAGE_NAME:$version"
    fi
    
    log_step "运行Docker容器..."
    log_info "镜像: $image_name"
    
    # 停止现有容器
    if docker ps -q -f name="$PROJECT_NAME" | grep -q .; then
        log_info "停止现有容器..."
        docker stop "$PROJECT_NAME" || true
        docker rm "$PROJECT_NAME" || true
    fi
    
    # 运行新容器
    docker run -d \
        --name "$PROJECT_NAME" \
        --restart unless-stopped \
        -p 8080:8080 \
        -v "$(pwd)/config.yml:/app/config.yml:ro" \
        -v "$(pwd)/data:/app/data" \
        "$image_name"
    
    log_success "容器启动成功"
    log_info "容器名称: $PROJECT_NAME"
    log_info "查看日志: docker logs -f $PROJECT_NAME"
}

# 清理镜像
clean_images() {
    log_step "清理本地Docker镜像..."
    
    # 清理项目相关镜像
    local images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "$IMAGE_NAME" || true)
    
    if [ -n "$images" ]; then
        log_info "发现以下镜像:"
        echo "$images"
        
        read -p "确认删除这些镜像? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "$images" | xargs docker rmi -f
            log_success "镜像清理完成"
        else
            log_info "取消清理操作"
        fi
    else
        log_info "未找到相关镜像"
    fi
    
    # 清理悬空镜像
    local dangling=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling" ]; then
        log_info "清理悬空镜像..."
        docker rmi $dangling
    fi
}

# 主函数
main() {
    local command="build"
    local version="${IMAGE_VERSION:-$DEFAULT_VERSION}"
    local registry="${DOCKER_REGISTRY:-$DEFAULT_REGISTRY}"
    local platform="$DEFAULT_PLATFORM"
    local dockerfile="$DOCKERFILE"
    local no_cache=false
    
    # 全局变量
    PUSH_LATEST=false
    VERBOSE=false
    EXTRA_TAG=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--version)
                version="$2"
                shift 2
                ;;
            -r|--registry)
                registry="$2"
                shift 2
                ;;
            -p|--platform)
                platform="$2"
                shift 2
                ;;
            -t|--tag)
                EXTRA_TAG="$2"
                shift 2
                ;;
            -f|--file)
                dockerfile="$2"
                shift 2
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --push-latest)
                PUSH_LATEST=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            build|push|build-push|clean|run|stop|logs|shell|help)
                command="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 执行命令
    case $command in
        build)
            build_image "$version" "$registry" "$platform" "$dockerfile" "$no_cache"
            ;;
        push)
            if [ -z "$registry" ]; then
                log_error "推送镜像需要指定仓库地址 (-r 或 --registry)"
                exit 1
            fi
            build_image_tags "$version" "$registry"
            push_image "$registry"
            ;;
        build-push)
            if [ -z "$registry" ]; then
                log_error "推送镜像需要指定仓库地址 (-r 或 --registry)"
                exit 1
            fi
            if build_image "$version" "$registry" "$platform" "$dockerfile" "$no_cache"; then
                push_image "$registry"
            fi
            ;;
        clean)
            clean_images
            ;;
        run)
            run_container "$version" "$registry"
            ;;
        stop)
            log_info "停止容器: $PROJECT_NAME"
            docker stop "$PROJECT_NAME" || true
            docker rm "$PROJECT_NAME" || true
            ;;
        logs)
            log_info "查看容器日志: $PROJECT_NAME"
            docker logs -f "$PROJECT_NAME"
            ;;
        shell)
            log_info "进入容器shell: $PROJECT_NAME"
            docker exec -it "$PROJECT_NAME" /bin/sh
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
