# Docker 容器间 InfluxDB 通信指南

## 🐳 概述

当 InfluxDB 服务器和测试程序都运行在 Docker 容器中时，需要通过 Docker 网络进行通信。本指南提供了完整的解决方案。

## 🔧 解决方案

### 1. Docker Compose 网络（推荐）

#### 配置说明
- 使用 Docker Compose 自动创建网络
- 容器通过服务名进行通信
- 自动DNS解析和服务发现

#### 关键配置
```yaml
# docker-compose.yml
services:
  influxdb:
    image: influxdb:2.7
    container_name: influxdb
    # ... 其他配置

  influxdb-test:
    build: .
    depends_on:
      - influxdb
    environment:
      - INFLUXDB_URL=http://influxdb:8086  # 使用服务名
```

### 2. 网络通信配置

#### URL 配置对比
```yaml
# ❌ 错误配置（容器内无法访问）
influxdb:
  url: "http://localhost:8086"

# ✅ 正确配置（使用Docker服务名）
influxdb:
  url: "http://influxdb:8086"
```

#### 完整配置示例
```yaml
# config-docker-network.yml
influxdb:
  url: "http://influxdb:8086"               # Docker服务名
  token: "your-token-here"
  organization: "mdc-org"
  bucket: "device-data"
  timeout: 15s                              # 较长超时适应容器启动
  retry_count: 5                            # 更多重试次数
  retry_delay: 3s                           # 较长重试延迟
```

## 🚀 使用方法

### 快速开始

1. **设置Docker环境**
   ```bash
   ./test_docker_network.sh setup
   ```

2. **运行网络测试**
   ```bash
   ./test_docker_network.sh test
   ```

3. **查看容器状态**
   ```bash
   ./test_docker_network.sh status
   ```

### 详细步骤

#### 步骤1：启动InfluxDB服务
```bash
# 启动InfluxDB容器
docker-compose up -d influxdb

# 等待服务启动
docker-compose logs -f influxdb
```

#### 步骤2：测试网络连通性
```bash
# 测试DNS解析
docker-compose run --rm influxdb-test nslookup influxdb

# 测试端口连接
docker-compose run --rm influxdb-test nc -z influxdb 8086

# 测试HTTP连接
docker-compose run --rm influxdb-test curl -f http://influxdb:8086/health
```

#### 步骤3：运行InfluxDB测试
```bash
# 使用Docker网络配置运行测试
docker-compose run --rm influxdb-test ./influxdb-test config-docker-network.yml
```

## 🔍 故障排除

### 常见问题

#### 1. 连接被拒绝 (Connection Refused)
```
Error: dial tcp [::1]:8086: connect: connection refused
```

**原因**: 使用了 `localhost` 而不是 Docker 服务名

**解决方案**:
```yaml
# 修改配置文件
influxdb:
  url: "http://influxdb:8086"  # 使用服务名而不是localhost
```

#### 2. DNS 解析失败
```
Error: no such host
```

**原因**: 容器不在同一网络中或服务名错误

**解决方案**:
```bash
# 检查网络连接
docker network ls
docker network inspect influxdb_test_network

# 确保服务名正确
docker-compose ps
```

#### 3. 服务启动顺序问题
```
Error: InfluxDB service not ready
```

**解决方案**:
```yaml
# 在docker-compose.yml中添加依赖
services:
  influxdb-test:
    depends_on:
      - influxdb
    # 或使用健康检查
    depends_on:
      influxdb:
        condition: service_healthy
```

### 调试命令

#### 网络诊断
```bash
# 查看容器网络配置
docker inspect <container_id> | grep -A 20 NetworkSettings

# 查看网络详情
docker network inspect influxdb_test_network

# 进入容器调试
docker-compose exec influxdb-test sh
```

#### 连接测试
```bash
# 在容器内测试连接
docker-compose exec influxdb-test curl -v http://influxdb:8086/health

# 查看容器日志
docker-compose logs influxdb
docker-compose logs influxdb-test
```

## 📊 网络架构

### Docker Compose 网络拓扑
```
┌─────────────────────────────────────┐
│        Docker Host                  │
│                                     │
│  ┌─────────────────────────────────┐│
│  │     influxdb_test_network       ││
│  │                                 ││
│  │  ┌─────────────┐ ┌─────────────┐││
│  │  │  influxdb   │ │influxdb-test│││
│  │  │   :8086     │ │             │││
│  │  │             │ │             │││
│  │  └─────────────┘ └─────────────┘││
│  │         │              │        ││
│  │         └──────────────┘        ││
│  │       http://influxdb:8086      ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

### 通信流程
1. **DNS解析**: `influxdb` → `172.x.x.x`
2. **TCP连接**: 测试程序 → InfluxDB:8086
3. **HTTP请求**: REST API 调用
4. **数据传输**: 写入/查询数据

## 🛠️ 高级配置

### 自定义网络
```yaml
# docker-compose.yml
networks:
  influxdb_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  influxdb:
    networks:
      - influxdb_network
  
  influxdb-test:
    networks:
      - influxdb_network
```

### 环境变量配置
```bash
# 设置环境变量
export INFLUXDB_URL=http://influxdb:8086
export INFLUXDB_TOKEN=your-token
export INFLUXDB_ORG=mdc-org
export INFLUXDB_BUCKET=device-data

# 运行容器
docker-compose run --rm influxdb-test
```

### 健康检查
```yaml
# docker-compose.yml
services:
  influxdb:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 📝 最佳实践

1. **使用服务名**: 始终使用 Docker 服务名而不是 IP 地址
2. **设置超时**: 容器启动需要时间，设置合理的超时时间
3. **健康检查**: 使用健康检查确保服务就绪
4. **依赖管理**: 正确设置服务依赖关系
5. **网络隔离**: 使用自定义网络提高安全性
6. **日志监控**: 监控容器日志以快速发现问题

## 🔧 工具脚本

项目提供了 `test_docker_network.sh` 脚本来简化 Docker 网络测试：

```bash
# 查看帮助
./test_docker_network.sh help

# 设置环境
./test_docker_network.sh setup

# 运行测试
./test_docker_network.sh test

# 查看状态
./test_docker_network.sh status

# 清理环境
./test_docker_network.sh cleanup
```

这个脚本自动化了整个 Docker 网络测试流程，包括环境设置、连通性测试、服务验证等。
