# InfluxDB测试配置文件 - JSON日志格式
# 用于测试JSON格式日志输出

# 服务配置
service:
  name: "influxdb-test-json"
  debug: true
  timeout: 30s

# InfluxDB配置
influxdb:
  url: "http://localhost:8086"
  token: "1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw=="
  organization: "mdc-org"
  bucket: "device-data"
  measurement: "test_data"
  batch_size: 100
  flush_interval: 5s
  timeout: 10s
  retry_count: 3
  retry_delay: 2s

# 测试配置
test:
  write_count: 50                       # 减少数量以便快速测试
  query_limit: 20
  test_device_id: "json-test-device-001"
  test_interval: 1s

# 日志配置 - JSON格式
logging:
  enable_file: true
  log_dir: "./logs"
  log_file: "influxdb-test-json.log"
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true
  level: "info"
  format: "json"                        # JSON格式日志
