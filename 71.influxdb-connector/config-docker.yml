# InfluxDB测试配置文件 - Docker版本
# 用于Docker容器环境中的InfluxDB连接测试

# 服务配置
service:
  name: "influxdb-test-docker"
  debug: true
  timeout: 30s

# InfluxDB配置 - Docker网络内部连接
influxdb:
  url: "http://influxdb:8086"               # Docker内部服务名
  token: "1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw=="
  organization: "mdc-org"                   # 组织名称
  bucket: "device-data"                     # 存储桶名称
  measurement: "test_data"                  # 测量名称（用于测试）
  batch_size: 100                          # 批量写入大小
  flush_interval: 5s                       # 刷新间隔
  timeout: 10s                             # 操作超时时间
  retry_count: 3                           # 重试次数
  retry_delay: 2s                          # 重试延迟

# 测试配置
test:
  write_count: 100                         # 写入测试数据条数
  query_limit: 50                          # 查询结果限制
  test_device_id: "docker-test-device-001" # 测试设备ID
  test_interval: 1s                        # 测试数据生成间隔
