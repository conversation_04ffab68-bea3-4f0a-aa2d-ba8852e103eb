# InfluxDB Docker网络通信配置文件
# 用于Docker容器间通信的InfluxDB连接测试

# 服务配置
service:
  name: "influxdb-docker-network-test"
  debug: true
  timeout: 30s

# InfluxDB配置 - Docker容器间通信
influxdb:
  url: "http://influxdb:8086"               # 使用Docker服务名，而不是localhost
  token: "1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw=="
  organization: "test-auto-org"             # 新的组织名称（将被自动创建）
  bucket: "test-auto-bucket"                # 新的存储桶名称（将被自动创建）
  measurement: "docker_network_test"        # 测量名称
  batch_size: 100                          # 批量写入大小
  flush_interval: 5s                       # 刷新间隔
  timeout: 15s                             # 较长的超时时间，适应容器启动
  retry_count: 5                           # 更多重试次数
  retry_delay: 3s                          # 较长的重试延迟

  # 自动创建功能 - 启用自动创建组织和存储桶
  auto_create_org: true                    # 启用自动创建组织
  auto_create_bucket: true                 # 启用自动创建存储桶
  bucket_retention_days: 30                # 存储桶保留30天
  organization_description: "自动创建的测试组织 - Docker网络测试专用"
  bucket_description: "自动创建的测试存储桶 - Docker网络测试数据存储"

# 测试配置
test:
  write_count: 50                          # 适中的测试数据量
  query_limit: 20                          # 查询结果限制
  test_device_id: "docker-network-device"  # Docker网络测试设备ID
  test_interval: 1s                        # 测试数据生成间隔

# 日志配置
logging:
  enable_file: true
  log_dir: "./logs"
  log_file: "influxdb-docker-network-test.log"
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true
  level: "info"                            # 标准日志级别
  format: "text"                           # 文本格式便于查看
