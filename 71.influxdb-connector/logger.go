package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

/**
 * 日志管理模块
 *
 * 功能：提供文件日志输出、日志轮转和格式化功能
 *
 * 架构设计：
 * - 多输出支持：同时输出到控制台和文件
 * - 日志轮转：自动管理日志文件大小和数量
 * - 格式化输出：支持文本和JSON格式
 * - 级别控制：支持不同日志级别过滤
 * - 线程安全：支持并发日志写入
 */

// Logger 日志管理器结构体
// 封装日志输出和管理功能
type Logger struct {
	/** 标准日志记录器 */
	logger *log.Logger

	/** 日志配置 */
	config LoggingConfig

	/** 日志文件句柄 */
	logFile *os.File

	/** 当前日志文件路径 */
	currentLogPath string

	/** 日志级别映射 */
	levelMap map[string]int
}

// LogLevel 日志级别常量
const (
	LevelDebug = iota
	LevelInfo
	LevelWarn
	LevelError
)

// NewLogger 创建新的日志管理器
// 根据配置初始化日志输出和轮转策略
//
// 参数：
//   - config: 日志配置信息
//
// 返回：
//   - *Logger: 日志管理器实例
//   - error: 创建错误
//
// 使用示例：
//
//	logger, err := NewLogger(config.Logging)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	defer logger.Close()
func NewLogger(config LoggingConfig) (*Logger, error) {
	// 初始化级别映射
	levelMap := map[string]int{
		"debug": LevelDebug,
		"info":  LevelInfo,
		"warn":  LevelWarn,
		"error": LevelError,
	}

	logger := &Logger{
		config:   config,
		levelMap: levelMap,
	}

	// 设置日志输出
	if err := logger.setupOutput(); err != nil {
		return nil, fmt.Errorf("设置日志输出失败: %w", err)
	}

	return logger, nil
}

// setupOutput 设置日志输出
// 根据配置创建文件输出和多重输出
func (l *Logger) setupOutput() error {
	var writers []io.Writer

	// 始终输出到控制台
	writers = append(writers, os.Stdout)

	// 如果启用文件日志
	if l.config.EnableFile {
		if err := l.createLogFile(); err != nil {
			return fmt.Errorf("创建日志文件失败: %w", err)
		}
		writers = append(writers, l.logFile)
	}

	// 创建多重输出
	multiWriter := io.MultiWriter(writers...)

	// 设置日志格式
	var flags int
	if l.config.Format == "json" {
		flags = 0 // JSON格式不需要标准前缀
	} else {
		flags = log.LstdFlags | log.Lmicroseconds // 文本格式包含时间戳
	}

	l.logger = log.New(multiWriter, "", flags)
	return nil
}

// createLogFile 创建日志文件
// 创建日志目录和文件，设置文件权限
func (l *Logger) createLogFile() error {
	// 创建日志目录
	if err := os.MkdirAll(l.config.LogDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 构建日志文件路径
	l.currentLogPath = filepath.Join(l.config.LogDir, l.config.LogFile)

	// 打开或创建日志文件
	file, err := os.OpenFile(l.currentLogPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	l.logFile = file
	return nil
}

// shouldLog 检查是否应该记录日志
// 根据配置的日志级别过滤日志消息
//
// 参数：
//   - level: 日志级别字符串
//
// 返回：
//   - bool: 是否应该记录
func (l *Logger) shouldLog(level string) bool {
	configLevel, exists := l.levelMap[strings.ToLower(l.config.Level)]
	if !exists {
		configLevel = LevelInfo // 默认级别
	}

	msgLevel, exists := l.levelMap[strings.ToLower(level)]
	if !exists {
		msgLevel = LevelInfo // 默认级别
	}

	return msgLevel >= configLevel
}

// formatMessage 格式化日志消息
// 根据配置的格式输出文本或JSON格式的日志
//
// 参数：
//   - level: 日志级别
//   - message: 日志消息
//
// 返回：
//   - string: 格式化后的消息
func (l *Logger) formatMessage(level, message string) string {
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	if l.config.Format == "json" {
		return fmt.Sprintf(`{"timestamp":"%s","level":"%s","message":"%s"}`,
			timestamp, strings.ToUpper(level), message)
	}

	// 文本格式
	return fmt.Sprintf("[%s] %s", strings.ToUpper(level), message)
}

// Debug 记录调试级别日志
func (l *Logger) Debug(format string, args ...interface{}) {
	if l.shouldLog("debug") {
		message := fmt.Sprintf(format, args...)
		formatted := l.formatMessage("debug", message)
		l.logger.Println(formatted)
	}
}

// Info 记录信息级别日志
func (l *Logger) Info(format string, args ...interface{}) {
	if l.shouldLog("info") {
		message := fmt.Sprintf(format, args...)
		formatted := l.formatMessage("info", message)
		l.logger.Println(formatted)
	}
}

// Warn 记录警告级别日志
func (l *Logger) Warn(format string, args ...interface{}) {
	if l.shouldLog("warn") {
		message := fmt.Sprintf(format, args...)
		formatted := l.formatMessage("warn", message)
		l.logger.Println(formatted)
	}
}

// Error 记录错误级别日志
func (l *Logger) Error(format string, args ...interface{}) {
	if l.shouldLog("error") {
		message := fmt.Sprintf(format, args...)
		formatted := l.formatMessage("error", message)
		l.logger.Println(formatted)
	}
}

// LogTestResult 记录测试结果
// 专门用于记录测试结果的结构化日志
//
// 参数：
//   - testName: 测试名称
//   - success: 是否成功
//   - duration: 执行时间
//   - details: 详细信息
func (l *Logger) LogTestResult(testName string, success bool, duration time.Duration, details string) {
	status := "SUCCESS"
	if !success {
		status = "FAILED"
	}

	if l.config.Format == "json" {
		message := fmt.Sprintf(`{"test_name":"%s","status":"%s","duration_ms":%d,"details":"%s"}`,
			testName, status, duration.Milliseconds(), details)
		l.Info("TEST_RESULT: %s", message)
	} else {
		l.Info("🧪 测试结果 - %s: %s (耗时: %v) - %s", testName, status, duration, details)
	}
}

// LogPerformanceStats 记录性能统计
// 专门用于记录性能统计信息的结构化日志
//
// 参数：
//   - stats: InfluxDB统计信息
func (l *Logger) LogPerformanceStats(stats InfluxDBStats) {
	if l.config.Format == "json" {
		message := fmt.Sprintf(`{"connection_status":"%s","total_writes":%d,"success_writes":%d,"failed_writes":%d,"total_queries":%d,"success_queries":%d,"failed_queries":%d,"avg_response_time_ms":%.2f}`,
			stats.ConnectionStatus, stats.TotalWrites, stats.SuccessWrites, stats.FailedWrites,
			stats.TotalQueries, stats.SuccessQueries, stats.FailedQueries, stats.AvgResponseTime)
		l.Info("PERFORMANCE_STATS: %s", message)
	} else {
		l.Info("📊 性能统计 - 连接: %s, 写入: %d/%d, 查询: %d/%d, 响应时间: %.2fms",
			stats.ConnectionStatus, stats.SuccessWrites, stats.TotalWrites,
			stats.SuccessQueries, stats.TotalQueries, stats.AvgResponseTime)
	}
}

// LogServerTestResult 记录服务器测试结果
// 专门用于记录服务器连接测试的详细结果
//
// 参数：
//   - result: 服务器测试结果
func (l *Logger) LogServerTestResult(result *ServerTestResult) {
	if l.config.Format == "json" {
		// JSON格式记录
		message := fmt.Sprintf(`{"network_connectivity":%t,"service_availability":%t,"authentication_test":%t,"organization_test":%t,"bucket_test":%t,"read_permission_test":%t,"write_permission_test":%t,"server_version":"%s","total_duration_ms":%d,"error_count":%d,"warning_count":%d}`,
			result.NetworkConnectivity, result.ServiceAvailability, result.AuthenticationTest,
			result.OrganizationTest, result.BucketTest, result.ReadPermissionTest, result.WritePermissionTest,
			result.ServerVersion, result.TotalDuration.Milliseconds(), len(result.Errors), len(result.Warnings))
		l.Info("SERVER_TEST_RESULT: %s", message)

		// 记录错误详情
		if len(result.Errors) > 0 {
			for i, err := range result.Errors {
				errorMsg := fmt.Sprintf(`{"error_index":%d,"error_message":"%s"}`, i+1, err)
				l.Error("SERVER_TEST_ERROR: %s", errorMsg)
			}
		}

		// 记录警告详情
		if len(result.Warnings) > 0 {
			for i, warning := range result.Warnings {
				warningMsg := fmt.Sprintf(`{"warning_index":%d,"warning_message":"%s"}`, i+1, warning)
				l.Warn("SERVER_TEST_WARNING: %s", warningMsg)
			}
		}
	} else {
		// 文本格式记录
		l.Info("🔍 服务器测试结果摘要:")
		l.Info("   网络连通性: %s", formatTestResult(result.NetworkConnectivity))
		l.Info("   服务可用性: %s", formatTestResult(result.ServiceAvailability))
		l.Info("   授权验证: %s", formatTestResult(result.AuthenticationTest))
		l.Info("   组织验证: %s", formatTestResult(result.OrganizationTest))
		l.Info("   存储桶验证: %s", formatTestResult(result.BucketTest))
		l.Info("   读权限: %s", formatTestResult(result.ReadPermissionTest))
		l.Info("   写权限: %s", formatTestResult(result.WritePermissionTest))

		if result.ServerVersion != "" {
			l.Info("   服务器版本: %s", result.ServerVersion)
		}
		l.Info("   测试耗时: %v", result.TotalDuration)

		// 记录错误
		if len(result.Errors) > 0 {
			l.Error("服务器测试错误 (%d个):", len(result.Errors))
			for i, err := range result.Errors {
				l.Error("   %d. %s", i+1, err)
			}
		}

		// 记录警告
		if len(result.Warnings) > 0 {
			l.Warn("服务器测试警告 (%d个):", len(result.Warnings))
			for i, warning := range result.Warnings {
				l.Warn("   %d. %s", i+1, warning)
			}
		}
	}
}

// formatTestResult 格式化测试结果
// 将布尔值转换为可读的测试结果字符串
func formatTestResult(passed bool) string {
	if passed {
		return "✅ 通过"
	}
	return "❌ 失败"
}

// checkRotation 检查日志轮转
// 根据文件大小和配置决定是否需要轮转日志文件
func (l *Logger) checkRotation() error {
	if !l.config.EnableFile || l.logFile == nil {
		return nil
	}

	// 获取文件信息
	fileInfo, err := l.logFile.Stat()
	if err != nil {
		return err
	}

	// 检查文件大小（转换为MB）
	fileSizeMB := fileInfo.Size() / (1024 * 1024)
	if fileSizeMB >= int64(l.config.MaxSize) {
		return l.rotateLog()
	}

	return nil
}

// rotateLog 执行日志轮转
// 关闭当前文件，重命名并创建新文件
func (l *Logger) rotateLog() error {
	// 关闭当前文件
	if l.logFile != nil {
		l.logFile.Close()
	}

	// 生成轮转文件名
	timestamp := time.Now().Format("20060102-150405")
	rotatedPath := fmt.Sprintf("%s.%s", l.currentLogPath, timestamp)

	// 重命名当前文件
	if err := os.Rename(l.currentLogPath, rotatedPath); err != nil {
		return fmt.Errorf("重命名日志文件失败: %w", err)
	}

	// 创建新的日志文件
	if err := l.createLogFile(); err != nil {
		return fmt.Errorf("创建新日志文件失败: %w", err)
	}

	// 重新设置输出
	return l.setupOutput()
}

// Close 关闭日志管理器
// 清理资源，关闭文件句柄
func (l *Logger) Close() error {
	if l.logFile != nil {
		return l.logFile.Close()
	}
	return nil
}
