# InfluxDB 自动创建功能指南

## 🎯 功能概述

InfluxDB Connector 支持自动创建组织和存储桶功能，当指定的组织或存储桶不存在时，程序会自动创建它们。

## ✅ 功能特性

### 🏢 自动创建组织
- 检测组织是否存在
- 如果不存在且启用自动创建，则创建新组织
- 支持自定义组织描述信息
- 记录创建过程到日志

### 🪣 自动创建存储桶
- 检测存储桶是否存在
- 如果不存在且启用自动创建，则创建新存储桶
- 支持设置数据保留策略
- 支持自定义存储桶描述信息
- 记录创建过程到日志

## 🔧 配置说明

### 配置参数

```yaml
influxdb:
  # 基本连接配置
  url: "http://localhost:8086"
  token: "your-admin-token"              # 需要管理员权限的Token
  organization: "your-org"
  bucket: "your-bucket"
  
  # 自动创建功能配置
  auto_create_org: true                  # 启用自动创建组织
  auto_create_bucket: true               # 启用自动创建存储桶
  bucket_retention_days: 30              # 存储桶保留天数（0=永久保留）
  organization_description: "组织描述"    # 组织描述信息
  bucket_description: "存储桶描述"        # 存储桶描述信息
```

### 配置选项详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `auto_create_org` | bool | false | 是否自动创建组织 |
| `auto_create_bucket` | bool | false | 是否自动创建存储桶 |
| `bucket_retention_days` | int | 0 | 存储桶数据保留天数，0表示永久保留 |
| `organization_description` | string | 自动生成 | 组织描述信息 |
| `bucket_description` | string | 自动生成 | 存储桶描述信息 |

## 🔑 权限要求

### Token 权限

要使用自动创建功能，Token 必须具有以下权限：

#### 创建组织权限
- `write:orgs` - 创建和修改组织的权限

#### 创建存储桶权限
- `write:buckets` - 创建和修改存储桶的权限
- `read:orgs` - 读取组织信息的权限

### 获取管理员 Token

1. **通过 InfluxDB UI 获取**
   ```
   1. 登录 InfluxDB Web UI (http://localhost:8086)
   2. 进入 Data > API Tokens
   3. 点击 "Generate API Token"
   4. 选择 "All Access API Token" 或自定义权限
   5. 复制生成的 Token
   ```

2. **通过 CLI 获取**
   ```bash
   # 创建具有所有权限的 Token
   influx auth create \
     --org your-org \
     --all-access \
     --description "Auto-create token"
   ```

## 🚀 使用示例

### 示例 1：完全自动创建

```yaml
# config-auto-create-full.yml
influxdb:
  url: "http://localhost:8086"
  token: "your-admin-token"
  organization: "new-test-org"           # 将被自动创建
  bucket: "new-test-bucket"              # 将被自动创建
  auto_create_org: true
  auto_create_bucket: true
  bucket_retention_days: 7               # 保留7天
  organization_description: "测试环境组织"
  bucket_description: "测试数据存储桶"
```

### 示例 2：仅自动创建存储桶

```yaml
# config-auto-create-bucket.yml
influxdb:
  url: "http://localhost:8086"
  token: "your-token"
  organization: "existing-org"           # 现有组织
  bucket: "new-bucket"                   # 将被自动创建
  auto_create_org: false
  auto_create_bucket: true
  bucket_retention_days: 30
  bucket_description: "新的数据存储桶"
```

## 📊 测试结果

### 成功创建的日志示例

```
🔧 组织 'test-new-org' 不存在，尝试自动创建...
🔧 开始创建组织: test-new-org
✅ 组织创建成功: test-new-org (ID: 0123456789abcdef)

🔧 存储桶 'test-new-bucket' 不存在，尝试自动创建...
🔧 开始创建存储桶: test-new-bucket
✅ 存储桶创建成功: test-new-bucket (ID: fedcba9876543210)
📅 存储桶保留策略: 7 天
```

### 权限不足的错误示例

```
❌ 组织验证测试失败: 创建组织失败: unauthorized: write:orgs is unauthorized
```

## 🛠️ 故障排除

### 常见问题

#### 1. 权限不足错误
**错误**: `unauthorized: write:orgs is unauthorized`

**解决方案**:
- 确保使用具有管理员权限的 Token
- 检查 Token 是否包含 `write:orgs` 和 `write:buckets` 权限
- 重新生成具有所有权限的 Token

#### 2. 组织已存在但无法访问
**错误**: `organization name "xxx" not found`

**解决方案**:
- 检查 Token 是否有访问该组织的权限
- 确认组织名称拼写正确
- 使用具有该组织访问权限的 Token

#### 3. 存储桶创建失败
**错误**: `创建存储桶时无法找到组织`

**解决方案**:
- 确保组织已存在或启用自动创建组织
- 检查组织名称配置是否正确
- 确认 Token 有访问该组织的权限

### 调试步骤

1. **检查 Token 权限**
   ```bash
   # 使用 InfluxDB CLI 验证 Token
   influx auth list --token your-token
   ```

2. **验证连接**
   ```bash
   # 测试基本连接
   curl -H "Authorization: Token your-token" \
        http://localhost:8086/api/v2/me
   ```

3. **查看详细日志**
   ```bash
   # 启用调试模式
   go run . config-auto-create.yml --debug
   ```

## 📝 最佳实践

### 1. 权限管理
- 使用专门的管理员 Token 进行自动创建
- 生产环境中谨慎使用自动创建功能
- 定期轮换 API Token

### 2. 配置管理
- 为不同环境使用不同的配置文件
- 在配置文件中明确标注自动创建选项
- 设置合理的数据保留策略

### 3. 监控和日志
- 监控自动创建的警告日志
- 定期检查创建的组织和存储桶
- 记录自动创建的操作以便审计

## 🔄 测试脚本

项目提供了专门的测试脚本：

```bash
# 显示配置说明
./test_auto_create.sh --show-config

# 测试自动创建功能
./test_auto_create.sh --test-create

# 清理测试数据
./test_auto_create.sh --cleanup
```

## ⚠️ 注意事项

1. **权限要求**: 自动创建功能需要管理员级别的 Token
2. **生产环境**: 在生产环境中谨慎使用，建议手动创建组织和存储桶
3. **数据保留**: 设置合理的保留策略，避免数据意外丢失
4. **审计日志**: 自动创建操作会记录到警告日志中
5. **错误处理**: 如果自动创建失败，程序会继续执行其他测试

这个功能特别适用于：
- 开发和测试环境的快速搭建
- 自动化部署脚本
- CI/CD 流水线中的环境初始化
