version: '3.8'

# InfluxDB测试程序 Docker Compose配置
# 包含测试程序和依赖的InfluxDB服务

name: influxdb_test

services:
  # InfluxDB测试程序
  influxdb-test:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_TIME: ${BUILD_TIME:-$(date -u +%Y-%m-%dT%H:%M:%SZ)}
        GIT_COMMIT: ${GIT_COMMIT:-unknown}
        VERSION: ${VERSION:-1.0.0}
    container_name: influxdb_test_app
    restart: unless-stopped
    environment:
      # 应用配置
      - APP_ENV=docker
      - APP_DEBUG=true
      - APP_LOG_LEVEL=info
      
      # InfluxDB连接配置 - 使用服务名进行容器间通信
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw==
      - INFLUXDB_ORG=mdc-org
      - INFLUXDB_BUCKET=device-data
      
      # 测试配置
      - TEST_WRITE_COUNT=1000
      - TEST_QUERY_LIMIT=100
      - TEST_DEVICE_ID=docker-test-device
    volumes:
      # 挂载配置文件（可选，用于自定义配置）
      - ./config.yml:/app/config.yml:ro
      # 挂载数据目录（用于临时文件）
      - ./data:/app/data
      # 挂载日志目录（用于日志文件持久化）
      - ./logs:/app/logs
    depends_on:
      influxdb:
        condition: service_healthy
    networks:
      - influxdb_test_network
    # 如果需要定时运行测试，可以使用以下配置
    # command: ["sh", "-c", "while true; do ./influxdb-test config.yml; sleep 300; done"]
    
    # 健康检查
    healthcheck:
      test: ["CMD", "pgrep", "-f", "influxdb-test"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # InfluxDB时序数据库
  influxdb:
    image: influxdb:2.7-alpine
    container_name: influxdb_test_db
    restart: unless-stopped
    ports:
      - "8087:8086"
    environment:
      # InfluxDB初始化配置
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=password123
      - DOCKER_INFLUXDB_INIT_ORG=mdc-org
      - DOCKER_INFLUXDB_INIT_BUCKET=device-data
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw==
      
      # 性能优化配置
      - INFLUXD_REPORTING_DISABLED=true
      - INFLUXD_HTTP_BIND_ADDRESS=:8086
    volumes:
      # 持久化数据存储
      - influxdb_data:/var/lib/influxdb2
    networks:
      - influxdb_test_network
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Grafana可视化（可选）
  grafana:
    image: grafana/grafana:10.2.0
    container_name: influxdb_test_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # Grafana配置
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_INSTALL_PLUGINS=grafana-influxdb-datasource
      - GF_RENDERING_SERVER_URL=http://renderer:8081/render
      - GF_RENDERING_CALLBACK_URL=http://grafana:3000/
    volumes:
      # Grafana数据持久化
      - grafana_data:/var/lib/grafana
      # 预配置数据源和仪表板
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      influxdb:
        condition: service_healthy
    networks:
      - influxdb_test_network
    profiles:
      - monitoring

# 网络配置
networks:
  influxdb_test_network:
    driver: bridge
    name: influxdb_test_network
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  # InfluxDB数据持久化
  influxdb_data:
    driver: local
    name: influxdb_test_data
  
  # Grafana数据持久化
  grafana_data:
    driver: local
    name: grafana_test_data

# 扩展配置
x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

# 应用默认日志配置到所有服务
x-common-variables: &common-variables
  logging: *default-logging
