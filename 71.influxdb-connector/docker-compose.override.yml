# Docker Compose 开发环境覆盖配置
# 用于本地开发和测试的配置覆盖

version: '3.8'

services:
  # InfluxDB测试程序 - 开发配置
  influxdb-test:
    # 开发模式下直接挂载源码，支持热重载
    volumes:
      - .:/app/src:ro
      - ./data:/app/data
      - ./logs:/app/logs
    
    # 开发环境变量
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - APP_LOG_LEVEL=debug
      - GO_ENV=development
    
    # 开发模式命令 - 使用go run实现热重载
    command: ["sh", "-c", "cd /app/src && go run . config.yml"]
    
    # 开发端口映射
    ports:
      - "8080:8080"
      - "6060:6060"  # pprof调试端口
    
    # 开发模式不重启
    restart: "no"
    
    # 开发模式标签
    labels:
      - "dev.mode=true"
      - "dev.hot-reload=enabled"

  # InfluxDB - 开发配置
  influxdb:
    # 开发模式端口映射
    ports:
      - "8086:8086"
      - "8088:8088"  # RPC端口
    
    # 开发环境变量
    environment:
      - INFLUXD_LOG_LEVEL=debug
      - INFLUXD_REPORTING_DISABLED=true
    
    # 开发模式挂载配置
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - ./influxdb-dev.conf:/etc/influxdb2/influxdb.conf:ro
      - ./logs/influxdb:/var/log/influxdb2
    
    # 开发模式标签
    labels:
      - "dev.mode=true"
      - "dev.database=influxdb"

  # Grafana - 开发配置
  grafana:
    # 开发模式端口
    ports:
      - "3000:3000"
    
    # 开发环境变量
    environment:
      - GF_LOG_LEVEL=debug
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=true
      - GF_INSTALL_PLUGINS=grafana-influxdb-datasource,grafana-piechart-panel
    
    # 开发模式挂载
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dev-provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dev-dashboards:/var/lib/grafana/dashboards:ro
      - ./logs/grafana:/var/log/grafana
    
    # 开发模式标签
    labels:
      - "dev.mode=true"
      - "dev.monitoring=grafana"

  # Redis - 开发缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: influxdb_test_redis_dev
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass dev123
    volumes:
      - redis_dev_data:/data
      - ./logs/redis:/var/log/redis
    networks:
      - influxdb_test_network
    profiles:
      - cache
    labels:
      - "dev.mode=true"
      - "dev.cache=redis"

  # Prometheus - 监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: influxdb_test_prometheus_dev
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/dev-config.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_dev_data:/prometheus
      - ./logs/prometheus:/var/log/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - influxdb_test_network
    profiles:
      - monitoring
    labels:
      - "dev.mode=true"
      - "dev.monitoring=prometheus"

# 开发模式数据卷
volumes:
  redis_dev_data:
    driver: local
    name: influxdb_test_redis_dev_data
  
  prometheus_dev_data:
    driver: local
    name: influxdb_test_prometheus_dev_data
