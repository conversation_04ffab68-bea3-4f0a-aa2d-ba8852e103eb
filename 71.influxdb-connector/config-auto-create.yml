# InfluxDB自动创建配置文件
# 功能：自动创建组织和存储桶，适用于初始化环境或测试

# 服务配置
service:
  name: "influxdb-auto-create-test"
  debug: true
  timeout: 30s

# InfluxDB配置 - 启用自动创建功能
influxdb:
  url: "http://localhost:8086"
  token: "1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw=="
  organization: "mdc-org"                   # 如果不存在将自动创建
  bucket: "device-data"                     # 如果不存在将自动创建
  measurement: "auto_create_test"
  batch_size: 100
  flush_interval: 5s
  timeout: 15s
  retry_count: 3
  retry_delay: 2s
  
  # 自动创建功能配置
  auto_create_org: true                     # 启用自动创建组织
  auto_create_bucket: true                  # 启用自动创建存储桶
  bucket_retention_days: 30                 # 存储桶保留30天（0表示永久保留）
  organization_description: "MDC系统自动创建的组织，用于设备数据管理"
  bucket_description: "MDC系统自动创建的存储桶，用于存储设备监控数据"

# 测试配置
test:
  write_count: 50                           # 适中的测试数据量
  query_limit: 20                           # 查询结果限制
  test_device_id: "auto-create-device"      # 自动创建测试设备ID
  test_interval: 1s                         # 测试数据生成间隔

# 日志配置
logging:
  enable_file: true
  log_dir: "./logs"
  log_file: "influxdb-auto-create-test.log"
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true
  level: "info"                             # 标准日志级别
  format: "text"                            # 文本格式便于查看
