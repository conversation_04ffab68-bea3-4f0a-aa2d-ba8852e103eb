package main

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

/**
 * 配置结构体定义
 *
 * 功能：定义InfluxDB测试程序的所有配置参数
 *
 * 架构设计：
 * - 分层配置：服务配置、InfluxDB配置、测试配置分离
 * - YAML支持：使用YAML格式配置文件，便于维护
 * - 类型安全：使用强类型定义，避免配置错误
 * - 默认值：提供合理的默认配置值
 */

// Config 主配置结构体
// 包含服务、InfluxDB、测试和日志相关的所有配置参数
type Config struct {
	/** 服务基础配置：服务名称、调试模式、超时设置 */
	Service ServiceConfig `yaml:"service"`

	/** InfluxDB连接配置：URL、认证、批量处理等参数 */
	InfluxDB InfluxDBConfig `yaml:"influxdb"`

	/** 测试配置：测试数据量、查询限制、设备ID等 */
	Test TestConfig `yaml:"test"`

	/** 日志配置：文件输出、轮转策略、格式设置 */
	Logging LoggingConfig `yaml:"logging"`
}

// ServiceConfig 服务配置结构体
// 定义服务的基本运行参数
type ServiceConfig struct {
	/** 服务名称，用于日志标识和监控 */
	Name string `yaml:"name"`

	/** 调试模式开关，控制详细日志输出 */
	Debug bool `yaml:"debug"`

	/** 服务超时时间，控制整体操作超时 */
	Timeout time.Duration `yaml:"timeout"`
}

// InfluxDBConfig InfluxDB配置结构体
// 定义InfluxDB连接和操作的所有参数
type InfluxDBConfig struct {
	/** InfluxDB服务器URL地址 */
	URL string `yaml:"url"`

	/** API访问令牌，用于身份验证 */
	Token string `yaml:"token"`

	/** 组织名称，InfluxDB的命名空间 */
	Organization string `yaml:"organization"`

	/** 存储桶名称，数据存储的容器 */
	Bucket string `yaml:"bucket"`

	/** 测量名称，类似表名的概念 */
	Measurement string `yaml:"measurement"`

	/** 批量写入大小，优化写入性能 */
	BatchSize int `yaml:"batch_size"`

	/** 刷新间隔，控制数据刷新频率 */
	FlushInterval time.Duration `yaml:"flush_interval"`

	/** 操作超时时间，防止操作阻塞 */
	Timeout time.Duration `yaml:"timeout"`

	/** 重试次数，提高操作可靠性 */
	RetryCount int `yaml:"retry_count"`

	/** 重试延迟，控制重试间隔 */
	RetryDelay time.Duration `yaml:"retry_delay"`

	/** 自动创建组织，如果组织不存在则自动创建 */
	AutoCreateOrg bool `yaml:"auto_create_org"`

	/** 自动创建存储桶，如果存储桶不存在则自动创建 */
	AutoCreateBucket bool `yaml:"auto_create_bucket"`

	/** 存储桶保留策略（天数），0表示永久保留 */
	BucketRetentionDays int `yaml:"bucket_retention_days"`

	/** 组织描述，用于自动创建组织时的描述信息 */
	OrganizationDescription string `yaml:"organization_description"`

	/** 存储桶描述，用于自动创建存储桶时的描述信息 */
	BucketDescription string `yaml:"bucket_description"`
}

// TestConfig 测试配置结构体
// 定义测试相关的参数和行为
type TestConfig struct {
	/** 写入测试的数据条数 */
	WriteCount int `yaml:"write_count"`

	/** 查询结果的最大条数限制 */
	QueryLimit int `yaml:"query_limit"`

	/** 测试使用的设备ID */
	TestDeviceID string `yaml:"test_device_id"`

	/** 测试数据生成的时间间隔 */
	TestInterval time.Duration `yaml:"test_interval"`
}

// LoggingConfig 日志配置结构体
// 定义日志输出、轮转和格式相关的参数
type LoggingConfig struct {
	/** 是否启用文件日志输出 */
	EnableFile bool `yaml:"enable_file"`

	/** 日志文件存储目录 */
	LogDir string `yaml:"log_dir"`

	/** 日志文件名称 */
	LogFile string `yaml:"log_file"`

	/** 单个日志文件最大大小（MB） */
	MaxSize int `yaml:"max_size"`

	/** 保留的日志文件数量 */
	MaxBackups int `yaml:"max_backups"`

	/** 日志文件保留天数 */
	MaxAge int `yaml:"max_age"`

	/** 是否压缩旧日志文件 */
	Compress bool `yaml:"compress"`

	/** 日志级别：debug, info, warn, error */
	Level string `yaml:"level"`

	/** 日志格式：text, json */
	Format string `yaml:"format"`
}

// LoadConfig 加载配置文件
// 从指定路径读取YAML配置文件并解析为Config结构体
//
// 参数：
//   - configPath: 配置文件路径
//
// 返回：
//   - *Config: 解析后的配置对象
//   - error: 加载或解析错误
//
// 使用示例：
//
//	config, err := LoadConfig("config.yml")
//	if err != nil {
//	    log.Fatal(err)
//	}
func LoadConfig(configPath string) (*Config, error) {
	// 读取配置文件内容
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 创建配置对象并设置默认值
	config := &Config{}

	// 解析YAML配置
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证必要的配置项
	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

// validateConfig 验证配置的有效性
// 检查必要的配置项是否已设置，确保程序能正常运行
//
// 参数：
//   - config: 待验证的配置对象
//
// 返回：
//   - error: 验证错误，nil表示验证通过
func validateConfig(config *Config) error {
	// 验证InfluxDB配置
	if config.InfluxDB.URL == "" {
		return fmt.Errorf("InfluxDB URL不能为空")
	}
	if config.InfluxDB.Token == "" {
		return fmt.Errorf("InfluxDB Token不能为空")
	}
	if config.InfluxDB.Organization == "" {
		return fmt.Errorf("InfluxDB Organization不能为空")
	}
	if config.InfluxDB.Bucket == "" {
		return fmt.Errorf("InfluxDB Bucket不能为空")
	}

	// 设置自动创建的默认值
	if config.InfluxDB.BucketRetentionDays <= 0 {
		config.InfluxDB.BucketRetentionDays = 0 // 默认永久保留
	}
	if config.InfluxDB.OrganizationDescription == "" {
		config.InfluxDB.OrganizationDescription = fmt.Sprintf("自动创建的组织: %s", config.InfluxDB.Organization)
	}
	if config.InfluxDB.BucketDescription == "" {
		config.InfluxDB.BucketDescription = fmt.Sprintf("自动创建的存储桶: %s", config.InfluxDB.Bucket)
	}

	// 验证测试配置
	if config.Test.WriteCount <= 0 {
		config.Test.WriteCount = 100 // 设置默认值
	}
	if config.Test.QueryLimit <= 0 {
		config.Test.QueryLimit = 50 // 设置默认值
	}
	if config.Test.TestDeviceID == "" {
		config.Test.TestDeviceID = "test-device-001" // 设置默认值
	}

	// 验证日志配置
	if config.Logging.LogDir == "" {
		config.Logging.LogDir = "./logs" // 设置默认值
	}
	if config.Logging.LogFile == "" {
		config.Logging.LogFile = "influxdb-test.log" // 设置默认值
	}
	if config.Logging.MaxSize <= 0 {
		config.Logging.MaxSize = 100 // 设置默认值（MB）
	}
	if config.Logging.MaxBackups <= 0 {
		config.Logging.MaxBackups = 5 // 设置默认值
	}
	if config.Logging.MaxAge <= 0 {
		config.Logging.MaxAge = 30 // 设置默认值（天）
	}
	if config.Logging.Level == "" {
		config.Logging.Level = "info" // 设置默认值
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "text" // 设置默认值
	}

	return nil
}
