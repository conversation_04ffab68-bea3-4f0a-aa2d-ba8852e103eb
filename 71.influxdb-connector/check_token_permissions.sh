#!/bin/bash

# InfluxDB Token权限检查脚本
# 功能：检查Token权限并提供获取管理员Token的指导

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFLUXDB_URL="http://localhost:8086"
CONFIG_FILE="config-docker-network.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 从配置文件提取Token
extract_token_from_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    local token=$(grep "token:" "$CONFIG_FILE" | sed 's/.*token: *"\([^"]*\)".*/\1/' | head -1)
    if [ -z "$token" ]; then
        log_error "无法从配置文件中提取Token"
        return 1
    fi
    
    echo "$token"
}

# 检查Token基本有效性
check_token_basic() {
    local token="$1"
    
    log_step "检查Token基本有效性..."
    
    local response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Token $token" \
        "$INFLUXDB_URL/api/v2/me" \
        -o /tmp/token_check.json)
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        local user_name=$(cat /tmp/token_check.json | grep -o '"name":"[^"]*"' | sed 's/"name":"\([^"]*\)"/\1/')
        log_success "Token有效，用户: $user_name"
        rm -f /tmp/token_check.json
        return 0
    else
        log_error "Token无效或已过期 (HTTP: $http_code)"
        rm -f /tmp/token_check.json
        return 1
    fi
}

# 检查Token权限
check_token_permissions() {
    local token="$1"
    
    log_step "检查Token权限..."
    
    # 检查组织权限
    log_info "检查组织权限..."
    local org_response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Token $token" \
        "$INFLUXDB_URL/api/v2/orgs" \
        -o /tmp/orgs_check.json)
    
    local org_http_code="${org_response: -3}"
    
    if [ "$org_http_code" = "200" ]; then
        log_success "✅ 有读取组织权限"
        
        # 显示现有组织
        log_info "现有组织列表:"
        if command -v jq &> /dev/null; then
            cat /tmp/orgs_check.json | jq -r '.orgs[]? | "  - \(.name) (ID: \(.id))"' 2>/dev/null || echo "  无法解析组织列表"
        else
            grep -o '"name":"[^"]*"' /tmp/orgs_check.json | sed 's/"name":"\([^"]*\)"/  - \1/' || echo "  无法解析组织列表"
        fi
    else
        log_error "❌ 无读取组织权限 (HTTP: $org_http_code)"
    fi
    
    # 检查存储桶权限
    log_info "检查存储桶权限..."
    local bucket_response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Token $token" \
        "$INFLUXDB_URL/api/v2/buckets" \
        -o /tmp/buckets_check.json)
    
    local bucket_http_code="${bucket_response: -3}"
    
    if [ "$bucket_http_code" = "200" ]; then
        log_success "✅ 有读取存储桶权限"
        
        # 显示现有存储桶
        log_info "现有存储桶列表:"
        if command -v jq &> /dev/null; then
            cat /tmp/buckets_check.json | jq -r '.buckets[]? | "  - \(.name) (组织: \(.orgID))"' 2>/dev/null || echo "  无法解析存储桶列表"
        else
            grep -o '"name":"[^"]*"' /tmp/buckets_check.json | sed 's/"name":"\([^"]*\)"/  - \1/' || echo "  无法解析存储桶列表"
        fi
    else
        log_error "❌ 无读取存储桶权限 (HTTP: $bucket_http_code)"
    fi
    
    # 清理临时文件
    rm -f /tmp/orgs_check.json /tmp/buckets_check.json
}

# 测试创建组织权限
test_create_org_permission() {
    local token="$1"
    
    log_step "测试创建组织权限..."
    
    # 尝试创建一个测试组织
    local test_org_name="permission-test-org-$(date +%s)"
    local create_response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Authorization: Token $token" \
        -H "Content-Type: application/json" \
        -d "{\"name\":\"$test_org_name\",\"description\":\"权限测试组织\"}" \
        "$INFLUXDB_URL/api/v2/orgs" \
        -o /tmp/create_org_test.json)
    
    local create_http_code="${create_response: -3}"
    
    if [ "$create_http_code" = "201" ]; then
        log_success "✅ 有创建组织权限"
        
        # 获取创建的组织ID
        local org_id=""
        if command -v jq &> /dev/null; then
            org_id=$(cat /tmp/create_org_test.json | jq -r '.id' 2>/dev/null)
        fi
        
        # 删除测试组织
        if [ -n "$org_id" ]; then
            curl -s -X DELETE \
                -H "Authorization: Token $token" \
                "$INFLUXDB_URL/api/v2/orgs/$org_id" > /dev/null
            log_info "已删除测试组织"
        fi
    else
        log_error "❌ 无创建组织权限 (HTTP: $create_http_code)"
        if [ -f /tmp/create_org_test.json ]; then
            local error_msg=$(cat /tmp/create_org_test.json)
            log_error "错误详情: $error_msg"
        fi
    fi
    
    rm -f /tmp/create_org_test.json
}

# 显示获取管理员Token的指导
show_admin_token_guide() {
    log_step "获取管理员Token指导..."
    
    echo ""
    log_info "要使用自动创建功能，需要具有以下权限的Token："
    echo "  - write:orgs (创建组织权限)"
    echo "  - write:buckets (创建存储桶权限)"
    echo "  - read:orgs (读取组织权限)"
    echo "  - read:buckets (读取存储桶权限)"
    
    echo ""
    log_info "获取管理员Token的方法："
    echo ""
    echo "方法1: 通过InfluxDB Web UI"
    echo "  1. 打开浏览器访问: $INFLUXDB_URL"
    echo "  2. 登录InfluxDB"
    echo "  3. 进入 Data > API Tokens"
    echo "  4. 点击 'Generate API Token'"
    echo "  5. 选择 'All Access API Token'"
    echo "  6. 输入描述信息，如 'Auto-create token'"
    echo "  7. 点击 'Save' 并复制生成的Token"
    
    echo ""
    echo "方法2: 通过InfluxDB CLI (如果已安装)"
    echo "  influx auth create \\"
    echo "    --org your-org \\"
    echo "    --all-access \\"
    echo "    --description 'Auto-create token'"
    
    echo ""
    echo "方法3: 使用初始管理员Token"
    echo "  如果这是新安装的InfluxDB，可以使用初始设置时创建的管理员Token"
    
    echo ""
    log_warn "注意事项："
    echo "  - 管理员Token具有完全访问权限，请妥善保管"
    echo "  - 建议为自动创建功能创建专门的Token"
    echo "  - 生产环境中谨慎使用自动创建功能"
}

# 显示配置更新指导
show_config_update_guide() {
    local token="$1"
    
    log_step "配置更新指导..."
    
    echo ""
    log_info "更新配置文件以使用新Token："
    echo ""
    echo "1. 编辑配置文件: $CONFIG_FILE"
    echo "2. 更新token字段:"
    echo "   token: \"your-new-admin-token\""
    echo ""
    echo "3. 确保启用自动创建功能:"
    echo "   auto_create_org: true"
    echo "   auto_create_bucket: true"
    echo ""
    echo "4. 重新运行测试:"
    echo "   go run . $CONFIG_FILE"
}

# 主函数
main() {
    echo "🔑 InfluxDB Token权限检查工具"
    echo "=================================="
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 提取Token
    local token=$(extract_token_from_config)
    if [ $? -ne 0 ]; then
        exit 1
    fi
    
    log_info "从配置文件提取Token: ${token:0:20}..."
    
    # 检查Token基本有效性
    if ! check_token_basic "$token"; then
        show_admin_token_guide
        exit 1
    fi
    
    # 检查Token权限
    check_token_permissions "$token"
    
    # 测试创建组织权限
    test_create_org_permission "$token"
    
    echo ""
    echo "=================================="
    
    # 显示指导信息
    show_admin_token_guide
    show_config_update_guide "$token"
    
    echo ""
    log_info "检查完成！"
}

# 执行主函数
main "$@"
