#!/bin/bash

# InfluxDB测试程序运行脚本
# 功能：启动InfluxDB测试程序，支持不同的运行模式

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="influxdb-test"
CONFIG_FILE="config.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "InfluxDB测试程序运行脚本"
    echo ""
    echo "用法: $0 [选项] [命令]"
    echo ""
    echo "命令:"
    echo "  run          运行测试程序（默认）"
    echo "  build        编译程序"
    echo "  clean        清理编译文件"
    echo "  deps         安装依赖"
    echo "  check        检查环境"
    echo "  help         显示帮助信息"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE    指定配置文件（默认: config.yml）"
    echo "  -v, --verbose        详细输出"
    echo "  -h, --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 run                    # 使用默认配置运行"
    echo "  $0 run -c test.yml        # 使用指定配置运行"
    echo "  $0 build                  # 编译程序"
    echo "  $0 check                  # 检查环境"
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        log_error "Go未安装或不在PATH中"
        return 1
    fi
    
    local go_version=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $go_version"
    return 0
}

# 检查InfluxDB连接
check_influxdb() {
    local influxdb_url="http://localhost:8086"
    
    log_info "检查InfluxDB连接: $influxdb_url"
    
    if command -v curl &> /dev/null; then
        if curl -s "$influxdb_url/health" > /dev/null; then
            log_info "InfluxDB连接正常"
            return 0
        else
            log_warn "InfluxDB连接失败，请确保InfluxDB服务正在运行"
            log_info "启动InfluxDB服务: cd ../docker-service && ./start.sh"
            return 1
        fi
    else
        log_warn "curl未安装，跳过InfluxDB连接检查"
        return 0
    fi
}

# 安装依赖
install_deps() {
    log_info "安装Go依赖..."
    
    if [ ! -f "go.mod" ]; then
        log_error "go.mod文件不存在"
        return 1
    fi
    
    go mod tidy
    go mod download
    
    log_info "依赖安装完成"
}

# 编译程序
build_program() {
    log_info "编译程序..."
    
    # 设置编译参数
    local build_time=$(date -u '+%Y-%m-%d_%H:%M:%S')
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    # 编译
    go build -ldflags "-X main.BuildTime=$build_time -X main.GitCommit=$git_commit" -o "$PROJECT_NAME" .
    
    if [ $? -eq 0 ]; then
        log_info "编译成功: $PROJECT_NAME"
        return 0
    else
        log_error "编译失败"
        return 1
    fi
}

# 运行程序
run_program() {
    local config_file="$1"
    
    log_info "启动InfluxDB测试程序..."
    log_info "配置文件: $config_file"
    
    # 检查配置文件
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 运行程序
    if [ -f "$PROJECT_NAME" ]; then
        log_info "运行编译后的程序"
        ./"$PROJECT_NAME" "$config_file"
    else
        log_info "直接运行Go程序"
        go run . "$config_file"
    fi
}

# 清理文件
clean_files() {
    log_info "清理编译文件..."
    
    if [ -f "$PROJECT_NAME" ]; then
        rm "$PROJECT_NAME"
        log_info "删除: $PROJECT_NAME"
    fi
    
    # 清理Go缓存
    go clean -cache
    
    log_info "清理完成"
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查Go环境
    if ! check_go; then
        return 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_warn "配置文件不存在: $CONFIG_FILE"
    else
        log_info "配置文件: $CONFIG_FILE"
    fi
    
    # 检查InfluxDB连接
    check_influxdb
    
    log_info "环境检查完成"
}

# 主函数
main() {
    local command="run"
    local config_file="$CONFIG_FILE"
    local verbose=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                config_file="$2"
                shift 2
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            run|build|clean|deps|check|help)
                command="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$verbose" = true ]; then
        set -x
    fi
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 执行命令
    case $command in
        run)
            check_environment
            run_program "$config_file"
            ;;
        build)
            install_deps
            build_program
            ;;
        clean)
            clean_files
            ;;
        deps)
            install_deps
            ;;
        check)
            check_environment
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
