#!/bin/bash

# InfluxDB错误日志分析脚本
# 功能：分析和统计InfluxDB测试程序的错误日志

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="./logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "InfluxDB错误日志分析脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --all            分析所有日志文件"
    echo "  --errors-only    只显示错误信息"
    echo "  --summary        显示错误统计摘要"
    echo "  --json           分析JSON格式日志"
    echo "  --recent         分析最近的日志"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --all         # 分析所有日志文件"
    echo "  $0 --errors-only # 只显示错误信息"
    echo "  $0 --summary     # 显示错误统计"
}

# 分析所有日志文件
analyze_all_logs() {
    log_step "分析所有InfluxDB测试日志文件..."
    
    if [ ! -d "$LOG_DIR" ]; then
        log_error "日志目录不存在: $LOG_DIR"
        return 1
    fi
    
    log_info "日志目录: $LOG_DIR"
    log_info "发现的日志文件:"
    
    local log_files=($(find "$LOG_DIR" -name "*.log" -type f))
    
    if [ ${#log_files[@]} -eq 0 ]; then
        log_warn "未找到日志文件"
        return 1
    fi
    
    for log_file in "${log_files[@]}"; do
        local file_size=$(wc -l < "$log_file")
        local file_name=$(basename "$log_file")
        echo "  📄 $file_name ($file_size 行)"
    done
    
    echo ""
    
    # 分析每个日志文件
    for log_file in "${log_files[@]}"; do
        analyze_single_log "$log_file"
        echo ""
    done
}

# 分析单个日志文件
analyze_single_log() {
    local log_file="$1"
    local file_name=$(basename "$log_file")
    
    log_info "=== 分析日志文件: $file_name ==="
    
    # 基本统计
    local total_lines=$(wc -l < "$log_file")
    local error_lines=$(grep -c "\[ERROR\]" "$log_file" 2>/dev/null || echo "0")
    local warn_lines=$(grep -c "\[WARN\]" "$log_file" 2>/dev/null || echo "0")
    local info_lines=$(grep -c "\[INFO\]" "$log_file" 2>/dev/null || echo "0")
    local debug_lines=$(grep -c "\[DEBUG\]" "$log_file" 2>/dev/null || echo "0")
    
    echo "📊 基本统计:"
    echo "  总行数: $total_lines"
    echo "  ERROR: $error_lines"
    echo "  WARN:  $warn_lines"
    echo "  INFO:  $info_lines"
    echo "  DEBUG: $debug_lines"
    
    # 错误分析
    if [ "$error_lines" -gt 0 ]; then
        echo ""
        echo "❌ 错误信息详情:"
        grep "\[ERROR\]" "$log_file" | while read -r line; do
            echo "  $line"
        done
        
        # 错误类型分析
        echo ""
        echo "🔍 错误类型分析:"
        
        # 连接错误
        local conn_errors=$(grep -c "connection refused\|connect: connection refused\|dial tcp" "$log_file" 2>/dev/null || echo "0")
        if [ "$conn_errors" -gt 0 ]; then
            echo "  🔌 网络连接错误: $conn_errors 次"
        fi
        
        # 授权错误
        local auth_errors=$(grep -c "unauthorized\|Token授权验证失败\|unauthorized access" "$log_file" 2>/dev/null || echo "0")
        if [ "$auth_errors" -gt 0 ]; then
            echo "  🔐 授权验证错误: $auth_errors 次"
        fi
        
        # 查询错误
        local query_errors=$(grep -c "查询.*失败\|查询数据失败" "$log_file" 2>/dev/null || echo "0")
        if [ "$query_errors" -gt 0 ]; then
            echo "  🔍 查询操作错误: $query_errors 次"
        fi
        
        # 写入错误
        local write_errors=$(grep -c "写入.*失败\|Write error" "$log_file" 2>/dev/null || echo "0")
        if [ "$write_errors" -gt 0 ]; then
            echo "  ✍️ 写入操作错误: $write_errors 次"
        fi
    fi
    
    # 警告分析
    if [ "$warn_lines" -gt 0 ]; then
        echo ""
        echo "⚠️ 警告信息:"
        grep "\[WARN\]" "$log_file" | while read -r line; do
            echo "  $line"
        done
    fi
    
    # 测试结果分析
    local failed_tests=$(grep -c "测试结果.*FAILED\|测试.*失败" "$log_file" 2>/dev/null || echo "0")
    local success_tests=$(grep -c "测试结果.*SUCCESS\|测试.*通过" "$log_file" 2>/dev/null || echo "0")
    
    if [ "$failed_tests" -gt 0 ] || [ "$success_tests" -gt 0 ]; then
        echo ""
        echo "🧪 测试结果统计:"
        echo "  成功测试: $success_tests"
        echo "  失败测试: $failed_tests"
        
        if [ "$failed_tests" -gt 0 ]; then
            echo ""
            echo "❌ 失败的测试:"
            grep "测试结果.*FAILED\|测试.*失败" "$log_file" | while read -r line; do
                echo "  $line"
            done
        fi
    fi
}

# 只显示错误信息
show_errors_only() {
    log_step "提取所有错误信息..."
    
    local log_files=($(find "$LOG_DIR" -name "*.log" -type f))
    
    if [ ${#log_files[@]} -eq 0 ]; then
        log_warn "未找到日志文件"
        return 1
    fi
    
    local total_errors=0
    
    for log_file in "${log_files[@]}"; do
        local file_name=$(basename "$log_file")
        local error_count=$(grep -c "\[ERROR\]" "$log_file" 2>/dev/null || echo "0")
        
        if [ "$error_count" -gt 0 ]; then
            echo ""
            log_error "文件: $file_name (错误数: $error_count)"
            echo "----------------------------------------"
            grep "\[ERROR\]" "$log_file" | while read -r line; do
                echo "$line"
            done
            total_errors=$((total_errors + error_count))
        fi
    done
    
    echo ""
    log_info "总错误数: $total_errors"
}

# 显示错误统计摘要
show_summary() {
    log_step "生成错误统计摘要..."
    
    local log_files=($(find "$LOG_DIR" -name "*.log" -type f))
    
    if [ ${#log_files[@]} -eq 0 ]; then
        log_warn "未找到日志文件"
        return 1
    fi
    
    echo "📊 InfluxDB测试错误统计摘要"
    echo "========================================"
    
    local total_files=${#log_files[@]}
    local total_errors=0
    local total_warnings=0
    local files_with_errors=0
    
    echo ""
    echo "📁 日志文件统计:"
    printf "%-30s %8s %8s %8s\n" "文件名" "错误数" "警告数" "总行数"
    echo "----------------------------------------"
    
    for log_file in "${log_files[@]}"; do
        local file_name=$(basename "$log_file")
        local error_count=$(grep -c "\[ERROR\]" "$log_file" 2>/dev/null || echo "0")
        local warn_count=$(grep -c "\[WARN\]" "$log_file" 2>/dev/null || echo "0")
        local total_lines=$(wc -l < "$log_file")
        
        printf "%-30s %8s %8s %8s\n" "$file_name" "$error_count" "$warn_count" "$total_lines"
        
        total_errors=$((total_errors + error_count))
        total_warnings=$((total_warnings + warn_count))
        
        if [ "$error_count" -gt 0 ]; then
            files_with_errors=$((files_with_errors + 1))
        fi
    done
    
    echo "----------------------------------------"
    printf "%-30s %8s %8s %8s\n" "总计" "$total_errors" "$total_warnings" "-"
    
    echo ""
    echo "📈 总体统计:"
    echo "  日志文件总数: $total_files"
    echo "  有错误的文件: $files_with_errors"
    echo "  错误总数: $total_errors"
    echo "  警告总数: $total_warnings"
    
    if [ "$total_errors" -gt 0 ]; then
        local error_rate=$(awk "BEGIN {printf \"%.1f\", $files_with_errors * 100 / $total_files}")
        echo "  错误文件比例: $error_rate%"
    fi
    
    echo ""
    echo "🔍 常见错误类型:"
    
    # 统计各种错误类型
    local conn_errors=0
    local auth_errors=0
    local query_errors=0
    local write_errors=0
    
    for log_file in "${log_files[@]}"; do
        conn_errors=$((conn_errors + $(grep -c "connection refused\|connect: connection refused\|dial tcp" "$log_file" 2>/dev/null || echo "0")))
        auth_errors=$((auth_errors + $(grep -c "unauthorized\|Token授权验证失败\|unauthorized access" "$log_file" 2>/dev/null || echo "0")))
        query_errors=$((query_errors + $(grep -c "查询.*失败\|查询数据失败" "$log_file" 2>/dev/null || echo "0")))
        write_errors=$((write_errors + $(grep -c "写入.*失败\|Write error" "$log_file" 2>/dev/null || echo "0")))
    done
    
    echo "  🔌 网络连接错误: $conn_errors"
    echo "  🔐 授权验证错误: $auth_errors"
    echo "  🔍 查询操作错误: $query_errors"
    echo "  ✍️ 写入操作错误: $write_errors"
    
    echo ""
    echo "========================================"
}

# 分析JSON格式日志
analyze_json_logs() {
    log_step "分析JSON格式日志..."
    
    local json_logs=($(find "$LOG_DIR" -name "*json*.log" -type f))
    
    if [ ${#json_logs[@]} -eq 0 ]; then
        log_warn "未找到JSON格式日志文件"
        return 1
    fi
    
    for log_file in "${json_logs[@]}"; do
        local file_name=$(basename "$log_file")
        log_info "分析JSON日志: $file_name"
        
        if command -v jq &> /dev/null; then
            echo ""
            echo "📊 JSON日志统计:"
            
            # 按级别统计
            local error_count=$(grep '"level":"ERROR"' "$log_file" | wc -l)
            local warn_count=$(grep '"level":"WARN"' "$log_file" | wc -l)
            local info_count=$(grep '"level":"INFO"' "$log_file" | wc -l)
            local debug_count=$(grep '"level":"DEBUG"' "$log_file" | wc -l)
            
            echo "  ERROR: $error_count"
            echo "  WARN:  $warn_count"
            echo "  INFO:  $info_count"
            echo "  DEBUG: $debug_count"
            
            if [ "$error_count" -gt 0 ]; then
                echo ""
                echo "❌ JSON格式错误详情:"
                grep '"level":"ERROR"' "$log_file" | while read -r line; do
                    echo "$line" | jq -r '.timestamp + " [" + .level + "] " + .message'
                done
            fi
        else
            log_warn "未安装jq命令，无法解析JSON格式"
            echo "原始JSON错误日志:"
            grep '"level":"ERROR"' "$log_file"
        fi
        
        echo ""
    done
}

# 主函数
main() {
    local action="all"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --all)
                action="all"
                shift
                ;;
            --errors-only)
                action="errors"
                shift
                ;;
            --summary)
                action="summary"
                shift
                ;;
            --json)
                action="json"
                shift
                ;;
            --recent)
                action="recent"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 执行操作
    case $action in
        all)
            analyze_all_logs
            ;;
        errors)
            show_errors_only
            ;;
        summary)
            show_summary
            ;;
        json)
            analyze_json_logs
            ;;
        recent)
            # 分析最近的日志文件
            local recent_log=$(find "$LOG_DIR" -name "*.log" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
            if [ -n "$recent_log" ]; then
                log_info "分析最近的日志文件: $(basename "$recent_log")"
                analyze_single_log "$recent_log"
            else
                log_warn "未找到日志文件"
            fi
            ;;
        *)
            log_error "未知操作: $action"
            exit 1
            ;;
    esac
    
    log_success "分析完成"
}

# 执行主函数
main "$@"
