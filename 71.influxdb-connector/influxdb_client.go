package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	"github.com/influxdata/influxdb-client-go/v2/api/write"
	"github.com/influxdata/influxdb-client-go/v2/domain"
)

/**
 * InfluxDB客户端封装
 *
 * 功能：提供InfluxDB连接、写入、查询和健康检查功能
 *
 * 架构设计：
 * - 客户端封装：封装InfluxDB官方客户端，提供简化接口
 * - 连接管理：自动连接测试和重连机制
 * - 批量操作：支持批量写入，提高性能
 * - 错误处理：完善的错误处理和重试机制
 * - 统计监控：实时统计操作性能和状态
 */

// InfluxDBClient InfluxDB客户端结构体
// 封装InfluxDB连接和操作，提供统一的数据库访问接口
type InfluxDBClient struct {
	/** InfluxDB客户端实例 */
	client influxdb2.Client

	/** 写入API，用于数据写入操作 */
	writeAPI api.WriteAPI

	/** 查询API，用于数据查询操作 */
	queryAPI api.QueryAPI

	/** 客户端配置信息 */
	config InfluxDBConfig

	/** 操作统计信息 */
	stats InfluxDBStats

	/** 并发安全锁 */
	mutex sync.RWMutex

	/** 上下文，用于控制操作生命周期 */
	ctx context.Context

	/** 取消函数，用于清理资源 */
	cancel context.CancelFunc
}

// InfluxDBStats InfluxDB操作统计结构体
// 记录客户端的运行状态和性能指标
type InfluxDBStats struct {
	/** 连接状态：connecting, connected, disconnected, error */
	ConnectionStatus string

	/** 总写入次数 */
	TotalWrites int64

	/** 成功写入次数 */
	SuccessWrites int64

	/** 失败写入次数 */
	FailedWrites int64

	/** 总查询次数 */
	TotalQueries int64

	/** 成功查询次数 */
	SuccessQueries int64

	/** 失败查询次数 */
	FailedQueries int64

	/** 最后一次错误信息 */
	LastError string

	/** 最后一次操作时间 */
	LastOperationTime time.Time

	/** 平均响应时间（毫秒） */
	AvgResponseTime float64
}

// TestData 测试数据结构体
// 定义用于测试的传感器数据格式
type TestData struct {
	/** 设备ID */
	DeviceID string

	/** 时间戳 */
	Timestamp time.Time

	/** 温度值 */
	Temperature float64

	/** 湿度值 */
	Humidity float64

	/** 压力值 */
	Pressure float64

	/** 数据类型标识 */
	DataType string
}

// NewInfluxDBClient 创建新的InfluxDB客户端
// 初始化客户端连接，设置API接口和错误处理
//
// 参数：
//   - config: InfluxDB配置信息
//
// 返回：
//   - *InfluxDBClient: 客户端实例
//   - error: 创建错误
//
// 使用示例：
//
//	client, err := NewInfluxDBClient(config.InfluxDB)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	defer client.Close()
func NewInfluxDBClient(config InfluxDBConfig) (*InfluxDBClient, error) {
	log.Printf("🔗 正在创建InfluxDB客户端连接...")

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 创建InfluxDB客户端，配置批量写入参数
	client := influxdb2.NewClientWithOptions(
		config.URL,
		config.Token,
		influxdb2.DefaultOptions().
			SetBatchSize(uint(config.BatchSize)).
			SetFlushInterval(uint(config.FlushInterval.Milliseconds())).
			SetRetryInterval(uint(config.RetryDelay.Milliseconds())).
			SetMaxRetries(uint(config.RetryCount)),
	)

	// 创建客户端实例
	influxClient := &InfluxDBClient{
		client: client,
		config: config,
		ctx:    ctx,
		cancel: cancel,
		stats: InfluxDBStats{
			ConnectionStatus: "connecting",
		},
	}

	// 初始化API接口
	influxClient.writeAPI = client.WriteAPI(config.Organization, config.Bucket)
	influxClient.queryAPI = client.QueryAPI(config.Organization)

	// 设置错误处理
	influxClient.setupErrorHandling()

	// 测试连接
	if err := influxClient.testConnection(); err != nil {
		client.Close()
		cancel()
		return nil, fmt.Errorf("InfluxDB连接测试失败: %w", err)
	}

	influxClient.updateConnectionStatus("connected")
	log.Printf("✅ InfluxDB客户端连接成功")

	return influxClient, nil
}

// setupErrorHandling 设置错误处理
// 监听写入API的错误事件，更新统计信息
func (c *InfluxDBClient) setupErrorHandling() {
	// 启动错误监听协程
	go func() {
		for err := range c.writeAPI.Errors() {
			c.mutex.Lock()
			c.stats.FailedWrites++
			c.stats.LastError = err.Error()
			c.stats.LastOperationTime = time.Now()
			c.mutex.Unlock()

			log.Printf("❌ InfluxDB写入错误: %v", err)
		}
	}()
}

// testConnection 测试InfluxDB连接
// 执行健康检查，验证连接是否正常
//
// 返回：
//   - error: 连接错误，nil表示连接正常
func (c *InfluxDBClient) testConnection() error {
	log.Printf("🔍 正在测试InfluxDB连接...")

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	// 执行健康检查
	health, err := c.client.Health(ctx)
	if err != nil {
		c.updateConnectionStatus("error")
		return fmt.Errorf("健康检查失败: %w", err)
	}

	// 检查健康状态
	if health.Status != "pass" {
		c.updateConnectionStatus("error")
		return fmt.Errorf("InfluxDB健康状态异常: %s", health.Status)
	}

	log.Printf("✅ InfluxDB连接测试通过，状态: %s", health.Status)
	return nil
}

// ServerTestResult 服务器测试结果结构体
// 记录各项服务器测试的详细结果
type ServerTestResult struct {
	/** 网络连通性测试结果 */
	NetworkConnectivity bool

	/** 服务可用性测试结果 */
	ServiceAvailability bool

	/** 授权验证测试结果 */
	AuthenticationTest bool

	/** 组织验证测试结果 */
	OrganizationTest bool

	/** 存储桶验证测试结果 */
	BucketTest bool

	/** 读权限测试结果 */
	ReadPermissionTest bool

	/** 写权限测试结果 */
	WritePermissionTest bool

	/** 服务器版本信息 */
	ServerVersion string

	/** 服务器构建信息 */
	ServerBuild string

	/** 错误信息列表 */
	Errors []string

	/** 警告信息列表 */
	Warnings []string

	/** 测试总耗时 */
	TotalDuration time.Duration
}

// updateConnectionStatus 更新连接状态
// 线程安全地更新连接状态信息
//
// 参数：
//   - status: 新的连接状态
func (c *InfluxDBClient) updateConnectionStatus(status string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.stats.ConnectionStatus = status
	c.stats.LastOperationTime = time.Now()
}

// WriteTestData 写入单条测试数据
// 将测试数据转换为InfluxDB Point并写入数据库
//
// 参数：
//   - data: 测试数据对象
//
// 返回：
//   - error: 写入错误，nil表示成功
//
// 使用示例：
//
//	data := &TestData{
//	    DeviceID: "test-001",
//	    Timestamp: time.Now(),
//	    Temperature: 25.5,
//	    Humidity: 60.0,
//	    Pressure: 1013.25,
//	}
//	err := client.WriteTestData(data)
func (c *InfluxDBClient) WriteTestData(data *TestData) error {
	startTime := time.Now()

	// 更新统计信息
	c.mutex.Lock()
	c.stats.TotalWrites++
	c.mutex.Unlock()

	// 创建数据点
	point := influxdb2.NewPointWithMeasurement(c.config.Measurement).
		AddTag("device_id", data.DeviceID).
		AddTag("data_type", data.DataType).
		AddField("temperature", data.Temperature).
		AddField("humidity", data.Humidity).
		AddField("pressure", data.Pressure).
		SetTime(data.Timestamp)

	// 写入数据点
	c.writeAPI.WritePoint(point)

	// 更新成功统计
	c.mutex.Lock()
	c.stats.SuccessWrites++
	c.stats.LastOperationTime = time.Now()
	// 计算平均响应时间
	responseTime := float64(time.Since(startTime).Milliseconds())
	if c.stats.AvgResponseTime == 0 {
		c.stats.AvgResponseTime = responseTime
	} else {
		c.stats.AvgResponseTime = (c.stats.AvgResponseTime + responseTime) / 2
	}
	c.mutex.Unlock()

	log.Printf("📝 写入测试数据: 设备=%s, 温度=%.1f°C, 湿度=%.1f%%, 压力=%.2fhPa",
		data.DeviceID, data.Temperature, data.Humidity, data.Pressure)

	return nil
}

// BatchWriteTestData 批量写入测试数据
// 批量写入多条测试数据，提高写入效率
//
// 参数：
//   - dataList: 测试数据列表
//
// 返回：
//   - error: 写入错误，nil表示成功
func (c *InfluxDBClient) BatchWriteTestData(dataList []*TestData) error {
	if len(dataList) == 0 {
		return nil
	}

	startTime := time.Now()

	// 更新统计信息
	c.mutex.Lock()
	c.stats.TotalWrites += int64(len(dataList))
	c.mutex.Unlock()

	// 创建数据点列表
	var points []*write.Point
	for _, data := range dataList {
		point := influxdb2.NewPointWithMeasurement(c.config.Measurement).
			AddTag("device_id", data.DeviceID).
			AddTag("data_type", data.DataType).
			AddField("temperature", data.Temperature).
			AddField("humidity", data.Humidity).
			AddField("pressure", data.Pressure).
			SetTime(data.Timestamp)

		points = append(points, point)
	}

	// 批量写入数据点
	for _, point := range points {
		c.writeAPI.WritePoint(point)
	}

	// 强制刷新缓冲区
	c.writeAPI.Flush()

	// 更新成功统计
	c.mutex.Lock()
	c.stats.SuccessWrites += int64(len(dataList))
	c.stats.LastOperationTime = time.Now()
	// 计算平均响应时间
	responseTime := float64(time.Since(startTime).Milliseconds())
	if c.stats.AvgResponseTime == 0 {
		c.stats.AvgResponseTime = responseTime
	} else {
		c.stats.AvgResponseTime = (c.stats.AvgResponseTime + responseTime) / 2
	}
	c.mutex.Unlock()

	log.Printf("📝 批量写入测试数据: %d条记录", len(dataList))
	return nil
}

// QueryTestData 查询测试数据
// 根据设备ID和时间范围查询测试数据
//
// 参数：
//   - deviceID: 设备ID，为空则查询所有设备
//   - limit: 查询结果限制数量
//   - timeRange: 时间范围（小时），0表示查询所有时间
//
// 返回：
//   - []*TestData: 查询结果列表
//   - error: 查询错误
//
// 使用示例：
//
//	data, err := client.QueryTestData("test-001", 10, 1)
func (c *InfluxDBClient) QueryTestData(deviceID string, limit int, timeRange int) ([]*TestData, error) {
	startTime := time.Now()

	// 更新统计信息
	c.mutex.Lock()
	c.stats.TotalQueries++
	c.mutex.Unlock()

	// 构建查询语句
	var query string
	if deviceID != "" {
		if timeRange > 0 {
			query = fmt.Sprintf(`
				from(bucket: "%s")
				|> range(start: -%dh)
				|> filter(fn: (r) => r._measurement == "%s")
				|> filter(fn: (r) => r.device_id == "%s")
				|> limit(n: %d)
				|> sort(columns: ["_time"], desc: true)
			`, c.config.Bucket, timeRange, c.config.Measurement, deviceID, limit)
		} else {
			query = fmt.Sprintf(`
				from(bucket: "%s")
				|> range(start: -30d)
				|> filter(fn: (r) => r._measurement == "%s")
				|> filter(fn: (r) => r.device_id == "%s")
				|> limit(n: %d)
				|> sort(columns: ["_time"], desc: true)
			`, c.config.Bucket, c.config.Measurement, deviceID, limit)
		}
	} else {
		if timeRange > 0 {
			query = fmt.Sprintf(`
				from(bucket: "%s")
				|> range(start: -%dh)
				|> filter(fn: (r) => r._measurement == "%s")
				|> limit(n: %d)
				|> sort(columns: ["_time"], desc: true)
			`, c.config.Bucket, timeRange, c.config.Measurement, limit)
		} else {
			query = fmt.Sprintf(`
				from(bucket: "%s")
				|> range(start: -30d)
				|> filter(fn: (r) => r._measurement == "%s")
				|> limit(n: %d)
				|> sort(columns: ["_time"], desc: true)
			`, c.config.Bucket, c.config.Measurement, limit)
		}
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	// 执行查询
	result, err := c.queryAPI.Query(ctx, query)
	if err != nil {
		c.mutex.Lock()
		c.stats.FailedQueries++
		c.stats.LastError = err.Error()
		c.mutex.Unlock()
		return nil, fmt.Errorf("查询失败: %w", err)
	}

	// 解析查询结果
	var testDataList []*TestData
	dataMap := make(map[string]*TestData) // 用于合并同一时间点的不同字段

	for result.Next() {
		record := result.Record()
		timestamp := record.Time()
		deviceIDValue := record.ValueByKey("device_id")
		field := record.Field()
		value := record.Value()

		// 创建唯一键
		key := fmt.Sprintf("%s_%d", deviceIDValue, timestamp.Unix())

		// 获取或创建TestData对象
		testData, exists := dataMap[key]
		if !exists {
			testData = &TestData{
				DeviceID:  fmt.Sprintf("%v", deviceIDValue),
				Timestamp: timestamp,
				DataType:  "sensor",
			}
			dataMap[key] = testData
		}

		// 根据字段类型设置值
		switch field {
		case "temperature":
			if v, ok := value.(float64); ok {
				testData.Temperature = v
			}
		case "humidity":
			if v, ok := value.(float64); ok {
				testData.Humidity = v
			}
		case "pressure":
			if v, ok := value.(float64); ok {
				testData.Pressure = v
			}
		}
	}

	// 检查查询错误
	if result.Err() != nil {
		c.mutex.Lock()
		c.stats.FailedQueries++
		c.stats.LastError = result.Err().Error()
		c.mutex.Unlock()
		return nil, fmt.Errorf("查询结果错误: %w", result.Err())
	}

	// 转换为列表
	for _, data := range dataMap {
		testDataList = append(testDataList, data)
	}

	// 更新成功统计
	c.mutex.Lock()
	c.stats.SuccessQueries++
	c.stats.LastOperationTime = time.Now()
	// 计算平均响应时间
	responseTime := float64(time.Since(startTime).Milliseconds())
	if c.stats.AvgResponseTime == 0 {
		c.stats.AvgResponseTime = responseTime
	} else {
		c.stats.AvgResponseTime = (c.stats.AvgResponseTime + responseTime) / 2
	}
	c.mutex.Unlock()

	log.Printf("🔍 查询测试数据完成: 设备=%s, 结果数量=%d", deviceID, len(testDataList))
	return testDataList, nil
}

// GetStats 获取客户端统计信息
// 返回客户端的运行状态和性能统计数据
//
// 返回：
//   - InfluxDBStats: 统计信息副本
func (c *InfluxDBClient) GetStats() InfluxDBStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 返回统计信息的副本
	return InfluxDBStats{
		ConnectionStatus:  c.stats.ConnectionStatus,
		TotalWrites:       c.stats.TotalWrites,
		SuccessWrites:     c.stats.SuccessWrites,
		FailedWrites:      c.stats.FailedWrites,
		TotalQueries:      c.stats.TotalQueries,
		SuccessQueries:    c.stats.SuccessQueries,
		FailedQueries:     c.stats.FailedQueries,
		LastError:         c.stats.LastError,
		LastOperationTime: c.stats.LastOperationTime,
		AvgResponseTime:   c.stats.AvgResponseTime,
	}
}

// PrintStats 打印统计信息
// 格式化输出客户端的运行状态和性能统计
func (c *InfluxDBClient) PrintStats() {
	stats := c.GetStats()

	log.Printf("📊 InfluxDB客户端统计信息:")
	log.Printf("   连接状态: %s", stats.ConnectionStatus)
	log.Printf("   写入统计: 总计=%d, 成功=%d, 失败=%d",
		stats.TotalWrites, stats.SuccessWrites, stats.FailedWrites)
	log.Printf("   查询统计: 总计=%d, 成功=%d, 失败=%d",
		stats.TotalQueries, stats.SuccessQueries, stats.FailedQueries)
	log.Printf("   平均响应时间: %.2fms", stats.AvgResponseTime)

	if stats.LastError != "" {
		log.Printf("   最后错误: %s", stats.LastError)
	}

	if !stats.LastOperationTime.IsZero() {
		log.Printf("   最后操作时间: %s", stats.LastOperationTime.Format("2006-01-02 15:04:05"))
	}
}

// Flush 强制刷新写入缓冲区
// 确保所有待写入的数据都被发送到InfluxDB
func (c *InfluxDBClient) Flush() {
	log.Printf("🔄 正在刷新InfluxDB写入缓冲区...")
	c.writeAPI.Flush()
	log.Printf("✅ InfluxDB写入缓冲区刷新完成")
}

// Close 关闭InfluxDB客户端
// 清理资源，关闭连接和协程
func (c *InfluxDBClient) Close() {
	log.Printf("🔒 正在关闭InfluxDB客户端...")

	// 刷新写入缓冲区
	c.writeAPI.Flush()

	// 关闭客户端
	c.client.Close()

	// 取消上下文
	c.cancel()

	// 更新连接状态
	c.updateConnectionStatus("disconnected")

	log.Printf("✅ InfluxDB客户端已关闭")
}

// TestServerConnectivity 全面测试InfluxDB服务器连接
// 执行多项测试以验证服务器的各项功能和权限
//
// 返回：
//   - *ServerTestResult: 详细的测试结果
//   - error: 测试过程中的严重错误
//
// 使用示例：
//
//	result, err := client.TestServerConnectivity()
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// 检查各项测试结果
func (c *InfluxDBClient) TestServerConnectivity() (*ServerTestResult, error) {
	startTime := time.Now()
	result := &ServerTestResult{
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	log.Printf("🔍 开始全面测试InfluxDB服务器连接...")

	// 1. 网络连通性测试
	log.Printf("📡 测试网络连通性...")
	if err := c.testNetworkConnectivity(result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("网络连通性测试失败: %v", err))
		log.Printf("❌ 网络连通性测试失败: %v", err)
	} else {
		result.NetworkConnectivity = true
		log.Printf("✅ 网络连通性测试通过")
	}

	// 2. 服务可用性测试
	log.Printf("🏥 测试服务可用性...")
	if err := c.testServiceAvailability(result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("服务可用性测试失败: %v", err))
		log.Printf("❌ 服务可用性测试失败: %v", err)
	} else {
		result.ServiceAvailability = true
		log.Printf("✅ 服务可用性测试通过")
	}

	// 3. 授权验证测试
	log.Printf("🔐 测试授权验证...")
	if err := c.testAuthentication(result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("授权验证测试失败: %v", err))
		log.Printf("❌ 授权验证测试失败: %v", err)
	} else {
		result.AuthenticationTest = true
		log.Printf("✅ 授权验证测试通过")
	}

	// 4. 组织验证测试
	log.Printf("🏢 测试组织验证...")
	if err := c.testOrganization(result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("组织验证测试失败: %v", err))
		log.Printf("❌ 组织验证测试失败: %v", err)
	} else {
		result.OrganizationTest = true
		log.Printf("✅ 组织验证测试通过")
	}

	// 5. 存储桶验证测试
	log.Printf("🪣 测试存储桶验证...")
	if err := c.testBucket(result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("存储桶验证测试失败: %v", err))
		log.Printf("❌ 存储桶验证测试失败: %v", err)
	} else {
		result.BucketTest = true
		log.Printf("✅ 存储桶验证测试通过")
	}

	// 6. 读权限测试
	log.Printf("📖 测试读权限...")
	if err := c.testReadPermission(result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("读权限测试失败: %v", err))
		log.Printf("❌ 读权限测试失败: %v", err)
	} else {
		result.ReadPermissionTest = true
		log.Printf("✅ 读权限测试通过")
	}

	// 7. 写权限测试
	log.Printf("✍️ 测试写权限...")
	if err := c.testWritePermission(result); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("写权限测试失败: %v", err))
		log.Printf("❌ 写权限测试失败: %v", err)
	} else {
		result.WritePermissionTest = true
		log.Printf("✅ 写权限测试通过")
	}

	// 计算总耗时
	result.TotalDuration = time.Since(startTime)

	// 输出测试总结
	c.printTestSummary(result)

	return result, nil
}

// testNetworkConnectivity 测试网络连通性
// 验证是否能够连接到InfluxDB服务器
func (c *InfluxDBClient) testNetworkConnectivity(result *ServerTestResult) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, 5*time.Second)
	defer cancel()

	// 尝试执行简单的ping操作
	_, err := c.client.Ping(ctx)
	if err != nil {
		return fmt.Errorf("无法连接到InfluxDB服务器: %w", err)
	}

	return nil
}

// testServiceAvailability 测试服务可用性
// 验证InfluxDB服务是否正常运行
func (c *InfluxDBClient) testServiceAvailability(result *ServerTestResult) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	// 执行健康检查
	health, err := c.client.Health(ctx)
	if err != nil {
		return fmt.Errorf("健康检查失败: %w", err)
	}

	// 检查健康状态
	if health.Status != "pass" {
		return fmt.Errorf("服务状态异常: %s", health.Status)
	}

	// 记录服务器版本信息
	if health.Version != nil {
		result.ServerVersion = *health.Version
		log.Printf("📋 服务器版本: %s", result.ServerVersion)
	}

	return nil
}

// testAuthentication 测试授权验证
// 验证提供的Token是否有效
func (c *InfluxDBClient) testAuthentication(result *ServerTestResult) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	// 尝试获取用户信息来验证授权
	usersAPI := c.client.UsersAPI()
	user, err := usersAPI.Me(ctx)
	if err != nil {
		return fmt.Errorf("Token授权验证失败: %w", err)
	}

	if user == nil {
		return fmt.Errorf("无法获取用户信息，Token可能无效")
	}

	log.Printf("🔐 授权用户: %s", user.Name)
	return nil
}

// testOrganization 测试组织验证
// 验证指定的组织是否存在且可访问，支持自动创建
func (c *InfluxDBClient) testOrganization(result *ServerTestResult) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	// 获取组织API
	orgsAPI := c.client.OrganizationsAPI()

	// 查找指定的组织
	org, err := orgsAPI.FindOrganizationByName(ctx, c.config.Organization)
	if err != nil {
		// 如果启用自动创建且组织不存在，则尝试创建
		if c.config.AutoCreateOrg && isNotFoundError(err) {
			log.Printf("🔧 组织 '%s' 不存在，尝试自动创建...", c.config.Organization)
			return c.createOrganization(ctx, result)
		}
		return fmt.Errorf("无法找到组织 '%s': %w", c.config.Organization, err)
	}

	if org == nil {
		// 如果启用自动创建，则尝试创建组织
		if c.config.AutoCreateOrg {
			log.Printf("🔧 组织 '%s' 不存在，尝试自动创建...", c.config.Organization)
			return c.createOrganization(ctx, result)
		}
		return fmt.Errorf("组织 '%s' 不存在", c.config.Organization)
	}

	log.Printf("🏢 组织信息: %s (ID: %s)", org.Name, *org.Id)
	return nil
}

// isNotFoundError 检查错误是否为"未找到"类型的错误
// 用于判断组织或存储桶是否不存在
func isNotFoundError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "not found") ||
		strings.Contains(errStr, "404") ||
		strings.Contains(errStr, "does not exist")
}

// createOrganization 创建新的组织
// 当组织不存在且启用自动创建时调用
func (c *InfluxDBClient) createOrganization(ctx context.Context, result *ServerTestResult) error {
	log.Printf("🔧 开始创建组织: %s", c.config.Organization)

	// 获取组织API
	orgsAPI := c.client.OrganizationsAPI()

	// 创建组织对象
	orgCreate := &domain.Organization{
		Name:        c.config.Organization,
		Description: &c.config.OrganizationDescription,
	}

	// 执行创建
	org, err := orgsAPI.CreateOrganization(ctx, orgCreate)
	if err != nil {
		return fmt.Errorf("创建组织失败: %w", err)
	}

	if org == nil {
		return fmt.Errorf("创建组织返回空结果")
	}

	log.Printf("✅ 组织创建成功: %s (ID: %s)", org.Name, *org.Id)

	// 记录警告信息
	result.Warnings = append(result.Warnings, fmt.Sprintf("自动创建了组织: %s", c.config.Organization))

	return nil
}

// testBucket 测试存储桶验证
// 验证指定的存储桶是否存在且可访问，支持自动创建
func (c *InfluxDBClient) testBucket(result *ServerTestResult) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	// 获取存储桶API
	bucketsAPI := c.client.BucketsAPI()

	// 查找指定的存储桶
	bucket, err := bucketsAPI.FindBucketByName(ctx, c.config.Bucket)
	if err != nil {
		// 如果启用自动创建且存储桶不存在，则尝试创建
		if c.config.AutoCreateBucket && isNotFoundError(err) {
			log.Printf("🔧 存储桶 '%s' 不存在，尝试自动创建...", c.config.Bucket)
			return c.createBucket(ctx, result)
		}
		return fmt.Errorf("无法找到存储桶 '%s': %w", c.config.Bucket, err)
	}

	if bucket == nil {
		// 如果启用自动创建，则尝试创建存储桶
		if c.config.AutoCreateBucket {
			log.Printf("🔧 存储桶 '%s' 不存在，尝试自动创建...", c.config.Bucket)
			return c.createBucket(ctx, result)
		}
		return fmt.Errorf("存储桶 '%s' 不存在", c.config.Bucket)
	}

	log.Printf("🪣 存储桶信息: %s (ID: %s)", bucket.Name, *bucket.Id)
	return nil
}

// createBucket 创建新的存储桶
// 当存储桶不存在且启用自动创建时调用
func (c *InfluxDBClient) createBucket(ctx context.Context, result *ServerTestResult) error {
	log.Printf("🔧 开始创建存储桶: %s", c.config.Bucket)

	// 首先需要获取组织ID
	orgsAPI := c.client.OrganizationsAPI()
	org, err := orgsAPI.FindOrganizationByName(ctx, c.config.Organization)
	if err != nil {
		return fmt.Errorf("创建存储桶时无法找到组织 '%s': %w", c.config.Organization, err)
	}

	if org == nil {
		return fmt.Errorf("创建存储桶时组织 '%s' 不存在", c.config.Organization)
	}

	// 获取存储桶API
	bucketsAPI := c.client.BucketsAPI()

	// 设置保留策略
	var retentionRules []domain.RetentionRule
	if c.config.BucketRetentionDays > 0 {
		retentionSeconds := int64(c.config.BucketRetentionDays * 24 * 3600)
		retentionRules = append(retentionRules, domain.RetentionRule{
			EverySeconds: retentionSeconds,
		})
	}

	// 创建存储桶对象
	bucketCreate := &domain.Bucket{
		Name:           c.config.Bucket,
		Description:    &c.config.BucketDescription,
		OrgID:          org.Id,
		RetentionRules: retentionRules,
	}

	// 执行创建
	bucket, err := bucketsAPI.CreateBucket(ctx, bucketCreate)
	if err != nil {
		return fmt.Errorf("创建存储桶失败: %w", err)
	}

	if bucket == nil {
		return fmt.Errorf("创建存储桶返回空结果")
	}

	log.Printf("✅ 存储桶创建成功: %s (ID: %s)", bucket.Name, *bucket.Id)

	// 显示保留策略信息
	if c.config.BucketRetentionDays > 0 {
		log.Printf("📅 存储桶保留策略: %d 天", c.config.BucketRetentionDays)
	} else {
		log.Printf("📅 存储桶保留策略: 永久保留")
	}

	// 记录警告信息
	result.Warnings = append(result.Warnings, fmt.Sprintf("自动创建了存储桶: %s", c.config.Bucket))

	return nil
}

// testReadPermission 测试读权限
// 验证是否有权限从指定存储桶读取数据
func (c *InfluxDBClient) testReadPermission(result *ServerTestResult) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	// 构建简单的查询语句
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: -1h)
		|> limit(n: 1)
	`, c.config.Bucket)

	// 执行查询测试
	queryResult, err := c.queryAPI.Query(ctx, query)
	if err != nil {
		return fmt.Errorf("读权限测试失败: %w", err)
	}

	// 检查查询结果
	if queryResult.Err() != nil {
		return fmt.Errorf("查询执行错误: %w", queryResult.Err())
	}

	log.Printf("📖 读权限验证通过")
	return nil
}

// testWritePermission 测试写权限
// 验证是否有权限向指定存储桶写入数据
func (c *InfluxDBClient) testWritePermission(result *ServerTestResult) error {
	// 创建测试数据点
	testPoint := influxdb2.NewPointWithMeasurement("connectivity_test").
		AddTag("test_type", "write_permission").
		AddTag("client", "influxdb-test").
		AddField("test_value", 1).
		AddField("timestamp", time.Now().Unix()).
		SetTime(time.Now())

	// 写入测试数据点
	c.writeAPI.WritePoint(testPoint)

	// 强制刷新以确保写入
	c.writeAPI.Flush()

	// 等待写入完成
	time.Sleep(1 * time.Second)

	log.Printf("✍️ 写权限验证通过")
	return nil
}

// printTestSummary 打印测试总结
// 输出详细的测试结果摘要
func (c *InfluxDBClient) printTestSummary(result *ServerTestResult) {
	log.Printf("\n📊 InfluxDB服务器连接测试总结:")
	log.Printf("=" + strings.Repeat("=", 50))

	// 基本信息
	if result.ServerVersion != "" {
		log.Printf("🔧 服务器版本: %s", result.ServerVersion)
	}
	log.Printf("⏱️  总测试时间: %v", result.TotalDuration)

	// 测试结果
	log.Printf("\n🧪 测试结果:")
	c.printTestItem("网络连通性", result.NetworkConnectivity)
	c.printTestItem("服务可用性", result.ServiceAvailability)
	c.printTestItem("授权验证", result.AuthenticationTest)
	c.printTestItem("组织验证", result.OrganizationTest)
	c.printTestItem("存储桶验证", result.BucketTest)
	c.printTestItem("读权限", result.ReadPermissionTest)
	c.printTestItem("写权限", result.WritePermissionTest)

	// 统计通过率
	totalTests := 7
	passedTests := 0
	if result.NetworkConnectivity {
		passedTests++
	}
	if result.ServiceAvailability {
		passedTests++
	}
	if result.AuthenticationTest {
		passedTests++
	}
	if result.OrganizationTest {
		passedTests++
	}
	if result.BucketTest {
		passedTests++
	}
	if result.ReadPermissionTest {
		passedTests++
	}
	if result.WritePermissionTest {
		passedTests++
	}

	passRate := float64(passedTests) / float64(totalTests) * 100
	log.Printf("\n📈 通过率: %d/%d (%.1f%%)", passedTests, totalTests, passRate)

	// 错误信息
	if len(result.Errors) > 0 {
		log.Printf("\n❌ 错误信息:")
		for i, err := range result.Errors {
			log.Printf("   %d. %s", i+1, err)
		}
	}

	// 警告信息
	if len(result.Warnings) > 0 {
		log.Printf("\n⚠️  警告信息:")
		for i, warning := range result.Warnings {
			log.Printf("   %d. %s", i+1, warning)
		}
	}

	// 总体状态
	if passedTests == totalTests {
		log.Printf("\n🎉 所有测试通过！InfluxDB服务器连接完全正常")
	} else if passedTests >= totalTests/2 {
		log.Printf("\n⚠️  部分测试失败，请检查配置和权限")
	} else {
		log.Printf("\n❌ 大部分测试失败，请检查服务器状态和配置")
	}

	log.Printf("=" + strings.Repeat("=", 50))
}

// printTestItem 打印单个测试项结果
// 格式化输出测试项的名称和结果状态
func (c *InfluxDBClient) printTestItem(name string, passed bool) {
	status := "❌ 失败"
	if passed {
		status = "✅ 通过"
	}
	log.Printf("   %-12s: %s", name, status)
}
