# InfluxDB测试配置文件
# 用于测试InfluxDB连接、写入和查询功能

# 服务配置
service:
  name: "influxdb-test"
  debug: true
  timeout: 30s

# InfluxDB配置 - 使用docker-service中的InfluxDB实例
influxdb:
  url: "http://localhost:8086"           # InfluxDB服务器地址
  token: "1mP5kuBXyOh14IzhoW7M8oOjiWDBjYgvOWIUXIgui5iwSXC_bJpEOANTmzoP8m6iYSrxd2Q9KSSXQAV0le03nw=="           # 认证令牌（来自docker-compose配置）
  organization: "mdc-org"                # 组织名称（来自docker-compose配置）
  bucket: "device-data"                  # 存储桶名称（来自docker-compose配置）
  measurement: "test_data"               # 测量名称（用于测试）
  batch_size: 100                       # 批量写入大小
  flush_interval: 5s                    # 刷新间隔
  timeout: 10s                          # 操作超时时间
  retry_count: 3                        # 重试次数
  retry_delay: 2s                       # 重试延迟

  # 自动创建功能（可选）
  auto_create_org: false                # 是否自动创建组织
  auto_create_bucket: false             # 是否自动创建存储桶
  bucket_retention_days: 0              # 存储桶保留天数（0=永久保留）
  organization_description: "MDC系统组织"  # 组织描述
  bucket_description: "设备数据存储桶"      # 存储桶描述

# 测试配置
test:
  write_count: 100                      # 写入测试数据条数
  query_limit: 50                       # 查询结果限制
  test_device_id: "test-device-001"     # 测试设备ID
  test_interval: 1s                     # 测试数据生成间隔

# 日志配置
logging:
  enable_file: true                     # 启用文件日志
  log_dir: "./logs"                     # 日志目录
  log_file: "influxdb-test.log"         # 日志文件名
  max_size: 100                         # 单个日志文件最大大小(MB)
  max_backups: 5                        # 保留的日志文件数量
  max_age: 30                           # 日志文件保留天数
  compress: true                        # 是否压缩旧日志文件
  level: "info"                         # 日志级别: debug, info, warn, error
  format: "text"                        # 日志格式: text, json
