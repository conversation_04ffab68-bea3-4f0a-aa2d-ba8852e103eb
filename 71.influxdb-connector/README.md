# InfluxDB 测试程序

这是一个用于测试 InfluxDB 连接、写入和查询功能的 Go 程序。程序提供了完整的 InfluxDB 客户端封装，支持单条和批量数据写入、灵活的数据查询以及详细的性能统计。

## 功能特性

### 🔗 连接管理
- 全面的服务器连接测试（网络、授权、权限）
- 自动连接测试和健康检查
- 连接状态监控和错误处理
- 优雅的资源清理和连接关闭

### 🔧 自动创建功能
- 自动创建不存在的组织和存储桶
- 支持自定义保留策略和描述信息
- 详细的创建过程日志记录
- 适用于开发环境和自动化部署

### 📝 数据写入
- 单条数据写入，适合实时数据处理
- 批量数据写入，提高写入性能
- 自动批量配置和缓冲区管理
- 写入错误监控和重试机制

### 🔍 数据查询
- 灵活的查询条件（设备ID、时间范围）
- 支持结果数量限制
- 自动数据类型转换和结果解析
- 查询性能统计

### 📊 性能监控
- 实时统计写入和查询性能
- 连接状态和错误信息跟踪
- 平均响应时间计算
- 详细的操作日志

### 📝 日志系统
- 同时输出到控制台和文件
- 支持文本和JSON两种格式
- 自动日志轮转和压缩
- 可配置的日志级别过滤
- 结构化的测试结果记录
- 性能统计日志

## 项目结构

```
71.influxdb-test/
├── main.go              # 主程序入口
├── config.go            # 配置管理
├── influxdb_client.go   # InfluxDB客户端封装
├── logger.go            # 日志管理模块
├── config.yml           # 配置文件
├── config-json-log.yml  # JSON日志格式配置
├── config-server-test.yml # 服务器测试配置
├── go.mod              # Go模块定义
├── run.sh              # 运行脚本
├── logs/               # 日志文件目录
└── README.md           # 项目说明
```

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- Go 1.21 或更高版本
- InfluxDB 2.x 服务

### 2. 启动 InfluxDB 服务

使用项目中的 Docker 服务：

```bash
# 启动 InfluxDB 和其他基础服务
cd ../docker-service
./start.sh
```

InfluxDB 服务配置：
- URL: http://localhost:8086
- 组织: mdc-org
- 存储桶: device-data
- 令牌: mdc-token-123456789

### 3. 运行测试程序

```bash
# 使用运行脚本（推荐）
./run.sh

# 或者直接运行
go run .

# 指定配置文件
./run.sh -c config.yml
```

### 4. 查看测试结果

程序将依次执行以下测试：

1. **服务器连接测试** - 全面验证 InfluxDB 服务器状态
   - 网络连通性测试
   - 服务可用性检查
   - Token 授权验证
   - 组织和存储桶验证
   - 读写权限测试
2. **基础连接测试** - 验证 InfluxDB 连接状态
3. **单条写入测试** - 写入单条测试数据
4. **批量写入测试** - 批量写入多条数据
5. **查询测试** - 查询写入的数据
6. **统计信息** - 显示性能统计

## 配置说明

### 配置文件结构

```yaml
# 服务配置
service:
  name: "influxdb-test"
  debug: true
  timeout: 30s

# InfluxDB配置
influxdb:
  url: "http://localhost:8086"
  token: "mdc-token-123456789"
  organization: "mdc-org"
  bucket: "device-data"
  measurement: "test_data"
  batch_size: 100
  flush_interval: 5s
  timeout: 10s
  retry_count: 3
  retry_delay: 2s

# 测试配置
test:
  write_count: 100
  query_limit: 50
  test_device_id: "test-device-001"
  test_interval: 1s
```

### 配置参数说明

#### 服务配置 (service)
- `name`: 服务名称，用于日志标识
- `debug`: 调试模式开关
- `timeout`: 服务超时时间

#### InfluxDB配置 (influxdb)
- `url`: InfluxDB 服务器地址
- `token`: API 访问令牌
- `organization`: 组织名称
- `bucket`: 存储桶名称
- `measurement`: 测量名称（类似表名）
- `batch_size`: 批量写入大小
- `flush_interval`: 刷新间隔
- `timeout`: 操作超时时间
- `retry_count`: 重试次数
- `retry_delay`: 重试延迟

#### 测试配置 (test)
- `write_count`: 批量写入测试的数据条数
- `query_limit`: 查询结果限制数量
- `test_device_id`: 测试设备ID
- `test_interval`: 测试数据生成间隔

#### 日志配置 (logging)
- `enable_file`: 是否启用文件日志输出
- `log_dir`: 日志文件存储目录
- `log_file`: 日志文件名称
- `max_size`: 单个日志文件最大大小（MB）
- `max_backups`: 保留的日志文件数量
- `max_age`: 日志文件保留天数
- `compress`: 是否压缩旧日志文件
- `level`: 日志级别（debug, info, warn, error）
- `format`: 日志格式（text, json）

## 运行脚本使用

### 基本命令

```bash
# 运行测试程序
./run.sh run

# 编译程序
./run.sh build

# 安装依赖
./run.sh deps

# 检查环境
./run.sh check

# 清理文件
./run.sh clean

# 显示帮助
./run.sh help
```

### 选项参数

```bash
# 指定配置文件
./run.sh run -c custom.yml

# 使用JSON格式日志
./run.sh run -c config-json-log.yml

# 详细输出
./run.sh run -v

# 显示帮助
./run.sh -h
```

## 日志功能

### 日志输出格式

项目支持两种日志格式：

#### 文本格式（默认）
```
2025/07/12 18:06:48.128263 [INFO] 🚀 启动InfluxDB测试程序
2025/07/12 18:06:48.149495 [INFO] 🧪 测试结果 - 连接测试: SUCCESS (耗时: 15.084µs) - InfluxDB连接和健康检查
2025/07/12 18:06:53.192678 [INFO] 📊 性能统计 - 连接: connected, 写入: 101/101, 查询: 2/2, 响应时间: 9.25ms
```

#### JSON格式
```json
{"timestamp":"2025-07-12 18:07:43.706","level":"INFO","message":"🚀 启动InfluxDB测试程序"}
{"timestamp":"2025-07-12 18:07:43.711","level":"INFO","message":"TEST_RESULT: {\"test_name\":\"连接测试\",\"status\":\"SUCCESS\",\"duration_ms\":0,\"details\":\"InfluxDB连接和健康检查\"}"}
{"timestamp":"2025-07-12 18:07:48.742","level":"INFO","message":"PERFORMANCE_STATS: {\"connection_status\":\"connected\",\"total_writes\":51,\"success_writes\":51,\"failed_writes\":0,\"total_queries\":2,\"success_queries\":2,\"failed_queries\":0,\"avg_response_time_ms\":6.50}"}
```

### 日志文件管理

- **自动轮转**: 当日志文件达到配置的最大大小时自动轮转
- **压缩存储**: 旧日志文件自动压缩以节省空间
- **保留策略**: 根据配置保留指定数量的日志文件和天数
- **并发安全**: 支持多线程安全的日志写入

### 查看日志

```bash
# 查看最新日志
tail -f logs/influxdb-test.log

# 查看JSON格式日志
tail -f logs/influxdb-test-json.log

# 使用jq解析JSON日志
tail -f logs/influxdb-test-json.log | jq '.'

# 过滤特定级别的日志
grep "ERROR" logs/influxdb-test.log

# 查看测试结果
grep "TEST_RESULT" logs/influxdb-test-json.log | jq '.message | fromjson'
```

## 测试数据格式

程序使用以下测试数据结构：

```go
type TestData struct {
    DeviceID    string    // 设备ID
    Timestamp   time.Time // 时间戳
    Temperature float64   // 温度值 (°C)
    Humidity    float64   // 湿度值 (%)
    Pressure    float64   // 压力值 (hPa)
    DataType    string    // 数据类型标识
}
```

数据将以以下格式存储在 InfluxDB 中：
- **测量名称**: test_data
- **标签**: device_id, data_type
- **字段**: temperature, humidity, pressure
- **时间戳**: 自动设置

## 🔧 自动创建功能

### 功能概述

InfluxDB Connector 支持自动创建组织和存储桶功能，当指定的组织或存储桶不存在时，程序会自动创建它们。

### 配置参数

```yaml
influxdb:
  # 基本配置
  organization: "your-org"
  bucket: "your-bucket"

  # 自动创建功能
  auto_create_org: true                  # 启用自动创建组织
  auto_create_bucket: true               # 启用自动创建存储桶
  bucket_retention_days: 30              # 存储桶保留天数（0=永久保留）
  organization_description: "组织描述"    # 组织描述信息
  bucket_description: "存储桶描述"        # 存储桶描述信息
```

### 权限要求

⚠️ **重要**: 自动创建功能需要管理员级别的 Token，包含以下权限：
- `write:orgs` - 创建组织权限
- `write:buckets` - 创建存储桶权限
- `read:orgs` - 读取组织信息权限

### 使用示例

```bash
# 测试自动创建功能
go run . config-auto-create.yml

# 使用测试脚本
./test_auto_create.sh --test-create

# 显示配置说明
./test_auto_create.sh --show-config
```

### 测试结果示例

```
🔧 组织 'test-new-org' 不存在，尝试自动创建...
✅ 组织创建成功: test-new-org (ID: 0123456789abcdef)

🔧 存储桶 'test-new-bucket' 不存在，尝试自动创建...
✅ 存储桶创建成功: test-new-bucket (ID: fedcba9876543210)
📅 存储桶保留策略: 30 天
```

### 故障排除

如果遇到权限错误：
```
❌ 创建组织失败: unauthorized: write:orgs is unauthorized
```

解决方案：
1. 确保使用具有管理员权限的 Token
2. 在 InfluxDB UI 中生成 "All Access API Token"
3. 检查 Token 是否包含必要的权限

详细指南请参考：[AUTO_CREATE_GUIDE.md](AUTO_CREATE_GUIDE.md)

## 服务器连接测试

### 测试项目

项目提供了全面的 InfluxDB 服务器连接测试，包括以下7个测试项目：

#### 1. 网络连通性测试
- 验证是否能够连接到 InfluxDB 服务器
- 检查网络路径和端口可达性
- 测试基本的网络通信

#### 2. 服务可用性测试
- 执行 InfluxDB 健康检查
- 验证服务状态是否正常
- 获取服务器版本信息

#### 3. 授权验证测试
- 验证提供的 Token 是否有效
- 检查 Token 权限范围
- 获取授权用户信息

#### 4. 组织验证测试
- 验证指定组织是否存在
- 检查组织访问权限
- 获取组织详细信息

#### 5. 存储桶验证测试
- 验证指定存储桶是否存在
- 检查存储桶访问权限
- 获取存储桶详细信息

#### 6. 读权限测试
- 执行简单的数据查询操作
- 验证是否有读取权限
- 测试查询API可用性

#### 7. 写权限测试
- 写入测试数据点
- 验证是否有写入权限
- 测试写入API可用性

### 测试结果示例

```
📊 InfluxDB服务器连接测试总结:
===================================================
🔧 服务器版本: v2.7.12
⏱️  总测试时间: 1.01356325s

🧪 测试结果:
   网络连通性       : ✅ 通过
   服务可用性       : ✅ 通过
   授权验证        : ✅ 通过
   组织验证        : ✅ 通过
   存储桶验证       : ✅ 通过
   读权限         : ✅ 通过
   写权限         : ✅ 通过

📈 通过率: 7/7 (100.0%)

🎉 所有测试通过！InfluxDB服务器连接完全正常
===================================================
```

### 专用配置

使用 `config-server-test.yml` 进行服务器测试：

```bash
# 运行服务器连接测试
go run . config-server-test.yml

# 使用测试脚本
./test_logs.sh --server-test
```

## 性能统计

程序提供详细的性能统计信息：

- **连接状态**: connecting, connected, disconnected, error
- **写入统计**: 总计、成功、失败次数
- **查询统计**: 总计、成功、失败次数
- **响应时间**: 平均响应时间（毫秒）
- **错误信息**: 最后一次错误详情
- **操作时间**: 最后一次操作时间

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 InfluxDB 服务是否启动
   - 验证 URL 和端口配置
   - 确认网络连接正常

2. **认证失败**
   - 检查 token 配置是否正确
   - 验证组织和存储桶名称
   - 确认权限设置

3. **写入失败**
   - 检查存储桶是否存在
   - 验证数据格式是否正确
   - 查看错误日志详情

4. **查询无结果**
   - 确认数据已写入成功
   - 检查查询条件和时间范围
   - 等待数据写入完成

### 调试方法

1. 启用调试模式：
   ```yaml
   service:
     debug: true
   ```

2. 使用详细输出：
   ```bash
   ./run.sh run -v
   ```

3. 检查 InfluxDB 日志：
   ```bash
   docker logs mdc_influxdb
   ```

## 扩展开发

### 添加新的测试用例

1. 在 `main.go` 中添加新的测试函数
2. 在 `runTests` 函数中调用新测试
3. 更新配置文件添加相关参数

### 自定义数据结构

1. 修改 `TestData` 结构体
2. 更新写入和查询方法
3. 调整配置文件中的字段映射

### 集成到其他项目

1. 复制 `influxdb_client.go` 和 `config.go`
2. 根据需要修改配置结构
3. 实现自定义的数据模型和业务逻辑

## Docker 部署

### Docker 镜像构建

```bash
# 使用构建脚本
./docker_build_linux_x86.sh build

# 或直接使用 docker build
docker build -t influxdb-test:latest .
```

### Docker 运行

```bash
# 单独运行测试程序（需要外部 InfluxDB）
docker run --rm --network host \
  -v $(pwd)/config.yml:/app/config.yml:ro \
  -v $(pwd)/data:/app/data \
  influxdb-test:latest

# 使用 Docker Compose 运行完整服务栈
docker-compose up -d influxdb
docker-compose run --rm influxdb-test
```

## 🐳 Docker 容器间通信

### 网络配置要点

当 InfluxDB 服务器和测试程序都运行在 Docker 容器中时，需要注意以下配置：

#### ❌ 错误配置
```yaml
influxdb:
  url: "http://localhost:8086"  # 容器内无法访问
```

#### ✅ 正确配置
```yaml
influxdb:
  url: "http://influxdb:8086"   # 使用Docker服务名
```

### 网络测试工具

项目提供了专门的 Docker 网络测试工具：

```bash
# 查看网络通信指南
./docker-network-example.sh

# 设置Docker环境
./test_docker_network.sh setup

# 运行网络通信测试
./test_docker_network.sh test

# 查看容器状态
./test_docker_network.sh status
```

### 网络连通性测试

```bash
# DNS解析测试
docker-compose run --rm influxdb-test nslookup influxdb

# 端口连接测试
docker-compose run --rm influxdb-test nc -z influxdb 8086

# HTTP连接测试
docker-compose run --rm influxdb-test curl -f http://influxdb:8086/health

# 运行完整测试
docker-compose run --rm influxdb-test ./influxdb-test config-docker-network.yml
```

### 故障排除

#### 常见问题

1. **Connection Refused**
   - 原因：使用了 `localhost` 而不是服务名
   - 解决：将 URL 改为 `http://influxdb:8086`

2. **DNS 解析失败**
   - 原因：容器不在同一网络
   - 解决：检查 `docker-compose.yml` 配置

3. **服务未就绪**
   - 原因：InfluxDB 启动需要时间
   - 解决：增加重试次数和超时时间

详细的 Docker 网络通信指南请参考：[DOCKER_NETWORK_GUIDE.md](DOCKER_NETWORK_GUIDE.md)

### Docker Compose 配置

项目提供了多个 Docker Compose 配置文件：

- `docker-compose.yml` - 基础配置，包含 InfluxDB 和测试程序
- `docker-compose.override.yml` - 开发环境覆盖配置
- `docker-compose.prod.yml` - 生产环境配置

```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 包含监控服务
docker-compose --profile monitoring up -d
```

### 构建脚本功能

`docker_build_linux_x86.sh` 脚本提供了完整的 Docker 管理功能：

```bash
# 构建镜像
./docker_build_linux_x86.sh build

# 构建并推送
./docker_build_linux_x86.sh build-push -r your-registry.com

# 运行容器
./docker_build_linux_x86.sh run

# 查看日志
./docker_build_linux_x86.sh logs

# 清理镜像
./docker_build_linux_x86.sh clean
```

## 测试结果

### 性能测试结果

最新测试结果（Docker 环境）：

```
📊 InfluxDB客户端统计信息:
   连接状态: connected
   写入统计: 总计=101, 成功=101, 失败=0
   查询统计: 总计=2, 成功=2, 失败=0
   平均响应时间: 6.88ms
   最后操作时间: 2025-07-12 17:58:58

✅ 批量写入测试通过: 100条记录, 耗时0.01s, 速率10,441.2条/秒
✅ 查询测试通过: 返回50条记录
```

### 功能验证

- ✅ InfluxDB 连接和健康检查
- ✅ 单条数据写入
- ✅ 批量数据写入（100条记录）
- ✅ 数据查询和结果解析
- ✅ 性能统计和监控
- ✅ Docker 容器化部署
- ✅ 错误处理和重试机制

## 许可证

本项目遵循 MIT 许可证。
