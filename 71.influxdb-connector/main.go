package main

import (
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"
)

/**
 * InfluxDB测试程序主入口
 *
 * 功能：测试InfluxDB连接、写入和查询功能
 *
 * 测试流程：
 * 1. 加载配置文件
 * 2. 创建InfluxDB客户端连接
 * 3. 执行连接测试
 * 4. 执行写入测试（单条和批量）
 * 5. 执行查询测试
 * 6. 显示统计信息
 * 7. 清理资源
 *
 * 使用方法：
 *   go run . [config_file]
 *   默认配置文件: config.yml
 */

func main() {
	log.Printf("🚀 启动InfluxDB测试程序")

	// 获取配置文件路径
	configPath := "config.yml"
	if len(os.Args) > 1 {
		configPath = os.Args[1]
	}

	// 加载配置
	config, err := LoadConfig(configPath)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化日志系统
	logger, err := NewLogger(config.Logging)
	if err != nil {
		log.Fatalf("❌ 初始化日志系统失败: %v", err)
	}
	defer logger.Close()

	logger.Info("🚀 启动InfluxDB测试程序")
	logger.Info("📋 配置加载成功: %s", config.Service.Name)
	if config.Service.Debug {
		logger.Debug("🔧 调试模式已启用")
	}

	// 记录配置信息
	logger.Info("📝 配置信息:")
	logger.Info("  - InfluxDB URL: %s", config.InfluxDB.URL)
	logger.Info("  - 组织: %s", config.InfluxDB.Organization)
	logger.Info("  - 存储桶: %s", config.InfluxDB.Bucket)
	logger.Info("  - 测试设备: %s", config.Test.TestDeviceID)
	logger.Info("  - 写入数量: %d", config.Test.WriteCount)
	logger.Info("  - 日志文件: %s", filepath.Join(config.Logging.LogDir, config.Logging.LogFile))

	// 创建InfluxDB客户端
	client, err := NewInfluxDBClient(config.InfluxDB)
	if err != nil {
		logger.Error("❌ 创建InfluxDB客户端失败: %v", err)
		log.Fatalf("❌ 创建InfluxDB客户端失败: %v", err)
	}
	defer client.Close()

	// 设置信号处理
	setupSignalHandler(client, logger)

	// 执行测试流程
	if err := runTests(client, config, logger); err != nil {
		logger.Error("❌ 测试执行失败: %v", err)
		log.Fatalf("❌ 测试执行失败: %v", err)
	}

	logger.Info("🎉 InfluxDB测试程序执行完成")
	log.Printf("🎉 InfluxDB测试程序执行完成")
}

// runTests 执行所有测试
// 按顺序执行连接测试、写入测试和查询测试
//
// 参数：
//   - client: InfluxDB客户端
//   - config: 配置信息
//   - logger: 日志管理器
//
// 返回：
//   - error: 测试错误
func runTests(client *InfluxDBClient, config *Config, logger *Logger) error {
	logger.Info("🧪 开始执行InfluxDB功能测试")
	log.Printf("🧪 开始执行InfluxDB功能测试")

	// 1. 服务器连接测试
	logger.Info("\n=== 1. 服务器连接测试 ===")
	log.Printf("\n=== 1. 服务器连接测试 ===")
	startTime := time.Now()
	serverResult, err := client.TestServerConnectivity()
	logger.LogTestResult("服务器连接测试", err == nil, time.Since(startTime), "全面的InfluxDB服务器连接验证")
	if err != nil {
		return fmt.Errorf("服务器连接测试失败: %w", err)
	}

	// 记录服务器测试结果到日志
	logger.LogServerTestResult(serverResult)

	// 2. 基础连接测试
	logger.Info("\n=== 2. 基础连接测试 ===")
	log.Printf("\n=== 2. 基础连接测试 ===")
	startTime = time.Now()
	err = testConnection(client, logger)
	logger.LogTestResult("基础连接测试", err == nil, time.Since(startTime), "InfluxDB连接和健康检查")
	if err != nil {
		return fmt.Errorf("基础连接测试失败: %w", err)
	}

	// 3. 单条写入测试
	logger.Info("\n=== 3. 单条写入测试 ===")
	log.Printf("\n=== 3. 单条写入测试 ===")
	startTime = time.Now()
	err = testSingleWrite(client, config, logger)
	logger.LogTestResult("单条写入测试", err == nil, time.Since(startTime), "写入单条测试数据")
	if err != nil {
		return fmt.Errorf("单条写入测试失败: %w", err)
	}

	// 4. 批量写入测试
	logger.Info("\n=== 4. 批量写入测试 ===")
	log.Printf("\n=== 4. 批量写入测试 ===")
	startTime = time.Now()
	err = testBatchWrite(client, config, logger)
	logger.LogTestResult("批量写入测试", err == nil, time.Since(startTime), fmt.Sprintf("批量写入%d条记录", config.Test.WriteCount))
	if err != nil {
		return fmt.Errorf("批量写入测试失败: %w", err)
	}

	// 5. 查询测试
	logger.Info("\n=== 5. 查询测试 ===")
	log.Printf("\n=== 5. 查询测试 ===")
	startTime = time.Now()
	err = testQuery(client, config, logger)
	logger.LogTestResult("查询测试", err == nil, time.Since(startTime), "查询测试数据并验证结果")
	if err != nil {
		return fmt.Errorf("查询测试失败: %w", err)
	}

	// 6. 显示统计信息
	logger.Info("\n=== 6. 统计信息 ===")
	log.Printf("\n=== 6. 统计信息 ===")
	stats := client.GetStats()
	logger.LogPerformanceStats(stats)
	client.PrintStats()

	return nil
}

// testConnection 测试连接
// 验证InfluxDB连接是否正常
func testConnection(client *InfluxDBClient, logger *Logger) error {
	logger.Info("🔗 测试InfluxDB连接...")
	log.Printf("🔗 测试InfluxDB连接...")

	stats := client.GetStats()
	if stats.ConnectionStatus != "connected" {
		logger.Error("连接状态异常: %s", stats.ConnectionStatus)
		return fmt.Errorf("连接状态异常: %s", stats.ConnectionStatus)
	}

	logger.Info("✅ 连接测试通过")
	log.Printf("✅ 连接测试通过")
	return nil
}

// testSingleWrite 测试单条写入
// 写入单条测试数据并验证
func testSingleWrite(client *InfluxDBClient, config *Config, logger *Logger) error {
	logger.Info("📝 测试单条数据写入...")
	log.Printf("📝 测试单条数据写入...")

	// 生成测试数据
	testData := generateTestData(config.Test.TestDeviceID, 1)[0]
	logger.Debug("生成测试数据: 设备=%s, 温度=%.1f°C", testData.DeviceID, testData.Temperature)

	// 写入数据
	if err := client.WriteTestData(testData); err != nil {
		logger.Error("写入数据失败: %v", err)
		return fmt.Errorf("写入数据失败: %w", err)
	}

	// 刷新缓冲区
	client.Flush()

	// 等待数据写入完成
	time.Sleep(2 * time.Second)

	logger.Info("✅ 单条写入测试通过")
	log.Printf("✅ 单条写入测试通过")
	return nil
}

// testBatchWrite 测试批量写入
// 批量写入测试数据并验证性能
func testBatchWrite(client *InfluxDBClient, config *Config, logger *Logger) error {
	logger.Info("📝 测试批量数据写入...")
	log.Printf("📝 测试批量数据写入...")

	// 生成批量测试数据
	testDataList := generateTestData(config.Test.TestDeviceID, config.Test.WriteCount)
	logger.Debug("生成批量测试数据: %d条记录", len(testDataList))

	// 记录开始时间
	startTime := time.Now()

	// 批量写入数据
	if err := client.BatchWriteTestData(testDataList); err != nil {
		logger.Error("批量写入数据失败: %v", err)
		return fmt.Errorf("批量写入数据失败: %w", err)
	}

	// 计算写入性能
	duration := time.Since(startTime)
	rate := float64(config.Test.WriteCount) / duration.Seconds()

	logger.Info("✅ 批量写入测试通过: %d条记录, 耗时%.2fs, 速率%.1f条/秒",
		config.Test.WriteCount, duration.Seconds(), rate)
	log.Printf("✅ 批量写入测试通过: %d条记录, 耗时%.2fs, 速率%.1f条/秒",
		config.Test.WriteCount, duration.Seconds(), rate)

	return nil
}

// testQuery 测试查询
// 查询测试数据并验证结果
func testQuery(client *InfluxDBClient, config *Config, logger *Logger) error {
	logger.Info("🔍 测试数据查询...")
	log.Printf("🔍 测试数据查询...")

	// 等待数据写入完成
	time.Sleep(3 * time.Second)

	// 查询指定设备的数据
	results, err := client.QueryTestData(config.Test.TestDeviceID, config.Test.QueryLimit, 1)
	if err != nil {
		logger.Error("查询数据失败: %v", err)
		return fmt.Errorf("查询数据失败: %w", err)
	}

	// 验证查询结果
	if len(results) == 0 {
		logger.Warn("⚠️  查询结果为空，可能数据还未写入完成")
		log.Printf("⚠️  查询结果为空，可能数据还未写入完成")
	} else {
		logger.Info("✅ 查询测试通过: 返回%d条记录", len(results))
		log.Printf("✅ 查询测试通过: 返回%d条记录", len(results))

		// 显示前几条记录
		displayCount := 3
		if len(results) < displayCount {
			displayCount = len(results)
		}

		logger.Info("📋 查询结果示例（前%d条）:", displayCount)
		log.Printf("📋 查询结果示例（前%d条）:", displayCount)
		for i := 0; i < displayCount; i++ {
			data := results[i]
			resultMsg := fmt.Sprintf("   设备: %s, 时间: %s, 温度: %.1f°C, 湿度: %.1f%%, 压力: %.2fhPa",
				data.DeviceID,
				data.Timestamp.Format("2006-01-02 15:04:05"),
				data.Temperature,
				data.Humidity,
				data.Pressure)
			logger.Info(resultMsg)
			log.Printf(resultMsg)
		}
	}

	// 查询所有设备的数据
	allResults, err := client.QueryTestData("", 10, 1)
	if err != nil {
		logger.Error("查询所有设备数据失败: %v", err)
		return fmt.Errorf("查询所有设备数据失败: %w", err)
	}

	logger.Info("📊 所有设备查询结果: %d条记录", len(allResults))
	log.Printf("📊 所有设备查询结果: %d条记录", len(allResults))
	return nil
}

// generateTestData 生成测试数据
// 创建指定数量的模拟传感器数据
//
// 参数：
//   - deviceID: 设备ID
//   - count: 数据条数
//
// 返回：
//   - []*TestData: 测试数据列表
func generateTestData(deviceID string, count int) []*TestData {
	var dataList []*TestData
	baseTime := time.Now()

	for i := 0; i < count; i++ {
		data := &TestData{
			DeviceID:    deviceID,
			Timestamp:   baseTime.Add(-time.Duration(i) * time.Second),
			Temperature: 20.0 + rand.Float64()*15.0,   // 20-35°C
			Humidity:    40.0 + rand.Float64()*40.0,   // 40-80%
			Pressure:    1000.0 + rand.Float64()*50.0, // 1000-1050hPa
			DataType:    "sensor",
		}
		dataList = append(dataList, data)
	}

	return dataList
}

// setupSignalHandler 设置信号处理
// 处理程序中断信号，确保资源正确清理
func setupSignalHandler(client *InfluxDBClient, logger *Logger) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		logger.Info("\n🛑 接收到中断信号，正在清理资源...")
		log.Printf("\n🛑 接收到中断信号，正在清理资源...")
		client.Close()
		logger.Close()
		os.Exit(0)
	}()
}
