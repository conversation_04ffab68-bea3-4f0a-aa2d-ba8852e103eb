# Docker Compose 生产环境配置
# 用于生产部署的优化配置

version: '3.8'

name: influxdb_test_prod

services:
  # InfluxDB测试程序 - 生产配置
  influxdb-test:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
        VERSION: ${VERSION:-1.0.0}
    
    image: ${DOCKER_REGISTRY:-}mdc-system/influxdb-test:${VERSION:-1.0.0}
    container_name: influxdb_test_prod
    restart: always
    
    # 生产环境变量
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_LOG_LEVEL=info
      
      # InfluxDB连接配置
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=${INFLUXDB_TOKEN}
      - INFLUXDB_ORG=${INFLUXDB_ORG:-mdc-org}
      - INFLUXDB_BUCKET=${INFLUXDB_BUCKET:-device-data}
      
      # 测试配置
      - TEST_WRITE_COUNT=${TEST_WRITE_COUNT:-5000}
      - TEST_QUERY_LIMIT=${TEST_QUERY_LIMIT:-1000}
      - TEST_DEVICE_ID=${TEST_DEVICE_ID:-prod-test-device}
    
    # 生产模式挂载
    volumes:
      - ./config/prod-config.yml:/app/config.yml:ro
      - prod_data:/app/data
      - prod_logs:/app/logs
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # 健康检查
    healthcheck:
      test: ["CMD", "pgrep", "-f", "influxdb-test"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    depends_on:
      influxdb:
        condition: service_healthy
    
    networks:
      - influxdb_prod_network
    
    # 生产模式标签
    labels:
      - "env=production"
      - "service=influxdb-test"
      - "version=${VERSION:-1.0.0}"

  # InfluxDB - 生产配置
  influxdb:
    image: influxdb:2.7-alpine
    container_name: influxdb_prod
    restart: always
    
    # 生产端口（内部访问）
    expose:
      - "8086"
    
    # 生产环境变量
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=${INFLUXDB_ADMIN_USER:-admin}
      - DOCKER_INFLUXDB_INIT_PASSWORD=${INFLUXDB_ADMIN_PASSWORD}
      - DOCKER_INFLUXDB_INIT_ORG=${INFLUXDB_ORG:-mdc-org}
      - DOCKER_INFLUXDB_INIT_BUCKET=${INFLUXDB_BUCKET:-device-data}
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=${INFLUXDB_TOKEN}
      
      # 生产优化配置
      - INFLUXD_REPORTING_DISABLED=true
      - INFLUXD_HTTP_BIND_ADDRESS=:8086
      - INFLUXD_LOG_LEVEL=info
    
    # 生产数据持久化
    volumes:
      - influxdb_prod_data:/var/lib/influxdb2
      - ./config/influxdb-prod.conf:/etc/influxdb2/influxdb.conf:ro
      - prod_logs:/var/log/influxdb2
    
    # 生产资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 5
        window: 300s
    
    # 生产健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    
    networks:
      - influxdb_prod_network
    
    # 生产标签
    labels:
      - "env=production"
      - "service=influxdb"
      - "database=timeseries"

  # Nginx反向代理（可选）
  nginx:
    image: nginx:1.25-alpine
    container_name: influxdb_test_nginx_prod
    restart: always
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx/prod-nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - prod_logs:/var/log/nginx
    
    depends_on:
      - influxdb-test
      - influxdb
    
    networks:
      - influxdb_prod_network
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.2'
        reservations:
          memory: 64M
          cpus: '0.1'
    
    profiles:
      - proxy
    
    labels:
      - "env=production"
      - "service=nginx"
      - "role=reverse-proxy"

  # 日志收集器（可选）
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: influxdb_test_fluentd_prod
    restart: always
    
    volumes:
      - ./fluentd/prod-fluent.conf:/fluentd/etc/fluent.conf:ro
      - prod_logs:/var/log/containers:ro
      - fluentd_data:/var/log/fluentd
    
    networks:
      - influxdb_prod_network
    
    profiles:
      - logging
    
    labels:
      - "env=production"
      - "service=fluentd"
      - "role=log-collector"

# 生产网络配置
networks:
  influxdb_prod_network:
    driver: bridge
    name: influxdb_prod_network
    ipam:
      config:
        - subnet: **********/16
    # 生产网络安全配置
    driver_opts:
      com.docker.network.bridge.name: br-influxdb-prod
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"

# 生产数据卷配置
volumes:
  # InfluxDB生产数据
  influxdb_prod_data:
    driver: local
    name: influxdb_prod_data
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/influxdb-test}/influxdb
  
  # 应用生产数据
  prod_data:
    driver: local
    name: influxdb_test_prod_data
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/influxdb-test}/data
  
  # 生产日志
  prod_logs:
    driver: local
    name: influxdb_test_prod_logs
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-/var/log/influxdb-test}
  
  # Fluentd数据
  fluentd_data:
    driver: local
    name: influxdb_test_fluentd_data

# 生产环境配置模板
x-production-defaults: &production-defaults
  restart: always
  logging:
    driver: "json-file"
    options:
      max-size: "50m"
      max-file: "5"
  security_opt:
    - no-new-privileges:true
  read_only: false
  tmpfs:
    - /tmp:noexec,nosuid,size=100m
