#!/bin/bash

# InfluxDB管理员Token设置脚本
# 功能：帮助获取和配置管理员Token以使用自动创建功能

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFLUXDB_URL="http://localhost:8086"
CONFIG_FILE="config-admin-test.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "InfluxDB管理员Token设置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --guide          显示获取管理员Token的指导"
    echo "  --set-token      设置管理员Token到配置文件"
    echo "  --test           测试自动创建功能"
    echo "  --open-ui        打开InfluxDB Web UI"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --guide       # 显示获取Token指导"
    echo "  $0 --set-token   # 交互式设置Token"
    echo "  $0 --test        # 测试自动创建功能"
}

# 显示获取管理员Token的详细指导
show_token_guide() {
    log_step "获取InfluxDB管理员Token指导"
    
    echo ""
    log_info "🔑 为什么需要管理员Token？"
    echo "自动创建组织和存储桶需要以下权限："
    echo "  ✓ write:orgs - 创建组织权限"
    echo "  ✓ write:buckets - 创建存储桶权限"
    echo "  ✓ read:orgs - 读取组织权限"
    echo "  ✓ read:buckets - 读取存储桶权限"
    
    echo ""
    log_info "📋 获取管理员Token的步骤："
    echo ""
    echo "1️⃣  打开InfluxDB Web UI"
    echo "   浏览器访问: $INFLUXDB_URL"
    echo ""
    echo "2️⃣  登录InfluxDB"
    echo "   使用您的管理员账户登录"
    echo ""
    echo "3️⃣  进入API Tokens页面"
    echo "   点击左侧菜单: Data > API Tokens"
    echo ""
    echo "4️⃣  生成新Token"
    echo "   点击右上角: Generate API Token"
    echo ""
    echo "5️⃣  选择Token类型"
    echo "   选择: All Access API Token"
    echo "   (这将给予所有权限，包括创建组织和存储桶)"
    echo ""
    echo "6️⃣  设置Token描述"
    echo "   描述: Auto-create test token"
    echo "   点击: Save"
    echo ""
    echo "7️⃣  复制Token"
    echo "   复制生成的Token字符串"
    echo "   ⚠️  注意：Token只显示一次，请立即复制保存"
    
    echo ""
    log_warn "⚠️  安全提醒："
    echo "  • 管理员Token具有完全访问权限"
    echo "  • 请妥善保管，不要泄露给他人"
    echo "  • 建议定期轮换Token"
    echo "  • 测试完成后可以删除该Token"
    
    echo ""
    log_info "🚀 获取Token后，运行以下命令设置："
    echo "  $0 --set-token"
}

# 打开InfluxDB Web UI
open_influxdb_ui() {
    log_step "打开InfluxDB Web UI..."
    
    log_info "正在打开浏览器访问: $INFLUXDB_URL"
    
    # 检查InfluxDB是否运行
    if curl -s "$INFLUXDB_URL/health" > /dev/null 2>&1; then
        log_success "InfluxDB服务正在运行"
        
        # 尝试打开浏览器
        if command -v open &> /dev/null; then
            open "$INFLUXDB_URL"
            log_success "已在默认浏览器中打开InfluxDB UI"
        elif command -v xdg-open &> /dev/null; then
            xdg-open "$INFLUXDB_URL"
            log_success "已在默认浏览器中打开InfluxDB UI"
        else
            log_info "请手动在浏览器中访问: $INFLUXDB_URL"
        fi
    else
        log_error "InfluxDB服务未运行或无法访问"
        log_info "请确保InfluxDB服务已启动"
    fi
}

# 交互式设置Token
set_admin_token() {
    log_step "设置管理员Token到配置文件"
    
    echo ""
    log_info "请按照以下步骤操作："
    echo "1. 如果还没有获取Token，请先运行: $0 --guide"
    echo "2. 或者运行: $0 --open-ui 打开InfluxDB UI"
    echo ""
    
    # 提示输入Token
    echo -n "请输入管理员Token: "
    read -r admin_token
    
    if [ -z "$admin_token" ]; then
        log_error "Token不能为空"
        return 1
    fi
    
    # 验证Token格式（基本检查）
    if [ ${#admin_token} -lt 20 ]; then
        log_warn "Token长度似乎太短，请确认是否正确"
    fi
    
    # 测试Token有效性
    log_info "正在验证Token有效性..."
    local response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Token $admin_token" \
        "$INFLUXDB_URL/api/v2/me" \
        -o /tmp/token_test.json)
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "Token验证成功"
        
        # 获取用户信息
        if command -v jq &> /dev/null && [ -f /tmp/token_test.json ]; then
            local user_name=$(cat /tmp/token_test.json | jq -r '.name // "未知"')
            log_info "授权用户: $user_name"
        fi
        
        # 更新配置文件
        log_info "正在更新配置文件: $CONFIG_FILE"
        
        # 备份原配置文件
        if [ -f "$CONFIG_FILE" ]; then
            cp "$CONFIG_FILE" "${CONFIG_FILE}.backup"
            log_info "已备份原配置文件为: ${CONFIG_FILE}.backup"
        fi
        
        # 替换Token
        sed -i.tmp "s/token: \"REPLACE_WITH_ADMIN_TOKEN\"/token: \"$admin_token\"/" "$CONFIG_FILE"
        rm -f "${CONFIG_FILE}.tmp"
        
        log_success "配置文件更新完成"
        
        # 测试创建权限
        log_info "正在测试创建权限..."
        test_create_permissions "$admin_token"
        
    else
        log_error "Token验证失败 (HTTP: $http_code)"
        if [ -f /tmp/token_test.json ]; then
            cat /tmp/token_test.json
        fi
        return 1
    fi
    
    rm -f /tmp/token_test.json
}

# 测试创建权限
test_create_permissions() {
    local token="$1"
    
    # 测试创建组织权限
    local test_org_name="permission-test-$(date +%s)"
    local create_response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Authorization: Token $token" \
        -H "Content-Type: application/json" \
        -d "{\"name\":\"$test_org_name\",\"description\":\"权限测试\"}" \
        "$INFLUXDB_URL/api/v2/orgs" \
        -o /tmp/create_test.json)
    
    local create_http_code="${create_response: -3}"
    
    if [ "$create_http_code" = "201" ]; then
        log_success "✅ 具有创建组织权限"
        
        # 删除测试组织
        if command -v jq &> /dev/null && [ -f /tmp/create_test.json ]; then
            local org_id=$(cat /tmp/create_test.json | jq -r '.id')
            if [ -n "$org_id" ] && [ "$org_id" != "null" ]; then
                curl -s -X DELETE \
                    -H "Authorization: Token $token" \
                    "$INFLUXDB_URL/api/v2/orgs/$org_id" > /dev/null
                log_info "已清理测试组织"
            fi
        fi
    else
        log_error "❌ 无创建组织权限 (HTTP: $create_http_code)"
        if [ -f /tmp/create_test.json ]; then
            cat /tmp/create_test.json
        fi
    fi
    
    rm -f /tmp/create_test.json
}

# 测试自动创建功能
test_auto_create() {
    log_step "测试自动创建功能..."
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        log_info "请先运行: $0 --set-token"
        return 1
    fi
    
    # 检查Token是否已设置
    if grep -q "REPLACE_WITH_ADMIN_TOKEN" "$CONFIG_FILE"; then
        log_error "配置文件中的Token尚未设置"
        log_info "请先运行: $0 --set-token"
        return 1
    fi
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go未安装或不在PATH中"
        return 1
    fi
    
    # 设置Go环境
    export GOROOT=/opt/homebrew/opt/go/libexec
    export PATH=$GOROOT/bin:$PATH
    
    log_info "使用配置文件: $CONFIG_FILE"
    log_info "开始测试自动创建功能..."
    
    echo ""
    echo "========================================"
    
    # 运行测试
    if go run . "$CONFIG_FILE"; then
        log_success "自动创建功能测试完成"
        
        # 分析日志结果
        if [ -f "logs/influxdb-admin-test.log" ]; then
            echo ""
            log_info "测试结果分析："
            
            # 检查自动创建记录
            local org_created=$(grep -c "组织创建成功" logs/influxdb-admin-test.log 2>/dev/null || echo "0")
            local bucket_created=$(grep -c "存储桶创建成功" logs/influxdb-admin-test.log 2>/dev/null || echo "0")
            
            if [ "$org_created" -gt 0 ]; then
                log_success "✅ 组织自动创建成功"
            fi
            
            if [ "$bucket_created" -gt 0 ]; then
                log_success "✅ 存储桶自动创建成功"
            fi
            
            # 显示测试通过率
            local pass_rate=$(grep "通过率:" logs/influxdb-admin-test.log | tail -1)
            if [ -n "$pass_rate" ]; then
                log_info "$pass_rate"
            fi
        fi
    else
        log_error "自动创建功能测试失败"
        return 1
    fi
    
    echo "========================================"
}

# 主函数
main() {
    local action="guide"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --guide)
                action="guide"
                shift
                ;;
            --set-token)
                action="set-token"
                shift
                ;;
            --test)
                action="test"
                shift
                ;;
            --open-ui)
                action="open-ui"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    echo "🔧 InfluxDB管理员Token设置工具"
    echo "=================================="
    
    # 执行操作
    case $action in
        guide)
            show_token_guide
            ;;
        set-token)
            set_admin_token
            ;;
        test)
            test_auto_create
            ;;
        open-ui)
            open_influxdb_ui
            ;;
        *)
            log_error "未知操作: $action"
            exit 1
            ;;
    esac
    
    echo ""
    log_success "操作完成"
}

# 执行主函数
main "$@"
