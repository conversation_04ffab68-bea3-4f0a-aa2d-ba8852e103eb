#!/bin/bash

# Docker网络通信示例脚本
# 演示如何在Docker容器间进行InfluxDB通信

set -e

echo "🐳 Docker容器间InfluxDB通信示例"
echo "=================================="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker环境
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 显示网络配置对比
show_network_config() {
    echo ""
    log_info "网络配置对比:"
    echo "----------------------------------------"
    echo "❌ 错误配置（容器内无法访问）:"
    echo "   influxdb:"
    echo "     url: \"http://localhost:8086\""
    echo ""
    echo "✅ 正确配置（使用Docker服务名）:"
    echo "   influxdb:"
    echo "     url: \"http://influxdb:8086\""
    echo "----------------------------------------"
}

# 显示Docker Compose配置
show_compose_config() {
    echo ""
    log_info "Docker Compose网络配置:"
    echo "----------------------------------------"
    cat << 'EOF'
# docker-compose.yml
version: '3.8'

services:
  influxdb:
    image: influxdb:2.7
    container_name: influxdb
    ports:
      - "8086:8086"
    environment:
      - INFLUXDB_DB=testdb
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=password

  influxdb-test:
    build: .
    depends_on:
      - influxdb
    environment:
      - INFLUXDB_URL=http://influxdb:8086  # 关键：使用服务名
    volumes:
      - ./logs:/app/logs
EOF
    echo "----------------------------------------"
}

# 显示网络测试命令
show_test_commands() {
    echo ""
    log_info "网络连通性测试命令:"
    echo "----------------------------------------"
    echo "1. DNS解析测试:"
    echo "   docker-compose run --rm influxdb-test nslookup influxdb"
    echo ""
    echo "2. 端口连接测试:"
    echo "   docker-compose run --rm influxdb-test nc -z influxdb 8086"
    echo ""
    echo "3. HTTP连接测试:"
    echo "   docker-compose run --rm influxdb-test curl -f http://influxdb:8086/health"
    echo ""
    echo "4. 运行InfluxDB测试:"
    echo "   docker-compose run --rm influxdb-test ./influxdb-test config-docker-network.yml"
    echo "----------------------------------------"
}

# 显示故障排除指南
show_troubleshooting() {
    echo ""
    log_info "常见问题和解决方案:"
    echo "----------------------------------------"
    echo "问题1: Connection Refused"
    echo "  原因: 使用了localhost而不是服务名"
    echo "  解决: 将localhost改为influxdb"
    echo ""
    echo "问题2: DNS解析失败"
    echo "  原因: 容器不在同一网络"
    echo "  解决: 检查docker-compose.yml配置"
    echo ""
    echo "问题3: 服务未就绪"
    echo "  原因: InfluxDB启动需要时间"
    echo "  解决: 增加重试次数和超时时间"
    echo "----------------------------------------"
}

# 显示网络架构图
show_network_diagram() {
    echo ""
    log_info "Docker网络架构:"
    echo "----------------------------------------"
    cat << 'EOF'
┌─────────────────────────────────────┐
│        Docker Host                  │
│                                     │
│  ┌─────────────────────────────────┐│
│  │     influxdb_test_network       ││
│  │                                 ││
│  │  ┌─────────────┐ ┌─────────────┐││
│  │  │  influxdb   │ │influxdb-test│││
│  │  │   :8086     │ │             │││
│  │  │             │ │             │││
│  │  └─────────────┘ └─────────────┘││
│  │         │              │        ││
│  │         └──────────────┘        ││
│  │       http://influxdb:8086      ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
EOF
    echo "----------------------------------------"
}

# 显示配置文件示例
show_config_example() {
    echo ""
    log_info "Docker网络配置文件示例:"
    echo "----------------------------------------"
    echo "文件: config-docker-network.yml"
    echo ""
    cat << 'EOF'
influxdb:
  url: "http://influxdb:8086"               # 使用Docker服务名
  token: "your-token-here"
  organization: "mdc-org"
  bucket: "device-data"
  timeout: 15s                              # 较长超时适应容器启动
  retry_count: 5                            # 更多重试次数
  retry_delay: 3s                           # 较长重试延迟

logging:
  enable_file: true
  log_dir: "./logs"
  log_file: "influxdb-docker-network-test.log"
  level: "info"
  format: "text"
EOF
    echo "----------------------------------------"
}

# 主函数
main() {
    check_docker
    
    echo ""
    log_info "Docker容器间InfluxDB通信完整指南"
    
    show_network_config
    show_compose_config
    show_config_example
    show_test_commands
    show_network_diagram
    show_troubleshooting
    
    echo ""
    log_info "快速开始:"
    echo "1. 确保配置文件使用服务名: http://influxdb:8086"
    echo "2. 运行: docker-compose up -d influxdb"
    echo "3. 测试: docker-compose run --rm influxdb-test ./influxdb-test config-docker-network.yml"
    echo ""
    log_info "详细测试脚本: ./test_docker_network.sh"
    echo ""
}

# 执行主函数
main "$@"
