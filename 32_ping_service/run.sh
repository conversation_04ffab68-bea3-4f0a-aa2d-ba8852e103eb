#!/bin/bash

# 设备网络监控服务启动脚本
#
# 功能说明：
# 本脚本用于启动设备网络监控服务，包括环境检查、依赖验证、
# 服务启动和状态监控等功能
#
# 使用方法：
# ./run.sh                    # 使用默认配置启动
# ./run.sh config.yml         # 使用指定配置启动
# ./run.sh -h                 # 显示帮助信息
#
# 环境要求：
# - Go 1.21+
# - 网络连通性
# - 12_server_api服务运行中
#
# <AUTHOR>
# @version 1.0.0
# @since 2025-07-02

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="设备网络监控服务"
SERVICE_NAME="ping_service"
DEFAULT_CONFIG="config.yml"
LOG_DIR="logs"
PID_FILE="$LOG_DIR/${SERVICE_NAME}.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
${PROJECT_NAME} 启动脚本

使用方法:
    $0 [配置文件]           启动服务
    $0 -h, --help          显示此帮助信息
    $0 -v, --version       显示版本信息
    $0 -s, --status        检查服务状态
    $0 -k, --kill          停止服务

参数说明:
    配置文件               YAML格式的配置文件路径 (默认: $DEFAULT_CONFIG)

示例:
    $0                     # 使用默认配置启动
    $0 config.yml          # 使用指定配置启动
    $0 prod_config.yml     # 使用生产环境配置启动

环境要求:
    - Go 1.21+
    - 12_server_api服务运行中 (http://localhost:9005)
    - 网络连通性

日志文件:
    - 服务日志: $LOG_DIR/${SERVICE_NAME}.log
    - 错误日志: $LOG_DIR/${SERVICE_NAME}_error.log
    - PID文件: $PID_FILE

EOF
}

# 显示版本信息
show_version() {
    echo "${PROJECT_NAME} v1.0.0"
    echo "构建时间: $(date)"
    echo "Go版本: $(go version 2>/dev/null || echo '未安装')"
}

# 检查依赖
check_dependencies() {
    log_info "检查运行环境..."

    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go未安装或不在PATH中"
        log_error "请安装Go 1.21+: https://golang.org/dl/"
        exit 1
    fi

    # 检查Go版本
    GO_VERSION=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    MAJOR_VERSION=$(echo $GO_VERSION | cut -d. -f1)
    MINOR_VERSION=$(echo $GO_VERSION | cut -d. -f2)
    
    if [ "$MAJOR_VERSION" -lt 1 ] || ([ "$MAJOR_VERSION" -eq 1 ] && [ "$MINOR_VERSION" -lt 21 ]); then
        log_error "Go版本过低: $GO_VERSION，需要1.21+"
        exit 1
    fi

    log_info "✅ Go环境检查通过: $GO_VERSION"

    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi

    log_info "✅ 配置文件检查通过: $CONFIG_FILE"
}

# 检查API服务
check_api_services() {
    log_info "检查API服务连通性..."

    # 从配置文件中提取API地址（简单解析）
    DEVICE_API_URL=$(grep -A 5 "device_api:" "$CONFIG_FILE" | grep "url:" | sed 's/.*url: *"\([^"]*\)".*/\1/' | head -1)
    COLLECT_API_URL=$(grep -A 5 "collect_api:" "$CONFIG_FILE" | grep "url:" | sed 's/.*url: *"\([^"]*\)".*/\1/' | head -1)

    if [ -z "$DEVICE_API_URL" ]; then
        log_warn "⚠️  无法从配置文件解析设备API地址"
    else
        log_info "测试设备API连接: $DEVICE_API_URL"
        if curl -s --connect-timeout 5 "$DEVICE_API_URL" > /dev/null 2>&1; then
            log_info "✅ 设备API连接正常"
        else
            log_warn "⚠️  设备API连接失败，请检查12_server_api服务"
            log_warn "   启动命令: cd ../12_server_api && ./run.sh"
        fi
    fi

    if [ -z "$COLLECT_API_URL" ]; then
        log_warn "⚠️  无法从配置文件解析采集API地址"
    else
        log_info "测试采集API连接: $COLLECT_API_URL"
        # 采集API可能需要POST请求，这里只测试连通性
        if curl -s --connect-timeout 5 "${COLLECT_API_URL%/api/collect}" > /dev/null 2>&1; then
            log_info "✅ 采集API连接正常"
        else
            log_warn "⚠️  采集API连接失败，请检查12_server_api服务"
        fi
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p "$LOG_DIR"
    
    log_info "✅ 目录创建完成"
}

# 检查服务状态
check_service_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_info "✅ 服务正在运行 (PID: $PID)"
            return 0
        else
            log_warn "⚠️  PID文件存在但进程不存在，清理PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    log_info "❌ 服务未运行"
    return 1
}

# 停止服务
stop_service() {
    log_info "停止${PROJECT_NAME}..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_info "发送停止信号到进程 $PID"
            kill -TERM "$PID"
            
            # 等待进程退出
            for i in {1..10}; do
                if ! ps -p "$PID" > /dev/null 2>&1; then
                    log_info "✅ 服务已停止"
                    rm -f "$PID_FILE"
                    return 0
                fi
                sleep 1
            done
            
            # 强制杀死进程
            log_warn "强制停止进程 $PID"
            kill -KILL "$PID" 2>/dev/null || true
            rm -f "$PID_FILE"
            log_info "✅ 服务已强制停止"
        else
            log_warn "⚠️  PID文件存在但进程不存在"
            rm -f "$PID_FILE"
        fi
    else
        log_info "❌ 服务未运行"
    fi
}

# 启动服务
start_service() {
    log_info "启动${PROJECT_NAME}..."
    
    # 检查是否已经运行
    if check_service_status > /dev/null 2>&1; then
        log_error "服务已在运行中"
        exit 1
    fi
    
    # 设置Go环境变量
    export GOROOT=/usr/local/go
    export PATH=/usr/local/go/bin:$PATH
    export GOPROXY=direct
    export GOSUMDB=off

    # 构建项目
    log_info "构建项目..."
    if ! go build -o "$SERVICE_NAME" .; then
        log_error "项目构建失败"
        log_error "请检查Go环境和依赖"
        log_error "手动构建命令: export GOROOT=/usr/local/go && export PATH=/usr/local/go/bin:\$PATH && go build -o $SERVICE_NAME ."
        exit 1
    fi
    log_info "✅ 项目构建完成"
    
    # 启动服务
    log_info "启动服务进程..."

    # 检查可执行文件是否存在
    if [ ! -f "./$SERVICE_NAME" ]; then
        log_error "可执行文件不存在: ./$SERVICE_NAME"
        exit 1
    fi

    # 检查可执行文件权限
    if [ ! -x "./$SERVICE_NAME" ]; then
        log_info "设置可执行权限..."
        chmod +x "./$SERVICE_NAME"
    fi

    # 启动服务并捕获输出
    nohup "./$SERVICE_NAME" -config "$CONFIG_FILE" > "$LOG_DIR/${SERVICE_NAME}_output.log" 2>&1 &
    SERVICE_PID=$!

    # 保存PID
    echo "$SERVICE_PID" > "$PID_FILE"

    # 等待服务启动
    sleep 3

    if ps -p "$SERVICE_PID" > /dev/null 2>&1; then
        log_info "✅ ${PROJECT_NAME}启动成功 (PID: $SERVICE_PID)"
        log_info "📄 日志文件: $LOG_DIR/${SERVICE_NAME}.log"
        log_info "📊 输出日志: $LOG_DIR/${SERVICE_NAME}_output.log"
        log_info ""
        log_info "监控命令:"
        log_info "  tail -f $LOG_DIR/${SERVICE_NAME}.log          # 查看服务日志"
        log_info "  tail -f $LOG_DIR/${SERVICE_NAME}_output.log   # 查看输出日志"
        log_info "  $0 -s                                         # 检查服务状态"
        log_info "  $0 -k                                         # 停止服务"
    else
        log_error "服务启动失败"
        log_error ""
        log_error "🔍 故障排查:"
        log_error "   1. 查看输出日志: cat $LOG_DIR/${SERVICE_NAME}_output.log"
        log_error "   2. 查看服务日志: cat $LOG_DIR/${SERVICE_NAME}.log"
        log_error "   3. 手动启动测试: ./$SERVICE_NAME -config $CONFIG_FILE"
        log_error "   4. 检查配置文件: cat $CONFIG_FILE"
        log_error ""

        # 显示输出日志的前几行
        if [ -f "$LOG_DIR/${SERVICE_NAME}_output.log" ]; then
            log_error "最近的输出日志:"
            tail -10 "$LOG_DIR/${SERVICE_NAME}_output.log" | sed 's/^/   /'
        fi

        rm -f "$PID_FILE"
        exit 1
    fi
}

# 主函数
main() {
    cd "$SCRIPT_DIR"
    
    # 解析命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--version)
            show_version
            exit 0
            ;;
        -s|--status)
            check_service_status
            exit $?
            ;;
        -k|--kill)
            stop_service
            exit 0
            ;;
        "")
            CONFIG_FILE="$DEFAULT_CONFIG"
            ;;
        *)
            CONFIG_FILE="$1"
            ;;
    esac
    
    # 显示启动信息
    echo "=================================================="
    echo "🚀 ${PROJECT_NAME}"
    echo "📁 工作目录: $SCRIPT_DIR"
    echo "⚙️  配置文件: $CONFIG_FILE"
    echo "📅 启动时间: $(date)"
    echo "=================================================="
    echo ""
    
    # 执行启动流程
    create_directories
    check_dependencies
    check_api_services
    start_service
}

# 信号处理
trap 'log_info "接收到中断信号，正在清理..."; exit 130' INT TERM

# 执行主函数
main "$@"
