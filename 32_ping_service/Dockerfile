# 设备网络监控服务 Dockerfile
#
# 功能说明：
# 本Dockerfile用于构建设备网络监控服务的容器镜像，采用多阶段构建
# 优化镜像大小，包含完整的运行环境和必要的网络工具
#
# 构建特性：
# - 多阶段构建：减小最终镜像大小
# - Alpine Linux：轻量级基础镜像
# - 网络工具：包含ping、curl等网络诊断工具
# - 非root用户：提高安全性
# - 健康检查：容器健康状态监控
#
# 构建命令：
# docker build -t ping_service:1.0.0 .
#
# 运行命令：
# docker run -d --name ping_service \
#   -v $(pwd)/config.yml:/app/config.yml \
#   -v $(pwd)/logs:/app/logs \
#   ping_service:1.0.0
#
# <AUTHOR>
# @version 1.0.0
# @since 2025-07-02

# ================================
# 第一阶段：构建阶段
# ================================
FROM golang:1.21-alpine AS builder

# 构建参数
ARG VERSION=1.0.0
ARG BUILD_DATE
ARG VCS_REF

# 设置工作目录
WORKDIR /build

# 安装构建依赖
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

# 复制go模块文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用程序
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-w -s -X main.Version=${VERSION} -X main.BuildDate=${BUILD_DATE} -X main.VCSRef=${VCS_REF}" \
    -a -installsuffix cgo \
    -o ping_service .

# ================================
# 第二阶段：运行阶段
# ================================
FROM alpine:3.18

# 镜像元数据
LABEL maintainer="系统开发团队" \
      version="${VERSION}" \
      description="设备网络监控服务" \
      build-date="${BUILD_DATE}" \
      vcs-ref="${VCS_REF}"

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    iputils \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用用户
RUN addgroup -g 1000 appgroup && \
    adduser -D -u 1000 -G appgroup appuser

# 设置工作目录
WORKDIR /app

# 创建必要目录
RUN mkdir -p /app/logs /app/data && \
    chown -R appuser:appgroup /app

# 从构建阶段复制二进制文件
COPY --from=builder /build/ping_service /app/ping_service

# 复制配置文件模板
COPY config.yml /app/config.yml.template

# 复制启动脚本
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# 设置文件权限
RUN chown -R appuser:appgroup /app && \
    chmod +x /app/ping_service

# 切换到非root用户
USER appuser

# 暴露端口（如果需要）
# EXPOSE 8080

# 设置环境变量
ENV PING_CONFIG_FILE=/app/config.yml
ENV PING_LOG_LEVEL=info
ENV PING_LOG_FORMAT=json

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 数据卷
VOLUME ["/app/logs", "/app/data"]

# 启动命令
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["/app/ping_service", "-config", "/app/config.yml"]
