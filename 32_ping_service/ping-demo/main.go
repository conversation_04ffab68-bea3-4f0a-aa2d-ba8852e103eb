package main

import (
	"fmt"
	"os/exec"
	"runtime"
	"time"
)

func ping(host string) {
	// 根据操作系统选择 ping 命令参数
	cmd := "ping"
	var args []string
	if runtime.GOOS == "windows" {
		args = []string{"-n", "1", host} // Windows 采用 -n 1 每次只 ping 一次
	} else {
		args = []string{"-c", "1", host} // Unix-like 系统采用 -c 1 每次只 ping 一次
	}

	// 执行 ping 命令
	out, err := exec.Command(cmd, args...).Output()
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	// 打印 ping 输出
	fmt.Println(string(out))
}

func main() {
	host := "**************" // 你可以替换为任何有效的主机
	for {
		ping(host)
		time.Sleep(1 * time.Second) // 每次 ping 之间等待 1 秒
	}
}
