#!/bin/bash

# 设备网络监控服务Docker构建脚本
#
# 功能说明：
# 本脚本用于构建设备网络监控服务的Docker镜像，包括多阶段构建、
# 镜像优化、版本管理等功能
#
# 使用方法：
# ./docker_build_ping_service.sh              # 构建默认版本
# ./docker_build_ping_service.sh 1.0.1        # 构建指定版本
# ./docker_build_ping_service.sh --push       # 构建并推送到仓库
#
# 构建特性：
# - 多阶段构建减小镜像大小
# - 自动版本标签管理
# - 构建缓存优化
# - 安全扫描集成
#
# <AUTHOR>
# @version 1.0.0
# @since 2025-07-02

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="ping_service"
IMAGE_NAME="ping_service"
DEFAULT_VERSION="1.0.0"
DOCKERFILE="Dockerfile"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
设备网络监控服务 Docker构建脚本

使用方法:
    $0 [版本号]             构建指定版本的镜像
    $0 --push [版本号]      构建并推送镜像到仓库
    $0 --clean             清理构建缓存和临时文件
    $0 -h, --help          显示此帮助信息

参数说明:
    版本号                  镜像版本标签 (默认: $DEFAULT_VERSION)
    --push                 构建完成后推送到Docker仓库
    --clean                清理Docker构建缓存

示例:
    $0                     # 构建默认版本 ($DEFAULT_VERSION)
    $0 1.0.1               # 构建版本 1.0.1
    $0 --push 1.0.1        # 构建版本 1.0.1 并推送
    $0 --clean             # 清理构建缓存

构建输出:
    镜像名称: $IMAGE_NAME:版本号
    镜像名称: $IMAGE_NAME:latest (最新版本)

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查构建环境..."

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        log_error "请安装Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi

    # 检查Docker服务
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        log_error "请启动Docker服务"
        exit 1
    fi

    log_info "✅ Docker环境检查通过"

    # 检查Dockerfile
    if [ ! -f "$DOCKERFILE" ]; then
        log_error "Dockerfile不存在: $DOCKERFILE"
        exit 1
    fi

    log_info "✅ Dockerfile检查通过"
}

# 清理构建缓存
clean_build_cache() {
    log_info "清理Docker构建缓存..."
    
    # 清理构建缓存
    docker builder prune -f || true
    
    # 清理未使用的镜像
    docker image prune -f || true
    
    # 清理项目相关的镜像（可选）
    if docker images | grep -q "$IMAGE_NAME"; then
        log_warn "发现项目镜像，是否删除? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            docker images | grep "$IMAGE_NAME" | awk '{print $3}' | xargs docker rmi -f || true
            log_info "✅ 项目镜像已删除"
        fi
    fi
    
    log_info "✅ 构建缓存清理完成"
}

# 构建镜像
build_image() {
    local version="$1"
    local push_flag="$2"
    
    log_info "开始构建Docker镜像..."
    log_info "项目: $PROJECT_NAME"
    log_info "版本: $version"
    log_info "镜像: $IMAGE_NAME:$version"
    
    # 构建参数
    BUILD_ARGS=(
        --build-arg "VERSION=$version"
        --build-arg "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')"
        --build-arg "VCS_REF=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
        --tag "$IMAGE_NAME:$version"
        --tag "$IMAGE_NAME:latest"
    )
    
    # 执行构建
    log_info "执行Docker构建..."
    if docker build "${BUILD_ARGS[@]}" .; then
        log_info "✅ Docker镜像构建成功"
    else
        log_error "❌ Docker镜像构建失败"
        exit 1
    fi
    
    # 显示镜像信息
    log_info "📊 镜像信息:"
    docker images | grep "$IMAGE_NAME" | head -2
    
    # 显示镜像大小
    IMAGE_SIZE=$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep "$IMAGE_NAME:$version" | awk '{print $2}')
    log_info "📦 镜像大小: $IMAGE_SIZE"
    
    # 推送镜像（如果需要）
    if [ "$push_flag" = "true" ]; then
        push_image "$version"
    fi
}

# 推送镜像
push_image() {
    local version="$1"
    
    log_info "推送Docker镜像到仓库..."
    
    # 推送版本标签
    log_info "推送 $IMAGE_NAME:$version"
    if docker push "$IMAGE_NAME:$version"; then
        log_info "✅ 版本镜像推送成功"
    else
        log_error "❌ 版本镜像推送失败"
        exit 1
    fi
    
    # 推送latest标签
    log_info "推送 $IMAGE_NAME:latest"
    if docker push "$IMAGE_NAME:latest"; then
        log_info "✅ Latest镜像推送成功"
    else
        log_error "❌ Latest镜像推送失败"
        exit 1
    fi
    
    log_info "🚀 所有镜像推送完成"
}

# 验证镜像
verify_image() {
    local version="$1"
    
    log_info "验证Docker镜像..."
    
    # 检查镜像是否存在
    if ! docker images | grep -q "$IMAGE_NAME.*$version"; then
        log_error "镜像不存在: $IMAGE_NAME:$version"
        exit 1
    fi
    
    # 运行基本测试
    log_info "运行镜像基本测试..."
    if docker run --rm "$IMAGE_NAME:$version" --help > /dev/null 2>&1; then
        log_info "✅ 镜像基本功能正常"
    else
        log_warn "⚠️  镜像基本测试失败，可能需要检查"
    fi
    
    # 安全扫描（如果有工具）
    if command -v trivy &> /dev/null; then
        log_info "执行安全扫描..."
        trivy image "$IMAGE_NAME:$version" || log_warn "⚠️  安全扫描发现问题"
    fi
}

# 主函数
main() {
    cd "$SCRIPT_DIR"
    
    local version="$DEFAULT_VERSION"
    local push_flag="false"
    local clean_flag="false"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --push)
                push_flag="true"
                shift
                ;;
            --clean)
                clean_flag="true"
                shift
                ;;
            *)
                if [[ "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    version="$1"
                else
                    log_error "无效的版本号格式: $1"
                    log_error "版本号格式应为: x.y.z (例如: 1.0.0)"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 显示构建信息
    echo "=================================================="
    echo "🐳 设备网络监控服务 Docker构建"
    echo "📁 工作目录: $SCRIPT_DIR"
    echo "🏷️  镜像版本: $version"
    echo "📅 构建时间: $(date)"
    echo "=================================================="
    echo ""
    
    # 执行操作
    if [ "$clean_flag" = "true" ]; then
        clean_build_cache
        exit 0
    fi
    
    check_dependencies
    build_image "$version" "$push_flag"
    verify_image "$version"
    
    # 显示完成信息
    echo ""
    echo "=================================================="
    echo "✅ Docker镜像构建完成"
    echo "🏷️  镜像标签: $IMAGE_NAME:$version"
    echo "🏷️  最新标签: $IMAGE_NAME:latest"
    if [ "$push_flag" = "true" ]; then
        echo "🚀 镜像已推送到仓库"
    fi
    echo ""
    echo "使用方法:"
    echo "  docker run --rm $IMAGE_NAME:$version --help"
    echo "  docker run -v \$(pwd)/config.yml:/app/config.yml $IMAGE_NAME:$version"
    echo "=================================================="
}

# 信号处理
trap 'log_info "接收到中断信号，正在清理..."; exit 130' INT TERM

# 执行主函数
main "$@"
