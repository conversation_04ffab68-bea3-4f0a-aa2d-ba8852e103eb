/**
 * Ping监控模块
 *
 * 功能概述：
 * 本模块实现对设备IP地址的ping监控功能，包括ping执行、
 * 结果解析、状态统计和异常检测等核心功能
 *
 * 主要功能：
 * - Ping执行：执行系统ping命令监控设备网络连通性
 * - 结果解析：解析ping命令输出，提取响应时间和丢包率
 * - 状态统计：统计ping成功率、平均响应时间等指标
 * - 异常检测：检测网络异常和设备离线状态
 * - 跨平台支持：支持Windows、Linux、macOS等操作系统
 *
 * 技术特性：
 * - 系统调用：使用系统原生ping命令确保准确性
 * - 正则解析：使用正则表达式解析ping输出
 * - 超时控制：支持ping超时和重试机制
 * - 并发安全：支持多goroutine并发ping
 * - 跨平台：自动适配不同操作系统的ping命令
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-02
 */
package main

import (
	"context"
	"fmt"
	"os/exec"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"shared/logger"
)

/**
 * Ping结果结构体
 *
 * 包含单次ping操作的完整结果信息
 */
type PingResult struct {
	/** 目标IP地址 */
	IPAddress string

	/** Ping是否成功 */
	Success bool

	/** 响应时间（毫秒） */
	ResponseTime float64

	/** 丢包率（百分比，0-100） */
	PacketLoss float64

	/** 发送的包数量 */
	PacketsSent int

	/** 接收的包数量 */
	PacketsReceived int

	/** 错误信息 */
	ErrorMessage string

	/** 执行时间戳 */
	Timestamp time.Time

	/** 执行耗时 */
	Duration time.Duration
}

/**
 * Ping监控器结构体
 *
 * 封装ping监控的核心功能
 */
type PingMonitor struct {
	/** 配置信息 */
	config *Config

	/** ping命令参数缓存 */
	pingCommand string
	pingArgs    []string
}

/**
 * NewPingMonitor 创建新的ping监控器
 *
 * @param config 配置信息
 * @return *PingMonitor ping监控器实例
 */
func NewPingMonitor(config *Config) *PingMonitor {
	monitor := &PingMonitor{
		config: config,
	}

	// 根据操作系统初始化ping命令参数
	monitor.initPingCommand()

	return monitor
}

/**
 * initPingCommand 初始化ping命令参数
 *
 * 功能：根据不同操作系统设置合适的ping命令参数
 */
func (pm *PingMonitor) initPingCommand() {
	pm.pingCommand = "ping"

	// 根据操作系统设置不同的参数
	switch runtime.GOOS {
	case "windows":
		// Windows: ping -n count -l size target
		pm.pingArgs = []string{
			"-n", strconv.Itoa(pm.config.Ping.Count),
			"-l", strconv.Itoa(pm.config.Ping.PacketSize),
			"-w", strconv.Itoa(int(pm.config.Ping.Timeout.Milliseconds())),
		}
	case "darwin", "linux":
		// Unix-like: ping -c count -s size -W timeout target
		pm.pingArgs = []string{
			"-c", strconv.Itoa(pm.config.Ping.Count),
			"-s", strconv.Itoa(pm.config.Ping.PacketSize),
			"-W", strconv.Itoa(int(pm.config.Ping.Timeout.Milliseconds())),
		}
	default:
		// 默认使用Linux参数
		pm.pingArgs = []string{
			"-c", strconv.Itoa(pm.config.Ping.Count),
			"-W", strconv.Itoa(int(pm.config.Ping.Timeout.Milliseconds())),
		}
	}

	logger.Debugf("🔧 初始化ping命令: %s %v", pm.pingCommand, pm.pingArgs)
}

/**
 * Ping 执行ping操作
 *
 * 功能：对指定IP地址执行ping操作，并解析结果
 *
 * @param ipAddress 目标IP地址
 * @return PingResult ping结果
 */
func (pm *PingMonitor) Ping(ipAddress string) PingResult {
	startTime := time.Now()
	result := PingResult{
		IPAddress:   ipAddress,
		Timestamp:   startTime,
		PacketsSent: pm.config.Ping.Count,
	}

	// 构建完整的ping命令参数
	args := append(pm.pingArgs, ipAddress)

	logger.Debugf("🏓 执行ping: %s %v", pm.pingCommand, args)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 
		pm.config.Ping.Timeout+5*time.Second) // 额外5秒缓冲时间
	defer cancel()

	// 执行ping命令
	cmd := exec.CommandContext(ctx, pm.pingCommand, args...)
	output, err := cmd.CombinedOutput()

	result.Duration = time.Since(startTime)

	if err != nil {
		result.Success = false
		result.ErrorMessage = fmt.Sprintf("ping命令执行失败: %v", err)
		result.PacketLoss = 100.0
		logger.Debugf("❌ Ping失败: %s - %s", ipAddress, result.ErrorMessage)
		return result
	}

	// 解析ping输出
	pm.parsePingOutput(string(output), &result)

	if result.Success {
		logger.Debugf("✅ Ping成功: %s - 响应时间: %.2fms, 丢包率: %.1f%%",
			ipAddress, result.ResponseTime, result.PacketLoss)
	} else {
		logger.Debugf("❌ Ping失败: %s - 丢包率: %.1f%%",
			ipAddress, result.PacketLoss)
	}

	return result
}

/**
 * parsePingOutput 解析ping命令输出
 *
 * 功能：解析不同操作系统的ping命令输出，提取关键指标
 *
 * @param output ping命令输出
 * @param result ping结果指针
 */
func (pm *PingMonitor) parsePingOutput(output string, result *PingResult) {
	switch runtime.GOOS {
	case "windows":
		pm.parseWindowsPingOutput(output, result)
	case "darwin", "linux":
		pm.parseUnixPingOutput(output, result)
	default:
		pm.parseUnixPingOutput(output, result)
	}
}

/**
 * parseWindowsPingOutput 解析Windows ping输出
 *
 * @param output ping命令输出
 * @param result ping结果指针
 */
func (pm *PingMonitor) parseWindowsPingOutput(output string, result *PingResult) {
	lines := strings.Split(output, "\n")

	// 解析响应时间
	timeRegex := regexp.MustCompile(`时间[<=](\d+)ms|time[<=](\d+)ms`)
	var responseTimes []float64

	for _, line := range lines {
		if matches := timeRegex.FindStringSubmatch(line); matches != nil {
			timeStr := matches[1]
			if timeStr == "" {
				timeStr = matches[2]
			}
			if timeMs, err := strconv.ParseFloat(timeStr, 64); err == nil {
				responseTimes = append(responseTimes, timeMs)
			}
		}
	}

	// 解析统计信息
	statsRegex := regexp.MustCompile(`已发送 = (\d+)，已接收 = (\d+)，丢失 = (\d+)|Sent = (\d+), Received = (\d+), Lost = (\d+)`)
	for _, line := range lines {
		if matches := statsRegex.FindStringSubmatch(line); matches != nil {
			var sent, received int
			if matches[1] != "" {
				// 中文输出
				sent, _ = strconv.Atoi(matches[1])
				received, _ = strconv.Atoi(matches[2])
			} else {
				// 英文输出
				sent, _ = strconv.Atoi(matches[4])
				received, _ = strconv.Atoi(matches[5])
			}

			result.PacketsSent = sent
			result.PacketsReceived = received
			if sent > 0 {
				result.PacketLoss = float64(sent-received) / float64(sent) * 100
			}
			break
		}
	}

	// 计算平均响应时间
	if len(responseTimes) > 0 {
		var total float64
		for _, time := range responseTimes {
			total += time
		}
		result.ResponseTime = total / float64(len(responseTimes))
		result.Success = true
	} else {
		result.Success = false
		result.PacketLoss = 100.0
	}
}

/**
 * parseUnixPingOutput 解析Unix-like系统ping输出
 *
 * @param output ping命令输出
 * @param result ping结果指针
 */
func (pm *PingMonitor) parseUnixPingOutput(output string, result *PingResult) {
	lines := strings.Split(output, "\n")

	// 解析响应时间
	timeRegex := regexp.MustCompile(`time=([0-9.]+)\s*ms`)
	var responseTimes []float64

	for _, line := range lines {
		if matches := timeRegex.FindStringSubmatch(line); matches != nil {
			if timeMs, err := strconv.ParseFloat(matches[1], 64); err == nil {
				responseTimes = append(responseTimes, timeMs)
			}
		}
	}

	// 解析统计信息
	statsRegex := regexp.MustCompile(`(\d+) packets transmitted, (\d+) (?:packets )?received, (?:.*?(\d+(?:\.\d+)?)% packet loss)?`)
	for _, line := range lines {
		if matches := statsRegex.FindStringSubmatch(line); matches != nil {
			sent, _ := strconv.Atoi(matches[1])
			received, _ := strconv.Atoi(matches[2])

			result.PacketsSent = sent
			result.PacketsReceived = received

			if matches[3] != "" {
				result.PacketLoss, _ = strconv.ParseFloat(matches[3], 64)
			} else if sent > 0 {
				result.PacketLoss = float64(sent-received) / float64(sent) * 100
			}
			break
		}
	}

	// 计算平均响应时间
	if len(responseTimes) > 0 {
		var total float64
		for _, time := range responseTimes {
			total += time
		}
		result.ResponseTime = total / float64(len(responseTimes))
		result.Success = true
	} else {
		result.Success = false
		if result.PacketLoss == 0 {
			result.PacketLoss = 100.0
		}
	}
}

/**
 * PingWithRetry 带重试的ping操作
 *
 * 功能：执行ping操作，失败时自动重试
 *
 * @param ipAddress 目标IP地址
 * @return PingResult 最终ping结果
 */
func (pm *PingMonitor) PingWithRetry(ipAddress string) PingResult {
	var lastResult PingResult

	for attempt := 0; attempt <= pm.config.Ping.RetryCount; attempt++ {
		if attempt > 0 {
			logger.Debugf("🔄 Ping重试 %d/%d: %s", attempt, pm.config.Ping.RetryCount, ipAddress)
			time.Sleep(pm.config.Ping.RetryInterval)
		}

		result := pm.Ping(ipAddress)
		lastResult = result

		if result.Success {
			return result
		}
	}

	logger.Debugf("❌ Ping最终失败: %s (已重试 %d 次)", ipAddress, pm.config.Ping.RetryCount)
	return lastResult
}

/**
 * IsValidIP 验证IP地址格式
 *
 * 功能：验证IP地址格式是否正确
 *
 * @param ip IP地址字符串
 * @return bool 是否为有效IP地址
 */
func (pm *PingMonitor) IsValidIP(ip string) bool {
	// 简单的IP地址格式验证
	ipRegex := regexp.MustCompile(`^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)
	return ipRegex.MatchString(ip)
}
