/**
 * 配置管理模块
 *
 * 功能概述：
 * 本模块负责加载和管理ping服务的配置信息，包括设备API配置、
 * 数据采集API配置、ping参数配置和日志配置等
 *
 * 主要功能：
 * - 配置文件加载：支持YAML格式的配置文件
 * - 配置验证：验证配置参数的有效性
 * - 默认值处理：为未配置的参数提供合理的默认值
 * - 环境变量支持：支持通过环境变量覆盖配置
 * - 配置热更新：支持运行时配置的动态更新
 *
 * 配置结构：
 * - DeviceAPI：设备列表获取API配置
 * - CollectAPI：数据采集API配置
 * - Ping：ping监控参数配置
 * - Logging：日志系统配置
 * - Global：全局运行参数配置
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-02
 */
package main

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v2"
)

/**
 * 主配置结构体
 *
 * 包含ping服务的所有配置信息，用于控制服务的运行行为
 */
type Config struct {
	/** 设备API配置 - 用于获取设备列表 */
	DeviceAPI DeviceAPIConfig `yaml:"device_api"`

	/** 数据采集API配置 - 用于提交ping结果 */
	CollectAPI CollectAPIConfig `yaml:"collect_api"`

	/** Ping监控配置 */
	Ping PingConfig `yaml:"ping"`

	/** 全局配置 */
	Global GlobalConfig `yaml:"global"`

	/** 日志配置 */
	Logging LoggingConfig `yaml:"logging"`
}

/**
 * 设备API配置结构体
 *
 * 配置从哪里获取设备列表信息
 */
type DeviceAPIConfig struct {
	/** API服务地址 */
	URL string `yaml:"url"`

	/** 请求超时时间 */
	Timeout time.Duration `yaml:"timeout"`

	/** 失败重试次数 */
	RetryCount int `yaml:"retry_count"`

	/** 设备配置刷新间隔 */
	RefreshInterval time.Duration `yaml:"refresh_interval"`
}

/**
 * 数据采集API配置结构体
 *
 * 配置ping结果数据的提交目标
 */
type CollectAPIConfig struct {
	/** 数据采集服务地址 */
	URL string `yaml:"url"`

	/** 请求超时时间 */
	Timeout time.Duration `yaml:"timeout"`

	/** 失败重试次数 */
	RetryCount int `yaml:"retry_count"`

	/** 批量提交大小 */
	BatchSize int `yaml:"batch_size"`

	/** 批量提交间隔 */
	BatchInterval time.Duration `yaml:"batch_interval"`
}

/**
 * Ping监控配置结构体
 *
 * 配置ping监控的各种参数
 */
type PingConfig struct {
	/** Ping间隔时间 */
	Interval time.Duration `yaml:"interval"`

	/** Ping超时时间 */
	Timeout time.Duration `yaml:"timeout"`

	/** 每次ping的包数量 */
	Count int `yaml:"count"`

	/** 包大小（字节） */
	PacketSize int `yaml:"packet_size"`

	/** 并发ping的最大数量 */
	ConcurrentLimit int `yaml:"concurrent_limit"`

	/** 失败重试次数 */
	RetryCount int `yaml:"retry_count"`

	/** 失败重试间隔 */
	RetryInterval time.Duration `yaml:"retry_interval"`
}

/**
 * 全局配置结构体
 *
 * 配置服务的全局运行参数
 */
type GlobalConfig struct {
	/** 运行模式：forever(永久运行) | duration(持续时间) */
	RunMode string `yaml:"run_mode"`

	/** 运行持续时间（run_mode=duration时有效） */
	RunDuration time.Duration `yaml:"run_duration"`

	/** 统计信息输出间隔 */
	StatsInterval time.Duration `yaml:"stats_interval"`

	/** 最大并发数限制 */
	ConcurrentLimit int `yaml:"concurrent_limit"`

	/** 是否启用详细模式 */
	Verbose bool `yaml:"verbose"`
}

/**
 * 日志配置结构体
 *
 * 配置日志系统的各种参数
 */
type LoggingConfig struct {
	/** 日志级别：debug, info, warn, error */
	Level string `yaml:"level"`

	/** 日志格式：text, json */
	Format string `yaml:"format"`

	/** 输出目标：stdout, stderr, 文件路径 */
	Output string `yaml:"output"`

	/** 是否输出设备详细日志 */
	DeviceLogs bool `yaml:"device_logs"`

	/** 日志轮转配置 */
	Rotation LogRotationConfig `yaml:"rotation"`
}

/**
 * 日志轮转配置结构体
 *
 * 配置日志文件的轮转策略
 */
type LogRotationConfig struct {
	/** 是否启用日志轮转 */
	Enabled bool `yaml:"enabled"`

	/** 单个文件最大大小（MB） */
	MaxSize int `yaml:"max_size"`

	/** 文件保留天数 */
	MaxAge int `yaml:"max_age"`

	/** 保留的备份文件数量 */
	MaxBackups int `yaml:"max_backups"`

	/** 是否压缩旧文件 */
	Compress bool `yaml:"compress"`
}

/**
 * 设备信息结构体
 *
 * 从API获取的设备信息
 */
type DeviceInfo struct {
	/** 设备ID */
	ID string `json:"id"`

	/** 设备名称 */
	Name string `json:"name"`

	/** 设备IP地址 */
	IPAddress string `json:"ip_address"`

	/** 设备类型 */
	Type string `json:"type"`

	/** 设备品牌 */
	Brand string `json:"brand"`

	/** 设备型号 */
	Model string `json:"model"`

	/** 设备位置 */
	Location string `json:"location"`

	/** 是否启用 */
	Enabled bool `json:"enabled"`

	/** 最后更新时间 */
	LastUpdated time.Time `json:"last_updated"`
}

/**
 * LoadConfig 加载配置文件
 *
 * 功能：从指定路径加载YAML配置文件，并进行配置验证和默认值设置
 *
 * @param configFile 配置文件路径
 * @return *Config 配置对象
 * @return error 错误信息
 */
func LoadConfig(configFile string) (*Config, error) {
	// 读取配置文件
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	setDefaultValues(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	return &config, nil
}

/**
 * setDefaultValues 设置配置默认值
 *
 * 功能：为未配置的参数设置合理的默认值
 *
 * @param config 配置对象指针
 */
func setDefaultValues(config *Config) {
	// 设备API默认值
	if config.DeviceAPI.Timeout == 0 {
		config.DeviceAPI.Timeout = 10 * time.Second
	}
	if config.DeviceAPI.RetryCount == 0 {
		config.DeviceAPI.RetryCount = 3
	}
	if config.DeviceAPI.RefreshInterval == 0 {
		config.DeviceAPI.RefreshInterval = 60 * time.Second
	}

	// 数据采集API默认值
	if config.CollectAPI.Timeout == 0 {
		config.CollectAPI.Timeout = 5 * time.Second
	}
	if config.CollectAPI.RetryCount == 0 {
		config.CollectAPI.RetryCount = 3
	}
	if config.CollectAPI.BatchSize == 0 {
		config.CollectAPI.BatchSize = 10
	}
	if config.CollectAPI.BatchInterval == 0 {
		config.CollectAPI.BatchInterval = 5 * time.Second
	}

	// Ping配置默认值
	if config.Ping.Interval == 0 {
		config.Ping.Interval = 30 * time.Second
	}
	if config.Ping.Timeout == 0 {
		config.Ping.Timeout = 3 * time.Second
	}
	if config.Ping.Count == 0 {
		config.Ping.Count = 1
	}
	if config.Ping.PacketSize == 0 {
		config.Ping.PacketSize = 32
	}
	if config.Ping.ConcurrentLimit == 0 {
		config.Ping.ConcurrentLimit = 50
	}
	if config.Ping.RetryCount == 0 {
		config.Ping.RetryCount = 2
	}
	if config.Ping.RetryInterval == 0 {
		config.Ping.RetryInterval = 1 * time.Second
	}

	// 全局配置默认值
	if config.Global.RunMode == "" {
		config.Global.RunMode = "forever"
	}
	if config.Global.StatsInterval == 0 {
		config.Global.StatsInterval = 30 * time.Second
	}
	if config.Global.ConcurrentLimit == 0 {
		config.Global.ConcurrentLimit = 100
	}

	// 日志配置默认值
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "json"
	}
	if config.Logging.Output == "" {
		config.Logging.Output = "logs/ping_service.log"
	}
}

/**
 * validateConfig 验证配置参数
 *
 * 功能：验证配置参数的有效性，确保服务能够正常运行
 *
 * @param config 配置对象指针
 * @return error 验证错误信息
 */
func validateConfig(config *Config) error {
	// 验证必需的URL配置
	if config.DeviceAPI.URL == "" {
		return fmt.Errorf("device_api.url 不能为空")
	}
	if config.CollectAPI.URL == "" {
		return fmt.Errorf("collect_api.url 不能为空")
	}

	// 验证运行模式
	if config.Global.RunMode != "forever" && config.Global.RunMode != "duration" {
		return fmt.Errorf("global.run_mode 必须是 'forever' 或 'duration'")
	}

	// 验证日志级别
	validLevels := map[string]bool{"debug": true, "info": true, "warn": true, "error": true}
	if !validLevels[config.Logging.Level] {
		return fmt.Errorf("logging.level 必须是 'debug', 'info', 'warn' 或 'error'")
	}

	// 验证日志格式
	if config.Logging.Format != "text" && config.Logging.Format != "json" {
		return fmt.Errorf("logging.format 必须是 'text' 或 'json'")
	}

	return nil
}
