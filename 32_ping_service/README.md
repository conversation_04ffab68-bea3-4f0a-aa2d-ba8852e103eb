# 设备网络监控服务 (32_ping_service)

## 📋 项目概述

设备网络监控服务是一个专门用于监控制造业设备网络连通性的Go语言服务。该服务从设备管理API获取设备列表，定期ping设备IP地址，监控网络连通性状态，并将ping结果提交到数据采集服务进行存储和分析。

## 🎯 核心功能

### 网络监控
- **设备发现**: 从API动态获取设备列表和IP地址信息
- **连通性检测**: 定期ping设备IP，检测网络连通性
- **状态监控**: 实时监控所有设备的网络状态
- **故障检测**: 及时发现网络故障和设备离线

### 数据采集
- **结果收集**: 收集ping响应时间、丢包率等网络指标
- **批量上报**: 将ping结果批量提交到collect API
- **数据格式**: 标准化的网络监控数据格式
- **实时传输**: 秒级的数据上报和存储

### 性能特性
- **高并发**: 支持数百台设备同时ping监控
- **可配置**: 丰富的配置选项，支持不同监控场景
- **实时性**: 秒级的ping监控和状态上报
- **可观测**: 详细的日志记录和统计信息

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   设备管理API   │    │  Ping监控服务   │    │  数据采集API   │
│ (12_server_api) │◄───┤ (32_ping_service)├───►│ (12_server_api) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   网络设备群   │
                       │  (Ping Target)  │
                       └─────────────────┘
```

### 核心组件
- **PingManager**: 统一管理所有设备ping任务
- **APIClient**: 处理与外部API的通信
- **PingMonitor**: 执行ping操作和结果解析
- **ConfigManager**: 配置文件管理和验证

## 🚀 快速开始

### 环境要求
- Go 1.21+
- 网络连通性
- 12_server_api服务运行中

### 安装运行

1. **克隆项目**
```bash
cd 32_ping_service
```

2. **配置服务**
```bash
# 编辑配置文件
vim config.yml

# 主要配置项：
# - device_api.url: 设备列表API地址
# - collect_api.url: 数据采集API地址
# - ping.interval: ping间隔时间
```

3. **启动服务**
```bash
# 使用启动脚本（推荐）
./run.sh

# 或直接运行
go run . -config config.yml
```

4. **验证运行**
```bash
# 查看日志
tail -f logs/ping_service.log

# 检查服务状态
./run.sh -s
```

## 📊 配置说明

### 主要配置项

```yaml
# 设备API配置
device_api:
  url: "http://localhost:9005/api/machines"
  timeout: 10s
  retry_count: 3

# 数据采集API配置
collect_api:
  url: "http://localhost:9005/api/collect"
  timeout: 5s
  batch_size: 10
  batch_interval: 5s

# Ping监控配置
ping:
  interval: 30s        # ping间隔
  timeout: 3s          # ping超时
  count: 1             # ping包数量
  concurrent_limit: 50 # 并发限制

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "logs/ping_service.log"
```

### 环境适配

**开发环境**:
```yaml
ping:
  interval: 30s      # 较短间隔用于测试
logging:
  level: "debug"     # 详细日志
```

**生产环境**:
```yaml
ping:
  interval: 300s     # 较长间隔减少负载
  concurrent_limit: 200
logging:
  level: "warn"      # 只记录警告和错误
```

## 🐳 Docker部署

### 构建镜像
```bash
# 构建默认版本
./docker_build_ping_service.sh

# 构建指定版本
./docker_build_ping_service.sh 1.0.1

# 构建并推送
./docker_build_ping_service.sh --push 1.0.1
```

### 运行容器
```bash
# 基本运行
docker run -d --name ping_service \
  -v $(pwd)/config.yml:/app/config.yml \
  -v $(pwd)/logs:/app/logs \
  ping_service:1.0.0

# 使用环境变量
docker run -d --name ping_service \
  -e DEVICE_API_URL="http://api-server:9005/api/machines" \
  -e COLLECT_API_URL="http://api-server:9005/api/collect" \
  -e PING_LOG_LEVEL="info" \
  -v $(pwd)/logs:/app/logs \
  ping_service:1.0.0
```

## 📈 监控指标

### 全局统计
- 总设备数量
- 在线/离线设备数
- 总ping次数和成功率
- 平均响应时间

### 设备统计
- 设备ping成功率
- 平均/最小/最大响应时间
- 连续失败次数
- 最后成功/失败时间

### 日志示例
```json
{
  "level": "info",
  "time": "2025-07-02T10:30:00Z",
  "msg": "📊 网络监控统计报告",
  "total_devices": 50,
  "online_devices": 48,
  "offline_devices": 2,
  "success_rate": 96.5,
  "avg_response_time": 12.3
}
```

## 🔧 运维管理

### 服务控制
```bash
# 启动服务
./run.sh

# 检查状态
./run.sh -s

# 停止服务
./run.sh -k

# 查看帮助
./run.sh -h
```

### 日志管理
```bash
# 查看实时日志
tail -f logs/ping_service.log

# 查看错误日志
grep "ERROR" logs/ping_service.log

# 查看统计报告
grep "统计报告" logs/ping_service.log
```

### 故障排查
```bash
# 检查API连接
curl -s http://localhost:9005/api/machines

# 测试ping功能
ping -c 1 设备IP地址

# 查看详细日志
# 设置 logging.level: "debug"
```

## 📝 开发指南

### 项目结构
```
32_ping_service/
├── main.go              # 主程序入口
├── config.go            # 配置管理
├── api_client.go        # API客户端
├── ping.go              # Ping监控
├── manager.go           # 任务管理器
├── config.yml           # 配置文件
├── run.sh               # 启动脚本
├── Dockerfile           # Docker构建
└── README.md            # 项目文档
```

### 扩展开发
1. **添加新的监控指标**: 修改`PingResult`结构体
2. **支持新的协议**: 扩展`PingMonitor`接口
3. **自定义数据格式**: 修改`PingResultData`结构体
4. **添加告警功能**: 在`PingManager`中集成告警逻辑

## 🤝 参考项目

本项目基于 `02_generate_data` 项目的架构模式开发：
- 配置管理模式
- API客户端设计
- 任务管理器架构
- 日志和错误处理

## 📄 许可证

本项目采用企业内部许可证，仅供内部使用。

---

**技术支持**: 系统开发团队  
**更新时间**: 2025-07-02  
**版本**: 1.0.0
