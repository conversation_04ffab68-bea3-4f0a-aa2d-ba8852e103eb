/**
 * Ping管理器模块
 *
 * 功能概述：
 * 本模块实现ping管理器功能，负责创建、管理和监控多个设备的ping任务，
 * 是整个ping服务的核心控制组件，统一管理所有设备的ping监控和数据上报
 *
 * 主要功能：
 * - 设备管理：从API获取设备列表并创建ping任务
 * - 任务调度：统一调度所有设备的ping监控任务
 * - 数据收集：收集所有ping结果并批量提交
 * - 状态监控：实时监控所有设备的网络状态
 * - 统计报告：生成网络连通性统计报告
 * - 异常处理：处理网络异常和设备离线情况
 *
 * 架构设计：
 * - 管理器模式：统一管理多个ping任务
 * - 生产者消费者模式：ping任务生产结果，管理器消费并上报
 * - 观察者模式：监控所有ping任务的状态变化
 * - 策略模式：支持不同的ping策略和上报策略
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-02
 */
package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	"shared/logger"
)

/**
 * 设备ping任务结构体
 *
 * 表示单个设备的ping监控任务
 */
type DevicePingTask struct {
	/** 设备信息 */
	Device DeviceInfo

	/** Ping监控器 */
	Monitor *PingMonitor

	/** 任务上下文 */
	Context context.Context

	/** 取消函数 */
	Cancel context.CancelFunc

	/** 最后ping结果 */
	LastResult PingResult

	/** 最后ping时间 */
	LastPingTime time.Time

	/** ping统计信息 */
	Stats PingStats

	/** 任务状态 */
	Status string // "running", "stopped", "error"
}

/**
 * Ping统计信息结构体
 *
 * 记录设备ping的统计数据
 */
type PingStats struct {
	/** 总ping次数 */
	TotalPings int64

	/** 成功次数 */
	SuccessCount int64

	/** 失败次数 */
	FailureCount int64

	/** 总响应时间 */
	TotalResponseTime float64

	/** 最小响应时间 */
	MinResponseTime float64

	/** 最大响应时间 */
	MaxResponseTime float64

	/** 连续失败次数 */
	ConsecutiveFailures int64

	/** 最后成功时间 */
	LastSuccessTime time.Time

	/** 最后失败时间 */
	LastFailureTime time.Time
}

/**
 * Ping管理器结构体
 *
 * 统一管理所有设备的ping监控任务
 */
type PingManager struct {
	/** 配置信息 */
	config *Config

	/** API客户端 */
	apiClient *APIClient

	/** Ping监控器 */
	pingMonitor *PingMonitor

	/** 设备ping任务列表 */
	tasks map[string]*DevicePingTask

	/** 任务列表读写锁 */
	tasksMutex sync.RWMutex

	/** 全局上下文 */
	ctx context.Context

	/** 全局取消函数 */
	cancel context.CancelFunc

	/** 等待组 */
	wg sync.WaitGroup

	/** 结果通道 */
	resultChan chan PingResultData

	/** 批量结果缓存 */
	resultBuffer []PingResultData

	/** 缓存锁 */
	bufferMutex sync.Mutex

	/** 全局统计信息 */
	globalStats GlobalPingStats

	/** 统计锁 */
	statsMutex sync.RWMutex

	/** 是否正在运行 */
	running bool
}

/**
 * 全局ping统计信息结构体
 */
type GlobalPingStats struct {
	/** 总设备数 */
	TotalDevices int

	/** 在线设备数 */
	OnlineDevices int

	/** 离线设备数 */
	OfflineDevices int

	/** 总ping次数 */
	TotalPings int64

	/** 成功次数 */
	SuccessCount int64

	/** 失败次数 */
	FailureCount int64

	/** 平均响应时间 */
	AverageResponseTime float64

	/** 成功率 */
	SuccessRate float64

	/** 最后更新时间 */
	LastUpdated time.Time
}

/**
 * NewPingManager 创建新的ping管理器
 *
 * @param config 配置信息
 * @return *PingManager ping管理器实例
 */
func NewPingManager(config *Config) *PingManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &PingManager{
		config:       config,
		apiClient:    NewAPIClient(config),
		pingMonitor:  NewPingMonitor(config),
		tasks:        make(map[string]*DevicePingTask),
		ctx:          ctx,
		cancel:       cancel,
		resultChan:   make(chan PingResultData, 1000), // 缓冲1000个结果
		resultBuffer: make([]PingResultData, 0, config.CollectAPI.BatchSize),
		running:      false,
	}
}

/**
 * Initialize 初始化ping管理器
 *
 * 功能：从API获取设备列表并创建ping任务
 *
 * @return error 错误信息
 */
func (pm *PingManager) Initialize() error {
	logger.Infof("🚀 初始化Ping管理器...")

	// 测试API连接
	if err := pm.apiClient.TestConnection(); err != nil {
		return fmt.Errorf("API连接测试失败: %v", err)
	}

	// 获取设备列表
	devices, err := pm.apiClient.GetDeviceList()
	if err != nil {
		return fmt.Errorf("获取设备列表失败: %v", err)
	}

	if len(devices) == 0 {
		return fmt.Errorf("没有找到启用的设备")
	}

	// 创建ping任务
	validDeviceCount := 0
	for _, device := range devices {
		if device.IPAddress == "" {
			logger.Warnf("⚠️  设备 %s 没有IP地址，跳过", device.ID)
			continue
		}

		if !pm.pingMonitor.IsValidIP(device.IPAddress) {
			logger.Warnf("⚠️  设备 %s 的IP地址格式无效: %s，跳过", device.ID, device.IPAddress)
			continue
		}

		// 创建设备ping任务
		task := pm.createDevicePingTask(device)
		pm.tasksMutex.Lock()
		pm.tasks[device.ID] = task
		pm.tasksMutex.Unlock()

		validDeviceCount++
		logger.Infof("✅ 创建ping任务: %s (%s) - %s", device.ID, device.Name, device.IPAddress)
	}

	if validDeviceCount == 0 {
		return fmt.Errorf("没有找到有效的设备IP地址")
	}

	// 更新全局统计
	pm.statsMutex.Lock()
	pm.globalStats.TotalDevices = validDeviceCount
	pm.globalStats.LastUpdated = time.Now()
	pm.statsMutex.Unlock()

	logger.Infof("✅ Ping管理器初始化完成，有效设备数: %d", validDeviceCount)
	return nil
}

/**
 * createDevicePingTask 创建设备ping任务
 *
 * @param device 设备信息
 * @return *DevicePingTask ping任务
 */
func (pm *PingManager) createDevicePingTask(device DeviceInfo) *DevicePingTask {
	ctx, cancel := context.WithCancel(pm.ctx)

	return &DevicePingTask{
		Device:  device,
		Monitor: pm.pingMonitor,
		Context: ctx,
		Cancel:  cancel,
		Stats: PingStats{
			MinResponseTime: 999999, // 初始化为很大的值
		},
		Status: "stopped",
	}
}

/**
 * Start 启动ping管理器
 *
 * 功能：启动所有ping任务和数据处理协程
 *
 * @return error 错误信息
 */
func (pm *PingManager) Start() error {
	if pm.running {
		return fmt.Errorf("ping管理器已经在运行")
	}

	logger.Infof("🚀 启动Ping管理器...")

	pm.running = true

	// 启动结果处理协程
	pm.wg.Add(1)
	go pm.resultProcessor()

	// 启动批量提交协程
	pm.wg.Add(1)
	go pm.batchSubmitter()

	// 启动统计报告协程
	pm.wg.Add(1)
	go pm.statsReporter()

	// 启动所有设备ping任务
	pm.tasksMutex.RLock()
	taskCount := len(pm.tasks)
	pm.tasksMutex.RUnlock()

	logger.Infof("📱 启动 %d 个设备ping任务...", taskCount)

	pm.tasksMutex.RLock()
	for deviceID, task := range pm.tasks {
		pm.wg.Add(1)
		go pm.runDevicePingTask(deviceID, task)
	}
	pm.tasksMutex.RUnlock()

	logger.Infof("✅ Ping管理器启动完成")
	return nil
}

/**
 * runDevicePingTask 运行设备ping任务
 *
 * @param deviceID 设备ID
 * @param task ping任务
 */
func (pm *PingManager) runDevicePingTask(deviceID string, task *DevicePingTask) {
	defer pm.wg.Done()

	task.Status = "running"
	logger.Infof("🏓 启动设备ping任务: %s (%s)", deviceID, task.Device.IPAddress)

	ticker := time.NewTicker(pm.config.Ping.Interval)
	defer ticker.Stop()

	// 立即执行一次ping
	pm.executePing(task)

	for {
		select {
		case <-task.Context.Done():
			task.Status = "stopped"
			logger.Infof("🛑 设备ping任务停止: %s", deviceID)
			return
		case <-ticker.C:
			pm.executePing(task)
		}
	}
}

/**
 * executePing 执行ping操作
 *
 * @param task ping任务
 */
func (pm *PingManager) executePing(task *DevicePingTask) {
	// 执行ping
	result := task.Monitor.PingWithRetry(task.Device.IPAddress)
	task.LastResult = result
	task.LastPingTime = time.Now()

	// 更新任务统计
	pm.updateTaskStats(task, result)

	// 构建结果数据
	resultData := PingResultData{
		DeviceID:     task.Device.ID,
		DeviceName:   task.Device.Name,
		IPAddress:    task.Device.IPAddress,
		Success:      result.Success,
		ResponseTime: result.ResponseTime,
		PacketLoss:   result.PacketLoss,
		ErrorMessage: result.ErrorMessage,
		Timestamp:    result.Timestamp,
		DataType:     "network_ping",
		Location:     task.Device.Location,
	}

	// 发送到结果通道
	select {
	case pm.resultChan <- resultData:
	default:
		logger.Warnf("⚠️  结果通道已满，丢弃ping结果: %s", task.Device.ID)
	}
}

/**
 * updateTaskStats 更新任务统计信息
 *
 * @param task ping任务
 * @param result ping结果
 */
func (pm *PingManager) updateTaskStats(task *DevicePingTask, result PingResult) {
	task.Stats.TotalPings++

	if result.Success {
		task.Stats.SuccessCount++
		task.Stats.TotalResponseTime += result.ResponseTime
		task.Stats.ConsecutiveFailures = 0
		task.Stats.LastSuccessTime = result.Timestamp

		// 更新最小/最大响应时间
		if result.ResponseTime < task.Stats.MinResponseTime {
			task.Stats.MinResponseTime = result.ResponseTime
		}
		if result.ResponseTime > task.Stats.MaxResponseTime {
			task.Stats.MaxResponseTime = result.ResponseTime
		}
	} else {
		task.Stats.FailureCount++
		task.Stats.ConsecutiveFailures++
		task.Stats.LastFailureTime = result.Timestamp
	}
}

/**
 * resultProcessor 结果处理协程
 *
 * 功能：处理ping结果，添加到批量缓存中
 */
func (pm *PingManager) resultProcessor() {
	defer pm.wg.Done()

	logger.Infof("📊 启动结果处理协程")

	for {
		select {
		case <-pm.ctx.Done():
			logger.Infof("🛑 结果处理协程停止")
			return
		case result := <-pm.resultChan:
			pm.bufferMutex.Lock()
			pm.resultBuffer = append(pm.resultBuffer, result)

			// 如果缓存满了，立即提交
			if len(pm.resultBuffer) >= pm.config.CollectAPI.BatchSize {
				pm.submitBufferedResults()
			}
			pm.bufferMutex.Unlock()
		}
	}
}

/**
 * batchSubmitter 批量提交协程
 *
 * 功能：定期提交缓存的ping结果
 */
func (pm *PingManager) batchSubmitter() {
	defer pm.wg.Done()

	logger.Infof("📤 启动批量提交协程")

	ticker := time.NewTicker(pm.config.CollectAPI.BatchInterval)
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			// 程序退出前提交剩余结果
			pm.bufferMutex.Lock()
			if len(pm.resultBuffer) > 0 {
				pm.submitBufferedResults()
			}
			pm.bufferMutex.Unlock()
			logger.Infof("🛑 批量提交协程停止")
			return
		case <-ticker.C:
			pm.bufferMutex.Lock()
			if len(pm.resultBuffer) > 0 {
				pm.submitBufferedResults()
			}
			pm.bufferMutex.Unlock()
		}
	}
}

/**
 * submitBufferedResults 提交缓存的结果
 *
 * 注意：调用此函数前必须已获取bufferMutex锁
 */
func (pm *PingManager) submitBufferedResults() {
	if len(pm.resultBuffer) == 0 {
		return
	}

	// 复制缓存数据
	results := make([]PingResultData, len(pm.resultBuffer))
	copy(results, pm.resultBuffer)

	// 清空缓存
	pm.resultBuffer = pm.resultBuffer[:0]

	// 异步提交，避免阻塞
	go func() {
		if err := pm.apiClient.SubmitPingResultBatch(results); err != nil {
			logger.Errorf("❌ 批量提交ping结果失败: %v", err)
		}
	}()
}

/**
 * statsReporter 统计报告协程
 *
 * 功能：定期输出统计信息
 */
func (pm *PingManager) statsReporter() {
	defer pm.wg.Done()

	logger.Infof("📈 启动统计报告协程")

	ticker := time.NewTicker(pm.config.Global.StatsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			logger.Infof("🛑 统计报告协程停止")
			return
		case <-ticker.C:
			pm.generateStatsReport()
		}
	}
}

/**
 * generateStatsReport 生成统计报告
 */
func (pm *PingManager) generateStatsReport() {
	pm.tasksMutex.RLock()
	defer pm.tasksMutex.RUnlock()

	var totalPings, successCount, failureCount int64
	var totalResponseTime float64
	var onlineDevices, offlineDevices int

	for _, task := range pm.tasks {
		totalPings += task.Stats.TotalPings
		successCount += task.Stats.SuccessCount
		failureCount += task.Stats.FailureCount
		totalResponseTime += task.Stats.TotalResponseTime

		// 判断设备在线状态（最近3次ping都成功认为在线）
		if task.Stats.ConsecutiveFailures < 3 && task.Stats.SuccessCount > 0 {
			onlineDevices++
		} else {
			offlineDevices++
		}
	}

	// 更新全局统计
	pm.statsMutex.Lock()
	pm.globalStats.TotalPings = totalPings
	pm.globalStats.SuccessCount = successCount
	pm.globalStats.FailureCount = failureCount
	pm.globalStats.OnlineDevices = onlineDevices
	pm.globalStats.OfflineDevices = offlineDevices
	pm.globalStats.LastUpdated = time.Now()

	if totalPings > 0 {
		pm.globalStats.SuccessRate = float64(successCount) / float64(totalPings) * 100
	}
	if successCount > 0 {
		pm.globalStats.AverageResponseTime = totalResponseTime / float64(successCount)
	}
	pm.statsMutex.Unlock()

	// 输出统计报告
	logger.Infof("📊 网络监控统计报告:")
	logger.Infof("   设备总数: %d (在线: %d, 离线: %d)",
		pm.globalStats.TotalDevices, onlineDevices, offlineDevices)
	logger.Infof("   Ping统计: 总数=%d, 成功=%d, 失败=%d, 成功率=%.1f%%",
		totalPings, successCount, failureCount, pm.globalStats.SuccessRate)
	if pm.globalStats.AverageResponseTime > 0 {
		logger.Infof("   平均响应时间: %.2fms", pm.globalStats.AverageResponseTime)
	}
}

/**
 * Stop 停止ping管理器
 *
 * 功能：停止所有ping任务和协程
 */
func (pm *PingManager) Stop() {
	if !pm.running {
		return
	}

	logger.Infof("🛑 停止Ping管理器...")

	// 取消所有任务
	pm.cancel()

	// 停止所有设备任务
	pm.tasksMutex.RLock()
	for _, task := range pm.tasks {
		task.Cancel()
	}
	pm.tasksMutex.RUnlock()

	pm.running = false
	logger.Infof("✅ Ping管理器已停止")
}

/**
 * Wait 等待所有任务完成
 */
func (pm *PingManager) Wait() {
	pm.wg.Wait()
	logger.Infof("✅ 所有Ping任务已完成")
}

/**
 * GetGlobalStats 获取全局统计信息
 *
 * @return GlobalPingStats 全局统计信息
 */
func (pm *PingManager) GetGlobalStats() GlobalPingStats {
	pm.statsMutex.RLock()
	defer pm.statsMutex.RUnlock()
	return pm.globalStats
}

/**
 * GetDeviceStats 获取设备统计信息
 *
 * @param deviceID 设备ID
 * @return *PingStats 设备统计信息
 * @return bool 是否找到设备
 */
func (pm *PingManager) GetDeviceStats(deviceID string) (*PingStats, bool) {
	pm.tasksMutex.RLock()
	defer pm.tasksMutex.RUnlock()

	if task, exists := pm.tasks[deviceID]; exists {
		return &task.Stats, true
	}
	return nil, false
}
