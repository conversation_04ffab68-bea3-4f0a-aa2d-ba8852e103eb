# 设备网络监控服务配置文件
# 
# 功能说明：
# 本配置文件定义了ping服务的所有运行参数，包括API连接、ping参数、
# 日志配置等，支持灵活的配置管理和环境适配
#
# 配置结构：
# - device_api: 设备列表获取API配置
# - collect_api: 数据采集API配置  
# - ping: ping监控参数配置
# - global: 全局运行参数配置
# - logging: 日志系统配置
#
# 使用说明：
# 1. 根据实际环境修改API地址
# 2. 调整ping间隔和超时参数
# 3. 配置合适的日志级别和输出
# 4. 设置批量提交参数优化性能

# 设备API配置 - 用于获取设备列表
device_api:
  # API服务地址 - 12_server_api的设备列表接口
  url: "http://localhost:9005/api/public/devices/configs"
  
  # 请求超时时间
  timeout: 10s
  
  # 失败重试次数
  retry_count: 3
  
  # 设备配置刷新间隔（定期重新获取设备列表）
  refresh_interval: 300s  # 5分钟

# 数据采集API配置 - 用于提交ping结果
collect_api:
  # 数据采集服务地址 - 暂时使用模拟端点（演示模式）
  url: "http://localhost:9001/api/v1/device/data"
  
  # 请求超时时间
  timeout: 5s
  
  # 失败重试次数
  retry_count: 3
  
  # 批量提交大小（一次提交多少个ping结果）
  batch_size: 10
  
  # 批量提交间隔（多长时间提交一次）
  batch_interval: 5s

# Ping监控配置
ping:
  # Ping间隔时间（多长时间ping一次）
  interval: 1s
  
  # Ping超时时间（单次ping的超时）
  timeout: 3s
  
  # 每次ping的包数量
  count: 1
  
  # 包大小（字节）
  packet_size: 32
  
  # 并发ping的最大数量（同时ping多少个设备）
  concurrent_limit: 50
  
  # 失败重试次数
  retry_count: 2
  
  # 失败重试间隔
  retry_interval: 1s

# 全局配置
global:
  # 运行模式：forever(永久运行) | duration(持续时间)
  run_mode: "forever"
  
  # 运行持续时间（run_mode=duration时有效）
  run_duration: 1h
  
  # 统计信息输出间隔
  stats_interval: 30s
  
  # 最大并发数限制
  concurrent_limit: 100
  
  # 是否启用详细模式
  verbose: false

# 日志配置
logging:
  # 日志级别：debug, info, warn, error
  level: "info"
  
  # 日志格式：text, json
  format: "json"
  
  # 输出目标：stdout, stderr, 文件路径
  output: "logs/ping_service.log"
  
  # 是否输出设备详细日志
  device_logs: true
  
  # 日志轮转配置
  rotation:
    # 是否启用日志轮转
    enabled: true
    
    # 单个文件最大大小（MB）
    max_size: 100
    
    # 文件保留天数
    max_age: 30
    
    # 保留的备份文件数量
    max_backups: 10
    
    # 是否压缩旧文件
    compress: true

# 环境配置说明：
# 
# 开发环境：
# - device_api.url: "http://localhost:9005/api/machines"
# - collect_api.url: "http://localhost:9005/api/collect"
# - ping.interval: 30s (较短间隔用于测试)
# - logging.level: "debug" (详细日志)
#
# 测试环境：
# - device_api.url: "http://test-server:9005/api/machines"
# - collect_api.url: "http://test-server:9005/api/collect"
# - ping.interval: 60s (中等间隔)
# - logging.level: "info"
#
# 生产环境：
# - device_api.url: "http://prod-server:9005/api/machines"
# - collect_api.url: "http://prod-server:9005/api/collect"
# - ping.interval: 300s (较长间隔减少网络负载)
# - logging.level: "warn" (只记录警告和错误)
# - ping.concurrent_limit: 200 (更高并发)
# - collect_api.batch_size: 50 (更大批量)

# 性能调优建议：
#
# 高频监控场景：
# - ping.interval: 10s
# - collect_api.batch_size: 5
# - collect_api.batch_interval: 2s
#
# 大规模设备场景：
# - ping.concurrent_limit: 500
# - collect_api.batch_size: 100
# - collect_api.batch_interval: 10s
#
# 低网络负载场景：
# - ping.interval: 600s (10分钟)
# - ping.count: 3 (多次ping提高准确性)
# - collect_api.batch_size: 20

# 故障排查配置：
#
# 调试模式：
# - logging.level: "debug"
# - logging.device_logs: true
# - global.verbose: true
# - ping.retry_count: 5
#
# 网络问题排查：
# - ping.timeout: 10s
# - ping.count: 5
# - ping.retry_count: 3
# - ping.retry_interval: 2s
