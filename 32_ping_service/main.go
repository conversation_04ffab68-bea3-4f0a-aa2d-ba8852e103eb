/**
 * 设备网络连通性监控服务主程序
 *
 * 功能概述：
 * 本程序是制造业设备网络监控系统，负责定期ping设备IP地址，
 * 监控设备网络连通性状态，并将ping结果提交到数据采集服务
 *
 * 核心功能：
 * - 设备发现：从API动态获取设备列表和IP地址信息
 * - 网络监控：定期ping设备IP，检测网络连通性
 * - 状态采集：收集ping响应时间、丢包率等网络指标
 * - 数据上报：将ping结果提交到collect API进行存储
 * - 实时监控：实时统计和监控所有设备的网络状态
 * - 故障告警：检测网络故障并记录异常状态
 *
 * 技术特性：
 * - 高并发：支持数百台设备同时ping监控
 * - 可配置：丰富的配置选项，支持不同监控场景
 * - 实时性：秒级的ping监控和状态上报
 * - 可观测：详细的日志记录和统计信息
 * - 优雅关闭：支持信号处理和优雅关闭机制
 *
 * 架构设计：
 * - 管理器模式：PingManager统一管理所有设备ping任务
 * - 工厂模式：根据配置动态创建ping监控实例
 * - 并发模式：每个设备运行在独立的goroutine中
 * - 观察者模式：统一的状态监控和事件处理
 *
 * 业务场景：
 * - 网络监控：监控设备网络连通性状态
 * - 故障检测：及时发现网络故障和设备离线
 * - 性能分析：分析网络延迟和稳定性
 * - 运维支持：为运维人员提供网络状态数据
 *
 * 使用方法：
 * ```bash
 * # 开发环境运行
 * go run . -config config.yml
 *
 * # 生产环境编译运行
 * go build -o ping_service .
 * ./ping_service -config config.yml
 * ```
 *
 * @package main
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-02
 */
package main

import (
	"flag"
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/signal"
	"syscall"
	"time"

	"shared/logger"
)

/**
 * 主函数 - 设备网络监控服务入口点
 *
 * 功能：负责程序的完整生命周期管理，包括初始化、启动、运行和关闭
 *
 * 启动流程：
 * 1. 参数解析：解析命令行参数，获取配置文件路径
 * 2. 随机数初始化：初始化随机数种子
 * 3. 配置加载：加载并验证配置文件
 * 4. 日志初始化：初始化日志系统和轮转配置
 * 5. Ping管理器创建：创建ping管理器实例
 * 6. 设备初始化：从API获取设备列表并初始化ping任务
 * 7. 信号处理：设置优雅关闭的信号处理
 * 8. 监控启动：启动所有设备的ping监控
 * 9. 等待关闭：等待中断信号并执行优雅关闭
 *
 * 错误处理：
 * - 任何关键步骤失败都会导致程序退出
 * - 详细的错误日志记录，便于问题诊断
 * - 优雅的错误处理，避免数据丢失
 *
 * @example
 * // 使用默认配置启动
 * go run .
 *
 * // 使用指定配置启动
 * go run . -config ping_config.yml
 */
func main() {
	/** 解析命令行参数，获取配置文件路径 */
	configFile := flag.String("config", "config.yml", "配置文件路径")
	flag.Parse()

	/** 初始化随机数种子，确保每次运行生成不同的随机数据 */
	rand.Seed(time.Now().UnixNano())

	/** 输出启动信息，便于运维人员确认程序状态 */
	fmt.Printf("🚀 设备网络监控服务启动中...\n")
	fmt.Printf("📄 配置文件: %s\n", *configFile)

	// 加载配置文件
	config, err := LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("❌ 配置加载失败: %v", err)
	}

	fmt.Printf("✅ 配置加载成功\n")

	// 初始化日志系统
	logger.InitLoggerWithRotation(
		config.Logging.Level,
		config.Logging.Format,
		config.Logging.Output,
		logger.LogRotationConfig{
			Enabled:    config.Logging.Rotation.Enabled,
			MaxSize:    config.Logging.Rotation.MaxSize,
			MaxAge:     config.Logging.Rotation.MaxAge,
			MaxBackups: config.Logging.Rotation.MaxBackups,
			Compress:   config.Logging.Rotation.Compress,
		},
	)

	logger.Infof("🎯 设备API服务: %s", config.DeviceAPI.URL)
	logger.Infof("📡 数据采集服务: %s", config.CollectAPI.URL)
	logger.Infof("⏱️  Ping间隔: %v", config.Ping.Interval)

	// 创建Ping管理器
	manager := NewPingManager(config)

	// 初始化所有设备
	if err := manager.Initialize(); err != nil {
		fmt.Printf("❌ 设备初始化失败\n")
		fmt.Printf("错误详情: %v\n", err)
		fmt.Printf("\n💡 解决建议:\n")
		fmt.Printf("   1. 检查12_server_api服务: cd 12_server_api && ./run.sh\n")
		fmt.Printf("   2. 测试API连接: curl -s %s\n", config.DeviceAPI.URL)
		fmt.Printf("   3. 检查MongoDB服务: docker ps | grep mongo\n")
		fmt.Printf("   4. 启动依赖服务: docker-compose up -d mongodb\n")
		fmt.Printf("   查看详细日志: tail -f logs/ping_service.log\n")
		fmt.Printf("\n")
		log.Fatalf("程序退出")
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动Ping管理器
	if err := manager.Start(); err != nil {
		log.Fatalf("❌ Ping服务启动失败: %v", err)
	}

	// 等待中断信号
	go func() {
		<-sigChan
		fmt.Printf("\n🛑 接收到中断信号，正在停止所有Ping任务...\n")
		manager.Stop()
	}()

	// 等待所有任务完成
	manager.Wait()

	fmt.Printf("👋 程序已退出\n")
}
