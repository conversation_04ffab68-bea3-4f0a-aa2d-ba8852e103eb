#!/bin/bash

# 设备网络监控服务 Docker入口脚本
#
# 功能说明：
# 本脚本作为Docker容器的入口点，负责容器启动时的初始化工作，
# 包括配置文件处理、环境变量设置、权限检查等
#
# 主要功能：
# - 配置文件初始化：处理配置文件模板和环境变量替换
# - 环境检查：检查必要的环境变量和文件权限
# - 日志目录：创建和设置日志目录权限
# - 信号处理：优雅处理容器停止信号
# - 健康检查：提供容器健康状态检查
#
# 环境变量支持：
# - PING_CONFIG_FILE: 配置文件路径
# - PING_LOG_LEVEL: 日志级别
# - PING_LOG_FORMAT: 日志格式
# - DEVICE_API_URL: 设备API地址
# - COLLECT_API_URL: 采集API地址
#
# <AUTHOR>
# @version 1.0.0
# @since 2025-07-02

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_NAME="docker-entrypoint.sh"
CONFIG_FILE="${PING_CONFIG_FILE:-/app/config.yml}"
CONFIG_TEMPLATE="/app/config.yml.template"
LOG_DIR="/app/logs"
DATA_DIR="/app/data"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示启动信息
show_startup_info() {
    echo "=================================================="
    echo "🐳 设备网络监控服务容器启动"
    echo "📅 启动时间: $(date)"
    echo "🏷️  容器ID: ${HOSTNAME}"
    echo "👤 运行用户: $(whoami)"
    echo "📁 工作目录: $(pwd)"
    echo "⚙️  配置文件: ${CONFIG_FILE}"
    echo "=================================================="
}

# 检查环境
check_environment() {
    log_info "检查容器环境..."

    # 检查用户权限
    if [ "$(whoami)" = "root" ]; then
        log_warn "⚠️  以root用户运行，建议使用非root用户"
    fi

    # 检查必要目录
    for dir in "$LOG_DIR" "$DATA_DIR"; do
        if [ ! -d "$dir" ]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi
        
        # 检查目录权限
        if [ ! -w "$dir" ]; then
            log_error "目录不可写: $dir"
            exit 1
        fi
    done

    log_info "✅ 环境检查通过"
}

# 处理配置文件
process_config_file() {
    log_info "处理配置文件..."

    # 如果配置文件不存在，从模板创建
    if [ ! -f "$CONFIG_FILE" ]; then
        if [ -f "$CONFIG_TEMPLATE" ]; then
            log_info "从模板创建配置文件: $CONFIG_FILE"
            cp "$CONFIG_TEMPLATE" "$CONFIG_FILE"
        else
            log_error "配置文件和模板都不存在"
            exit 1
        fi
    fi

    # 环境变量替换
    if [ -n "${DEVICE_API_URL:-}" ]; then
        log_info "设置设备API地址: $DEVICE_API_URL"
        sed -i "s|url: \"http://localhost:9005/api/machines\"|url: \"$DEVICE_API_URL\"|g" "$CONFIG_FILE"
    fi

    if [ -n "${COLLECT_API_URL:-}" ]; then
        log_info "设置采集API地址: $COLLECT_API_URL"
        sed -i "s|url: \"http://localhost:9005/api/collect\"|url: \"$COLLECT_API_URL\"|g" "$CONFIG_FILE"
    fi

    if [ -n "${PING_LOG_LEVEL:-}" ]; then
        log_info "设置日志级别: $PING_LOG_LEVEL"
        sed -i "s|level: \"info\"|level: \"$PING_LOG_LEVEL\"|g" "$CONFIG_FILE"
    fi

    if [ -n "${PING_LOG_FORMAT:-}" ]; then
        log_info "设置日志格式: $PING_LOG_FORMAT"
        sed -i "s|format: \"json\"|format: \"$PING_LOG_FORMAT\"|g" "$CONFIG_FILE"
    fi

    # 验证配置文件
    if [ ! -r "$CONFIG_FILE" ]; then
        log_error "配置文件不可读: $CONFIG_FILE"
        exit 1
    fi

    log_info "✅ 配置文件处理完成"
}

# 显示配置信息
show_config_info() {
    log_info "当前配置信息:"
    
    # 从配置文件提取关键信息
    if command -v grep >/dev/null 2>&1; then
        DEVICE_API=$(grep -A 5 "device_api:" "$CONFIG_FILE" | grep "url:" | sed 's/.*url: *"\([^"]*\)".*/\1/' | head -1)
        COLLECT_API=$(grep -A 5 "collect_api:" "$CONFIG_FILE" | grep "url:" | sed 's/.*url: *"\([^"]*\)".*/\1/' | head -1)
        PING_INTERVAL=$(grep -A 10 "ping:" "$CONFIG_FILE" | grep "interval:" | sed 's/.*interval: *\([^ ]*\).*/\1/' | head -1)
        LOG_LEVEL=$(grep -A 10 "logging:" "$CONFIG_FILE" | grep "level:" | sed 's/.*level: *"\([^"]*\)".*/\1/' | head -1)
        
        echo "  📡 设备API: ${DEVICE_API:-未配置}"
        echo "  📤 采集API: ${COLLECT_API:-未配置}"
        echo "  ⏱️  Ping间隔: ${PING_INTERVAL:-未配置}"
        echo "  📝 日志级别: ${LOG_LEVEL:-未配置}"
    fi
}

# 健康检查函数
health_check() {
    # 检查进程是否运行
    if pgrep -f "ping_service" > /dev/null; then
        echo "healthy"
        exit 0
    else
        echo "unhealthy"
        exit 1
    fi
}

# 信号处理函数
signal_handler() {
    log_info "接收到停止信号，正在优雅关闭..."
    
    # 发送TERM信号给ping_service进程
    if [ -n "${PING_SERVICE_PID:-}" ]; then
        kill -TERM "$PING_SERVICE_PID" 2>/dev/null || true
        wait "$PING_SERVICE_PID" 2>/dev/null || true
    fi
    
    log_info "服务已停止"
    exit 0
}

# 启动服务
start_service() {
    log_info "启动设备网络监控服务..."
    
    # 显示启动命令
    log_debug "启动命令: $*"
    
    # 启动服务并获取PID
    exec "$@" &
    PING_SERVICE_PID=$!
    
    log_info "✅ 服务已启动 (PID: $PING_SERVICE_PID)"
    
    # 等待服务进程
    wait "$PING_SERVICE_PID"
}

# 主函数
main() {
    # 检查是否是健康检查调用
    if [ "${1:-}" = "health" ]; then
        health_check
        exit $?
    fi
    
    # 显示启动信息
    show_startup_info
    
    # 设置信号处理
    trap signal_handler SIGTERM SIGINT
    
    # 执行初始化
    check_environment
    process_config_file
    show_config_info
    
    echo ""
    log_info "🚀 开始启动服务..."
    
    # 启动服务
    start_service "$@"
}

# 执行主函数
main "$@"
