/**
 * 设备状态常量定义 - Go语言版本
 *
 * 功能：定义制造业设备管理系统中使用的标准设备状态常量
 * 用途：为Go语言项目提供统一的设备状态定义，确保系统间的一致性
 *
 * 使用方式：
 * ```go
 * import "path/to/define"
 *
 * // 使用设备状态常量
 * if deviceStatus == define.DeviceStatusProduction {
 *     // 设备正在生产
 * }
 *
 * // 获取状态描述
 * description := define.GetDescription(deviceStatus)
 * ```
 *
 * 维护说明：
 * - 只保留5个核心状态：生产/待机/报警/关机/未知
 * - 其他状态通过映射归入核心状态
 * - 状态描述通过GetDescription函数获取
 *
 * @package define
 * @version 2.0.0
 * <AUTHOR> System Team
 * @created 2025-06-21
 */

package define

// =============================================================================
// 核心设备状态常量（5个核心状态）
// =============================================================================

/**
 * 设备状态常量定义
 *
 * 简化为5个核心状态，涵盖设备的所有运行情况
 * 其他状态通过映射归入这5个核心状态
 */
const (
	// DeviceStatusProduction 生产状态
	// 含义：设备正在正常生产或运行，处于工作状态
	// 包含：production, running, working, active, producing
	// 用途：计入设备利用率，表示设备有效工作时间
	DeviceStatusProduction = "production"

	// DeviceStatusIdle 待机状态
	// 含义：设备空闲等待或进行非生产性活动
	// 包含：idle, standby, waiting, ready, maintenance, adjusting, setup
	// 用途：设备可用但未进行生产的时间
	DeviceStatusIdle = "idle"

	// DeviceStatusAlarm 报警状态
	// 含义：设备发生故障、错误或触发报警
	// 包含：alarm, fault, error, failed
	// 用途：设备异常状态，需要人工干预
	DeviceStatusAlarm = "alarm"

	// DeviceStatusShutdown 关机状态
	// 含义：设备已关闭、停机或离线
	// 包含：shutdown, stop, stopped, offline, disconnected, off
	// 用途：设备不可用状态，不计入工作时间
	DeviceStatusShutdown = "shutdown"

	// DeviceStatusUnknown 未知状态
	// 含义：无法确定设备当前状态
	// 包含：unknown, 空字符串, 无效状态
	// 用途：异常情况处理，需要人工确认
	DeviceStatusUnknown = "unknown"
)

// =============================================================================
// 状态描述映射表
// =============================================================================

/**
 * 设备状态描述映射表
 *
 * 为每个核心状态提供详细的中文描述
 * 用于界面显示和日志记录
 */
var StatusDescriptions = map[string]string{
	DeviceStatusProduction: "生产中 - 设备正在正常生产或运行",
	DeviceStatusIdle:       "待机中 - 设备空闲等待或进行维护调试",
	DeviceStatusAlarm:      "报警中 - 设备发生故障或触发报警",
	DeviceStatusShutdown:   "已关机 - 设备已关闭或离线",
	DeviceStatusUnknown:    "未知状态 - 无法确定设备当前状态",
}

// =============================================================================
// 状态映射表
// =============================================================================

/**
 * 设备状态映射表
 *
 * 用于将不同系统或设备的状态值映射到5个核心状态
 * 支持多种命名习惯和历史兼容性
 */
var DeviceStatusMapping = map[string]string{
	// 生产状态映射 -> production
	"production": DeviceStatusProduction,
	"producing":  DeviceStatusProduction,
	"working":    DeviceStatusProduction,
	"running":    DeviceStatusProduction,
	"run":        DeviceStatusProduction,
	"active":     DeviceStatusProduction,

	// 待机状态映射 -> idle
	"idle":        DeviceStatusIdle,
	"standby":     DeviceStatusIdle,
	"waiting":     DeviceStatusIdle,
	"ready":       DeviceStatusIdle,
	"maintenance": DeviceStatusIdle,
	"maintain":    DeviceStatusIdle,
	"service":     DeviceStatusIdle,
	"adjusting":   DeviceStatusIdle,
	"setup":       DeviceStatusIdle,

	// 报警状态映射 -> alarm
	"alarm":  DeviceStatusAlarm,
	"fault":  DeviceStatusAlarm,
	"error":  DeviceStatusAlarm,
	"failed": DeviceStatusAlarm,

	// 关机状态映射 -> shutdown
	"shutdown":     DeviceStatusShutdown,
	"stop":         DeviceStatusShutdown,
	"stopped":      DeviceStatusShutdown,
	"offline":      DeviceStatusShutdown,
	"disconnected": DeviceStatusShutdown,
	"off":          DeviceStatusShutdown,

	// 未知状态映射 -> unknown
	"unknown": DeviceStatusUnknown,
	"":        DeviceStatusUnknown,
}

// =============================================================================
// 状态分类定义
// =============================================================================

/**
 * 状态分类常量
 *
 * 将5个核心状态按照业务逻辑进行分类
 */
const (
	// StatusCategoryProductive 生产性状态分类
	// 包含：production
	// 用途：计入设备利用率的有效工作时间
	StatusCategoryProductive = "productive"

	// StatusCategoryNonProductive 非生产性状态分类
	// 包含：idle
	// 用途：设备可用但未进行生产的时间
	StatusCategoryNonProductive = "non_productive"

	// StatusCategoryUnavailable 不可用状态分类
	// 包含：alarm, shutdown
	// 用途：设备无法工作的时间，影响可用性
	StatusCategoryUnavailable = "unavailable"

	// StatusCategoryUnknown 未知状态分类
	// 包含：unknown
	// 用途：异常情况处理
	StatusCategoryUnknown = "unknown_category"
)

/**
 * 状态分类映射表
 *
 * 将5个核心状态映射到对应的分类
 * 用于统计分析和业务逻辑处理
 */
var StatusCategoryMapping = map[string]string{
	// 生产性状态
	DeviceStatusProduction: StatusCategoryProductive,

	// 非生产性状态
	DeviceStatusIdle: StatusCategoryNonProductive,

	// 不可用状态
	DeviceStatusAlarm:    StatusCategoryUnavailable,
	DeviceStatusShutdown: StatusCategoryUnavailable,

	// 未知状态
	DeviceStatusUnknown: StatusCategoryUnknown,
}

// =============================================================================
// 辅助函数
// =============================================================================

/**
 * MapDeviceStatus 映射设备状态
 *
 * 功能：将输入的状态值映射到5个核心状态之一
 * 用途：统一不同来源的状态数据，确保系统一致性
 *
 * @param {string} status - 原始状态值
 * @returns {string} 映射后的核心状态值
 *
 * @example
 * standardStatus := MapDeviceStatus("producing") // 返回 "production"
 * standardStatus := MapDeviceStatus("fault") // 返回 "alarm"
 */
func MapDeviceStatus(status string) string {
	if mappedStatus, exists := DeviceStatusMapping[status]; exists {
		return mappedStatus
	}
	return DeviceStatusUnknown
}

/**
 * GetDescription 获取状态描述
 *
 * 功能：根据设备状态获取对应的中文描述
 * 用途：用于界面显示和日志记录
 *
 * @param {string} status - 设备状态
 * @returns {string} 状态描述
 *
 * @example
 * description := GetDescription("production") // 返回 "生产中 - 设备正在正常生产或运行"
 * description := GetDescription("fault") // 先映射到alarm，返回 "报警中 - 设备发生故障或触发报警"
 */
func GetDescription(status string) string {
	// 先映射到核心状态
	coreStatus := MapDeviceStatus(status)

	if description, exists := StatusDescriptions[coreStatus]; exists {
		return description
	}
	return StatusDescriptions[DeviceStatusUnknown]
}

/**
 * GetStatusCategory 获取状态分类
 *
 * 功能：根据设备状态获取对应的分类
 * 用途：用于统计分析和业务逻辑处理
 *
 * @param {string} status - 设备状态
 * @returns {string} 状态分类
 *
 * @example
 * category := GetStatusCategory("production") // 返回 "productive"
 */
func GetStatusCategory(status string) string {
	// 先映射到核心状态
	coreStatus := MapDeviceStatus(status)

	if category, exists := StatusCategoryMapping[coreStatus]; exists {
		return category
	}
	return StatusCategoryUnknown
}

/**
 * IsProductiveStatus 判断是否为生产性状态
 *
 * 功能：判断给定状态是否为生产性状态
 * 用途：用于利用率计算和生产统计
 *
 * @param {string} status - 设备状态
 * @returns {bool} 是否为生产性状态
 *
 * @example
 * isProductive := IsProductiveStatus("production") // 返回 true
 * isProductive := IsProductiveStatus("running") // 返回 true（映射到production）
 */
func IsProductiveStatus(status string) bool {
	return GetStatusCategory(status) == StatusCategoryProductive
}

/**
 * IsAvailableStatus 判断是否为可用状态
 *
 * 功能：判断给定状态是否表示设备可用
 * 用途：用于设备可用性统计和维护计划
 *
 * @param {string} status - 设备状态
 * @returns {bool} 是否为可用状态
 *
 * @example
 * isAvailable := IsAvailableStatus("idle") // 返回 true
 * isAvailable := IsAvailableStatus("maintenance") // 返回 true（映射到idle）
 */
func IsAvailableStatus(status string) bool {
	category := GetStatusCategory(status)
	return category == StatusCategoryProductive || category == StatusCategoryNonProductive
}
