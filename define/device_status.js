/**
 * 设备状态常量定义 - JavaScript版本
 *
 * 功能：定义制造业设备管理系统中使用的标准设备状态常量
 * 用途：为JavaScript项目提供统一的设备状态定义，确保系统间的一致性
 *
 * 使用方式：
 * ```javascript
 * const { DeviceStatus, DeviceStatusMapping } = require('./define/device_status');
 *
 * // 使用设备状态常量
 * if (deviceStatus === DeviceStatus.PRODUCTION) {
 *     // 设备正在生产
 * }
 *
 * // 获取状态描述
 * const description = DeviceStatusMapping.getDescription(deviceStatus);
 * ```
 *
 * 维护说明：
 * - 只保留5个核心状态：生产/待机/报警/关机/未知
 * - 其他状态通过映射归入核心状态
 * - 状态描述通过getDescription函数获取
 *
 * @module DeviceStatus
 * @version 2.0.0
 * <AUTHOR> System Team
 * @created 2025-06-21
 */

// =============================================================================
// 核心设备状态常量（5个核心状态）
// =============================================================================

/**
 * 设备状态常量对象
 *
 * 简化为5个核心状态，涵盖设备的所有运行情况
 * 其他状态通过映射归入这5个核心状态
 */
const DeviceStatus = {
  /**
   * 生产状态
   * 含义：设备正在正常生产或运行，处于工作状态
   * 包含：production, running, working, active, producing
   * 用途：计入设备利用率，表示设备有效工作时间
   */
  PRODUCTION: 'production',

  /**
   * 待机状态
   * 含义：设备空闲等待或进行非生产性活动
   * 包含：idle, standby, waiting, ready, maintenance, adjusting, setup
   * 用途：设备可用但未进行生产的时间
   */
  IDLE: 'idle',

  /**
   * 报警状态
   * 含义：设备发生故障、错误或触发报警
   * 包含：alarm, fault, error, failed
   * 用途：设备异常状态，需要人工干预
   */
  ALARM: 'alarm',

  /**
   * 关机状态
   * 含义：设备已关闭、停机或离线
   * 包含：shutdown, stop, stopped, offline, disconnected, off
   * 用途：设备不可用状态，不计入工作时间
   */
  SHUTDOWN: 'shutdown',

  /**
   * 未知状态
   * 含义：无法确定设备当前状态
   * 包含：unknown, 空字符串, 无效状态
   * 用途：异常情况处理，需要人工确认
   */
  UNKNOWN: 'unknown'
};

// =============================================================================
// 状态描述映射表
// =============================================================================

/**
 * 设备状态描述映射表
 *
 * 为每个核心状态提供详细的中文描述
 * 用于界面显示和日志记录
 */
const statusDescriptions = {
  [DeviceStatus.PRODUCTION]: '生产中 - 设备正在正常生产或运行',
  [DeviceStatus.IDLE]: '待机中 - 设备空闲等待或进行维护调试',
  [DeviceStatus.ALARM]: '报警中 - 设备发生故障或触发报警',
  [DeviceStatus.SHUTDOWN]: '已关机 - 设备已关闭或离线',
  [DeviceStatus.UNKNOWN]: '未知状态 - 无法确定设备当前状态',
};

// =============================================================================
// 状态分类常量
// =============================================================================

/**
 * 设备状态分类定义
 *
 * 将5个核心状态按照业务逻辑进行分类
 */
const StatusCategory = {
  /**
   * 生产性状态分类
   * 包含：production
   * 用途：计入设备利用率的有效工作时间
   */
  PRODUCTIVE: 'productive',

  /**
   * 非生产性状态分类
   * 包含：idle
   * 用途：设备可用但未进行生产的时间
   */
  NON_PRODUCTIVE: 'non_productive',

  /**
   * 不可用状态分类
   * 包含：alarm, shutdown
   * 用途：设备无法工作的时间，影响可用性
   */
  UNAVAILABLE: 'unavailable',

  /**
   * 未知状态分类
   * 包含：unknown
   * 用途：异常情况处理
   */
  UNKNOWN: 'unknown_category'
};

// =============================================================================
// 状态映射表
// =============================================================================

/**
 * 设备状态映射表
 *
 * 用于将不同系统或设备的状态值映射到5个核心状态
 * 支持多种命名习惯和历史兼容性
 */
const deviceStatusMapping = {
  // 生产状态映射 -> production
  'production': DeviceStatus.PRODUCTION,
  'producing': DeviceStatus.PRODUCTION,
  'working': DeviceStatus.PRODUCTION,
  'running': DeviceStatus.PRODUCTION,
  'run': DeviceStatus.PRODUCTION,
  'active': DeviceStatus.PRODUCTION,

  // 待机状态映射 -> idle
  'idle': DeviceStatus.IDLE,
  'standby': DeviceStatus.IDLE,
  'waiting': DeviceStatus.IDLE,
  'ready': DeviceStatus.IDLE,
  'maintenance': DeviceStatus.IDLE,
  'maintain': DeviceStatus.IDLE,
  'service': DeviceStatus.IDLE,
  'adjusting': DeviceStatus.IDLE,
  'setup': DeviceStatus.IDLE,

  // 报警状态映射 -> alarm
  'alarm': DeviceStatus.ALARM,
  'fault': DeviceStatus.ALARM,
  'error': DeviceStatus.ALARM,
  'failed': DeviceStatus.ALARM,

  // 关机状态映射 -> shutdown
  'shutdown': DeviceStatus.SHUTDOWN,
  'stop': DeviceStatus.SHUTDOWN,
  'stopped': DeviceStatus.SHUTDOWN,
  'offline': DeviceStatus.SHUTDOWN,
  'disconnected': DeviceStatus.SHUTDOWN,
  'off': DeviceStatus.SHUTDOWN,

  // 未知状态映射 -> unknown
  'unknown': DeviceStatus.UNKNOWN,
  '': DeviceStatus.UNKNOWN,
};

/**
 * 状态分类映射表
 *
 * 将5个核心状态映射到对应的分类
 * 用于统计分析和业务逻辑处理
 */
const statusCategoryMapping = {
  // 生产性状态
  [DeviceStatus.PRODUCTION]: StatusCategory.PRODUCTIVE,

  // 非生产性状态
  [DeviceStatus.IDLE]: StatusCategory.NON_PRODUCTIVE,

  // 不可用状态
  [DeviceStatus.ALARM]: StatusCategory.UNAVAILABLE,
  [DeviceStatus.SHUTDOWN]: StatusCategory.UNAVAILABLE,

  // 未知状态
  [DeviceStatus.UNKNOWN]: StatusCategory.UNKNOWN,
};

// =============================================================================
// 辅助函数
// =============================================================================

/**
 * 设备状态映射工具类
 */
class DeviceStatusMapping {
  /**
   * 映射设备状态
   *
   * 功能：将输入的状态值映射到5个核心状态之一
   * 用途：统一不同来源的状态数据，确保系统一致性
   *
   * @param {string} status - 原始状态值
   * @returns {string} 映射后的核心状态值
   *
   * @example
   * const standardStatus = DeviceStatusMapping.mapStatus('producing'); // 返回 'production'
   * const standardStatus = DeviceStatusMapping.mapStatus('fault'); // 返回 'alarm'
   */
  static mapStatus(status) {
    return deviceStatusMapping[status.toLowerCase()] || DeviceStatus.UNKNOWN;
  }

  /**
   * 获取状态描述
   *
   * 功能：根据设备状态获取对应的中文描述
   * 用途：用于界面显示和日志记录
   *
   * @param {string} status - 设备状态
   * @returns {string} 状态描述
   *
   * @example
   * const description = DeviceStatusMapping.getDescription('production'); // 返回 "生产中 - 设备正在正常生产或运行"
   * const description = DeviceStatusMapping.getDescription('fault'); // 先映射到alarm，返回 "报警中 - 设备发生故障或触发报警"
   */
  static getDescription(status) {
    // 先映射到核心状态
    const coreStatus = this.mapStatus(status);
    return statusDescriptions[coreStatus] || statusDescriptions[DeviceStatus.UNKNOWN];
  }

  /**
   * 获取状态分类
   *
   * 功能：根据设备状态获取对应的分类
   * 用途：用于统计分析和业务逻辑处理
   *
   * @param {string} status - 设备状态
   * @returns {string} 状态分类
   *
   * @example
   * const category = DeviceStatusMapping.getStatusCategory('production'); // 返回 'productive'
   */
  static getStatusCategory(status) {
    // 先映射到核心状态
    const coreStatus = this.mapStatus(status);
    return statusCategoryMapping[coreStatus] || StatusCategory.UNKNOWN;
  }

  /**
   * 判断是否为生产性状态
   *
   * 功能：判断给定状态是否为生产性状态
   * 用途：用于利用率计算和生产统计
   *
   * @param {string} status - 设备状态
   * @returns {boolean} 是否为生产性状态
   *
   * @example
   * const isProductive = DeviceStatusMapping.isProductiveStatus('production'); // 返回 true
   * const isProductive = DeviceStatusMapping.isProductiveStatus('running'); // 返回 true（映射到production）
   */
  static isProductiveStatus(status) {
    return this.getStatusCategory(status) === StatusCategory.PRODUCTIVE;
  }

  /**
   * 判断是否为可用状态
   *
   * 功能：判断给定状态是否表示设备可用
   * 用途：用于设备可用性统计和维护计划
   *
   * @param {string} status - 设备状态
   * @returns {boolean} 是否为可用状态
   *
   * @example
   * const isAvailable = DeviceStatusMapping.isAvailableStatus('idle'); // 返回 true
   * const isAvailable = DeviceStatusMapping.isAvailableStatus('maintenance'); // 返回 true（映射到idle）
   */
  static isAvailableStatus(status) {
    const category = this.getStatusCategory(status);
    return category === StatusCategory.PRODUCTIVE || category === StatusCategory.NON_PRODUCTIVE;
  }

  /**
   * 获取所有核心状态
   *
   * @returns {string[]} 核心状态数组
   */
  static getCoreStatuses() {
    return [
      DeviceStatus.PRODUCTION,
      DeviceStatus.IDLE,
      DeviceStatus.ALARM,
      DeviceStatus.SHUTDOWN,
      DeviceStatus.UNKNOWN
    ];
  }

  /**
   * 获取生产性状态
   *
   * @returns {string[]} 生产性状态数组
   */
  static getProductiveStatuses() {
    return [DeviceStatus.PRODUCTION];
  }

  /**
   * 获取故障相关状态
   *
   * @returns {string[]} 故障状态数组
   */
  static getFaultStatuses() {
    return [DeviceStatus.ALARM];
  }

  /**
   * 获取离线相关状态
   *
   * @returns {string[]} 离线状态数组
   */
  static getOfflineStatuses() {
    return [DeviceStatus.SHUTDOWN];
  }
}

// =============================================================================
// 模块导出
// =============================================================================

module.exports = {
  DeviceStatus,
  StatusCategory,
  DeviceStatusMapping,
  deviceStatusMapping,
  statusCategoryMapping
};
