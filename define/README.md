# 设备状态常量定义

## 📋 概述

本目录包含制造业设备管理系统中使用的标准设备状态常量定义，支持多种编程语言，确保系统间的一致性。

**核心特性**：简化为5个核心状态，其他状态通过映射归入核心状态，提供`GetDescription()`函数获取状态描述。

## 🎯 设计目标

- **简化标准**：只保留5个核心状态，降低复杂性
- **智能映射**：历史状态自动映射到核心状态
- **多语言支持**：支持Go、TypeScript、JavaScript、Python等主流语言
- **描述功能**：提供GetDescription()函数获取状态中文描述
- **易于维护**：集中管理，便于更新和扩展

## 📁 文件结构

```
define/
├── README.md           # 使用说明文档
├── device_status.go    # Go语言版本
├── device_status.ts    # TypeScript版本
├── device_status.js    # JavaScript版本（CommonJS）
└── device_status.py    # Python版本
```

## 🔧 核心设备状态（5个状态）

### 1. 生产状态 (`production`)
- **含义**：设备正在正常生产或运行，处于工作状态
- **包含状态**：production, running, working, active, producing
- **用途**：计入设备利用率，表示设备有效工作时间
- **描述**：生产中 - 设备正在正常生产或运行

### 2. 待机状态 (`idle`)
- **含义**：设备空闲等待或进行非生产性活动
- **包含状态**：idle, standby, waiting, ready, maintenance, adjusting, setup
- **用途**：设备可用但未进行生产的时间
- **描述**：待机中 - 设备空闲等待或进行维护调试

### 3. 报警状态 (`alarm`)
- **含义**：设备发生故障、错误或触发报警
- **包含状态**：alarm, fault, error, failed
- **用途**：设备异常状态，需要人工干预
- **描述**：报警中 - 设备发生故障或触发报警

### 4. 关机状态 (`shutdown`)
- **含义**：设备已关闭、停机或离线
- **包含状态**：shutdown, stop, stopped, offline, disconnected, off
- **用途**：设备不可用状态，不计入工作时间
- **描述**：已关机 - 设备已关闭或离线

### 5. 未知状态 (`unknown`)
- **含义**：无法确定设备当前状态
- **包含状态**：unknown, 空字符串, 无效状态
- **用途**：异常情况处理，需要人工确认
- **描述**：未知状态 - 无法确定设备当前状态

## 📊 状态分类

### 生产性状态 (`productive`)
- **包含**：`production`
- **用途**：计入设备利用率的有效工作时间
- **业务意义**：设备正在创造价值的时间

### 非生产性状态 (`non_productive`)
- **包含**：`idle`
- **用途**：设备可用但未进行生产的时间
- **业务意义**：设备准备或维护时间

### 不可用状态 (`unavailable`)
- **包含**：`alarm`, `shutdown`
- **用途**：设备无法工作的时间，影响可用性
- **业务意义**：设备停机或故障时间

### 未知状态 (`unknown_category`)
- **包含**：`unknown`
- **用途**：异常情况处理
- **业务意义**：需要人工确认的异常状态

## 🚀 使用示例

### Go语言

```go
package main

import (
    "fmt"
    "path/to/define"
)

func main() {
    // 使用设备状态常量
    if deviceStatus == define.DeviceStatusProduction {
        fmt.Println("设备正在生产")
    }

    // 状态映射
    standardStatus := define.MapDeviceStatus("producing")
    fmt.Println("标准状态:", standardStatus) // 输出: production

    // 获取状态描述
    description := define.GetDescription("fault")
    fmt.Println("状态描述:", description) // 输出: 报警中 - 设备发生故障或触发报警

    // 状态分类判断
    if define.IsProductiveStatus(deviceStatus) {
        fmt.Println("这是生产性状态")
    }
}
```

### TypeScript

```typescript
import { DeviceStatus, DeviceStatusMapping } from './define/device_status';

// 使用设备状态常量
if (deviceStatus === DeviceStatus.PRODUCTION) {
    console.log('设备正在生产');
}

// 状态映射
const standardStatus = DeviceStatusMapping.mapStatus('producing');
console.log('标准状态:', standardStatus); // 输出: production

// 获取状态描述
const description = DeviceStatusMapping.getDescription('fault');
console.log('状态描述:', description); // 输出: 报警中 - 设备发生故障或触发报警

// 状态分类判断
if (DeviceStatusMapping.isProductiveStatus(deviceStatus)) {
    console.log('这是生产性状态');
}
```

### JavaScript

```javascript
const { DeviceStatus, DeviceStatusMapping } = require('./define/device_status');

// 使用设备状态常量
if (deviceStatus === DeviceStatus.PRODUCTION) {
    console.log('设备正在生产');
}

// 状态映射
const standardStatus = DeviceStatusMapping.mapStatus('producing');
console.log('标准状态:', standardStatus); // 输出: production

// 获取状态描述
const description = DeviceStatusMapping.getDescription('fault');
console.log('状态描述:', description); // 输出: 报警中 - 设备发生故障或触发报警

// 状态分类判断
if (DeviceStatusMapping.isProductiveStatus(deviceStatus)) {
    console.log('这是生产性状态');
}
```

### Python

```python
from define.device_status import DeviceStatus, DeviceStatusMapping

# 使用设备状态常量
if device_status == DeviceStatus.PRODUCTION:
    print('设备正在生产')

# 状态映射
standard_status = DeviceStatusMapping.map_status('producing')
print(f'标准状态: {standard_status}')  # 输出: production

# 获取状态描述
description = DeviceStatusMapping.get_description('fault')
print(f'状态描述: {description}')  # 输出: 报警中 - 设备发生故障或触发报警

# 状态分类判断
if DeviceStatusMapping.is_productive_status(device_status):
    print('这是生产性状态')
```

## 🔄 智能状态映射

系统支持多种历史状态值自动映射到5个核心状态：

### 映射到生产状态 (`production`)
| 输入状态 | 说明 |
|---------|------|
| `producing` | 生产状态的别名 |
| `working` | 工作状态映射到生产 |
| `running` | 运行状态映射到生产 |
| `active` | 活跃状态映射到生产 |

### 映射到待机状态 (`idle`)
| 输入状态 | 说明 |
|---------|------|
| `standby` | 待机状态的别名 |
| `waiting` | 等待状态映射到待机 |
| `ready` | 就绪状态映射到待机 |
| `maintenance` | 维护状态映射到待机 |
| `adjusting` | 调机状态映射到待机 |

### 映射到报警状态 (`alarm`)
| 输入状态 | 说明 |
|---------|------|
| `fault` | 故障状态映射到报警 |
| `error` | 错误状态映射到报警 |
| `failed` | 失败状态映射到报警 |

### 映射到关机状态 (`shutdown`)
| 输入状态 | 说明 |
|---------|------|
| `stop` | 停止状态映射到关机 |
| `stopped` | 停止状态映射到关机 |
| `offline` | 离线状态映射到关机 |
| `disconnected` | 未连接状态映射到关机 |
| `off` | 关闭状态映射到关机 |

### 映射到未知状态 (`unknown`)
| 输入状态 | 说明 |
|---------|------|
| 空字符串 | 空值映射到未知 |
| 无效状态 | 不在映射表中的状态 |

## 🛠️ 维护指南

### 添加新状态

1. **更新所有语言版本**：在所有4个文件中添加新的状态常量
2. **更新映射表**：在相应的映射表中添加新状态的分类
3. **更新文档**：在README中添加新状态的说明
4. **测试验证**：确保所有项目中的使用都正常

### 修改现有状态

1. **向后兼容**：保留原有状态值，添加映射关系
2. **渐进迁移**：逐步更新各个项目的使用
3. **文档更新**：更新相关文档和注释

### 版本管理

- 使用语义化版本控制
- 重大变更需要更新主版本号
- 新增状态更新次版本号
- 文档修正更新修订版本号

## 📈 项目集成

### 现有项目

以下项目应该使用这些常量定义：

1. **02_device_collect** - 设备数据采集服务
2. **02_generate_data** - 数据生成服务
3. **05_data_push** - 数据推送服务
4. **12_server_api** - 后端API服务
5. **13_server_admin** - 管理后台
6. **20_viewer** - 数据查看器
7. **30_machine_status** - 设备状态监控

### 集成步骤

1. **导入常量定义**：根据项目语言选择对应的文件
2. **替换硬编码**：将项目中的硬编码状态值替换为常量
3. **使用映射函数**：对外部输入的状态值进行标准化处理
4. **更新测试**：确保所有测试用例使用新的常量定义

## ⚠️ 注意事项

1. **数据库兼容性**：确保数据库中存储的状态值与常量定义一致
2. **API兼容性**：API接口的状态值应该使用标准常量
3. **前后端一致性**：前端和后端必须使用相同的状态定义
4. **历史数据处理**：对于历史数据，使用映射函数进行标准化

## 🔍 故障排除

### 常见问题

1. **状态不匹配**：检查是否使用了正确的常量定义
2. **映射失败**：确认输入状态是否在映射表中
3. **分类错误**：验证状态分类映射是否正确

### 调试方法

1. **日志输出**：记录状态转换过程
2. **单元测试**：编写测试验证状态映射逻辑
3. **数据验证**：定期检查数据库中的状态值分布

## 📞 支持

如有问题或建议，请联系：
- **开发团队**：MDC System Team
- **文档维护**：请提交Issue或Pull Request
- **版本更新**：关注项目更新日志
