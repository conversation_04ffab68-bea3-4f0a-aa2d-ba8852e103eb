# MDC系统统一部署配置文件
# 合并了所有子系统的Docker Compose配置
# 包含: 设备采集、数据推送、API服务、管理后台、机器状态看板

services:
  # 设备数据采集服务 (02_device_collect)
  mdc_device_collect:
    image: mdc_device_collect:1.1.0
    container_name: mdc_device_collect
    restart: always
    ports:
      - "9001:8081"
    volumes:
      - ./02_device_collect/data:/data
      - ./02_device_collect/logs:/app/logs
      - ./02_device_collect/config.yml:/app/config.yml
    environment:
      - TZ=Asia/Shanghai
    networks:
      - mdc_full_mdc_network

  # 数据推送服务 (05_data_push)
  mdc_data_push:
    image: mdc_data_push:1.1.2
    container_name: mdc_data_push
    restart: always
    ports:
      - "8085:8085"
    volumes:
      - ./05_data_push/data:/data
      - ./05_data_push/logs:/app/logs
      - ./05_data_push/scripts:/app/scripts
      - ./05_data_push/config.yml:/app/config.yml
    environment:
      - TZ=Asia/Shanghai
    networks:
      - mdc_full_mdc_network

  # API服务器 (12_server_api)
  mdc_server_api:
    image: mdc_server_api:1.1.5
    container_name: mdc_server_api
    restart: always
    ports:
      - "9005:9005"
    volumes:
      - ./12_server_api/data:/app/data
      - ./12_server_api/logs:/app/logs
      - ./12_server_api/config.yml:/app/config.yml
    environment:
      TZ: Asia/Shanghai
      SITE_NAME: 演示智能制造系统2
      SITE_LOGO: /assets/demo-logo.png
      SITE_SUPPORT: 演示科技公司2
      SITE_SUPPORT_INFO: "技术支持: 400-DEMO-222"
    networks:
      - mdc_full_mdc_network

  # 管理后台 (13_server_admin)
  mdc_server_admin:
    image: mdc_server_admin:1.1.3
    container_name: mdc_server_admin
    ports:
      - "9101:80"
    restart: always
    volumes:
      # 挂载配置文件
      - ./13_server_admin/public/config.js:/usr/share/nginx/html/config.js 
      # 图标
      - ./13_server_admin/public/logo192.png:/usr/share/nginx/html/logo192.png
      - ./13_server_admin/public/logo512.png:/usr/share/nginx/html/logo512.png
      # 收藏夹图标
      - ./13_server_admin/public/favicon.ico:/usr/share/nginx/html/favicon.ico
    networks:
      - mdc_full_mdc_network

  # 机器状态看板 (30_machine_status)
  mdc_dashboard:
    image: mdc_dashboard:1.1.12
    container_name: mdc_dashboard
    ports:
      - "${NEXT_PUBLIC_APP_PORT:-9100}:${NEXT_PUBLIC_APP_PORT:-9100}"
    restart: always
    environment:
      # 基础配置
      NODE_ENV: ${NODE_ENV:-production}
      NEXT_TELEMETRY_DISABLED: ${NEXT_TELEMETRY_DISABLED:-1}
      PORT: ${NEXT_PUBLIC_APP_PORT:-9100}
      HOSTNAME: 0.0.0.0

      # 统一的API配置 - 只使用一个环境变量
      NEXT_PUBLIC_API_BASE_URL: ${NEXT_PUBLIC_API_BASE_URL:-http://localhost:9005}
      REACT_APP_API_BASE_URL: http://localhost:9005

      # 调试配置
      NEXT_PUBLIC_DEBUG_API: ${NEXT_PUBLIC_DEBUG_API:-false}
      NEXT_PUBLIC_API_TIMEOUT: ${NEXT_PUBLIC_API_TIMEOUT:-10000}
      NEXT_PUBLIC_API_RETRY_COUNT: ${NEXT_PUBLIC_API_RETRY_COUNT:-3}
    networks:
      - mdc_full_mdc_network
    # 如果需要挂载配置文件，可以取消下面的注释
    # volumes:
    #   - ./30_machine_status/config:/app/config
    #   - ./30_machine_status/data:/app/data
    #   - ./30_machine_status/.env:/app/.env:ro  # 挂载环境变量文件（只读）

# 网络配置
# 使用外部网络，与其他MDC服务共享
# 这个网络需要预先创建: docker network create mdc_full_mdc_network
networks:
  mdc_full_mdc_network:
    external: true
