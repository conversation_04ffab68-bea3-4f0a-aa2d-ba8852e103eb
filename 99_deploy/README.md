# MDC系统统一部署说明

## 概述
这个目录包含了MDC系统所有服务的统一部署配置，将以下5个子系统合并到一个docker-compose.yml文件中：

- **02_device_collect**: 设备数据采集服务 (端口: 9001)
- **05_data_push**: 数据推送服务 (端口: 8085)
- **12_server_api**: API服务器 (端口: 9005)
- **13_server_admin**: 管理后台 (端口: 9101)
- **30_machine_status**: 机器状态看板 (端口: 9100)

## 部署前准备

### 1. 创建Docker网络
```bash
docker network create mdc_full_mdc_network
```

### 2. 确保镜像已构建
确保所有服务的Docker镜像都已构建完成：
- mdc_device_collect:1.0.0
- mdc_data_push:1.0.2
- mdc_server_api:1.0.4
- mdc_server_admin:1.0.3
- mdc_dashboard:1.0.7

### 3. 准备配置文件和数据目录
确保以下目录和文件存在：
```
99_deploy/
├── 02_device_collect/
│   ├── data/
│   ├── logs/
│   └── config.yml
├── 05_data_push/
│   ├── data/
│   ├── logs/
│   ├── scripts/
│   └── config.yml
├── 12_server_api/
│   ├── data/
│   ├── logs/
│   └── config.yml
├── 13_server_admin/
│   └── public/
│       ├── config.js
│       ├── logo192.png
│       ├── logo512.png
│       └── favicon.ico
└── 30_machine_status/
    ├── config/ (可选)
    ├── data/ (可选)
    └── .env (可选)
```

## 部署命令

### 启动所有服务
```bash
cd 99_deploy
docker-compose up -d
```

### 查看服务状态
```bash
docker-compose ps
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f mdc_server_api
```

### 停止所有服务
```bash
docker-compose down
```

### 重启特定服务
```bash
docker-compose restart mdc_server_api
```

## 服务访问地址

- **设备采集服务**: http://localhost:9001
- **数据推送服务**: http://localhost:8085
- **API服务器**: http://localhost:9005
- **管理后台**: http://localhost:9101
- **机器状态看板**: http://localhost:9100

## 环境变量配置

机器状态看板支持以下环境变量配置：
- `NEXT_PUBLIC_APP_PORT`: 应用端口 (默认: 9100)
- `NODE_ENV`: 运行环境 (默认: production)
- `NEXT_PUBLIC_API_BASE_URL`: API基础URL (默认: http://localhost:9005)
- `NEXT_PUBLIC_DEBUG_API`: API调试模式 (默认: false)

可以创建 `.env` 文件来设置这些变量：
```bash
# .env 文件示例
NEXT_PUBLIC_APP_PORT=9100
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=http://localhost:9005
NEXT_PUBLIC_DEBUG_API=false
```

## 注意事项

1. **网络配置**: 所有服务都连接到 `mdc_full_mdc_network` 外部网络，确保服务间可以相互通信
2. **数据持久化**: 各服务的数据、日志和配置文件都通过volume挂载到宿主机
3. **时区设置**: 大部分服务设置为 `Asia/Shanghai` 时区
4. **重启策略**: 所有服务都配置为 `always` 重启策略，确保服务稳定运行

## 故障排除

### 服务无法启动
1. 检查Docker镜像是否存在
2. 检查端口是否被占用
3. 检查配置文件是否正确
4. 检查网络是否创建

### 服务间通信问题
1. 确认所有服务都在同一个Docker网络中
2. 检查防火墙设置
3. 验证服务配置中的地址和端口

### 数据丢失问题
1. 检查volume挂载路径是否正确
2. 确认宿主机目录权限设置
3. 定期备份重要数据
