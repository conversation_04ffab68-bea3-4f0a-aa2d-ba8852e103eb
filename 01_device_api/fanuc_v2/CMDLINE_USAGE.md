# FANUC v2 命令行参数使用说明

## 概述

FANUC v2 程序现在支持通过命令行参数指定目标设备的IP地址和端口号，提供了更灵活的设备连接方式。

## 支持的参数

### `-ip` 参数
- **类型**: 字符串
- **默认值**: `**************`
- **说明**: 指定FANUC CNC设备的IP地址
- **示例**: `-ip *************`

### `-port` 参数
- **类型**: 整数
- **默认值**: `8193`
- **说明**: 指定FOCAS2通信端口号
- **示例**: `-port 8194`

### `-h` 参数
- **说明**: 显示帮助信息
- **示例**: `-h`

## 使用示例

### 1. 使用默认参数
```bash
./fanuc_v2
```
- 连接到: `**************:8193`

### 2. 指定IP地址
```bash
./fanuc_v2 -ip *************
```
- 连接到: `*************:8193`

### 3. 指定端口号
```bash
./fanuc_v2 -port 8194
```
- 连接到: `**************:8194`

### 4. 同时指定IP和端口
```bash
./fanuc_v2 -ip ************* -port 8194
```
- 连接到: `*************:8194`

### 5. 查看帮助信息
```bash
./fanuc_v2 -h
```

## 编译和运行

### 编译程序
```bash
# 启用CGO编译
CGO_ENABLED=1 go build -o fanuc_v2 main.go
```

### 运行程序
```bash
# 使用默认参数
./fanuc_v2

# 指定设备参数
./fanuc_v2 -ip ************* -port 8193
```

## 日志记录

程序会将命令行参数信息记录到日志文件中：

- **日志文件**: `logs/fanuc_v2_demo.log`
- **记录内容**: 
  - 命令行参数解析结果
  - 目标设备信息
  - 连接状态和结果

### 查看日志
```bash
# 查看完整日志
cat logs/fanuc_v2_demo.log

# 实时监控日志
tail -f logs/fanuc_v2_demo.log

# 查看参数相关日志
grep "命令行参数\|目标设备" logs/fanuc_v2_demo.log
```

## 错误处理

### 无效参数
如果提供了无效的参数，程序会显示错误信息并退出：
```bash
./fanuc_v2 -invalid
# 输出: flag provided but not defined: -invalid
```

### 连接失败
如果无法连接到指定的设备，程序会：
1. 记录错误信息到日志
2. 显示连接失败消息
3. 安全退出程序

## 注意事项

1. **IP地址格式**: 必须是有效的IPv4地址格式
2. **端口范围**: 端口号必须在1-65535范围内
3. **设备可达性**: 确保目标设备IP地址可以访问
4. **防火墙设置**: 确保目标端口没有被防火墙阻止
5. **FANUC设置**: 确保FANUC设备已启用FOCAS2/Ethernet功能

## 常见问题

### Q: 如何确定FANUC设备的IP地址？
A: 可以通过FANUC设备的操作面板查看网络设置，或使用网络扫描工具查找设备。

### Q: 端口8193被占用怎么办？
A: 可以尝试使用其他端口号，或检查FANUC设备的端口配置。

### Q: 程序编译失败怎么办？
A: 确保已安装CGO环境和FOCAS2库文件，并设置正确的编译参数。

### Q: 连接超时怎么办？
A: 检查网络连接、IP地址正确性、防火墙设置和设备状态。

## 技术实现

### 代码结构
- **参数定义**: 使用`flag`包定义命令行参数
- **参数解析**: `parseCommandLineArgs()`函数处理参数解析
- **参数使用**: 在连接函数中使用解析后的参数值
- **日志记录**: 将参数信息记录到企业级日志系统

### 关键函数
- `parseCommandLineArgs()`: 解析命令行参数
- `initializeConnection()`: 使用参数建立连接
- `main()`: 程序入口，调用参数解析

## 更新历史

- **v2.1**: 添加命令行参数支持
- **v2.0**: 基础FOCAS2功能实现
- **v1.0**: 初始版本
