/**
 * FANUC设备采集器日志管理模块
 *
 * 功能概述：
 * 本模块提供FANUC设备采集器的完整日志管理功能，基于logrus和lumberjack库
 * 实现结构化日志记录、日志轮转、多级别日志输出等企业级日志管理特性
 *
 * 主要功能：
 * - 多级别日志：支持Debug、Info、Warn、Error、Fatal五个日志级别
 * - 多种格式：支持JSON结构化格式和文本可读格式
 * - 多种输出：支持控制台输出、文件输出、标准错误输出
 * - 日志轮转：自动日志文件轮转、压缩、清理功能
 * - 统一接口：提供全局统一的日志记录接口
 * - 配置灵活：支持运行时的日志配置调整
 *
 * 技术特性：
 * - 基于logrus：使用业界标准的logrus日志库
 * - 日志轮转：使用lumberjack实现自动日志轮转
 * - 结构化日志：支持JSON格式的结构化日志输出
 * - 时间格式化：自定义时间戳格式，便于日志分析
 * - 颜色支持：控制台输出支持颜色高亮
 * - 线程安全：支持并发环境下的安全日志记录
 *
 * 日志级别说明：
 * - Debug：调试信息，开发阶段使用，生产环境通常关闭
 * - Info：一般信息，记录程序正常运行的关键节点
 * - Warn：警告信息，记录可能的问题但不影响程序运行
 * - Error：错误信息，记录程序运行中的错误但程序可继续
 * - Fatal：致命错误，记录导致程序无法继续运行的错误
 *
 * 输出格式：
 * - JSON格式：结构化日志，便于日志分析工具处理
 * - 文本格式：人类可读格式，便于开发调试
 *
 * 轮转策略：
 * - 按大小轮转：单个文件达到指定大小时自动轮转
 * - 按时间清理：超过指定天数的日志文件自动删除
 * - 备份数量：保留指定数量的历史日志文件
 * - 自动压缩：历史日志文件自动压缩节省空间
 *
 * 应用场景：
 * - 开发调试：详细的调试信息输出，便于问题排查
 * - 生产监控：关键业务节点的日志记录，便于运维监控
 * - 故障诊断：错误和异常信息的详细记录，便于故障分析
 * - 性能分析：关键操作的时间和性能数据记录
 * - 审计追踪：重要操作的完整审计日志
 *
 * 业务价值：
 * - 问题排查：详细的日志信息便于快速定位和解决问题
 * - 运维监控：实时的系统运行状态监控和告警
 * - 性能优化：基于日志数据的性能分析和优化
 * - 合规审计：完整的操作审计日志，满足合规要求
 *
 * @package logger
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package logger

/**
 * 标准库和第三方库导入
 *
 * 标准库：
 * - io: 输入输出接口，用于日志写入器
 * - os: 操作系统接口，用于文件操作和标准输出
 * - path/filepath: 文件路径操作，用于日志目录管理
 *
 * 第三方库：
 * - github.com/sirupsen/logrus: 结构化日志库，提供多级别日志功能
 * - gopkg.in/natefinch/lumberjack.v2: 日志轮转库，提供自动轮转功能
 */
import (
	"io"            /** 输入输出接口，用于日志写入器 */
	"os"            /** 操作系统接口，用于文件和标准输出 */
	"path/filepath" /** 文件路径操作，用于日志目录管理 */

	"github.com/sirupsen/logrus"       /** 结构化日志库，提供多级别日志 */
	"gopkg.in/natefinch/lumberjack.v2" /** 日志轮转库，提供自动轮转 */
)

/**
 * 全局日志实例
 *
 * 功能：提供全局统一的日志记录接口，基于logrus实现
 *
 * 特性：
 * - 全局单例：整个应用程序共享同一个日志实例
 * - 线程安全：支持并发环境下的安全日志记录
 * - 多级别：支持Debug、Info、Warn、Error、Fatal五个级别
 * - 多格式：支持JSON结构化和文本可读两种格式
 * - 多输出：支持控制台、文件、标准错误等多种输出
 *
 * 使用方式：
 * - 直接调用：logger.Info("消息")
 * - 格式化调用：logger.Infof("格式化消息: %s", value)
 * - 结构化调用：logger.WithFields(fields).Info("消息")
 *
 * 初始化：
 * 通过InitLogger()或InitLoggerWithRotation()函数进行初始化
 */
var Logger *logrus.Logger

/**
 * 日志轮转配置结构体
 *
 * 功能：定义日志文件的自动轮转、压缩和清理策略
 *
 * 轮转机制：
 * - 大小轮转：当日志文件达到MaxSize时自动创建新文件
 * - 时间清理：超过MaxAge天数的日志文件自动删除
 * - 数量限制：最多保留MaxBackups个历史日志文件
 * - 自动压缩：历史日志文件可选择自动压缩
 *
 * 配置建议：
 * - 开发环境：MaxSize=10MB, MaxAge=1天, MaxBackups=3, Compress=false
 * - 测试环境：MaxSize=50MB, MaxAge=3天, MaxBackups=5, Compress=true
 * - 生产环境：MaxSize=100MB, MaxAge=7天, MaxBackups=10, Compress=true
 *
 * @struct LogRotationConfig
 */
type LogRotationConfig struct {
	/** 是否启用日志轮转功能，false时使用普通文件输出 */
	Enabled bool

	/** 单个日志文件的最大大小（MB），达到此大小时自动轮转 */
	MaxSize int

	/** 日志文件的保留天数，超过此天数的文件自动删除 */
	MaxAge int

	/** 保留的历史日志文件数量，超过此数量的最老文件被删除 */
	MaxBackups int

	/** 是否压缩历史日志文件，true时使用gzip压缩节省空间 */
	Compress bool
}

// InitLogger 初始化FANUC v2日志系统
// 根据配置参数设置日志级别、格式和输出目标
// 支持文本和JSON格式，可输出到控制台或文件，支持日志轮转
// 参数:
//   - level: 日志级别，支持"debug", "info", "warn", "error"
//   - format: 日志格式，"json"为结构化格式，其他为文本格式
//   - output: 输出目标，"stdout"为控制台，其他为文件路径
//
// 示例:
//
//	InitLogger("info", "json", "logs/fanuc_v2.log")
//	InitLogger("debug", "text", "stdout")
func InitLogger(level, format, output string) {
	// 使用默认的轮转配置：自动分拆、保留3天、自动压缩
	rotation := LogRotationConfig{
		Enabled:    true,
		MaxSize:    100,  // 100MB
		MaxAge:     3,    // 保留3天
		MaxBackups: 10,   // 保留10个备份文件
		Compress:   true, // 自动压缩
	}
	InitLoggerWithRotation(level, format, output, rotation)
}

// InitLoggerWithRotation 初始化带日志轮转功能的FANUC v2日志系统
// 支持日志文件自动轮转、压缩和清理功能
// 参数:
//   - level: 日志级别，支持"debug", "info", "warn", "error"
//   - format: 日志格式，"json"为结构化格式，其他为文本格式
//   - output: 输出目标，"stdout"为控制台，其他为文件路径
//   - rotation: 日志轮转配置
//
// 示例:
//
//	rotation := LogRotationConfig{
//	    Enabled: true,
//	    MaxSize: 100,    // 100MB
//	    MaxAge: 3,       // 3天
//	    MaxBackups: 10,  // 10个备份文件
//	    Compress: true,  // 压缩旧文件
//	}
//	InitLoggerWithRotation("info", "json", "logs/fanuc_v2.log", rotation)
func InitLoggerWithRotation(level, format, output string, rotation LogRotationConfig) {
	// 创建新的日志实例
	Logger = logrus.New()

	// 根据字符串设置日志级别
	// 日志级别从低到高：debug < info < warn < error < fatal
	switch level {
	case "debug":
		Logger.SetLevel(logrus.DebugLevel) // 显示所有级别的日志
	case "info":
		Logger.SetLevel(logrus.InfoLevel) // 显示info及以上级别的日志
	case "warn":
		Logger.SetLevel(logrus.WarnLevel) // 显示warn及以上级别的日志
	case "error":
		Logger.SetLevel(logrus.ErrorLevel) // 只显示error和fatal级别的日志
	default:
		Logger.SetLevel(logrus.InfoLevel) // 默认使用info级别
	}

	// 根据配置设置日志输出格式
	if format == "json" {
		// JSON格式：结构化日志，便于日志分析工具处理
		Logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05", // 自定义时间格式
		})
	} else {
		// 文本格式：人类可读，便于开发调试
		Logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,                  // 显示完整时间戳
			TimestampFormat: "2006-01-02 15:04:05", // 自定义时间格式
			ForceColors:     true,                  // 强制使用颜色
		})
	}

	// 设置日志输出目标
	var writer io.Writer
	if output == "" || output == "stdout" {
		// 输出到标准输出
		writer = os.Stdout
	} else if output == "stderr" {
		// 输出到标准错误
		writer = os.Stderr
	} else {
		// 输出到文件
		if rotation.Enabled {
			// 启用日志轮转
			writer = createRotatingWriter(output, rotation)
		} else {
			// 普通文件输出
			file, err := os.OpenFile(output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				// 文件打开失败，回退到标准输出
				Logger.Errorf("Failed to open log file %s: %v, falling back to stdout", output, err)
				writer = os.Stdout
			} else {
				writer = file
			}
		}
	}

	Logger.SetOutput(writer)

	// 记录日志系统初始化信息
	if rotation.Enabled && output != "stdout" && output != "stderr" {
		Logger.Infof("FANUC v2 Logger initialized with rotation: file=%s, maxSize=%dMB, maxAge=%dd, maxBackups=%d, compress=%v",
			output, rotation.MaxSize, rotation.MaxAge, rotation.MaxBackups, rotation.Compress)
	} else {
		Logger.Infof("FANUC v2 Logger initialized: level=%s, format=%s, output=%s", level, format, output)
	}
}

// createRotatingWriter 创建支持轮转的日志写入器
// 使用lumberjack库实现日志文件的自动轮转、压缩和清理
// 参数:
//   - filename: 日志文件路径
//   - rotation: 日志轮转配置
//
// 返回:
//   - io.Writer: 支持轮转的写入器
func createRotatingWriter(filename string, rotation LogRotationConfig) io.Writer {
	// 确保日志目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		// 目录创建失败，记录错误并回退到标准输出
		logrus.Errorf("Failed to create log directory %s: %v, falling back to stdout", dir, err)
		return os.Stdout
	}

	// 设置默认值
	maxSize := rotation.MaxSize
	if maxSize <= 0 {
		maxSize = 100 // 默认100MB
	}

	maxAge := rotation.MaxAge
	if maxAge <= 0 {
		maxAge = 3 // 默认3天（按需求设置）
	}

	maxBackups := rotation.MaxBackups
	if maxBackups <= 0 {
		maxBackups = 10 // 默认10个备份文件
	}

	// 创建lumberjack轮转写入器
	return &lumberjack.Logger{
		Filename:   filename,          // 日志文件路径
		MaxSize:    maxSize,           // 单个文件最大大小(MB)
		MaxAge:     maxAge,            // 文件保留天数
		MaxBackups: maxBackups,        // 保留的备份文件数量
		Compress:   rotation.Compress, // 是否压缩旧文件
		LocalTime:  true,              // 使用本地时间
	}
}

// Info 记录信息级别日志
// 用于记录一般的程序运行信息，如服务启动、配置加载等
// 参数:
//   - args: 可变参数，支持多个值的输出
//
// 示例:
//
//	Info("FANUC设备连接成功")
//	Info("数据采集启动:", deviceID)
func Info(args ...interface{}) {
	if Logger != nil {
		Logger.Info(args...)
	}
}

// Infof 记录格式化的信息级别日志
// 支持printf风格的格式化字符串，便于输出结构化信息
// 参数:
//   - format: 格式化字符串，支持%s, %d, %v等占位符
//   - args: 格式化参数
//
// 示例:
//
//	Infof("[%s] 设备连接成功，句柄: %d", deviceID, handle)
//	Infof("[%s] 数据采集成功: %d 条记录", deviceID, count)
func Infof(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Infof(format, args...)
	}
}

// Debug 记录调试级别日志
// 用于开发和调试阶段的详细信息输出
// 只在日志级别设置为debug时才会输出
// 参数:
//   - args: 可变参数，支持多个值的输出
//
// 示例:
//
//	Debug("进入FOCAS函数调用")
//	Debug("句柄值:", handle)
func Debug(args ...interface{}) {
	if Logger != nil {
		Logger.Debug(args...)
	}
}

// Debugf 记录格式化的调试级别日志
// 支持详细的调试信息输出，便于问题排查
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
//
// 示例:
//
//	Debugf("[%s] FOCAS调用: %s, 返回值: %d", deviceID, funcName, ret)
//	Debugf("[%s] 数据解析: %+v", deviceID, data)
func Debugf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Debugf(format, args...)
	}
}

// Warn 记录警告级别日志
// 用于记录可能的问题或异常情况，但不影响程序继续运行
// 参数:
//   - args: 可变参数，支持多个值的输出
//
// 示例:
//
//	Warn("设备连接超时，正在重试")
//	Warn("数据推送失败，使用缓存")
func Warn(args ...interface{}) {
	if Logger != nil {
		Logger.Warn(args...)
	}
}

// Warnf 记录格式化的警告级别日志
// 用于输出格式化的警告信息
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
//
// 示例:
//
//	Warnf("[%s] 连接重试 %d/%d 次", deviceID, current, max)
//	Warnf("[%s] 内存使用率达到 %d%%", deviceID, usage)
func Warnf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Warnf(format, args...)
	}
}

// Error 记录错误级别日志
// 用于记录程序运行中的错误，但程序可以继续运行
// 参数:
//   - args: 可变参数，支持多个值的输出
//
// 示例:
//
//	Error("FOCAS函数调用失败")
//	Error("数据推送错误:", err)
func Error(args ...interface{}) {
	if Logger != nil {
		Logger.Error(args...)
	}
}

// Errorf 记录格式化的错误级别日志
// 用于输出详细的错误信息，便于问题定位
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
//
// 示例:
//
//	Errorf("[%s] FOCAS错误: 函数=%s, 错误码=%d", deviceID, funcName, errorCode)
//	Errorf("[%s] 连接失败: %v", deviceID, err)
func Errorf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Errorf(format, args...)
	}
}

// Fatal 记录致命错误日志并退出程序
// 用于记录导致程序无法继续运行的严重错误
// 调用后程序会立即退出（os.Exit(1)）
// 参数:
//   - args: 可变参数，支持多个值的输出
//
// 示例:
//
//	Fatal("FOCAS库初始化失败，程序无法启动")
//	Fatal("配置文件加载失败:", err)
func Fatal(args ...interface{}) {
	if Logger != nil {
		Logger.Fatal(args...)
	}
}

// Fatalf 记录格式化的致命错误日志并退出程序
// 支持格式化输出致命错误信息
// 调用后程序会立即退出（os.Exit(1)）
// 参数:
//   - format: 格式化字符串
//   - args: 格式化参数
//
// 示例:
//
//	Fatalf("端口 %d 已被占用，程序无法启动", port)
//	Fatalf("FOCAS库版本不兼容: %v", err)
func Fatalf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Fatalf(format, args...)
	}
}
