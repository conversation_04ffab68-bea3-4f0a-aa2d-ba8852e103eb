#!/bin/bash

# FANUC设备采集器启动脚本
# 在fanuc-focas-dev-x86 Docker容器中启动FANUC v2设备采集服务

echo "🚀 启动 FANUC设备采集器 v2.0 (Docker容器版)"
echo "=================================================="

# Docker容器配置
CONTAINER_NAME="fanuc-focas-dev-x86"
CONTAINER_WORK_DIR="/app/src"
CONTAINER_FWLIB_DIR="/usr/local/lib"
HOST_PROJECT_DIR="$(pwd)"

# 检查Docker是否可用
echo "🐳 检查Docker环境..."
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装或不在PATH中"
    echo "请确保Docker已正确安装并启动"
    exit 1
fi

# 检查Docker服务是否运行
if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker服务未运行"
    echo "请启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查是否在正确的目录
if [ ! -f "main.go" ]; then
    echo "❌ 错误: 未找到main.go文件"
    echo "请确保在01_device_api/fanuc_v2目录中运行此脚本"
    exit 1
fi

# 检查容器是否存在
echo "🔍 检查Docker容器..."
if ! docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "❌ 错误: 未找到Docker容器 ${CONTAINER_NAME}"
    echo "请确保容器已创建。创建命令示例："
    echo "./build-x86.sh run-it"
    exit 1
fi

# 检查容器是否运行
if ! docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "🔄 启动Docker容器..."
    docker start ${CONTAINER_NAME}
    if [ $? -ne 0 ]; then
        echo "❌ 错误: 启动Docker容器失败"
        exit 1
    fi
    # 等待容器完全启动
    echo "⏳ 等待容器完全启动..."
    sleep 5

    # 验证容器是否真正启动
    if ! docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo "❌ 错误: 容器启动后立即退出，请检查容器配置"
        echo "查看容器日志: docker logs ${CONTAINER_NAME}"
        exit 1
    fi
fi

echo "✅ Docker容器 ${CONTAINER_NAME} 运行中"

# 检查Docker守护进程状态
echo "🔍 检查Docker守护进程状态..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker守护进程不稳定"
    echo "建议重启Docker Desktop"
    exit 1
fi

# 检查容器内的Go环境
echo "🔧 检查容器内Go环境..."
if ! docker exec ${CONTAINER_NAME} go version &> /dev/null; then
    echo "❌ 错误: 容器内Go环境不可用"
    echo "请确保容器镜像包含Go运行环境"
    exit 1
fi

# 显示容器内Go版本信息
GO_VERSION=$(docker exec ${CONTAINER_NAME} go version)
echo "✅ 容器内Go版本: ${GO_VERSION}"

# 检查容器内FOCAS2库文件

if ! docker exec ${CONTAINER_NAME} bash -c "test -f '${CONTAINER_FWLIB_DIR}/libfwlib32.so' || test -f '${CONTAINER_FWLIB_DIR}/fwlib/libfwlib32.a'"; then
    echo "❌ 错误: 容器内未找到FOCAS2库文件"
    echo "请确保FOCAS2库文件已正确安装"
    exit 1
fi

# if ! docker exec ${CONTAINER_NAME} test -f "${CONTAINER_FWLIB_DIR}/fwlib32.h"; then
#     echo "❌ 错误: 容器内未找到FOCAS2头文件 fwlib32.h"
#     echo "请确保FOCAS2库文件已正确安装"
#     exit 1
# fi

echo "✅ 容器内FOCAS2库文件检查通过"

# 检查配置文件
echo "🔍 检查配置文件..."
if ! docker exec ${CONTAINER_NAME} test -f "${CONTAINER_WORK_DIR}/config.json"; then
    echo "⚠️  警告: 未找到config.json配置文件"
    echo "将使用默认配置运行"
else
    echo "✅ 配置文件检查通过"
fi

# 检查端口是否被占用
PORT=9010
echo "🔍 检查端口 ${PORT}..."
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  警告: 主机端口 $PORT 已被占用"
    echo "这可能是容器内的服务正在运行，这是正常的"
    echo "如果需要重启服务，请先停止容器内的进程"
fi

# 在容器内准备运行环境
echo "📁 在容器内准备运行环境..."

# 等待一下确保Docker守护进程稳定
sleep 2

# 使用单个Docker命令完成所有准备工作
docker exec ${CONTAINER_NAME} bash -c "
    set -e
    echo '📁 创建必要目录...'
    mkdir -p ${CONTAINER_WORK_DIR}/logs ${CONTAINER_WORK_DIR}/cache ${CONTAINER_WORK_DIR}/data

    echo '📂 切换到工作目录...'
    cd ${CONTAINER_WORK_DIR}

    echo '📦 下载Go依赖...'
    go mod tidy

    echo '✅ 环境准备完成'
"

if [ $? -ne 0 ]; then
    echo "❌ 错误: 容器内环境准备失败"
    echo "💡 建议："
    echo "   1. 检查容器是否正常运行: docker ps"
    echo "   2. 检查容器内Go环境: docker exec ${CONTAINER_NAME} go version"
    echo "   3. 检查网络连接"
    echo "   4. 查看容器日志: docker logs ${CONTAINER_NAME}"
    exit 1
fi

echo "✅ 容器内环境准备完成"

echo ""
echo "🌐 在Docker容器内启动FANUC设备采集器..."
echo "容器名称: ${CONTAINER_NAME}"
echo "工作目录: ${CONTAINER_WORK_DIR}"
echo "端口映射: ${PORT}:${PORT}"
echo "访问地址: http://localhost:${PORT}"
echo "API文档: http://localhost:${PORT}/api/v1"
echo ""
echo "功能特性:"
echo "  - FANUC FOCAS2设备采集"
echo "  - 实时数据推送"
echo "  - REST API接口"
echo "  - 设备状态监控"
echo "  - 数据缓存和重试"
echo ""
echo "容器管理命令:"
echo "  查看日志: docker logs -f ${CONTAINER_NAME}"
echo "  进入容器: docker exec -it ${CONTAINER_NAME} bash"
echo "  停止容器: docker stop ${CONTAINER_NAME}"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=================================================="

# 最终检查Docker环境稳定性
echo "🔍 最终检查Docker环境稳定性..."
if ! docker exec ${CONTAINER_NAME} echo "Docker连接正常" >/dev/null 2>&1; then
    echo "❌ 错误: Docker连接不稳定"
    echo "建议重启Docker Desktop后重试"
    exit 1
fi

# 在容器内启动FANUC设备采集器
echo "🚀 启动FANUC设备采集器..."
echo "💡 提示: 程序启动后将持续运行，按 Ctrl+C 可以停止"
echo ""

# 使用更稳定的方式启动程序
docker exec -w ${CONTAINER_WORK_DIR} ${CONTAINER_NAME} bash -c "
    echo '🎯 FANUC Data Collector v2 启动中...'
    go run cmd/main.go
"
