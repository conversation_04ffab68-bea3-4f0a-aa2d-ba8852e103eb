#!/bin/bash

# FANUC v2 运行时 FOCAS 脚本执行构建脚本
# 功能：构建包含运行时 FOCAS 脚本执行的优化镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
IMAGE_NAME="mdc_dcf"
IMAGE_TAG="2.0.7-runtime"
DOCKERFILE="Dockerfile"

echo "=================================================================="
echo "           FANUC v2 运行时 FOCAS 脚本执行构建"
echo "=================================================================="
echo

# 显示构建信息
echo -e "${BLUE}📋 构建信息:${NC}"
echo "   镜像名称: ${IMAGE_NAME}:${IMAGE_TAG}"
echo "   Dockerfile: ${DOCKERFILE}"
echo "   构建类型: 多阶段构建 + 运行时FOCAS脚本执行"
echo "   目标架构: linux/386"
echo

# 检查必要文件
echo -e "${BLUE}📂 检查构建环境...${NC}"

if [ ! -f "${DOCKERFILE}" ]; then
    echo -e "${RED}❌ Dockerfile 不存在${NC}"
    exit 1
fi

if [ ! -d "fwlib" ]; then
    echo -e "${RED}❌ fwlib 目录不存在${NC}"
    exit 1
fi

# 检查 fwlib 脚本
echo -e "${BLUE}📋 检查 fwlib 脚本:${NC}"
if [ -f "fwlib/build-deps.sh" ]; then
    echo -e "${GREEN}✅ build-deps.sh 存在${NC}"
    echo "   内容预览: $(head -3 fwlib/build-deps.sh | tail -1)"
else
    echo -e "${YELLOW}⚠️  build-deps.sh 不存在${NC}"
fi

if [ -f "fwlib/install.sh" ]; then
    echo -e "${GREEN}✅ install.sh 存在${NC}"
    echo "   内容预览: $(head -3 fwlib/install.sh | tail -1)"
else
    echo -e "${YELLOW}⚠️  install.sh 不存在${NC}"
fi

if [ -f "fwlib/libfwlib32-linux-x86.so.1.0.5" ]; then
    echo -e "${GREEN}✅ FOCAS库文件存在${NC}"
    echo "   文件大小: $(ls -lh fwlib/libfwlib32-linux-x86.so.1.0.5 | awk '{print $5}')"
else
    echo -e "${YELLOW}⚠️  FOCAS库文件不存在${NC}"
fi

if [ -f "fwlib/fwlib32.h" ]; then
    echo -e "${GREEN}✅ FOCAS头文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  FOCAS头文件不存在${NC}"
fi

echo -e "${GREEN}✅ 构建环境检查完成${NC}"
echo

# 清理旧镜像
echo -e "${BLUE}🧹 清理旧镜像...${NC}"
if docker images | grep -q "${IMAGE_NAME}.*${IMAGE_TAG}"; then
    docker rmi "${IMAGE_NAME}:${IMAGE_TAG}" 2>/dev/null || true
    echo -e "${GREEN}✅ 旧镜像已清理${NC}"
else
    echo -e "${YELLOW}⚠️  没有找到旧镜像${NC}"
fi

# 开始构建
echo -e "${BLUE}🔨 开始构建镜像...${NC}"
echo "=================================================================="

# 记录构建开始时间
BUILD_START=$(date +%s)

# 执行Docker构建
docker build \
    --no-cache \
    --platform linux/386 \
    --tag "${IMAGE_NAME}:${IMAGE_TAG}" \
    --file "${DOCKERFILE}" \
    --progress=plain \
    .

# 记录构建结束时间
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

echo "=================================================================="
echo -e "${GREEN}🎉 构建完成！${NC}"
echo

# 显示构建结果
echo -e "${BLUE}📊 构建结果:${NC}"
echo "   构建时间: ${BUILD_TIME} 秒"

# 显示镜像信息
echo -e "${BLUE}🖼️  镜像信息:${NC}"
docker images "${IMAGE_NAME}:${IMAGE_TAG}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 验证镜像内容
echo
echo -e "${BLUE}🔍 验证镜像内容...${NC}"
echo "=================================================================="

# 检查是否包含源代码文件
echo -e "${BLUE}🔍 检查源代码文件:${NC}"
SOURCE_FILES=$(docker run --rm "${IMAGE_NAME}:${IMAGE_TAG}" find /app -name "*.go" 2>/dev/null | wc -l)
if [ "$SOURCE_FILES" -eq 0 ]; then
    echo -e "${GREEN}✅ 镜像中不包含Go源代码文件${NC}"
else
    echo -e "${RED}❌ 镜像中仍包含 $SOURCE_FILES 个Go源代码文件${NC}"
fi

# 检查可执行文件
echo -e "${BLUE}🔍 检查可执行文件:${NC}"
docker run --rm "${IMAGE_NAME}:${IMAGE_TAG}" ls -la /app/bin/

# 检查FOCAS库
echo -e "${BLUE}🔍 检查FOCAS库:${NC}"
docker run --rm "${IMAGE_NAME}:${IMAGE_TAG}" ls -la /usr/local/lib/libfwlib32* 2>/dev/null || echo "FOCAS库文件未找到"

# 检查动态链接配置
echo -e "${BLUE}🔍 检查动态链接配置:${NC}"
docker run --rm "${IMAGE_NAME}:${IMAGE_TAG}" cat /etc/ld.so.conf.d/focas.conf 2>/dev/null || echo "FOCAS配置文件未找到"

# 检查动态链接器
echo -e "${BLUE}🔍 检查动态链接器:${NC}"
docker run --rm "${IMAGE_NAME}:${IMAGE_TAG}" ldconfig -p | grep fwlib || echo "动态链接器中未找到FOCAS库"

# 测试启动脚本
echo
echo -e "${BLUE}🚀 测试启动脚本...${NC}"
echo "=================================================================="
docker run --rm --name fanuc_test_startup "${IMAGE_NAME}:${IMAGE_TAG}" /bin/bash -c "
    echo '测试启动脚本中的FOCAS检查...'
    /app/entrypoint.sh /bin/bash -c 'echo 启动脚本测试完成'
" | head -30

echo
echo "=================================================================="
echo -e "${GREEN}✅ 运行时 FOCAS 脚本执行构建完成！${NC}"
echo
echo -e "${BLUE}📝 使用方法:${NC}"
echo "1. 直接运行:"
echo "   docker run --rm --name fanuc_runtime_test ${IMAGE_NAME}:${IMAGE_TAG}"
echo
echo "2. 进入容器检查FOCAS配置:"
echo "   docker run --rm -it ${IMAGE_NAME}:${IMAGE_TAG} /bin/bash"
echo
echo "3. 检查FOCAS库链接:"
echo "   docker run --rm ${IMAGE_NAME}:${IMAGE_TAG} ldconfig -p | grep fwlib"
echo
echo "4. 更新docker-compose.yml使用新镜像:"
echo "   image: ${IMAGE_NAME}:${IMAGE_TAG}"
echo "=================================================================="
