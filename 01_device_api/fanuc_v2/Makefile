# FANUC Data Collector v2 Makefile

# 应用信息
APP_NAME = fanuc-collector-v2
VERSION = 2.0.0
BUILD_TIME = $(shell date +%Y-%m-%d\ %H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建参数
LDFLAGS = -ldflags "-X 'main.AppVersion=$(VERSION)' -X 'main.BuildTime=$(BUILD_TIME)' -X 'main.GitCommit=$(GIT_COMMIT)'"
BUILD_DIR = build
BINARY = $(BUILD_DIR)/$(APP_NAME)

# Go参数
GOCMD = go
GOBUILD = $(GOCMD) build
GOCLEAN = $(GOCMD) clean
GOTEST = $(GOCMD) test
GOGET = $(GOCMD) get
GOMOD = $(GOCMD) mod

# Docker参数
DOCKER_IMAGE = fanuc-collector-v2
DOCKER_TAG = $(VERSION)

.PHONY: all build clean test deps run dev docker help

# 默认目标
all: clean deps build

# 构建应用
build:
	@echo "🔨 构建应用..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=1 $(GOBUILD) $(LDFLAGS) -o $(BINARY) ./cmd/main.go
	@echo "✅ 构建完成: $(BINARY)"

# 构建Linux版本 (用于Docker)
build-linux:
	@echo "🔨 构建Linux版本..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux ./cmd/main.go
	@echo "✅ Linux版本构建完成"

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	@rm -rf $(BUILD_DIR)
	$(GOCLEAN)
	@echo "✅ 清理完成"

# 运行测试
test:
	@echo "🧪 运行测试..."
	$(GOTEST) -v ./...
	@echo "✅ 测试完成"

# 测试数据格式兼容性
test-format:
	@echo "🧪 测试数据格式兼容性..."
	./test_data_format.sh
	@echo "✅ 数据格式测试完成"

# 安装依赖
deps:
	@echo "📦 安装依赖..."
	$(GOMOD) download
	$(GOMOD) tidy
	@echo "✅ 依赖安装完成"

# 运行应用 (开发模式)
run: build
	@echo "🚀 启动应用..."
	./$(BINARY) -config config.json

# 开发模式 (直接运行源码)
dev:
	@echo "🔧 开发模式启动..."
	$(GOCMD) run ./cmd/main.go -config config.json

# 在Docker中运行 (用于FOCAS开发)
run-docker:
	@echo "🐳 在Docker中运行..."
	docker exec -it fanuc-focas-dev-x86 /bin/bash -c "cd /app/src && make dev"

# 构建Docker镜像
docker: build-linux
	@echo "🐳 构建Docker镜像..."
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	docker tag $(DOCKER_IMAGE):$(DOCKER_TAG) $(DOCKER_IMAGE):latest
	@echo "✅ Docker镜像构建完成"

# 运行Docker容器
docker-run:
	@echo "🐳 运行Docker容器..."
	docker run -d \
		--name $(APP_NAME) \
		-p 9005:9005 \
		-v $(PWD)/config.json:/app/config.json \
		-v $(PWD)/devices.json:/app/devices.json \
		$(DOCKER_IMAGE):latest

# 停止Docker容器
docker-stop:
	@echo "🛑 停止Docker容器..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

# 查看日志
logs:
	@echo "📋 查看应用日志..."
	docker logs -f $(APP_NAME)

# 格式化代码
fmt:
	@echo "🎨 格式化代码..."
	$(GOCMD) fmt ./...
	@echo "✅ 代码格式化完成"

# 代码检查
lint:
	@echo "🔍 代码检查..."
	golangci-lint run
	@echo "✅ 代码检查完成"

# 生成API文档
docs:
	@echo "📚 生成API文档..."
	swag init -g cmd/main.go
	@echo "✅ API文档生成完成"

# 安装开发工具
install-tools:
	@echo "🛠️  安装开发工具..."
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint
	$(GOGET) -u github.com/swaggo/swag/cmd/swag
	@echo "✅ 开发工具安装完成"

# 创建发布包
release: clean deps test build
	@echo "📦 创建发布包..."
	@mkdir -p $(BUILD_DIR)/release
	@cp $(BINARY) $(BUILD_DIR)/release/
	@cp config.json $(BUILD_DIR)/release/config.example.json
	@cp devices.json $(BUILD_DIR)/release/devices.example.json
	@cp README.md $(BUILD_DIR)/release/ 2>/dev/null || true
	@cd $(BUILD_DIR) && tar -czf $(APP_NAME)-$(VERSION).tar.gz release/
	@echo "✅ 发布包创建完成: $(BUILD_DIR)/$(APP_NAME)-$(VERSION).tar.gz"

# 性能测试
benchmark:
	@echo "⚡ 运行性能测试..."
	$(GOTEST) -bench=. -benchmem ./...
	@echo "✅ 性能测试完成"

# 代码覆盖率
coverage:
	@echo "📊 生成代码覆盖率报告..."
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "✅ 覆盖率报告生成完成: coverage.html"

# 安全检查
security:
	@echo "🔒 运行安全检查..."
	gosec ./...
	@echo "✅ 安全检查完成"

# 依赖检查
deps-check:
	@echo "🔍 检查依赖..."
	$(GOMOD) verify
	nancy sleuth
	@echo "✅ 依赖检查完成"

# 显示帮助信息
help:
	@echo "FANUC Data Collector v2 - 构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  build         构建应用"
	@echo "  build-linux   构建Linux版本"
	@echo "  clean         清理构建文件"
	@echo "  test          运行测试"
	@echo "  test-format   测试数据格式兼容性"
	@echo "  deps          安装依赖"
	@echo "  run           运行应用"
	@echo "  dev           开发模式运行"
	@echo "  run-docker    在Docker中运行"
	@echo "  docker        构建Docker镜像"
	@echo "  docker-run    运行Docker容器"
	@echo "  docker-stop   停止Docker容器"
	@echo "  logs          查看日志"
	@echo "  fmt           格式化代码"
	@echo "  lint          代码检查"
	@echo "  docs          生成API文档"
	@echo "  release       创建发布包"
	@echo "  benchmark     性能测试"
	@echo "  coverage      代码覆盖率"
	@echo "  security      安全检查"
	@echo "  deps-check    依赖检查"
	@echo "  help          显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make build    # 构建应用"
	@echo "  make run      # 运行应用"
	@echo "  make test     # 运行测试"
	@echo "  make docker   # 构建Docker镜像"

# 快速启动 (一键部署)
quick-start: clean deps build
	@echo "🚀 快速启动应用..."
	@echo "📋 配置信息:"
	@echo "   - API服务器: http://localhost:9005"
	@echo "   - 健康检查: http://localhost:9005/api/v1/system/health"
	@echo "   - 设备列表: http://localhost:9005/api/v1/devices"
	@echo "   - 事件流: http://localhost:9005/api/v1/events"
	@echo ""
	./$(BINARY) -config config.json
