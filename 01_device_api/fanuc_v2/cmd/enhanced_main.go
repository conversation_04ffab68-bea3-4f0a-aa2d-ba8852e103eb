package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"fanuc_v2/collector"
	"fanuc_v2/config"
	"fanuc_v2/logger"
	"fanuc_v2/manager"
)

// 增强版FANUC v2主程序
// 集成了系统健康监控、资源管理和FOCAS进程管理功能
func main() {
	// 检查命令行参数
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "-h", "--help":
			showUsage()
			return
		case "-v", "--version":
			fmt.Println("增强版FANUC v2数据采集系统 v2.0.0")
			return
		}
	}

	// 初始化系统
	if err := initSystem(); err != nil {
		fmt.Fprintf(os.Stderr, "系统初始化失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	logger.InitLogger("info", "json", "logs/enhanced_fanuc_v2.log")
	logger.Infof("启动增强版FANUC v2数据采集系统...")

	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		logger.Fatalf("加载配置失败: %v", err)
	}

	// 创建增强版设备管理器
	deviceManager := manager.NewEnhancedDeviceManager(cfg)

	// 启动设备管理器
	if err := deviceManager.Start(); err != nil {
		logger.Fatalf("启动设备管理器失败: %v", err)
	}

	// 启动HTTP监控服务器
	go startMonitoringServer(deviceManager)

	// 等待系统信号
	waitForShutdown(deviceManager)
}

// loadConfig 加载配置
func loadConfig() (*config.Config, error) {
	// 这里应该从配置文件加载配置
	// 为了演示，我们使用默认配置
	cfg := &config.Config{
		// 使用简化的配置结构
	}

	logger.Infof("配置加载完成")
	return cfg, nil
}

// startMonitoringServer 启动监控服务器
func startMonitoringServer(deviceManager *manager.EnhancedDeviceManager) {
	mux := http.NewServeMux()

	// 系统状态接口
	mux.HandleFunc("/api/status", func(w http.ResponseWriter, r *http.Request) {
		status := deviceManager.GetStatus()
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(status)
	})

	// 健康检查接口
	mux.HandleFunc("/api/health", func(w http.ResponseWriter, r *http.Request) {
		health := map[string]interface{}{
			"status":    "healthy",
			"timestamp": time.Now().Format("2006-01-02 15:04:05"),
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(health)
	})

	// FOCAS状态接口
	mux.HandleFunc("/api/focas", func(w http.ResponseWriter, r *http.Request) {
		focasManager := collector.GetFOCASManager()
		status := focasManager.GetStatus()
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(status)
	})

	// 启动HTTP服务器
	server := &http.Server{
		Addr:    ":8080",
		Handler: mux,
	}

	logger.Infof("启动监控服务器: http://localhost:8080")
	logger.Infof("可用接口:")
	logger.Infof("  - GET /api/status  - 系统状态")
	logger.Infof("  - GET /api/health  - 健康检查")
	logger.Infof("  - GET /api/focas   - FOCAS状态")

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		logger.Errorf("监控服务器启动失败: %v", err)
	}
}

// waitForShutdown 等待关闭信号
func waitForShutdown(deviceManager *manager.EnhancedDeviceManager) {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	sig := <-sigChan
	logger.Infof("收到关闭信号: %v", sig)

	// 创建关闭上下文，设置30秒超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优雅关闭
	gracefulShutdown(ctx, deviceManager)
}

// gracefulShutdown 优雅关闭
func gracefulShutdown(ctx context.Context, deviceManager *manager.EnhancedDeviceManager) {
	logger.Infof("开始优雅关闭...")

	// 创建关闭完成通道
	done := make(chan bool, 1)

	// 在goroutine中执行关闭操作
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("关闭过程中发生panic: %v", r)
			}
			done <- true
		}()

		// 停止设备管理器
		if err := deviceManager.Stop(); err != nil {
			logger.Errorf("停止设备管理器失败: %v", err)
		}

		logger.Infof("设备管理器已停止")
	}()

	// 等待关闭完成或超时
	select {
	case <-done:
		logger.Infof("优雅关闭完成")
	case <-ctx.Done():
		logger.Warnf("关闭超时，强制退出")
	}

	logger.Infof("增强版FANUC v2数据采集系统已退出")
}

// 辅助函数：打印系统信息
func printSystemInfo() {
	fmt.Println("========================================")
	fmt.Println("增强版FANUC v2数据采集系统")
	fmt.Println("========================================")
	fmt.Println("功能特性:")
	fmt.Println("✅ 全局FOCAS进程管理")
	fmt.Println("✅ 系统健康监控")
	fmt.Println("✅ 资源使用监控")
	fmt.Println("✅ 自动故障恢复")
	fmt.Println("✅ 智能连接管理")
	fmt.Println("✅ 实时状态监控")
	fmt.Println("========================================")
	fmt.Printf("启动时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println("========================================")
}

// 辅助函数：显示使用帮助
func showUsage() {
	fmt.Println("使用方法:")
	fmt.Println("  go run cmd/enhanced_main.go")
	fmt.Println("")
	fmt.Println("环境变量:")
	fmt.Println("  LOG_LEVEL    - 日志级别 (debug, info, warn, error)")
	fmt.Println("  CONFIG_FILE  - 配置文件路径")
	fmt.Println("  HTTP_PORT    - 监控服务器端口")
	fmt.Println("")
	fmt.Println("监控接口:")
	fmt.Println("  GET /api/status  - 系统状态")
	fmt.Println("  GET /api/health  - 健康检查")
	fmt.Println("  GET /api/focas   - FOCAS状态")
}

// 辅助函数：检查系统要求
func checkSystemRequirements() error {
	// 检查FOCAS库文件
	if _, err := os.Stat("fwlib/libfwlib32.so"); os.IsNotExist(err) {
		return fmt.Errorf("FOCAS库文件不存在: fwlib/libfwlib32.so")
	}

	// 检查头文件
	if _, err := os.Stat("fwlib/fwlib32.h"); os.IsNotExist(err) {
		return fmt.Errorf("FOCAS头文件不存在: fwlib/fwlib32.h")
	}

	return nil
}

// 辅助函数：初始化系统
func initSystem() error {
	// 检查系统要求
	if err := checkSystemRequirements(); err != nil {
		return fmt.Errorf("系统要求检查失败: %v", err)
	}

	// 打印系统信息
	printSystemInfo()

	return nil
}
