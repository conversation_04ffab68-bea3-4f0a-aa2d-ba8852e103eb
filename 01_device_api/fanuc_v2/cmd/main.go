/**
 * FANUC设备采集器v2.0主程序
 *
 * 功能概述：
 * 本程序是FANUC设备采集器v2.0的主入口程序，实现了企业级多设备数据采集系统的
 * 完整功能，包括设备管理、数据采集、API服务、事件处理、系统监控等核心功能
 *
 * 主要功能：
 * - 应用程序生命周期管理：启动、运行、优雅关闭
 * - 配置管理：加载、验证、应用系统配置
 * - 设备管理：创建和管理多个FANUC设备采集器
 * - API服务：提供完整的REST API接口服务
 * - 事件处理：统一的事件监听和处理机制
 * - 系统监控：内存、协程、性能监控
 * - 信号处理：优雅的信号处理和关闭机制
 * - 日志管理：企业级日志记录和轮转
 *
 * 架构设计：
 * - 微服务架构：模块化设计，职责分离
 * - 事件驱动：基于事件的异步处理架构
 * - 并发安全：使用Go协程和通道进行并发处理
 * - 优雅关闭：支持优雅的应用程序关闭
 * - 可观测性：完整的日志记录和监控
 *
 * 技术特性：
 * - 命令行参数：支持丰富的命令行参数配置
 * - 配置驱动：基于配置文件的灵活配置
 * - 信号处理：支持SIGINT、SIGTERM信号的优雅处理
 * - 上下文控制：使用context控制应用程序生命周期
 * - 并发处理：多协程并发处理不同功能模块
 * - 资源管理：正确的资源创建、使用和释放
 *
 * 运行模式：
 * - 交互模式：默认的前台运行模式，支持控制台交互
 * - 守护进程模式：后台运行模式，适合生产环境部署
 * - 调试模式：详细的调试信息输出，便于开发调试
 *
 * 监控功能：
 * - 内存监控：实时监控内存使用情况，自动触发GC
 * - 协程监控：监控协程数量，防止协程泄漏
 * - 性能监控：定期输出系统性能指标
 * - 健康检查：定期检查系统健康状态
 *
 * 可靠性保证：
 * - 配置验证：启动前验证配置文件的有效性
 * - 错误处理：完善的错误处理和恢复机制
 * - 优雅关闭：确保所有资源正确释放
 * - 状态监控：实时监控系统运行状态
 *
 * 业务价值：
 * - 企业级稳定性：7x24小时稳定运行的企业级应用
 * - 高性能：支持多设备并发采集，高吞吐量数据处理
 * - 易维护：清晰的架构设计和完整的日志记录
 * - 易扩展：模块化设计，便于功能扩展和定制
 *
 * @package main
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package main

/**
 * 标准库和第三方库导入
 *
 * 标准库：
 * - context: 上下文控制，用于应用程序生命周期管理
 * - flag: 命令行参数解析，支持丰富的启动参数
 * - fmt: 格式化输入输出，用于控制台信息显示
 * - os: 操作系统接口，用于信号处理和环境变量
 * - os/signal: 信号处理，用于优雅关闭应用程序
 * - runtime: 运行时信息，用于系统监控和性能分析
 * - syscall: 系统调用，用于信号常量定义
 * - time: 时间处理，用于定时任务和时间戳
 *
 * 项目内部包：
 * - fanuc_v2/api: API服务器，提供REST API接口
 * - fanuc_v2/config: 配置管理，系统配置加载和验证
 * - fanuc_v2/logger: 日志管理，企业级日志记录
 * - fanuc_v2/manager: 设备管理器，多设备统一管理
 */
import (
	"context"       /** 上下文控制，用于应用程序生命周期管理 */
	"flag"          /** 命令行参数解析，支持启动参数配置 */
	"fmt"           /** 格式化输入输出，用于控制台信息显示 */
	"os"            /** 操作系统接口，用于信号和环境处理 */
	"os/signal"     /** 信号处理，用于优雅关闭机制 */
	"path/filepath" /** 文件路径处理，用于获取绝对路径 */
	"runtime"       /** 运行时信息，用于系统监控 */
	"syscall"       /** 系统调用，用于信号常量 */
	"time"          /** 时间处理，用于定时任务 */

	"fanuc_v2/api" /** API服务器，REST API接口 */
	"fanuc_v2/collector"
	"fanuc_v2/config"  /** 配置管理，系统配置功能 */
	"fanuc_v2/logger"  /** 日志管理，企业级日志 */
	"fanuc_v2/manager" /** 设备管理器，多设备管理 */
)

/**
 * 应用程序常量定义
 *
 * 功能：定义应用程序的基本信息常量
 *
 * 用途：
 * - 版本管理：统一的版本信息管理
 * - 品牌标识：应用程序名称和作者信息
 * - 帮助信息：命令行帮助和版本显示
 * - 日志标识：日志中的应用程序标识
 */
const (
	/** 应用程序名称，用于横幅显示和日志标识 */
	AppName = "FANUC Data Collector v2"

	/** 应用程序版本号，遵循语义化版本规范 */
	AppVersion = "2.0.8"

	/** 应用程序作者信息 */
	AppAuthor = "FANUC Team"
)

/**
 * 命令行参数变量定义
 *
 * 功能：定义应用程序支持的命令行参数
 *
 * 参数说明：
 * - config: 配置文件路径，支持JSON和YAML格式
 * - help: 显示详细的帮助信息和使用说明
 * - version: 显示版本信息和系统信息
 * - daemon: 守护进程模式，适合生产环境部署
 *
 * 使用示例：
 * - ./fanuc_v2 -config /etc/fanuc/config.json
 * - ./fanuc_v2 -daemon
 * - ./fanuc_v2 -help
 * - ./fanuc_v2 -version
 */
var (
	/** 配置文件路径参数，默认为当前目录的config.json */
	configFile = flag.String("config", "config.json", "配置文件路径")

	/** 帮助信息显示参数，显示详细的使用说明 */
	showHelp = flag.Bool("help", false, "显示帮助信息")

	/** 版本信息显示参数，显示版本和系统信息 */
	showVersion = flag.Bool("version", false, "显示版本信息")

	/** 守护进程模式参数，以后台服务方式运行 */
	daemon = flag.Bool("daemon", false, "以守护进程模式运行")
)

/**
 * 主函数 - 应用程序入口点
 *
 * 功能：FANUC设备采集器的主入口函数，负责应用程序的完整生命周期管理
 *
 * 执行流程：
 * 1. 命令行参数解析和处理
 * 2. 配置文件加载和验证
 * 3. 日志系统初始化
 * 4. 核心组件创建和初始化
 * 5. 后台服务启动
 * 6. 信号监听和优雅关闭
 *
 * 架构设计：
 * - 分阶段初始化：按依赖关系分阶段初始化各个组件
 * - 错误快速失败：初始化阶段遇到错误立即退出
 * - 并发启动：使用协程并发启动各个服务
 * - 优雅关闭：支持信号处理和资源清理
 *
 * 可靠性保证：
 * - 配置验证：启动前验证所有配置的有效性
 * - 依赖检查：确保所有依赖组件正确初始化
 * - 错误处理：完善的错误处理和日志记录
 * - 资源管理：确保所有资源正确创建和释放
 */
func main() {

	fmt.Println("========================================")
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println("========================================")
	fmt.Printf("📦 版本: %s\n", AppVersion) /** 版本号 */

	/** 解析命令行参数，获取用户输入的启动选项 */
	flag.Parse()

	/**
	 * 处理帮助信息请求
	 * 如果用户指定了-help参数，显示详细的使用说明后退出
	 */
	if *showHelp {
		showHelpInfo()
		return
	}

	/**
	 * 处理版本信息请求
	 * 如果用户指定了-version参数，显示版本和系统信息后退出
	 */
	if *showVersion {
		showVersionInfo()
		return
	}

	/**
	 * 显示应用程序启动横幅
	 * 包含应用名称、版本、作者、系统信息等
	 */
	showBanner()

	/**
	 * 加载系统配置文件
	 * 支持JSON和YAML格式的配置文件
	 * 配置文件包含设备配置、推送配置、日志配置等
	 */
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		logger.Fatalf("❌ 加载配置文件失败: %v", err)
	}

	/**
	 * 验证配置文件的有效性
	 * 检查必需字段、数据类型、取值范围等
	 * 确保配置符合系统要求
	 */
	if err := cfg.Validate(); err != nil {
		logger.Fatalf("❌ 配置验证失败: %v", err)
	}

	/**
	 * 初始化企业级日志系统
	 * 配置日志级别、格式、输出目标
	 * 启用日志轮转、压缩、清理功能
	 */
	logger.InitLoggerWithRotation(
		cfg.Logger.Level,  /** 日志级别：debug/info/warn/error/fatal */
		cfg.Logger.Format, /** 日志格式：json/text */
		cfg.Logger.Output, /** 输出目标：console/file/both */
		logger.LogRotationConfig{
			Enabled:    cfg.Logger.Rotation.Enabled,    /** 是否启用日志轮转 */
			MaxSize:    cfg.Logger.Rotation.MaxSize,    /** 单个日志文件最大大小(MB) */
			MaxAge:     cfg.Logger.Rotation.MaxAge,     /** 日志文件保留天数 */
			MaxBackups: cfg.Logger.Rotation.MaxBackups, /** 保留的历史日志文件数量 */
			Compress:   cfg.Logger.Rotation.Compress,   /** 是否压缩历史日志文件 */
		},
	)

	/**
	 * 确保FOCAS通信日志文件存在
	 * 在程序启动时主动检查和创建FOCAS通信日志文件
	 * 避免后续FOCAS库调用时因日志文件不存在而出错
	 */

	logger.Info("========================================")
	logger.Info(time.Now().Format("2006-01-02 15:04:05"))
	logger.Info(AppVersion)
	logger.Info("========================================")
	logger.Info("🔍 检查FOCAS通信日志文件...")
	if err := collector.EnsureFocasLogFileExists(); err != nil {
		logger.Warnf("⚠️  FOCAS日志文件检查失败: %v", err)
		// 不退出程序，因为这不是致命错误
	} else {
		logger.Info("✅ FOCAS通信日志文件检查完成")
	}

	/**
	 * 获取并打印实际的文件路径信息
	 * 包括配置文件绝对路径和日志文件绝对路径
	 * 便于调试和运维人员确认文件位置
	 */

	// 获取配置文件的绝对路径
	configAbsPath, err := filepath.Abs(*configFile)
	if err != nil {
		configAbsPath = *configFile // 如果获取失败，使用原始路径
	}

	// 获取当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		workDir = "未知"
	}

	// 获取日志文件的绝对路径
	var logAbsPath string
	if cfg.Logger.Output == "stdout" || cfg.Logger.Output == "stderr" || cfg.Logger.Output == "" {
		logAbsPath = cfg.Logger.Output + " (控制台输出)"
	} else {
		if filepath.IsAbs(cfg.Logger.Output) {
			logAbsPath = cfg.Logger.Output
		} else {
			// 相对路径，基于当前工作目录计算绝对路径
			logAbsPath = filepath.Join(workDir, cfg.Logger.Output)
		}

		// 尝试获取绝对路径
		if absPath, err := filepath.Abs(logAbsPath); err == nil {
			logAbsPath = absPath
		}
	}

	// 获取缓存目录的绝对路径
	var cacheAbsPath string
	if filepath.IsAbs(cfg.Cache.StoragePath) {
		cacheAbsPath = cfg.Cache.StoragePath
	} else {
		cacheAbsPath = filepath.Join(workDir, cfg.Cache.StoragePath)
		if absPath, err := filepath.Abs(cacheAbsPath); err == nil {
			cacheAbsPath = absPath
		}
	}

	// 检查文件和目录的存在性
	configExists := "✅ 存在"
	if _, err := os.Stat(configAbsPath); os.IsNotExist(err) {
		configExists = "❌ 不存在"
	}

	var logDirExists string
	var logDirPath string
	if cfg.Logger.Output != "stdout" && cfg.Logger.Output != "stderr" && cfg.Logger.Output != "" {
		logDirPath = filepath.Dir(logAbsPath)
		if _, err := os.Stat(logDirPath); os.IsNotExist(err) {
			logDirExists = "❌ 日志目录不存在"
		} else {
			logDirExists = "✅ 日志目录存在"
		}
	} else {
		logDirExists = "📺 控制台输出"
		logDirPath = "N/A"
	}

	cacheExists := "✅ 存在"
	if _, err := os.Stat(cacheAbsPath); os.IsNotExist(err) {
		cacheExists = "❌ 不存在"
		// 尝试创建缓存目录
		if err := os.MkdirAll(cacheAbsPath, 0755); err == nil {
			cacheExists = "✅ 已创建"
		} else {
			cacheExists = "❌ 创建失败"
		}
	}

	/** 记录配置加载成功的详细信息 */
	logger.Info("================================================================")
	logger.Info("🎯 FANUC v2 配置信息详情")
	logger.Info("================================================================")
	logger.Infof("✅ 配置文件加载成功")
	logger.Infof("📁 当前工作目录: %s", workDir)
	logger.Info("----------------------------------------------------------------")
	logger.Info("📂 文件路径信息:")
	logger.Infof("   📄 配置文件: %s (%s)", configAbsPath, configExists)
	logger.Infof("   📝 日志文件: %s", logAbsPath)
	logger.Infof("   📁 日志目录: %s (%s)", logDirPath, logDirExists)
	logger.Infof("   💾 缓存目录: %s (%s)", cacheAbsPath, cacheExists)
	logger.Info("----------------------------------------------------------------")
	logger.Info("⚙️  系统配置:")
	logger.Infof("   📋 日志级别: %s", cfg.Logger.Level)
	logger.Infof("   📝 日志格式: %s", cfg.Logger.Format)
	logger.Infof("   📤 日志输出: %s", cfg.Logger.Output)
	logger.Infof("   🔄 日志轮转: %t (大小:%dMB, 保留:%d天, 备份:%d个)",
		cfg.Logger.Rotation.Enabled, cfg.Logger.Rotation.MaxSize,
		cfg.Logger.Rotation.MaxAge, cfg.Logger.Rotation.MaxBackups)
	logger.Infof("   🌐 服务端口: %d", cfg.Server.Port)
	logger.Infof("   📡 设备配置源: %s", cfg.Devices.ConfigSource)
	if cfg.Devices.ConfigSource == "api" {
		logger.Infof("   🔗 API地址: %s", cfg.Devices.ConfigAPI)
	} else {
		logger.Infof("   📄 设备配置文件: %s", cfg.Devices.ConfigFile)
	}
	logger.Info("================================================================")

	/**
	 * 创建应用程序上下文
	 * 用于控制整个应用程序的生命周期
	 * 支持优雅关闭和资源清理
	 */
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() /** 确保上下文在函数退出时被取消 */

	/**
	 * 创建设备管理器实例
	 * 设备管理器是系统的核心组件，负责：
	 * - 管理多个FANUC设备采集器
	 * - 处理设备数据采集和推送
	 * - 管理设备连接状态和健康检查
	 */
	deviceManager := manager.NewDeviceManager(cfg)

	/**
	 * 初始化设备管理器
	 *
	 * 高可用设计：
	 * - 初始化失败时程序继续运行，不会崩溃
	 * - 设备配置加载失败时启动重试机制
	 * - API服务不可用时自动降级处理
	 * - 支持运行时动态加载配置和启动设备
	 *
	 * 包括：
	 * - 创建设备采集器实例（可能延迟创建）
	 * - 初始化推送器和缓存管理器
	 * - 启动后台任务和定时器
	 * - 启动配置重试任务（如果需要）
	 */
	if err := deviceManager.Initialize(); err != nil {
		logger.Warnf("⚠️  设备管理器初始化遇到问题: %v", err)
		logger.Info("🔄 程序将继续运行，设备配置将在后台重试加载")
		logger.Info("💡 您可以通过以下方式解决:")
		logger.Info("   1. 检查12_server_api服务是否启动 (端口9005)")
		logger.Info("   2. 检查网络连接和防火墙设置")
		logger.Info("   3. 检查配置文件格式是否正确")
		logger.Info("   4. 使用API手动启动设备采集")
	} else {
		logger.Info("✅ 设备管理器初始化成功")
	}

	/**
	 * 创建API服务器实例
	 * API服务器提供：
	 * - RESTful API接口
	 * - 设备控制和状态查询
	 * - 系统监控和健康检查
	 * - 实时事件流(Server-Sent Events)
	 */
	apiServer := api.NewServer(cfg, deviceManager)

	/**
	 * 启动事件处理协程
	 * 监听设备管理器产生的事件：
	 * - 设备连接/断开事件
	 * - 数据采集成功/失败事件
	 * - 系统错误和警告事件
	 */
	go handleEvents(deviceManager)

	/**
	 * 启动系统监控协程
	 * 监控系统运行状态：
	 * - 内存使用情况监控
	 * - 协程数量监控
	 * - 性能指标统计
	 */
	go systemMonitor(ctx, cfg)

	/**
	 * 设置系统信号处理
	 * 监听操作系统信号：
	 * - SIGINT: Ctrl+C中断信号
	 * - SIGTERM: 终止信号(kill命令)
	 */
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	/**
	 * 启动API服务器协程
	 * 在独立协程中启动HTTP服务器
	 * 如果启动失败，取消应用程序上下文
	 */
	go func() {
		logger.Info("🚀 启动API服务器...")
		if err := apiServer.Start(); err != nil {
			logger.Errorf("❌ API服务器启动失败: %v", err)
			cancel() /** 启动失败时取消上下文，触发应用程序退出 */
		}
	}()

	/**
	 * 等待退出信号
	 * 使用select语句同时监听：
	 * - 系统信号通道：用户主动退出
	 * - 上下文取消：程序内部错误退出
	 */
	select {
	case sig := <-sigCh:
		logger.Infof("📡 收到退出信号: %v", sig)
	case <-ctx.Done():
		logger.Info("📡 应用上下文取消")
	}

	/**
	 * 开始优雅关闭流程
	 * 按正确顺序关闭各个组件
	 * 确保数据完整性和资源释放
	 */
	logger.Info("🔄 开始优雅关闭...")

	/**
	 * 停止API服务器
	 * 停止接受新的HTTP请求
	 * 等待现有请求处理完成
	 */
	if err := apiServer.Stop(); err != nil {
		logger.Warnf("⚠️  停止API服务器失败: %v", err)
	}

	/**
	 * 关闭设备管理器
	 * 停止所有设备采集器
	 * 关闭推送器和缓存管理器
	 * 释放所有相关资源
	 */
	if err := deviceManager.Close(); err != nil {
		logger.Warnf("⚠️  关闭设备管理器失败: %v", err)
	}

	/**
	 * 清理FOCAS库资源
	 * 根据FOCAS2文档，程序退出时应调用cnc_exitprocess
	 * 释放当前进程中所有已分配的library handles
	 */
	logger.Info("🧹 清理FOCAS库资源...")
	focasManager := collector.GetFOCASManager()
	if err := focasManager.CleanupFOCAS(); err != nil {
		logger.Warnf("⚠️  清理FOCAS库失败: %v", err)
	} else {
		logger.Info("✅ FOCAS库资源已清理")
	}

	logger.Info("✅ 应用已安全退出")
}

/**
 * 显示应用程序启动横幅
 *
 * 功能：在应用程序启动时显示美观的ASCII艺术横幅和系统信息
 *
 * 显示内容：
 * - ASCII艺术字体的应用程序名称
 * - 应用程序基本信息（名称、版本、作者）
 * - 系统运行环境信息（Go版本、操作系统、架构）
 * - 启动时间戳
 *
 * 设计目的：
 * - 品牌标识：清晰的应用程序标识
 * - 版本信息：便于版本确认和问题排查
 * - 环境信息：便于环境兼容性确认
 * - 用户体验：提供友好的启动反馈
 *
 * 使用场景：
 * - 应用程序启动时的欢迎界面
 * - 版本和环境信息的快速确认
 * - 日志文件中的启动标记
 */
func showBanner() {
	/**
	 * 显示ASCII艺术横幅
	 * 使用Unicode字符绘制的FANUC V2标识
	 * 包含边框装饰和中文说明
	 */
	/*
			fmt.Println(`
		╔══════════════════════════════════════════════════════════════╗
		║                                                              ║
		║    ███████╗ █████╗ ███╗   ██╗██╗   ██╗ ██████╗    ██╗   ██╗ ║
		║    ██╔════╝██╔══██╗████╗  ██║██║   ██║██╔════╝    ██║   ██║ ║
		║    █████╗  ███████║██╔██╗ ██║██║   ██║██║         ██║   ██║ ║
		║    ██╔══╝  ██╔══██║██║╚██╗██║██║   ██║██║         ╚██╗ ██╔╝ ║
		║    ██║     ██║  ██║██║ ╚████║╚██████╔╝╚██████╗     ╚████╔╝  ║
		║    ╚═╝     ╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝  ╚═════╝      ╚═══╝   ║
		║                                                              ║
		║              企业级多设备数据采集系统                          ║
		║                                                              ║
		╚══════════════════════════════════════════════════════════════╝`)

	*/

	/** 显示应用程序基本信息，使用emoji图标增强可读性 */
	fmt.Printf("\n🎯 %s\n", AppName)       /** 应用程序名称 */
	fmt.Printf("📦 版本: %s\n", AppVersion)  /** 版本号 */
	fmt.Printf("👨‍💻 作者: %s\n", AppAuthor) /** 作者信息 */

	/** 显示系统运行环境信息 */
	//fmt.Printf("🐹 Go版本: %s\n", runtime.Version())                          /** Go运行时版本 */
	//fmt.Printf("💻 系统: %s/%s\n", runtime.GOOS, runtime.GOARCH)              /** 操作系统和架构 */
	fmt.Printf("⏰ 启动时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05")) /** 启动时间戳 */
}

/**
 * 显示版本信息
 *
 * 功能：显示应用程序的版本和系统信息
 *
 * 显示内容：
 * - 应用程序名称和版本号
 * - Go运行时版本信息
 * - 操作系统和架构信息
 * - 应用程序作者信息
 *
 * 使用场景：
 * - 用户通过-version参数查询版本
 * - 技术支持时的版本确认
 * - 部署环境的版本验证
 * - 问题排查时的环境信息收集
 */
func showVersionInfo() {
	fmt.Printf("%s %s\n", AppName, AppVersion)              /** 应用名称和版本 */
	fmt.Printf("Go版本: %s\n", runtime.Version())             /** Go运行时版本 */
	fmt.Printf("系统: %s/%s\n", runtime.GOOS, runtime.GOARCH) /** 操作系统和架构 */
	fmt.Printf("作者: %s\n", AppAuthor)                       /** 作者信息 */
}

/**
 * 显示帮助信息
 *
 * 功能：显示详细的使用说明和功能介绍
 *
 * 显示内容：
 * - 应用程序简介和用法说明
 * - 命令行参数详细说明
 * - 核心功能特性列表
 * - 完整的API接口文档
 * - 实用的使用示例
 *
 * 设计目的：
 * - 用户指南：为新用户提供完整的使用指南
 * - 参数说明：详细的命令行参数说明
 * - 功能介绍：展示系统的核心功能和特性
 * - API文档：提供API接口的快速参考
 *
 * 使用场景：
 * - 新用户学习如何使用系统
 * - 查询命令行参数的用法
 * - 了解系统功能和API接口
 * - 获取使用示例和最佳实践
 */
func showHelpInfo() {
	/** 显示应用程序标题和简介 */
	fmt.Printf("%s - 企业级多设备数据采集系统\n\n", AppName)

	/** 显示基本用法说明 */
	fmt.Printf("用法:\n")
	fmt.Printf("  %s [选项]\n\n", os.Args[0])

	/** 显示命令行参数详细说明 */
	fmt.Printf("选项:\n")
	fmt.Printf("  -config string    配置文件路径 (默认: config.json)\n")
	fmt.Printf("  -daemon          以守护进程模式运行\n")
	fmt.Printf("  -version         显示版本信息\n")
	fmt.Printf("  -help            显示此帮助信息\n\n")

	/** 显示核心功能特性列表 */
	fmt.Printf("功能特性:\n")
	fmt.Printf("  ✅ 多设备并发采集\n")                    /** 支持多个FANUC设备同时采集 */
	fmt.Printf("  ✅ 多种推送通道 (RESTful/MQTT/NATS)\n") /** 支持多种数据推送协议 */
	fmt.Printf("  ✅ 推送失败缓存机制\n")                   /** 数据推送失败时的缓存和重试 */
	fmt.Printf("  ✅ 完整的REST API\n")                /** 提供完整的REST API接口 */
	fmt.Printf("  ✅ 实时事件流\n")                      /** Server-Sent Events实时事件 */
	fmt.Printf("  ✅ 系统健康监控\n")                     /** 系统和设备健康状态监控 */
	fmt.Printf("  ✅ 高可用设计\n\n")                    /** 企业级高可用架构设计 */

	/** 显示API接口文档 */
	fmt.Printf("API接口:\n")
	fmt.Printf("  GET    /api/v1/devices              获取设备列表\n")
	fmt.Printf("  POST   /api/v1/devices/start        启动所有设备\n")
	fmt.Printf("  POST   /api/v1/devices/stop         停止所有设备\n")
	fmt.Printf("  POST   /api/v1/devices/{id}/start   启动指定设备\n")
	fmt.Printf("  POST   /api/v1/devices/{id}/stop    停止指定设备\n")
	fmt.Printf("  GET    /api/v1/system/metrics       获取系统指标\n")
	fmt.Printf("  GET    /api/v1/system/health        获取健康状态\n")
	fmt.Printf("  GET    /api/v1/events               事件流 (SSE)\n\n")

	/** 显示实用的使用示例 */
	fmt.Printf("示例:\n")
	fmt.Printf("  %s -config /etc/fanuc/config.json\n", os.Args[0]) /** 指定配置文件启动 */
	fmt.Printf("  %s -daemon\n", os.Args[0])                        /** 守护进程模式启动 */
	fmt.Printf("  curl http://localhost:9005/api/v1/devices\n")     /** API调用示例 */
}

/**
 * 事件处理函数
 *
 * 功能：监听和处理设备管理器产生的各种事件
 *
 * 处理的事件类型：
 * - error: 错误事件，记录系统和设备错误
 * - warning: 警告事件，记录潜在问题和警告信息
 * - info: 信息事件，记录一般性信息
 * - started: 启动事件，记录设备或服务启动
 * - stopped: 停止事件，记录设备或服务停止
 * - connected: 连接事件，记录设备连接成功
 * - disconnected: 断开事件，记录设备连接断开
 * - data: 数据事件，记录数据采集和推送
 *
 * 设计特性：
 * - 事件驱动：基于事件的异步处理模式
 * - 类型分类：根据事件类型进行不同的处理
 * - 日志记录：将事件信息记录到日志系统
 * - 图标标识：使用emoji图标增强日志可读性
 * - 频率控制：对高频事件进行适当的过滤
 *
 * 运行模式：
 * - 协程运行：在独立协程中运行，不阻塞主线程
 * - 持续监听：持续监听事件通道直到通道关闭
 * - 实时处理：实时处理接收到的事件
 *
 * 使用场景：
 * - 系统监控：实时监控系统和设备状态
 * - 问题诊断：记录错误和警告信息便于问题排查
 * - 运维管理：提供运维人员所需的状态信息
 * - 审计日志：记录重要的系统事件用于审计
 *
 * @param {*manager.DeviceManager} dm - 设备管理器实例
 */
func handleEvents(dm *manager.DeviceManager) {
	/**
	 * 持续监听事件通道
	 * 使用range循环监听设备管理器的事件通道
	 * 当通道关闭时循环自动结束
	 */
	for event := range dm.GetEventChannel() {
		/**
		 * 根据事件类型进行分类处理
		 * 不同类型的事件使用不同的图标和日志级别
		 */
		switch event.Type {
		case "error":
			/** 错误事件：系统或设备发生错误 */
			logger.Infof("❌ [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		case "warning":
			/** 警告事件：潜在问题或需要注意的情况 */
			logger.Infof("⚠️  [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		case "info":
			/** 信息事件：一般性信息和状态更新 */
			logger.Infof("ℹ️  [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		case "started":
			/** 启动事件：设备或服务成功启动 */
			logger.Infof("🟢 [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		case "stopped":
			/** 停止事件：设备或服务停止运行 */
			logger.Infof("🔴 [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		case "connected":
			/** 连接事件：设备成功建立连接 */
			logger.Infof("🔗 [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		case "disconnected":
			/** 断开事件：设备连接断开 */
			logger.Infof("🔌 [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		case "data":
			/**
			 * 数据事件：数据采集和推送成功
			 * 由于数据事件频率较高，进行选择性记录
			 * 过滤掉系统级别的数据事件，只记录设备级别的
			 */
			if event.DeviceID != "system" {
				logger.Infof("📊 [%s] 数据推送成功", event.DeviceID)
			}
		default:
			/** 默认处理：未知类型的事件 */
			logger.Infof("📝 [%s] %s: %s", event.DeviceID, event.Type, event.Message)
		}
	}
}

/**
 * 系统监控函数
 *
 * 功能：监控系统运行状态和性能指标
 *
 * 监控指标：
 * - 内存使用：监控应用程序内存使用情况
 * - 协程数量：监控Go协程的数量，防止协程泄漏
 * - 垃圾回收：在内存使用过高时主动触发GC
 * - 系统状态：定期输出系统运行状态
 *
 * 监控策略：
 * - 定时检查：按配置的间隔定时检查系统状态
 * - 阈值告警：超过配置阈值时记录警告日志
 * - 自动优化：内存使用过高时自动触发垃圾回收
 * - 调试输出：在调试模式下定期输出详细状态
 *
 * 技术特性：
 * - 协程运行：在独立协程中运行，不阻塞主线程
 * - 上下文控制：支持通过context取消监控
 * - 配置驱动：监控行为完全由配置文件控制
 * - 性能友好：低开销的监控实现
 *
 * 可靠性保证：
 * - 内存保护：防止内存使用过高导致系统不稳定
 * - 协程监控：及时发现协程泄漏问题
 * - 自动恢复：通过GC自动释放内存
 * - 状态跟踪：详细的系统状态跟踪和记录
 *
 * 使用场景：
 * - 生产监控：生产环境的系统健康监控
 * - 性能调优：性能问题的发现和分析
 * - 故障预防：提前发现潜在的系统问题
 * - 运维管理：为运维人员提供系统状态信息
 *
 * @param {context.Context} ctx - 上下文，用于控制监控生命周期
 * @param {*config.SystemConfig} cfg - 系统配置，包含监控配置参数
 */
func systemMonitor(ctx context.Context, cfg *config.SystemConfig) {
	/**
	 * 检查监控是否启用
	 * 如果配置中禁用了指标监控，直接返回
	 */
	if !cfg.Monitoring.MetricsEnabled {
		return
	}

	/**
	 * 创建定时器
	 * 根据配置的健康检查间隔创建定时器
	 * 使用defer确保定时器在函数退出时被停止
	 */
	ticker := time.NewTicker(time.Duration(cfg.Monitoring.HealthCheckInterval) * time.Second)
	defer ticker.Stop()

	/**
	 * 监控主循环
	 * 使用select语句同时监听上下文取消和定时器事件
	 */
	for {
		select {
		case <-ctx.Done():
			/** 上下文取消，退出监控循环 */
			return
		case <-ticker.C:
			/** 定时器触发，执行系统监控检查 */

			/**
			 * 监控内存使用情况
			 * 读取Go运行时的内存统计信息
			 */
			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			/**
			 * 计算当前内存使用量（MB）
			 * m.Alloc表示当前分配的内存字节数
			 */
			memoryMB := float64(m.Alloc) / 1024 / 1024

			/**
			 * 检查内存使用是否超过阈值
			 * 如果超过配置的最大内存限制，记录警告并触发GC
			 */
			if memoryMB > float64(cfg.Monitoring.MaxMemoryMB) {
				logger.Infof("⚠️  内存使用过高: %.2f MB (限制: %d MB)", memoryMB, cfg.Monitoring.MaxMemoryMB)

				/**
				 * 主动触发垃圾回收
				 * 尝试释放不再使用的内存
				 */
				runtime.GC()
				logger.Infof("🧹 已触发垃圾回收")
			}

			/**
			 * 监控协程数量
			 * 检查当前运行的协程数量，防止协程泄漏
			 */
			goroutines := runtime.NumGoroutine()
			if goroutines > 1000 {
				logger.Infof("⚠️  协程数量过多: %d", goroutines)
			}

			/**
			 * 调试模式下的详细状态输出
			 * 在调试级别下定期输出系统状态信息
			 */
			if cfg.Logger.Level == "debug" {
				logger.Infof("📊 系统状态 - 内存: %.2f MB, 协程: %d", memoryMB, goroutines)
			}
		}
	}
}
