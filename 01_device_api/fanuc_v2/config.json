{"server": {"host": "0.0.0.0", "port": 9110, "read_timeout": 30, "write_timeout": 30}, "devices": {"config_source": "api", "config_file": "devices.json", "config_api": "http://mdc_server_api:9005/api/public/devices/configs", "auto_start": true, "refresh_interval": 60}, "data_push": {"type": "restful", "enabled": true, "batch_size": 10, "timeout": 30, "config": {"url": "http://mdc_device_collect:9001/api/v1/device/data", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "FANUC-Collector-v2/2.0.0"}, "username": "", "password": "", "timeout": 30}}, "cache": {"type": "memory", "max_size": 10000, "max_age": 3600, "flush_interval": 60, "storage_path": "/app/cache"}, "logger": {"level": "info", "format": "text", "output": "/app/logs/fanuc_v2.log", "rotation": {"enabled": true, "max_size": 10, "max_age": 3, "max_backups": 50, "compress": true}}, "monitoring": {"health_check_interval": 30, "metrics_enabled": true, "max_memory_mb": 512}}