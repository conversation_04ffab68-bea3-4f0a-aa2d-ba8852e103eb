/**
 * FANUC设备数据缓存管理模块
 *
 * 功能概述：
 * 本模块提供FANUC设备采集系统的数据缓存管理功能，当数据推送失败时，
 * 将数据缓存到本地内存中，并提供FIFO（先进先出）的数据重试机制，
 * 确保数据不丢失且按正确顺序推送
 *
 * 主要功能：
 * - 数据缓存：将推送失败的设备数据缓存到内存中
 * - FIFO保证：严格按照先进先出的顺序管理缓存数据
 * - 自动清理：定期清理过期的缓存数据，防止内存泄漏
 * - 容量管理：支持最大缓存容量限制，自动淘汰最老数据
 * - 设备索引：按设备ID建立索引，支持按设备查询缓存
 * - 批量操作：支持批量存储、获取、删除操作，提高效率
 * - 性能监控：详细的缓存性能指标和统计信息
 *
 * 技术特性：
 * - 内存缓存：基于内存的高性能缓存实现
 * - 并发安全：使用读写锁保护缓存数据的并发访问
 * - FIFO队列：使用队列数据结构确保数据顺序
 * - 双重索引：全局FIFO队列 + 设备索引的双重索引结构
 * - 自动清理：后台定时清理过期数据
 * - 内存优化：合理的内存使用和垃圾回收策略
 *
 * 缓存策略：
 * - 容量限制：达到最大容量时自动淘汰最老数据
 * - 时间限制：超过最大存活时间的数据自动清理
 * - FIFO顺序：严格按照数据产生时间顺序管理
 * - 设备隔离：支持按设备ID进行缓存管理
 *
 * 数据结构：
 * - 主存储：map[string]*CacheItem，以缓存项ID为key
 * - 设备索引：map[string][]string，以设备ID为key，缓存项ID列表为value
 * - FIFO队列：[]string，按时间顺序存储所有缓存项ID
 * - 性能指标：CacheMetrics，记录缓存的各种统计信息
 *
 * 应用场景：
 * - 网络故障：网络不稳定导致数据推送失败时的缓存
 * - 服务故障：目标服务不可用时的数据缓存
 * - 流量控制：高峰期流量控制时的数据缓存
 * - 数据恢复：系统重启后的数据恢复
 *
 * 可靠性保证：
 * - 数据不丢失：推送失败的数据全部缓存
 * - 顺序保证：严格的FIFO顺序确保数据顺序正确
 * - 内存保护：容量限制防止内存溢出
 * - 自动恢复：定期重试缓存数据的推送
 *
 * 业务价值：
 * - 数据完整性：确保设备数据不因网络或服务故障而丢失
 * - 系统稳定性：提高系统对故障的容错能力
 * - 运维友好：详细的缓存监控信息，便于运维管理
 * - 性能优化：高效的缓存机制，减少数据丢失风险
 *
 * @package cache
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package cache

/**
 * 标准库导入
 *
 * - fmt: 格式化输入输出，用于错误信息格式化
 * - sync: 并发控制，用于读写锁保护缓存数据
 * - time: 时间处理，用于缓存项时间戳和过期时间计算
 *
 * 项目内部包：
 * - fanuc_v2/models: 数据模型，设备数据和缓存项结构
 */
import (
	"fmt"  /** 格式化输入输出，用于错误信息处理 */
	"sync" /** 并发控制，用于读写锁保护 */
	"time" /** 时间处理，用于时间戳和过期计算 */

	"fanuc_v2/models" /** 数据模型，设备数据和缓存结构 */
)

/**
 * 缓存管理器接口
 *
 * 功能：定义缓存管理器的标准接口，支持多种缓存实现
 *
 * 设计原则：
 * - 接口隔离：每个方法职责单一，便于实现和测试
 * - 依赖倒置：依赖抽象接口而非具体实现
 * - 开闭原则：对扩展开放，对修改关闭
 * - 里氏替换：所有实现都可以互相替换
 *
 * 使用场景：
 * - 策略模式：不同缓存策略的统一接口
 * - 工厂模式：工厂创建不同类型的缓存管理器
 * - 适配器模式：适配不同的缓存存储后端
 *
 * @interface CacheManager
 */
type CacheManager interface {
	/**
	 * 存储数据到缓存
	 *
	 * 功能：将设备数据存储到缓存中，用于推送失败时的数据保护
	 *
	 * @param {*models.DeviceData} data - 要缓存的设备数据
	 * @returns {error} 存储失败时返回错误信息
	 */
	Store(data *models.DeviceData) error

	/**
	 * 从缓存获取指定设备的数据
	 *
	 * 功能：获取指定设备的缓存数据，支持数量限制
	 *
	 * @param {string} deviceID - 设备ID
	 * @param {int} limit - 最大返回数量，0表示不限制
	 * @returns {[]*models.CacheItem} 缓存项列表
	 * @returns {error} 获取失败时返回错误信息
	 */
	Get(deviceID string, limit int) ([]*models.CacheItem, error)

	/**
	 * 获取所有缓存数据
	 *
	 * 功能：按FIFO顺序获取所有缓存数据，用于批量重试推送
	 *
	 * @param {int} limit - 最大返回数量，0表示不限制
	 * @returns {[]*models.CacheItem} 按FIFO顺序排列的缓存项列表
	 * @returns {error} 获取失败时返回错误信息
	 */
	GetAll(limit int) ([]*models.CacheItem, error)

	/**
	 * 移除指定的缓存项
	 *
	 * 功能：删除指定ID的缓存项，通常在数据推送成功后调用
	 *
	 * @param {string} itemID - 缓存项ID
	 * @returns {error} 移除失败时返回错误信息
	 */
	Remove(itemID string) error

	/**
	 * 批量移除缓存项
	 *
	 * 功能：批量删除多个缓存项，提高删除效率
	 *
	 * @param {[]string} itemIDs - 缓存项ID列表
	 * @returns {error} 移除失败时返回错误信息
	 */
	RemoveBatch(itemIDs []string) error

	/**
	 * 清空指定设备的缓存
	 *
	 * 功能：删除指定设备的所有缓存数据
	 *
	 * @param {string} deviceID - 设备ID
	 * @returns {error} 清空失败时返回错误信息
	 */
	Clear(deviceID string) error

	/**
	 * 清空所有缓存
	 *
	 * 功能：删除所有缓存数据，用于系统重置或清理
	 *
	 * @returns {error} 清空失败时返回错误信息
	 */
	ClearAll() error

	/**
	 * 获取缓存大小
	 *
	 * 功能：返回当前缓存中的数据项数量
	 *
	 * @returns {int} 缓存项数量
	 */
	Size() int

	/**
	 * 获取缓存指标
	 *
	 * 功能：返回缓存的性能指标和统计信息
	 *
	 * @returns {CacheMetrics} 缓存性能指标
	 */
	GetMetrics() CacheMetrics

	/**
	 * 关闭缓存管理器
	 *
	 * 功能：关闭缓存管理器，释放资源，执行清理操作
	 *
	 * @returns {error} 关闭失败时返回错误信息
	 */
	Close() error
}

// CacheMetrics 缓存指标
type CacheMetrics struct {
	TotalItems  int       `json:"total_items"`
	DeviceCount int       `json:"device_count"`
	OldestItem  time.Time `json:"oldest_item"`
	NewestItem  time.Time `json:"newest_item"`
	TotalSize   int64     `json:"total_size"` // 字节
	HitCount    int64     `json:"hit_count"`
	MissCount   int64     `json:"miss_count"`
	StoreCount  int64     `json:"store_count"`
	RemoveCount int64     `json:"remove_count"`
	LastCleanup time.Time `json:"last_cleanup"`
}

// MemoryCacheManager 内存缓存管理器
// 实现FIFO队列，确保数据按照时间顺序缓存和推送
type MemoryCacheManager struct {
	items       map[string]*models.CacheItem
	deviceIndex map[string][]string // 设备ID -> 缓存项ID列表（FIFO顺序）
	fifoQueue   []string            // 全局FIFO队列，按时间顺序存储所有缓存项ID
	maxSize     int
	maxAge      time.Duration
	metrics     CacheMetrics
	mu          sync.RWMutex
	stopCh      chan struct{}
}

// NewMemoryCacheManager 创建内存缓存管理器
func NewMemoryCacheManager(maxSize int, maxAge time.Duration) *MemoryCacheManager {
	manager := &MemoryCacheManager{
		items:       make(map[string]*models.CacheItem),
		deviceIndex: make(map[string][]string),
		fifoQueue:   make([]string, 0),
		maxSize:     maxSize,
		maxAge:      maxAge,
		stopCh:      make(chan struct{}),
	}

	// 启动清理协程
	go manager.cleanupLoop()

	return manager
}

// Store 存储数据到缓存
func (m *MemoryCacheManager) Store(data *models.DeviceData) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 生成缓存项ID
	itemID := fmt.Sprintf("%s_%d", data.DeviceID, time.Now().UnixNano())

	item := &models.CacheItem{
		ID:        itemID,
		DeviceID:  data.DeviceID,
		Data:      data,
		Timestamp: time.Now(),
	}

	// 检查缓存大小限制
	if len(m.items) >= m.maxSize {
		m.evictOldest()
	}

	// 存储缓存项
	m.items[itemID] = item

	// 更新设备索引（FIFO顺序）
	if _, exists := m.deviceIndex[data.DeviceID]; !exists {
		m.deviceIndex[data.DeviceID] = make([]string, 0)
	}
	m.deviceIndex[data.DeviceID] = append(m.deviceIndex[data.DeviceID], itemID)

	// 更新全局FIFO队列（最重要：确保推送顺序）
	m.fifoQueue = append(m.fifoQueue, itemID)

	// 更新指标
	m.metrics.StoreCount++
	m.updateMetrics()

	return nil
}

// Get 从缓存获取数据
func (m *MemoryCacheManager) Get(deviceID string, limit int) ([]*models.CacheItem, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	itemIDs, exists := m.deviceIndex[deviceID]
	if !exists {
		m.metrics.MissCount++
		return nil, nil
	}

	var items []*models.CacheItem
	count := 0

	// 从最新的开始返回
	for i := len(itemIDs) - 1; i >= 0 && count < limit; i-- {
		if item, exists := m.items[itemIDs[i]]; exists {
			items = append(items, item)
			count++
		}
	}

	if len(items) > 0 {
		m.metrics.HitCount++
	} else {
		m.metrics.MissCount++
	}

	return items, nil
}

// GetAll 获取所有缓存数据（按FIFO顺序）
// 确保数据按照缓存时间的先后顺序返回，实现真正的FIFO
func (m *MemoryCacheManager) GetAll(limit int) ([]*models.CacheItem, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var items []*models.CacheItem
	count := 0

	// 按FIFO队列顺序获取数据（最重要：确保推送顺序）
	for _, itemID := range m.fifoQueue {
		if count >= limit {
			break
		}

		// 检查缓存项是否仍然存在（可能已被清理）
		if item, exists := m.items[itemID]; exists {
			items = append(items, item)
			count++
		}
	}

	return items, nil
}

// Remove 移除缓存项
func (m *MemoryCacheManager) Remove(itemID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	item, exists := m.items[itemID]
	if !exists {
		return fmt.Errorf("缓存项不存在: %s", itemID)
	}

	// 从items中删除
	delete(m.items, itemID)

	// 从设备索引中删除
	if itemIDs, exists := m.deviceIndex[item.DeviceID]; exists {
		for i, id := range itemIDs {
			if id == itemID {
				m.deviceIndex[item.DeviceID] = append(itemIDs[:i], itemIDs[i+1:]...)
				break
			}
		}

		// 如果设备没有缓存项了，删除设备索引
		if len(m.deviceIndex[item.DeviceID]) == 0 {
			delete(m.deviceIndex, item.DeviceID)
		}
	}

	// 从FIFO队列中删除（重要：维护队列一致性）
	for i, id := range m.fifoQueue {
		if id == itemID {
			m.fifoQueue = append(m.fifoQueue[:i], m.fifoQueue[i+1:]...)
			break
		}
	}

	// 更新指标
	m.metrics.RemoveCount++
	m.updateMetrics()

	return nil
}

// RemoveBatch 批量移除缓存项
func (m *MemoryCacheManager) RemoveBatch(itemIDs []string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 创建一个map用于快速查找要删除的ID
	toRemove := make(map[string]bool)
	for _, itemID := range itemIDs {
		toRemove[itemID] = true
	}

	for _, itemID := range itemIDs {
		item, exists := m.items[itemID]
		if !exists {
			continue
		}

		// 从items中删除
		delete(m.items, itemID)

		// 从设备索引中删除
		if deviceItemIDs, exists := m.deviceIndex[item.DeviceID]; exists {
			for i, id := range deviceItemIDs {
				if id == itemID {
					m.deviceIndex[item.DeviceID] = append(deviceItemIDs[:i], deviceItemIDs[i+1:]...)
					break
				}
			}

			// 如果设备没有缓存项了，删除设备索引
			if len(m.deviceIndex[item.DeviceID]) == 0 {
				delete(m.deviceIndex, item.DeviceID)
			}
		}

		m.metrics.RemoveCount++
	}

	// 从FIFO队列中批量删除（重要：维护队列一致性）
	newQueue := make([]string, 0, len(m.fifoQueue))
	for _, id := range m.fifoQueue {
		if !toRemove[id] {
			newQueue = append(newQueue, id)
		}
	}
	m.fifoQueue = newQueue

	m.updateMetrics()
	return nil
}

// Clear 清空指定设备的缓存
func (m *MemoryCacheManager) Clear(deviceID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	itemIDs, exists := m.deviceIndex[deviceID]
	if !exists {
		return nil
	}

	// 删除所有相关缓存项
	for _, itemID := range itemIDs {
		delete(m.items, itemID)
		m.metrics.RemoveCount++
	}

	// 删除设备索引
	delete(m.deviceIndex, deviceID)

	m.updateMetrics()
	return nil
}

// ClearAll 清空所有缓存
func (m *MemoryCacheManager) ClearAll() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.metrics.RemoveCount += int64(len(m.items))
	m.items = make(map[string]*models.CacheItem)
	m.deviceIndex = make(map[string][]string)
	m.fifoQueue = make([]string, 0) // 清空FIFO队列

	m.updateMetrics()
	return nil
}

// Size 获取缓存大小
func (m *MemoryCacheManager) Size() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.items)
}

// GetMetrics 获取缓存指标
func (m *MemoryCacheManager) GetMetrics() CacheMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.metrics
}

// Close 关闭缓存管理器
func (m *MemoryCacheManager) Close() error {
	close(m.stopCh)
	return nil
}

// evictOldest 淘汰最老的缓存项
// 使用FIFO队列，直接淘汰队列头部的项（最老的）
func (m *MemoryCacheManager) evictOldest() {
	if len(m.fifoQueue) == 0 {
		return
	}

	// 从FIFO队列头部获取最老的项
	oldestID := m.fifoQueue[0]
	item, exists := m.items[oldestID]
	if !exists {
		// 如果项不存在，从队列中移除并重试
		m.fifoQueue = m.fifoQueue[1:]
		m.evictOldest()
		return
	}

	// 删除最老的项
	delete(m.items, oldestID)

	// 从设备索引中删除
	if itemIDs, exists := m.deviceIndex[item.DeviceID]; exists {
		for i, id := range itemIDs {
			if id == oldestID {
				m.deviceIndex[item.DeviceID] = append(itemIDs[:i], itemIDs[i+1:]...)
				break
			}
		}

		if len(m.deviceIndex[item.DeviceID]) == 0 {
			delete(m.deviceIndex, item.DeviceID)
		}
	}

	// 从FIFO队列中删除（队列头部）
	m.fifoQueue = m.fifoQueue[1:]
}

// updateMetrics 更新指标
func (m *MemoryCacheManager) updateMetrics() {
	m.metrics.TotalItems = len(m.items)
	m.metrics.DeviceCount = len(m.deviceIndex)

	// 计算最老和最新的项
	var oldest, newest time.Time
	for _, item := range m.items {
		if oldest.IsZero() || item.Timestamp.Before(oldest) {
			oldest = item.Timestamp
		}
		if newest.IsZero() || item.Timestamp.After(newest) {
			newest = item.Timestamp
		}
	}

	m.metrics.OldestItem = oldest
	m.metrics.NewestItem = newest
}

// cleanupLoop 清理循环
func (m *MemoryCacheManager) cleanupLoop() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.cleanup()
		case <-m.stopCh:
			return
		}
	}
}

// cleanup 清理过期数据
func (m *MemoryCacheManager) cleanup() {
	m.mu.Lock()
	defer m.mu.Unlock()

	now := time.Now()
	var expiredIDs []string

	for id, item := range m.items {
		if now.Sub(item.Timestamp) > m.maxAge {
			expiredIDs = append(expiredIDs, id)
		}
	}

	// 创建一个map用于快速查找过期ID
	expiredMap := make(map[string]bool)
	for _, id := range expiredIDs {
		expiredMap[id] = true
	}

	// 删除过期项
	for _, id := range expiredIDs {
		item := m.items[id]
		delete(m.items, id)

		// 从设备索引中删除
		if itemIDs, exists := m.deviceIndex[item.DeviceID]; exists {
			for i, itemID := range itemIDs {
				if itemID == id {
					m.deviceIndex[item.DeviceID] = append(itemIDs[:i], itemIDs[i+1:]...)
					break
				}
			}

			if len(m.deviceIndex[item.DeviceID]) == 0 {
				delete(m.deviceIndex, item.DeviceID)
			}
		}
	}

	// 从FIFO队列中删除过期项（重要：维护队列一致性）
	newQueue := make([]string, 0, len(m.fifoQueue))
	for _, id := range m.fifoQueue {
		if !expiredMap[id] {
			newQueue = append(newQueue, id)
		}
	}
	m.fifoQueue = newQueue

	m.metrics.LastCleanup = now
	m.updateMetrics()
}
