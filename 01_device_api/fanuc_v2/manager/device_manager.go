/**
 * FANUC设备管理器模块
 *
 * 功能概述：
 * 本模块实现FANUC设备采集系统的核心管理功能，负责统一管理多个FANUC设备的
 * 数据采集、连接状态、配置更新、数据推送和系统监控，是整个采集系统的中央控制器
 *
 * 主要功能：
 * - 设备管理：统一管理多个FANUC设备采集器的生命周期
 * - 配置管理：支持文件和API两种配置源的动态加载和刷新
 * - 数据处理：处理采集器产生的数据，执行推送和缓存逻辑
 * - 连接监控：监控所有设备的连接状态和采集状态
 * - 自动启动：支持全局和单设备的自动启动配置
 * - 缓存管理：管理推送失败数据的本地缓存和重试
 * - 健康检查：定期检查系统和设备的健康状态
 * - 事件管理：统一的事件发布和日志记录机制
 *
 * 架构设计：
 * - 中央管理器：作为所有设备采集器的中央控制器
 * - 事件驱动：基于事件的异步处理架构
 * - 插件化：支持不同类型的推送器和缓存管理器
 * - 并发安全：使用读写锁保护共享资源
 * - 生命周期管理：完整的组件生命周期管理
 *
 * 技术特性：
 * - 并发处理：每个设备采集器独立运行，支持并发采集
 * - 动态配置：支持运行时的配置动态更新和设备热插拔
 * - 故障恢复：完善的错误处理和自动恢复机制
 * - 资源管理：正确的资源创建、使用和释放
 * - 监控集成：详细的性能监控和健康检查
 *
 * 数据流程：
 * 1. 配置加载：从文件或API加载设备配置
 * 2. 采集器创建：为每个设备创建独立的采集器
 * 3. 数据采集：采集器定时采集设备数据
 * 4. 数据处理：管理器接收并处理采集的数据
 * 5. 数据推送：将数据推送到目标系统
 * 6. 缓存管理：推送失败时缓存数据并重试
 * 7. 状态监控：监控设备和系统的运行状态
 *
 * 可靠性保证：
 * - 故障隔离：单个设备故障不影响其他设备
 * - 数据不丢失：推送失败时自动缓存数据
 * - 自动恢复：定期重试缓存的数据推送
 * - 健康监控：实时监控系统和设备健康状态
 * - 优雅关闭：支持优雅关闭和资源清理
 *
 * 业务价值：
 * - 集中管理：统一管理大量FANUC设备，简化运维
 * - 高可用性：多重保障机制确保数据采集的连续性
 * - 可扩展性：支持动态添加和移除设备
 * - 运维友好：丰富的监控信息和事件通知
 *
 * @package manager
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package manager

/**
 * 标准库和第三方库导入
 *
 * 标准库：
 * - context: 上下文控制，用于管理器和采集器的生命周期
 * - encoding/json: JSON编码解码，用于配置文件和API响应解析
 * - fmt: 格式化输入输出，用于错误信息和日志格式化
 * - io/ioutil: 文件I/O操作，用于配置文件读取
 * - net/http: HTTP客户端，用于从API获取设备配置
 * - sync: 并发控制，用于读写锁和并发安全
 * - time: 时间处理，用于定时任务和时间戳
 *
 * 项目内部包：
 * - fanuc_v2/cache: 缓存管理，处理推送失败的数据缓存
 * - fanuc_v2/collector: 设备采集器，负责单个设备的数据采集
 * - fanuc_v2/config: 配置管理，系统和设备配置结构
 * - fanuc_v2/logger: 日志记录，统一的日志输出
 * - fanuc_v2/models: 数据模型，设备数据和事件结构
 * - fanuc_v2/pusher: 数据推送器，负责数据推送到目标系统
 */
import (
	"context"       /** 上下文控制，用于生命周期管理 */
	"encoding/json" /** JSON编码解码，用于配置解析 */
	"fmt"           /** 格式化输入输出，用于错误处理 */
	"io/ioutil"     /** 文件I/O操作，用于配置文件读取 */
	"net/http"      /** HTTP客户端，用于API配置获取 */
	"sync"          /** 并发控制，用于读写锁保护 */
	"time"          /** 时间处理，用于定时任务 */

	"fanuc_v2/cache"     /** 缓存管理，数据缓存和重试 */
	"fanuc_v2/collector" /** 设备采集器，单设备数据采集 */
	"fanuc_v2/config"    /** 配置管理，系统配置结构 */
	"fanuc_v2/logger"    /** 日志记录，统一日志输出 */
	"fanuc_v2/models"    /** 数据模型，设备数据结构 */
	"fanuc_v2/pusher"    /** 数据推送器，数据推送功能 */
)

/**
 * 设备管理器结构体
 *
 * 功能：作为FANUC设备采集系统的中央控制器，统一管理所有设备采集器
 *
 * 架构设计：
 * - 中央管理：统一管理多个设备采集器的生命周期
 * - 事件驱动：基于事件的异步处理和通知机制
 * - 插件化：支持不同类型的推送器和缓存管理器
 * - 并发安全：使用读写锁保护共享资源
 *
 * @struct DeviceManager
 */
type DeviceManager struct {
	/** 系统配置，包含所有模块的配置信息 */
	config *config.SystemConfig

	/** 设备采集器映射表，key为设备ID，value为采集器实例 */
	collectors map[string]*collector.DeviceCollector

	/** 推送器管理器，负责数据推送到目标系统 */
	pusherManager *pusher.PusherManager

	/** 缓存管理器，负责推送失败数据的缓存和重试 */
	cacheManager cache.CacheManager

	/** 上下文对象，用于控制管理器和所有子组件的生命周期 */
	ctx context.Context

	/** 取消函数，用于停止管理器和所有子组件 */
	cancel context.CancelFunc

	/** 读写锁，保护采集器映射表的并发访问 */
	mu sync.RWMutex

	/** 事件通道，用于发布系统和设备事件 */
	eventCh chan *models.DeviceEvent

	/** 系统性能指标，用于监控和统计 */
	metrics *models.SystemMetrics

	/** 管理器启动时间，用于运行时长统计 */
	startTime time.Time
}

// NewDeviceManager 创建设备管理器
func NewDeviceManager(cfg *config.SystemConfig) *DeviceManager {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建缓存管理器
	cacheManager := cache.NewMemoryCacheManager(
		cfg.Cache.MaxSize,
		time.Duration(cfg.Cache.MaxAge)*time.Second,
	)

	// 创建推送器管理器
	pusherManager := pusher.NewPusherManager(pusher.GlobalPusherFactory)

	dm := &DeviceManager{
		config:        cfg,
		collectors:    make(map[string]*collector.DeviceCollector),
		pusherManager: pusherManager,
		cacheManager:  cacheManager,
		ctx:           ctx,
		cancel:        cancel,
		eventCh:       make(chan *models.DeviceEvent, 100),
		metrics:       &models.SystemMetrics{},
		startTime:     time.Now(),
	}

	// 初始化推送器
	pushConfig := pusher.PusherConfig{
		Type:      cfg.DataPush.Type,
		Config:    cfg.DataPush.Config,
		Enabled:   cfg.DataPush.Enabled,
		BatchSize: cfg.DataPush.BatchSize,
		Timeout:   time.Duration(cfg.DataPush.Timeout) * time.Second,
	}

	if err := pusherManager.Initialize(pushConfig); err != nil {
		// 记录错误但继续运行
		dm.publishEvent("system", "error", fmt.Sprintf("初始化推送器失败: %v", err), nil)
	}

	// 启动后台任务
	go dm.backgroundTasks()

	return dm
}

// Initialize 初始化设备管理器
func (dm *DeviceManager) Initialize() error {
	// 尝试加载设备配置，失败时记录错误但继续运行
	if err := dm.loadDeviceConfigs(); err != nil {
		dm.publishEvent("system", "warning",
			fmt.Sprintf("初始化时加载设备配置失败: %v，将在后台定时重试", err), nil)
		logger.Warnf("⚠️  初始化时加载设备配置失败: %v，程序将继续运行并定时重试", err)

		// 启动配置重试任务
		go dm.configRetryTask()
	} else {
		// 配置加载成功，处理自动启动逻辑
		if err := dm.handleAutoStart(); err != nil {
			dm.publishEvent("system", "warning",
				fmt.Sprintf("自动启动失败: %v，可手动启动设备", err), nil)
			logger.Warnf("⚠️  自动启动失败: %v，可通过API手动启动设备", err)
		}
	}

	return nil
}

// handleAutoStart 处理自动启动逻辑
func (dm *DeviceManager) handleAutoStart() error {
	// 如果系统配置了全局自动启动，启动所有启用的设备
	if dm.config.Devices.AutoStart {
		dm.publishEvent("system", "info", "系统配置了全局自动启动，启动所有设备", nil)
		return dm.StartAllDevices()
	}

	// 否则，检查每个设备的单独自动启动配置
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var errors []string
	autoStartCount := 0

	for _, deviceConfig := range dm.config.Devices.Devices {
		if deviceConfig.Enabled && deviceConfig.AutoStart {
			if collector, exists := dm.collectors[deviceConfig.ID]; exists {
				if err := collector.StartCollection(dm.ctx); err != nil {
					errors = append(errors, fmt.Sprintf("设备 %s: %v", deviceConfig.ID, err))
				} else {
					autoStartCount++
					dm.publishEvent(deviceConfig.ID, "started",
						fmt.Sprintf("设备 %s 自动启动成功", deviceConfig.Name), nil)
				}
			}
		}
	}

	if autoStartCount > 0 {
		dm.publishEvent("system", "info",
			fmt.Sprintf("自动启动了 %d 个设备", autoStartCount), nil)
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分设备自动启动失败: %v", errors)
	}

	return nil
}

// loadDeviceConfigs 加载设备配置
func (dm *DeviceManager) loadDeviceConfigs() error {
	var devices []config.DeviceConfig
	var err error

	switch dm.config.Devices.ConfigSource {
	case "file":
		devices, err = dm.loadDeviceConfigsFromFile()
	case "api":
		devices, err = dm.loadDeviceConfigsFromAPI()
	default:
		return fmt.Errorf("不支持的配置源: %s", dm.config.Devices.ConfigSource)
	}

	if err != nil {
		return err
	}

	// 更新设备配置
	dm.config.Devices.Devices = devices

	// 创建或更新采集器
	dm.mu.Lock()
	defer dm.mu.Unlock()

	// 移除不存在的设备
	for deviceID := range dm.collectors {
		found := false
		for _, device := range devices {
			if device.ID == deviceID {
				found = true
				break
			}
		}
		if !found {
			dm.removeCollector(deviceID)
		}
	}

	// 添加或更新设备
	for _, device := range devices {
		if device.Enabled {
			if _, exists := dm.collectors[device.ID]; !exists {
				dm.addCollector(device)
			}
		} else {
			dm.removeCollector(device.ID)
		}
	}

	return nil
}

/**
 * 从文件加载设备配置
 *
 * 功能：从本地配置文件加载设备配置信息
 *
 * 容错机制：
 * - 文件不存在时使用内置默认配置
 * - 文件格式错误时记录详细错误信息
 * - 支持空配置文件（返回空设备列表）
 * - 文件权限问题时提供解决建议
 *
 * 可靠性保证：
 * - 详细的错误信息和解决建议
 * - 支持多种配置文件格式验证
 * - 配置项完整性检查
 *
 * @return []config.DeviceConfig 设备配置列表
 * @return error 错误信息，包含详细的故障原因和解决建议
 */
func (dm *DeviceManager) loadDeviceConfigsFromFile() ([]config.DeviceConfig, error) {
	// 如果没有指定配置文件，使用内置配置
	if dm.config.Devices.ConfigFile == "" {
		logger.Infof("📄 未指定设备配置文件，使用内置配置")
		return dm.config.Devices.Devices, nil
	}

	// 尝试读取配置文件
	data, err := ioutil.ReadFile(dm.config.Devices.ConfigFile)
	if err != nil {
		return nil, fmt.Errorf("读取设备配置文件失败: %v\n解决建议:\n1. 检查文件路径是否正确: %s\n2. 检查文件是否存在\n3. 检查文件读取权限",
			err, dm.config.Devices.ConfigFile)
	}

	// 检查文件是否为空
	if len(data) == 0 {
		logger.Warnf("⚠️  设备配置文件为空: %s", dm.config.Devices.ConfigFile)
		return []config.DeviceConfig{}, nil
	}

	// 尝试解析JSON格式
	var devices []config.DeviceConfig
	if err := json.Unmarshal(data, &devices); err != nil {
		return nil, fmt.Errorf("解析设备配置文件失败: %v\n文件路径: %s\n解决建议:\n1. 检查JSON格式是否正确\n2. 使用JSON验证工具检查语法\n3. 参考示例配置文件格式",
			err, dm.config.Devices.ConfigFile)
	}

	// 验证配置完整性
	for i, device := range devices {
		if device.ID == "" {
			return nil, fmt.Errorf("设备配置第 %d 项缺少ID字段", i+1)
		}
		if device.IP == "" {
			return nil, fmt.Errorf("设备 %s 缺少IP地址配置", device.ID)
		}
		if device.Port <= 0 {
			return nil, fmt.Errorf("设备 %s 端口配置无效: %d", device.ID, device.Port)
		}
	}

	logger.Infof("✅ 从文件成功加载 %d 个设备配置", len(devices))
	return devices, nil
}

/**
 * 从API加载设备配置
 *
 * 功能：从远程API服务获取设备配置信息
 *
 * 容错机制：
 * - 网络连接失败时提供详细的故障诊断
 * - API服务不可用时给出解决建议
 * - 响应格式错误时进行格式验证
 * - 超时处理和重试机制
 * - 支持降级到文件配置
 *
 * 可靠性保证：
 * - 详细的网络故障诊断信息
 * - API响应格式验证
 * - 配置数据完整性检查
 * - 超时和重试控制
 *
 * @return []config.DeviceConfig 设备配置列表
 * @return error 错误信息，包含详细的故障原因和解决建议
 */
func (dm *DeviceManager) loadDeviceConfigsFromAPI() ([]config.DeviceConfig, error) {
	if dm.config.Devices.ConfigAPI == "" {
		return nil, fmt.Errorf("API配置URL为空\n解决建议:\n1. 在config.json中配置devices.config_api字段\n2. 确保API服务地址正确\n3. 或者改用文件配置模式")
	}

	// 创建带超时的HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	logger.Infof("🌐 正在从API获取设备配置: %s", dm.config.Devices.ConfigAPI)

	// 发送HTTP请求
	resp, err := client.Get(dm.config.Devices.ConfigAPI)
	if err != nil {
		return nil, fmt.Errorf("请求设备配置API失败: %v\nAPI地址: %s\n故障诊断:\n1. 检查网络连接是否正常\n2. 检查API服务是否启动 (12_server_api)\n3. 检查防火墙设置\n4. 检查host.docker.internal域名解析\n5. 尝试ping %s",
			err, dm.config.Devices.ConfigAPI, "host.docker.internal")
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("设备配置API返回错误状态: %d %s\nAPI地址: %s\n解决建议:\n1. 检查API服务是否正常运行\n2. 检查API路径是否正确\n3. 检查API服务日志\n4. 状态码说明: %s",
			resp.StatusCode, resp.Status, dm.config.Devices.ConfigAPI, getHTTPStatusDescription(resp.StatusCode))
	}

	// 读取响应数据
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取API响应失败: %v\n解决建议:\n1. 检查网络连接稳定性\n2. 检查API响应大小是否过大", err)
	}

	// 检查响应是否为空
	if len(data) == 0 {
		return nil, fmt.Errorf("API返回空响应\nAPI地址: %s\n解决建议:\n1. 检查API服务是否正确处理请求\n2. 检查数据库中是否有设备配置数据", dm.config.Devices.ConfigAPI)
	}

	// 解析API响应
	var apiResponse struct {
		Success bool                  `json:"success"`
		Data    []config.DeviceConfig `json:"data"`
		Message string                `json:"message"`
	}

	if err := json.Unmarshal(data, &apiResponse); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %v\nAPI地址: %s\n响应内容: %s\n解决建议:\n1. 检查API返回格式是否为标准JSON\n2. 检查API响应结构是否正确\n3. 联系API服务开发者",
			err, dm.config.Devices.ConfigAPI, string(data))
	}

	// 检查API业务逻辑结果
	if !apiResponse.Success {
		return nil, fmt.Errorf("API返回业务错误: %s\nAPI地址: %s\n解决建议:\n1. 检查API服务内部逻辑\n2. 检查数据库连接\n3. 查看API服务日志",
			apiResponse.Message, dm.config.Devices.ConfigAPI)
	}

	// 验证返回的设备配置
	if apiResponse.Data == nil {
		logger.Warnf("⚠️  API返回的设备配置为空")
		return []config.DeviceConfig{}, nil
	}

	// 验证每个设备配置的完整性
	for i, device := range apiResponse.Data {
		if device.ID == "" {
			return nil, fmt.Errorf("API返回的设备配置第 %d 项缺少ID字段", i+1)
		}
		if device.IP == "" {
			return nil, fmt.Errorf("API返回的设备 %s 缺少IP地址配置", device.ID)
		}
		if device.Port <= 0 {
			return nil, fmt.Errorf("API返回的设备 %s 端口配置无效: %d", device.ID, device.Port)
		}
	}

	logger.Infof("✅ 从API成功加载 %d 个设备配置", len(apiResponse.Data))
	return apiResponse.Data, nil
}

/**
 * 获取HTTP状态码描述
 *
 * 功能：为常见的HTTP状态码提供中文描述和解决建议
 *
 * @param statusCode HTTP状态码
 * @return string 状态码描述和建议
 */
func getHTTPStatusDescription(statusCode int) string {
	switch statusCode {
	case 400:
		return "400 Bad Request - 请求格式错误"
	case 401:
		return "401 Unauthorized - 需要身份验证"
	case 403:
		return "403 Forbidden - 访问被拒绝"
	case 404:
		return "404 Not Found - API路径不存在，检查URL是否正确"
	case 500:
		return "500 Internal Server Error - API服务内部错误，检查服务日志"
	case 502:
		return "502 Bad Gateway - 网关错误，检查代理配置"
	case 503:
		return "503 Service Unavailable - 服务不可用，检查服务是否启动"
	case 504:
		return "504 Gateway Timeout - 网关超时，检查网络连接"
	default:
		return fmt.Sprintf("%d - 未知状态码", statusCode)
	}
}

// addCollector 添加采集器
func (dm *DeviceManager) addCollector(device config.DeviceConfig) {
	collector := collector.NewDeviceCollector(device)
	dm.collectors[device.ID] = collector

	// 启动数据处理协程
	go dm.handleCollectorData(device.ID, collector)
	go dm.handleCollectorErrors(device.ID, collector)

	dm.publishEvent(device.ID, "added", fmt.Sprintf("设备 %s 已添加", device.Name), nil)
}

// removeCollector 移除采集器
func (dm *DeviceManager) removeCollector(deviceID string) {
	if collector, exists := dm.collectors[deviceID]; exists {
		collector.StopCollection()
		collector.Disconnect()
		delete(dm.collectors, deviceID)

		dm.publishEvent(deviceID, "removed", fmt.Sprintf("设备 %s 已移除", deviceID), nil)
	}
}

// StartAllDevices 启动所有设备采集
func (dm *DeviceManager) StartAllDevices() error {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var errors []string
	for deviceID, collector := range dm.collectors {
		if err := collector.StartCollection(dm.ctx); err != nil {
			errors = append(errors, fmt.Sprintf("设备 %s: %v", deviceID, err))
		} else {
			dm.publishEvent(deviceID, "started", fmt.Sprintf("设备 %s 开始采集", deviceID), nil)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分设备启动失败: %v", errors)
	}

	return nil
}

// StopAllDevices 停止所有设备采集
func (dm *DeviceManager) StopAllDevices() error {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var errors []string
	for deviceID, collector := range dm.collectors {
		if err := collector.StopCollection(); err != nil {
			errors = append(errors, fmt.Sprintf("设备 %s: %v", deviceID, err))
		} else {
			dm.publishEvent(deviceID, "stopped", fmt.Sprintf("设备 %s 停止采集", deviceID), nil)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分设备停止失败: %v", errors)
	}

	return nil
}

// StartDevice 启动指定设备采集
func (dm *DeviceManager) StartDevice(deviceID string) error {
	dm.mu.RLock()
	collector, exists := dm.collectors[deviceID]
	dm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("设备不存在: %s", deviceID)
	}

	if err := collector.StartCollection(dm.ctx); err != nil {
		return err
	}

	dm.publishEvent(deviceID, "started", fmt.Sprintf("设备 %s 开始采集", deviceID), nil)
	return nil
}

// StopDevice 停止指定设备采集
func (dm *DeviceManager) StopDevice(deviceID string) error {
	dm.mu.RLock()
	collector, exists := dm.collectors[deviceID]
	dm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("设备不存在: %s", deviceID)
	}

	if err := collector.StopCollection(); err != nil {
		return err
	}

	dm.publishEvent(deviceID, "stopped", fmt.Sprintf("设备 %s 停止采集", deviceID), nil)
	return nil
}

/**
 * 配置重试任务
 *
 * 功能：当初始化时配置加载失败后，定时重试加载配置
 *
 * 重试策略：
 * - 初始延迟：30秒后开始第一次重试
 * - 重试间隔：每60秒重试一次
 * - 无限重试：直到成功或程序退出
 * - 成功后自动启动：配置加载成功后自动处理设备启动
 *
 * 可靠性保证：
 * - 错误隔离：重试失败不影响程序运行
 * - 状态通知：每次重试都会发布事件和记录日志
 * - 优雅退出：支持通过上下文取消重试任务
 */
func (dm *DeviceManager) configRetryTask() {
	// 初始延迟30秒，避免启动时的网络问题
	initialDelay := time.NewTimer(30 * time.Second)
	defer initialDelay.Stop()

	select {
	case <-dm.ctx.Done():
		return
	case <-initialDelay.C:
		// 继续执行重试逻辑
	}

	// 创建重试定时器，每60秒重试一次
	retryTicker := time.NewTicker(60 * time.Second)
	defer retryTicker.Stop()

	retryCount := 0

	for {
		select {
		case <-dm.ctx.Done():
			return
		case <-retryTicker.C:
			retryCount++
			logger.Infof("🔄 第 %d 次尝试加载设备配置...", retryCount)

			if err := dm.loadDeviceConfigs(); err != nil {
				dm.publishEvent("system", "warning",
					fmt.Sprintf("第 %d 次配置加载重试失败: %v", retryCount, err), nil)
				logger.Warnf("⚠️  第 %d 次配置加载重试失败: %v", retryCount, err)
			} else {
				// 配置加载成功
				dm.publishEvent("system", "success",
					fmt.Sprintf("第 %d 次重试成功，设备配置已加载", retryCount), nil)
				logger.Infof("✅ 第 %d 次重试成功，设备配置已加载", retryCount)

				// 处理自动启动逻辑
				if err := dm.handleAutoStart(); err != nil {
					dm.publishEvent("system", "warning",
						fmt.Sprintf("配置加载成功但自动启动失败: %v", err), nil)
					logger.Warnf("⚠️  配置加载成功但自动启动失败: %v", err)
				}

				// 配置加载成功，退出重试任务
				return
			}
		}
	}
}

// backgroundTasks 后台任务
func (dm *DeviceManager) backgroundTasks() {
	// 配置刷新定时器
	configTicker := time.NewTicker(time.Duration(dm.config.Devices.RefreshInterval) * time.Second)
	defer configTicker.Stop()

	// 缓存清理定时器
	cacheTicker := time.NewTicker(time.Duration(dm.config.Cache.FlushInterval) * time.Second)
	defer cacheTicker.Stop()

	// 健康检查定时器
	healthTicker := time.NewTicker(time.Duration(dm.config.Monitoring.HealthCheckInterval) * time.Second)
	defer healthTicker.Stop()

	for {
		select {
		case <-dm.ctx.Done():
			return
		case <-configTicker.C:
			if err := dm.RefreshDeviceConfigs(); err != nil {
				dm.publishEvent("system", "error", fmt.Sprintf("刷新设备配置失败: %v", err), nil)
			}
		case <-cacheTicker.C:
			dm.flushCachedData()
		case <-healthTicker.C:
			dm.performHealthCheck()
		}
	}
}

// handleCollectorData 处理采集器数据
func (dm *DeviceManager) handleCollectorData(deviceID string, collector *collector.DeviceCollector) {
	for {
		select {
		case <-dm.ctx.Done():
			return
		case data := <-collector.GetDataChannel():
			dm.processDeviceData(data)
		}
	}
}

// handleCollectorErrors 处理采集器错误
func (dm *DeviceManager) handleCollectorErrors(deviceID string, collector *collector.DeviceCollector) {
	for {
		select {
		case <-dm.ctx.Done():
			return
		case err := <-collector.GetErrorChannel():
			dm.publishEvent(deviceID, "error", err.Error(), nil)
		}
	}
}

// processDeviceData 处理设备数据
func (dm *DeviceManager) processDeviceData(data *models.DeviceData) {
	// 尝试推送数据
	if err := dm.pusherManager.Push(dm.ctx, data); err != nil {
		// 推送失败，缓存数据
		if cacheErr := dm.cacheManager.Store(data); cacheErr != nil {
			dm.publishEvent(data.DeviceID, "error",
				fmt.Sprintf("推送失败且缓存失败: push=%v, cache=%v", err, cacheErr), nil)
		} else {
			dm.publishEvent(data.DeviceID, "warning",
				fmt.Sprintf("推送失败，数据已缓存: %v", err), nil)
		}
	} else {
		dm.publishEvent(data.DeviceID, "data", "数据推送成功", map[string]interface{}{
			"timestamp": data.Timestamp,
		})
	}
}

// flushCachedData 刷新缓存数据
func (dm *DeviceManager) flushCachedData() {
	// 获取所有缓存数据
	cachedItems, err := dm.cacheManager.GetAll(dm.config.DataPush.BatchSize)
	if err != nil {
		dm.publishEvent("system", "error", fmt.Sprintf("获取缓存数据失败: %v", err), nil)
		return
	}

	if len(cachedItems) == 0 {
		return
	}

	// 转换为设备数据
	var dataList []*models.DeviceData
	var itemIDs []string

	for _, item := range cachedItems {
		dataList = append(dataList, item.Data)
		itemIDs = append(itemIDs, item.ID)
	}

	// 尝试批量推送
	if err := dm.pusherManager.PushBatch(dm.ctx, dataList); err != nil {
		dm.publishEvent("system", "warning",
			fmt.Sprintf("缓存数据推送失败: %v", err), nil)
	} else {
		// 推送成功，删除缓存项
		if err := dm.cacheManager.RemoveBatch(itemIDs); err != nil {
			dm.publishEvent("system", "error",
				fmt.Sprintf("删除已推送缓存项失败: %v", err), nil)
		} else {
			dm.publishEvent("system", "info",
				fmt.Sprintf("成功推送 %d 条缓存数据", len(dataList)), nil)
		}
	}
}

// performHealthCheck 执行健康检查
func (dm *DeviceManager) performHealthCheck() {
	// 检查推送器健康状态
	if !dm.pusherManager.IsHealthy(dm.ctx) {
		dm.publishEvent("system", "warning", "推送器健康检查失败", nil)
	}

	// 检查设备连接状态
	dm.mu.RLock()
	for deviceID, collector := range dm.collectors {
		if !collector.IsConnected() {
			dm.publishEvent(deviceID, "warning", "设备连接断开", nil)
			// 设备连接断开时，推送shutdown状态数据
			go dm.pushDeviceShutdownStatus(deviceID, collector)
		}
	}
	dm.mu.RUnlock()

	// 检查缓存大小
	cacheSize := dm.cacheManager.Size()
	if cacheSize > dm.config.Cache.MaxSize*80/100 { // 80%阈值
		dm.publishEvent("system", "warning",
			fmt.Sprintf("缓存使用率过高: %d/%d", cacheSize, dm.config.Cache.MaxSize), nil)
	}
}

// publishEvent 发布事件
func (dm *DeviceManager) publishEvent(deviceID, eventType, message string, data map[string]interface{}) {
	event := &models.DeviceEvent{
		Type:      eventType,
		DeviceID:  deviceID,
		Timestamp: time.Now(),
		Message:   message,
		Data:      data,
	}

	// 根据事件类型记录日志
	logMessage := fmt.Sprintf("[%s] %s", deviceID, message)
	switch eventType {
	case "error":
		logger.Error(logMessage)
	case "warning":
		logger.Warn(logMessage)
	case "info", "started", "stopped", "added", "removed":
		logger.Info(logMessage)
	case "data":
		logger.Debug(logMessage)
	default:
		logger.Info(logMessage)
	}

	select {
	case dm.eventCh <- event:
	default:
		// 事件通道满了，忽略事件
		logger.Warn("事件通道满，忽略事件:", message)
	}
}

/**
 * 刷新设备配置
 *
 * 功能：从配置源重新加载设备配置并处理自动启动逻辑
 *
 * 处理流程：
 * 1. 重新加载设备配置（从文件或API）
 * 2. 更新设备采集器列表（添加新设备，移除已删除设备）
 * 3. 根据配置处理自动启动逻辑
 * 4. 发布配置刷新事件
 *
 * 自动启动策略：
 * - 如果系统配置了全局自动启动，启动所有新添加的启用设备
 * - 否则，检查每个新设备的单独自动启动配置
 * - 对于配置刷新场景，只启动新添加的设备，不影响已运行的设备
 *
 * @return error 错误信息，包含详细的故障原因
 */
func (dm *DeviceManager) RefreshDeviceConfigs() error {
	logger.Infof("🔄 开始刷新设备配置...")

	// 记录刷新前的设备列表，用于识别新添加的设备
	dm.mu.RLock()
	oldDeviceIDs := make(map[string]bool)
	for deviceID := range dm.collectors {
		oldDeviceIDs[deviceID] = true
	}
	dm.mu.RUnlock()

	// 重新加载设备配置
	if err := dm.loadDeviceConfigs(); err != nil {
		logger.Errorf("❌ 刷新设备配置失败: %v", err)
		return err
	}

	// 识别新添加的设备并处理自动启动
	dm.mu.RLock()
	newDevices := make([]config.DeviceConfig, 0)
	allDevices := make([]config.DeviceConfig, 0)

	logger.Infof("🔍 配置刷新检查: 当前有 %d 个旧设备，从API加载了 %d 个设备配置",
		len(oldDeviceIDs), len(dm.config.Devices.Devices))

	for _, deviceConfig := range dm.config.Devices.Devices {
		allDevices = append(allDevices, deviceConfig)

		if deviceConfig.Enabled {
			if !oldDeviceIDs[deviceConfig.ID] {
				// 这是一个新添加的启用设备
				newDevices = append(newDevices, deviceConfig)
				logger.Infof("🆕 发现新设备: %s (%s) - 已启用", deviceConfig.ID, deviceConfig.Name)
			} else {
				logger.Debugf("📋 现有设备: %s (%s) - 已启用", deviceConfig.ID, deviceConfig.Name)
			}
		} else {
			logger.Debugf("⏸️  设备: %s (%s) - 已禁用", deviceConfig.ID, deviceConfig.Name)
		}
	}
	dm.mu.RUnlock()

	// 处理新设备的自动启动逻辑
	if len(newDevices) > 0 {
		logger.Infof("📋 发现 %d 个新添加的设备，检查自动启动配置...", len(newDevices))

		if err := dm.handleAutoStartForNewDevices(newDevices); err != nil {
			logger.Warnf("⚠️  新设备自动启动部分失败: %v", err)
			dm.publishEvent("system", "warning",
				fmt.Sprintf("配置刷新成功，但新设备自动启动部分失败: %v", err), nil)
		}
	} else {
		logger.Infof("📋 配置刷新完成，没有发现新设备需要自动启动")

		// 检查是否有设备没有启动但应该启动
		dm.mu.RLock()
		notStartedDevices := make([]config.DeviceConfig, 0)
		for _, deviceConfig := range allDevices {
			if deviceConfig.Enabled {
				if collector, exists := dm.collectors[deviceConfig.ID]; exists {
					if !collector.IsConnected() {
						notStartedDevices = append(notStartedDevices, deviceConfig)
					}
				}
			}
		}
		dm.mu.RUnlock()

		if len(notStartedDevices) > 0 && dm.config.Devices.AutoStart {
			logger.Infof("🔄 发现 %d 个未连接的设备，尝试启动...", len(notStartedDevices))
			if err := dm.handleAutoStartForNewDevices(notStartedDevices); err != nil {
				logger.Warnf("⚠️  重新启动设备部分失败: %v", err)
			}
		}
	}

	logger.Infof("✅ 设备配置刷新完成")
	dm.publishEvent("system", "success", "设备配置刷新完成", nil)

	return nil
}

/**
 * 处理新设备的自动启动逻辑
 *
 * 功能：为新添加的设备处理自动启动逻辑
 *
 * 自动启动策略：
 * - 如果系统配置了全局自动启动，启动所有新添加的启用设备
 * - 否则，检查每个新设备的单独自动启动配置
 * - 只处理新添加的设备，不影响已运行的设备
 *
 * 容错机制：
 * - 单个设备启动失败不影响其他设备
 * - 记录详细的启动结果和错误信息
 * - 发布设备启动事件
 *
 * @param newDevices []config.DeviceConfig 新添加的设备配置列表
 * @return error 错误信息，包含所有启动失败的设备信息
 */
func (dm *DeviceManager) handleAutoStartForNewDevices(newDevices []config.DeviceConfig) error {
	var errors []string
	autoStartCount := 0

	// 如果系统配置了全局自动启动，启动所有新添加的启用设备
	if dm.config.Devices.AutoStart {
		logger.Infof("🚀 系统配置了全局自动启动，启动所有新添加的设备")

		dm.mu.RLock()
		for _, deviceConfig := range newDevices {
			if collector, exists := dm.collectors[deviceConfig.ID]; exists {
				if err := collector.StartCollection(dm.ctx); err != nil {
					errors = append(errors, fmt.Sprintf("设备 %s: %v", deviceConfig.ID, err))
					logger.Errorf("❌ 设备 %s 自动启动失败: %v", deviceConfig.Name, err)
				} else {
					autoStartCount++
					dm.publishEvent(deviceConfig.ID, "started",
						fmt.Sprintf("设备 %s 自动启动成功", deviceConfig.Name), nil)
					logger.Infof("✅ 设备 %s 自动启动成功", deviceConfig.Name)
				}
			}
		}
		dm.mu.RUnlock()
	} else {
		// 检查每个新设备的单独自动启动配置
		logger.Infof("🔍 检查每个新设备的单独自动启动配置")

		dm.mu.RLock()
		for _, deviceConfig := range newDevices {
			if deviceConfig.AutoStart {
				if collector, exists := dm.collectors[deviceConfig.ID]; exists {
					if err := collector.StartCollection(dm.ctx); err != nil {
						errors = append(errors, fmt.Sprintf("设备 %s: %v", deviceConfig.ID, err))
						logger.Errorf("❌ 设备 %s 自动启动失败: %v", deviceConfig.Name, err)
					} else {
						autoStartCount++
						dm.publishEvent(deviceConfig.ID, "started",
							fmt.Sprintf("设备 %s 自动启动成功", deviceConfig.Name), nil)
						logger.Infof("✅ 设备 %s 自动启动成功", deviceConfig.Name)
					}
				}
			} else {
				logger.Infof("⏸️  设备 %s 未配置自动启动，跳过", deviceConfig.Name)
			}
		}
		dm.mu.RUnlock()
	}

	// 记录启动结果
	if autoStartCount > 0 {
		dm.publishEvent("system", "success",
			fmt.Sprintf("成功自动启动了 %d 个新设备", autoStartCount), nil)
		logger.Infof("🎉 成功自动启动了 %d 个新设备", autoStartCount)
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分新设备自动启动失败: %v", errors)
	}

	return nil
}

/**
 * 推送设备关机状态数据
 *
 * 功能：当检测到设备连接断开时，推送shutdown状态数据到数据采集服务
 *
 * 使用场景：
 * - 健康检查发现设备连接断开
 * - 设备采集器报告连接失败
 * - 设备主动断开连接
 *
 * 数据内容：
 * - 设备基本信息（ID、名称、IP、端口等）
 * - 连接状态：connected = false
 * - 设备状态：run_status_description = "shutdown"
 * - 时间戳和错误信息
 *
 * 容错机制：
 * - 异步执行，不阻塞健康检查流程
 * - 推送失败时记录错误日志
 * - 获取设备配置失败时使用默认值
 *
 * @param deviceID string 设备ID
 * @param collector *collector.DeviceCollector 设备采集器实例
 */
func (dm *DeviceManager) pushDeviceShutdownStatus(deviceID string, collector *collector.DeviceCollector) {
	logger.Infof("[%s] 推送设备关机状态数据", deviceID)

	// 获取设备配置信息
	var deviceConfig config.DeviceConfig
	found := false

	dm.mu.RLock()
	for _, cfg := range dm.config.Devices.Devices {
		if cfg.ID == deviceID {
			deviceConfig = cfg
			found = true
			break
		}
	}
	dm.mu.RUnlock()

	// 如果找不到设备配置，使用默认值
	if !found {
		logger.Warnf("[%s] 未找到设备配置，使用默认值", deviceID)
		deviceConfig = config.DeviceConfig{
			ID:   deviceID,
			Name: deviceID,
			IP:   "unknown",
			Port: 8193,
		}
	}

	// 创建关机状态的设备数据
	data := models.NewDeviceData(deviceConfig.ID, deviceConfig.Name)
	data.DataType = deviceConfig.DataType
	data.Location = fmt.Sprintf("%s:%d", deviceConfig.IP, deviceConfig.Port)
	data.Status.Connected = false // 关键：设置连接状态为false
	data.Status.LastSeen = time.Now()
	data.Status.CollectCount++

	// 设置设备配置信息（用于元数据构建）
	data.DeviceConfig = &models.DeviceConfigInfo{
		Brand:           deviceConfig.Brand,
		Model:           deviceConfig.Model,
		Location:        deviceConfig.Location,
		IP:              deviceConfig.IP,
		Port:            deviceConfig.Port,
		CollectInterval: deviceConfig.CollectInterval,
		DataType:        deviceConfig.DataType,
	}

	// 添加基础设备信息
	data.RawData["device_name"] = deviceConfig.Name
	data.RawData["device_ip"] = deviceConfig.IP
	data.RawData["device_port"] = deviceConfig.Port
	data.RawData["collect_interval"] = deviceConfig.CollectInterval
	data.RawData["timestamp"] = time.Now().Unix()

	// 设备关机状态信息
	data.RawData["connected"] = false
	data.RawData["connection_status"] = "shutdown"
	data.RawData["error_message"] = "设备连接断开"
	data.RawData["disconnect_reason"] = "health_check_failed"

	// 设置关机状态的默认值
	data.RawData["machine_id"] = ""
	data.RawData["alarm_status"] = -1
	data.RawData["auto_mode"] = -1
	data.RawData["run_status"] = -1
	data.RawData["run_status_description"] = "shutdown" // 关键：设置为shutdown状态
	data.RawData["current_program_number"] = -1
	data.RawData["actual_feedrate"] = -1
	data.RawData["actual_spindle_speed"] = -1
	data.RawData["workpiece_count"] = -1

	logger.Infof("[%s] 生成关机状态数据: %s (connected=false, status=shutdown)", deviceID, data.DeviceID)

	// 推送数据
	dm.processDeviceData(data)

	logger.Infof("[%s] 设备关机状态数据推送完成", deviceID)
}

// GetDeviceStatus 获取设备状态
func (dm *DeviceManager) GetDeviceStatus(deviceID string) (*models.DeviceMetrics, error) {
	dm.mu.RLock()
	collector, exists := dm.collectors[deviceID]
	dm.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("设备不存在: %s", deviceID)
	}

	metrics := collector.GetMetrics()
	deviceConfig, _ := dm.config.GetDeviceConfig(deviceID)

	return &models.DeviceMetrics{
		DeviceID:   deviceID,
		DeviceName: deviceConfig.Name,
		Status: models.DeviceStatus{
			Connected:    metrics.IsConnected,
			LastSeen:     metrics.LastCollectionTime,
			ErrorCount:   int(metrics.FailedCollections),
			LastError:    metrics.LastError,
			CollectCount: metrics.SuccessfulCollections,
		},
		CollectInterval: time.Duration(deviceConfig.CollectInterval) * time.Millisecond,
		LastCollectTime: metrics.LastCollectionTime,
		AvgCollectTime:  metrics.AvgCollectionTime,
		SuccessRate:     float64(metrics.SuccessfulCollections) / float64(metrics.TotalCollections),
		ErrorRate:       float64(metrics.FailedCollections) / float64(metrics.TotalCollections),
	}, nil
}

// GetSystemMetrics 获取系统指标
func (dm *DeviceManager) GetSystemMetrics() *models.SystemMetrics {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	metrics := &models.SystemMetrics{
		TotalDevices:     len(dm.collectors),
		ActiveDevices:    0,
		ConnectedDevices: 0,
		Uptime:           time.Since(dm.startTime),
		DeviceMetrics:    make(map[string]models.DeviceMetrics),
		CacheSize:        dm.cacheManager.Size(),
	}

	for deviceID, collector := range dm.collectors {
		collectorMetrics := collector.GetMetrics()

		if collectorMetrics.IsCollecting {
			metrics.ActiveDevices++
		}
		if collectorMetrics.IsConnected {
			metrics.ConnectedDevices++
		}

		metrics.TotalCollections += collectorMetrics.TotalCollections
		metrics.TotalErrors += collectorMetrics.FailedCollections

		deviceConfig, _ := dm.config.GetDeviceConfig(deviceID)
		deviceMetrics := models.DeviceMetrics{
			DeviceID:   deviceID,
			DeviceName: deviceConfig.Name,
			Status: models.DeviceStatus{
				Connected:    collectorMetrics.IsConnected,
				LastSeen:     collectorMetrics.LastCollectionTime,
				ErrorCount:   int(collectorMetrics.FailedCollections),
				LastError:    collectorMetrics.LastError,
				CollectCount: collectorMetrics.SuccessfulCollections,
			},
			CollectInterval: time.Duration(deviceConfig.CollectInterval) * time.Millisecond,
			LastCollectTime: collectorMetrics.LastCollectionTime,
			AvgCollectTime:  collectorMetrics.AvgCollectionTime,
		}

		if collectorMetrics.TotalCollections > 0 {
			deviceMetrics.SuccessRate = float64(collectorMetrics.SuccessfulCollections) / float64(collectorMetrics.TotalCollections)
			deviceMetrics.ErrorRate = float64(collectorMetrics.FailedCollections) / float64(collectorMetrics.TotalCollections)
		}

		metrics.DeviceMetrics[deviceID] = deviceMetrics
	}

	// 获取推送器指标
	pusherMetrics := dm.pusherManager.GetMetrics()
	metrics.TotalPushes = pusherMetrics.TotalPushes

	return metrics
}

// UpdatePusherConfig 更新推送器配置
func (dm *DeviceManager) UpdatePusherConfig(config models.PushConfigRequest) error {
	pushConfig := pusher.PusherConfig{
		Type:   config.Type,
		Config: config.Config,
	}

	if config.Enabled != nil {
		pushConfig.Enabled = *config.Enabled
	} else {
		pushConfig.Enabled = dm.config.DataPush.Enabled
	}

	if config.BatchSize != nil {
		pushConfig.BatchSize = *config.BatchSize
	} else {
		pushConfig.BatchSize = dm.config.DataPush.BatchSize
	}

	if config.Timeout != nil {
		pushConfig.Timeout = time.Duration(*config.Timeout) * time.Second
	} else {
		pushConfig.Timeout = time.Duration(dm.config.DataPush.Timeout) * time.Second
	}

	if err := dm.pusherManager.UpdateConfig(pushConfig); err != nil {
		return err
	}

	// 更新系统配置
	dm.config.DataPush.Type = config.Type
	dm.config.DataPush.Config = config.Config
	if config.Enabled != nil {
		dm.config.DataPush.Enabled = *config.Enabled
	}
	if config.BatchSize != nil {
		dm.config.DataPush.BatchSize = *config.BatchSize
	}
	if config.Timeout != nil {
		dm.config.DataPush.Timeout = *config.Timeout
	}

	return nil
}

// GetEventChannel 获取事件通道
func (dm *DeviceManager) GetEventChannel() <-chan *models.DeviceEvent {
	return dm.eventCh
}

// Close 关闭设备管理器
func (dm *DeviceManager) Close() error {
	dm.cancel()

	// 停止所有设备
	dm.StopAllDevices()

	// 关闭所有采集器
	dm.mu.Lock()
	for _, collector := range dm.collectors {
		collector.Disconnect()
	}
	dm.collectors = make(map[string]*collector.DeviceCollector)
	dm.mu.Unlock()

	// 关闭推送器和缓存
	dm.pusherManager.Close()
	dm.cacheManager.Close()

	return nil
}

// TestPush 测试推送功能
func (dm *DeviceManager) TestPush(ctx context.Context, data *models.DeviceData) error {
	return dm.pusherManager.Push(ctx, data)
}

// GetPusherMetrics 获取推送器指标
func (dm *DeviceManager) GetPusherMetrics() pusher.PusherMetrics {
	return dm.pusherManager.GetMetrics()
}
