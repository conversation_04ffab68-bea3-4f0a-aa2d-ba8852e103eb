# FANUC Data Collector v2

🏭 **企业级多设备数据采集系统** - 基于FANUC FOCAS2/Ethernet协议的高可用数据采集解决方案

## 🎯 **项目特性**

### ✨ **核心功能**
- 🔄 **多设备并发采集** - 支持同时采集多台FANUC CNC设备数据
- 🚀 **多种推送通道** - 支持RESTful API、MQTT、NATS JetStream推送
- 💾 **推送失败缓存** - 推送失败时自动缓存，连接恢复后同步推送
- 🎛️ **完整REST API** - 提供设备控制、状态监控、配置管理接口
- 📡 **实时事件流** - Server-Sent Events实时推送系统事件
- 🏥 **健康监控** - 完善的系统健康检查和异常处理机制
- 🔗 **数据格式兼容** - 完全兼容02_device_collect数据接收格式

### 🏗️ **架构设计**
- 📦 **模块化架构** - 采用设计模式，组件解耦，易于扩展
- 🔧 **配置驱动** - 支持文件和API两种配置源
- 🛡️ **高可用设计** - 异常恢复、重连机制、内存管理
- 🎨 **设计模式** - 工厂模式、策略模式、观察者模式

## 📋 **系统架构**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FANUC CNC 1   │    │   FANUC CNC 2   │    │   FANUC CNC N   │
│  192.168.1.100  │    │  192.168.1.101  │    │  192.168.1.10N  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Device Manager       │
                    │   (设备管理器)            │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    Data Collector        │
                    │   (数据采集器)            │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    Cache Manager         │
                    │   (缓存管理器)            │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   Pusher Manager         │
                    │   (推送管理器)            │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────▼───────┐    ┌──────────▼──────────┐    ┌────────▼────────┐
│  RESTful API  │    │       MQTT          │    │  NATS JetStream │
│   推送通道     │    │      推送通道        │    │     推送通道     │
└───────────────┘    └─────────────────────┘    └─────────────────┘
```

## 🚀 **快速开始**

### 📋 **环境要求**
- Go 1.19+
- Docker (用于FOCAS开发环境)
- FANUC CNC设备 (支持FOCAS2/Ethernet)

### 🔧 **安装步骤**

1. **克隆项目**
```bash
git clone <repository-url>
cd fanuc_v2
```

2. **安装依赖**
```bash
make deps
```

3. **配置设备**
```bash
# 编辑设备配置
vim devices.json

# 编辑系统配置
vim config.json
```

4. **构建运行**
```bash
# 构建应用
make build

# 运行应用
make run

# 或者开发模式
make dev
```

### 🐳 **Docker运行**

```bash
# 在Docker环境中运行 (用于FOCAS开发)
make run-docker

# 构建Docker镜像
make docker

# 运行Docker容器
make docker-run
```

## 🔗 **数据格式兼容性**

### **推送目标**
FANUC Data Collector v2 将采集到的设备数据推送到 `02_device_collect` 服务，数据格式完全兼容现有的数据采集系统。

### **数据格式**
```json
{
  "device_id": "fanuc_001",
  "data_type": "fanuc_cnc",
  "timestamp": "2025-01-29T10:30:45.123Z",
  "location": "**************:8193",
  "raw_data": {
    "device_name": "FANUC CNC Machine 001",
    "connected": true,
    "cnc_type": "FANUC Series 30i",
    "run_status": 3,
    "absolute_position": [12500, 8750, -2100, 0, 90000],
    "actual_feedrate": 1500,
    "actual_spindle_speed": 2000,
    "current_program": 1001,
    "has_alarm": false
  }
}
```

### **推送配置**
```json
{
  "data_push": {
    "type": "restful",
    "enabled": true,
    "config": {
      "url": "http://localhost:8081/api/v1/device/data"
    }
  }
}
```

详细的数据格式说明请参考 [DATA_FORMAT.md](DATA_FORMAT.md)

## ⚙️ **配置说明**

### 容器内环境

- **操作系统**: Ubuntu 16.04 LTS (i386)
- **Go版本**: 1.21.5 linux/386
- **架构**: x86 (386)
- **CGO**: 已启用
- **FOCAS库**: 预配置在 `/usr/local/lib/`
- **头文件**: 位于 `/usr/local/include/`
- **软件源**: 阿里云镜像源（提高下载速度）
- **自动脚本**: 构建时自动执行fwlib/scripts/build-deps.sh和install.sh

### 环境变量

```bash
GOROOT=/usr/local/go
GOPATH=/go
GOARCH=386
GOOS=linux
CGO_ENABLED=1
```

### 端口映射

- `9005`: API服务器
- `8081`: 数据采集服务
- `8082`: 数据推送服务
- `8083`: 其他服务

## 📁 目录结构

```
01_device_api/fanuc_v2/
├── Dockerfile              # x86 Docker镜像定义
├── docker-compose.yml      # 完整开发环境配置
├── build-x86.sh           # 构建和管理脚本
├── README.md              # 项目说明
├── docker/
│   └── lib/               # FOCAS库文件目录
│       ├── libfocas32.so.1.0.5
│       └── fwlib32.h
├── fwlib/                 # FOCAS库相关脚本
│   └── scripts/
│       ├── build-deps.sh  # 构建依赖安装脚本
│       └── install.sh     # FOCAS库安装脚本
├── src/                   # 源代码目录
├── configs/               # 配置文件
└── logs/                  # 日志文件
```

## 🛠️ 使用指南

### 构建脚本命令

```bash
./build-x86.sh build      # 构建Docker镜像
./build-x86.sh run        # 运行容器
./build-x86.sh stop       # 停止容器
./build-x86.sh restart    # 重启容器
./build-x86.sh shell      # 进入容器shell
./build-x86.sh logs       # 查看容器日志
./build-x86.sh status     # 查看状态
./build-x86.sh clean      # 清理镜像和容器
./build-x86.sh compose    # 使用docker-compose启动
```

### 在容器内开发

```bash
# 进入容器
./build-x86.sh shell

# 验证环境
go version                    # 查看Go版本
uname -m                     # 确认架构为i686
ldconfig -p | grep focas     # 检查FOCAS库

# 检查脚本执行状态
ls -la /tmp/focas-build-deps.installed  # 检查构建依赖是否安装
ldconfig -p | grep fwlib     # 检查fwlib库是否安装

# 编译项目
go build -v ./...

# 使用pkg-config
pkg-config --cflags focas    # 获取编译标志
pkg-config --libs focas      # 获取链接标志

# 编译FOCAS程序
gcc $(pkg-config --cflags focas) -o test test.c $(pkg-config --libs focas)
```

### 开发工作流

1. **环境准备**: Docker构建时自动执行fwlib/scripts/下的脚本
2. **代码编辑**: 在主机上编辑代码（通过卷挂载同步）
3. **容器编译**: 在x86容器内编译和测试
4. **调试运行**: 使用容器内的调试工具
5. **日志查看**: 通过挂载的日志目录查看

### 自动执行的脚本

构建Docker镜像时会自动执行以下脚本（如果存在）：

1. **`fwlib/scripts/build-deps.sh`**: 安装构建依赖
   - 安装多架构编译工具
   - 配置交叉编译环境
   - 安装必要的开发库

2. **`fwlib/scripts/install.sh`**: 安装FOCAS库
   - 复制库文件到系统目录
   - 创建符号链接
   - 配置动态链接器

## 🔍 故障排除

### 常见问题

1. **平台不支持错误**
   ```bash
   # 启用Docker实验性功能
   export DOCKER_CLI_EXPERIMENTAL=enabled
   
   # 或安装Docker Buildx
   docker buildx install
   ```

2. **FOCAS库找不到**
   ```bash
   # 检查库文件
   ls -la docker/lib/
   
   # 在容器内检查
   ldconfig -p | grep focas
   ```

3. **Go编译错误**
   ```bash
   # 确认架构设置
   go env GOARCH  # 应该显示 386
   go env CGO_ENABLED  # 应该显示 1
   ```

4. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .
   chmod +x build-x86.sh
   ```

### 调试命令

```bash
# 查看容器详细信息
docker inspect fanuc-focas-dev-x86

# 查看容器资源使用
docker stats fanuc-focas-dev-x86

# 查看容器进程
docker exec fanuc-focas-dev-x86 ps aux

# 查看网络配置
docker network ls
docker network inspect fanuc_fanuc-network
```

## 📚 相关文档

- [FOCAS2/Ethernet官方文档](https://www.fanuc.co.jp/)
- [Docker多平台构建](https://docs.docker.com/buildx/working-with-buildx/)
- [Go交叉编译](https://golang.org/doc/install/source#environment)

## ⚠️ 重要提示

1. **许可证要求**
   - FOCAS2/Ethernet是FANUC的商业软件
   - 确保您有合法的使用许可
   - 遵守FANUC的许可协议

2. **架构兼容性**
   - 此环境专门用于x86架构
   - 编译的程序只能在x86系统上运行
   - 确保目标部署环境为x86架构

3. **开发注意事项**
   - 在容器内进行编译和测试
   - 使用卷挂载进行代码同步
   - 定期备份重要数据

## 🤝 支持

如果您在使用过程中遇到问题：

1. 查看本文档的故障排除部分
2. 检查Docker和系统日志
3. 提交Issue并提供详细的错误信息
4. 确保FOCAS文件的合法性和完整性

---

**注意**: 本项目仅提供开发环境配置，FOCAS2/Ethernet库文件需要从FANUC官方获取。

# 构建 focas docker 开发环境
# 构建
- 2025-06-06
```
./build-x86.sh build
```

## 运行
```
./build-x86.sh run-it
```

## 获取健康度
```

curl http://localhost:9010/api/v1/system/health

http://localhost:9010/api/v1/system/health

```