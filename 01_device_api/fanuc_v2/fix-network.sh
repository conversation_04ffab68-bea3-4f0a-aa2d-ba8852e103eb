#!/bin/bash

# 修复 MDC 系统 Docker 网络连接问题
# 确保所有服务都能正确通信

set -e

echo "🔧 修复 MDC 系统网络连接..."

# 检查并连接 MongoDB 到主网络
echo "📡 检查 MongoDB 网络连接..."
if ! docker network inspect mdc_full_mdc_network | grep -q "mdc_mongodb"; then
    echo "🔗 连接 MongoDB 到主网络..."
    docker network connect mdc_full_mdc_network mdc_mongodb
    echo "✅ MongoDB 已连接到 mdc_full_mdc_network"
else
    echo "✅ MongoDB 已在 mdc_full_mdc_network 中"
fi

# 检查并连接 InfluxDB 到主网络（如果存在）
if docker ps --format "{{.Names}}" | grep -q "mdc_influxdb"; then
    echo "📡 检查 InfluxDB 网络连接..."
    if ! docker network inspect mdc_full_mdc_network | grep -q "mdc_influxdb"; then
        echo "🔗 连接 InfluxDB 到主网络..."
        docker network connect mdc_full_mdc_network mdc_influxdb
        echo "✅ InfluxDB 已连接到 mdc_full_mdc_network"
    else
        echo "✅ InfluxDB 已在 mdc_full_mdc_network 中"
    fi
fi

# 重启 API 服务以重新建立连接
echo "🔄 重启 API 服务..."
docker restart mdc_server_api

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 测试连接
echo "🧪 测试 API 连接..."
if curl -s http://localhost:9005/api/public/devices/configs | grep -q "error"; then
    echo "❌ API 仍然返回错误，需要进一步检查"
    echo "📋 建议检查："
    echo "   1. MongoDB 数据库是否包含必要的数据"
    echo "   2. API 服务的 MongoDB 连接字符串配置"
    echo "   3. API 服务日志：docker logs mdc_server_api"
else
    echo "✅ API 连接测试成功！"
fi

echo "🎉 网络修复完成！"
