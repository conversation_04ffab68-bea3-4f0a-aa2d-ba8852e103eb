/**
 * FANUC设备采集器REST API服务模块
 *
 * 功能概述：
 * 本模块提供FANUC设备采集器的完整REST API服务，包括设备控制、状态查询、
 * 系统管理、配置管理、缓存管理和实时事件流等功能，为前端界面和第三方系统
 * 提供标准化的HTTP接口
 *
 * 主要功能：
 * - 设备控制：启动、停止、刷新设备采集的REST接口
 * - 状态查询：获取设备状态、系统指标、健康检查的查询接口
 * - 系统管理：系统配置查看、性能监控、健康检查等管理接口
 * - 推送配置：推送器配置查看、更新、测试等配置接口
 * - 缓存管理：缓存指标查看、缓存清理等管理接口
 * - 事件流：基于Server-Sent Events的实时事件推送
 * - 静态文件：Web界面的静态文件服务
 *
 * 技术特性：
 * - RESTful设计：遵循REST架构风格的API设计
 * - 标准HTTP：使用标准HTTP方法和状态码
 * - JSON格式：统一的JSON请求和响应格式
 * - 中间件支持：日志记录、CORS跨域、错误处理等中间件
 * - 实时通信：Server-Sent Events实时事件推送
 * - 路由管理：基于gorilla/mux的灵活路由管理
 *
 * API分类：
 * - 设备控制API：/api/v1/devices/* - 设备启停控制
 * - 设备状态API：/api/v1/devices/* - 设备状态查询
 * - 系统管理API：/api/v1/system/* - 系统管理功能
 * - 推送配置API：/api/v1/pusher/* - 推送器配置管理
 * - 缓存管理API：/api/v1/cache/* - 缓存管理功能
 * - 事件流API：/api/v1/events - 实时事件流
 *
 * 安全特性：
 * - CORS支持：跨域资源共享配置
 * - 配置安全：敏感配置信息的安全过滤
 * - 错误处理：统一的错误响应格式
 * - 超时控制：HTTP请求的读写超时控制
 *
 * 监控集成：
 * - 请求日志：详细的HTTP请求日志记录
 * - 性能监控：请求处理时间统计
 * - 健康检查：系统和设备的健康状态检查
 * - 事件通知：实时的系统和设备事件通知
 *
 * 业务价值：
 * - 统一接口：为前端和第三方系统提供统一的API接口
 * - 实时监控：实时的设备状态和系统监控
 * - 远程控制：远程的设备控制和配置管理
 * - 系统集成：便于与MES、ERP等上层系统集成
 *
 * @package api
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package api

/**
 * 标准库和第三方库导入
 *
 * 标准库：
 * - encoding/json: JSON编码解码，用于API请求和响应处理
 * - fmt: 格式化输入输出，用于日志和错误信息格式化
 * - net/http: HTTP服务器，提供REST API服务
 * - time: 时间处理，用于超时控制和时间戳
 *
 * 第三方库：
 * - github.com/gorilla/mux: HTTP路由器，提供灵活的路由管理
 *
 * 项目内部包：
 * - fanuc_v2/config: 配置管理，获取系统配置信息
 * - fanuc_v2/manager: 设备管理器，提供设备管理功能
 * - fanuc_v2/models: 数据模型，API请求和响应的数据结构
 */
import (
	"encoding/json" /** JSON编码解码，用于API数据处理 */
	"fmt"           /** 格式化输入输出，用于日志和错误处理 */
	"net/http"      /** HTTP服务器，提供REST API服务 */
	"time"          /** 时间处理，用于超时和时间戳 */

	"fanuc_v2/config"  /** 配置管理，系统配置信息 */
	"fanuc_v2/manager" /** 设备管理器，设备管理功能 */
	"fanuc_v2/models"  /** 数据模型，API数据结构 */

	"github.com/gorilla/mux" /** HTTP路由器，提供路由管理 */
)

/**
 * REST API服务器结构体
 *
 * 功能：提供FANUC设备采集器的完整REST API服务
 *
 * 架构设计：
 * - MVC模式：控制器层，处理HTTP请求和响应
 * - 依赖注入：通过构造函数注入配置和设备管理器
 * - 中间件模式：支持日志、CORS等中间件
 * - RESTful设计：遵循REST架构风格
 *
 * 核心职责：
 * - 路由管理：定义和管理所有API路由
 * - 请求处理：处理HTTP请求并调用业务逻辑
 * - 响应格式化：统一的JSON响应格式
 * - 中间件处理：日志记录、CORS跨域等
 * - 静态文件服务：Web界面的静态文件服务
 *
 * @struct Server
 */
type Server struct {
	/** 系统配置，包含服务器配置和业务配置 */
	config *config.SystemConfig

	/** 设备管理器，提供设备管理和控制功能 */
	deviceManager *manager.DeviceManager

	/** HTTP路由器，管理所有API路由 */
	router *mux.Router

	/** HTTP服务器实例，提供HTTP服务 */
	server *http.Server
}

// NewServer 创建API服务器
func NewServer(cfg *config.SystemConfig, dm *manager.DeviceManager) *Server {
	s := &Server{
		config:        cfg,
		deviceManager: dm,
		router:        mux.NewRouter(),
	}

	s.setupRoutes()
	return s
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// API版本前缀
	api := s.router.PathPrefix("/api/v1").Subrouter()

	// 中间件
	api.Use(s.loggingMiddleware)
	api.Use(s.corsMiddleware)

	// API base
	api.HandleFunc("/ver", s.ver).Methods("GET")

	// 设备控制接口
	api.HandleFunc("/devices/start", s.startAllDevices).Methods("POST")
	api.HandleFunc("/devices/stop", s.stopAllDevices).Methods("POST")
	api.HandleFunc("/devices/{deviceId}/start", s.startDevice).Methods("POST")
	api.HandleFunc("/devices/{deviceId}/stop", s.stopDevice).Methods("POST")
	api.HandleFunc("/devices/refresh", s.refreshDevices).Methods("POST")

	// 设备状态接口
	api.HandleFunc("/devices", s.getDevices).Methods("GET")
	api.HandleFunc("/devices/{deviceId}", s.getDevice).Methods("GET")
	api.HandleFunc("/devices/{deviceId}/status", s.getDeviceStatus).Methods("GET")

	// 系统管理接口
	api.HandleFunc("/system/metrics", s.getSystemMetrics).Methods("GET")
	api.HandleFunc("/system/health", s.getSystemHealth).Methods("GET")
	api.HandleFunc("/system/config", s.getSystemConfig).Methods("GET")

	// 推送配置接口
	api.HandleFunc("/pusher/config", s.getPusherConfig).Methods("GET")
	api.HandleFunc("/pusher/config", s.updatePusherConfig).Methods("PUT")
	api.HandleFunc("/pusher/test", s.testPusher).Methods("POST")

	// 缓存管理接口
	api.HandleFunc("/cache/metrics", s.getCacheMetrics).Methods("GET")
	api.HandleFunc("/cache/clear", s.clearCache).Methods("POST")
	api.HandleFunc("/cache/{deviceId}/clear", s.clearDeviceCache).Methods("POST")

	// 事件流接口 (Server-Sent Events)
	api.HandleFunc("/events", s.getEventStream).Methods("GET")

	// 静态文件服务 (可选)
	s.router.PathPrefix("/").Handler(http.FileServer(http.Dir("./web/")))
}

// Start 启动服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)

	s.server = &http.Server{
		Addr:         addr,
		Handler:      s.router,
		ReadTimeout:  time.Duration(s.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(s.config.Server.WriteTimeout) * time.Second,
	}

	fmt.Printf("🚀 API服务器启动在 http://%s\n", addr)
	return s.server.ListenAndServe()
}

// Stop 停止服务器
func (s *Server) Stop() error {
	if s.server != nil {
		return s.server.Close()
	}
	return nil
}

// 中间件

// loggingMiddleware 日志中间件
func (s *Server) loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		next.ServeHTTP(w, r)
		duration := time.Since(start)

		fmt.Printf("[%s] %s %s - %v\n",
			time.Now().Format("2006-01-02 15:04:05"),
			r.Method, r.URL.Path, duration)
	})
}

// corsMiddleware CORS中间件
func (s *Server) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func (s *Server) ver(w http.ResponseWriter, r *http.Request) {
	s.writeSuccessResponse(w, "版本信息", map[string]string{
		"version": "2.0.7",
	})
}

// 设备控制接口

// startAllDevices 启动所有设备
func (s *Server) startAllDevices(w http.ResponseWriter, r *http.Request) {
	err := s.deviceManager.StartAllDevices()
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	s.writeSuccessResponse(w, "所有设备启动成功", nil)
}

// stopAllDevices 停止所有设备
func (s *Server) stopAllDevices(w http.ResponseWriter, r *http.Request) {
	err := s.deviceManager.StopAllDevices()
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	s.writeSuccessResponse(w, "所有设备停止成功", nil)
}

// startDevice 启动指定设备
func (s *Server) startDevice(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	deviceID := vars["deviceId"]

	err := s.deviceManager.StartDevice(deviceID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	s.writeSuccessResponse(w, fmt.Sprintf("设备 %s 启动成功", deviceID), nil)
}

// stopDevice 停止指定设备
func (s *Server) stopDevice(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	deviceID := vars["deviceId"]

	err := s.deviceManager.StopDevice(deviceID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	s.writeSuccessResponse(w, fmt.Sprintf("设备 %s 停止成功", deviceID), nil)
}

// refreshDevices 刷新设备配置
func (s *Server) refreshDevices(w http.ResponseWriter, r *http.Request) {
	err := s.deviceManager.RefreshDeviceConfigs()
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	s.writeSuccessResponse(w, "设备配置刷新成功", nil)
}

// 设备状态接口

// getDevices 获取所有设备
func (s *Server) getDevices(w http.ResponseWriter, r *http.Request) {
	devices := s.config.GetAllDeviceConfigs()
	metrics := s.deviceManager.GetSystemMetrics()

	var deviceList []map[string]interface{}
	for _, device := range devices {
		deviceInfo := map[string]interface{}{
			"id":      device.ID,
			"name":    device.Name,
			"ip":      device.IP,
			"port":    device.Port,
			"enabled": device.Enabled,
		}

		if deviceMetrics, exists := metrics.DeviceMetrics[device.ID]; exists {
			deviceInfo["status"] = deviceMetrics.Status
			deviceInfo["metrics"] = deviceMetrics
		}

		deviceList = append(deviceList, deviceInfo)
	}

	s.writeSuccessResponse(w, "获取设备列表成功", deviceList)
}

// getDevice 获取指定设备
func (s *Server) getDevice(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	deviceID := vars["deviceId"]

	device, exists := s.config.GetDeviceConfig(deviceID)
	if !exists {
		s.writeErrorResponse(w, http.StatusNotFound, "设备不存在")
		return
	}

	deviceStatus, err := s.deviceManager.GetDeviceStatus(deviceID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	deviceInfo := map[string]interface{}{
		"config":  device,
		"status":  deviceStatus.Status,
		"metrics": deviceStatus,
	}

	s.writeSuccessResponse(w, "获取设备信息成功", deviceInfo)
}

// getDeviceStatus 获取设备状态
func (s *Server) getDeviceStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	deviceID := vars["deviceId"]

	status, err := s.deviceManager.GetDeviceStatus(deviceID)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	s.writeSuccessResponse(w, "获取设备状态成功", status)
}

// 系统管理接口

// getSystemMetrics 获取系统指标
func (s *Server) getSystemMetrics(w http.ResponseWriter, r *http.Request) {
	metrics := s.deviceManager.GetSystemMetrics()
	s.writeSuccessResponse(w, "获取系统指标成功", metrics)
}

// getSystemHealth 获取系统健康状态
func (s *Server) getSystemHealth(w http.ResponseWriter, r *http.Request) {
	metrics := s.deviceManager.GetSystemMetrics()

	status := "healthy"
	if metrics.ConnectedDevices < metrics.TotalDevices {
		status = "degraded"
	}
	if metrics.ConnectedDevices == 0 && metrics.TotalDevices > 0 {
		status = "unhealthy"
	}

	health := models.HealthStatus{
		Status:     status,
		Timestamp:  time.Now(),
		SystemInfo: *metrics,
		Checks:     make(map[string]models.CheckResult),
	}

	// 添加各种健康检查
	health.Checks["devices"] = models.CheckResult{
		Status:    getCheckStatus(metrics.ConnectedDevices, metrics.TotalDevices),
		Message:   fmt.Sprintf("%d/%d 设备已连接", metrics.ConnectedDevices, metrics.TotalDevices),
		Timestamp: time.Now(),
	}

	health.Checks["cache"] = models.CheckResult{
		Status:    getCheckStatus(s.config.Cache.MaxSize-metrics.CacheSize, s.config.Cache.MaxSize),
		Message:   fmt.Sprintf("缓存使用: %d/%d", metrics.CacheSize, s.config.Cache.MaxSize),
		Timestamp: time.Now(),
	}

	s.writeSuccessResponse(w, "获取系统健康状态成功", health)
}

// getSystemConfig 获取系统配置
func (s *Server) getSystemConfig(w http.ResponseWriter, r *http.Request) {
	// 返回安全的配置信息（不包含敏感信息）
	safeConfig := map[string]interface{}{
		"server": s.config.Server,
		"devices": map[string]interface{}{
			"config_source":    s.config.Devices.ConfigSource,
			"auto_start":       s.config.Devices.AutoStart,
			"refresh_interval": s.config.Devices.RefreshInterval,
			"device_count":     len(s.config.Devices.Devices),
		},
		"data_push": map[string]interface{}{
			"type":       s.config.DataPush.Type,
			"enabled":    s.config.DataPush.Enabled,
			"batch_size": s.config.DataPush.BatchSize,
			"timeout":    s.config.DataPush.Timeout,
		},
		"cache":      s.config.Cache,
		"monitoring": s.config.Monitoring,
	}

	s.writeSuccessResponse(w, "获取系统配置成功", safeConfig)
}

// 辅助函数

// getCheckStatus 获取检查状态
func getCheckStatus(current, total int) string {
	if current == total {
		return "pass"
	} else if current > total/2 {
		return "warn"
	} else {
		return "fail"
	}
}

// writeSuccessResponse 写入成功响应
func (s *Server) writeSuccessResponse(w http.ResponseWriter, message string, data interface{}) {
	response := models.APIResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// writeErrorResponse 写入错误响应
func (s *Server) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	response := models.APIResponse{
		Success:   false,
		Message:   "请求失败",
		Error:     message,
		Timestamp: time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// 推送配置接口

// getPusherConfig 获取推送器配置
func (s *Server) getPusherConfig(w http.ResponseWriter, r *http.Request) {
	config := map[string]interface{}{
		"type":       s.config.DataPush.Type,
		"enabled":    s.config.DataPush.Enabled,
		"batch_size": s.config.DataPush.BatchSize,
		"timeout":    s.config.DataPush.Timeout,
		"config":     s.config.DataPush.Config,
	}

	s.writeSuccessResponse(w, "获取推送器配置成功", config)
}

// updatePusherConfig 更新推送器配置
func (s *Server) updatePusherConfig(w http.ResponseWriter, r *http.Request) {
	var req models.PushConfigRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.writeErrorResponse(w, http.StatusBadRequest, "请求参数解析失败")
		return
	}

	if err := s.deviceManager.UpdatePusherConfig(req); err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	s.writeSuccessResponse(w, "推送器配置更新成功", nil)
}

// testPusher 测试推送器
func (s *Server) testPusher(w http.ResponseWriter, r *http.Request) {
	// 创建测试数据
	testData := &models.DeviceData{
		DeviceID:   "test",
		DeviceName: "测试设备",
		DataType:   "fanuc_cnc",
		Timestamp:  time.Now(),
		RawData: map[string]interface{}{
			"test":      true,
			"message":   "这是一条测试数据",
			"connected": true,
		},
		Location: "test_location",
		Status: models.DeviceStatus{
			Connected: true,
			LastSeen:  time.Now(),
		},
	}

	// 尝试推送测试数据
	ctx := r.Context()
	if err := s.deviceManager.TestPush(ctx, testData); err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, fmt.Sprintf("推送测试失败: %v", err))
		return
	}

	s.writeSuccessResponse(w, "推送器测试成功", nil)
}

// 缓存管理接口

// getCacheMetrics 获取缓存指标
func (s *Server) getCacheMetrics(w http.ResponseWriter, r *http.Request) {
	// 这里需要从设备管理器获取缓存指标
	// 暂时返回基本信息
	metrics := map[string]interface{}{
		"size":     0, // s.deviceManager.GetCacheSize(),
		"max_size": s.config.Cache.MaxSize,
		"max_age":  s.config.Cache.MaxAge,
	}

	s.writeSuccessResponse(w, "获取缓存指标成功", metrics)
}

// clearCache 清空所有缓存
func (s *Server) clearCache(w http.ResponseWriter, r *http.Request) {
	// 这里需要调用设备管理器的清空缓存方法
	// if err := s.deviceManager.ClearAllCache(); err != nil {
	//     s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
	//     return
	// }

	s.writeSuccessResponse(w, "缓存清空成功", nil)
}

// clearDeviceCache 清空指定设备缓存
func (s *Server) clearDeviceCache(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	deviceID := vars["deviceId"]

	// 这里需要调用设备管理器的清空设备缓存方法
	// if err := s.deviceManager.ClearDeviceCache(deviceID); err != nil {
	//     s.writeErrorResponse(w, http.StatusInternalServerError, err.Error())
	//     return
	// }

	s.writeSuccessResponse(w, fmt.Sprintf("设备 %s 缓存清空成功", deviceID), nil)
}

// 事件流接口

// getEventStream 获取事件流 (Server-Sent Events)
func (s *Server) getEventStream(w http.ResponseWriter, r *http.Request) {
	// 设置SSE头部
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	// 获取查询参数
	deviceID := r.URL.Query().Get("device_id")
	eventType := r.URL.Query().Get("type")

	// 创建事件通道
	eventCh := make(chan *models.DeviceEvent, 10)
	defer close(eventCh)

	// 启动事件转发协程
	go func() {
		for {
			select {
			case <-r.Context().Done():
				return
			case event := <-s.deviceManager.GetEventChannel():
				// 过滤事件
				if deviceID != "" && event.DeviceID != deviceID {
					continue
				}
				if eventType != "" && event.Type != eventType {
					continue
				}

				select {
				case eventCh <- event:
				default:
					// 通道满了，跳过事件
				}
			}
		}
	}()

	// 发送事件流
	flusher, ok := w.(http.Flusher)
	if !ok {
		s.writeErrorResponse(w, http.StatusInternalServerError, "不支持事件流")
		return
	}

	// 发送初始连接事件
	fmt.Fprintf(w, "data: {\"type\":\"connected\",\"timestamp\":\"%s\"}\n\n", time.Now().Format(time.RFC3339))
	flusher.Flush()

	// 持续发送事件
	ticker := time.NewTicker(30 * time.Second) // 心跳
	defer ticker.Stop()

	for {
		select {
		case <-r.Context().Done():
			return
		case event := <-eventCh:
			eventData, _ := json.Marshal(event)
			fmt.Fprintf(w, "data: %s\n\n", eventData)
			flusher.Flush()
		case <-ticker.C:
			// 发送心跳
			fmt.Fprintf(w, "data: {\"type\":\"heartbeat\",\"timestamp\":\"%s\"}\n\n", time.Now().Format(time.RFC3339))
			flusher.Flush()
		}
	}
}

// 批量操作接口

// batchDeviceControl 批量设备控制
func (s *Server) batchDeviceControl(w http.ResponseWriter, r *http.Request) {
	var req models.DeviceControlRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.writeErrorResponse(w, http.StatusBadRequest, "请求参数解析失败")
		return
	}

	var results []map[string]interface{}
	var errors []string

	deviceIDs := req.DeviceIDs
	if req.DeviceID != "" {
		deviceIDs = append(deviceIDs, req.DeviceID)
	}

	for _, deviceID := range deviceIDs {
		result := map[string]interface{}{
			"device_id": deviceID,
			"success":   false,
		}

		var err error
		switch req.Action {
		case "start":
			err = s.deviceManager.StartDevice(deviceID)
		case "stop":
			err = s.deviceManager.StopDevice(deviceID)
		case "restart":
			err = s.deviceManager.StopDevice(deviceID)
			if err == nil {
				time.Sleep(1 * time.Second) // 等待1秒
				err = s.deviceManager.StartDevice(deviceID)
			}
		default:
			err = fmt.Errorf("不支持的操作: %s", req.Action)
		}

		if err != nil {
			result["error"] = err.Error()
			errors = append(errors, fmt.Sprintf("设备 %s: %v", deviceID, err))
		} else {
			result["success"] = true
		}

		results = append(results, result)
	}

	response := map[string]interface{}{
		"results": results,
		"total":   len(deviceIDs),
		"success": len(deviceIDs) - len(errors),
		"failed":  len(errors),
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	s.writeSuccessResponse(w, fmt.Sprintf("批量%s操作完成", req.Action), response)
}
