#!/bin/bash

# FANUC v2 采集监控脚本
# 用于实时监控采集循环状态，排查停止采集问题

echo "🔍 FANUC v2 采集监控脚本"
echo "=========================="
echo "监控目标: 排查停止采集问题"
echo "日志文件: logs/fanuc_v2.log"
echo "按 Ctrl+C 退出监控"
echo ""

# 创建日志目录
mkdir -p logs

# 检查日志文件是否存在
if [ ! -f "logs/fanuc_v2.log" ]; then
    echo "⚠️ 日志文件不存在，请先启动程序"
    echo "启动命令: go run ./cmd/main.go"
    exit 1
fi

# 显示最近的启动信息
echo "📊 最近的启动信息:"
echo "=================="
tail -n 20 logs/fanuc_v2.log | grep -E "启动|开始|初始化" | tail -5
echo ""

# 显示当前采集状态
echo "📈 当前采集状态:"
echo "================"
tail -n 50 logs/fanuc_v2.log | grep -E "采集循环|心跳|数据采集" | tail -10
echo ""

echo "🔄 开始实时监控..."
echo "=================="

# 实时监控关键日志
tail -f logs/fanuc_v2.log | while read line; do
    timestamp=$(echo "$line" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}')
    
    # 高亮显示关键事件
    if echo "$line" | grep -q "🚀 启动采集循环"; then
        echo -e "\033[1;32m[$timestamp] 🚀 采集循环启动\033[0m"
        echo "$line"
        echo ""
    elif echo "$line" | grep -q "🛑.*采集循环.*退出\|📊.*采集循环已停止"; then
        echo -e "\033[1;31m[$timestamp] 🛑 采集循环停止\033[0m"
        echo "$line"
        echo ""
    elif echo "$line" | grep -q "❌.*panic\|❌.*失败"; then
        echo -e "\033[1;31m[$timestamp] ❌ 错误事件\033[0m"
        echo "$line"
        echo ""
    elif echo "$line" | grep -q "💓.*心跳.*第[0-9]*0次"; then
        echo -e "\033[1;34m[$timestamp] 💓 心跳检查\033[0m"
        echo "$line" | grep -o "第[0-9]*次采集.*"
    elif echo "$line" | grep -q "📈 采集统计"; then
        echo -e "\033[1;33m[$timestamp] 📈 采集统计\033[0m"
        echo "$line" | grep -o "总次数=[0-9]*.*"
    elif echo "$line" | grep -q "⚠️.*耗时过长\|⚠️.*资源\|⚠️.*连接"; then
        echo -e "\033[1;33m[$timestamp] ⚠️ 警告事件\033[0m"
        echo "$line"
    elif echo "$line" | grep -q "✅.*连接建立成功\|✅.*数据采集完成"; then
        echo -e "\033[1;32m[$timestamp] ✅ 成功事件\033[0m"
        echo "$line" | grep -o "✅.*"
    fi
done
