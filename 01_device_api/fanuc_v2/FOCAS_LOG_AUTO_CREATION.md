# FOCAS通信日志文件自动创建功能

## 🎯 功能概述

为了确保FOCAS库能够正常工作，系统现在会自动检查并创建`focas_communication.log`文件。这个功能解决了FOCAS库在日志文件不存在时可能出现的问题。

## ✅ 已实现的功能

### 1. **自动目录创建**
- 检查`logs`目录是否存在
- 如果不存在，自动创建目录
- 设置适当的目录权限（755）

### 2. **自动文件创建**
- 检查`logs/focas_communication.log`文件是否存在
- 如果不存在，自动创建文件
- 写入初始化内容和时间戳
- 设置适当的文件权限（644）

### 3. **文件完整性验证**
- 验证文件是否可写
- 检查文件权限
- 确保文件内容正确

### 4. **程序启动时检查**
- 在程序启动时自动执行检查
- 不会因为日志文件问题导致程序启动失败
- 提供详细的日志输出

## 🔧 技术实现

### 核心函数

#### 1. `EnsureFocasLogFileExists()`
```go
// 可在程序启动时调用的公共函数
func EnsureFocasLogFileExists() error {
    fm := GetFOCASManager()
    
    // 设置默认日志路径
    logDir := "logs"
    logFilePath := filepath.Join(logDir, "focas_communication.log")
    
    // 确保日志目录存在
    if err := fm.ensureLogDirectory(logDir); err != nil {
        return fmt.Errorf("创建日志目录失败: %v", err)
    }
    
    // 设置日志文件路径
    fm.mu.Lock()
    fm.logFilePath = logFilePath
    fm.mu.Unlock()
    
    // 确保日志文件存在
    if err := fm.ensureFocasLogFile(); err != nil {
        return fmt.Errorf("确保FOCAS日志文件存在失败: %v", err)
    }
    
    return nil
}
```

#### 2. `ensureFocasLogFile()`
```go
// 确保FOCAS通信日志文件存在
func (fm *FOCASManager) ensureFocasLogFile() error {
    // 检查文件是否存在
    if _, err := os.Stat(fm.logFilePath); os.IsNotExist(err) {
        // 创建文件并写入初始内容
        file, err := os.Create(fm.logFilePath)
        if err != nil {
            return fmt.Errorf("创建FOCAS日志文件失败: %v", err)
        }
        defer file.Close()
        
        // 写入初始化信息
        initialContent := fmt.Sprintf("# FOCAS通信日志文件\n# 创建时间: %s\n# 用于记录FOCAS库的通信详情\n\n", 
            time.Now().Format("2006-01-02 15:04:05"))
        
        file.WriteString(initialContent)
        os.Chmod(fm.logFilePath, 0644)
    } else {
        // 验证文件是否可写
        return fm.verifyLogFileWritable()
    }
    
    return nil
}
```

#### 3. `verifyLogFileWritable()`
```go
// 验证日志文件是否可写
func (fm *FOCASManager) verifyLogFileWritable() error {
    file, err := os.OpenFile(fm.logFilePath, os.O_WRONLY|os.O_APPEND, 0644)
    if err != nil {
        return fmt.Errorf("无法以写入模式打开日志文件: %v", err)
    }
    defer file.Close()
    
    return nil
}
```

### 程序启动时的集成

在`cmd/main.go`中添加了启动时检查：

```go
/**
 * 确保FOCAS通信日志文件存在
 * 在程序启动时主动检查和创建FOCAS通信日志文件
 * 避免后续FOCAS库调用时因日志文件不存在而出错
 */
logger.Info("🔍 检查FOCAS通信日志文件...")
if err := collector.EnsureFocasLogFileExists(); err != nil {
    logger.Warnf("⚠️  FOCAS日志文件检查失败: %v", err)
    // 不退出程序，因为这不是致命错误
} else {
    logger.Info("✅ FOCAS通信日志文件检查完成")
}
```

## 🧪 测试验证

### 测试脚本
- `test_focas_log.sh`: 完整的功能测试脚本
- `test_focas_log_creation.go`: Go语言测试程序（需要Go环境）

### 测试覆盖
1. ✅ 目录不存在时的自动创建
2. ✅ 文件不存在时的自动创建
3. ✅ 文件已存在时的处理
4. ✅ 文件权限验证
5. ✅ 文件内容验证
6. ✅ 完整流程测试

### 测试结果
```
🎉 所有测试通过！
===================
✅ FOCAS日志目录自动创建功能正常
✅ FOCAS日志文件自动创建功能正常
✅ 文件权限设置正确
✅ 文件内容初始化正确
✅ 重复调用处理正确
```

## 📁 文件结构

```
01_device_api/fanuc_v2/
├── logs/
│   └── focas_communication.log    # 自动创建的FOCAS通信日志文件
├── collector/
│   └── focas_manager.go           # 包含自动创建逻辑
├── cmd/
│   └── main.go                    # 程序启动时调用检查
├── test_focas_log.sh              # 测试脚本
└── FOCAS_LOG_AUTO_CREATION.md     # 本文档
```

## 🚀 使用方法

### 自动使用
程序启动时会自动检查和创建FOCAS日志文件，无需手动操作。

### 手动测试
```bash
# 删除日志文件测试自动创建
rm logs/focas_communication.log

# 运行测试脚本
./test_focas_log.sh

# 启动程序（会自动创建文件）
go run ./cmd/main.go
```

## 💡 设计优势

1. **自动化**: 无需手动创建文件
2. **健壮性**: 处理各种异常情况
3. **非阻塞**: 不会因为日志文件问题导致程序启动失败
4. **权限安全**: 设置适当的文件和目录权限
5. **详细日志**: 提供详细的操作日志便于调试
6. **测试完备**: 提供完整的测试覆盖

## 🔍 故障排除

### 常见问题

1. **权限不足**: 确保程序有创建文件和目录的权限
2. **磁盘空间**: 确保有足够的磁盘空间
3. **路径问题**: 确保工作目录正确

### 日志输出示例

```
2025-07-17 15:05:39 INFO  🔍 检查FOCAS通信日志文件...
2025-07-17 15:05:39 INFO  FOCAS通信日志文件路径: logs/focas_communication.log
2025-07-17 15:05:39 INFO  FOCAS通信日志文件创建成功: logs/focas_communication.log
2025-07-17 15:05:39 INFO  ✅ FOCAS通信日志文件检查完成
```

## 🎯 总结

FOCAS通信日志文件自动创建功能已经完全实现并通过测试。这个功能确保了FOCAS库能够正常工作，避免了因为日志文件不存在而导致的问题。系统现在更加健壮和用户友好。
