# Docker Compose 配置文件
# MDC FANUC 设备采集器 v2 (mdc_dcf 1.0.2)
# 企业级多设备数据采集系统

version: '3.8'

services:
  # FANUC 设备采集器服务
  mdc_dcf:
    # 使用包含运行时FOCAS脚本执行且清理源代码的优化镜像
    image: mdc_dcf:2.0.0-runtime-fwlib-clean

    # 容器名称，与build-x86.sh中的配置保持一致
    container_name: mdc_dcf

    # 主机名配置 - 用于容器内部标识
    hostname: mdc_dcf

    # 平台架构配置 - 指定运行平台
    platform: linux/386

    # 重启策略：总是重启（除非手动停止）
    restart: unless-stopped

    # 端口映射配置
    ports:
      # FANUC API服务端口：主机端口9110映射到容器端口9110
      - "9110:9110"

    # 卷挂载配置 - 数据持久化和配置管理（适配优化镜像）
    volumes:
      # 日志目录挂载 - 持久化应用日志
      - ./logs2:/app/logs

      # 配置文件挂载 - 直接挂载到工作目录（优化镜像中没有/app/src）
      - ./config.json:/app/config.json:ro
      - ./config-docker-network.json:/app/config-docker-network.json:ro

      # 设备配置文件挂载（如果存在）
      - ./devices.json:/app/devices.json:ro

      # 缓存目录挂载 - 持久化缓存数据
      - ./cache2:/app/cache

    # 环境变量配置 - 适配优化镜像（运行时环境）
    environment:
      # 基础环境配置
      - TZ=Asia/Shanghai                    # 时区设置

      # 应用程序配置
      - SERVICE_NAME=mdc_dcf                # 服务名称
      - SERVICE_VERSION=2.0.0               # 服务版本（更新为优化版本）
      - API_PORT=9110                       # API服务端口

      # 日志配置
      - LOG_LEVEL=info                      # 日志级别
      - LOG_FORMAT=text                     # 日志格式

      # 监控配置
      - METRICS_ENABLED=true                # 启用指标监控
      - HEALTH_CHECK_INTERVAL=30            # 健康检查间隔（秒）
      - MAX_MEMORY_MB=512                   # 最大内存限制（MB）

      # 运行时配置
      - FANUC_CONFIG_FILE=config.json       # 配置文件名
      - FANUC_LOG_DIR=/app/logs             # 日志目录
      - FANUC_CACHE_DIR=/app/cache          # 缓存目录

    # 启动命令配置 - 适配优化镜像（无源码，直接运行编译好的程序）
    command: >
      /bin/bash -c "
        echo '🚀 启动FANUC采集器 v2.0.0 (优化版)...'
        cd /app

        # 创建必要的目录
        mkdir -p /app/logs /app/cache

        # 检查配置文件是否存在
        if [ ! -f config.json ]; then
            echo '❌ 配置文件 config.json 不存在'
            exit 1
        fi

        echo '📋 配置文件检查:'
        echo '   config.json: '$(ls -la config.json 2>/dev/null || echo '不存在')
        echo '   devices.json: '$(ls -la devices.json 2>/dev/null || echo '不存在')
        echo '   日志目录: '$(ls -ld /app/logs)
        echo '   缓存目录: '$(ls -ld /app/cache)

        # 检查编译好的程序是否存在
        if [ ! -f /app/bin/fanuc_collector ]; then
            echo '❌ 可执行文件不存在: /app/bin/fanuc_collector'
            echo '   这可能是镜像构建问题，请检查镜像是否正确构建'
            exit 1
        fi

        # 验证可执行文件权限
        chmod +x /app/bin/fanuc_collector

        # 显示程序信息
        echo '📋 程序信息:'
        echo '   可执行文件: '$(ls -la /app/bin/fanuc_collector)
        echo '   工作目录: '$(pwd)
        echo '   用户: '$(whoami)

        # 启动程序，使用绝对路径指定配置文件（更可靠）
        echo '▶️  启动程序: /app/bin/fanuc_collector -config /app/config.json'
        exec /app/bin/fanuc_collector -config /app/config.json
      "

    # 健康检查配置
    healthcheck:
      # 健康检查命令：检查API服务是否响应
      test: ["CMD", "curl", "-f", "http://localhost:9110/api/v1/health", "||", "exit", "1"]

      # 健康检查间隔时间
      interval: 30s

      # 健康检查超时时间
      timeout: 10s

      # 重试次数
      retries: 3

      # 启动等待时间
      start_period: 60s

    # 资源限制配置
    deploy:
      resources:
        # 资源限制
        limits:
          # 内存限制：512MB
          memory: 512M
          # CPU限制：1个CPU核心
          cpus: '1.0'

        # 资源预留
        reservations:
          # 预留内存：128MB
          memory: 128M
          # 预留CPU：0.25个核心
          cpus: '0.25'

    # 网络配置 - 连接到MDC系统网络
    networks:
      - mdc_full_mdc_network

    # 依赖服务配置（可选）
    # 如果需要等待其他服务启动，可以取消注释
    # depends_on:
    #   - mdc_server_api
    #   - mdc_device_collect

    # 容器标签配置
    labels:
      # 服务标识
      - "com.mdc.service.name=fanuc_collector"
      - "com.mdc.service.version=2.0.0"
      - "com.mdc.service.type=device_api"

      # 监控标签
      - "com.mdc.monitoring.enabled=true"
      - "com.mdc.monitoring.port=9110"
      - "com.mdc.monitoring.path=/api/v1/metrics"

      # 网络标签
      - "com.mdc.network.internal=true"
      - "com.mdc.network.api_port=9110"

      # 维护信息
      - "maintainer=FANUC Development Team"
      - "description=FANUC FOCAS2 Device Data Collector v2"

# 网络配置
networks:
  # 使用外部网络，与其他MDC服务共享
  # 这个网络应该已经存在，由主docker-compose文件创建
  mdc_full_mdc_network:
    external: true