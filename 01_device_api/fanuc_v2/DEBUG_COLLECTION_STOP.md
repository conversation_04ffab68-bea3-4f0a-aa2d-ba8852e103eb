# FANUC v2 采集停止问题排查指南

## 🎯 问题描述
系统在运行一段时间后（大约7分钟）会停止数据采集，需要找出真正的原因。

## 🔍 已加强的日志监控

### 1. 采集循环监控
- ✅ **启动/退出日志**: 记录采集循环的启动时间、退出时间和运行时长
- ✅ **心跳监控**: 每10次采集输出心跳信息，每100次输出统计信息
- ✅ **Panic捕获**: 详细记录panic信息和堆栈跟踪
- ✅ **性能监控**: 记录每次采集的耗时，警告过长的采集时间

### 2. 数据采集监控
- ✅ **连接监控**: 详细记录FOCAS连接建立和释放过程
- ✅ **数据读取监控**: 分别记录CNC ID、状态信息、动态数据、工件计数的读取耗时
- ✅ **资源监控**: 监控连接资源的获取和释放
- ✅ **错误捕获**: 详细记录所有采集过程中的错误

### 3. FOCAS连接监控
- ✅ **初始化监控**: 记录FOCAS库初始化过程和耗时
- ✅ **Handle管理**: 详细记录每个handle的创建和释放
- ✅ **连接性能**: 记录cnc_allclibhndl3和cnc_freelibhndl的调用耗时
- ✅ **错误分析**: 详细记录FOCAS API调用的返回值和错误码

### 4. 系统健康监控
- ✅ **健康检查**: 增强系统健康检查的日志输出
- ✅ **资源使用**: 监控系统资源使用情况
- ✅ **恢复流程**: 记录系统恢复流程的执行过程

## 🛠️ 排查工具

### 1. 实时监控脚本
```bash
./monitor_collection.sh
```
- 实时监控采集循环状态
- 高亮显示关键事件（启动、停止、错误、心跳）
- 彩色输出便于识别问题

### 2. 问题分析脚本
```bash
./debug_collection_stop.sh
```
- 分析日志文件中的采集停止模式
- 统计错误、panic、连接失败次数
- 查找7分钟停止的时间模式
- 提供排查建议

### 3. 手动日志分析
```bash
# 查看最近的采集循环状态
tail -f logs/fanuc_v2.log | grep -E "🚀|🛑|💓|❌"

# 查看FOCAS连接状态
tail -f logs/fanuc_v2.log | grep -E "FOCAS|cnc_allclibhndl3|cnc_freelibhndl"

# 查看性能问题
tail -f logs/fanuc_v2.log | grep -E "⚠️.*耗时过长|📈 采集统计"
```

## 🔍 重点排查方向

### 1. 采集循环异常退出
- **检查点**: 采集循环是否因为panic而退出
- **日志关键词**: `❌.*panic`, `🛑.*采集循环.*退出`
- **可能原因**: 
  - 未捕获的异常
  - 上下文取消
  - 资源耗尽

### 2. FOCAS连接问题
- **检查点**: FOCAS连接是否稳定
- **日志关键词**: `❌.*连接失败`, `cnc_allclibhndl3返回值`
- **可能原因**:
  - 网络超时
  - FOCAS库内部错误
  - Handle泄漏

### 3. 资源耗尽
- **检查点**: 系统资源是否耗尽
- **日志关键词**: `⚠️.*资源`, `获取连接资源失败`
- **可能原因**:
  - Handle未正确释放
  - 内存泄漏
  - 文件描述符耗尽

### 4. 性能问题
- **检查点**: 采集是否因为性能问题而停止
- **日志关键词**: `⚠️.*耗时过长`, `📈 采集统计`
- **可能原因**:
  - 网络延迟增加
  - 设备响应变慢
  - 系统负载过高

### 5. 定时器问题
- **检查点**: 定时器是否正常工作
- **日志关键词**: `💓.*心跳`, `⏰ 定时器触发`
- **可能原因**:
  - 定时器被阻塞
  - 系统时间问题
  - Goroutine泄漏

## 📊 监控指标

### 正常运行指标
- ✅ 心跳每10次采集输出一次
- ✅ 采集统计每100次输出一次
- ✅ 连接建立和释放成功
- ✅ 数据读取成功率高

### 异常指标
- ❌ 采集循环意外退出
- ❌ 连续连接失败
- ❌ 资源获取失败
- ❌ 采集耗时过长（超过间隔的50%）

### 警告指标
- ⚠️ 心跳通道满
- ⚠️ 数据读取部分失败
- ⚠️ 资源使用接近限制
- ⚠️ 采集耗时较长

## 🚀 使用步骤

1. **启动程序**:
   ```bash
   go run ./cmd/main.go
   ```

2. **开始监控**:
   ```bash
   # 终端1: 实时监控
   ./monitor_collection.sh
   
   # 终端2: 定期分析
   watch -n 30 ./debug_collection_stop.sh
   ```

3. **等待问题复现**: 让程序运行至少10分钟，观察是否出现7分钟停止的问题

4. **分析结果**: 当问题出现时，查看日志输出，重点关注停止前的事件序列

## 🎯 预期效果

通过这些增强的日志监控，我们应该能够：
- 精确定位采集停止的时间点
- 识别导致停止的具体原因
- 分析停止前的系统状态变化
- 找到7分钟停止问题的根本原因

一旦找到根本原因，就可以针对性地修复问题，彻底解决采集停止的问题。
