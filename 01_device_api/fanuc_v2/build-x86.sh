#!/bin/bash

# FANUC FOCAS2/Ethernet x86 Docker构建脚本
# Ubuntu 16.04 x86架构专用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
IMAGE_NAME="mdc_dcf"
IMAGE_TAG="2.0.1-dev" # ubuntu16.04-x86_v2
CONTAINER_NAME="mdc_dcf"
PLATFORM="linux/386"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 错误处理
error_exit() {
    log_error "$1"
    exit 1
}

# 显示帮助信息
show_help() {
    echo "FANUC FOCAS2/Ethernet x86 Docker构建脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  build     构建Docker镜像"
    echo "  deploy    构建镜像并部署运行程序"
    echo "  run       运行容器(交互模式)"
    echo "  run-auto  运行容器(后台模式)"
    echo "  stop      停止容器"
    echo "  restart   重启容器"
    echo "  logs      查看容器日志"
    echo "  shell     进入容器shell"
    echo "  clean     清理镜像和容器"
    echo "  status    查看容器状态"
    echo "  help      显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 build     # 构建镜像"
    echo "  $0 deploy    # 构建并部署运行程序"
    echo "  $0 run       # 运行容器"
    echo "  $0 shell     # 进入容器"
}

# 检查Docker是否支持x86平台
check_platform_support() {
    log_info "检查Docker平台支持..."
    
    if ! docker buildx version >/dev/null 2>&1; then
        log_warning "Docker Buildx未安装，尝试启用实验性功能"
        export DOCKER_CLI_EXPERIMENTAL=enabled
    fi
    
    # 检查是否支持多平台构建
    if docker buildx ls | grep -q "linux/386"; then
        log_success "Docker支持x86平台构建"
    else
        log_warning "Docker可能不支持x86平台，将尝试使用标准构建"
    fi
}

# 检查FOCAS文件和脚本
check_focas_files() {
    log_info "检查FOCAS文件和脚本..."

    # 检查FOCAS脚本
    if [ -f "fwlib/build-deps.sh" ]; then
        log_success "找到构建依赖脚本: fwlib/build-deps.sh"
    else
        log_warning "构建依赖脚本不存在: fwlib/build-deps.sh"
    fi

    if [ -f "fwlib/install.sh" ]; then
        log_success "找到安装脚本: fwlib/install.sh"
    else
        log_warning "安装脚本不存在: fwlib/install.sh"
    fi

    if [ ! -f "fwlib/libfocas32.so.1.0.5" ]; then
        log_warning "FOCAS库文件不存在: fwlib/libfocas32.so.1.0.5"
        log_info "请将FOCAS库文件放置在fwlib/目录中"
    else
        log_success "找到FOCAS库文件"
    fi

    if [ ! -f "fwlib/fwlib32.h" ]; then
        log_warning "FOCAS头文件不存在: fwlib/fwlib32.h"
        log_info "请将FOCAS头文件放置在fwlib/目录中"
    else
        log_success "找到FOCAS头文件"
    fi
}

# 构建Docker镜像
build_image() {
    log_info "开始构建Docker镜像..."
    
    check_platform_support
    check_focas_files
    
    log_info "构建参数:"
    echo "  镜像名称: $IMAGE_NAME:$IMAGE_TAG"
    echo "  平台: $PLATFORM"
    echo "  Dockerfile: ./Dockerfile-dev"
    
    # 使用buildx进行多平台构建
    if docker buildx version >/dev/null 2>&1; then
        log_info "使用Docker Buildx构建..."
        docker buildx build \
            --platform $PLATFORM \
            --tag $IMAGE_NAME:$IMAGE_TAG \
            --file ./Dockerfile-dev \
            --load \
            .
    else
        log_info "使用标准Docker构建..."
        docker build \
            --platform $PLATFORM \
            --tag $IMAGE_NAME:$IMAGE_TAG \
            --file ./Dockerfile-dev \
            .
    fi
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建成功: $IMAGE_NAME:$IMAGE_TAG"
        
        # 显示镜像信息
        log_info "镜像信息:"
        docker images $IMAGE_NAME:$IMAGE_TAG
    else
        error_exit "Docker镜像构建失败"
    fi
}

# 运行容器
run_container() {
    log_info "启动容器..."
    
    # 检查容器是否已存在
    if docker ps -a | grep -q $CONTAINER_NAME; then
        log_warning "容器 $CONTAINER_NAME 已存在"
        read -p "是否删除现有容器并重新创建？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker rm -f $CONTAINER_NAME
        else
            log_info "使用现有容器"
            docker start $CONTAINER_NAME
            return
        fi
    fi
    
    # 创建必要的目录
    mkdir -p logs configs
    
    # 运行容器
    docker run -d \
        --name $CONTAINER_NAME \
        --platform $PLATFORM \
        --hostname fanuc-dev-x86 \
        -v "$(pwd):/app/src" \
        -v "$(pwd)/logs:/app/logs" \
        -v "$(pwd)/configs:/app/configs" \
        -v "$(pwd)/fwlib:/app/fwlib" \
        -e GO_ENV=development \
        -e CGO_ENABLED=1 \
        -e GOOS=linux \
        -e GOARCH=386 \
        -e TZ=Asia/Shanghai \
        --restart unless-stopped \
        $IMAGE_NAME:$IMAGE_TAG \
        tail -f /dev/null
    
    if [ $? -eq 0 ]; then
        log_success "容器启动成功: $CONTAINER_NAME"
        log_info "容器状态:"
        docker ps | grep $CONTAINER_NAME
    else
        error_exit "容器启动失败"
    fi
}

# 使用docker-compose运行
run_compose() {
    log_info "使用docker-compose启动服务..."
    
    if [ ! -f "docker-compose.yml" ]; then
        error_exit "docker-compose.yml文件不存在"
    fi
    
    # 构建并启动服务
    docker-compose up -d --build
    
    if [ $? -eq 0 ]; then
        log_success "服务启动成功"
        log_info "服务状态:"
        docker-compose ps
    else
        error_exit "服务启动失败"
    fi
}

# 运行容器(交互模式)
run_container_interactive() {
    log_info "启动容器(交互模式)..."

    # 检查容器是否已存在并删除
    if docker ps -a | grep -q $CONTAINER_NAME; then
        log_info "删除现有容器..."
        docker rm -f $CONTAINER_NAME
    fi

    # 创建必要的目录
    mkdir -p logs configs

    log_info "启动交互式容器，按 Ctrl+C 退出..."

    # 运行容器(交互模式) - 使用mdc_full_mdc_network网络
    docker run -it --rm \
        --name $CONTAINER_NAME \
        --platform $PLATFORM \
        --hostname fanuc-dev-x86 \
        --network mdc_full_mdc_network \
        -p 9110:9110 \
        -v "$(pwd):/app/src" \
        -v "$(pwd)/logs:/app/logs" \
        -v "$(pwd)/configs:/app/configs" \
        -v "$(pwd)/fwlib:/app/fwlib" \
        -e GO_ENV=development \
        -e CGO_ENABLED=1 \
        -e GOOS=linux \
        -e GOARCH=386 \
        -e TZ=Asia/Shanghai \
        $IMAGE_NAME:$IMAGE_TAG \
        /bin/bash
}

# 停止容器
stop_container() {
    log_info "停止容器..."
    
    if docker ps | grep -q $CONTAINER_NAME; then
        docker stop $CONTAINER_NAME
        log_success "容器已停止: $CONTAINER_NAME"
    else
        log_warning "容器未运行: $CONTAINER_NAME"
    fi
}

# 重启容器
restart_container() {
    log_info "重启容器..."
    stop_container
    sleep 2
    
    if docker ps -a | grep -q $CONTAINER_NAME; then
        docker start $CONTAINER_NAME
        log_success "容器已重启: $CONTAINER_NAME"
    else
        log_warning "容器不存在，将创建新容器"
        run_container
    fi
}

# 查看日志
show_logs() {
    log_info "查看容器日志..."
    
    if docker ps -a | grep -q $CONTAINER_NAME; then
        docker logs -f $CONTAINER_NAME
    else
        log_error "容器不存在: $CONTAINER_NAME"
    fi
}

# 进入容器shell
enter_shell() {
    log_info "进入容器shell..."
    
    if docker ps | grep -q $CONTAINER_NAME; then
        docker exec -it $CONTAINER_NAME /bin/bash
    else
        log_error "容器未运行: $CONTAINER_NAME"
        log_info "尝试启动容器..."
        run_container
        sleep 3
        docker exec -it $CONTAINER_NAME /bin/bash
    fi
}

# 清理镜像和容器
clean_all() {
    log_warning "这将删除所有相关的容器和镜像"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理容器..."
        docker rm -f $CONTAINER_NAME 2>/dev/null || true
        
        log_info "清理镜像..."
        docker rmi $IMAGE_NAME:$IMAGE_TAG 2>/dev/null || true
        
        log_info "清理未使用的镜像..."
        docker image prune -f
        
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 部署容器 - 构建镜像并运行程序
deploy_container() {
    log_info "开始部署流程..."

    # 第一步：构建Docker镜像
    log_info "步骤1: 构建Docker镜像"
    build_image

    if [ $? -ne 0 ]; then
        error_exit "Docker镜像构建失败，部署终止"
    fi

    # 第二步：检查并停止现有容器
    log_info "步骤2: 检查现有容器"
    if docker ps -a | grep -q $CONTAINER_NAME; then
        log_warning "发现现有容器 $CONTAINER_NAME，正在停止并删除..."
        docker stop $CONTAINER_NAME 2>/dev/null || true
        docker rm $CONTAINER_NAME 2>/dev/null || true
    fi

    # 第三步：创建必要的目录
    log_info "步骤3: 创建必要的目录"
    mkdir -p logs configs

    # 第四步：编译Go程序
    log_info "步骤4: 在容器中编译Go程序"

    # 创建临时容器来编译程序
    TEMP_CONTAINER="${CONTAINER_NAME}-build"

    # 启动临时容器进行编译
    docker run --rm \
        --name $TEMP_CONTAINER \
        --platform $PLATFORM \
        -v "$(pwd):/app/src" \
        -v "$(pwd)/fwlib:/app/fwlib" \
        -e CGO_ENABLED=1 \
        -e GOOS=linux \
        -e GOARCH=386 \
        -w /app/src \
        $IMAGE_NAME:$IMAGE_TAG \
        /bin/bash -c "
            echo '🔨 开始编译Go程序...'

            # 检查Go模块
            if [ -f go.mod ]; then
                echo '📦 更新Go模块依赖...'
                go mod tidy
            fi

            # 编译主程序
            echo '⚙️  编译 cmd/main.go...'
            go build -o /app/bin/fanuc_collector ./cmd/main.go

            if [ \$? -eq 0 ]; then
                echo '✅ 编译成功'
                ls -la /app/bin/fanuc_collector
            else
                echo '❌ 编译失败'
                exit 1
            fi
        "

    if [ $? -ne 0 ]; then
        error_exit "程序编译失败，部署终止"
    fi

    # 第五步：启动生产容器
    log_info "步骤5: 启动生产容器"

    # 运行生产容器，自动执行编译好的程序
    docker run -d \
        --name $CONTAINER_NAME \
        --platform $PLATFORM \
        --hostname fanuc-prod-x86 \
        --network mdc_full_mdc_network \
        -p 9110:9110 \
        -v "$(pwd):/app/src" \
        -v "$(pwd)/logs:/app/logs" \
        -v "$(pwd)/configs:/app/configs" \
        -v "$(pwd)/fwlib:/app/fwlib" \
        -e GO_ENV=production \
        -e CGO_ENABLED=1 \
        -e GOOS=linux \
        -e GOARCH=386 \
        -e TZ=Asia/Shanghai \
        --restart unless-stopped \
        $IMAGE_NAME:$IMAGE_TAG \
        /bin/bash -c "
            echo '🚀 启动FANUC采集器...'
            cd /app/src

            # 检查编译的程序是否存在
            if [ ! -f /app/bin/fanuc_collector ]; then
                echo '❌ 编译的程序不存在，重新编译...'
                go build -o /app/bin/fanuc_collector ./cmd/main.go
            fi

            # 设置执行权限
            chmod +x /app/bin/fanuc_collector

            # 启动程序
            echo '▶️  启动程序: /app/bin/fanuc_collector'
            exec /app/bin/fanuc_collector
        "

    if [ $? -eq 0 ]; then
        log_success "部署成功！容器已启动: $CONTAINER_NAME"

        # 等待几秒让容器完全启动
        sleep 3

        log_info "容器状态:"
        docker ps | grep $CONTAINER_NAME

        log_info "查看启动日志:"
        docker logs --tail 20 $CONTAINER_NAME

        echo
        log_success "🎉 部署完成！"
        log_info "💡 使用以下命令管理容器:"
        echo "  查看日志: $0 logs"
        echo "  进入容器: $0 shell"
        echo "  停止容器: $0 stop"
        echo "  查看状态: $0 status"

    else
        error_exit "容器启动失败"
    fi
}

# 查看状态
show_status() {
    log_info "容器状态:"
    docker ps -a | grep $CONTAINER_NAME || echo "容器不存在"

    echo
    log_info "镜像信息:"
    docker images $IMAGE_NAME:$IMAGE_TAG || echo "镜像不存在"

    echo
    log_info "系统信息:"
    echo "Docker版本: $(docker --version)"
    echo "系统架构: $(uname -m)"
    echo "可用平台: $(docker buildx ls 2>/dev/null | grep -o 'linux/[^,]*' | tr '\n' ' ' || echo '标准Docker')"
}

# 主函数
main() {
    case "${1:-help}" in
        build)
            build_image
            ;;
        deploy)
            deploy_container
            ;;
        run-auto)
            run_container
            ;;
        run)
            run_container_interactive
            ;;
        compose)
            run_compose
            ;;
        stop)
            stop_container
            ;;
        restart)
            restart_container
            ;;
        logs)
            show_logs
            ;;
        shell)
            enter_shell
            ;;
        clean)
            clean_all
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本开始
echo "=================================================================="
echo "           FANUC FOCAS2/Ethernet x86 Docker管理脚本"
echo "=================================================================="
echo

# 运行主函数
main "$@"
