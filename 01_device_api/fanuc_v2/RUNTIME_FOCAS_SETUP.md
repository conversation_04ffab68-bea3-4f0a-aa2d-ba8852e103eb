# 运行时 FOCAS 脚本执行说明

## 🎯 为什么需要在运行时环境执行 fwlib 脚本？

虽然在构建阶段已经执行了 FOCAS 脚本，但在运行时环境中再次执行这些脚本有以下重要原因：

### 1. 环境一致性保证
- **构建环境 vs 运行环境**：构建阶段和运行时阶段可能有不同的系统配置
- **库路径验证**：确保运行时环境中的库路径配置正确
- **权限设置**：确保运行时用户有正确的文件访问权限

### 2. 动态链接器配置
- **ldconfig 更新**：确保动态链接器缓存包含 FOCAS 库
- **符号链接验证**：验证库文件的符号链接在运行时环境中正确
- **路径解析**：确保程序能在运行时正确找到 FOCAS 库

### 3. 系统级配置
- **环境变量设置**：某些脚本可能设置运行时需要的环境变量
- **系统服务配置**：可能需要配置系统级服务或守护进程
- **网络配置**：FOCAS 可能需要特定的网络配置

## 🔧 实现方案

### Dockerfile 修改

```dockerfile
# 运行时阶段
FROM i386/ubuntu:16.04 AS runtime

# ... 其他配置 ...

# 复制FOCAS脚本到运行时环境并执行
COPY fwlib/ /tmp/fwlib/
RUN echo "运行时环境配置FOCAS..." && \
    # 配置动态链接器
    echo "/usr/local/lib" > /etc/ld.so.conf.d/focas.conf && \
    ldconfig && \
    # 执行FOCAS相关脚本（如果存在）
    if [ -f "/tmp/fwlib/build-deps.sh" ]; then \
        echo "执行 build-deps.sh..." && \
        chmod +x /tmp/fwlib/build-deps.sh && \
        cd /tmp && \
        ./fwlib/build-deps.sh; \
    else \
        echo "build-deps.sh 脚本不存在，跳过执行"; \
    fi && \
    if [ -f "/tmp/fwlib/install.sh" ]; then \
        echo "执行 install.sh..." && \
        chmod +x /tmp/fwlib/install.sh && \
        cd /tmp && \
        ./fwlib/install.sh; \
    else \
        echo "install.sh 脚本不存在，跳过执行"; \
    fi && \
    # 清理临时文件
    echo "清理FOCAS临时文件..." && \
    rm -rf /tmp/fwlib
```

### 启动脚本增强

启动脚本中增加了更详细的 FOCAS 环境检查：

```bash
# 检查FOCAS库
if [ -f "/usr/local/lib/libfwlib32.so" ]; then
    echo "✓ FOCAS库文件存在"
else
    echo "⚠ FOCAS库文件未找到"
fi

# 检查动态链接
if ldconfig -p | grep -q "libfwlib32"; then
    echo "✓ 动态链接器已配置"
else
    echo "⚠ 动态链接器未配置"
fi

# 检查FOCAS配置文件
if [ -f "/etc/ld.so.conf.d/focas.conf" ]; then
    echo "✓ FOCAS动态链接配置存在"
else
    echo "⚠ FOCAS动态链接配置未找到"
fi
```

## 🔍 验证方法

### 1. 构建新镜像

```bash
# 使用新的构建脚本
./build-with-runtime-fwlib.sh
```

### 2. 验证 FOCAS 配置

```bash
# 检查动态链接器配置
docker run --rm mdc_dcf:2.0.0-runtime-fwlib ldconfig -p | grep fwlib

# 检查库文件
docker run --rm mdc_dcf:2.0.0-runtime-fwlib ls -la /usr/local/lib/libfwlib32*

# 检查配置文件
docker run --rm mdc_dcf:2.0.0-runtime-fwlib cat /etc/ld.so.conf.d/focas.conf
```

### 3. 测试程序启动

```bash
# 测试启动脚本
docker run --rm mdc_dcf:2.0.0-runtime-fwlib /app/entrypoint.sh /bin/bash -c "echo 测试完成"
```

## 📊 优势对比

### 之前的方案
- ❌ 只在构建阶段执行 FOCAS 脚本
- ❌ 运行时环境可能缺少必要配置
- ❌ 动态链接器可能未正确更新

### 优化后的方案
- ✅ 构建阶段和运行时阶段都执行 FOCAS 脚本
- ✅ 确保运行时环境配置完整
- ✅ 动态链接器在运行时环境中正确配置
- ✅ 启动脚本提供详细的环境检查

## 🚀 使用建议

1. **开发环境**：使用 `mdc_dcf:2.0.0-runtime-fwlib` 镜像进行测试
2. **生产环境**：验证 FOCAS 配置正确后部署
3. **故障排查**：使用启动脚本的详细检查信息定位问题

## ⚠️ 注意事项

1. **脚本幂等性**：确保 fwlib 脚本可以安全地多次执行
2. **权限问题**：运行时用户需要有执行脚本的权限
3. **依赖检查**：脚本应该检查依赖是否已安装
4. **错误处理**：脚本应该有适当的错误处理机制

## 🎉 预期效果

- 🔧 **配置完整性**：运行时环境 FOCAS 配置完整
- 🚀 **启动可靠性**：程序启动时能正确加载 FOCAS 库
- 🔍 **问题诊断**：启动脚本提供详细的环境检查信息
- 🛡️ **环境隔离**：每个容器都有独立完整的 FOCAS 环境
