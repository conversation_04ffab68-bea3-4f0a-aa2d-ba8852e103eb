#!/bin/bash

# FANUC v2 采集循环停止问题最终修复部署脚本
# 解决采集循环7分钟后停止的问题

echo "🔧 FANUC v2 采集循环停止问题最终修复"
echo "======================================"

# 检查当前目录
if [ ! -f "collector/device_collector.go" ]; then
    echo "❌ 错误: 请在 fanuc_v2 项目根目录下运行此脚本"
    exit 1
fi

# 备份原始文件
echo "📦 备份原始文件..."
BACKUP_DIR="backup_final_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp collector/device_collector.go "$BACKUP_DIR/"
echo "✅ 原始文件已备份到: $BACKUP_DIR/"

# 检查修改是否已应用
echo "🔍 检查修改状态..."
CHECKS_PASSED=0
TOTAL_CHECKS=6

# 检查1: lastCollectionAttemptTime 字段
if grep -q "lastCollectionAttemptTime.*time.Time" collector/device_collector.go; then
    echo "✅ 1. lastCollectionAttemptTime 字段已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 1. lastCollectionAttemptTime 字段未找到"
fi

# 检查2: 智能健康检查
if grep -q "基础超时时间：采集间隔的5倍" collector/device_collector.go; then
    echo "✅ 2. 智能健康检查机制已实现"
    ((CHECKS_PASSED++))
else
    echo "❌ 2. 智能健康检查机制未找到"
fi

# 检查3: 监控器panic恢复
if grep -q "采集循环监控器发生panic" collector/device_collector.go; then
    echo "✅ 3. 监控器panic恢复机制已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 3. 监控器panic恢复机制未找到"
fi

# 检查4: 防频繁重启机制
if grep -q "距离上次重启时间太近" collector/device_collector.go; then
    echo "✅ 4. 防频繁重启机制已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 4. 防频繁重启机制未找到"
fi

# 检查5: 心跳日志
if grep -q "采集循环心跳" collector/device_collector.go; then
    echo "✅ 5. 心跳日志机制已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 5. 心跳日志机制未找到"
fi

# 检查6: 时间戳更新机制
if grep -q "同时更新尝试时间" collector/device_collector.go; then
    echo "✅ 6. 时间戳更新机制已完善"
    ((CHECKS_PASSED++))
else
    echo "❌ 6. 时间戳更新机制未找到"
fi

echo ""
echo "📊 修改检查结果: $CHECKS_PASSED/$TOTAL_CHECKS 项通过"

if [ $CHECKS_PASSED -eq $TOTAL_CHECKS ]; then
    echo "✅ 所有修改已正确应用"
else
    echo "⚠️  部分修改未应用，请检查代码"
fi

# 编译测试
echo ""
echo "🔨 编译测试..."
if go build -o /tmp/fanuc_v2_final_test ./cmd/main.go 2>/dev/null; then
    echo "✅ 编译成功"
    rm -f /tmp/fanuc_v2_final_test
else
    echo "❌ 编译失败，请检查代码语法"
    echo "🔄 恢复备份文件..."
    cp "$BACKUP_DIR/device_collector.go" collector/
    echo "✅ 已恢复原始文件"
    exit 1
fi

# 显示修改摘要
echo ""
echo "📋 最终修复摘要:"
echo "1. ✨ 增强数据结构 - 添加时间跟踪和失败计数字段"
echo "2. 🧠 智能健康检查 - 动态超时和连续检查机制"
echo "3. 🛡️  增强监控器 - panic恢复和防频繁重启"
echo "4. 💪 增强采集循环 - 自动重启和心跳日志"
echo "5. ⏰ 完善时间戳更新 - 全面的活跃状态跟踪"

# 显示关键改进
echo ""
echo "🎯 关键改进:"
echo "- 解决采集循环7分钟后停止的问题"
echo "- 监控器能正确检测和自动重启采集循环"
echo "- 系统具备多层次的异常恢复能力"
echo "- 提供详细的运行状态监控和日志"

# 显示测试建议
echo ""
echo "🧪 测试建议:"
echo "1. 运行程序至少1小时，观察是否还会停止"
echo "2. 观察以下关键日志:"
echo "   - '采集循环心跳' - 正常运行指标"
echo "   - '采集循环健康检查失败' - 问题检测"
echo "   - '检测到采集循环可能停止' - 自动重启触发"
echo "3. 模拟网络中断，测试自动恢复能力"

# 显示部署后监控
echo ""
echo "📊 部署后监控指标:"
echo "✅ 正常运行指标:"
echo "  - '采集循环心跳: 第X次采集' - 每30次采集输出"
echo "  - '数据采集成功' - 成功采集数据"
echo "  - '采集循环健康检查恢复正常' - 健康状态恢复"
echo ""
echo "⚠️  问题检测指标:"
echo "  - '采集循环健康检查失败 (X/3)' - 健康检查失败"
echo "  - '检测到采集循环可能停止' - 自动重启触发"
echo "  - '采集循环已重新启动' - 重启成功"
echo ""
echo "❌ 异常情况指标:"
echo "  - 'panic' - 异常情况（应该很少出现）"
echo "  - '距离上次重启时间太近' - 防频繁重启触发"

# 显示验收标准
echo ""
echo "✅ 验收标准:"
echo "1. 系统运行时间超过1小时不中断"
echo "2. 监控器能正确检测和恢复问题"
echo "3. 数据采集保持连续性"
echo "4. 系统具备自动恢复能力"
echo "5. 日志记录完整，便于问题排查"

echo ""
echo "✅ 最终修复部署完成!"
echo "📁 备份文件位置: $BACKUP_DIR/"
echo "📖 详细说明请查看: COLLECTION_LOOP_FIX_FINAL.md"
echo ""
echo "🚀 建议运行命令:"
echo "  go build -o fanuc_v2 ./cmd/main.go"
echo "  ./fanuc_v2 -config config.json"
echo ""
echo "🔍 实时监控命令:"
echo "  tail -f logs/fanuc_v2.log | grep -E '心跳|健康检查|重新启动|panic'"
