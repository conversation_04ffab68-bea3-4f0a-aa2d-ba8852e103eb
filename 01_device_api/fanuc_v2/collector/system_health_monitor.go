package collector

import (
	"context"
	"math/rand"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// SystemHealthMonitor 系统级健康监控器
// 负责监控所有设备采集器的整体健康状态，检测系统性故障并进行恢复
type SystemHealthMonitor struct {
	mu                     sync.RWMutex                // 保护并发访问的读写锁
	collectors             map[string]*DeviceCollector // 所有设备采集器的映射
	lastGlobalCheck        time.Time                   // 上次全局检查时间
	consecutiveFailures    int                         // 连续系统性故障次数
	maxConsecutiveFailures int                         // 最大连续故障次数阈值
	checkInterval          time.Duration               // 健康检查间隔
	logger                 *logrus.Entry               // 日志记录器
	ctx                    context.Context             // 上下文
	cancel                 context.CancelFunc          // 取消函数
	running                bool                        // 监控器是否正在运行
}

// NewSystemHealthMonitor 创建新的系统健康监控器
func NewSystemHealthMonitor() *SystemHealthMonitor {
	ctx, cancel := context.WithCancel(context.Background())

	return &SystemHealthMonitor{
		collectors:             make(map[string]*DeviceCollector),
		maxConsecutiveFailures: 3,                // 连续3次系统性故障触发恢复
		checkInterval:          30 * time.Second, // 每30秒检查一次
		logger:                 logrus.WithField("component", "SystemHealthMonitor"),
		ctx:                    ctx,
		cancel:                 cancel,
	}
}

// RegisterCollector 注册设备采集器到监控器
func (shm *SystemHealthMonitor) RegisterCollector(deviceID string, collector *DeviceCollector) {
	shm.mu.Lock()
	defer shm.mu.Unlock()

	shm.collectors[deviceID] = collector
	shm.logger.Infof("注册设备采集器: %s，当前总数: %d", deviceID, len(shm.collectors))
}

// UnregisterCollector 从监控器中注销设备采集器
func (shm *SystemHealthMonitor) UnregisterCollector(deviceID string) {
	shm.mu.Lock()
	defer shm.mu.Unlock()

	delete(shm.collectors, deviceID)
	shm.logger.Infof("注销设备采集器: %s，当前总数: %d", deviceID, len(shm.collectors))
}

// Start 启动系统健康监控
func (shm *SystemHealthMonitor) Start() {
	shm.mu.Lock()
	if shm.running {
		shm.mu.Unlock()
		shm.logger.Warnf("系统健康监控器已在运行")
		return
	}
	shm.running = true
	shm.mu.Unlock()

	shm.logger.Infof("启动系统健康监控器，检查间隔: %v", shm.checkInterval)

	go shm.monitorLoop()
}

// Stop 停止系统健康监控
func (shm *SystemHealthMonitor) Stop() {
	shm.mu.Lock()
	defer shm.mu.Unlock()

	if !shm.running {
		shm.logger.Warnf("系统健康监控器未在运行")
		return
	}

	shm.logger.Infof("停止系统健康监控器")
	shm.cancel()
	shm.running = false
}

// monitorLoop 监控循环
func (shm *SystemHealthMonitor) monitorLoop() {
	ticker := time.NewTicker(shm.checkInterval)
	defer ticker.Stop()

	shm.logger.Infof("系统健康监控循环已启动")

	for {
		select {
		case <-shm.ctx.Done():
			shm.logger.Infof("系统健康监控循环收到停止信号")
			return
		case <-ticker.C:
			shm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (shm *SystemHealthMonitor) performHealthCheck() {
	checkStart := time.Now()
	shm.logger.Debugf("🔍 开始系统健康检查")

	shm.mu.RLock()
	collectors := make(map[string]*DeviceCollector)
	for id, collector := range shm.collectors {
		collectors[id] = collector
	}
	shm.mu.RUnlock()

	if len(collectors) == 0 {
		shm.logger.Debugf("⚠️ 没有注册的设备采集器")
		return
	}

	shm.logger.Debugf("📊 检查 %d 个设备采集器的健康状态", len(collectors))

	// 检查系统级健康状态
	systemHealthStart := time.Now()
	systemHealthy := shm.checkSystemHealth(collectors)
	systemHealthDuration := time.Since(systemHealthStart)

	if systemHealthy {
		shm.logger.Debugf("✅ 系统健康检查通过，耗时: %v", systemHealthDuration)
	} else {
		shm.logger.Warnf("❌ 系统健康检查失败，耗时: %v", systemHealthDuration)
	}

	// 检查资源使用情况
	resourceStart := time.Now()
	shm.checkResourceUsage()
	resourceDuration := time.Since(resourceStart)
	shm.logger.Debugf("📈 资源使用检查完成，耗时: %v", resourceDuration)

	// 记录健康状态
	logStart := time.Now()
	shm.logHealthStatus(collectors, systemHealthy)
	logDuration := time.Since(logStart)

	totalDuration := time.Since(checkStart)
	shm.logger.Debugf("📋 健康状态记录完成，耗时: %v，总检查耗时: %v", logDuration, totalDuration)

	// 更新检查时间
	shm.mu.Lock()
	shm.lastGlobalCheck = time.Now()
	shm.mu.Unlock()
}

// checkSystemHealth 检查系统级健康状态
func (shm *SystemHealthMonitor) checkSystemHealth(collectors map[string]*DeviceCollector) bool {
	healthyDevices := 0
	unhealthyDevices := 0
	activeDevices := 0

	for deviceID, collector := range collectors {
		if collector.IsCollectionHealthy() {
			healthyDevices++
		} else {
			unhealthyDevices++
			shm.logger.Debugf("设备不健康: %s", deviceID)
		}

		if collector.collecting {
			activeDevices++
		}
	}

	totalDevices := len(collectors)
	healthyRatio := float64(healthyDevices) / float64(totalDevices)

	shm.logger.Debugf("健康检查结果: 总设备=%d, 健康=%d, 不健康=%d, 活跃=%d, 健康率=%.2f",
		totalDevices, healthyDevices, unhealthyDevices, activeDevices, healthyRatio)

	// 如果所有活跃设备都不健康，认为是系统性故障
	systemFailure := activeDevices > 0 && healthyDevices == 0

	if systemFailure {
		shm.mu.Lock()
		shm.consecutiveFailures++
		consecutiveFailures := shm.consecutiveFailures
		shm.mu.Unlock()

		shm.logger.Warnf("检测到系统性故障: 所有活跃设备都不健康 (连续失败: %d/%d)",
			consecutiveFailures, shm.maxConsecutiveFailures)

		// 连续多次系统性故障，触发系统恢复
		if consecutiveFailures >= shm.maxConsecutiveFailures {
			shm.logger.Errorf("检测到严重系统性故障，触发系统恢复")
			shm.triggerSystemRecovery(collectors)

			shm.mu.Lock()
			shm.consecutiveFailures = 0
			shm.mu.Unlock()
		}

		return false
	}

	// 系统健康，重置连续失败计数
	shm.mu.Lock()
	if shm.consecutiveFailures > 0 {
		shm.logger.Infof("系统健康状态恢复，重置连续失败计数: %d -> 0", shm.consecutiveFailures)
		shm.consecutiveFailures = 0
	}
	shm.mu.Unlock()

	return true
}

// triggerSystemRecovery 触发系统恢复
func (shm *SystemHealthMonitor) triggerSystemRecovery(collectors map[string]*DeviceCollector) {
	shm.logger.Warnf("开始系统恢复流程...")

	// 1. 停止所有采集器
	shm.logger.Infof("步骤1: 停止所有采集器")
	for deviceID, collector := range collectors {
		shm.logger.Infof("停止采集器: %s", deviceID)
		collector.StopCollection()
		collector.Disconnect()
	}

	// 2. 重启FOCAS系统
	shm.logger.Infof("步骤2: 重启FOCAS系统")
	focasManager := GetFOCASManager()

	// 先清理现有的FOCAS资源
	if err := focasManager.CleanupFOCAS(); err != nil {
		shm.logger.Warnf("清理FOCAS资源失败: %v", err)
	}

	// 等待一段时间确保资源完全释放
	time.Sleep(2 * time.Second)

	// 重新初始化FOCAS库
	if err := focasManager.InitializeFOCAS(); err != nil {
		shm.logger.Errorf("重新初始化FOCAS系统失败: %v", err)
		return
	}

	// 3. 等待系统稳定
	shm.logger.Infof("步骤3: 等待系统稳定")
	time.Sleep(5 * time.Second)

	// 4. 重新启动所有采集器（错开启动时间）
	shm.logger.Infof("步骤4: 重新启动所有采集器")
	for deviceID, collector := range collectors {
		go func(id string, c *DeviceCollector) {
			// 随机延迟0-5秒，避免同时启动
			delay := time.Duration(rand.Intn(5)) * time.Second
			time.Sleep(delay)

			shm.logger.Infof("重新启动采集器: %s (延迟: %v)", id, delay)
			if err := c.StartCollection(context.Background()); err != nil {
				shm.logger.Errorf("重启采集器失败 [%s]: %v", id, err)
			} else {
				shm.logger.Infof("重启采集器成功: %s", id)
			}
		}(deviceID, collector)
	}

	shm.logger.Infof("系统恢复流程完成")
}

// checkResourceUsage 检查资源使用情况
func (shm *SystemHealthMonitor) checkResourceUsage() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 内存使用情况（MB）
	allocMB := m.Alloc / 1024 / 1024
	sysMB := m.Sys / 1024 / 1024

	// 如果内存使用超过阈值，触发GC
	if allocMB > 100 { // 100MB阈值
		shm.logger.Warnf("内存使用较高: 分配=%dMB, 系统=%dMB，触发GC", allocMB, sysMB)
		runtime.GC()

		// GC后重新检查
		runtime.ReadMemStats(&m)
		newAllocMB := m.Alloc / 1024 / 1024
		shm.logger.Infof("GC完成，内存使用: %dMB -> %dMB", allocMB, newAllocMB)
	}

	// 记录Goroutine数量
	numGoroutines := runtime.NumGoroutine()
	if numGoroutines > 100 { // 100个Goroutine阈值
		shm.logger.Warnf("Goroutine数量较多: %d", numGoroutines)
	}
}

// logHealthStatus 记录健康状态
func (shm *SystemHealthMonitor) logHealthStatus(collectors map[string]*DeviceCollector, systemHealthy bool) {
	healthyCount := 0
	collectingCount := 0

	for _, collector := range collectors {
		if collector.IsCollectionHealthy() {
			healthyCount++
		}
		if collector.collecting {
			collectingCount++
		}
	}

	totalCount := len(collectors)

	if systemHealthy {
		shm.logger.Debugf("系统健康检查: 正常 - 总设备=%d, 健康=%d, 采集中=%d",
			totalCount, healthyCount, collectingCount)
	} else {
		shm.logger.Warnf("系统健康检查: 异常 - 总设备=%d, 健康=%d, 采集中=%d",
			totalCount, healthyCount, collectingCount)
	}
}

// GetStatus 获取监控器状态
func (shm *SystemHealthMonitor) GetStatus() map[string]interface{} {
	shm.mu.RLock()
	defer shm.mu.RUnlock()

	status := map[string]interface{}{
		"running":                  shm.running,
		"registered_devices":       len(shm.collectors),
		"consecutive_failures":     shm.consecutiveFailures,
		"max_consecutive_failures": shm.maxConsecutiveFailures,
		"check_interval":           shm.checkInterval.String(),
	}

	if !shm.lastGlobalCheck.IsZero() {
		status["last_check"] = shm.lastGlobalCheck.Format("2006-01-02 15:04:05")
		status["time_since_last_check"] = time.Since(shm.lastGlobalCheck).String()
	}

	return status
}

// GetDeviceStatuses 获取所有设备状态
func (shm *SystemHealthMonitor) GetDeviceStatuses() map[string]map[string]interface{} {
	shm.mu.RLock()
	defer shm.mu.RUnlock()

	statuses := make(map[string]map[string]interface{})

	for deviceID, collector := range shm.collectors {
		statuses[deviceID] = map[string]interface{}{
			"healthy":    collector.IsCollectionHealthy(),
			"collecting": collector.collecting,
			"connected":  collector.IsConnected(),
		}
	}

	return statuses
}
