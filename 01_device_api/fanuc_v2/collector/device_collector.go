package collector

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"
	"unsafe"

	"fanuc_v2/config"
	"fanuc_v2/define"
	"fanuc_v2/logger"
	"fanuc_v2/models"
)

/*
#cgo CFLAGS: -I../fwlib
#cgo LDFLAGS: -L../fwlib -lfwlib32 -ldl
#include "fwlib32.h"
#include <stdlib.h>
*/
import "C"

/**
 * FANUC设备数据采集器模块
 *
 * 功能概述：
 * 本模块实现FANUC CNC设备的数据采集功能，使用FOCAS2库与设备进行通信，
 * 采集设备的实时状态、位置、程序、报警等信息，并提供可靠的连接管理和错误恢复机制
 *
 * 主要功能：
 * - 设备连接：建立和管理与FANUC CNC设备的FOCAS2连接
 * - 数据采集：定时采集设备的各种状态和运行数据
 * - 连接管理：独立连接模式，每次采集都建立新连接
 * - 错误恢复：完善的错误处理和自动重连机制
 * - 状态监控：实时监控设备连接状态和采集性能
 * - 数据推送：将采集的数据推送到数据处理通道
 *
 * 技术特性：
 * - CGO集成：使用CGO调用FANUC FOCAS2 C语言库
 * - 独立连接：每次数据采集都建立独立的FOCAS连接
 * - 并发安全：使用读写锁保护共享资源
 * - 上下文控制：使用context控制采集生命周期
 * - 错误隔离：单次采集失败不影响后续采集
 * - 内存安全：正确处理C语言内存分配和释放
 *
 * 采集策略：
 * - 定时采集：按配置的间隔定时采集数据
 * - 独立连接：每次采集建立新连接，避免连接状态问题
 * - 故障容错：连接失败时推送离线状态数据
 * - 重试机制：支持错误码-8的自动重连
 * - 数据完整性：确保采集数据的完整性和一致性
 *
 * 数据类型：
 * - 系统信息：CNC ID、系统配置、版本信息
 * - 状态信息：运行状态、报警状态、模式状态
 * - 位置数据：各轴位置、进给速度、主轴转速
 * - 程序信息：当前程序、序列号、程序名称
 * - 参数数据：工件计数等关键参数
 *
 * 可靠性保证：
 * - 连接隔离：每次采集独立连接，避免连接状态污染
 * - 错误恢复：自动处理各种FOCAS错误码
 * - 状态跟踪：详细的连接和采集状态跟踪
 * - 资源管理：正确的FOCAS资源创建和释放
 * - 异常处理：完善的panic恢复和错误处理
 *
 * 业务价值：
 * - 实时监控：提供FANUC设备的实时状态监控
 * - 数据完整：确保设备数据的完整采集和传输
 * - 系统稳定：高可靠的连接管理和错误恢复
 * - 运维友好：详细的日志记录和状态监控
 *
 * @package collector
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */

/**
 * 标准库和第三方库导入
 *
 * 标准库：
 * - context: 上下文控制，用于采集生命周期管理
 * - fmt: 格式化输入输出，用于错误信息和日志
 * - sync: 并发控制，用于读写锁和并发安全
 * - time: 时间处理，用于定时采集和超时控制
 * - unsafe: 不安全指针操作，用于C语言交互
 *
 * 项目内部包：
 * - fanuc_v2/config: 配置管理，获取设备配置信息
 * - fanuc_v2/logger: 日志记录，统一的日志输出
 * - fanuc_v2/models: 数据模型，设备数据结构定义
 */

/*
CGO编译配置和C语言接口定义

编译标志说明：
- CFLAGS: 指定FOCAS2头文件路径，../fwlib相对于collector目录
- LDFLAGS: 链接FOCAS2库文件和动态链接库
  - -L../fwlib: 指定库文件搜索路径
  - -lfwlib32: 链接FOCAS2库
  - -ldl: 链接动态加载库

头文件包含：
- fwlib32.h: FANUC FOCAS2库的主要头文件
- stdlib.h: 标准C库，提供内存管理函数
*/

/**
 * 导入C语言支持
 *
 * 功能：启用CGO功能，允许Go代码调用FOCAS2 C语言函数
 */

/**
 * 设备采集器结构体
 *
 * 功能：实现单个FANUC设备的数据采集功能，管理设备连接和数据采集流程
 *
 * 架构设计：
 * - 独立连接模式：每次采集都建立独立的FOCAS连接
 * - 并发安全：使用读写锁保护共享状态
 * - 通道通信：使用Go通道进行数据和错误传递
 * - 上下文控制：使用context控制采集生命周期
 *
 * 核心职责：
 * - 连接管理：建立、维护、释放FOCAS连接
 * - 数据采集：定时采集设备的各种数据
 * - 状态监控：监控设备连接和采集状态
 * - 错误处理：处理各种FOCAS错误和网络异常
 * - 数据传输：将采集的数据传输到处理通道
 *
 * 工作模式：
 * - 定时采集：按配置间隔定时执行数据采集
 * - 独立连接：每次采集建立新的FOCAS连接
 * - 异步处理：采集和数据处理异步进行
 * - 故障容错：连接失败时推送离线状态
 *
 * @struct DeviceCollector
 */
type DeviceCollector struct {
	/** 设备配置信息，包含IP、端口、采集间隔等参数 */
	config config.DeviceConfig

	/** 设备连接状态，true表示已连接，false表示未连接 */
	connected bool

	/** 采集运行状态，true表示正在采集，false表示已停止 */
	collecting bool

	/** 读写锁，保护共享状态的并发访问 */
	mu sync.RWMutex

	/** 数据通道，用于传输采集到的设备数据 */
	dataCh chan *models.DeviceData

	/** 错误通道，用于传输采集过程中的错误信息 */
	errorCh chan error

	/** 采集器性能指标，用于监控和统计 */
	metrics *CollectorMetrics

	/** 上下文对象，用于控制采集器的生命周期 */
	ctx context.Context

	/** 取消函数，用于停止采集器运行 */
	cancel context.CancelFunc

	/** 最后一次采集时间，用于健康检查 */
	lastCollectionTime time.Time

	/** 最后一次采集尝试时间，包括重连等操作，用于更准确的健康检查 */
	lastCollectionAttemptTime time.Time

	/** 连续失败次数，用于动态调整超时时间 */
	consecutiveFailures int

	/** 最大连续失败次数，超过此数值将触发特殊处理 */
	maxConsecutiveFailures int

	/** 连接失败计数器，用于控制断开连接数据的推送频率 */
	connectionFailureCount int

	/** 连接失败推送阈值，连续失败达到此次数才推送断开连接数据 */
	connectionFailureThreshold int

	/** FOCAS连接超时控制，用于防止连接卡死 */
	focasConnectTimeout time.Duration

	/** 连接池信号量，限制同时连接数 */
	connectionSemaphore chan struct{}

	/** 采集循环监控通道，用于检测采集循环是否正常运行 */
	heartbeatCh chan time.Time

	/** 资源监控器，用于监控连接和系统资源使用情况 */
	resourceMonitor *ResourceMonitor
}

/**
 * 采集器指标结构体
 *
 * 功能：记录设备采集器的运行统计和性能指标
 *
 * 统计维度：
 * - 采集统计：总次数、成功次数、失败次数
 * - 时间信息：最后采集时间、平均采集时间
 * - 状态信息：连接状态、采集状态
 * - 错误信息：最后错误的详细信息
 *
 * 用途：
 * - 性能监控：监控采集器的性能表现
 * - 故障诊断：记录错误信息，便于问题排查
 * - 运维管理：提供运维决策的数据支持
 * - 质量评估：评估数据采集的质量和稳定性
 *
 * @struct CollectorMetrics
 */
type CollectorMetrics struct {
	/** 总采集次数，包括成功和失败的采集 */
	TotalCollections int64

	/** 成功采集的次数 */
	SuccessfulCollections int64

	/** 失败采集的次数 */
	FailedCollections int64

	/** 最后一次采集的时间 */
	LastCollectionTime time.Time

	/** 最后一次错误的详细信息 */
	LastError string

	/** 平均采集时间，用于性能分析 */
	AvgCollectionTime time.Duration

	/** 当前连接状态 */
	IsConnected bool

	/** 当前采集状态 */
	IsCollecting bool
}

// NewDeviceCollector 创建设备采集器
func NewDeviceCollector(cfg config.DeviceConfig) *DeviceCollector {
	ctx, cancel := context.WithCancel(context.Background())

	logger.Infof("[%s:%s] 创建设备采集器: %s:%d", cfg.ID, cfg.IP, cfg.IP, cfg.Port)

	now := time.Now()
	return &DeviceCollector{
		config:                     cfg,
		dataCh:                     make(chan *models.DeviceData, 100),
		errorCh:                    make(chan error, 100),
		metrics:                    &CollectorMetrics{},
		ctx:                        ctx,
		cancel:                     cancel,
		lastCollectionTime:         now,
		lastCollectionAttemptTime:  now,
		consecutiveFailures:        0,
		maxConsecutiveFailures:     3,
		connectionFailureCount:     0,
		connectionFailureThreshold: 3,                      // 连续3次失败才推送断开连接数据
		focasConnectTimeout:        5 * time.Second,        // FOCAS连接超时5秒
		connectionSemaphore:        make(chan struct{}, 2), // 最多同时2个连接

		heartbeatCh:     make(chan time.Time, 10),
		resourceMonitor: NewResourceMonitor(10), // 默认最大10个连接
	}
}

// StartCollection 启动采集 - 使用独立连接模式
func (dc *DeviceCollector) StartCollection(ctx context.Context) error {
	logger.Infof("[%s:%s] 开始启动设备采集（独立连接模式）...", dc.config.ID, dc.config.IP)

	dc.mu.Lock()
	if dc.collecting {
		dc.mu.Unlock()
		logger.Infof("[%s:%s] 设备已在采集中", dc.config.ID, dc.config.IP)
		return nil
	}

	// 设置采集状态
	dc.collecting = true
	dc.metrics.IsCollecting = true
	dc.lastCollectionTime = time.Now()        // 初始化最后采集时间
	dc.lastCollectionAttemptTime = time.Now() // 初始化最后尝试时间
	dc.mu.Unlock()

	// 启动采集循环监控
	dc.startCollectionMonitor(ctx)

	// 直接启动采集循环，每次采集都会独立连接
	go dc.safeCollectionLoop(ctx)

	logger.Infof("[%s:%s] 设备采集已启动（每次采集独立连接）", dc.config.ID, dc.config.IP)
	return nil
}

// ConnectWithTimeout 带超时的连接方法（简化版）
// 在新的设计中，每次采集都是独立连接，所以这个方法主要用于测试连接
func (dc *DeviceCollector) ConnectWithTimeout(timeout time.Duration) error {
	logger.Debugf("[%s:%s] 测试连接，超时: %v", dc.config.ID, dc.config.IP, timeout)

	// 确保FOCAS库已初始化
	focasManager := GetFOCASManager()
	if err := focasManager.InitializeFOCAS(); err != nil {
		return fmt.Errorf("初始化FOCAS库失败: %v", err)
	}

	// 尝试建立连接进行测试
	var libhndl C.ushort
	if err := dc.establishConnection(&libhndl); err != nil {
		logger.Debugf("[%s:%s] 连接测试失败: %v", dc.config.ID, dc.config.IP, err)
		return err
	}

	// 立即释放连接
	dc.releaseConnection(libhndl)

	logger.Debugf("[%s:%s] 连接测试成功", dc.config.ID, dc.config.IP)
	return nil
}

// safeCollectionLoop 安全的采集循环
func (dc *DeviceCollector) safeCollectionLoop(ctx context.Context) {
	startTime := time.Now()
	logger.Infof("[%s:%s] 🚀 启动采集循环，开始时间: %v", dc.config.ID, dc.config.IP, startTime)

	defer func() {
		endTime := time.Now()
		duration := endTime.Sub(startTime)
		if r := recover(); r != nil {
			logger.Errorf("[%s:%s] ❌ 采集循环发生panic: %v，运行时长: %v", dc.config.ID, dc.config.IP, r, duration)
			// 打印堆栈信息
			logger.Errorf("[%s:%s] 堆栈信息: %s", dc.config.ID, dc.config.IP, debug.Stack())

			// 更新尝试时间，即使发生panic也要表示循环曾经运行过
			dc.mu.Lock()
			dc.lastCollectionAttemptTime = time.Now()
			dc.collecting = false
			dc.metrics.IsCollecting = false
			dc.mu.Unlock()

			// 发生panic时，尝试重新启动采集循环
			go func() {
				time.Sleep(5 * time.Second) // 等待5秒后重新启动
				logger.Infof("[%s:%s] 🔄 尝试重新启动采集循环", dc.config.ID, dc.config.IP)
				go dc.safeCollectionLoop(ctx)
			}()
		} else {
			// 正常退出时也要更新状态
			dc.mu.Lock()
			dc.collecting = false
			dc.metrics.IsCollecting = false
			dc.mu.Unlock()
			logger.Infof("[%s:%s] 🛑 采集循环正常退出，运行时长: %v", dc.config.ID, dc.config.IP, duration)
		}
		logger.Infof("[%s:%s] 📊 采集循环已停止，总运行时长: %v", dc.config.ID, dc.config.IP, duration)
	}()

	ticker := time.NewTicker(time.Duration(dc.config.CollectInterval) * time.Millisecond)
	defer ticker.Stop()

	// 添加心跳计数器，用于检测采集循环是否正常运行
	heartbeatCount := 0
	lastLogTime := time.Now()

	logger.Infof("[%s:%s] 📊 采集循环已启动，采集间隔: %dms", dc.config.ID, dc.config.IP, dc.config.CollectInterval)

	for {
		select {
		case <-ctx.Done():
			logger.Infof("[%s:%s] 🛑 采集循环收到停止信号，总采集次数: %d", dc.config.ID, dc.config.IP, heartbeatCount)
			return
		case tickTime := <-ticker.C:
			// 更新采集计数和心跳时间
			heartbeatCount++
			currentTime := time.Now()

			// 每10次采集输出一次详细日志，每100次输出一次统计信息
			if heartbeatCount%10 == 0 {
				timeSinceLastLog := currentTime.Sub(lastLogTime)
				avgInterval := timeSinceLastLog / 10
				logger.Infof("[%s:%s] 💓 采集循环心跳: 第%d次采集, 平均间隔: %v, 定时器时间: %v",
					dc.config.ID, dc.config.IP, heartbeatCount, avgInterval, tickTime.Format("15:04:05.000"))
				lastLogTime = currentTime
			}

			// 每100次采集输出一次统计信息
			if heartbeatCount%100 == 0 {
				totalRunTime := currentTime.Sub(startTime)
				avgCollectTime := totalRunTime / time.Duration(heartbeatCount)
				logger.Infof("[%s:%s] 📈 采集统计: 总次数=%d, 总运行时间=%v, 平均采集时间=%v",
					dc.config.ID, dc.config.IP, heartbeatCount, totalRunTime, avgCollectTime)
			}

			// 更新最后尝试时间，表示采集循环正在活跃
			dc.mu.Lock()
			dc.lastCollectionAttemptTime = time.Now()
			dc.mu.Unlock()

			// 发送心跳信号到监控通道
			select {
			case dc.heartbeatCh <- currentTime:
				logger.Debugf("[%s:%s] 💓 心跳信号发送成功", dc.config.ID, dc.config.IP)
			default:
				logger.Warnf("[%s:%s] ⚠️ 心跳通道满了，跳过这次心跳", dc.config.ID, dc.config.IP)
			}

			// 执行数据采集
			logger.Debugf("[%s:%s] 🔄 开始第 %d 次数据采集", dc.config.ID, dc.config.IP, heartbeatCount)
			collectionStart := time.Now()
			dc.performRealDataCollection()
			collectionDuration := time.Since(collectionStart)
			logger.Debugf("[%s:%s] ✅ 完成第 %d 次数据采集，耗时: %v", dc.config.ID, dc.config.IP, heartbeatCount, collectionDuration)

			// 如果采集时间过长，记录警告
			if collectionDuration > time.Duration(dc.config.CollectInterval)*time.Millisecond/2 {
				logger.Warnf("[%s:%s] ⚠️ 数据采集耗时过长: %v (间隔: %dms)",
					dc.config.ID, dc.config.IP, collectionDuration, dc.config.CollectInterval)
			}
		}
	}
}

// performRealDataCollection 执行真实数据采集 - 每次都独立连接和断开
func (dc *DeviceCollector) performRealDataCollection() {
	collectionStart := time.Now()
	logger.Debugf("[%s:%s] 🔄 开始数据采集，时间: %v", dc.config.ID, dc.config.IP, collectionStart)

	defer func() {
		collectionDuration := time.Since(collectionStart)
		if r := recover(); r != nil {
			logger.Errorf("[%s:%s] ❌ 数据采集发生panic: %v，耗时: %v", dc.config.ID, dc.config.IP, r, collectionDuration)
			logger.Errorf("[%s:%s] 堆栈信息: %s", dc.config.ID, dc.config.IP, debug.Stack())
			// 确保连接状态正确更新
			dc.updateConnectionStatus(false)
			// 更新尝试时间，即使发生panic也要表示循环还在运行
			dc.mu.Lock()
			dc.lastCollectionAttemptTime = time.Now()
			dc.mu.Unlock()
		} else {
			logger.Debugf("[%s:%s] ✅ 数据采集完成，总耗时: %v", dc.config.ID, dc.config.IP, collectionDuration)
		}
	}()

	// 检查资源限制
	// logger.Debugf("[%s:%s] 🔍 检查资源限制", dc.config.ID, dc.config.IP)
	// if err := dc.resourceMonitor.CheckResourceLimits(); err != nil {
	// 	logger.Warnf("[%s:%s] ⚠️ 资源检查失败: %v", dc.config.ID, dc.config.IP, err)
	// 	dc.pushDisconnectedData()
	// 	return
	// }

	// 获取连接资源
	// logger.Debugf("[%s:%s] 🔗 获取连接资源", dc.config.ID, dc.config.IP)
	// if err := dc.resourceMonitor.AcquireConnection(); err != nil {
	// 	logger.Warnf("[%s:%s] ⚠️ 获取连接资源失败: %v", dc.config.ID, dc.config.IP, err)
	// 	dc.pushDisconnectedData()
	// 	return
	// }

	// 每次采集都建立新的连接
	var libhndl C.ushort
	connected := false

	logger.Debugf("[%s:%s] 🔌 开始独立连接采集", dc.config.ID, dc.config.IP)

	// 确保在函数结束时释放连接资源
	defer func() {
		releaseStart := time.Now()
		if connected && libhndl != 0 {
			logger.Debugf("[%s:%s] 🔓 释放连接，句柄: %d", dc.config.ID, dc.config.IP, libhndl)
			dc.releaseConnection(libhndl)
		}
		dc.resourceMonitor.ReleaseConnection()
		dc.updateConnectionStatus(false)
		logger.Debugf("[%s:%s] 🔓 资源释放完成，耗时: %v", dc.config.ID, dc.config.IP, time.Since(releaseStart))
	}()

	// 1. 尝试建立连接
	connectStart := time.Now()
	logger.Debugf("[%s:%s] 🔗 尝试建立FOCAS连接", dc.config.ID, dc.config.IP)
	if err := dc.establishConnection(&libhndl); err != nil {
		connectDuration := time.Since(connectStart)
		logger.Warnf("[%s:%s] ❌ 建立连接失败: %v，耗时: %v", dc.config.ID, dc.config.IP, err, connectDuration)
		connected = false
		dc.updateConnectionStatus(false)

		// 处理连接失败计数器
		dc.handleConnectionFailure()
		return
	}

	connectDuration := time.Since(connectStart)
	connected = true
	dc.updateConnectionStatus(true)
	logger.Infof("[%s:%s] ✅ 连接建立成功，句柄: %d，耗时: %v", dc.config.ID, dc.config.IP, libhndl, connectDuration)

	// 连接成功，清零失败计数器
	dc.resetConnectionFailureCount()

	// 2. 创建设备数据
	data := models.NewDeviceData(dc.config.ID, dc.config.Name)
	data.DataType = dc.config.DataType
	data.Location = fmt.Sprintf("%s:%d", dc.config.IP, dc.config.Port)
	data.Status.Connected = true
	data.Status.LastSeen = time.Now()
	data.Status.CollectCount++

	// 设置设备配置信息（用于元数据构建）
	data.DeviceConfig = &models.DeviceConfigInfo{
		Brand:           dc.config.Brand,
		Model:           dc.config.Model,
		Location:        dc.config.Location,
		IP:              dc.config.IP,
		Port:            dc.config.Port,
		CollectInterval: dc.config.CollectInterval,
		DataType:        dc.config.DataType,
	}

	// 添加基础设备信息
	data.RawData["device_name"] = dc.config.Name
	data.RawData["device_ip"] = dc.config.IP
	data.RawData["device_port"] = dc.config.Port
	data.RawData["collect_interval"] = dc.config.CollectInterval
	data.RawData["timestamp"] = time.Now().Unix()

	// 3. 执行数据采集（使用当前连接的句柄）
	collectSuccess := true
	dataReadStart := time.Now()
	logger.Debugf("[%s:%s] 📖 开始数据读取，句柄: %d", dc.config.ID, dc.config.IP, libhndl)

	// 3.1 读取CNC ID (机器ID) - 使用句柄值
	cncIdStart := time.Now()
	if machineId := dc.readCNCID(libhndl); machineId != "" {
		data.RawData["machine_id"] = machineId
		logger.Debugf("[%s:%s] ✅ CNC ID读取成功: %s，耗时: %v", dc.config.ID, dc.config.IP, machineId, time.Since(cncIdStart))
	} else {
		logger.Warnf("[%s:%s] ❌ CNC ID读取失败，耗时: %v", dc.config.ID, dc.config.IP, time.Since(cncIdStart))
		collectSuccess = false
	}

	// 3.2 读取状态信息 - 使用句柄值
	statusStart := time.Now()
	if statusInfo := dc.readStatusInfo(libhndl); statusInfo != nil {
		for k, v := range statusInfo {
			data.RawData[k] = v
		}
		logger.Debugf("[%s:%s] ✅ 状态信息读取成功，耗时: %v", dc.config.ID, dc.config.IP, time.Since(statusStart))
	} else {
		logger.Warnf("[%s:%s] ❌ 状态信息读取失败，耗时: %v", dc.config.ID, dc.config.IP, time.Since(statusStart))
	}

	// 3.3 读取动态数据 - 使用句柄值
	dynamicStart := time.Now()
	if dynamicData := dc.readDynamicData(libhndl); dynamicData != nil {
		for k, v := range dynamicData {
			data.RawData[k] = v
		}
		logger.Debugf("[%s:%s] ✅ 动态数据读取成功，耗时: %v", dc.config.ID, dc.config.IP, time.Since(dynamicStart))
	} else {
		logger.Warnf("[%s:%s] ❌ 动态数据读取失败，耗时: %v", dc.config.ID, dc.config.IP, time.Since(dynamicStart))
	}

	// 3.4 读取工件计数参数 - 使用句柄值
	workpieceStart := time.Now()
	if workpieceCount := dc.readWorkpieceCount(libhndl); workpieceCount >= 0 {
		data.RawData["workpiece_count"] = workpieceCount
		logger.Debugf("[%s:%s] ✅ 工件计数读取成功: %d，耗时: %v", dc.config.ID, dc.config.IP, workpieceCount, time.Since(workpieceStart))
	} else {
		logger.Debugf("[%s:%s] ⚠️ 工件计数读取失败，耗时: %v", dc.config.ID, dc.config.IP, time.Since(workpieceStart))
	}

	dataReadDuration := time.Since(dataReadStart)
	logger.Debugf("[%s:%s] 📖 数据读取完成，总耗时: %v", dc.config.ID, dc.config.IP, dataReadDuration)

	// 4. 记录采集结果
	if collectSuccess {
		logger.Infof("[%s:%s] 数据采集成功: %s", dc.config.ID, dc.config.IP, data.DeviceID)
		// 更新最后采集时间，确保健康检查不会误判
		dc.mu.Lock()
		dc.lastCollectionTime = time.Now()
		dc.lastCollectionAttemptTime = time.Now() // 同时更新尝试时间
		dc.consecutiveFailures = 0                // 重置连续失败计数
		dc.mu.Unlock()

		// 发送心跳信号（非阻塞）
		select {
		case dc.heartbeatCh <- time.Now():
		default:
			// 心跳通道满，忽略
		}
	} else {
		logger.Infof("[%s:%s] 数据采集部分失败: %s", dc.config.ID, dc.config.IP, data.DeviceID)
		// 即使采集失败，也要更新尝试时间，表示循环还在运行
		dc.mu.Lock()
		dc.lastCollectionAttemptTime = time.Now()
		dc.consecutiveFailures++ // 增加连续失败计数
		dc.mu.Unlock()
	}

	// 5. 发送数据到通道（非阻塞）
	select {
	case dc.dataCh <- data:
		logger.Infof("[%s:%s] 数据已发送到处理通道", dc.config.ID, dc.config.IP)
		// 数据推送成功，清零连接失败计数器
		dc.resetConnectionFailureCount()
	default:
		logger.Infof("[%s:%s] 数据通道满，丢弃数据", dc.config.ID, dc.config.IP)
	}
}

// pushDisconnectedData 推送连接失败时的数据（兼容性函数，直接推送）
func (dc *DeviceCollector) pushDisconnectedData() {
	logger.Debugf("[%s:%s] 📤 直接推送连接失败状态数据（兼容模式）", dc.config.ID, dc.config.IP)

	// 更新最后尝试时间，表示采集循环正在活跃
	dc.mu.Lock()
	dc.lastCollectionAttemptTime = time.Now()
	dc.consecutiveFailures++ // 增加连续失败计数
	dc.mu.Unlock()

	// 直接调用内部推送函数
	dc.pushDisconnectedDataInternal()
}

// establishConnection 建立FOCAS连接（设备独立管理handle）- 增强版本，支持真正的超时控制
func (dc *DeviceCollector) establishConnection(libhndl *C.ushort) error {
	connectStart := time.Now()
	logger.Debugf("[%s:%s] 🔗 开始建立FOCAS连接到 %s:%d", dc.config.ID, dc.config.IP, dc.config.IP, dc.config.Port)

	// 1. 获取连接池信号量，限制同时连接数
	select {
	case dc.connectionSemaphore <- struct{}{}:
		logger.Debugf("[%s:%s] 🎫 获取连接池信号量成功", dc.config.ID, dc.config.IP)
		defer func() {
			<-dc.connectionSemaphore
			logger.Debugf("[%s:%s] 🎫 释放连接池信号量", dc.config.ID, dc.config.IP)
		}()
	case <-time.After(2 * time.Second):
		return fmt.Errorf("获取连接池信号量超时，系统繁忙")
	}

	// 2. 确保FOCAS库已初始化
	focasManager := GetFOCASManager()
	initStart := time.Now()
	if err := focasManager.InitializeFOCAS(); err != nil {
		logger.Errorf("[%s:%s] ❌ 初始化FOCAS库失败: %v，耗时: %v", dc.config.ID, dc.config.IP, err, time.Since(initStart))
		return fmt.Errorf("初始化FOCAS库失败: %v", err)
	}
	logger.Debugf("[%s:%s] ✅ FOCAS库初始化完成，耗时: %v", dc.config.ID, dc.config.IP, time.Since(initStart))

	// 3. 使用context实现真正的超时控制
	ctx, cancel := context.WithTimeout(context.Background(), dc.focasConnectTimeout)
	defer cancel()

	// 4. 在goroutine中调用FOCAS连接，实现真正的超时控制
	type connectionResult struct {
		ret    C.short
		handle C.ushort
		err    error
	}

	resultCh := make(chan connectionResult, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("[%s:%s] 🚨 FOCAS连接调用发生panic: %v", dc.config.ID, dc.config.IP, r)
				resultCh <- connectionResult{err: fmt.Errorf("FOCAS连接调用panic: %v", r)}
			}
		}()

		// 创建IP地址的C字符串
		ip := C.CString(dc.config.IP)
		defer C.free(unsafe.Pointer(ip))

		// 调用cnc_allclibhndl3获取library handle
		handleStart := time.Now()
		logger.Debugf("[%s:%s] 📞 调用cnc_allclibhndl3，配置超时: %d秒，实际超时: %v",
			dc.config.ID, dc.config.IP, dc.config.Timeout, dc.focasConnectTimeout)

		var tempHandle C.ushort
		ret := C.cnc_allclibhndl3(ip, C.ushort(dc.config.Port), C.long(dc.config.Timeout), &tempHandle)
		handleDuration := time.Since(handleStart)

		logger.Infof("[%s:%s] 📞 cnc_allclibhndl3返回值: %d, 耗时: %v, 句柄: %d",
			dc.config.ID, dc.config.IP, ret, handleDuration, tempHandle)

		resultCh <- connectionResult{ret: ret, handle: tempHandle, err: nil}
	}()

	// 5. 等待连接结果或超时
	select {
	case result := <-resultCh:
		if result.err != nil {
			return result.err
		}

		if result.ret != C.EW_OK {
			totalDuration := time.Since(connectStart)
			logger.Errorf("[%s:%s] ❌ FOCAS连接失败，错误码: %d，总耗时: %v",
				dc.config.ID, dc.config.IP, result.ret, totalDuration)
			return HandleConnectionError(dc.config.ID, result.ret)
		}

		*libhndl = result.handle
		totalDuration := time.Since(connectStart)
		logger.Infof("[%s:%s] ✅ FOCAS连接建立成功，句柄: %d，总耗时: %v",
			dc.config.ID, dc.config.IP, *libhndl, totalDuration)
		return nil

	case <-ctx.Done():
		totalDuration := time.Since(connectStart)
		logger.Errorf("[%s:%s] ⏰ FOCAS连接超时，超时时间: %v，总耗时: %v",
			dc.config.ID, dc.config.IP, dc.focasConnectTimeout, totalDuration)
		return fmt.Errorf("FOCAS连接超时，超过 %v", dc.focasConnectTimeout)
	}
}

// releaseConnection 释放FOCAS连接（设备独立管理handle）- 增强版本，支持超时控制
func (dc *DeviceCollector) releaseConnection(libhndl C.ushort) {
	releaseStart := time.Now()

	if libhndl == 0 {
		logger.Debugf("[%s:%s] 🔓 library handle为0，无需释放", dc.config.ID, dc.config.IP)
		return
	}

	logger.Debugf("[%s:%s] 🔓 开始释放FOCAS连接，句柄: %d", dc.config.ID, dc.config.IP, libhndl)

	// 使用超时控制释放连接，避免释放操作卡死
	done := make(chan bool, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("[%s:%s] 🚨 FOCAS连接释放发生panic: %v", dc.config.ID, dc.config.IP, r)
			}
			done <- true
		}()

		// 设备自己管理handle - 直接调用cnc_freelibhndl
		freeStart := time.Now()
		ret := C.cnc_freelibhndl(libhndl)
		freeDuration := time.Since(freeStart)

		logger.Debugf("[%s:%s] 🔓 cnc_freelibhndl返回值: %d，耗时: %v", dc.config.ID, dc.config.IP, ret, freeDuration)
	}()

	// 等待释放完成或超时
	select {
	case <-done:
		totalDuration := time.Since(releaseStart)
		logger.Debugf("[%s:%s] ✅ FOCAS连接释放完成，总耗时: %v", dc.config.ID, dc.config.IP, totalDuration)
	case <-time.After(3 * time.Second):
		totalDuration := time.Since(releaseStart)
		logger.Warnf("[%s:%s] ⏰ FOCAS连接释放超时，总耗时: %v", dc.config.ID, dc.config.IP, totalDuration)
	}

}

// cleanupFOCASResources 清理FOCAS资源，用于恢复卡死状态
func (dc *DeviceCollector) cleanupFOCASResources() {
	logger.Infof("[%s:%s] 🧹 开始清理FOCAS资源", dc.config.ID, dc.config.IP)

	// 获取FOCAS管理器
	focasManager := GetFOCASManager()

	// 尝试清理FOCAS进程资源
	done := make(chan bool, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("[%s:%s] 🚨 FOCAS资源清理发生panic: %v", dc.config.ID, dc.config.IP, r)
			}
			done <- true
		}()

		// 调用cnc_exitprocess清理资源
		logger.Debugf("[%s:%s] 📞 调用cnc_exitprocess清理资源", dc.config.ID, dc.config.IP)
		C.cnc_exitprocess()

		// 重新初始化FOCAS进程
		logger.Debugf("[%s:%s] 📞 调用cnc_startupprocess重新初始化", dc.config.ID, dc.config.IP)

		// 获取FOCAS管理器的日志文件路径
		focasManager := GetFOCASManager()
		logFilePath := "logs/focas_communication.log" // 默认路径
		if focasManager != nil {
			// 尝试获取实际的日志文件路径
			logFilePath = "logs/focas_communication.log"
		}

		// 创建日志文件名的C字符串
		logFileName := C.CString(logFilePath)
		defer C.free(unsafe.Pointer(logFileName))

		// 调用cnc_startupprocess，参数：日志级别(0=正常)，日志文件名
		ret := C.cnc_startupprocess(C.long(0), logFileName)
		logger.Debugf("[%s:%s] 📞 cnc_startupprocess返回值: %d", dc.config.ID, dc.config.IP, ret)
	}()

	// 等待清理完成或超时
	select {
	case <-done:
		logger.Infof("[%s:%s] ✅ FOCAS资源清理完成", dc.config.ID, dc.config.IP)
	case <-time.After(5 * time.Second):
		logger.Warnf("[%s:%s] ⏰ FOCAS资源清理超时", dc.config.ID, dc.config.IP)
	}

	// 重新初始化FOCAS管理器
	if err := focasManager.InitializeFOCAS(); err != nil {
		logger.Errorf("[%s:%s] ❌ 重新初始化FOCAS管理器失败: %v", dc.config.ID, dc.config.IP, err)
	} else {
		logger.Infof("[%s:%s] ✅ FOCAS管理器重新初始化成功", dc.config.ID, dc.config.IP)
	}
}

// updateConnectionStatus 更新连接状态
func (dc *DeviceCollector) updateConnectionStatus(connected bool) {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	dc.connected = connected
	dc.metrics.IsConnected = connected

	if connected {
		logger.Infof("[%s:%s] 连接状态更新: 已连接", dc.config.ID, dc.config.IP)
	} else {
		logger.Infof("[%s:%s] 连接状态更新: 已断开", dc.config.ID, dc.config.IP)
	}
}

// readCNCID 读取CNC ID并组成长字符串 - 简化版，无重连逻辑
func (dc *DeviceCollector) readCNCID(libhndl C.ushort) string {
	if libhndl == 0 {
		logger.Debugf("[%s:%s] 无效的句柄", dc.config.ID, dc.config.IP)
		return ""
	}

	var cncid [4]C.ulong
	ret := C.cnc_rdcncid(libhndl, &cncid[0])

	if ret == C.EW_OK {
		// 成功读取
		machineId := fmt.Sprintf("%08x-%08x-%08x-%08x",
			uint32(cncid[0]), uint32(cncid[1]), uint32(cncid[2]), uint32(cncid[3]))

		logger.Debugf("[%s:%s] 机器ID: %s (句柄: %d)", dc.config.ID, dc.config.IP, machineId, libhndl)
		return machineId
	}

	// 读取失败
	logger.Debugf("[%s:%s] 读取CNC ID失败: %d (句柄: %d)", dc.config.ID, dc.config.IP, ret, libhndl)
	return ""
}

// readStatusInfo 读取状态信息 - 简化版，无重连逻辑
func (dc *DeviceCollector) readStatusInfo(libhndl C.ushort) map[string]interface{} {
	if libhndl == 0 {
		logger.Debugf("[%s:%s] 无效的句柄", dc.config.ID, dc.config.IP)
		return nil
	}

	var statinfo C.ODBST
	ret := C.cnc_statinfo(libhndl, &statinfo)

	if ret == C.EW_SOCKET {
		logger.Debugf("[%s:%s] 状态信息读取返回-16，设备未连接 (句柄: %d)", dc.config.ID, dc.config.IP, libhndl)
		return (map[string]interface{}{"status": define.DeviceStatusShutdown})
	}

	if ret == C.EW_OK {
		// 成功读取
		statusData := make(map[string]interface{})

		// 使用英文字段名称
		statusData["alarm_status"] = int(statinfo.alarm)       // 报警状态
		statusData["auto_mode"] = int(statinfo.aut)            // 自动模式
		statusData["edit_status"] = int(statinfo.edit)         // 编辑状态
		statusData["emergency_stop"] = int(statinfo.emergency) // 急停状态
		statusData["handwheel_status"] = int(statinfo.hdck)    // 手轮状态
		statusData["motion_status"] = int(statinfo.motion)     // 运动状态
		statusData["spindle_status"] = int(statinfo.mstb)      // 主轴状态
		statusData["run_status"] = int(statinfo.run)           // 运行状态
		statusData["time_mode"] = int(statinfo.tmmode)         // 时间模式

		// 运行状态说明
		runStatusDesc := define.DeviceStatusUnknown

		// https://blog.csdn.net/yangxd_ah/article/details/107723136
		// https://github.com/15831944/PressSolution/blob/0aceee365cadd042c5c9bd016c170c0f8a8fca15/FanucCnc/Fanuc.cs#L3089
		logger.Debugf("aut=%d, run=%d, alarm=%d, emergency=%d", statinfo.aut, statinfo.run, statinfo.alarm, statinfo.emergency)

		if statinfo.alarm == 1 || statinfo.emergency > 0 {
			runStatusDesc = define.DeviceStatusAlarm
		} else if statinfo.run == 3 && int(statinfo.aut) == 1 {
			runStatusDesc = define.DeviceStatusProduction
		} else {
			runStatusDesc = define.DeviceStatusIdle
		}

		statusData["run_status_description"] = runStatusDesc

		logger.Debugf("[%s:%s] 状态信息读取成功: 运行状态=%s, 报警状态=%d (句柄: %d)",
			dc.config.ID, dc.config.IP, runStatusDesc, int(statinfo.alarm), libhndl)

		return statusData
	}

	// 读取失败
	logger.Debugf("[%s:%s] 读取状态信息失败: %d (句柄: %d)", dc.config.ID, dc.config.IP, ret, libhndl)
	return nil
}

// readDynamicData 读取动态数据 - 简化版，无重连逻辑
func (dc *DeviceCollector) readDynamicData(libhndl C.ushort) map[string]interface{} {
	if libhndl == 0 {
		logger.Debugf("[%s:%s] 无效的句柄", dc.config.ID, dc.config.IP)
		return nil
	}

	var odbdy C.ODBDY2
	ret := C.cnc_rddynamic2(libhndl, C.short(-1), C.short(C.sizeof_ODBDY2), &odbdy)

	if ret == C.EW_OK {
		// 成功读取
		dynamicData := make(map[string]interface{})

		// 使用英文字段名称
		dynamicData["axis_count"] = int(odbdy.dummy)               // 轴数
		dynamicData["alarm_status_dynamic"] = int(odbdy.alarm)     // 报警状态
		dynamicData["current_program_number"] = int(odbdy.prgnum)  // 当前程序号
		dynamicData["main_program_number"] = int(odbdy.prgmnum)    // 主程序号
		dynamicData["current_sequence_number"] = int(odbdy.seqnum) // 当前序列号
		dynamicData["actual_feedrate"] = int(odbdy.actf)           // 实际进给速度 (mm/min)
		dynamicData["actual_spindle_speed"] = int(odbdy.acts)      // 实际主轴转速 (rpm)

		logger.Debugf("[%s:%s] 动态数据读取成功: 程序号=%d, 进给速度=%d, 主轴转速=%d (句柄: %d)",
			dc.config.ID, dc.config.IP, int(odbdy.prgnum), int(odbdy.actf), int(odbdy.acts), libhndl)

		return dynamicData
	}

	// 读取失败
	logger.Debugf("[%s:%s] 读取动态数据失败: %d (句柄: %d)", dc.config.ID, dc.config.IP, ret, libhndl)
	return nil
}

// readWorkpieceCount 读取工件计数参数 - 简化版，无重连逻辑
func (dc *DeviceCollector) readWorkpieceCount(libhndl C.ushort) int {
	if libhndl == 0 {
		logger.Debugf("[%s:%s] 无效的句柄", dc.config.ID, dc.config.IP)
		return -1
	}

	var param C.IODBPSD
	length := C.short(4 + 4) // 4字节头部 + 4字节数据
	ret := C.cnc_rdparam(libhndl, C.short(6711), C.short(0), length, &param)

	if ret == C.EW_OK {
		// 成功读取
		workpieceCount := int(*(*C.long)(unsafe.Pointer(&param.u[0])))
		logger.Debugf("[%s:%s] 工件计数: %d (句柄: %d)", dc.config.ID, dc.config.IP, workpieceCount, libhndl)
		return workpieceCount
	}

	// 读取失败
	logger.Debugf("[%s:%s] 读取工件计数失败: %d (句柄: %d)", dc.config.ID, dc.config.IP, ret, libhndl)
	return -1
}

// 其他方法保持不变...
func (dc *DeviceCollector) reconnectLoop(ctx context.Context) {
	logger.Infof("[%s:%s] 启动重连循环", dc.config.ID, dc.config.IP)
	retryDelay := time.Duration(dc.config.RetryDelay) * time.Second
	maxRetries := dc.config.RetryCount
	retryCount := 0

	ticker := time.NewTicker(retryDelay)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Infof("[%s:%s] 重连循环收到停止信号", dc.config.ID, dc.config.IP)
			return
		case <-ticker.C:
			dc.mu.RLock()
			connected := dc.connected
			collecting := dc.collecting
			dc.mu.RUnlock()

			if connected {
				if !collecting {
					dc.mu.Lock()
					dc.collecting = true
					dc.metrics.IsCollecting = true
					dc.mu.Unlock()
					go dc.safeCollectionLoop(ctx)
					logger.Infof("[%s:%s] 重连成功，开始采集", dc.config.ID, dc.config.IP)
				}
				return
			}

			logger.Infof("[%s:%s] 尝试重连 (%d/%d)", dc.config.ID, dc.config.IP, retryCount+1, maxRetries)
			connectTimeout := time.Duration(dc.config.Timeout) * time.Second
			if err := dc.ConnectWithTimeout(connectTimeout); err != nil {
				retryCount++
				logger.Infof("[%s:%s] 重连失败 (%d/%d): %v", dc.config.ID, dc.config.IP, retryCount, maxRetries, err)

				if maxRetries > 0 && retryCount >= maxRetries {
					logger.Infof("[%s:%s] 达到最大重试次数，停止重连", dc.config.ID, dc.config.IP)
					return
				}
			} else {
				logger.Infof("[%s:%s] 重连成功", dc.config.ID, dc.config.IP)
				retryCount = 0
			}
		}
	}
}

func (dc *DeviceCollector) StopCollection() error {
	dc.mu.Lock()
	defer dc.mu.Unlock()
	dc.collecting = false
	dc.metrics.IsCollecting = false
	logger.Infof("[%s:%s] 设备采集已停止", dc.config.ID, dc.config.IP)
	return nil
}

func (dc *DeviceCollector) Disconnect() error {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	// 在新的设计中，每次采集都是独立连接，不需要释放持久handle
	// 只需要更新连接状态
	dc.connected = false
	dc.metrics.IsConnected = false
	logger.Infof("[%s:%s] 设备连接已断开", dc.config.ID, dc.config.IP)
	return nil
}

func (dc *DeviceCollector) GetDataChannel() <-chan *models.DeviceData {
	return dc.dataCh
}

func (dc *DeviceCollector) GetErrorChannel() <-chan error {
	return dc.errorCh
}

func (dc *DeviceCollector) GetMetrics() *CollectorMetrics {
	dc.mu.RLock()
	defer dc.mu.RUnlock()
	metrics := *dc.metrics
	return &metrics
}

func (dc *DeviceCollector) IsConnected() bool {
	dc.mu.RLock()
	defer dc.mu.RUnlock()
	return dc.connected
}

func (dc *DeviceCollector) IsCollecting() bool {
	dc.mu.RLock()
	defer dc.mu.RUnlock()
	return dc.collecting
}

func (dc *DeviceCollector) Close() error {
	dc.cancel()
	dc.StopCollection()
	dc.Disconnect()
	close(dc.dataCh)
	close(dc.errorCh)
	close(dc.heartbeatCh)
	return nil
}

// IsCollectionHealthy 检查采集循环是否健康
func (dc *DeviceCollector) IsCollectionHealthy() bool {
	dc.mu.RLock()
	collecting := dc.collecting
	lastTime := dc.lastCollectionTime
	lastAttemptTime := dc.lastCollectionAttemptTime
	consecutiveFailures := dc.consecutiveFailures
	dc.mu.RUnlock()

	if !collecting {
		return false
	}

	// 基础超时时间：采集间隔的5倍（增加容错时间）
	baseTimeout := time.Duration(dc.config.CollectInterval*5) * time.Millisecond

	// 如果有连续失败，增加额外的容错时间
	// 每次失败增加2个采集间隔的容错时间，最多增加10个间隔
	extraTimeout := time.Duration(consecutiveFailures*2) * time.Duration(dc.config.CollectInterval) * time.Millisecond
	maxExtraTimeout := time.Duration(dc.config.CollectInterval*10) * time.Millisecond
	if extraTimeout > maxExtraTimeout {
		extraTimeout = maxExtraTimeout
	}

	totalTimeout := baseTimeout + extraTimeout

	// 优先检查最后尝试时间，如果没有则检查最后采集时间
	checkTime := lastAttemptTime
	if checkTime.IsZero() {
		checkTime = lastTime
	}

	timeSinceLastActivity := time.Since(checkTime)
	isHealthy := timeSinceLastActivity < totalTimeout

	// 如果不健康，记录详细信息用于调试
	if !isHealthy {
		logger.Debugf("[%s|%s] 采集循环健康检查: 不健康 - 距离上次活动: %v, 超时阈值: %v, 连续失败: %d",
			dc.config.ID, dc.config.IP, timeSinceLastActivity, totalTimeout, consecutiveFailures)
	}

	return isHealthy
}

// startCollectionMonitor 启动采集循环监控
func (dc *DeviceCollector) startCollectionMonitor(ctx context.Context) {
	go func() {
		// 添加panic恢复，确保监控器不会崩溃
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("[%s|%s] 采集循环监控器发生panic: %v", dc.config.ID, dc.config.IP, r)
				// 重新启动监控器
				time.Sleep(5 * time.Second)
				dc.startCollectionMonitor(ctx)
			}
		}()

		// 监控间隔设置为采集间隔的5倍，减少误判
		monitorInterval := time.Duration(dc.config.CollectInterval*5) * time.Millisecond
		ticker := time.NewTicker(monitorInterval)
		defer ticker.Stop()

		consecutiveUnhealthyCount := 0
		maxUnhealthyCount := 2         // 连续2次不健康才重启（加快恢复速度）
		lastRestartTime := time.Time{} // 记录上次重启时间，防止频繁重启

		logger.Infof("[%s|%s] 采集循环监控器已启动，监控间隔: %v", dc.config.ID, dc.config.IP, monitorInterval)

		for {
			select {
			case <-ctx.Done():
				logger.Infof("[%s|%s] 采集循环监控器收到退出信号", dc.config.ID, dc.config.IP)
				return
			case <-ticker.C:
				dc.mu.RLock()
				collecting := dc.collecting
				dc.mu.RUnlock()

				if collecting {
					if !dc.IsCollectionHealthy() {
						consecutiveUnhealthyCount++
						logger.Debugf("[%s|%s] 采集循环健康检查失败 (%d/%d)",
							dc.config.ID, dc.config.IP, consecutiveUnhealthyCount, maxUnhealthyCount)

						// 只有连续多次检查都不健康才重启
						if consecutiveUnhealthyCount >= maxUnhealthyCount {
							// 检查是否距离上次重启太近（防止频繁重启）
							if time.Since(lastRestartTime) < 30*time.Second {
								logger.Warnf("[%s|%s] 距离上次重启时间太近，跳过本次重启", dc.config.ID, dc.config.IP)
								consecutiveUnhealthyCount = 0
								continue
							}

							logger.Warnf("[%s|%s] 检测到采集循环可能停止，尝试重新启动", dc.config.ID, dc.config.IP)
							lastRestartTime = time.Now()

							// 停止当前采集
							dc.mu.Lock()
							dc.collecting = false
							dc.metrics.IsCollecting = false
							dc.mu.Unlock()

							// 清理FOCAS资源，解决可能的卡死问题
							logger.Infof("[%s|%s] 🧹 在重启前清理FOCAS资源", dc.config.ID, dc.config.IP)
							dc.cleanupFOCASResources()

							// 等待一段时间后重新启动
							time.Sleep(3 * time.Second)

							// 重新启动采集循环
							if err := dc.StartCollection(ctx); err != nil {
								logger.Errorf("[%s|%s] 重新启动采集循环失败: %v", dc.config.ID, dc.config.IP, err)
								// 重启失败时，等待更长时间再重试
								time.Sleep(10 * time.Second)
							} else {
								logger.Infof("[%s|%s] 采集循环已重新启动", dc.config.ID, dc.config.IP)
							}

							consecutiveUnhealthyCount = 0 // 重置计数
						}
					} else {
						// 健康检查通过，重置计数
						if consecutiveUnhealthyCount > 0 {
							logger.Debugf("[%s|%s] 采集循环健康检查恢复正常", dc.config.ID, dc.config.IP)
							consecutiveUnhealthyCount = 0
						}
					}
				} else {
					consecutiveUnhealthyCount = 0 // 如果没有在采集，重置计数
				}
			}
		}
	}()
}

// handleConnectionFailure 处理连接失败，增加计数器并决定是否推送断开连接数据
func (dc *DeviceCollector) handleConnectionFailure() {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	// 增加连接失败计数器
	dc.connectionFailureCount++

	logger.Debugf("[%s:%s] 🔢 连接失败计数: %d/%d",
		dc.config.ID, dc.config.IP, dc.connectionFailureCount, dc.connectionFailureThreshold)

	// 检查是否达到推送阈值
	if dc.connectionFailureCount >= dc.connectionFailureThreshold {
		logger.Warnf("[%s:%s] ⚠️  连续连接失败达到阈值(%d次)，推送断开连接数据",
			dc.config.ID, dc.config.IP, dc.connectionFailureThreshold)

		// 推送断开连接数据
		dc.pushDisconnectedDataInternal()

		// 重置计数器，避免重复推送
		dc.connectionFailureCount = 0
		logger.Debugf("[%s:%s] 🔄 连接失败计数器已重置", dc.config.ID, dc.config.IP)
	} else {
		logger.Debugf("[%s:%s] 📊 连接失败未达到推送阈值，暂不推送断开连接数据",
			dc.config.ID, dc.config.IP)
	}

	// 更新最后尝试时间和连续失败计数
	dc.lastCollectionAttemptTime = time.Now()
	dc.consecutiveFailures++
}

// resetConnectionFailureCount 重置连接失败计数器
func (dc *DeviceCollector) resetConnectionFailureCount() {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	if dc.connectionFailureCount > 0 {
		logger.Debugf("[%s:%s] ✅ 连接成功，重置连接失败计数器 (之前: %d)",
			dc.config.ID, dc.config.IP, dc.connectionFailureCount)
		dc.connectionFailureCount = 0
	}

	// 同时重置连续失败计数
	if dc.consecutiveFailures > 0 {
		logger.Debugf("[%s:%s] ✅ 重置连续失败计数器 (之前: %d)",
			dc.config.ID, dc.config.IP, dc.consecutiveFailures)
		dc.consecutiveFailures = 0
	}
}

// pushDisconnectedDataInternal 内部推送断开连接数据的函数
func (dc *DeviceCollector) pushDisconnectedDataInternal() {
	logger.Infof("[%s:%s] 📤 推送连接失败状态数据", dc.config.ID, dc.config.IP)

	// 创建断开连接状态的设备数据
	data := models.NewDeviceData(dc.config.ID, dc.config.Name)
	data.DataType = dc.config.DataType
	data.Location = fmt.Sprintf("%s:%d", dc.config.IP, dc.config.Port)
	data.Status.Connected = false
	data.Status.LastSeen = time.Now()
	data.Status.CollectCount++

	// 设置设备配置信息
	data.DeviceConfig = &models.DeviceConfigInfo{
		Brand:           dc.config.Brand,
		Model:           dc.config.Model,
		Location:        dc.config.Location,
		IP:              dc.config.IP,
		Port:            dc.config.Port,
		CollectInterval: dc.config.CollectInterval,
		DataType:        dc.config.DataType,
	}

	// 添加基础设备信息
	data.RawData["device_name"] = dc.config.Name
	data.RawData["device_ip"] = dc.config.IP
	data.RawData["device_port"] = dc.config.Port
	data.RawData["collect_interval"] = dc.config.CollectInterval
	data.RawData["timestamp"] = time.Now().Unix()

	// 连接失败时的状态信息
	data.RawData["connected"] = false
	data.RawData["connection_status"] = "failed"
	data.RawData["error_message"] = "设备连接失败"
	data.RawData["failure_reason"] = "connection_failed"
	data.RawData["last_error_code"] = -16 // cnc_allclibhndl3返回的错误码

	// 设置默认值表示数据不可用
	data.RawData["machine_id"] = ""
	data.RawData["alarm_status"] = -1
	data.RawData["auto_mode"] = -1
	data.RawData["run_status"] = -1
	data.RawData["run_status_description"] = define.DeviceStatusShutdown
	data.RawData["current_program_number"] = -1
	data.RawData["actual_feedrate"] = -1
	data.RawData["actual_spindle_speed"] = -1
	data.RawData["workpiece_count"] = -1

	logger.Infof("[%s:%s] 📋 生成断开连接数据: %s (connected=false)", dc.config.ID, dc.config.IP, data.DeviceID)

	// 发送数据到通道（非阻塞）
	select {
	case dc.dataCh <- data:
		logger.Infof("[%s:%s] ✅ 断开连接数据已发送到处理通道", dc.config.ID, dc.config.IP)
	default:
		logger.Warnf("[%s:%s] ⚠️  数据通道满，丢弃断开连接数据", dc.config.ID, dc.config.IP)
	}
}
