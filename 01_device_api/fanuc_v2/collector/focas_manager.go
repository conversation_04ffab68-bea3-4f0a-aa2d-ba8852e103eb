package collector

/*
#cgo LDFLAGS: -L../fwlib -lfwlib32 -Wl,-rpath=../fwlib
#include "../fwlib/fwlib32.h"
#include <stdlib.h>
*/
import "C"

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
	"unsafe"

	"github.com/sirupsen/logrus"
)

// FOCASManager 基于FOCAS2文档的正确实现
// 根据官方文档，cnc_startupprocess主要用于创建日志文件，而不是管理进程
type FOCASManager struct {
	mu             sync.Mutex    // 保护并发访问的互斥锁
	initialized    bool          // 是否已初始化（调用过cnc_startupprocess）
	logFileCreated bool          // 日志文件是否已创建
	logFilePath    string        // 日志文件路径
	startTime      time.Time     // 初始化时间
	logger         *logrus.Entry // 日志记录器
}

// 全局FOCAS管理器实例
var (
	focasManager = &FOCASManager{
		logger: logrus.WithField("component", "FOCASManager"),
	}
)

// GetFOCASManager 获取全局FOCAS管理器实例
func GetFOCASManager() *FOCASManager {
	return focasManager
}

// EnsureFocasLogFileExists 确保FOCAS通信日志文件存在（可在程序启动时调用）
func EnsureFocasLogFileExists() error {
	fm := GetFOCASManager()

	// 设置默认日志路径
	logDir := "logs"
	logFilePath := filepath.Join(logDir, "focas_communication.log")

	// 确保日志目录存在
	if err := fm.ensureLogDirectory(logDir); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 设置日志文件路径
	fm.mu.Lock()
	fm.logFilePath = logFilePath
	fm.mu.Unlock()

	// 确保日志文件存在
	if err := fm.ensureFocasLogFile(); err != nil {
		return fmt.Errorf("确保FOCAS日志文件存在失败: %v", err)
	}

	fm.logger.Infof("FOCAS通信日志文件检查完成: %s", logFilePath)
	return nil
}

// InitializeFOCAS 初始化FOCAS库（基于文档的正确实现）
// 根据文档，这个函数主要是为了创建具有适当权限的日志文件
func (fm *FOCASManager) InitializeFOCAS() error {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	// 如果已经初始化，直接返回
	if fm.initialized {
		fm.logger.Debugf("FOCAS库已初始化，日志文件: %s", fm.logFilePath)
		return nil
	}

	fm.logger.Infof("开始初始化FOCAS库...")

	// 1. 确保日志目录存在并有适当权限
	logDir := "logs"
	if err := fm.ensureLogDirectory(logDir); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 2. 设置日志文件路径
	fm.logFilePath = filepath.Join(logDir, "focas_communication.log")
	fm.logger.Infof("FOCAS通信日志文件路径: %s", fm.logFilePath)

	// 3. 确保FOCAS通信日志文件存在
	if err := fm.ensureFocasLogFile(); err != nil {
		return fmt.Errorf("确保FOCAS日志文件存在失败: %v", err)
	}

	// 4. 调用cnc_startupprocess创建日志文件
	// 根据文档：在获取library handle之前调用，用于创建日志文件
	if err := fm.callStartupProcess(); err != nil {
		return fmt.Errorf("调用cnc_startupprocess失败: %v", err)
	}

	// 4. 标记为已初始化
	fm.initialized = true
	fm.logFileCreated = true
	fm.startTime = time.Now()

	fm.logger.Infof("FOCAS库初始化成功，日志文件: %s", fm.logFilePath)
	return nil
}

// ensureLogDirectory 确保日志目录存在并有适当权限
func (fm *FOCASManager) ensureLogDirectory(logDir string) error {
	// 检查目录是否存在
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		// 创建目录，权限设置为0755（所有者读写执行，组和其他用户读执行）
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return fmt.Errorf("创建目录失败: %v", err)
		}
		fm.logger.Infof("已创建日志目录: %s", logDir)
	}

	// 验证目录权限
	info, err := os.Stat(logDir)
	if err != nil {
		return fmt.Errorf("获取目录信息失败: %v", err)
	}

	// 检查是否有写权限
	if info.Mode().Perm()&0200 == 0 {
		return fmt.Errorf("日志目录没有写权限: %s", logDir)
	}

	fm.logger.Debugf("日志目录权限验证通过: %s (权限: %o)", logDir, info.Mode().Perm())
	return nil
}

// ensureFocasLogFile 确保FOCAS通信日志文件存在
func (fm *FOCASManager) ensureFocasLogFile() error {
	// 检查日志文件是否存在
	if _, err := os.Stat(fm.logFilePath); os.IsNotExist(err) {
		fm.logger.Infof("FOCAS通信日志文件不存在，正在创建: %s", fm.logFilePath)

		// 创建空的日志文件
		file, err := os.Create(fm.logFilePath)
		if err != nil {
			return fmt.Errorf("创建FOCAS日志文件失败: %v", err)
		}
		defer file.Close()

		// 写入初始化信息
		initialContent := fmt.Sprintf("# FOCAS通信日志文件\n# 创建时间: %s\n# 用于记录FOCAS库的通信详情\n\n",
			time.Now().Format("2006-01-02 15:04:05"))

		if _, err := file.WriteString(initialContent); err != nil {
			fm.logger.Warnf("写入日志文件初始内容失败: %v", err)
			// 不返回错误，因为文件已经创建成功
		}

		// 设置文件权限为644（所有者读写，组和其他用户只读）
		if err := os.Chmod(fm.logFilePath, 0644); err != nil {
			fm.logger.Warnf("设置日志文件权限失败: %v", err)
			// 不返回错误，因为文件已经创建成功
		}

		fm.logger.Infof("FOCAS通信日志文件创建成功: %s", fm.logFilePath)
	} else if err != nil {
		return fmt.Errorf("检查FOCAS日志文件状态失败: %v", err)
	} else {
		fm.logger.Debugf("FOCAS通信日志文件已存在: %s", fm.logFilePath)

		// 验证文件是否可写
		if err := fm.verifyLogFileWritable(); err != nil {
			return fmt.Errorf("FOCAS日志文件不可写: %v", err)
		}
	}

	return nil
}

// verifyLogFileWritable 验证日志文件是否可写
func (fm *FOCASManager) verifyLogFileWritable() error {
	// 尝试以追加模式打开文件
	file, err := os.OpenFile(fm.logFilePath, os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("无法以写入模式打开日志文件: %v", err)
	}
	defer file.Close()

	fm.logger.Debugf("FOCAS日志文件可写性验证通过: %s", fm.logFilePath)
	return nil
}

// callStartupProcess 调用cnc_startupprocess
func (fm *FOCASManager) callStartupProcess() error {
	// 创建日志文件名的C字符串
	// 根据文档，这个文件名会被FOCAS库用来创建通信日志
	logFileName := C.CString(fm.logFilePath)
	defer C.free(unsafe.Pointer(logFileName))

	fm.logger.Debugf("调用cnc_startupprocess，日志文件: %s", fm.logFilePath)

	// 调用FOCAS库函数
	// 参数1: 日志级别 (0=正常)
	// 参数2: 日志文件名
	ret := C.cnc_startupprocess(C.long(0), logFileName)

	fm.logger.Debugf("cnc_startupprocess返回值: %d", ret)

	if ret != C.EW_OK {
		return fmt.Errorf("cnc_startupprocess失败，错误码: %d", ret)
	}

	// 验证日志文件是否创建成功
	if _, err := os.Stat(fm.logFilePath); err != nil {
		fm.logger.Warnf("日志文件可能未创建: %v", err)
		// 不返回错误，因为某些情况下FOCAS可能不会立即创建文件
	} else {
		fm.logger.Infof("FOCAS通信日志文件创建成功: %s", fm.logFilePath)
	}

	return nil
}

// CleanupFOCAS 清理FOCAS库资源
// 根据文档，cnc_exitprocess会释放当前进程中所有已分配的library handles
func (fm *FOCASManager) CleanupFOCAS() error {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	if !fm.initialized {
		fm.logger.Debugf("FOCAS库未初始化，无需清理")
		return nil
	}

	fm.logger.Infof("开始清理FOCAS库资源...")

	// 调用cnc_exitprocess
	// 根据文档：释放当前进程中所有已分配的library handles
	ret := C.cnc_exitprocess()

	fm.logger.Debugf("cnc_exitprocess返回值: %d", ret)

	if ret != C.EW_OK {
		fm.logger.Warnf("cnc_exitprocess失败，错误码: %d", ret)
		// 不返回错误，因为清理过程中的错误不应该阻止程序退出
	} else {
		fm.logger.Infof("FOCAS库资源清理成功")
	}

	// 重置状态
	fm.initialized = false
	fm.logFileCreated = false
	uptime := time.Since(fm.startTime)
	fm.startTime = time.Time{}

	fm.logger.Infof("FOCAS库已清理，运行时间: %v", uptime)
	return nil
}

// GetStatus 获取FOCAS管理器状态
func (fm *FOCASManager) GetStatus() map[string]interface{} {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	status := map[string]interface{}{
		"initialized":      fm.initialized,
		"log_file_created": fm.logFileCreated,
		"log_file_path":    fm.logFilePath,
	}

	if fm.initialized {
		status["uptime"] = time.Since(fm.startTime).String()
		status["start_time"] = fm.startTime.Format("2006-01-02 15:04:05")
	}

	return status
}

// IsInitialized 检查是否已初始化
func (fm *FOCASManager) IsInitialized() bool {
	fm.mu.Lock()
	defer fm.mu.Unlock()
	return fm.initialized
}

// GetLogFilePath 获取日志文件路径
func (fm *FOCASManager) GetLogFilePath() string {
	fm.mu.Lock()
	defer fm.mu.Unlock()
	return fm.logFilePath
}

// GetUptime 获取运行时间
func (fm *FOCASManager) GetUptime() time.Duration {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	if !fm.initialized {
		return 0
	}

	return time.Since(fm.startTime)
}

// LogStatus 记录状态到日志
func (fm *FOCASManager) LogStatus() {
	status := fm.GetStatus()
	fm.logger.Infof("FOCAS管理器状态: %+v", status)
}

// ValidateLogFile 验证日志文件状态
func (fm *FOCASManager) ValidateLogFile() error {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	if !fm.initialized || fm.logFilePath == "" {
		return fmt.Errorf("FOCAS库未初始化")
	}

	// 检查日志文件是否存在
	info, err := os.Stat(fm.logFilePath)
	if err != nil {
		if os.IsNotExist(err) {
			fm.logger.Warnf("FOCAS日志文件不存在: %s", fm.logFilePath)
			return fmt.Errorf("日志文件不存在: %s", fm.logFilePath)
		}
		return fmt.Errorf("检查日志文件失败: %v", err)
	}

	// 检查文件权限
	if info.Mode().Perm()&0200 == 0 {
		return fmt.Errorf("日志文件没有写权限: %s", fm.logFilePath)
	}

	fm.logger.Debugf("日志文件验证通过: %s (大小: %d bytes, 权限: %o)",
		fm.logFilePath, info.Size(), info.Mode().Perm())

	return nil
}

// HandleConnectionError 处理连接错误
// 根据FOCAS库的错误码返回相应的错误信息
func HandleConnectionError(deviceID string, ret C.short) error {
	switch ret {
	case -16: // EW_SOCKET - Socket communication error
		return fmt.Errorf("[%s] 连接失败: 网络连接错误或设备不可达 (错误码: -16)", deviceID)
	case -8: // EW_FUNC - Function not available
		return fmt.Errorf("[%s] 连接失败: 功能不可用或参数错误 (错误码: -8)", deviceID)
	case -15: // EW_HANDLE - Handle allocation failed
		return fmt.Errorf("[%s] 连接失败: 句柄分配失败 (错误码: -15)", deviceID)
	case -17: // EW_NODLL - DLL not found
		return fmt.Errorf("[%s] 连接失败: FOCAS库文件未找到 (错误码: -17)", deviceID)
	case -1: // EW_BUSY - CNC is busy
		return fmt.Errorf("[%s] 连接失败: CNC系统忙碌 (错误码: -1)", deviceID)
	case -2: // EW_RESET - CNC is in reset state
		return fmt.Errorf("[%s] 连接失败: CNC系统处于复位状态 (错误码: -2)", deviceID)
	default:
		return fmt.Errorf("[%s] 连接失败: 未知错误 (错误码: %d)", deviceID, ret)
	}
}
