package collector

import (
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ResourceMonitor 资源监控器
// 负责监控系统资源使用情况，包括连接数、内存使用、Goroutine数量等
type ResourceMonitor struct {
	mu                 sync.RWMutex   // 保护并发访问的读写锁
	maxConnections     int            // 最大连接数限制
	currentConnections int            // 当前连接数
	memoryThresholdMB  uint64         // 内存使用阈值（MB）
	goroutineThreshold int            // Goroutine数量阈值
	logger             *logrus.Entry  // 日志记录器
	startTime          time.Time      // 监控器启动时间
	stats              *ResourceStats // 资源统计信息
}

// ResourceStats 资源统计信息
type ResourceStats struct {
	mu                 sync.RWMutex
	TotalConnections   int64     // 总连接数
	FailedConnections  int64     // 失败连接数
	MaxConcurrentConns int       // 最大并发连接数
	LastGCTime         time.Time // 上次GC时间
	GCCount            int64     // GC次数
	PeakMemoryUsageMB  uint64    // 峰值内存使用（MB）
	PeakGoroutineCount int       // 峰值Goroutine数量
}

// NewResourceMonitor 创建新的资源监控器
func NewResourceMonitor(maxConnections int) *ResourceMonitor {
	return &ResourceMonitor{
		maxConnections:     maxConnections,
		memoryThresholdMB:  100, // 默认100MB内存阈值
		goroutineThreshold: 100, // 默认100个Goroutine阈值
		logger:             logrus.WithField("component", "ResourceMonitor"),
		startTime:          time.Now(),
		stats:              &ResourceStats{},
	}
}

// AcquireConnection 获取连接资源
// 在建立新连接前调用，检查是否超过最大连接数限制
func (rm *ResourceMonitor) AcquireConnection() error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 检查连接数限制
	if rm.currentConnections >= rm.maxConnections {
		rm.stats.mu.Lock()
		rm.stats.FailedConnections++
		rm.stats.mu.Unlock()

		return fmt.Errorf("达到最大连接数限制: %d/%d", rm.currentConnections, rm.maxConnections)
	}

	// 增加当前连接数
	rm.currentConnections++

	// 更新统计信息
	rm.stats.mu.Lock()
	rm.stats.TotalConnections++
	if rm.currentConnections > rm.stats.MaxConcurrentConns {
		rm.stats.MaxConcurrentConns = rm.currentConnections
	}
	rm.stats.mu.Unlock()

	rm.logger.Debugf("获取连接资源成功，当前连接数: %d/%d", rm.currentConnections, rm.maxConnections)
	return nil
}

// ReleaseConnection 释放连接资源
// 在连接断开后调用，减少当前连接数
func (rm *ResourceMonitor) ReleaseConnection() {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if rm.currentConnections > 0 {
		rm.currentConnections--
		rm.logger.Debugf("释放连接资源，当前连接数: %d/%d", rm.currentConnections, rm.maxConnections)
	} else {
		// rm.logger.Warnf("尝试释放连接资源，但当前连接数已为0")
	}
}

// CheckResourceLimits 检查资源限制
// 检查内存使用、Goroutine数量等系统资源
func (rm *ResourceMonitor) CheckResourceLimits() error {
	// 检查连接数限制
	rm.mu.RLock()
	currentConns := rm.currentConnections
	maxConns := rm.maxConnections
	rm.mu.RUnlock()

	if currentConns >= maxConns {
		return fmt.Errorf("达到最大连接数限制: %d/%d", currentConns, maxConns)
	}

	// 检查内存使用情况
	if err := rm.checkMemoryUsage(); err != nil {
		return err
	}

	// 检查Goroutine数量
	if err := rm.checkGoroutineCount(); err != nil {
		return err
	}

	return nil
}

// checkMemoryUsage 检查内存使用情况
func (rm *ResourceMonitor) checkMemoryUsage() error {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 当前内存使用（MB）
	allocMB := m.Alloc / 1024 / 1024
	sysMB := m.Sys / 1024 / 1024

	// 更新峰值内存使用
	rm.stats.mu.Lock()
	if allocMB > rm.stats.PeakMemoryUsageMB {
		rm.stats.PeakMemoryUsageMB = allocMB
	}
	rm.stats.mu.Unlock()

	// 如果内存使用超过阈值
	if allocMB > rm.memoryThresholdMB {
		rm.logger.Warnf("内存使用超过阈值: 分配=%dMB, 系统=%dMB, 阈值=%dMB",
			allocMB, sysMB, rm.memoryThresholdMB)

		// 触发垃圾回收
		rm.triggerGC()

		// 如果内存使用过高，返回错误
		if allocMB > rm.memoryThresholdMB*2 {
			return fmt.Errorf("内存使用过高: %dMB (阈值: %dMB)", allocMB, rm.memoryThresholdMB)
		}
	}

	return nil
}

// checkGoroutineCount 检查Goroutine数量
func (rm *ResourceMonitor) checkGoroutineCount() error {
	numGoroutines := runtime.NumGoroutine()

	// 更新峰值Goroutine数量
	rm.stats.mu.Lock()
	if numGoroutines > rm.stats.PeakGoroutineCount {
		rm.stats.PeakGoroutineCount = numGoroutines
	}
	rm.stats.mu.Unlock()

	// 如果Goroutine数量超过阈值
	if numGoroutines > rm.goroutineThreshold {
		rm.logger.Warnf("Goroutine数量超过阈值: %d (阈值: %d)", numGoroutines, rm.goroutineThreshold)

		// 如果Goroutine数量过多，返回错误
		if numGoroutines > rm.goroutineThreshold*2 {
			return fmt.Errorf("Goroutine数量过多: %d (阈值: %d)", numGoroutines, rm.goroutineThreshold)
		}
	}

	return nil
}

// triggerGC 触发垃圾回收
func (rm *ResourceMonitor) triggerGC() {
	rm.logger.Infof("触发垃圾回收...")

	// 记录GC前的内存使用
	var beforeGC runtime.MemStats
	runtime.ReadMemStats(&beforeGC)
	beforeAllocMB := beforeGC.Alloc / 1024 / 1024

	// 执行垃圾回收
	runtime.GC()

	// 记录GC后的内存使用
	var afterGC runtime.MemStats
	runtime.ReadMemStats(&afterGC)
	afterAllocMB := afterGC.Alloc / 1024 / 1024

	// 更新统计信息
	rm.stats.mu.Lock()
	rm.stats.LastGCTime = time.Now()
	rm.stats.GCCount++
	rm.stats.mu.Unlock()

	rm.logger.Infof("垃圾回收完成: 内存使用 %dMB -> %dMB (释放: %dMB)",
		beforeAllocMB, afterAllocMB, beforeAllocMB-afterAllocMB)
}

// GetCurrentStatus 获取当前资源状态
func (rm *ResourceMonitor) GetCurrentStatus() map[string]interface{} {
	rm.mu.RLock()
	currentConns := rm.currentConnections
	maxConns := rm.maxConnections
	rm.mu.RUnlock()

	// 获取内存统计
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	allocMB := m.Alloc / 1024 / 1024
	sysMB := m.Sys / 1024 / 1024

	// 获取Goroutine数量
	numGoroutines := runtime.NumGoroutine()

	// 获取统计信息
	rm.stats.mu.RLock()
	stats := *rm.stats
	rm.stats.mu.RUnlock()

	return map[string]interface{}{
		"connections": map[string]interface{}{
			"current": currentConns,
			"max":     maxConns,
			"usage":   fmt.Sprintf("%.1f%%", float64(currentConns)/float64(maxConns)*100),
		},
		"memory": map[string]interface{}{
			"allocated_mb": allocMB,
			"system_mb":    sysMB,
			"threshold_mb": rm.memoryThresholdMB,
			"peak_mb":      stats.PeakMemoryUsageMB,
		},
		"goroutines": map[string]interface{}{
			"current":   numGoroutines,
			"threshold": rm.goroutineThreshold,
			"peak":      stats.PeakGoroutineCount,
		},
		"statistics": map[string]interface{}{
			"total_connections":    stats.TotalConnections,
			"failed_connections":   stats.FailedConnections,
			"max_concurrent_conns": stats.MaxConcurrentConns,
			"gc_count":             stats.GCCount,
			"last_gc_time":         stats.LastGCTime.Format("2006-01-02 15:04:05"),
			"uptime":               time.Since(rm.startTime).String(),
		},
	}
}

// SetMemoryThreshold 设置内存阈值
func (rm *ResourceMonitor) SetMemoryThreshold(thresholdMB uint64) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	oldThreshold := rm.memoryThresholdMB
	rm.memoryThresholdMB = thresholdMB
	rm.logger.Infof("内存阈值已更新: %dMB -> %dMB", oldThreshold, thresholdMB)
}

// SetGoroutineThreshold 设置Goroutine阈值
func (rm *ResourceMonitor) SetGoroutineThreshold(threshold int) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	oldThreshold := rm.goroutineThreshold
	rm.goroutineThreshold = threshold
	rm.logger.Infof("Goroutine阈值已更新: %d -> %d", oldThreshold, threshold)
}

// SetMaxConnections 设置最大连接数
func (rm *ResourceMonitor) SetMaxConnections(maxConns int) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if maxConns < rm.currentConnections {
		return fmt.Errorf("新的最大连接数(%d)不能小于当前连接数(%d)", maxConns, rm.currentConnections)
	}

	oldMax := rm.maxConnections
	rm.maxConnections = maxConns
	rm.logger.Infof("最大连接数已更新: %d -> %d", oldMax, maxConns)
	return nil
}

// GetConnectionUsage 获取连接使用率
func (rm *ResourceMonitor) GetConnectionUsage() float64 {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	if rm.maxConnections == 0 {
		return 0
	}

	return float64(rm.currentConnections) / float64(rm.maxConnections)
}

// IsResourceHealthy 检查资源是否健康
func (rm *ResourceMonitor) IsResourceHealthy() bool {
	// 检查连接使用率
	if rm.GetConnectionUsage() > 0.9 { // 90%使用率阈值
		return false
	}

	// 检查内存使用
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	allocMB := m.Alloc / 1024 / 1024
	if allocMB > rm.memoryThresholdMB {
		return false
	}

	// 检查Goroutine数量
	numGoroutines := runtime.NumGoroutine()
	if numGoroutines > rm.goroutineThreshold {
		return false
	}

	return true
}

// LogResourceStatus 记录资源状态到日志
func (rm *ResourceMonitor) LogResourceStatus() {
	status := rm.GetCurrentStatus()
	rm.logger.Infof("资源监控状态: %+v", status)
}
