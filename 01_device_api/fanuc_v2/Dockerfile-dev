# FANUC FOCAS2/Ethernet 开发环境 - Ubuntu 16.04 x86
# 专门用于x86架构的FOCAS开发

# FROM --platform=linux/386 ubuntu:16.04
FROM i386/ubuntu:16.04

# 代理配置参数
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG http_proxy
ARG https_proxy
ARG no_proxy

# 设置代理环境变量
ENV HTTP_PROXY=${HTTP_PROXY}
ENV HTTPS_PROXY=${HTTPS_PROXY}
ENV NO_PROXY=${NO_PROXY}
ENV http_proxy=${http_proxy}
ENV https_proxy=${https_proxy}
ENV no_proxy=${no_proxy}

# 配置APT代理（如果有代理）
RUN if [ -n "$HTTP_PROXY" ]; then \
        echo "Acquire::http::Proxy \"$HTTP_PROXY\";" > /etc/apt/apt.conf.d/01proxy; \
        echo "Acquire::https::Proxy \"$HTTPS_PROXY\";" >> /etc/apt/apt.conf.d/01proxy; \
    fi


# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Go语言版本配置 (x86版本)
ENV GO_VERSION=1.23.9
ENV GOROOT=/usr/local/go
ENV GOPATH=/go
ENV PATH=$GOROOT/bin:$GOPATH/bin:$PATH
ENV CGO_ENABLED=1
ENV GOOS=linux
ENV GOARCH=386

# 工作目录
WORKDIR /app

# 更新软件源为阿里云镜像（提高下载速度）
RUN cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial-backports main restricted universe multiverse" >> /etc/apt/sources.list

# 更新软件源并安装基础依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    curl \
    wget \
    git \
    vim \
    nano \
    unzip \
    tar \
    ca-certificates \
    # 编译工具链
    build-essential \
    gcc \
    g++ \
    make \
    libc6-dev \
    pkg-config \
    # 网络工具
    net-tools \
    iputils-ping \
    telnet \
    # 其他工具
    htop \
    tree \
    file \
    lsof \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Go语言 (x86版本)
RUN echo "下载Go语言 x86版本..." && \
    wget -q https://go.dev/dl/go1.23.9.linux-386.tar.gz -O /tmp/go.tar.gz && \
    echo "解压Go语言..." && \
    tar -C /usr/local -xzf /tmp/go.tar.gz && \
    rm /tmp/go.tar.gz && \
    echo "验证Go安装..." && \
    /usr/local/go/bin/go version

# 创建Go工作目录
RUN mkdir -p $GOPATH/src $GOPATH/bin $GOPATH/pkg && \
    chmod -R 755 $GOPATH

# 创建应用目录结构
RUN mkdir -p /app/src \
             /app/bin \
             /app/logs \
             /app/configs \
             /app/fwlib/

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户
RUN useradd -m -s /bin/bash fanuc && \
    usermod -aG sudo fanuc && \
    echo "fanuc:fanuc" | chpasswd && \
    chown -R fanuc:fanuc /app && \
    chown -R fanuc:fanuc $GOPATH

# 复制FOCAS库文件和头文件（如果存在）
COPY fwlib/ /app/fwlib/
RUN if [ -f /app/fwlib/libfocas32.so.1.0.5 ]; then \
        echo "安装FOCAS库文件..." && \
        cp /app/fwlib/libfocas32.so.1.0.5 /usr/local/lib/ && \
        ln -sf /usr/local/lib/libfocas32.so.1.0.5 /usr/local/lib/libfocas32.so && \
        chmod 755 /usr/local/lib/libfocas32.so.1.0.5; \
    fi

RUN if [ -f /app/fwlib/fwlib32.h ]; then \
        echo "安装FOCAS头文件..." && \
        cp /app/fwlib/fwlib32.h /usr/local/include/ && \
        chmod 644 /usr/local/include/fwlib32.h; \
    fi

# 配置动态链接器
RUN echo "/usr/local/lib" > /etc/ld.so.conf.d/focas.conf && \
    ldconfig

# 创建pkg-config配置
RUN mkdir -p /usr/local/lib/pkgconfig && \
    echo 'prefix=/usr/local' > /usr/local/lib/pkgconfig/focas.pc && \
    echo 'exec_prefix=${prefix}' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'libdir=${exec_prefix}/lib' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'includedir=${prefix}/include' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo '' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Name: FOCAS2/Ethernet' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Description: FANUC FOCAS2/Ethernet Library for Linux' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Version: 1.0.0' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Libs: -L${libdir} -lfocas32' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Cflags: -I${includedir}' >> /usr/local/lib/pkgconfig/focas.pc

# 设置环境变量
RUN echo '# FOCAS2/Ethernet Environment' >> /etc/profile.d/focas.sh && \
    echo 'export FOCAS_HOME=/usr/local' >> /etc/profile.d/focas.sh && \
    echo 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/lib' >> /etc/profile.d/focas.sh && \
    echo 'export PKG_CONFIG_PATH=$PKG_CONFIG_PATH:/usr/local/lib/pkgconfig' >> /etc/profile.d/focas.sh && \
    chmod 644 /etc/profile.d/focas.sh

# 复制项目文件
COPY . /app/src/
RUN chown -R fanuc:fanuc /app/src

# 执行FOCAS相关脚本（如果存在）
RUN if [ -f "/app/src/fwlib/build-deps.sh" ]; then \
        echo "执行 build-deps.sh..." && \
        chmod +x /app/src/fwlib/build-deps.sh && \
        cd /app/src && \
        ./fwlib/build-deps.sh; \
    else \
        echo "build-deps.sh 脚本不存在，跳过执行"; \
    fi

RUN if [ -f "/app/src/fwlib/install.sh" ]; then \
        echo "执行 install.sh..." && \
        chmod +x /app/src/fwlib/install.sh && \
        cd /app/src && \
        ./fwlib/install.sh; \
    else \
        echo "install.sh 脚本不存在，跳过执行"; \
    fi

# 切换到fanuc用户
USER fanuc

# 设置工作目录
WORKDIR /app/src

# 初始化Go模块（如果存在go.mod）
RUN if [ -f go.mod ]; then \
        echo "初始化Go模块..." && \
        go mod tidy; \
    fi

# 创建启动脚本
USER root
COPY <<EOF /app/entrypoint.sh
#!/bin/bash

# FANUC FOCAS2/Ethernet 开发环境启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=================================================================="
echo "           FANUC FOCAS2/Ethernet 开发环境 (Ubuntu 16.04 x86)"
echo "=================================================================="
echo

# 显示系统信息
echo -e "\${BLUE}系统信息:\${NC}"
echo "操作系统: \$(cat /etc/os-release | grep PRETTY_NAME | cut -d'\"' -f2)"
echo "架构: \$(uname -m)"
echo "内核: \$(uname -r)"
echo

# 显示Go信息
echo -e "\${BLUE}Go环境:\${NC}"
echo "Go版本: \$(go version)"
echo "GOROOT: \$GOROOT"
echo "GOPATH: \$GOPATH"
echo "GOARCH: \$GOARCH"
echo "CGO_ENABLED: \$CGO_ENABLED"
echo

# 检查FOCAS库
echo -e "\${BLUE}FOCAS库检查:\${NC}"
if [ -f "/usr/local/lib/libfocas32.so" ]; then
    echo -e "\${GREEN}✓\${NC} FOCAS库文件: \$(ls -la /usr/local/lib/libfocas32.so*)"
else
    echo -e "\${YELLOW}⚠\${NC} FOCAS库文件未找到"
fi

if [ -f "/usr/local/include/fwlib32.h" ]; then
    echo -e "\${GREEN}✓\${NC} FOCAS头文件: /usr/local/include/fwlib32.h"
else
    echo -e "\${YELLOW}⚠\${NC} FOCAS头文件未找到"
fi

# 检查动态链接
if ldconfig -p | grep -q "libfocas32"; then
    echo -e "\${GREEN}✓\${NC} 动态链接器已配置"
else
    echo -e "\${YELLOW}⚠\${NC} 动态链接器未配置"
fi

# 检查FOCAS脚本执行状态
echo
echo -e "\${BLUE}FOCAS脚本执行状态:\${NC}"
if [ -f "/app/src/fwlib/scripts/build-deps.sh" ]; then
    echo -e "\${GREEN}✓\${NC} build-deps.sh 脚本已找到并执行"
else
    echo -e "\${YELLOW}⚠\${NC} build-deps.sh 脚本未找到"
fi

if [ -f "/app/src/fwlib/scripts/install.sh" ]; then
    echo -e "\${GREEN}✓\${NC} install.sh 脚本已找到并执行"
else
    echo -e "\${YELLOW}⚠\${NC} install.sh 脚本未找到"
fi

echo
echo -e "\${BLUE}工作目录:\${NC} \$(pwd)"
echo -e "\${BLUE}用户:\${NC} \$(whoami)"

echo
echo "=================================================================="
echo "可用命令:"
echo "  go version                    # 查看Go版本"
echo "  go build                      # 编译项目"
echo "  go run main.go               # 运行项目"
echo "  ldconfig -p | grep focas     # 检查FOCAS库"
echo "  pkg-config --cflags focas    # 获取编译标志"
echo "  pkg-config --libs focas      # 获取链接标志"
echo "=================================================================="
echo

# 如果有参数，执行参数命令
if [ \$# -gt 0 ]; then
    echo "执行命令: \$@"
    exec "\$@"
else
    # 默认保持容器运行
    echo "容器已启动，使用 'docker exec -it fanuc-focas-dev-x86 /bin/bash' 进入容器"
    exec tail -f /dev/null
fi
EOF

RUN chmod +x /app/entrypoint.sh

# 暴露端口
#EXPOSE 9005 8081 8082 8083
EXPOSE 8081

# 设置启动脚本
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令 - 保持容器运行
CMD ["tail", "-f", "/dev/null"]

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD go version || exit 1

# 标签信息
LABEL maintainer="FANUC Development Team"
LABEL description="FANUC FOCAS2/Ethernet Development Environment - Ubuntu 16.04 x86"
LABEL version="1.0.0"
LABEL architecture="i386"
LABEL go.version="1.23.9"
LABEL ubuntu.version="16.04"
