#!/bin/bash

# Docker构建脚本 - 支持失败重试机制
# 用途：构建FANUC v2 Docker镜像，包含运行时FWLIB库
# 重试次数：最多3次
# 重试间隔：每次失败后等待5秒

# bash ./build-x86.sh deploy
# bash ./build-optimized.sh

# 设置重试参数
MAX_RETRIES=99
RETRY_DELAY=5
BUILD_SCRIPT="./build-with-runtime-fwlib.sh"

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}开始构建FANUC v2 Docker镜像...${NC}"
echo "构建脚本: $BUILD_SCRIPT"
echo "最大重试次数: $MAX_RETRIES"
echo "重试间隔: ${RETRY_DELAY}秒"
echo "----------------------------------------"

# 重试函数
retry_build() {
    local attempt=1

    while [ $attempt -le $MAX_RETRIES ]; do
        echo -e "${YELLOW}第 $attempt 次尝试构建...${NC}"

        # 执行构建脚本
        if bash "$BUILD_SCRIPT"; then
            echo -e "${GREEN}✅ 构建成功！${NC}"
            echo "总尝试次数: $attempt"
            return 0
        else
            echo -e "${RED}❌ 第 $attempt 次构建失败${NC}"

            if [ $attempt -lt $MAX_RETRIES ]; then
                echo -e "${YELLOW}等待 ${RETRY_DELAY} 秒后重试...${NC}"
                sleep $RETRY_DELAY
                attempt=$((attempt + 1))
            else
                echo -e "${RED}❌ 已达到最大重试次数 ($MAX_RETRIES)，构建失败${NC}"
                return 1
            fi
        fi
    done
}

# 执行重试构建
retry_build

# 检查最终结果
if [ $? -eq 0 ]; then
    echo -e "${GREEN}🎉 Docker镜像构建完成！${NC}"
    exit 0
else
    echo -e "${RED}💥 Docker镜像构建失败，请检查错误信息${NC}"
    exit 1
fi