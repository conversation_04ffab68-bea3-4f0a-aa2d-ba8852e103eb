# FOCAS连接卡死问题解决方案

## 🎯 问题分析

根据日志分析，发现FOCAS库的 `cnc_allclibhndl3` 函数调用会卡死，导致采集线程阻塞：

### 问题现象
- **时间点**: 08:44:15 和 08:44:16
- **现象**: 调用 `cnc_allclibhndl3` 后没有返回结果
- **影响**: 采集线程卡死，健康检查失败，系统自动重启

### 根本原因
1. **FOCAS库内部超时失效**: 虽然设置了超时，但库内部可能没有正确处理
2. **资源管理问题**: FOCAS库内部资源可能出现死锁或泄漏
3. **网络连接问题**: 底层网络连接可能出现异常

## ✅ 解决方案实现

### 1. **真正的超时控制**

#### 增强的连接函数
```go
// 使用context + goroutine实现真正的超时控制
ctx, cancel := context.WithTimeout(context.Background(), dc.focasConnectTimeout)
defer cancel()

resultCh := make(chan connectionResult, 1)
go func() {
    // 在goroutine中调用FOCAS函数
    ret := C.cnc_allclibhndl3(ip, port, timeout, &handle)
    resultCh <- connectionResult{ret: ret, handle: handle}
}()

// 等待结果或超时
select {
case result := <-resultCh:
    // 正常返回
case <-ctx.Done():
    // 超时处理
    return fmt.Errorf("FOCAS连接超时")
}
```

#### 关键特性
- **真正超时**: 不依赖FOCAS库内部超时机制
- **Panic保护**: 捕获FOCAS库可能的panic
- **详细日志**: 记录每个步骤的耗时和状态

### 2. **连接池管理**

#### 信号量控制
```go
// 限制同时连接数，避免资源耗尽
connectionSemaphore: make(chan struct{}, 2) // 最多同时2个连接

// 获取连接许可
select {
case dc.connectionSemaphore <- struct{}{}:
    // 获取成功
    defer func() { <-dc.connectionSemaphore }()
case <-time.After(2 * time.Second):
    return fmt.Errorf("获取连接池信号量超时")
}
```

#### 优势
- **资源控制**: 防止同时创建过多连接
- **系统保护**: 避免FOCAS库资源耗尽
- **快速失败**: 连接池满时快速返回错误

### 3. **FOCAS资源清理**

#### 资源清理函数
```go
func (dc *DeviceCollector) cleanupFOCASResources() {
    // 清理FOCAS进程资源
    C.cnc_exitprocess()
    
    // 重新初始化FOCAS进程
    C.cnc_startupprocess()
    
    // 重新初始化FOCAS管理器
    focasManager.InitializeFOCAS()
}
```

#### 应用场景
- **健康检查失败**: 在重启采集循环前清理资源
- **连接超时**: 在检测到连接卡死时清理资源
- **系统恢复**: 在系统异常后恢复正常状态

### 4. **增强的健康检查**

#### 更快的恢复机制
```go
maxUnhealthyCount := 2  // 连续2次不健康才重启（原来是3次）
```

#### 集成资源清理
```go
// 在重启前清理FOCAS资源
logger.Infof("🧹 在重启前清理FOCAS资源")
dc.cleanupFOCASResources()

// 等待清理完成后重启
time.Sleep(3 * time.Second)
dc.StartCollection(ctx)
```

## 🔧 技术实现细节

### 新增字段
```go
type DeviceCollector struct {
    // FOCAS连接超时控制
    focasConnectTimeout time.Duration        // 默认5秒
    
    // 连接池信号量
    connectionSemaphore chan struct{}        // 最多2个并发连接
}
```

### 核心函数

#### 1. `establishConnection()` - 增强版连接
- ✅ 真正的超时控制（5秒）
- ✅ 连接池管理（最多2个并发）
- ✅ Panic保护和恢复
- ✅ 详细的性能日志

#### 2. `releaseConnection()` - 增强版释放
- ✅ 超时控制（3秒）
- ✅ Panic保护
- ✅ 非阻塞释放

#### 3. `cleanupFOCASResources()` - 资源清理
- ✅ 清理FOCAS进程资源
- ✅ 重新初始化FOCAS库
- ✅ 超时保护（5秒）

## 📊 性能优化

### 超时时间设置
- **连接超时**: 5秒（原来依赖FOCAS库内部超时）
- **释放超时**: 3秒（防止释放操作卡死）
- **清理超时**: 5秒（资源清理操作）
- **信号量超时**: 2秒（连接池获取）

### 并发控制
- **最大并发连接**: 2个（防止资源耗尽）
- **健康检查阈值**: 2次失败（加快恢复速度）
- **重启间隔**: 30秒（防止频繁重启）

## 🔍 日志监控

### 关键日志模式

#### 连接成功
```
[DEVICE001:192.168.1.100] 🎫 获取连接池信号量成功
[DEVICE001:192.168.1.100] 📞 调用cnc_allclibhndl3，配置超时: 10秒，实际超时: 5s
[DEVICE001:192.168.1.100] ✅ FOCAS连接建立成功，句柄: 1，总耗时: 150ms
```

#### 连接超时
```
[DEVICE001:192.168.1.100] ⏰ FOCAS连接超时，超时时间: 5s，总耗时: 5.001s
```

#### 资源清理
```
[DEVICE001:192.168.1.100] 🧹 开始清理FOCAS资源
[DEVICE001:192.168.1.100] 📞 调用cnc_exitprocess清理资源
[DEVICE001:192.168.1.100] 📞 调用cnc_startupprocess重新初始化
[DEVICE001:192.168.1.100] ✅ FOCAS资源清理完成
```

#### 健康检查和恢复
```
[DEVICE001:192.168.1.100] 采集循环健康检查失败 (2/2)
[DEVICE001:192.168.1.100] 🧹 在重启前清理FOCAS资源
[DEVICE001:192.168.1.100] 采集循环已重新启动
```

## 🧪 测试验证

### 测试场景

#### 1. 正常连接测试
- 验证连接池工作正常
- 验证超时控制有效
- 验证性能日志完整

#### 2. 连接超时测试
- 模拟网络延迟
- 验证5秒超时生效
- 验证系统快速恢复

#### 3. 资源清理测试
- 模拟FOCAS库卡死
- 验证资源清理功能
- 验证系统自动恢复

#### 4. 并发连接测试
- 多设备同时连接
- 验证连接池限制
- 验证系统稳定性

### 监控命令
```bash
# 监控连接状态
tail -f logs/fanuc_v2.log | grep -E '🎫|📞|✅|⏰|🧹'

# 监控健康检查
tail -f logs/fanuc_v2.log | grep -E '健康检查|重新启动|清理资源'

# 监控超时问题
tail -f logs/fanuc_v2.log | grep -E '超时|timeout|卡死'
```

## 🎯 预期效果

### 问题解决
- ✅ **防止连接卡死**: 真正的超时控制，5秒必定返回
- ✅ **快速恢复**: 2次健康检查失败即重启，恢复时间缩短
- ✅ **资源管理**: 连接池控制并发，防止资源耗尽
- ✅ **自动清理**: 检测到问题时自动清理FOCAS资源

### 性能提升
- ✅ **响应速度**: 连接超时从可能的无限等待降到5秒
- ✅ **恢复速度**: 健康检查阈值从3次降到2次
- ✅ **系统稳定性**: 连接池和资源清理提高整体稳定性
- ✅ **监控能力**: 详细日志便于问题排查

### 兼容性
- ✅ **向后兼容**: 保持原有API不变
- ✅ **配置灵活**: 超时时间和连接数可调整
- ✅ **渐进式**: 可以逐步启用新功能

## 💡 使用建议

### 1. 监控重点
- 关注连接超时日志
- 监控资源清理频率
- 观察健康检查恢复情况

### 2. 参数调优
- 根据网络环境调整超时时间
- 根据设备数量调整连接池大小
- 根据稳定性要求调整健康检查阈值

### 3. 故障排查
- 如果频繁超时，检查网络连接
- 如果频繁清理资源，检查FOCAS库版本
- 如果系统不稳定，适当增加超时时间

## 🎉 总结

通过实现真正的超时控制、连接池管理、资源清理和增强的健康检查，成功解决了FOCAS连接卡死问题。系统现在具备了更强的稳定性和自恢复能力，能够有效应对各种异常情况。
