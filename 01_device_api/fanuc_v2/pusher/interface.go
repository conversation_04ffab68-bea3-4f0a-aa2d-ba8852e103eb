/**
 * FANUC设备数据推送器模块
 *
 * 功能概述：
 * 本模块提供FANUC设备采集器的数据推送功能，定义了统一的数据推送接口，
 * 支持多种推送目标（RESTful API、消息队列、数据库等），实现可插拔的
 * 推送器架构，确保设备数据能够可靠地推送到目标系统
 *
 * 主要功能：
 * - 推送接口：定义统一的数据推送接口，支持多种推送实现
 * - 推送管理：提供推送器的创建、配置、管理和监控功能
 * - 批量推送：支持单条和批量数据推送，提高推送效率
 * - 健康检查：实时监控推送器的健康状态和连接状态
 * - 性能监控：详细的推送性能指标和统计信息
 * - 配置管理：支持推送器配置的动态更新和热重载
 * - 错误处理：完善的错误处理和重试机制
 *
 * 架构设计：
 * - 接口抽象：DataPusher接口定义统一的推送规范
 * - 工厂模式：PusherFactory负责创建不同类型的推送器
 * - 管理器模式：PusherManager统一管理推送器的生命周期
 * - 策略模式：支持不同的推送策略和目标系统
 * - 适配器模式：适配不同的推送协议和数据格式
 *
 * 技术特性：
 * - 可插拔架构：支持动态加载不同类型的推送器
 * - 并发安全：支持并发环境下的安全数据推送
 * - 超时控制：支持推送操作的超时控制和取消
 * - 批量优化：智能的批量推送和分批处理
 * - 性能监控：实时的推送性能监控和指标统计
 * - 配置热更新：支持运行时的配置动态更新
 *
 * 推送器类型：
 * - RESTful推送器：通过HTTP/HTTPS推送到REST API
 * - 消息队列推送器：推送到Kafka、RabbitMQ等消息队列
 * - 数据库推送器：直接写入数据库（MySQL、PostgreSQL等）
 * - 文件推送器：写入本地或远程文件系统
 * - 自定义推送器：支持用户自定义的推送实现
 *
 * 数据流程：
 * 1. 数据接收：接收来自设备采集器的DeviceData
 * 2. 数据转换：将DeviceData转换为PushData格式
 * 3. 批量处理：根据配置进行批量聚合或单条处理
 * 4. 推送执行：调用具体推送器执行数据推送
 * 5. 结果处理：处理推送结果，更新指标和状态
 * 6. 错误处理：处理推送失败，执行重试或缓存
 *
 * 可靠性保证：
 * - 重试机制：推送失败时的自动重试
 * - 超时保护：防止推送操作无限等待
 * - 健康检查：实时监控推送器健康状态
 * - 错误隔离：单个推送失败不影响其他推送
 * - 配置验证：推送器配置的有效性验证
 *
 * 业务价值：
 * - 数据集成：将设备数据集成到企业信息系统
 * - 实时性：确保设备数据的实时推送和处理
 * - 可扩展性：支持多种推送目标和协议
 * - 可靠性：确保数据推送的可靠性和完整性
 * - 监控性：提供完整的推送监控和告警
 *
 * @package pusher
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package pusher

/**
 * 标准库导入
 *
 * - context: 上下文控制，用于推送操作的超时和取消
 * - fmt: 格式化输入输出，用于错误信息格式化
 * - time: 时间处理，用于超时控制和性能统计
 *
 * 项目内部包：
 * - fanuc_v2/models: 数据模型，设备数据和推送数据结构
 */
import (
	"context" /** 上下文控制，用于推送操作的生命周期管理 */
	"fmt"     /** 格式化输入输出，用于错误信息处理 */
	"time"    /** 时间处理，用于超时控制和性能统计 */

	"fanuc_v2/models" /** 数据模型，设备数据和推送数据结构 */
)

/**
 * 数据推送器接口
 *
 * 功能：定义统一的数据推送接口，支持多种推送实现
 *
 * 设计原则：
 * - 接口隔离：每个方法职责单一，便于实现和测试
 * - 依赖倒置：依赖抽象接口而非具体实现
 * - 开闭原则：对扩展开放，对修改关闭
 * - 里氏替换：所有实现都可以互相替换
 *
 * 实现类型：
 * - RESTfulPusher：HTTP/HTTPS REST API推送
 * - KafkaPusher：Kafka消息队列推送
 * - DatabasePusher：数据库直接写入
 * - FilePusher：文件系统写入
 * - CustomPusher：用户自定义推送器
 *
 * @interface DataPusher
 */
type DataPusher interface {
	/**
	 * 推送单条数据
	 *
	 * 功能：将单条设备数据推送到目标系统
	 *
	 * @param {context.Context} ctx - 上下文，用于超时控制和取消
	 * @param {*models.PushData} data - 要推送的数据
	 * @returns {error} 推送失败时返回错误信息
	 */
	Push(ctx context.Context, data *models.PushData) error

	/**
	 * 批量推送数据
	 *
	 * 功能：批量推送多条设备数据，提高推送效率
	 *
	 * @param {context.Context} ctx - 上下文，用于超时控制和取消
	 * @param {[]*models.PushData} data - 要推送的数据列表
	 * @returns {error} 推送失败时返回错误信息
	 */
	PushBatch(ctx context.Context, data []*models.PushData) error

	/**
	 * 检查推送器健康状态
	 *
	 * 功能：检查推送器是否正常工作，包括网络连接、服务可用性等
	 *
	 * @param {context.Context} ctx - 上下文，用于超时控制
	 * @returns {bool} true表示健康，false表示不健康
	 */
	IsHealthy(ctx context.Context) bool

	/**
	 * 获取推送器配置
	 *
	 * 功能：返回当前推送器的配置信息
	 *
	 * @returns {map[string]interface{}} 配置信息映射表
	 */
	GetConfig() map[string]interface{}

	/**
	 * 更新推送器配置
	 *
	 * 功能：动态更新推送器配置，支持热重载
	 *
	 * @param {map[string]interface{}} config - 新的配置信息
	 * @returns {error} 更新失败时返回错误信息
	 */
	UpdateConfig(config map[string]interface{}) error

	/**
	 * 关闭推送器
	 *
	 * 功能：关闭推送器，释放资源，执行清理操作
	 *
	 * @returns {error} 关闭失败时返回错误信息
	 */
	Close() error

	/**
	 * 获取推送器指标
	 *
	 * 功能：返回推送器的性能指标和统计信息
	 *
	 * @returns {PusherMetrics} 推送器性能指标
	 */
	GetMetrics() PusherMetrics
}

// PusherMetrics 推送器指标
type PusherMetrics struct {
	TotalPushes      int64         `json:"total_pushes"`
	SuccessfulPushes int64         `json:"successful_pushes"`
	FailedPushes     int64         `json:"failed_pushes"`
	AvgPushTime      time.Duration `json:"avg_push_time"`
	LastPushTime     time.Time     `json:"last_push_time"`
	LastError        string        `json:"last_error,omitempty"`
	IsHealthy        bool          `json:"is_healthy"`
}

// PusherFactory 推送器工厂接口
type PusherFactory interface {
	CreatePusher(pusherType string, config map[string]interface{}) (DataPusher, error)
	GetSupportedTypes() []string
}

// PusherConfig 推送器配置
type PusherConfig struct {
	Type      string                 `json:"type"`
	Config    map[string]interface{} `json:"config"`
	Enabled   bool                   `json:"enabled"`
	BatchSize int                    `json:"batch_size"`
	Timeout   time.Duration          `json:"timeout"`
}

// BasePusher 基础推送器实现
type BasePusher struct {
	config  map[string]interface{}
	metrics PusherMetrics
}

// GetConfig 获取配置
func (b *BasePusher) GetConfig() map[string]interface{} {
	return b.config
}

// GetMetrics 获取指标
func (b *BasePusher) GetMetrics() PusherMetrics {
	return b.metrics
}

// UpdateMetrics 更新指标
func (b *BasePusher) UpdateMetrics(success bool, duration time.Duration, err error) {
	b.metrics.TotalPushes++
	b.metrics.LastPushTime = time.Now()

	if success {
		b.metrics.SuccessfulPushes++
		b.metrics.IsHealthy = true
		b.metrics.LastError = ""
	} else {
		b.metrics.FailedPushes++
		if err != nil {
			b.metrics.LastError = err.Error()
		}
		// 如果连续失败超过阈值，标记为不健康
		if b.metrics.FailedPushes > b.metrics.SuccessfulPushes {
			b.metrics.IsHealthy = false
		}
	}

	// 计算平均推送时间
	if b.metrics.TotalPushes > 0 {
		totalTime := time.Duration(b.metrics.TotalPushes) * b.metrics.AvgPushTime
		b.metrics.AvgPushTime = (totalTime + duration) / time.Duration(b.metrics.TotalPushes)
	} else {
		b.metrics.AvgPushTime = duration
	}
}

// PusherManager 推送器管理器
type PusherManager struct {
	pusher  DataPusher
	config  PusherConfig
	factory PusherFactory
}

// NewPusherManager 创建推送器管理器
func NewPusherManager(factory PusherFactory) *PusherManager {
	return &PusherManager{
		factory: factory,
	}
}

// Initialize 初始化推送器
func (pm *PusherManager) Initialize(config PusherConfig) error {
	pusher, err := pm.factory.CreatePusher(config.Type, config.Config)
	if err != nil {
		return err
	}

	pm.pusher = pusher
	pm.config = config
	return nil
}

// Push 推送数据 - 接受DeviceData并转换为PushData
func (pm *PusherManager) Push(ctx context.Context, data *models.DeviceData) error {
	if pm.pusher == nil {
		return ErrPusherNotInitialized
	}

	if !pm.config.Enabled {
		return ErrPusherDisabled
	}

	// 设置超时
	if pm.config.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, pm.config.Timeout)
		defer cancel()
	}

	// 转换为推送格式
	pushData := data.ToPushData()
	return pm.pusher.Push(ctx, pushData)
}

// PushBatch 批量推送数据 - 接受DeviceData并转换为PushData
func (pm *PusherManager) PushBatch(ctx context.Context, data []*models.DeviceData) error {
	if pm.pusher == nil {
		return ErrPusherNotInitialized
	}

	if !pm.config.Enabled {
		return ErrPusherDisabled
	}

	// 设置超时
	if pm.config.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, pm.config.Timeout)
		defer cancel()
	}

	// 转换为推送格式
	pushDataList := make([]*models.PushData, len(data))
	for i, deviceData := range data {
		pushDataList[i] = deviceData.ToPushData()
	}

	// 如果数据量超过批次大小，分批推送
	if len(pushDataList) <= pm.config.BatchSize {
		return pm.pusher.PushBatch(ctx, pushDataList)
	}

	// 分批推送
	for i := 0; i < len(pushDataList); i += pm.config.BatchSize {
		end := i + pm.config.BatchSize
		if end > len(pushDataList) {
			end = len(pushDataList)
		}

		batch := pushDataList[i:end]
		if err := pm.pusher.PushBatch(ctx, batch); err != nil {
			return err
		}
	}

	return nil
}

// IsHealthy 检查健康状态
func (pm *PusherManager) IsHealthy(ctx context.Context) bool {
	if pm.pusher == nil {
		return false
	}
	return pm.pusher.IsHealthy(ctx)
}

// UpdateConfig 更新配置
func (pm *PusherManager) UpdateConfig(config PusherConfig) error {
	// 如果推送器类型改变，需要重新创建
	if pm.config.Type != config.Type {
		if pm.pusher != nil {
			pm.pusher.Close()
		}

		pusher, err := pm.factory.CreatePusher(config.Type, config.Config)
		if err != nil {
			return err
		}
		pm.pusher = pusher
	} else if pm.pusher != nil {
		// 只更新配置
		if err := pm.pusher.UpdateConfig(config.Config); err != nil {
			return err
		}
	}

	pm.config = config
	return nil
}

// GetMetrics 获取指标
func (pm *PusherManager) GetMetrics() PusherMetrics {
	if pm.pusher == nil {
		return PusherMetrics{}
	}
	return pm.pusher.GetMetrics()
}

// Close 关闭推送器
func (pm *PusherManager) Close() error {
	if pm.pusher != nil {
		return pm.pusher.Close()
	}
	return nil
}

// 错误定义
var (
	ErrPusherNotInitialized = fmt.Errorf("推送器未初始化")
	ErrPusherDisabled       = fmt.Errorf("推送器已禁用")
	ErrUnsupportedType      = fmt.Errorf("不支持的推送器类型")
	ErrInvalidConfig        = fmt.Errorf("无效的推送器配置")
)
