package pusher

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"fanuc_v2/models"
)

// RestfulPusher RESTful API推送器
type RestfulPusher struct {
	BasePusher
	client   *http.Client
	url      string
	headers  map[string]string
	method   string
	username string
	password string
}

// RestfulConfig RESTful推送器配置
type RestfulConfig struct {
	URL      string            `json:"url"`
	Method   string            `json:"method"`
	Headers  map[string]string `json:"headers"`
	Username string            `json:"username"`
	Password string            `json:"password"`
	Timeout  int               `json:"timeout"` // 秒
}

// NewRestfulPusher 创建RESTful推送器
func NewRestfulPusher(config map[string]interface{}) (*RestfulPusher, error) {
	pusher := &RestfulPusher{
		BasePusher: BasePusher{
			config: config,
			metrics: PusherMetrics{
				IsHealthy: true,
			},
		},
		method:  "POST",
		headers: make(map[string]string),
	}

	if err := pusher.parseConfig(config); err != nil {
		return nil, err
	}

	// 创建HTTP客户端
	timeout := 30 * time.Second
	if timeoutVal, ok := config["timeout"]; ok {
		if timeoutInt, ok := timeoutVal.(int); ok {
			timeout = time.Duration(timeoutInt) * time.Second
		}
	}

	pusher.client = &http.Client{
		Timeout: timeout,
	}

	return pusher, nil
}

// parseConfig 解析配置
func (r *RestfulPusher) parseConfig(config map[string]interface{}) error {
	// URL是必需的
	if url, ok := config["url"].(string); ok {
		r.url = url
	} else {
		return fmt.Errorf("URL配置缺失或无效")
	}

	// HTTP方法
	if method, ok := config["method"].(string); ok {
		r.method = method
	}

	// 请求头
	if headers, ok := config["headers"].(map[string]interface{}); ok {
		for k, v := range headers {
			if strVal, ok := v.(string); ok {
				r.headers[k] = strVal
			}
		}
	}

	// 认证信息
	if username, ok := config["username"].(string); ok {
		r.username = username
	}
	if password, ok := config["password"].(string); ok {
		r.password = password
	}

	// 设置默认Content-Type
	if _, exists := r.headers["Content-Type"]; !exists {
		r.headers["Content-Type"] = "application/json"
	}

	return nil
}

// Push 推送单条数据
func (r *RestfulPusher) Push(ctx context.Context, data *models.PushData) error {
	start := time.Now()
	err := r.pushData(ctx, data)
	duration := time.Since(start)

	r.UpdateMetrics(err == nil, duration, err)
	return err
}

// PushBatch 批量推送数据
func (r *RestfulPusher) PushBatch(ctx context.Context, data []*models.PushData) error {
	start := time.Now()
	err := r.pushBatchData(ctx, data)
	duration := time.Since(start)

	r.UpdateMetrics(err == nil, duration, err)
	return err
}

// pushData 推送单条数据的内部实现
func (r *RestfulPusher) pushData(ctx context.Context, data *models.PushData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	req, err := http.NewRequestWithContext(ctx, r.method, r.url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	for k, v := range r.headers {
		req.Header.Set(k, v)
	}

	// 设置认证
	if r.username != "" && r.password != "" {
		req.SetBasicAuth(r.username, r.password)
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return nil
}

// pushBatchData 批量推送数据的内部实现
func (r *RestfulPusher) pushBatchData(ctx context.Context, data []*models.PushData) error {
	// 包装为批量数据结构
	batchData := map[string]interface{}{
		"data":      data,
		"count":     len(data),
		"timestamp": time.Now(),
	}

	jsonData, err := json.Marshal(batchData)
	if err != nil {
		return fmt.Errorf("序列化批量数据失败: %v", err)
	}

	req, err := http.NewRequestWithContext(ctx, r.method, r.url+"/batch", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建批量请求失败: %v", err)
	}

	// 设置请求头
	for k, v := range r.headers {
		req.Header.Set(k, v)
	}

	// 设置认证
	if r.username != "" && r.password != "" {
		req.SetBasicAuth(r.username, r.password)
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送批量请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取批量响应失败: %v", err)
	}

	// 检查状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("批量请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return nil
}

// IsHealthy 检查健康状态
func (r *RestfulPusher) IsHealthy(ctx context.Context) bool {
	// 发送健康检查请求
	healthURL := r.url + "/health"
	req, err := http.NewRequestWithContext(ctx, "GET", healthURL, nil)
	if err != nil {
		return false
	}

	// 设置认证
	if r.username != "" && r.password != "" {
		req.SetBasicAuth(r.username, r.password)
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode >= 200 && resp.StatusCode < 300
}

// UpdateConfig 更新配置
func (r *RestfulPusher) UpdateConfig(config map[string]interface{}) error {
	if err := r.parseConfig(config); err != nil {
		return err
	}

	r.config = config

	// 更新HTTP客户端超时
	if timeoutVal, ok := config["timeout"]; ok {
		if timeoutInt, ok := timeoutVal.(int); ok {
			timeout := time.Duration(timeoutInt) * time.Second
			r.client.Timeout = timeout
		}
	}

	return nil
}

// Close 关闭推送器
func (r *RestfulPusher) Close() error {
	// HTTP客户端不需要显式关闭
	return nil
}

// GetStatus 获取推送器状态
func (r *RestfulPusher) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"type":    "restful",
		"url":     r.url,
		"method":  r.method,
		"healthy": r.metrics.IsHealthy,
		"metrics": r.metrics,
	}
}

// TestConnection 测试连接
func (r *RestfulPusher) TestConnection(ctx context.Context) error {
	// 创建测试数据 - 使用新的数据结构
	testData := &models.PushData{
		DeviceID:  "test",
		DataType:  "fanuc_cnc",
		Timestamp: time.Now(),
		RawData: models.RawDataStruct{
			DataID:             "test_data_id",
			Connected:          true,
			Status:             "idle",
			StatusData:         map[string]interface{}{"test": true, "message": "这是一条测试数据"},
			Quantity:           0,
			CurrentProgram:     "TEST_PROG",
			MainProgram:        "TEST_MAIN",
			ActualFeedrate:     1000,
			ActualSpindleSpeed: 2000,
		},
		Metadata: models.MetadataStruct{
			Location:        "test_location",
			Version:         "2.0.0",
			DeviceType:      "fanuc_cnc",
			Brand:           "FANUC",
			Model:           "Test",
			CollectInterval: 3000,
		},
	}

	// 尝试推送测试数据
	return r.pushData(ctx, testData)
}
