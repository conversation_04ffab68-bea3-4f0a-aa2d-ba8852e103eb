package pusher

import (
	"context"
	"fmt"
	"strings"

	"fanuc_v2/models"
)

// DefaultPusherFactory 默认推送器工厂
type DefaultPusherFactory struct {
	creators map[string]PusherCreator
}

// PusherCreator 推送器创建函数
type PusherCreator func(config map[string]interface{}) (DataPusher, error)

// NewDefaultPusherFactory 创建默认推送器工厂
func NewDefaultPusherFactory() *DefaultPusherFactory {
	factory := &DefaultPusherFactory{
		creators: make(map[string]PusherCreator),
	}

	// 注册内置推送器
	factory.RegisterPusher("restful", func(config map[string]interface{}) (DataPusher, error) {
		return NewRestfulPusher(config)
	})

	factory.RegisterPusher("mqtt", func(config map[string]interface{}) (DataPusher, error) {
		return NewMQTTPusher(config)
	})

	factory.RegisterPusher("nats", func(config map[string]interface{}) (DataPusher, error) {
		return NewNATSPusher(config)
	})

	return factory
}

// RegisterPusher 注册推送器
func (f *DefaultPusherFactory) RegisterPusher(pusherType string, creator PusherCreator) {
	f.creators[strings.ToLower(pusherType)] = creator
}

// CreatePusher 创建推送器
func (f *DefaultPusherFactory) CreatePusher(pusherType string, config map[string]interface{}) (DataPusher, error) {
	creator, exists := f.creators[strings.ToLower(pusherType)]
	if !exists {
		return nil, fmt.Errorf("不支持的推送器类型: %s", pusherType)
	}

	return creator(config)
}

// GetSupportedTypes 获取支持的推送器类型
func (f *DefaultPusherFactory) GetSupportedTypes() []string {
	types := make([]string, 0, len(f.creators))
	for pusherType := range f.creators {
		types = append(types, pusherType)
	}
	return types
}

// MQTT推送器 (占位符实现)
func NewMQTTPusher(config map[string]interface{}) (DataPusher, error) {
	return &MQTTPusher{
		BasePusher: BasePusher{
			config: config,
			metrics: PusherMetrics{
				IsHealthy: true,
			},
		},
	}, nil
}

// MQTTPusher MQTT推送器
type MQTTPusher struct {
	BasePusher
	broker   string
	topic    string
	clientID string
	username string
	password string
}

// Push 推送单条数据
func (m *MQTTPusher) Push(ctx context.Context, data *models.PushData) error {
	// TODO: 实现MQTT推送逻辑
	return fmt.Errorf("MQTT推送器尚未实现")
}

// PushBatch 批量推送数据
func (m *MQTTPusher) PushBatch(ctx context.Context, data []*models.PushData) error {
	// TODO: 实现MQTT批量推送逻辑
	return fmt.Errorf("MQTT批量推送器尚未实现")
}

// IsHealthy 检查健康状态
func (m *MQTTPusher) IsHealthy(ctx context.Context) bool {
	// TODO: 实现MQTT健康检查
	return false
}

// UpdateConfig 更新配置
func (m *MQTTPusher) UpdateConfig(config map[string]interface{}) error {
	m.config = config
	return nil
}

// Close 关闭推送器
func (m *MQTTPusher) Close() error {
	return nil
}

// NATS推送器 (占位符实现)
func NewNATSPusher(config map[string]interface{}) (DataPusher, error) {
	return &NATSPusher{
		BasePusher: BasePusher{
			config: config,
			metrics: PusherMetrics{
				IsHealthy: true,
			},
		},
	}, nil
}

// NATSPusher NATS推送器
type NATSPusher struct {
	BasePusher
	url      string
	subject  string
	username string
	password string
}

// Push 推送单条数据
func (n *NATSPusher) Push(ctx context.Context, data *models.PushData) error {
	// TODO: 实现NATS推送逻辑
	return fmt.Errorf("NATS推送器尚未实现")
}

// PushBatch 批量推送数据
func (n *NATSPusher) PushBatch(ctx context.Context, data []*models.PushData) error {
	// TODO: 实现NATS批量推送逻辑
	return fmt.Errorf("NATS批量推送器尚未实现")
}

// IsHealthy 检查健康状态
func (n *NATSPusher) IsHealthy(ctx context.Context) bool {
	// TODO: 实现NATS健康检查
	return false
}

// UpdateConfig 更新配置
func (n *NATSPusher) UpdateConfig(config map[string]interface{}) error {
	n.config = config
	return nil
}

// Close 关闭推送器
func (n *NATSPusher) Close() error {
	return nil
}

// 全局推送器工厂实例
var GlobalPusherFactory = NewDefaultPusherFactory()

// CreatePusherFromConfig 从配置创建推送器
func CreatePusherFromConfig(config PusherConfig) (DataPusher, error) {
	return GlobalPusherFactory.CreatePusher(config.Type, config.Config)
}

// GetAvailablePusherTypes 获取可用的推送器类型
func GetAvailablePusherTypes() []string {
	return GlobalPusherFactory.GetSupportedTypes()
}

// ValidatePusherConfig 验证推送器配置
func ValidatePusherConfig(pusherType string, config map[string]interface{}) error {
	switch strings.ToLower(pusherType) {
	case "restful":
		return validateRestfulConfig(config)
	case "mqtt":
		return validateMQTTConfig(config)
	case "nats":
		return validateNATSConfig(config)
	default:
		return fmt.Errorf("不支持的推送器类型: %s", pusherType)
	}
}

// validateRestfulConfig 验证RESTful配置
func validateRestfulConfig(config map[string]interface{}) error {
	if _, ok := config["url"]; !ok {
		return fmt.Errorf("RESTful推送器缺少URL配置")
	}
	return nil
}

// validateMQTTConfig 验证MQTT配置
func validateMQTTConfig(config map[string]interface{}) error {
	if _, ok := config["broker"]; !ok {
		return fmt.Errorf("MQTT推送器缺少broker配置")
	}
	if _, ok := config["topic"]; !ok {
		return fmt.Errorf("MQTT推送器缺少topic配置")
	}
	return nil
}

// validateNATSConfig 验证NATS配置
func validateNATSConfig(config map[string]interface{}) error {
	if _, ok := config["url"]; !ok {
		return fmt.Errorf("NATS推送器缺少URL配置")
	}
	if _, ok := config["subject"]; !ok {
		return fmt.Errorf("NATS推送器缺少subject配置")
	}
	return nil
}
