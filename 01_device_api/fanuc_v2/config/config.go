/**
 * FANUC设备采集器配置管理模块
 *
 * 功能概述：
 * 本模块提供FANUC设备采集器的完整配置管理功能，包括系统配置、设备配置、
 * 数据推送配置、缓存配置、日志配置和监控配置的统一管理
 *
 * 主要功能：
 * - 配置加载：从JSON文件加载系统配置
 * - 配置验证：验证配置参数的合理性和完整性
 * - 默认值设置：为未配置的参数设置合理的默认值
 * - 动态更新：支持运行时的配置动态更新
 * - 并发安全：使用读写锁保护配置的并发访问
 * - 配置持久化：支持配置的保存和持久化
 *
 * 配置分类：
 * - 服务器配置：HTTP服务器的监听地址、端口、超时等
 * - 设备配置：FANUC设备的连接信息、采集参数等
 * - 数据推送配置：数据推送的目标、格式、批量等
 * - 缓存配置：数据缓存的类型、大小、过期时间等
 * - 日志配置：日志级别、格式、轮转策略等
 * - 监控配置：健康检查、性能监控、资源限制等
 *
 * 设计特性：
 * - 模块化：按功能模块组织配置结构
 * - 类型安全：使用强类型确保配置的正确性
 * - 向后兼容：支持配置的向后兼容性
 * - 环境适配：支持不同部署环境的配置差异
 * - 单例模式：全局唯一的配置实例
 *
 * 业务价值：
 * - 灵活配置：支持不同场景的灵活配置
 * - 运维友好：清晰的配置结构，便于运维管理
 * - 错误预防：配置验证机制，预防配置错误
 * - 性能优化：丰富的性能配置选项
 *
 * @package config
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package config

/**
 * 标准库导入
 *
 * - encoding/json: JSON编码解码，用于配置文件的解析和序列化
 * - fmt: 格式化输入输出，用于错误信息的格式化
 * - io/ioutil: 文件I/O操作，用于配置文件的读取和写入
 * - sync: 并发控制，用于配置的并发安全访问
 */
import (
	"encoding/json" /** JSON编码解码，用于配置文件处理 */
	"fmt"           /** 格式化输入输出，用于错误信息处理 */
	"io/ioutil"     /** 文件I/O操作，用于配置文件读写 */
	"sync"          /** 并发控制，用于配置的线程安全 */
)

/**
 * 系统配置结构体
 *
 * 功能：作为整个FANUC采集器的配置根节点，包含所有子模块的配置信息
 *
 * 设计原则：
 * - 模块化：按功能模块组织配置，便于管理和维护
 * - 层次化：清晰的配置层次结构，避免配置冲突
 * - 完整性：覆盖采集器运行所需的所有配置项
 * - 并发安全：使用读写锁保护配置的并发访问
 *
 * 配置模块说明：
 * - Server：HTTP服务器配置，包括监听地址、端口、超时等
 * - Devices：设备配置管理，包括设备列表和采集参数
 * - DataPush：数据推送配置，包括推送目标和格式
 * - Cache：缓存配置，包括缓存类型、大小、过期时间
 * - Logger：日志配置，包括级别、格式、轮转策略
 * - Monitoring：监控配置，包括健康检查和性能监控
 *
 * @struct SystemConfig
 */
type SystemConfig struct {
	/** HTTP服务器配置，包含监听地址、端口、超时等参数 */
	Server ServerConfig `json:"server"`

	/** 设备配置管理，包含设备列表和采集参数 */
	Devices DevicesConfig `json:"devices"`

	/** 数据推送配置，包含推送目标、格式、批量等参数 */
	DataPush DataPushConfig `json:"data_push"`

	/** 缓存配置，包含缓存类型、大小、过期时间等参数 */
	Cache CacheConfig `json:"cache"`

	/** 日志配置，包含级别、格式、轮转策略等参数 */
	Logger LoggerConfig `json:"logger"`

	/** 监控配置，包含健康检查、性能监控等参数 */
	Monitoring MonitoringConfig `json:"monitoring"`

	/** 读写锁，保护配置的并发访问 */
	mu sync.RWMutex
}

/**
 * 服务器配置结构体
 *
 * 功能：定义HTTP服务器的运行参数，包括网络配置和超时设置
 *
 * 配置项说明：
 * - Host：服务器监听地址，0.0.0.0表示监听所有网络接口
 * - Port：服务器监听端口，建议使用9005端口
 * - ReadTimeout：HTTP请求读取超时时间，防止慢客户端
 * - WriteTimeout：HTTP响应写入超时时间，防止慢网络
 *
 * @struct ServerConfig
 */
type ServerConfig struct {
	/** 服务器监听地址，如"0.0.0.0"或"127.0.0.1" */
	Host string `json:"host"`

	/** 服务器监听端口，建议使用9005 */
	Port int `json:"port"`

	/** HTTP请求读取超时时间（秒） */
	ReadTimeout int `json:"read_timeout"`

	/** HTTP响应写入超时时间（秒） */
	WriteTimeout int `json:"write_timeout"`
}

/**
 * 设备配置管理结构体
 *
 * 功能：管理FANUC设备的配置来源、刷新策略和设备列表
 *
 * 配置来源支持：
 * - file：从本地JSON文件读取设备配置
 * - api：从12_server_api动态获取设备配置
 *
 * 配置项说明：
 * - ConfigSource：配置来源类型，"file"或"api"
 * - ConfigFile：本地配置文件路径
 * - ConfigAPI：API配置接口URL
 * - AutoStart：是否自动启动所有设备
 * - RefreshInterval：配置刷新间隔（秒）
 * - Devices：设备配置列表
 *
 * @struct DevicesConfig
 */
type DevicesConfig struct {
	/** 配置来源类型："file"（文件）或"api"（API接口） */
	ConfigSource string `json:"config_source"`

	/** 本地设备配置文件路径，当ConfigSource为"file"时使用 */
	ConfigFile string `json:"config_file"`

	/** API配置接口URL，当ConfigSource为"api"时使用 */
	ConfigAPI string `json:"config_api"`

	/** 是否自动启动所有已启用的设备 */
	AutoStart bool `json:"auto_start"`

	/** 配置刷新间隔（秒），用于API配置的定时刷新 */
	RefreshInterval int `json:"refresh_interval"`

	/** 设备配置列表，包含所有FANUC设备的详细配置 */
	Devices []DeviceConfig `json:"devices"`
}

/**
 * 单个设备配置结构体
 *
 * 功能：定义单个FANUC设备的连接参数、采集配置和重试策略
 *
 * 网络配置：
 * - IP：FANUC设备的IP地址
 * - Port：FOCAS2通信端口，通常为8193
 * - Timeout：连接超时时间
 *
 * 采集配置：
 * - CollectInterval：数据采集间隔（毫秒）
 * - Enabled：是否启用此设备
 * - AutoStart：是否自动启动采集
 *
 * 重试策略：
 * - RetryCount：连接失败重试次数
 * - RetryDelay：重试间隔时间（秒）
 *
 * @struct DeviceConfig
 */
type DeviceConfig struct {
	/** 设备唯一标识符，如"FANUC_001" */
	ID string `json:"id"`

	/** 设备显示名称，如"车间A-机床1" */
	Name string `json:"name"`

	/** 设备数据类型，如"fanuc_30i", "fanuc_0i"等 */
	DataType string `json:"data_type"`

	/** FANUC设备的IP地址 */
	IP string `json:"ip"`

	/** FOCAS2通信端口，通常为8193 */
	Port int `json:"port"`

	/** 是否启用此设备的数据采集 */
	Enabled bool `json:"enabled"`

	/** 是否在系统启动时自动开始采集 */
	AutoStart bool `json:"auto_start"`

	/** 数据采集间隔（毫秒），建议1000-5000ms */
	CollectInterval int `json:"collect_interval"`

	/** 连接超时时间（秒） */
	Timeout int `json:"timeout"`

	/** 连接失败时的重试次数 */
	RetryCount int `json:"retry_count"`

	/** 重试间隔时间（秒） */
	RetryDelay int `json:"retry_delay"`

	/** 设备物理位置，如"车间A-01"等 */
	Location string `json:"location"`

	/** 设备品牌，如"FANUC", "Siemens"等 */
	Brand string `json:"brand"`

	/** 设备型号，如"30i", "0i"等 */
	Model string `json:"model"`
}

// DataPushConfig 数据推送配置
type DataPushConfig struct {
	Type      string                 `json:"type"` // "restful", "mqtt", "nats"
	Config    map[string]interface{} `json:"config"`
	Enabled   bool                   `json:"enabled"`
	BatchSize int                    `json:"batch_size"`
	Timeout   int                    `json:"timeout"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Type          string `json:"type"`           // "memory", "file", "redis"
	MaxSize       int    `json:"max_size"`       // 最大缓存条数
	MaxAge        int    `json:"max_age"`        // 最大缓存时间(秒)
	FlushInterval int    `json:"flush_interval"` // 刷新间隔(秒)
	StoragePath   string `json:"storage_path"`   // 文件存储路径
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level    string               `json:"level"`    // "debug", "info", "warn", "error"
	Format   string               `json:"format"`   // "text", "json"
	Output   string               `json:"output"`   // 输出路径或"stdout"
	Rotation LoggerRotationConfig `json:"rotation"` // 日志轮转配置
}

// LoggerRotationConfig 日志轮转配置
type LoggerRotationConfig struct {
	Enabled    bool `json:"enabled"`
	MaxSize    int  `json:"max_size"` // MB
	MaxBackups int  `json:"max_backups"`
	MaxAge     int  `json:"max_age"` // days
	Compress   bool `json:"compress"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	HealthCheckInterval int  `json:"health_check_interval"` // 秒
	MetricsEnabled      bool `json:"metrics_enabled"`
	MaxMemoryMB         int  `json:"max_memory_mb"`
}

var (
	globalConfig *SystemConfig
	configOnce   sync.Once
)

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*SystemConfig, error) {
	configOnce.Do(func() {
		globalConfig = &SystemConfig{}
	})

	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	globalConfig.mu.Lock()
	defer globalConfig.mu.Unlock()

	err = json.Unmarshal(data, globalConfig)
	if err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	setDefaults(globalConfig)

	return globalConfig, nil
}

// GetConfig 获取全局配置
func GetConfig() *SystemConfig {
	if globalConfig == nil {
		// 返回默认配置
		return getDefaultConfig()
	}
	return globalConfig
}

// setDefaults 设置默认值
func setDefaults(config *SystemConfig) {
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 9005
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = 30
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = 30
	}

	if config.Devices.RefreshInterval == 0 {
		config.Devices.RefreshInterval = 60
	}

	if config.DataPush.BatchSize == 0 {
		config.DataPush.BatchSize = 10
	}
	if config.DataPush.Timeout == 0 {
		config.DataPush.Timeout = 30
	}

	if config.Cache.MaxSize == 0 {
		config.Cache.MaxSize = 10000
	}
	if config.Cache.MaxAge == 0 {
		config.Cache.MaxAge = 3600
	}
	if config.Cache.FlushInterval == 0 {
		config.Cache.FlushInterval = 60
	}

	// 设置日志默认值
	if config.Logger.Level == "" {
		config.Logger.Level = "info"
	}
	if config.Logger.Format == "" {
		config.Logger.Format = "text"
	}
	if config.Logger.Output == "" {
		config.Logger.Output = "logs/fanuc_v2.log"
	}
	if !config.Logger.Rotation.Enabled {
		config.Logger.Rotation.Enabled = true
	}
	if config.Logger.Rotation.MaxSize == 0 {
		config.Logger.Rotation.MaxSize = 100
	}
	if config.Logger.Rotation.MaxAge == 0 {
		config.Logger.Rotation.MaxAge = 3 // 保留3天
	}
	if config.Logger.Rotation.MaxBackups == 0 {
		config.Logger.Rotation.MaxBackups = 10
	}

	if config.Monitoring.HealthCheckInterval == 0 {
		config.Monitoring.HealthCheckInterval = 30
	}
	if config.Monitoring.MaxMemoryMB == 0 {
		config.Monitoring.MaxMemoryMB = 512
	}

	// 设置设备默认值
	for i := range config.Devices.Devices {
		device := &config.Devices.Devices[i]
		if device.CollectInterval == 0 {
			device.CollectInterval = 1000 // 1秒
		}
		if device.Timeout == 0 {
			device.Timeout = 10
		}
		if device.RetryCount == 0 {
			device.RetryCount = 3
		}
		if device.RetryDelay == 0 {
			device.RetryDelay = 5
		}
	}
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *SystemConfig {
	config := &SystemConfig{
		Server: ServerConfig{
			Host:         "0.0.0.0",
			Port:         9005,
			ReadTimeout:  30,
			WriteTimeout: 30,
		},
		Devices: DevicesConfig{
			ConfigSource:    "file",
			ConfigFile:      "devices.json",
			AutoStart:       true,
			RefreshInterval: 60,
		},
		DataPush: DataPushConfig{
			Type:      "restful",
			Enabled:   true,
			BatchSize: 10,
			Timeout:   30,
			Config: map[string]interface{}{
				"url": "http://localhost:8081/api/v1/data",
			},
		},
		Cache: CacheConfig{
			Type:          "memory",
			MaxSize:       10000,
			MaxAge:        3600,
			FlushInterval: 60,
		},
		Logger: LoggerConfig{
			Level:  "info",
			Format: "text",
			Output: "logs/fanuc_v2.log",
			Rotation: LoggerRotationConfig{
				Enabled:    true,
				MaxSize:    100,
				MaxAge:     3, // 保留3天
				MaxBackups: 10,
				Compress:   true,
			},
		},
		Monitoring: MonitoringConfig{
			HealthCheckInterval: 30,
			MetricsEnabled:      true,
			MaxMemoryMB:         512,
		},
	}
	setDefaults(config)
	return config
}

// UpdateDeviceConfig 更新设备配置
func (c *SystemConfig) UpdateDeviceConfig(deviceID string, config DeviceConfig) {
	c.mu.Lock()
	defer c.mu.Unlock()

	for i, device := range c.Devices.Devices {
		if device.ID == deviceID {
			c.Devices.Devices[i] = config
			return
		}
	}
	// 如果设备不存在，添加新设备
	c.Devices.Devices = append(c.Devices.Devices, config)
}

// GetDeviceConfig 获取设备配置
func (c *SystemConfig) GetDeviceConfig(deviceID string) (DeviceConfig, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	for _, device := range c.Devices.Devices {
		if device.ID == deviceID {
			return device, true
		}
	}
	return DeviceConfig{}, false
}

// GetAllDeviceConfigs 获取所有设备配置
func (c *SystemConfig) GetAllDeviceConfigs() []DeviceConfig {
	c.mu.RLock()
	defer c.mu.RUnlock()

	configs := make([]DeviceConfig, len(c.Devices.Devices))
	copy(configs, c.Devices.Devices)
	return configs
}

// RemoveDeviceConfig 移除设备配置
func (c *SystemConfig) RemoveDeviceConfig(deviceID string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	for i, device := range c.Devices.Devices {
		if device.ID == deviceID {
			c.Devices.Devices = append(c.Devices.Devices[:i], c.Devices.Devices[i+1:]...)
			return
		}
	}
}

// SaveConfig 保存配置到文件
func (c *SystemConfig) SaveConfig(configPath string) error {
	c.mu.RLock()
	defer c.mu.RUnlock()

	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	err = ioutil.WriteFile(configPath, data, 0644)
	if err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	return nil
}

// Validate 验证配置
func (c *SystemConfig) Validate() error {
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", c.Server.Port)
	}

	for _, device := range c.Devices.Devices {
		if device.ID == "" {
			return fmt.Errorf("设备ID不能为空")
		}
		if device.IP == "" {
			return fmt.Errorf("设备IP不能为空: %s", device.ID)
		}
		if device.Port <= 0 || device.Port > 65535 {
			return fmt.Errorf("无效的设备端口: %s:%d", device.ID, device.Port)
		}
	}

	return nil
}
