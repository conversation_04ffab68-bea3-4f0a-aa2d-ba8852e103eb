#!/bin/bash

# FANUC v2 修改验证脚本（不进行编译测试）
# 只验证代码修改是否正确应用

echo "🔍 FANUC v2 修改验证（代码检查）"
echo "================================"

# 检查当前目录
if [ ! -f "collector/device_collector.go" ]; then
    echo "❌ 错误: 请在 fanuc_v2 项目根目录下运行此脚本"
    exit 1
fi

echo "📋 检查修改项目..."

CHECKS_PASSED=0
TOTAL_CHECKS=10

# 检查1: lastCollectionAttemptTime 字段
if grep -q "lastCollectionAttemptTime.*time.Time" collector/device_collector.go; then
    echo "✅ 1. lastCollectionAttemptTime 字段已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 1. lastCollectionAttemptTime 字段未找到"
fi

# 检查2: consecutiveFailures 字段
if grep -q "consecutiveFailures.*int" collector/device_collector.go; then
    echo "✅ 2. consecutiveFailures 字段已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 2. consecutiveFailures 字段未找到"
fi

# 检查3: heartbeatCh 字段
if grep -q "heartbeatCh.*chan.*time.Time" collector/device_collector.go; then
    echo "✅ 3. heartbeatCh 字段已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 3. heartbeatCh 字段未找到"
fi

# 检查4: IsCollectionHealthy 函数
if grep -q "func.*IsCollectionHealthy" collector/device_collector.go; then
    echo "✅ 4. IsCollectionHealthy 函数已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 4. IsCollectionHealthy 函数未找到"
fi

# 检查5: startCollectionMonitor 函数
if grep -q "func.*startCollectionMonitor" collector/device_collector.go; then
    echo "✅ 5. startCollectionMonitor 函数已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 5. startCollectionMonitor 函数未找到"
fi

# 检查6: 智能健康检查逻辑
if grep -q "基础超时时间：采集间隔的5倍" collector/device_collector.go; then
    echo "✅ 6. 智能健康检查机制已实现"
    ((CHECKS_PASSED++))
else
    echo "❌ 6. 智能健康检查机制未找到"
fi

# 检查7: 监控器panic恢复
if grep -q "采集循环监控器发生panic" collector/device_collector.go; then
    echo "✅ 7. 监控器panic恢复机制已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 7. 监控器panic恢复机制未找到"
fi

# 检查8: 防频繁重启机制
if grep -q "距离上次重启时间太近" collector/device_collector.go; then
    echo "✅ 8. 防频繁重启机制已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 8. 防频繁重启机制未找到"
fi

# 检查9: 心跳日志
if grep -q "采集循环心跳" collector/device_collector.go; then
    echo "✅ 9. 心跳日志机制已添加"
    ((CHECKS_PASSED++))
else
    echo "❌ 9. 心跳日志机制未找到"
fi

# 检查10: 时间戳更新机制
if grep -q "同时更新尝试时间" collector/device_collector.go; then
    echo "✅ 10. 时间戳更新机制已完善"
    ((CHECKS_PASSED++))
else
    echo "❌ 10. 时间戳更新机制未找到"
fi

echo ""
echo "📊 修改检查结果: $CHECKS_PASSED/$TOTAL_CHECKS 项通过"

if [ $CHECKS_PASSED -eq $TOTAL_CHECKS ]; then
    echo "🎉 所有修改已正确应用！"
    SUCCESS=true
else
    echo "⚠️  部分修改未应用，完成度: $(( CHECKS_PASSED * 100 / TOTAL_CHECKS ))%"
    SUCCESS=false
fi

# 代码统计
echo ""
echo "📊 代码统计:"
echo "总行数: $(wc -l < collector/device_collector.go)"
echo "包含 'lastCollectionAttemptTime' 的行数: $(grep -c 'lastCollectionAttemptTime' collector/device_collector.go)"
echo "包含 'consecutiveFailures' 的行数: $(grep -c 'consecutiveFailures' collector/device_collector.go)"
echo "包含 'IsCollectionHealthy' 的行数: $(grep -c 'IsCollectionHealthy' collector/device_collector.go)"
echo "包含 'startCollectionMonitor' 的行数: $(grep -c 'startCollectionMonitor' collector/device_collector.go)"
echo "包含 '健康检查' 的行数: $(grep -c '健康检查' collector/device_collector.go)"
echo "包含 'panic' 的行数: $(grep -c 'panic' collector/device_collector.go)"

# 显示关键修改摘要
if [ "$SUCCESS" = true ]; then
    echo ""
    echo "🎯 关键修改摘要:"
    echo "1. ✨ 数据结构增强 - 添加时间跟踪和失败计数字段"
    echo "2. 🧠 智能健康检查 - 动态超时和准确状态判断"
    echo "3. 🛡️  监控器增强 - panic恢复和防频繁重启"
    echo "4. 💪 采集循环增强 - 自动重启和心跳监控"
    echo "5. ⏰ 时间戳管理 - 全面的活跃状态跟踪"
    
    echo ""
    echo "🎯 预期解决的问题:"
    echo "- ✅ 采集循环7分钟后停止的问题"
    echo "- ✅ 监控器检测到不健康但不重启的问题"
    echo "- ✅ 系统缺乏自动恢复能力的问题"
    echo "- ✅ 缺乏运行状态监控的问题"
    
    echo ""
    echo "📋 部署后关键监控指标:"
    echo "✅ 正常运行:"
    echo "  - '采集循环心跳: 第X次采集' - 每30次采集输出"
    echo "  - '数据采集成功' - 成功采集数据"
    echo "⚠️  问题检测:"
    echo "  - '采集循环健康检查失败 (X/3)' - 健康检查失败"
    echo "  - '检测到采集循环可能停止' - 自动重启触发"
    echo "❌ 异常情况:"
    echo "  - 'panic' - 异常情况（应该很少出现）"
fi

echo ""
echo "✅ 代码修改验证完成!"
echo "📖 详细说明请查看: COLLECTION_LOOP_FIX_FINAL.md"

if [ "$SUCCESS" = true ]; then
    echo ""
    echo "🚀 下一步操作:"
    echo "1. 在Linux环境中编译: go build -o fanuc_v2 ./cmd/main.go"
    echo "2. 部署到远程服务器运行"
    echo "3. 观察日志，确认修复效果"
    echo "4. 进行至少1小时的稳定性测试"
else
    echo ""
    echo "⚠️  请检查未通过的修改项，确保所有修改都已正确应用"
fi
