name: Windows CMake

on:
  push:
    paths:
      - '.github/workflows/windows-cmake.yml'
      - 'example/**'

env:
  BUILD_TYPE: Release

jobs:
  build:
    runs-on: windows-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Submodules & Dependencies
      run: |
        git submodule update --init --recursive
        cd example/extern/libconfig
        mkdir build
        cd build
        cmake -A Win32 ..
        cmake --build . --config Release

    - name: Create Build Environment
      shell: powershell
      run: cmake -E make_directory ${{runner.workspace}}/example/build

    - name: Configure CMake
      working-directory: ${{runner.workspace}}/example/build
      shell: powershell
      run: cmake -A Win32 -DCMAKE_BUILD_TYPE=$env:BUILD_TYPE $env:GITHUB_WORKSPACE/example

    - name: Build
      working-directory: ${{runner.workspace}}/example/build
      shell: powershell
      run: cmake --build . --config $env:BUILD_TYPE

    - name: Test
      working-directory: ${{runner.workspace}}/example/build
      shell: powershell
      run: ctest -V -C $env:BUILD_TYPE
