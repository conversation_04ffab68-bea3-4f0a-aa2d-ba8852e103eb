# FANUC FOCAS2/Ethernet 生产环境 - Ubuntu 16.04 x86
# 多阶段构建：编译阶段 + 运行时阶段
# 优化镜像大小，编译后删除源代码

# ============================================================================
# 构建阶段：编译 Go 程序
# ============================================================================
FROM i386/ubuntu:16.04 AS builder

# 构建阶段的代理配置参数
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG http_proxy
ARG https_proxy
ARG no_proxy

# 设置构建阶段的代理环境变量
ENV HTTP_PROXY=${HTTP_PROXY}
ENV HTTPS_PROXY=${HTTPS_PROXY}
ENV NO_PROXY=${NO_PROXY}
ENV http_proxy=${http_proxy}
ENV https_proxy=${https_proxy}
ENV no_proxy=${no_proxy}

# 配置APT代理（如果有代理）
RUN if [ -n "$HTTP_PROXY" ]; then \
        echo "Acquire::http::Proxy \"$HTTP_PROXY\";" > /etc/apt/apt.conf.d/01proxy; \
        echo "Acquire::https::Proxy \"$HTTPS_PROXY\";" >> /etc/apt/apt.conf.d/01proxy; \
    fi

# 设置构建环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Go语言版本配置 (x86版本)
ENV GO_VERSION=1.23.9
ENV GOROOT=/usr/local/go
ENV GOPATH=/go
ENV PATH=$GOROOT/bin:$GOPATH/bin:$PATH
ENV CGO_ENABLED=1
ENV GOOS=linux
ENV GOARCH=386

# 构建工作目录
WORKDIR /build

# 更新软件源为阿里云镜像（提高下载速度）
RUN cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ xenial-backports main restricted universe multiverse" >> /etc/apt/sources.list

# 安装构建所需的依赖（最小化安装）
RUN apt-get update && apt-get install -y \
    # 基础工具
    curl \
    wget \
    ca-certificates \
    # 编译工具链（Go CGO 需要）
    build-essential \
    gcc \
    g++ \
    make \
    libc6-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Go语言 (x86版本)
RUN echo "下载Go语言 x86版本..." && \
    wget -q https://go.dev/dl/go1.23.9.linux-386.tar.gz -O /tmp/go.tar.gz && \
    echo "解压Go语言..." && \
    tar -C /usr/local -xzf /tmp/go.tar.gz && \
    rm /tmp/go.tar.gz && \
    echo "验证Go安装..." && \
    /usr/local/go/bin/go version

# 创建Go工作目录
RUN mkdir -p $GOPATH/src $GOPATH/bin $GOPATH/pkg && \
    chmod -R 755 $GOPATH

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制并安装FOCAS库文件和头文件（构建阶段需要）
COPY fwlib/ /tmp/fwlib/
RUN echo "安装FOCAS库和头文件..." && \
    # 安装FOCAS库文件
    if [ -f /tmp/fwlib/libfwlib32-linux-x86.so.1.0.5 ]; then \
        echo "安装FOCAS库文件..." && \
        cp /tmp/fwlib/libfwlib32-linux-x86.so.1.0.5 /usr/local/lib/libfwlib32.so.1.0.5 && \
        ln -sf /usr/local/lib/libfwlib32.so.1.0.5 /usr/local/lib/libfwlib32.so && \
        chmod 755 /usr/local/lib/libfwlib32.so.1.0.5; \
    fi && \
    # 安装FOCAS头文件
    if [ -f /tmp/fwlib/fwlib32.h ]; then \
        echo "安装FOCAS头文件..." && \
        cp /tmp/fwlib/fwlib32.h /usr/local/include/ && \
        chmod 644 /usr/local/include/fwlib32.h; \
    fi && \
    # 执行FOCAS相关脚本（如果存在）
    if [ -f "/tmp/fwlib/build-deps.sh" ]; then \
        echo "执行 build-deps.sh..." && \
        chmod +x /tmp/fwlib/build-deps.sh && \
        cd /tmp && \
        ./fwlib/build-deps.sh; \
    else \
        echo "build-deps.sh 脚本不存在，跳过执行"; \
    fi && \
    if [ -f "/tmp/fwlib/install.sh" ]; then \
        echo "执行 install.sh..." && \
        chmod +x /tmp/fwlib/install.sh && \
        cd /tmp && \
        ./fwlib/install.sh; \
    else \
        echo "install.sh 脚本不存在，跳过执行"; \
    fi && \
    # 清理临时文件
    echo "清理FOCAS临时文件..." && \
    rm -rf /tmp/fwlib

# 配置动态链接器
RUN echo "/usr/local/lib" > /etc/ld.so.conf.d/focas.conf && \
    ldconfig

# 创建pkg-config配置
RUN mkdir -p /usr/local/lib/pkgconfig && \
    echo 'prefix=/usr/local' > /usr/local/lib/pkgconfig/focas.pc && \
    echo 'exec_prefix=${prefix}' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'libdir=${exec_prefix}/lib' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'includedir=${prefix}/include' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo '' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Name: FOCAS2/Ethernet' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Description: FANUC FOCAS2/Ethernet Library for Linux' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Version: 1.0.0' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Libs: -L${libdir} -lfwlib32' >> /usr/local/lib/pkgconfig/focas.pc && \
    echo 'Cflags: -I${includedir}' >> /usr/local/lib/pkgconfig/focas.pc

# 设置环境变量
RUN echo '# FOCAS2/Ethernet Environment' >> /etc/profile.d/focas.sh && \
    echo 'export FOCAS_HOME=/usr/local' >> /etc/profile.d/focas.sh && \
    echo 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/lib' >> /etc/profile.d/focas.sh && \
    echo 'export PKG_CONFIG_PATH=$PKG_CONFIG_PATH:/usr/local/lib/pkgconfig' >> /etc/profile.d/focas.sh && \
    chmod 644 /etc/profile.d/focas.sh

# 复制Go源码进行编译（仅在构建阶段使用）
COPY go.mod go.sum /build/
COPY cmd/ /build/cmd/
COPY api/ /build/api/
COPY cache/ /build/cache/
COPY collector/ /build/collector/
COPY config/ /build/config/
COPY define/ /build/define/
COPY logger/ /build/logger/
COPY manager/ /build/manager/
COPY models/ /build/models/
COPY pusher/ /build/pusher/

# 编译Go程序
RUN cd /build && \
    echo "初始化Go模块..." && \
    go mod tidy && \
    echo "编译FANUC采集器..." && \
    go build -ldflags="-s -w" -o fanuc_collector ./cmd/main.go && \
    echo "编译完成，可执行文件大小:" && \
    ls -lh fanuc_collector

# ============================================================================
# 运行时阶段：创建最小化的运行时镜像
# ============================================================================
FROM i386/ubuntu:16.04 AS runtime

# 设置运行时环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装运行时必需的依赖（最小化）
RUN apt-get update && apt-get install -y \
    # 基础运行时库
    libc6 \
    # 网络工具（用于健康检查）
    curl \
    # 清理APT缓存
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN useradd -m -s /bin/bash fanuc && \
    echo "fanuc:fanuc" | chpasswd

# 创建应用目录结构
RUN mkdir -p /app/bin /app/logs /app/cache && \
    chown -R fanuc:fanuc /app

# 从构建阶段复制编译好的可执行文件
COPY --from=builder /build/fanuc_collector /app/bin/fanuc_collector

# 从构建阶段复制FOCAS运行时库
COPY --from=builder /usr/local/lib/libfwlib32.so* /usr/local/lib/

# 复制FOCAS脚本到运行时环境并执行（确保运行时环境正确配置）
COPY fwlib/ /tmp/fwlib/
RUN echo "运行时环境配置FOCAS..." && \
    # 配置动态链接器
    echo "/usr/local/lib" > /etc/ld.so.conf.d/focas.conf && \
    ldconfig && \
    # 执行FOCAS相关脚本（如果存在）
    if [ -f "/tmp/fwlib/build-deps.sh" ]; then \
        echo "执行 build-deps.sh..." && \
        chmod +x /tmp/fwlib/build-deps.sh && \
        cd /tmp && \
        ./fwlib/build-deps.sh; \
    else \
        echo "build-deps.sh 脚本不存在，跳过执行"; \
    fi && \
    if [ -f "/tmp/fwlib/install.sh" ]; then \
        echo "执行 install.sh..." && \
        chmod +x /tmp/fwlib/install.sh && \
        cd /tmp && \
        ./fwlib/install.sh; \
    else \
        echo "install.sh 脚本不存在，跳过执行"; \
    fi && \
    # 清理临时文件
    echo "清理FOCAS临时文件..." && \
    rm -rf /tmp/fwlib

# 设置可执行文件权限
RUN chmod +x /app/bin/fanuc_collector && \
    chown fanuc:fanuc /app/bin/fanuc_collector

# 创建优化的启动脚本
COPY <<EOF /app/entrypoint.sh
#!/bin/bash

# FANUC FOCAS2/Ethernet 生产环境启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=================================================================="
echo "           FANUC Data Collector v2 - 生产环境"
echo "=================================================================="
echo

# 显示系统信息
echo -e "\${BLUE}系统信息:\${NC}"
echo "操作系统: \$(cat /etc/os-release | grep PRETTY_NAME | cut -d'\"' -f2)"
echo "架构: \$(uname -m)"
echo "内核: \$(uname -r)"
echo "时区: \$(date +%Z)"
echo

# 检查FOCAS库
echo -e "\${BLUE}FOCAS库检查:\${NC}"
if [ -f "/usr/local/lib/libfwlib32.so" ]; then
    echo -e "\${GREEN}✓\${NC} FOCAS库文件: \$(ls -la /usr/local/lib/libfwlib32.so*)"
else
    echo -e "\${YELLOW}⚠\${NC} FOCAS库文件未找到"
fi

# 检查FOCAS头文件（运行时不需要，但可以检查是否存在）
if [ -f "/usr/local/include/fwlib32.h" ]; then
    echo -e "\${GREEN}✓\${NC} FOCAS头文件: /usr/local/include/fwlib32.h"
else
    echo -e "\${YELLOW}⚠\${NC} FOCAS头文件未找到（运行时不需要）"
fi

# 检查动态链接
if ldconfig -p | grep -q "libfwlib32"; then
    echo -e "\${GREEN}✓\${NC} 动态链接器已配置"
else
    echo -e "\${YELLOW}⚠\${NC} 动态链接器未配置"
fi

# 检查FOCAS配置文件
if [ -f "/etc/ld.so.conf.d/focas.conf" ]; then
    echo -e "\${GREEN}✓\${NC} FOCAS动态链接配置: \$(cat /etc/ld.so.conf.d/focas.conf)"
else
    echo -e "\${YELLOW}⚠\${NC} FOCAS动态链接配置未找到"
fi

# 验证FOCAS脚本执行结果
echo -e "\${BLUE}FOCAS脚本执行验证:\${NC}"
echo -e "\${GREEN}✓\${NC} build-deps.sh 和 install.sh 已在镜像构建时执行"
echo -e "\${GREEN}✓\${NC} 运行时环境FOCAS配置已完成"

# 检查可执行文件
echo -e "\${BLUE}应用程序检查:\${NC}"
if [ -f "/app/bin/fanuc_collector" ]; then
    echo -e "\${GREEN}✓\${NC} 可执行文件: \$(ls -la /app/bin/fanuc_collector)"
else
    echo -e "\${RED}❌\${NC} 可执行文件不存在"
    exit 1
fi

echo
echo -e "\${BLUE}工作目录:\${NC} \$(pwd)"
echo -e "\${BLUE}用户:\${NC} \$(whoami)"
echo

# 如果有参数，执行参数命令
if [ \$# -gt 0 ]; then
    echo "执行命令: \$@"
    exec "\$@"
else
    # 默认启动FANUC采集器
    echo "启动FANUC数据采集器..."
    echo "=================================================================="
    exec /app/bin/fanuc_collector
fi
EOF

RUN chmod +x /app/entrypoint.sh

# 切换到非root用户
USER fanuc

# 清理运行时镜像中的源代码文件（确保生产环境安全）
RUN echo "清理Go源代码文件..." && \
    find /app -name "*.go" -type f -delete && \
    find /app -name "go.mod" -type f -delete && \
    find /app -name "go.sum" -type f -delete && \
    echo "源代码文件清理完成"

# 设置工作目录
WORKDIR /app

# 暴露端口
EXPOSE 9110

# 设置启动脚本
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令 - 启动FANUC采集器
CMD []

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9110/api/v1/health || exit 1

# 标签信息
LABEL maintainer="FANUC Development Team"
LABEL description="FANUC FOCAS2/Ethernet Data Collector - Production Runtime"
LABEL version="2.0.0"
LABEL architecture="i386"
LABEL build.stage="multi-stage"
LABEL build.optimized="true"
