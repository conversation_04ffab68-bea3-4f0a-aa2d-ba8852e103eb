# Docker 构建忽略文件
# 用于减少 Docker 构建上下文大小，提高构建速度

# 版本控制
.git
.gitignore
.gitattributes

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
**/.DS_Store

# 构建产物和临时文件
build/
*.exe
*.dll
*.so
*.dylib
*.test
*.out
main
fanuc_collector
test_fanuc_collector

# 日志文件
logs/
*.log
*.log.*

# 缓存文件（但保留源码目录）
# cache/ - 注释掉，因为需要复制cache目录中的Go源码
*.cache

# 测试覆盖率文件
*.cover
coverage.html
coverage.out

# 依赖管理
vendor/

# 配置和数据文件（运行时挂载）
config*.json
devices.json
*.env

# 文档和说明文件（不需要在容器中）
*.md
docs/
doc/
README*
CHANGELOG*
LICENSE*

# 脚本文件（不需要在容器中运行，但保留fwlib中的脚本）
*.sh
!fwlib/*.sh
!fwlib/build-deps.sh
!fwlib/install.sh

# 测试文件
*_test.go
test/
tests/

# 开发工具配置
Makefile
docker-compose*.yml
Dockerfile*

# 临时文件和备份
*.tmp
*.temp
*.bak
*.backup
*~

# 压缩文件
*.zip
*.tar.gz
*.tgz
*.rar

# 其他不需要的文件
todo.md
TODO*
NOTES*
