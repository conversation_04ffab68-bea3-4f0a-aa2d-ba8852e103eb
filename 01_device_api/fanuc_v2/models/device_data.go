/**
 * FANUC设备数据模型模块
 *
 * 功能概述：
 * 本模块定义了FANUC设备采集器的完整数据模型，包括设备状态、系统信息、
 * 位置数据、程序信息、报警信息、负载信息等核心数据结构
 *
 * 主要功能：
 * - 数据模型定义：定义FANUC设备的各种数据结构
 * - 数据转换：提供数据格式转换和兼容性处理
 * - 数据验证：提供数据有效性验证功能
 * - 缓存支持：支持数据缓存和重试机制
 * - 监控指标：提供设备和系统的监控指标
 * - API接口：定义API请求和响应的数据结构
 *
 * 数据分类：
 * - 设备数据：设备状态、连接信息、采集统计
 * - 系统信息：CNC系统配置、版本、轴信息
 * - 位置数据：各轴位置、进给速度、主轴转速
 * - 程序信息：当前程序、序列号、程序名称
 * - 报警信息：报警状态、报警消息、报警类型
 * - 负载信息：伺服负载、主轴负载、负载监控
 *
 * 兼容性设计：
 * - 02_device_collect兼容：与数据采集服务的数据格式兼容
 * - 版本兼容：支持不同版本的数据格式
 * - 扩展性：易于添加新的数据字段和结构
 *
 * 技术特性：
 * - 类型安全：使用强类型确保数据的正确性
 * - JSON序列化：支持JSON格式的序列化和反序列化
 * - 时间处理：统一的时间戳和时间计算
 * - 内存优化：合理的数据结构设计，减少内存占用
 *
 * 业务价值：
 * - 数据标准化：统一的数据格式，便于系统集成
 * - 监控支持：丰富的监控指标，支持运维管理
 * - 扩展性：易于扩展的数据模型，支持业务发展
 * - 兼容性：良好的向后兼容性，保护投资
 *
 * @package models
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
package models

/**
 * 标准库导入
 *
 * - time: 时间处理，用于时间戳、持续时间等时间相关操作
 */
import (
	"fmt"
	"strconv"
	"time" /** 时间处理，用于时间戳和持续时间计算 */
)

/**
 * 设备状态结构体
 *
 * 功能：记录FANUC设备的连接状态、运行统计和错误信息
 *
 * 状态维度：
 * - 连接状态：设备是否在线连接
 * - 时间信息：最后通信时间
 * - 统计信息：采集次数、推送次数、缓存数量
 * - 错误信息：错误次数和最后错误详情
 *
 * @struct DeviceStatus
 */
type DeviceStatus struct {
	/** 设备是否已连接，true表示在线，false表示离线 */
	Connected bool `json:"connected"`

	/** 最后一次成功通信的时间 */
	LastSeen time.Time `json:"last_seen"`

	/** 累计错误次数，用于监控设备稳定性 */
	ErrorCount int `json:"error_count"`

	/** 最后一次错误的详细信息，为空表示无错误 */
	LastError string `json:"last_error,omitempty"`

	/** 累计数据采集次数 */
	CollectCount int64 `json:"collect_count"`

	/** 累计数据推送次数 */
	PushCount int64 `json:"push_count"`

	/** 当前缓存中的数据条数 */
	CacheCount int `json:"cache_count"`
}

/**
 * 系统信息结构体
 *
 * 功能：存储FANUC CNC系统的基础配置信息和状态
 *
 * 信息分类：
 * - 系统标识：CNC ID、类型、系列、版本
 * - 硬件配置：最大轴数、轴名称列表
 * - 运行状态：运行状态、报警状态、自动模式
 *
 * 数据来源：
 * - cnc_rdcncid：获取CNC ID
 * - cnc_sysinfo：获取系统信息
 * - cnc_statinfo：获取状态信息
 * - cnc_rdaxisname：获取轴名称
 *
 * @struct SystemInfo
 */
type SystemInfo struct {
	/** CNC系统唯一标识符，由4个32位数字组成 */
	CNCID string `json:"cnc_id"`

	/** CNC控制器类型，如"Series 30i" */
	CNCType string `json:"cnc_type"`

	/** 机床类型，如"Machining Center" */
	MTType string `json:"mt_type"`

	/** CNC系列信息 */
	Series string `json:"series"`

	/** CNC软件版本信息 */
	Version string `json:"version"`

	/** 最大轴数，表示设备支持的轴数量 */
	MaxAxis int16 `json:"max_axis"`

	/** 轴名称列表，如["X", "Y", "Z", "A", "B", "C"] */
	AxisNames []string `json:"axis_names"`

	/** 运行状态：0=停止，1=保持，2=启动，3=运行中，4=中断 */
	RunStatus int16 `json:"run_status"`

	/** 报警状态，0表示无报警，其他值表示有报警 */
	AlarmStatus int16 `json:"alarm_status"`

	/** 自动模式状态，0=手动，1=自动 */
	AutoMode int16 `json:"auto_mode"`
}

// PositionData 位置数据
type PositionData struct {
	AxisCount int32   `json:"axis_count"`
	Absolute  []int32 `json:"absolute"`
	Machine   []int32 `json:"machine"`
	Relative  []int32 `json:"relative"`
	Distance  []int32 `json:"distance"`
	ActualF   int32   `json:"actual_f"` // 实际进给速度
	ActualS   int32   `json:"actual_s"` // 实际主轴转速
}

// ProgramInfo 程序信息
type ProgramInfo struct {
	CurrentProgram int32  `json:"current_program"`
	MainProgram    int32  `json:"main_program"`
	SequenceNumber int32  `json:"sequence_number"`
	ProgramName    string `json:"program_name"`
	ProgramSize    int    `json:"program_size"`
}

// AlarmInfo 报警信息
type AlarmInfo struct {
	AlarmStatus   int16          `json:"alarm_status"`
	HasAlarm      bool           `json:"has_alarm"`
	AlarmMessages []AlarmMessage `json:"alarm_messages,omitempty"`
}

// AlarmMessage 报警消息
type AlarmMessage struct {
	Number  int32  `json:"number"`
	Type    int16  `json:"type"`
	Axis    int16  `json:"axis"`
	Message string `json:"message"`
}

// LoadInfo 负载信息
type LoadInfo struct {
	ServoLoads   []ServoLoad   `json:"servo_loads,omitempty"`
	SpindleLoads []SpindleLoad `json:"spindle_loads,omitempty"`
}

// ServoLoad 伺服负载
type ServoLoad struct {
	AxisName  string  `json:"axis_name"`
	LoadValue float64 `json:"load_value"`
	Unit      string  `json:"unit"`
}

// SpindleLoad 主轴负载
type SpindleLoad struct {
	SpindleName string  `json:"spindle_name"`
	LoadValue   float64 `json:"load_value"`
	Unit        string  `json:"unit"`
	Type        string  `json:"type"` // "load" 或 "speed"
}

// DeviceData 设备数据结构 - 兼容02_device_collect格式
type DeviceData struct {
	DeviceID  string                 `json:"device_id"`          // 设备ID，兼容02_device_collect
	DataType  string                 `json:"data_type"`          // 数据类型，兼容02_device_collect
	Timestamp time.Time              `json:"timestamp"`          // 时间戳，兼容02_device_collect
	RawData   map[string]interface{} `json:"raw_data"`           // 原始数据，兼容02_device_collect
	Location  string                 `json:"location,omitempty"` // 设备位置，兼容02_device_collect

	// 内部使用的详细数据结构（不会推送到02_device_collect）
	DeviceName   string        `json:"device_name,omitempty"`
	Status       DeviceStatus  `json:"status,omitempty"`
	SystemInfo   *SystemInfo   `json:"system_info,omitempty"`
	PositionData *PositionData `json:"position_data,omitempty"`
	ProgramInfo  *ProgramInfo  `json:"program_info,omitempty"`
	AlarmInfo    *AlarmInfo    `json:"alarm_info,omitempty"`
	LoadInfo     *LoadInfo     `json:"load_info,omitempty"`

	// API配置信息（用于元数据构建）
	DeviceConfig *DeviceConfigInfo `json:"device_config,omitempty"`
}

// DeviceConfigInfo 设备配置信息（从API获取）
type DeviceConfigInfo struct {
	Brand           string `json:"brand,omitempty"`
	Model           string `json:"model,omitempty"`
	Location        string `json:"location,omitempty"`
	IP              string `json:"ip,omitempty"`
	Port            int    `json:"port,omitempty"`
	CollectInterval int    `json:"collect_interval,omitempty"`
	DataType        string `json:"data_type,omitempty"`
}

// PushData 推送数据结构 - 与02_generate_data格式一致
type PushData struct {
	/** 设备唯一标识符，从配置文件或API配置信息获取，如"fanuc_001", "temp_001" */
	DeviceID string `json:"device_id"`

	/** 数据生成的时间戳，ISO 8601格式，如"2024-01-01T00:00:00Z" */
	Timestamp time.Time `json:"timestamp"`

	/** 数据类型，标准化类型，如"fanuc_30i", "fanuc_0i", "siemens_840d"等 */
	DataType string `json:"data_type"`

	/** 设备的原始数据，结构化存储传感器和状态信息 */
	RawData RawDataStruct `json:"raw_data"`

	/** 设备元数据，包含配置和环境信息 */
	Metadata MetadataStruct `json:"metadata"`
}

/**
 * 原始数据结构体 - 与02_generate_data格式一致
 *
 * 功能：存储设备从传感器收集的原始数据
 */
type RawDataStruct struct {
	/** 数据唯一标识符，使用雪花ID，如"1000000000000000000" */
	DataID string `json:"data_id"`

	/** 设备是否连接，true表示连接，false表示断开 */
	Connected bool `json:"connected"`

	/** 设备状态：production(生产), idle(空闲), fault(报警), shutdown(关机), disconnected(未连接), maintenance(保养), debugging(调试) */
	Status string `json:"status"`

	/** 状态数据，包含具体的状态信息 */
	StatusData map[string]interface{} `json:"status_data"`

	/** 累计产量 */
	Quantity int `json:"quantity"`

	/** 当前加工程序的名称 */
	CurrentProgram string `json:"current_program"`

	/** 主加工程序的名称 */
	MainProgram string `json:"main_program"`

	/** 实际进给速度 (mm/min) */
	ActualFeedrate int `json:"actual_feedrate"`

	/** 实际主轴转速 (rpm) */
	ActualSpindleSpeed int `json:"actual_spindle_speed"`
}

/**
 * 元数据结构体 - 与02_generate_data格式一致
 *
 * 功能：存储设备的配置和环境信息
 */
type MetadataStruct struct {
	/** 设备物理位置，如"DG-101", "车间A-01"等 */
	Location string `json:"location"`

	/** 数据采集器版本信息 */
	Version string `json:"version"`

	/** 设备类型，如"fanuc_30i", "fanuc_0i"等 */
	DeviceType string `json:"device_type"`

	/** 设备品牌 */
	Brand string `json:"brand,omitempty"`

	/** 设备型号 */
	Model string `json:"model,omitempty"`

	/** 设备IP地址 */
	IPAddress string `json:"ip_address,omitempty"`

	/** 设备端口 */
	Port int `json:"port,omitempty"`

	/** 采集间隔（毫秒） */
	CollectInterval int `json:"collect_interval,omitempty"`
}

// NewDeviceData 创建新的设备数据
func NewDeviceData(deviceID, deviceName string) *DeviceData {
	return &DeviceData{
		DeviceID:   deviceID,
		DeviceName: deviceName,
		DataType:   "fanuc_cnc", // 默认数据类型
		Timestamp:  time.Now(),
		RawData:    make(map[string]interface{}),
		Status: DeviceStatus{
			Connected: false,
			LastSeen:  time.Now(),
		},
	}
}

// ToPushData 转换为推送数据格式 - 与02_generate_data格式一致
func (d *DeviceData) ToPushData() *PushData {
	// 生成数据ID（使用时间戳+设备ID的简单方式）
	dataID := fmt.Sprintf("%s_%d", d.DeviceID, time.Now().UnixNano())

	// 构建状态数据
	statusData := make(map[string]interface{})
	statusData["emergency_stop"] = 0
	statusData["run_status"] = 2
	statusData["collect_count"] = d.Status.CollectCount
	statusData["error_count"] = d.Status.ErrorCount
	if d.Status.LastError != "" {
		statusData["last_error"] = d.Status.LastError
	}

	// 从原始数据中提取或设置默认值
	quantity := 0
	currentProgram := "UNKNOWN"
	mainProgram := "UNKNOWN"
	actualFeedrate := 0
	actualSpindleSpeed := 0

	// 从RawData中提取实际值 - 使用采集器实际使用的字段名

	// 工件计数：优先使用workpiece_count，其次quantity
	if val, ok := d.RawData["workpiece_count"].(int); ok && val >= 0 {
		quantity = val
	} else if val, ok := d.RawData["quantity"].(int); ok && val >= 0 {
		quantity = val
	}

	// 当前程序号：优先使用current_program_number，其次current_program
	// 程序号格式化为4位数字，不足4位时前面补0
	if val, ok := d.RawData["current_program_number"].(int); ok && val > 0 {
		currentProgram = fmt.Sprintf("%04d", val) // 格式化为4位数字，前面补0
	} else if val, ok := d.RawData["current_program"].(string); ok && val != "" {
		// 如果是字符串类型，尝试转换为数字后格式化
		if num, err := strconv.Atoi(val); err == nil && num > 0 {
			currentProgram = fmt.Sprintf("%04d", num) // 格式化为4位数字，前面补0
		} else {
			// 如果转换失败，保持原字符串（可能包含字母等）
			currentProgram = val
		}
	}

	// 主程序号：优先使用main_program_number，其次main_program
	// 程序号格式化为4位数字，不足4位时前面补0
	if val, ok := d.RawData["main_program_number"].(int); ok && val > 0 {
		mainProgram = fmt.Sprintf("%04d", val) // 格式化为4位数字，前面补0
	} else if val, ok := d.RawData["main_program"].(string); ok && val != "" {
		// 如果是字符串类型，尝试转换为数字后格式化
		if num, err := strconv.Atoi(val); err == nil && num > 0 {
			mainProgram = fmt.Sprintf("%04d", num) // 格式化为4位数字，前面补0
		} else {
			// 如果转换失败，保持原字符串（可能包含字母等）
			mainProgram = val
		}
	} else {
		// 如果没有主程序号，使用当前程序号
		mainProgram = currentProgram
	}

	// 实际进给速度
	if val, ok := d.RawData["actual_feedrate"].(int); ok {
		actualFeedrate = val
	}

	// 实际主轴转速
	if val, ok := d.RawData["actual_spindle_speed"].(int); ok {
		actualSpindleSpeed = val
	}

	// 确定设备状态 - 使用采集器实际使用的字段名
	status := "idle"
	if d.Status.Connected {
		// 优先使用run_status_description字段（采集器实际使用的字段）
		if val, ok := d.RawData["run_status_description"].(string); ok && val != "" {
			status = val
		} else if val, ok := d.RawData["status"].(string); ok && val != "" {
			status = val
		}
		// 如果状态为空或无效，根据run_status数值判断
		if status == "idle" || status == "" {
			if runStatus, ok := d.RawData["run_status"].(int); ok {
				switch runStatus {
				case 0:
					status = "idle" // 停止
				case 1:
					status = "idle" // 停止
				case 2:
					status = "production" // 运行
				case 3:
					status = "production" // 运行
				case 4:
					status = "fault" // 报警
				default:
					status = "idle"
				}
			}
		}
	} else {
		status = "shutdown"
	}

	// 构建原始数据结构
	rawData := RawDataStruct{
		DataID:             dataID,
		Connected:          d.Status.Connected,
		Status:             status,
		StatusData:         statusData,
		Quantity:           quantity,
		CurrentProgram:     currentProgram,
		MainProgram:        mainProgram,
		ActualFeedrate:     actualFeedrate,
		ActualSpindleSpeed: actualSpindleSpeed,
	}

	// 构建元数据结构 - 使用从API获取的真实设备信息
	metadata := MetadataStruct{
		Location:        d.Location,
		Version:         "2.0.0",
		DeviceType:      d.DataType,
		Brand:           "FANUC", // 默认品牌
		Model:           "CNC",   // 默认型号
		IPAddress:       "",
		Port:            8193, // 默认端口
		CollectInterval: 3000, // 默认采集间隔
	}

	// 从API配置中获取真实的设备信息
	if d.DeviceConfig != nil {
		if d.DeviceConfig.Brand != "" {
			metadata.Brand = d.DeviceConfig.Brand
		}
		if d.DeviceConfig.Model != "" {
			metadata.Model = d.DeviceConfig.Model
		}
		if d.DeviceConfig.IP != "" {
			metadata.IPAddress = d.DeviceConfig.IP
		}
		if d.DeviceConfig.Port > 0 {
			metadata.Port = d.DeviceConfig.Port
		}
		if d.DeviceConfig.CollectInterval > 0 {
			metadata.CollectInterval = d.DeviceConfig.CollectInterval
		}
		// 使用API配置中的位置信息（如果存在）
		if d.DeviceConfig.Location != "" {
			metadata.Location = d.DeviceConfig.Location
		}
		// 使用API配置中的数据类型（如果存在）
		if d.DeviceConfig.DataType != "" {
			metadata.DeviceType = d.DeviceConfig.DataType
		}
	}

	// 如果没有API配置，尝试从设备名称中提取IP地址
	if metadata.IPAddress == "" && d.DeviceName != "" {
		metadata.IPAddress = d.DeviceName
	}

	return &PushData{
		DeviceID:  d.DeviceID,
		Timestamp: d.Timestamp,
		DataType:  d.DataType,
		RawData:   rawData,
		Metadata:  metadata,
	}
}

// IsValid 验证设备数据
func (d *DeviceData) IsValid() bool {
	return d.DeviceID != "" && !d.Timestamp.IsZero()
}

// GetAge 获取数据年龄
func (d *DeviceData) GetAge() time.Duration {
	return time.Since(d.Timestamp)
}

// CacheItem 缓存项
type CacheItem struct {
	ID         string      `json:"id"`
	DeviceID   string      `json:"device_id"`
	Data       *DeviceData `json:"data"`
	Timestamp  time.Time   `json:"timestamp"`
	RetryCount int         `json:"retry_count"`
	LastError  string      `json:"last_error,omitempty"`
}

// DeviceEvent 设备事件
type DeviceEvent struct {
	Type      string                 `json:"type"` // "connected", "disconnected", "error", "data"
	DeviceID  string                 `json:"device_id"`
	Timestamp time.Time              `json:"timestamp"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data,omitempty"`
}

// CollectionResult 采集结果
type CollectionResult struct {
	DeviceID  string        `json:"device_id"`
	Success   bool          `json:"success"`
	Data      *DeviceData   `json:"data,omitempty"`
	Error     string        `json:"error,omitempty"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
}

// PushResult 推送结果
type PushResult struct {
	DeviceID  string        `json:"device_id"`
	Success   bool          `json:"success"`
	Error     string        `json:"error,omitempty"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
	DataCount int           `json:"data_count"`
}

// DeviceMetrics 设备指标
type DeviceMetrics struct {
	DeviceID        string        `json:"device_id"`
	DeviceName      string        `json:"device_name"`
	Status          DeviceStatus  `json:"status"`
	CollectInterval time.Duration `json:"collect_interval"`
	LastCollectTime time.Time     `json:"last_collect_time"`
	LastPushTime    time.Time     `json:"last_push_time"`
	AvgCollectTime  time.Duration `json:"avg_collect_time"`
	AvgPushTime     time.Duration `json:"avg_push_time"`
	SuccessRate     float64       `json:"success_rate"`
	ErrorRate       float64       `json:"error_rate"`
	DataThroughput  float64       `json:"data_throughput"` // 数据/秒
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	TotalDevices     int                      `json:"total_devices"`
	ActiveDevices    int                      `json:"active_devices"`
	ConnectedDevices int                      `json:"connected_devices"`
	TotalCollections int64                    `json:"total_collections"`
	TotalPushes      int64                    `json:"total_pushes"`
	TotalErrors      int64                    `json:"total_errors"`
	CacheSize        int                      `json:"cache_size"`
	MemoryUsageMB    float64                  `json:"memory_usage_mb"`
	CPUUsage         float64                  `json:"cpu_usage"`
	Uptime           time.Duration            `json:"uptime"`
	DeviceMetrics    map[string]DeviceMetrics `json:"device_metrics"`
}

// APIResponse 通用API响应
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// DeviceControlRequest 设备控制请求
type DeviceControlRequest struct {
	Action    string   `json:"action"` // "start", "stop", "restart"
	DeviceID  string   `json:"device_id,omitempty"`
	DeviceIDs []string `json:"device_ids,omitempty"`
}

// DeviceConfigRequest 设备配置请求
type DeviceConfigRequest struct {
	ConfigSource string `json:"config_source,omitempty"` // "file", "api"
	ConfigAPI    string `json:"config_api,omitempty"`
	AutoStart    *bool  `json:"auto_start,omitempty"`
}

// PushConfigRequest 推送配置请求
type PushConfigRequest struct {
	Type      string                 `json:"type"` // "restful", "mqtt", "nats"
	Config    map[string]interface{} `json:"config"`
	Enabled   *bool                  `json:"enabled,omitempty"`
	BatchSize *int                   `json:"batch_size,omitempty"`
	Timeout   *int                   `json:"timeout,omitempty"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status     string                 `json:"status"` // "healthy", "degraded", "unhealthy"
	Timestamp  time.Time              `json:"timestamp"`
	Checks     map[string]CheckResult `json:"checks"`
	SystemInfo SystemMetrics          `json:"system_info"`
}

// CheckResult 检查结果
type CheckResult struct {
	Status    string        `json:"status"` // "pass", "warn", "fail"
	Message   string        `json:"message"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
}
