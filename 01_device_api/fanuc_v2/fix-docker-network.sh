#!/bin/bash

# FANUC Docker网络连接修复脚本
#
# 功能说明：
# 解决Docker容器内无法访问宿主机API服务的问题
# 提供多种解决方案供用户选择
#
# 问题原因：
# Docker容器内的localhost指向容器本身，无法访问宿主机的API服务
#
# 解决方案：
# 1. 使用host网络模式（推荐）
# 2. 修改配置文件API地址
# 3. 使用Docker的host.docker.internal
#
# <AUTHOR>
# @version 1.0.0
# @since 2025-07-02

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示问题说明
show_problem_description() {
    echo "=================================================="
    echo "🔧 FANUC Docker网络连接问题修复"
    echo "=================================================="
    echo ""
    echo "📋 问题描述："
    echo "   Docker容器内无法访问宿主机的API服务"
    echo "   - 设备配置API: http://localhost:9005/api/public/devices/configs"
    echo "   - 数据推送API: http://localhost:9001/api/v1/device/data"
    echo ""
    echo "🔍 问题原因："
    echo "   Docker容器内的localhost指向容器本身，不是宿主机"
    echo ""
    echo "💡 解决方案："
    echo "   1. 使用host网络模式（推荐）"
    echo "   2. 修改配置文件API地址"
    echo "   3. 使用Docker的host.docker.internal"
    echo ""
}

# 方案1：修改build-x86.sh使用host网络
fix_with_host_network() {
    log_info "方案1：修改Docker运行脚本使用host网络模式"
    
    # 备份原文件
    if [ ! -f "build-x86.sh.backup" ]; then
        cp build-x86.sh build-x86.sh.backup
        log_info "已备份原文件: build-x86.sh.backup"
    fi
    
    # 修改Docker运行命令，添加--network host
    sed -i.tmp 's/docker run -it --rm \\/docker run -it --rm --network host \\/' build-x86.sh
    
    # 移除端口映射（host网络模式下不需要）
    sed -i.tmp '/^[[:space:]]*-p 9010:9010 \\/d' build-x86.sh
    
    # 清理临时文件
    rm -f build-x86.sh.tmp
    
    log_success "✅ 已修改build-x86.sh使用host网络模式"
    log_info "现在容器内可以直接访问宿主机的localhost服务"
}

# 方案2：创建Docker配置文件
create_docker_config() {
    log_info "方案2：创建Docker专用配置文件"
    
    # 检测宿主机IP
    HOST_IP=$(ip route get 1 | awk '{print $7; exit}' 2>/dev/null || echo "host.docker.internal")
    
    # 创建Docker专用配置
    cat > config-docker.json << EOF
{
  "server": {
    "host": "0.0.0.0",
    "port": 9110,
    "read_timeout": 30,
    "write_timeout": 30
  },
  "devices": {
    "config_source": "api",
    "config_file": "devices.json",
    "config_api": "http://${HOST_IP}:9005/api/public/devices/configs",
    "auto_start": false,
    "refresh_interval": 60
  },
  "data_push": {
    "type": "restful",
    "enabled": true,
    "batch_size": 10,
    "timeout": 30,
    "config": {
      "url": "http://${HOST_IP}:9001/api/v1/device/data",
      "method": "POST",
      "headers": {
        "Content-Type": "application/json",
        "User-Agent": "FANUC-Collector-v2/2.0.0"
      },
      "username": "",
      "password": "",
      "timeout": 30
    }
  },
  "cache": {
    "type": "memory",
    "max_size": 10000,
    "max_age": 3600,
    "flush_interval": 60,
    "storage_path": "./cache"
  },
  "logger": {
    "level": "info",
    "format": "text",
    "output": "logs/fanuc_v2.log",
    "rotation": {
      "enabled": true,
      "max_size": 10,
      "max_age": 3,
      "max_backups": 50,
      "compress": true
    }
  },
  "monitoring": {
    "health_check_interval": 30,
    "metrics_enabled": true,
    "max_memory_mb": 512
  }
}
EOF
    
    log_success "✅ 已创建Docker专用配置: config-docker.json"
    log_info "宿主机IP: $HOST_IP"
    log_info "使用方法: go run ./cmd/main.go -config config-docker.json"
}

# 方案3：修改build-x86.sh添加extra_hosts
fix_with_extra_hosts() {
    log_info "方案3：修改Docker运行脚本添加host映射"
    
    # 备份原文件
    if [ ! -f "build-x86.sh.backup" ]; then
        cp build-x86.sh build-x86.sh.backup
        log_info "已备份原文件: build-x86.sh.backup"
    fi
    
    # 添加--add-host参数
    sed -i.tmp '/--hostname fanuc-dev-x86 \\/a\
        --add-host host.docker.internal:host-gateway \\' build-x86.sh
    
    # 清理临时文件
    rm -f build-x86.sh.tmp
    
    # 创建使用host.docker.internal的配置文件
    cat > config-host-internal.json << EOF
{
  "server": {
    "host": "0.0.0.0",
    "port": 9110,
    "read_timeout": 30,
    "write_timeout": 30
  },
  "devices": {
    "config_source": "api",
    "config_file": "devices.json",
    "config_api": "http://host.docker.internal:9005/api/public/devices/configs",
    "auto_start": false,
    "refresh_interval": 60
  },
  "data_push": {
    "type": "restful",
    "enabled": true,
    "batch_size": 10,
    "timeout": 30,
    "config": {
      "url": "http://host.docker.internal:9001/api/v1/device/data",
      "method": "POST",
      "headers": {
        "Content-Type": "application/json",
        "User-Agent": "FANUC-Collector-v2/2.0.0"
      },
      "username": "",
      "password": "",
      "timeout": 30
    }
  },
  "cache": {
    "type": "memory",
    "max_size": 10000,
    "max_age": 3600,
    "flush_interval": 60,
    "storage_path": "./cache"
  },
  "logger": {
    "level": "info",
    "format": "text",
    "output": "logs/fanuc_v2.log",
    "rotation": {
      "enabled": true,
      "max_size": 10,
      "max_age": 3,
      "max_backups": 50,
      "compress": true
    }
  },
  "monitoring": {
    "health_check_interval": 30,
    "metrics_enabled": true,
    "max_memory_mb": 512
  }
}
EOF
    
    log_success "✅ 已修改build-x86.sh添加host映射"
    log_success "✅ 已创建配置文件: config-host-internal.json"
    log_info "使用方法: go run ./cmd/main.go -config config-host-internal.json"
}

# 检查API服务状态
check_api_services() {
    log_info "检查宿主机API服务状态..."
    
    # 检查12_server_api (9005端口)
    if curl -s --connect-timeout 3 http://localhost:9005/api/public/devices/configs > /dev/null 2>&1; then
        log_success "✅ 设备配置API服务正常 (localhost:9005)"
    else
        log_warning "⚠️  设备配置API服务不可用 (localhost:9005)"
        log_info "   启动命令: cd ../../12_server_api && ./run.sh"
    fi
    
    # 检查数据采集API (9001端口)
    if curl -s --connect-timeout 3 http://localhost:9001/health > /dev/null 2>&1; then
        log_success "✅ 数据采集API服务正常 (localhost:9001)"
    else
        log_warning "⚠️  数据采集API服务不可用 (localhost:9001)"
        log_info "   可能需要启动相应的数据采集服务"
    fi
}

# 显示使用说明
show_usage_instructions() {
    echo ""
    echo "=================================================="
    echo "📖 使用说明"
    echo "=================================================="
    echo ""
    echo "🚀 方案1（推荐）- Host网络模式："
    echo "   ./build-x86.sh run"
    echo "   go run ./cmd/main.go"
    echo ""
    echo "🚀 方案2 - Docker专用配置："
    echo "   ./build-x86.sh run"
    echo "   go run ./cmd/main.go -config config-docker.json"
    echo ""
    echo "🚀 方案3 - Host映射："
    echo "   ./build-x86.sh run"
    echo "   go run ./cmd/main.go -config config-host-internal.json"
    echo ""
    echo "🔍 测试连接："
    echo "   curl http://localhost:9005/api/public/devices/configs"
    echo "   curl http://host.docker.internal:9005/api/public/devices/configs"
    echo ""
    echo "📝 恢复原始配置："
    echo "   cp build-x86.sh.backup build-x86.sh"
    echo ""
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择修复方案："
    echo "1) 使用host网络模式（推荐，最简单）"
    echo "2) 创建Docker专用配置文件"
    echo "3) 使用host.docker.internal映射"
    echo "4) 检查API服务状态"
    echo "5) 显示使用说明"
    echo "6) 退出"
    echo ""
    read -p "请输入选项 (1-6): " choice
    
    case $choice in
        1)
            fix_with_host_network
            show_usage_instructions
            ;;
        2)
            create_docker_config
            show_usage_instructions
            ;;
        3)
            fix_with_extra_hosts
            show_usage_instructions
            ;;
        4)
            check_api_services
            show_menu
            ;;
        5)
            show_usage_instructions
            show_menu
            ;;
        6)
            log_info "退出修复脚本"
            exit 0
            ;;
        *)
            log_error "无效选项，请重新选择"
            show_menu
            ;;
    esac
}

# 主函数
main() {
    show_problem_description
    check_api_services
    show_menu
}

# 执行主函数
main
