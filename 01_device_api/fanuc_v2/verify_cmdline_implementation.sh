#!/bin/bash

# FANUC v2 命令行参数实现验证脚本
# 
# 功能：验证main.go中是否正确实现了命令行参数功能
# 
# 验证内容：
# 1. 检查是否导入了flag包
# 2. 检查是否定义了命令行参数变量
# 3. 检查是否实现了参数解析函数
# 4. 检查是否在main函数中调用了参数解析
# 5. 检查是否使用了参数变量替代常量
#
# 使用方法：
# ./verify_cmdline_implementation.sh

echo "=========================================="
echo "FANUC v2 命令行参数实现验证"
echo "=========================================="

# 检查main.go文件是否存在
if [ ! -f "main.go" ]; then
    echo "❌ main.go文件不存在"
    exit 1
fi

echo "✅ main.go文件存在"
echo ""

# 1. 检查flag包导入
echo "1. 检查flag包导入..."
if grep -q '"flag"' main.go; then
    echo "✅ 已导入flag包"
else
    echo "❌ 未导入flag包"
fi

# 2. 检查命令行参数变量定义
echo ""
echo "2. 检查命令行参数变量定义..."
if grep -q "var.*cncIP.*string" main.go; then
    echo "✅ 已定义cncIP变量"
else
    echo "❌ 未定义cncIP变量"
fi

if grep -q "var.*cncPort.*int" main.go; then
    echo "✅ 已定义cncPort变量"
else
    echo "❌ 未定义cncPort变量"
fi

# 3. 检查参数解析函数
echo ""
echo "3. 检查参数解析函数..."
if grep -q "func parseCommandLineArgs" main.go; then
    echo "✅ 已定义parseCommandLineArgs函数"
else
    echo "❌ 未定义parseCommandLineArgs函数"
fi

if grep -q "flag.StringVar.*cncIP" main.go; then
    echo "✅ 已配置IP参数解析"
else
    echo "❌ 未配置IP参数解析"
fi

if grep -q "flag.IntVar.*cncPort" main.go; then
    echo "✅ 已配置端口参数解析"
else
    echo "❌ 未配置端口参数解析"
fi

if grep -q "flag.Parse()" main.go; then
    echo "✅ 已调用flag.Parse()"
else
    echo "❌ 未调用flag.Parse()"
fi

# 4. 检查main函数中的调用
echo ""
echo "4. 检查main函数中的参数解析调用..."
if grep -q "parseCommandLineArgs()" main.go; then
    echo "✅ main函数中已调用parseCommandLineArgs()"
else
    echo "❌ main函数中未调用parseCommandLineArgs()"
fi

# 5. 检查是否使用了参数变量
echo ""
echo "5. 检查参数变量的使用..."
if grep -q "cncIP" main.go && grep -q "cncPort" main.go; then
    echo "✅ 已使用cncIP和cncPort变量"
    
    # 检查是否还有旧的常量使用
    if grep -q "CNC_IP" main.go || grep -q "CNC_PORT" main.go; then
        echo "⚠️  警告: 代码中仍有CNC_IP或CNC_PORT常量的使用"
        echo "   建议检查以下位置:"
        grep -n "CNC_IP\|CNC_PORT" main.go | head -5
    else
        echo "✅ 已完全替换为变量使用"
    fi
else
    echo "❌ 未使用cncIP和cncPort变量"
fi

# 6. 检查帮助信息实现
echo ""
echo "6. 检查帮助信息实现..."
if grep -q "flag.Usage" main.go; then
    echo "✅ 已自定义帮助信息"
else
    echo "❌ 未自定义帮助信息"
fi

# 7. 显示参数相关的代码片段
echo ""
echo "7. 显示关键代码片段..."
echo ""
echo "参数变量定义:"
echo "----------------------------------------"
grep -A 5 -B 2 "var.*cncIP\|var.*cncPort" main.go | head -10

echo ""
echo "参数解析函数:"
echo "----------------------------------------"
grep -A 10 "func parseCommandLineArgs" main.go | head -15

echo ""
echo "main函数中的调用:"
echo "----------------------------------------"
grep -A 3 -B 1 "parseCommandLineArgs()" main.go

echo ""
echo "参数使用示例:"
echo "----------------------------------------"
grep -n "cncIP\|cncPort" main.go | head -5

echo ""
echo "=========================================="
echo "命令行参数实现验证完成"
echo "=========================================="

# 8. 提供使用建议
echo ""
echo "💡 使用建议："
echo "   1. 编译程序: go build -o fanuc_v2 main.go"
echo "   2. 查看帮助: ./fanuc_v2 -h"
echo "   3. 使用默认参数: ./fanuc_v2"
echo "   4. 指定IP: ./fanuc_v2 -ip *************"
echo "   5. 指定端口: ./fanuc_v2 -port 8194"
echo "   6. 完整参数: ./fanuc_v2 -ip ************* -port 8194"

echo ""
echo "📝 注意事项："
echo "   - 确保目标设备IP地址可访问"
echo "   - FANUC标准端口通常为8193"
echo "   - 程序会将参数信息记录到日志文件中"
