# FANUC Data Collector v2 文档中心

## 📚 **文档目录**

### **核心文档**
- [编译运行指南.md](编译运行指南.md) - 完整的编译、运行、停止指南

### **快速脚本**
项目根目录下提供了以下快速脚本：

| 脚本 | 功能 | 用法 |
|------|------|------|
|  | 一键编译和启动 |  |
|  | 停止应用 |  |
|  | 检查运行状态 |  |

## 🚀 **快速开始**

### **1. 启动应用**
```bash
cd /app/src
./start_fanuc.sh
```

### **2. 检查状态**
```bash
./status_fanuc.sh
```

### **3. 停止应用**
```bash
./stop_fanuc.sh
```

## 📋 **手动操作**

### **编译**
```bash
cd /app/src
export CGO_ENABLED=1
go build -o build/fanuc-collector-v2 ./cmd/main.go
```

### **运行**
```bash
# 前台运行
./build/fanuc-collector-v2 -config config.json

# 后台运行
./build/fanuc-collector-v2 -config config.json > app.log 2>&1 &
```

### **停止**
```bash
pkill -f fanuc-collector
```

## 🔧 **配置文件**

- **config.json** - 系统配置（服务器、推送、缓存等）
- **devices.json** - 设备配置（设备列表和连接参数）

## 🧪 **测试**

### **API测试**
```bash
# 健康检查
curl http://localhost:9005/api/v1/system/health

# 启动设备
curl -X POST http://localhost:9005/api/v1/devices/fanuc_001/start

# 测试推送
curl -X POST http://localhost:9005/api/v1/pusher/test
```

### **数据格式测试**
```bash
./test_data_format_enhanced.sh
```

## 📊 **监控**

### **查看日志**
```bash
# 实时日志
tail -f app.log

# 搜索错误
grep -i error app.log

# 搜索连接日志
grep "设备连接" app.log
```

### **系统资源**
```bash
# 查看进程
ps aux | grep fanuc-collector

# 查看端口
netstat -tlnp | grep :9005

# 查看资源使用
top -p $(pgrep fanuc-collector)
```

## 🔍 **故障排除**

### **常见问题**
1. **编译失败** - 检查CGO_ENABLED=1
2. **连接失败** - 检查设备IP和端口
3. **端口占用** - 使用lsof -i :9005查找占用进程
4. **配置错误** - 验证JSON格式

### **日志分析**
```bash
# 查看启动日志
head -50 app.log

# 查看错误日志
grep -i "error\|fail\|panic" app.log

# 查看连接状态
grep "连接\|connect" app.log
```

## 📞 **技术支持**

如果遇到问题：
1. 查看 [编译运行指南.md](编译运行指南.md) 中的故障排除部分
2. 检查应用日志文件
3. 运行  检查系统状态
4. 验证配置文件格式

---

**最后更新**: 2025-05-30  
**版本**: v2.0.0  
**项目**: FANUC Data Collector v2
