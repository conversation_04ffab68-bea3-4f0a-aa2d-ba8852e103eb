# FANUC Data Collector v2 独立连接模式说明

## 📋 **概述**

FANUC Data Collector v2 现在采用**独立连接模式**，每次数据采集都会：
1. 建立新的FOCAS连接
2. 执行数据采集
3. 完成后立即断开连接

这种模式确保了每次数据采集都是独立的会话，提高了系统的稳定性和可靠性。

## 🔄 **工作流程**

### **传统模式 vs 独立连接模式**

| 阶段 | 传统模式 | 独立连接模式 |
|------|----------|--------------|
| 启动 | 建立持久连接 | 直接启动采集循环 |
| 采集 | 使用已有连接 | 每次建立新连接 |
| 异常 | 连接可能断开 | 自动重新连接 |
| 停止 | 断开持久连接 | 自然停止 |

### **独立连接模式详细流程**

```
每次采集周期:
┌─────────────────────────────────────────────────────────────┐
│ 1. 开始独立连接采集                                          │
│    ├── 调用 establishConnection()                           │
│    ├── cnc_startupprocess() - 启动FOCAS进程                │
│    └── cnc_allclibhndl3() - 建立以太网连接                 │
│                                                             │
│ 2. 执行数据采集                                             │
│    ├── readCNCID() - 读取机器ID                            │
│    ├── readStatusInfo() - 读取状态信息                     │
│    ├── readDynamicData() - 读取动态数据                    │
│    └── readWorkpieceCount() - 读取工件计数                 │
│                                                             │
│ 3. 释放连接 (defer执行)                                     │
│    ├── 调用 releaseConnection()                            │
│    ├── cnc_freelibhndl() - 释放连接句柄                   │
│    └── updateConnectionStatus(false) - 更新状态           │
│                                                             │
│ 4. 等待下一个采集周期                                       │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ **核心方法说明**

### **performRealDataCollection()**
```go
// 每次采集都独立连接和断开
func (dc *DeviceCollector) performRealDataCollection() {
    // 1. 建立连接
    var libhndl C.ushort
    if err := dc.establishConnection(&libhndl); err != nil {
        return // 连接失败，等待下次重试
    }
    
    // 2. 确保连接释放
    defer func() {
        dc.releaseConnection(libhndl)
        dc.updateConnectionStatus(false)
    }()
    
    // 3. 执行数据采集
    // ... 采集逻辑
}
```

### **establishConnection()**
```go
// 建立独立的FOCAS连接
func (dc *DeviceCollector) establishConnection(libhndl *C.ushort) error {
    // 启动FOCAS进程
    ret := C.cnc_startupprocess(C.long(0), log_fname)
    
    // 建立以太网连接
    ret = C.cnc_allclibhndl3(ip, port, timeout, libhndl)
    
    return nil
}
```

### **releaseConnection()**
```go
// 释放FOCAS连接
func (dc *DeviceCollector) releaseConnection(libhndl C.ushort) {
    ret := C.cnc_freelibhndl(libhndl)
    // 记录释放结果
}
```

## ✅ **优势**

### **1. 稳定性提升**
- **连接隔离**: 每次采集都是独立会话，避免连接状态污染
- **自动恢复**: 连接失败不影响下次采集
- **异常隔离**: 单次采集异常不会影响整体系统

### **2. 资源管理**
- **及时释放**: 每次采集后立即释放连接资源
- **避免泄漏**: 使用defer确保连接必定释放
- **减少占用**: 不保持长期连接，减少系统资源占用

### **3. 网络友好**
- **防火墙友好**: 短连接更容易通过网络设备
- **负载均衡**: 适合多设备并发采集
- **网络波动**: 对网络不稳定更有容忍性

### **4. 调试便利**
- **清晰日志**: 每次连接都有完整的日志记录
- **状态明确**: 连接状态实时更新
- **问题定位**: 容易定位特定采集周期的问题

## 📊 **性能考虑**

### **连接开销**
- **建立时间**: 每次连接约50-100ms
- **释放时间**: 每次释放约10-20ms
- **总开销**: 每次采集增加约100-150ms

### **采集频率建议**
| 采集间隔 | 连接开销占比 | 建议 |
|----------|--------------|------|
| 1秒 | ~15% | ✅ 推荐 |
| 3秒 | ~5% | ✅ 最佳 |
| 5秒 | ~3% | ✅ 理想 |
| 10秒+ | <2% | ✅ 完美 |

### **优化建议**
- 采集间隔建议设置为3秒以上
- 避免过于频繁的采集（<1秒）
- 根据实际需求调整采集频率

## 🔧 **配置参数**

### **devices.json 配置**
```json
{
  "id": "fanuc_001",
  "collect_interval": 3000,  // 建议3秒以上
  "timeout": 15,             // 连接超时时间
  "retry_count": 3,          // 重试次数
  "retry_delay": 10          // 重试间隔
}
```

### **关键参数说明**
- **collect_interval**: 采集间隔（毫秒），建议3000ms以上
- **timeout**: 单次连接超时时间（秒）
- **retry_count**: 连接失败重试次数
- **retry_delay**: 重试间隔（秒）

## 📈 **监控指标**

### **连接指标**
- 连接成功率
- 平均连接时间
- 连接失败次数
- 释放成功率

### **采集指标**
- 数据采集成功率
- 平均采集时间
- 数据完整性
- 推送成功率

### **日志示例**
```
[fanuc_001] 开始独立连接采集
[fanuc_001] 开始建立FOCAS连接
[fanuc_001] cnc_startupprocess返回值: 0
[fanuc_001] cnc_allclibhndl3返回值: 0, 耗时: 67ms, 句柄: 32769
[fanuc_001] 连接建立成功，句柄: 32769
[fanuc_001] 机器ID: 3c7b5d01-5f814293-be272789-6362c43d
[fanuc_001] 数据采集成功: fanuc_001
[fanuc_001] 数据已发送到处理通道
[fanuc_001] 释放FOCAS连接，句柄: 32769
[fanuc_001] cnc_freelibhndl返回值: 0
[fanuc_001] 连接已释放
```

## 🚀 **使用方法**

### **启动采集**
```bash
# 启动应用
./build/fanuc-collector-v2 -config config.json

# 启动设备采集
curl -X POST http://localhost:9005/api/v1/devices/fanuc_001/start
```

### **监控状态**
```bash
# 查看实时日志
tail -f app.log | grep "独立连接\|连接建立\|连接已释放"

# 检查采集状态
curl http://localhost:9005/api/v1/devices/fanuc_001
```

### **性能调优**
```bash
# 调整采集间隔（推荐3秒）
# 编辑 devices.json
"collect_interval": 3000

# 调整连接超时
"timeout": 15
```

## 📞 **故障排除**

### **常见问题**
1. **连接频繁失败** - 检查网络连接和设备状态
2. **采集间隔过短** - 增加collect_interval到3秒以上
3. **资源占用高** - 检查是否有连接泄漏

### **调试方法**
```bash
# 查看连接日志
grep "建立FOCAS连接\|释放FOCAS连接" app.log

# 查看采集成功率
grep "数据采集成功\|数据采集部分失败" app.log | wc -l

# 查看连接时间统计
grep "cnc_allclibhndl3.*耗时" app.log
```

---

**最后更新**: 2025-05-30  
**版本**: v2.0.0  
**模式**: 独立连接模式
