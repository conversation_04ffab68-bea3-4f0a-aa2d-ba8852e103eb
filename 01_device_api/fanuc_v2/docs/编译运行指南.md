# FANUC Data Collector v2 编译运行指南

## 📋 **目录**
- [环境要求](#环境要求)
- [停止应用](#停止应用)
- [手动编译](#手动编译)
- [运行应用](#运行应用)
- [验证和测试](#验证和测试)
- [故障排除](#故障排除)
- [快速脚本](#快速脚本)

---

## 🔧 **环境要求**

### **系统环境**
- **操作系统**: Ubuntu 16.04 x86 (Docker容器)
- **Go版本**: 1.19+ (当前: go1.23.9 linux/386)
- **CGO**: 必须启用 (CGO_ENABLED=1)
- **FOCAS库**: libfwlib32-linux-x86.so.1.0.5

### **依赖检查**
```bash
# 检查Go环境
go version

# 检查CGO支持
go env CGO_ENABLED

# 检查FOCAS库
ls -la fwlib/libfwlib32*.so*
```

---

## 🛑 **停止应用**

### **方法1: 优雅停止**
```bash
# 停止所有FANUC相关进程
pkill -TERM fanuc-collector

# 或者停止特定版本
pkill -TERM fanuc-collector-v2
pkill -TERM fanuc-collector-v2-enhanced
pkill -TERM fanuc-collector-v2-debug
```

### **方法2: 强制停止**
```bash
# 强制终止所有FANUC进程
pkill -9 fanuc-collector

# 或者使用kill命令
kill -9 <PID>
```

### **方法3: 停止特定进程**
```bash
# 查找进程ID
ps aux | grep fanuc-collector

# 停止特定PID
kill <PID>

# 批量停止
ps aux | grep fanuc-collector | awk '{print $2}' | xargs kill
```

### **验证停止状态**
```bash
# 检查进程是否停止
ps aux | grep fanuc-collector | grep -v grep || echo "✅ 所有进程已停止"

# 检查端口是否释放
netstat -tlnp | grep :9005 || echo "✅ 端口9005已释放"
```

---

## 🔧 **手动编译**

### **步骤1: 进入项目目录**
```bash
# 进入Docker容器
docker exec -it fanuc-focas-dev-x86 bash

# 导航到项目目录
cd /app/src
```

### **步骤2: 环境准备**
```bash
# 启用CGO（必需）
export CGO_ENABLED=1

# 检查Go模块
go mod tidy

# 验证依赖
go mod verify
```

### **步骤3: 编译选项**

#### **基础编译（推荐）**
```bash
# 编译标准版本
go build -o build/fanuc-collector-v2 ./cmd/main.go
```

#### **调试版本**
```bash
# 编译带调试信息的版本
go build -gcflags="all=-N -l" -o build/fanuc-collector-v2-debug ./cmd/main.go
```

#### **生产版本**
```bash
# 编译优化的生产版本（去除调试信息，减小文件大小）
go build -ldflags="-s -w" -o build/fanuc-collector-v2-prod ./cmd/main.go
```

#### **竞态检测版本**
```bash
# 编译带竞态检测的版本（用于开发测试）
go build -race -o build/fanuc-collector-v2-race ./cmd/main.go
```

### **步骤4: 验证编译结果**
```bash
# 查看编译的二进制文件
ls -la build/

# 检查文件类型和架构
file build/fanuc-collector-v2

# 检查文件大小
du -h build/fanuc-collector-v2
```

### **编译选项说明**

| 选项 | 说明 | 用途 |
|------|------|------|
| `CGO_ENABLED=1` | 启用CGO | 必需（FOCAS库依赖） |
| `-o <filename>` | 指定输出文件名 | 自定义二进制文件名 |
| `-ldflags="-s -w"` | 去除调试信息 | 减小文件大小 |
| `-gcflags="all=-N -l"` | 禁用优化 | 便于调试 |
| `-race` | 启用竞态检测 | 检测并发问题 |
| `-v` | 显示详细编译信息 | 调试编译问题 |

---

## 🚀 **运行应用**

### **配置文件准备**

#### **检查配置文件**
```bash
# 查看主配置文件
cat config.json

# 查看设备配置文件
cat devices.json

# 验证JSON格式
python -m json.tool config.json
python -m json.tool devices.json
```

#### **配置文件说明**
- **config.json**: 系统配置（服务器、推送、缓存、监控）
- **devices.json**: 设备配置（设备列表和连接参数）

### **运行方式**

#### **方式1: 前台运行（推荐用于调试）**
```bash
# 直接运行，可以看到实时日志
./build/fanuc-collector-v2 -config config.json

# 带详细日志
./build/fanuc-collector-v2 -config config.json -log-level debug
```

#### **方式2: 后台运行**
```bash
# 后台运行并保存日志
./build/fanuc-collector-v2 -config config.json > app.log 2>&1 &

# 获取进程ID
echo $! > fanuc.pid

# 查看实时日志
tail -f app.log
```

#### **方式3: 使用nohup运行**
```bash
# 使用nohup确保进程不会因终端关闭而停止
nohup ./build/fanuc-collector-v2 -config config.json > app.log 2>&1 &

# 查看进程
ps aux | grep fanuc-collector
```

#### **方式4: 开发模式运行**
```bash
# 直接运行Go代码（不编译二进制）
go run ./cmd/main.go -config config.json

# 带实时重载（需要安装air工具）
# go install github.com/cosmtrek/air@latest
# air
```

### **命令行参数**

| 参数 | 说明 | 示例 |
|------|------|------|
| `-config` | 指定配置文件路径 | `-config config.json` |
| `-log-level` | 设置日志级别 | `-log-level debug` |
| `--help` | 显示帮助信息 | `--help` |
| `--version` | 显示版本信息 | `--version` |

---

## ✅ **验证和测试**

### **检查运行状态**

#### **进程检查**
```bash
# 检查进程是否运行
ps aux | grep fanuc-collector

# 检查进程树
pstree -p | grep fanuc

# 检查进程资源使用
top -p $(pgrep fanuc-collector)
```

#### **网络检查**
```bash
# 检查端口监听
netstat -tlnp | grep :9005

# 检查网络连接
ss -tlnp | grep :9005

# 测试端口连通性
telnet localhost 9005
```

### **API测试**

#### **基础API测试**
```bash
# 健康检查
curl http://localhost:9005/api/v1/system/health

# 系统指标
curl http://localhost:9005/api/v1/system/metrics

# 设备列表
curl http://localhost:9005/api/v1/devices
```

#### **设备操作测试**
```bash
# 启动设备采集
curl -X POST http://localhost:9005/api/v1/devices/fanuc_001/start

# 停止设备采集
curl -X POST http://localhost:9005/api/v1/devices/fanuc_001/stop

# 查看设备状态
curl http://localhost:9005/api/v1/devices/fanuc_001
```

#### **推送器测试**
```bash
# 查看推送器配置
curl http://localhost:9005/api/v1/pusher/config

# 测试推送器
curl -X POST http://localhost:9005/api/v1/pusher/test

# 查看推送器指标
curl http://localhost:9005/api/v1/pusher/metrics
```

### **日志监控**
```bash
# 查看实时日志
tail -f app.log

# 查看最近的日志
tail -100 app.log

# 搜索错误日志
grep -i error app.log

# 搜索设备连接日志
grep "设备连接" app.log

# 搜索数据推送日志
grep "数据推送" app.log
```

---

## 🔧 **故障排除**

### **常见编译问题**

#### **CGO相关错误**
```bash
# 错误: CGO_ENABLED=0
export CGO_ENABLED=1

# 错误: 找不到头文件
ls -la fwlib/fwlib32.h

# 错误: 找不到库文件
ls -la fwlib/libfwlib32*.so*
```

#### **依赖问题**
```bash
# 清理模块缓存
go clean -modcache

# 重新下载依赖
go mod download

# 更新依赖
go get -u ./...
```

### **常见运行问题**

#### **端口占用**
```bash
# 查找占用端口的进程
lsof -i :9005

# 终止占用进程
kill $(lsof -t -i:9005)
```

#### **配置文件错误**
```bash
# 验证JSON格式
python -m json.tool config.json

# 检查文件权限
ls -la config.json devices.json

# 检查文件编码
file config.json devices.json
```

#### **FOCAS连接问题**
```bash
# 测试网络连通性
ping 192.168.19.112

# 测试端口连通性
telnet 192.168.19.112 8193

# 检查FOCAS库
ldd build/fanuc-collector-v2 | grep fwlib
```

### **性能问题**

#### **内存使用**
```bash
# 查看内存使用
ps aux | grep fanuc-collector | awk '{print $4, $6}'

# 使用pmap查看详细内存映射
pmap $(pgrep fanuc-collector)
```

#### **CPU使用**
```bash
# 查看CPU使用率
top -p $(pgrep fanuc-collector)

# 使用htop查看详细信息
htop -p $(pgrep fanuc-collector)
```

---

## 📜 **快速脚本**

### **一键编译运行脚本**
```bash
# 创建快速启动脚本
cat > start_fanuc.sh << 'SCRIPT_EOF'
#!/bin/bash

# FANUC Data Collector v2 快速启动脚本
set -e

PROJECT_DIR="/app/src"
BUILD_DIR="$PROJECT_DIR/build"
BINARY_NAME="fanuc-collector-v2"
CONFIG_FILE="config.json"
LOG_FILE="app.log"

cd $PROJECT_DIR

echo "🛑 停止现有进程..."
pkill -f $BINARY_NAME || echo "没有运行的进程"

echo "🔧 编译应用..."
export CGO_ENABLED=1
go build -o $BUILD_DIR/$BINARY_NAME ./cmd/main.go

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    echo "🚀 启动应用..."
    
    # 检查配置文件
    if [ ! -f $CONFIG_FILE ]; then
        echo "❌ 配置文件 $CONFIG_FILE 不存在"
        exit 1
    fi
    
    # 启动应用
    ./$BUILD_DIR/$BINARY_NAME -config $CONFIG_FILE > $LOG_FILE 2>&1 &
    
    # 保存PID
    echo $! > fanuc.pid
    
    echo "✅ 应用已启动，PID: $(cat fanuc.pid)"
    echo "📋 查看日志: tail -f $LOG_FILE"
    echo "🔍 检查状态: curl http://localhost:9005/api/v1/system/health"
    
    # 等待几秒钟检查启动状态
    sleep 3
    if ps -p $(cat fanuc.pid) > /dev/null; then
        echo "🎉 应用启动成功！"
    else
        echo "❌ 应用启动失败，请检查日志"
        tail -20 $LOG_FILE
    fi
else
    echo "❌ 编译失败"
    exit 1
fi
SCRIPT_EOF

chmod +x start_fanuc.sh
```

### **停止脚本**
```bash
# 创建停止脚本
cat > stop_fanuc.sh << 'SCRIPT_EOF'
#!/bin/bash

# FANUC Data Collector v2 停止脚本
echo "🛑 停止FANUC Data Collector v2..."

# 方法1: 使用PID文件
if [ -f fanuc.pid ]; then
    PID=$(cat fanuc.pid)
    if ps -p $PID > /dev/null; then
        echo "停止进程 PID: $PID"
        kill $PID
        sleep 2
        
        # 检查是否成功停止
        if ps -p $PID > /dev/null; then
            echo "强制停止进程 PID: $PID"
            kill -9 $PID
        fi
    fi
    rm -f fanuc.pid
fi

# 方法2: 停止所有相关进程
pkill -f fanuc-collector || echo "没有找到相关进程"

# 验证停止状态
if ps aux | grep fanuc-collector | grep -v grep > /dev/null; then
    echo "⚠️  仍有进程在运行:"
    ps aux | grep fanuc-collector | grep -v grep
else
    echo "✅ 所有进程已停止"
fi

# 检查端口释放
if netstat -tlnp | grep :9005 > /dev/null; then
    echo "⚠️  端口9005仍被占用"
else
    echo "✅ 端口9005已释放"
fi
SCRIPT_EOF

chmod +x stop_fanuc.sh
```

### **状态检查脚本**
```bash
# 创建状态检查脚本
cat > status_fanuc.sh << 'SCRIPT_EOF'
#!/bin/bash

# FANUC Data Collector v2 状态检查脚本
echo "📊 FANUC Data Collector v2 状态检查"
echo "===================================="

# 检查进程
echo ""
echo "🔍 进程状态:"
if ps aux | grep fanuc-collector | grep -v grep > /dev/null; then
    echo "✅ 进程正在运行:"
    ps aux | grep fanuc-collector | grep -v grep
else
    echo "❌ 没有运行的进程"
fi

# 检查端口
echo ""
echo "🌐 端口状态:"
if netstat -tlnp | grep :9005 > /dev/null; then
    echo "✅ 端口9005正在监听:"
    netstat -tlnp | grep :9005
else
    echo "❌ 端口9005未监听"
fi

# 检查API
echo ""
echo "🔗 API状态:"
if curl -s http://localhost:9005/api/v1/system/health > /dev/null; then
    echo "✅ API服务正常"
    curl -s http://localhost:9005/api/v1/system/health | head -c 100
    echo "..."
else
    echo "❌ API服务不可用"
fi

# 检查日志
echo ""
echo "📋 最新日志:"
if [ -f app.log ]; then
    echo "最后10行日志:"
    tail -10 app.log
else
    echo "❌ 日志文件不存在"
fi

echo ""
echo "📈 系统资源:"
if ps aux | grep fanuc-collector | grep -v grep > /dev/null; then
    PID=$(ps aux | grep fanuc-collector | grep -v grep | awk '{print $2}' | head -1)
    echo "CPU和内存使用:"
    ps aux | grep $PID | grep -v grep | awk '{print "CPU: " $3 "%, Memory: " $4 "%"}'
fi
SCRIPT_EOF

chmod +x status_fanuc.sh
```

### **使用快速脚本**
```bash
# 启动应用
./start_fanuc.sh

# 检查状态
./status_fanuc.sh

# 停止应用
./stop_fanuc.sh
```

---

## 📚 **相关文档**

- [数据格式说明](DATA_FORMAT.md)
- [部署指南](DEPLOYMENT.md)
- [API文档](API.md)
- [故障排除指南](TROUBLESHOOTING.md)

---

## 📞 **技术支持**

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 网络连接状态
3. FOCAS库是否正确安装
4. 配置文件格式是否正确

**最后更新**: 2025-05-30
**版本**: v2.0.0
**作者**: FANUC Team
