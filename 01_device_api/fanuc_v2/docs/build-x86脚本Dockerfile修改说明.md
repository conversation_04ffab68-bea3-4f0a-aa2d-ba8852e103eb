# build-x86.sh 脚本 Dockerfile 修改说明

## 📋 **修改概述**

已成功将 `build-x86.sh` 脚本所使用的 Dockerfile 文件从默认的 `Dockerfile` 改为 `Dockerfile-dev`，以便使用专门的开发环境配置。

## 🔧 **具体修改内容**

### **1. 修改构建参数显示**
**文件位置**: `build-x86.sh` 第129行

**修改前**:
```bash
echo "  Dockerfile: ./Dockerfile"
```

**修改后**:
```bash
echo "  Dockerfile: ./Dockerfile-dev"
```

### **2. 修改 Docker Buildx 构建命令**
**文件位置**: `build-x86.sh` 第134-139行

**修改前**:
```bash
docker buildx build \
    --platform $PLATFORM \
    --tag $IMAGE_NAME:$IMAGE_TAG \
    --load \
    .
```

**修改后**:
```bash
docker buildx build \
    --platform $PLATFORM \
    --tag $IMAGE_NAME:$IMAGE_TAG \
    --file ./Dockerfile-dev \
    --load \
    .
```

### **3. 修改标准 Docker 构建命令**
**文件位置**: `build-x86.sh` 第142-146行

**修改前**:
```bash
docker build \
    --platform $PLATFORM \
    --tag $IMAGE_NAME:$IMAGE_TAG \
    .
```

**修改后**:
```bash
docker build \
    --platform $PLATFORM \
    --tag $IMAGE_NAME:$IMAGE_TAG \
    --file ./Dockerfile-dev \
    .
```

### **4. 文件重命名**
**操作**: 将 `Dockerfile-dev.txt` 重命名为 `Dockerfile-dev`

**原因**: 确保脚本能够正确找到并使用开发环境的 Dockerfile 文件

## 📁 **文件结构**

修改后的文件结构：
```
01_device_api/fanuc_v2/
├── Dockerfile              # 原生产环境 Dockerfile
├── Dockerfile-dev          # 开发环境 Dockerfile (新使用)
├── build-x86.sh           # 构建脚本 (已修改)
└── ...
```

## 🎯 **修改效果**

### **修改前**
- `build-x86.sh` 使用默认的 `Dockerfile` 进行构建
- 可能使用生产环境配置

### **修改后**
- `build-x86.sh` 使用专门的 `Dockerfile-dev` 进行构建
- 使用开发环境专用配置
- 构建参数显示正确的 Dockerfile 文件名

## 🔍 **Dockerfile-dev 特性**

`Dockerfile-dev` 包含以下开发环境特性：

### **基础环境**
- **基础镜像**: `i386/ubuntu:16.04` (x86架构)
- **Go版本**: 1.23.9 (x86版本)
- **时区**: Asia/Shanghai

### **开发工具**
- 完整的编译工具链 (gcc, g++, make)
- 网络调试工具 (ping, telnet, net-tools)
- 开发辅助工具 (vim, nano, htop, tree)

### **FOCAS支持**
- 自动安装 FOCAS 库文件和头文件
- 配置动态链接器
- 创建 pkg-config 配置
- 执行 FOCAS 相关脚本

### **开发环境配置**
- 创建非root用户 (fanuc)
- 设置完整的Go开发环境
- 自动初始化Go模块
- 提供详细的启动信息和环境检查

## 🚀 **使用方法**

修改后，使用方式保持不变：

```bash
# 构建开发环境镜像
./build-x86.sh build

# 运行开发环境容器
./build-x86.sh run

# 部署开发环境
./build-x86.sh deploy
```

## ✅ **验证方法**

### **1. 检查构建参数**
运行构建命令时，应该看到：
```
[INFO] 构建参数:
  镜像名称: mdc_dcf:2.0.1-dev
  平台: linux/386
  Dockerfile: ./Dockerfile-dev
```

### **2. 检查Docker命令**
构建过程中应该使用 `--file ./Dockerfile-dev` 参数

### **3. 验证镜像内容**
进入容器后，应该看到开发环境的特性：
- Ubuntu 16.04 x86系统
- Go 1.23.9 开发环境
- FOCAS库和工具
- 开发辅助工具

## 📝 **注意事项**

1. **文件依赖**: 确保 `Dockerfile-dev` 文件存在且可读
2. **权限检查**: 确保脚本有执行权限
3. **Docker环境**: 确保Docker支持x86平台构建
4. **FOCAS文件**: 确保fwlib目录下有必要的FOCAS文件

## 🎉 **总结**

通过这次修改：
- ✅ `build-x86.sh` 现在使用专门的开发环境 Dockerfile
- ✅ 构建过程更加明确和可控
- ✅ 开发环境配置与生产环境分离
- ✅ 保持了脚本的向后兼容性
- ✅ 提供了更好的开发体验

修改已完成，可以正常使用开发环境进行FANUC v2项目的开发和测试。
