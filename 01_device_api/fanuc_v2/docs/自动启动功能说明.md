# FANUC Data Collector v2 自动启动功能说明

## 📋 **功能概述**

FANUC Data Collector v2 支持在程序启动后自动启动设备采集，无需手动调用API。支持两种自动启动模式：
1. **系统级全局自动启动** - 启动所有启用的设备
2. **设备级单独自动启动** - 仅启动配置了自动启动的设备

## 🔄 **工作原理**

### **启动流程**
```
程序启动
    ↓
设备管理器初始化
    ↓
加载设备配置
    ↓
handleAutoStart()
    ↓
┌─────────────────────┬─────────────────────┐
│  系统配置           │  设备配置           │
│  auto_start=true    │  auto_start=true    │
└─────────────────────┴─────────────────────┘
    ↓                         ↓
启动所有启用设备          启动指定设备
    ↓                         ↓
StartAllDevices()        StartCollection()
    ↓                         ↓
所有设备开始采集          指定设备开始采集
```

### **配置优先级**
1. **系统配置优先**: 如果 `config.json` 中 `auto_start=true`，启动所有启用设备
2. **设备配置生效**: 如果系统配置为 `false`，检查每个设备的 `auto_start` 设置

## 🛠️ **配置方法**

### **方法1: 系统级全局自动启动**

修改 `config.json`:
```json
{
  "devices": {
    "auto_start": true,
    "config_file": "devices.json"
  }
}
```

**效果**: 启动所有 `enabled=true` 的设备

### **方法2: 设备级单独自动启动**

保持 `config.json` 中 `auto_start=false`，修改 `devices.json`:
```json
[
  {
    "id": "fanuc_001",
    "name": "FANUC CNC Machine 001",
    "enabled": true,
    "auto_start": true,
    "ip": "**************",
    "port": 8193
  }
]
```

**效果**: 仅启动 `enabled=true` 且 `auto_start=true` 的设备

### **方法3: 混合配置**

可以为不同设备设置不同的自动启动策略：
```json
[
  {
    "id": "fanuc_001",
    "auto_start": true,    // 自动启动
    "enabled": true
  },
  {
    "id": "fanuc_002", 
    "auto_start": false,   // 手动启动
    "enabled": true
  }
]
```

## 📊 **实现详情**

### **handleAutoStart() 方法**
```go
func (dm *DeviceManager) handleAutoStart() error {
    // 系统级全局自动启动
    if dm.config.Devices.AutoStart {
        dm.publishEvent("system", "info", "系统配置了全局自动启动，启动所有设备", nil)
        return dm.StartAllDevices()
    }

    // 设备级单独自动启动
    var errors []string
    autoStartCount := 0

    for _, deviceConfig := range dm.config.Devices.Devices {
        if deviceConfig.Enabled && deviceConfig.AutoStart {
            if collector, exists := dm.collectors[deviceConfig.ID]; exists {
                if err := collector.StartCollection(dm.ctx); err != nil {
                    errors = append(errors, fmt.Sprintf("设备 %s: %v", deviceConfig.ID, err))
                } else {
                    autoStartCount++
                    dm.publishEvent(deviceConfig.ID, "started", 
                        fmt.Sprintf("设备 %s 自动启动成功", deviceConfig.Name), nil)
                }
            }
        }
    }

    if autoStartCount > 0 {
        dm.publishEvent("system", "info", 
            fmt.Sprintf("自动启动了 %d 个设备", autoStartCount), nil)
    }

    return nil
}
```

### **启动条件检查**
- **设备必须启用**: `enabled = true`
- **配置自动启动**: `auto_start = true`
- **采集器存在**: 设备采集器已创建

## 📋 **日志示例**

### **系统级自动启动日志**
```
[system] 系统配置了全局自动启动，启动所有设备
[fanuc_001] 设备采集已启动（每次采集独立连接）
[fanuc_001] 开始独立连接采集
[fanuc_001] 连接建立成功，句柄: 32769
[fanuc_001] 数据采集成功: fanuc_001
```

### **设备级自动启动日志**
```
[fanuc_001] 设备 FANUC CNC Machine 001 自动启动成功
[system] 自动启动了 1 个设备
[fanuc_001] 设备采集已启动（每次采集独立连接）
[fanuc_001] 开始独立连接采集
[fanuc_001] 连接建立成功，句柄: 32769
[fanuc_001] 数据采集成功: fanuc_001
```

### **自动启动失败日志**
```
[fanuc_001] 设备连接失败，将在后台重试: 连接设备失败: -16
[system] 部分设备自动启动失败: [设备 fanuc_001: 连接设备失败: -16]
```

## ✅ **功能优势**

### **1. 便利性**
- **无需手动操作**: 程序启动后自动开始数据采集
- **即插即用**: 适合生产环境的自动化部署
- **减少人工干预**: 降低操作复杂度

### **2. 灵活性**
- **多种启动模式**: 支持全局和单独设备启动
- **选择性启动**: 可以选择性启动部分设备
- **配置驱动**: 通过配置文件控制启动行为

### **3. 可靠性**
- **错误处理**: 启动失败不影响其他设备
- **状态追踪**: 详细记录启动过程和结果
- **事件通知**: 发布启动事件供监控

### **4. 兼容性**
- **向后兼容**: 不影响现有的手动启动API
- **配置兼容**: 兼容现有的配置文件格式
- **功能并存**: 自动启动和手动启动可以并存

## 🧪 **测试验证**

### **测试脚本**
- **test_auto_start.sh** - 自动启动功能专项测试
- 验证不同配置下的自动启动行为
- 检查启动时间和数据采集效果

### **测试场景**
1. **系统级自动启动测试**
2. **设备级自动启动测试**
3. **混合配置测试**
4. **启动失败处理测试**

### **运行测试**
```bash
# 编译
export CGO_ENABLED=1
go build -o build/fanuc-collector-v2 ./cmd/main.go

# 运行测试
./test_auto_start.sh

# 监控自动启动
tail -f app.log | grep -E "自动启动|handleAutoStart"
```

## 🎯 **使用建议**

### **生产环境配置**
```json
// devices.json - 推荐配置
[
  {
    "id": "fanuc_001",
    "enabled": true,
    "auto_start": true,     // 生产设备自动启动
    "collect_interval": 3000
  }
]

// config.json
{
  "devices": {
    "auto_start": false     // 使用设备级控制
  }
}
```

### **开发环境配置**
```json
// devices.json - 开发配置
[
  {
    "id": "fanuc_001",
    "enabled": true,
    "auto_start": false,    // 开发时手动启动
    "collect_interval": 5000
  }
]
```

### **测试环境配置**
```json
// config.json - 测试配置
{
  "devices": {
    "auto_start": true      // 测试时启动所有设备
  }
}
```

## 🔧 **故障排除**

### **常见问题**

#### **1. 自动启动不生效**
- 检查 `enabled` 是否为 `true`
- 检查 `auto_start` 配置
- 查看启动日志中的错误信息

#### **2. 部分设备启动失败**
- 检查设备连接状态
- 验证设备IP和端口配置
- 查看具体的错误日志

#### **3. 启动时间过长**
- 检查网络连接质量
- 调整连接超时设置
- 考虑设备启动顺序

### **调试方法**
```bash
# 查看自动启动日志
grep -E "自动启动|handleAutoStart" app.log

# 检查设备启动状态
curl http://localhost:9005/api/v1/devices

# 监控启动过程
tail -f app.log | grep -E "启动|连接|采集"
```

## 📊 **性能考虑**

### **启动时间**
- **单设备启动**: 通常1-3秒
- **多设备启动**: 并行启动，总时间取决于最慢设备
- **网络影响**: 网络延迟会影响启动时间

### **资源使用**
- **内存开销**: 每个设备约10-20MB
- **CPU使用**: 启动时短暂增加，稳定后恢复正常
- **网络带宽**: 取决于采集频率和数据量

## 🎉 **总结**

### **核心特性**
1. ✅ **自动启动支持** - 程序启动后自动开始数据采集
2. ✅ **多种启动模式** - 支持系统级和设备级配置
3. ✅ **灵活配置** - 可选择性启动部分设备
4. ✅ **错误处理** - 启动失败不影响其他设备
5. ✅ **详细日志** - 完整记录启动过程

### **使用效果**
- 🎯 **提高便利性** - 无需手动调用API启动设备
- 🎯 **适合自动化** - 适用于生产环境自动部署
- 🎯 **降低复杂度** - 减少人工操作步骤
- 🎯 **保持灵活性** - 支持多种配置策略

### **配置建议**
- **生产环境**: 使用设备级 `auto_start=true`
- **开发环境**: 使用 `auto_start=false`，手动启动
- **测试环境**: 使用系统级 `auto_start=true`

**🎉 自动启动功能已完成！现在程序启动后会根据配置自动启动设备采集，无需手动调用API。**

---

**实现日期**: 2025-05-30  
**版本**: v2.0.0  
**功能类型**: 自动启动机制  
**目标**: 提高使用便利性
