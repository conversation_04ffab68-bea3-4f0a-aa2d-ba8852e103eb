# FANUC Data Collector v2 错误码-8重连机制

## 📋 **机制概述**

为了确保 `performRealDataCollection` 中调用的所有函数都能获取到数据，保证推送的数据是完整的，已实现**错误码-8自动重连机制**。

当任何FOCAS函数（除了 `cnc_freelibhndl`）返回错误码-8时，系统会自动进行重新连接，确保数据采集的完整性和可靠性。

## 🔄 **工作原理**

### **错误码-8的含义**
- **错误码-8**: 通常表示 `EW_FUNC`（功能不可用或连接问题）
- **触发条件**: 网络中断、设备重启、连接超时等
- **影响**: 导致FOCAS函数调用失败，数据采集不完整

### **重连机制流程**
```
FOCAS函数调用 → 返回值检查 → 错误码-8? → 重新连接 → 重试调用
     ↓                ↓              ↓           ↓          ↓
   正常返回         成功/其他错误    是         新句柄      成功/失败
     ↓                ↓              ↓           ↓          ↓
   返回数据         记录错误        释放旧连接   重试读取    返回结果
```

## 🛠️ **实现详情**

### **1. readCNCID() 重连机制**

```go
func (dc *DeviceCollector) readCNCID(libhndl *C.ushort) string {
    // 最多重试2次（原始调用 + 1次重连）
    for attempt := 0; attempt < 2; attempt++ {
        ret := C.cnc_rdcncid(*libhndl, &cncid[0])
        
        if ret == C.EW_OK {
            // 成功读取，返回数据
            return machineId
        }
        
        if ret == -8 && attempt == 0 {
            // 错误码-8，进行重连
            log.Printf("CNC ID读取返回-8，尝试重新连接")
            
            // 释放当前连接
            C.cnc_freelibhndl(*libhndl)
            
            // 重新建立连接
            if err := dc.establishConnection(libhndl); err != nil {
                return ""
            }
            
            continue // 重试读取
        }
        
        // 其他错误或重试失败
        break
    }
    
    return ""
}
```

### **2. readStatusInfo() 重连机制**

```go
func (dc *DeviceCollector) readStatusInfo(libhndl *C.ushort) map[string]interface{} {
    for attempt := 0; attempt < 2; attempt++ {
        ret := C.cnc_statinfo(*libhndl, &statinfo)
        
        if ret == C.EW_OK {
            // 成功读取状态信息
            return statusData
        }
        
        if ret == -8 && attempt == 0 {
            // 错误码-8重连逻辑
            log.Printf("状态信息读取返回-8，尝试重新连接")
            // ... 重连代码
            continue
        }
        
        break
    }
    
    return nil
}
```

### **3. readDynamicData() 重连机制**

```go
func (dc *DeviceCollector) readDynamicData(libhndl *C.ushort) map[string]interface{} {
    for attempt := 0; attempt < 2; attempt++ {
        ret := C.cnc_rddynamic2(*libhndl, ...)
        
        if ret == C.EW_OK {
            // 成功读取动态数据
            return dynamicData
        }
        
        if ret == -8 && attempt == 0 {
            // 错误码-8重连逻辑
            log.Printf("动态数据读取返回-8，尝试重新连接")
            // ... 重连代码
            continue
        }
        
        break
    }
    
    return nil
}
```

### **4. readWorkpieceCount() 重连机制**

```go
func (dc *DeviceCollector) readWorkpieceCount(libhndl *C.ushort) int {
    for attempt := 0; attempt < 2; attempt++ {
        ret := C.cnc_rdparam(*libhndl, ...)
        
        if ret == C.EW_OK {
            // 成功读取工件计数
            return workpieceCount
        }
        
        if ret == -8 && attempt == 0 {
            // 错误码-8重连逻辑
            log.Printf("工件计数读取返回-8，尝试重新连接")
            // ... 重连代码
            continue
        }
        
        break
    }
    
    return -1
}
```

## ✅ **机制优势**

### **1. 数据完整性保证**
- **自动重连**: 错误码-8时自动重新连接
- **重试机制**: 每个函数最多重试2次
- **完整数据**: 确保推送的数据包含所有字段

### **2. 系统稳定性**
- **故障隔离**: 单个函数的连接问题不影响其他函数
- **自动恢复**: 无需人工干预，自动恢复连接
- **优雅降级**: 重连失败时返回默认值而不是崩溃

### **3. 调试和监控**
- **详细日志**: 记录每次重连的详细过程
- **状态追踪**: 可以追踪句柄变化和重连成功率
- **性能监控**: 统计重连次数和成功率

## 📊 **性能影响**

### **重连开销**
- **检测开销**: 每次函数调用增加1个比较操作
- **重连时间**: 每次重连约100-200ms
- **总体影响**: 仅在错误码-8时产生额外开销

### **重试策略**
- **最大重试**: 每个函数最多重试1次
- **快速失败**: 非-8错误码立即返回
- **避免循环**: 防止无限重连

## 🔍 **监控指标**

### **重连统计**
- 错误码-8检测次数
- 重连尝试次数
- 重连成功次数
- 重连失败次数
- 重连成功率

### **数据完整性**
- 完整数据采集次数
- 部分失败采集次数
- 数据完整率
- 各字段推送成功率

### **性能指标**
- 平均重连时间
- 重连对采集周期的影响
- 系统整体稳定性

## 📋 **日志示例**

### **正常采集日志**
```
[fanuc_001] 机器ID: 3c7b5d01-5f814293-be272789-6362c43d (句柄: 32769, 尝试: 1)
[fanuc_001] 状态信息读取成功: 运行状态=stop (句柄: 32769, 尝试: 1)
[fanuc_001] 动态数据读取成功: 程序号=1108 (句柄: 32769, 尝试: 1)
[fanuc_001] 工件计数: 218 (句柄: 32769, 尝试: 1)
[fanuc_001] 数据采集成功: fanuc_001
```

### **错误码-8重连日志**
```
[fanuc_001] 状态信息读取返回-8，尝试重新连接 (句柄: 32769)
[fanuc_001] 开始建立FOCAS连接
[fanuc_001] cnc_allclibhndl3返回值: 0, 耗时: 89ms, 句柄: 32770
[fanuc_001] 状态信息重连成功，新句柄: 32770
[fanuc_001] 状态信息读取成功: 运行状态=stop (句柄: 32770, 尝试: 2)
```

### **重连失败日志**
```
[fanuc_001] 动态数据读取返回-8，尝试重新连接 (句柄: 32769)
[fanuc_001] 动态数据重连失败: 连接设备失败: -16
[fanuc_001] 读取动态数据失败: -8 (句柄: 32769, 尝试: 1)
```

## 🧪 **测试验证**

### **测试脚本**
- **test_error_8_reconnect.sh** - 错误码-8重连专项测试
- 监控重连事件和数据完整性
- 统计重连成功率和性能指标

### **测试指标**
- 错误码-8检测准确性
- 重连成功率
- 数据完整性保证
- 系统稳定性验证

### **运行测试**
```bash
# 编译
export CGO_ENABLED=1
go build -o build/fanuc-collector-v2 ./cmd/main.go

# 运行测试
./test_error_8_reconnect.sh

# 监控重连事件
tail -f app.log | grep -E "返回-8|重连"
```

## 🎯 **使用建议**

### **监控重连活动**
```bash
# 查看错误码-8事件
grep "返回-8" app.log

# 查看重连成功事件
grep "重连成功" app.log

# 统计重连成功率
reconnect_attempts=$(grep "尝试重新连接" app.log | wc -l)
reconnect_success=$(grep "重连成功" app.log | wc -l)
echo "重连成功率: $((reconnect_success * 100 / reconnect_attempts))%"
```

### **数据完整性验证**
```bash
# 检查数据完整性
grep "数据采集成功" app.log | wc -l
grep "数据采集部分失败" app.log | wc -l

# 验证各字段推送
grep "machine_id.*3c7b5d01" app.log | wc -l
grep "状态信息读取成功" app.log | wc -l
grep "动态数据读取成功" app.log | wc -l
grep "工件计数:.*句柄:" app.log | wc -l
```

## 🔧 **配置优化**

### **采集间隔调整**
```json
{
  "collect_interval": 3000,  // 建议3秒以上，给重连留出时间
  "timeout": 15,             // 连接超时时间
  "retry_count": 3,          // 外层重试次数
  "retry_delay": 10          // 重试间隔
}
```

### **重连参数优化**
- **重试次数**: 每个函数最多重试1次（总共2次尝试）
- **重连超时**: 使用establishConnection的默认超时
- **失败处理**: 重连失败时返回默认值，不影响其他函数

## 🎉 **总结**

### **核心特性**
1. ✅ **错误码-8自动检测** - 精确识别需要重连的情况
2. ✅ **自动重连机制** - 释放旧连接，建立新连接
3. ✅ **重试机制** - 每个函数最多重试2次
4. ✅ **数据完整性保证** - 确保推送的数据是完整的
5. ✅ **详细日志记录** - 完整记录重连过程

### **预期效果**
- 🎯 **提高数据完整性** - 减少因连接问题导致的数据缺失
- 🎯 **增强系统稳定性** - 自动处理连接异常
- 🎯 **改善用户体验** - 无需人工干预的自动恢复
- 🎯 **便于问题诊断** - 详细的重连日志

**🎉 错误码-8重连机制已完成！现在所有FOCAS函数都具备自动重连能力，确保数据采集的完整性和可靠性。**

---

**实现日期**: 2025-05-30  
**版本**: v2.0.0  
**机制类型**: 错误码-8自动重连  
**目标**: 保证数据完整性
