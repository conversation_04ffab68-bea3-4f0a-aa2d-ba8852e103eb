# FANUC V2 Deploy 功能说明

## 概述

新增的 `deploy` 参数为 `build-x86.sh` 脚本提供了一键部署功能，它将构建 Docker 环境、编译 Go 程序并自动运行程序。

## 功能特性

### 🚀 一键部署
- 自动构建 Docker 镜像
- 在容器中编译 `./cmd/main.go` 程序
- 启动容器并自动运行编译好的程序
- 生产级别的容器配置

### 🔧 智能编译
- 使用临时容器进行程序编译
- 支持 CGO 编译（FANUC FOCAS 库）
- 自动处理 Go 模块依赖
- 编译结果保存到 `/app/bin/fanuc_collector`

### 🌐 网络配置
- 自动连接到 `mdc_full_mdc_network` 网络
- 暴露端口 9110 用于 API 服务
- 支持与其他 MDC 服务通信

### 📊 监控和日志
- 自动创建日志和配置目录
- 支持容器重启策略
- 提供详细的部署状态反馈

## 使用方法

### 基本用法
```bash
./build-x86.sh deploy
```

### 部署流程
1. **构建镜像**: 构建 FANUC FOCAS x86 Docker 镜像
2. **清理环境**: 停止并删除现有容器
3. **创建目录**: 创建必要的日志和配置目录
4. **编译程序**: 在临时容器中编译 Go 程序
5. **启动容器**: 启动生产容器并运行程序

### 部署后管理
```bash
# 查看容器状态
./build-x86.sh status

# 查看运行日志
./build-x86.sh logs

# 进入容器调试
./build-x86.sh shell

# 停止容器
./build-x86.sh stop

# 重启容器
./build-x86.sh restart
```

## 技术细节

### 容器配置
- **镜像**: `fanuc-focas_v2:1.0.1`
- **容器名**: `fanuc-focas-dev-x86_v2`
- **平台**: `linux/386`
- **网络**: `mdc_full_mdc_network`
- **端口**: `9110:9110`

### 环境变量
- `GO_ENV=production`: 生产环境模式
- `CGO_ENABLED=1`: 启用 CGO 支持
- `GOOS=linux`: Linux 操作系统
- `GOARCH=386`: x86 架构
- `TZ=Asia/Shanghai`: 时区设置

### 挂载目录
- `$(pwd):/app/src`: 源代码目录
- `$(pwd)/logs:/app/logs`: 日志目录
- `$(pwd)/configs:/app/configs`: 配置目录
- `$(pwd)/fwlib:/app/fwlib`: FOCAS 库目录

## 故障排除

### 常见问题

1. **编译失败**
   - 检查 Go 模块依赖: `go mod tidy`
   - 确认 FOCAS 库文件存在
   - 检查 CGO 环境配置

2. **容器启动失败**
   - 检查端口 9110 是否被占用
   - 确认 Docker 网络 `mdc_full_mdc_network` 存在
   - 查看容器日志: `./build-x86.sh logs`

3. **程序运行异常**
   - 检查配置文件 `config.json`
   - 确认 API 服务地址可访问
   - 查看详细日志进行调试

### 调试命令
```bash
# 检查容器状态
docker ps -a | grep fanuc-focas-dev-x86_v2

# 查看容器日志
docker logs fanuc-focas-dev-x86_v2

# 进入容器调试
docker exec -it fanuc-focas-dev-x86_v2 /bin/bash

# 检查编译的程序
docker exec fanuc-focas-dev-x86_v2 ls -la /app/bin/

# 手动运行程序
docker exec -it fanuc-focas-dev-x86_v2 /app/bin/fanuc_collector
```

## 与其他命令的区别

| 命令 | 功能 | 用途 |
|------|------|------|
| `build` | 仅构建镜像 | 开发环境准备 |
| `run` | 交互式运行 | 开发调试 |
| `run-auto` | 后台运行 | 手动启动服务 |
| `deploy` | 构建+编译+运行 | 生产部署 |

## 最佳实践

1. **部署前检查**
   - 确认配置文件正确
   - 检查网络连接
   - 验证 FOCAS 库文件

2. **生产环境**
   - 使用 `deploy` 命令进行部署
   - 定期检查容器状态
   - 监控日志文件大小

3. **开发环境**
   - 使用 `run` 命令进行调试
   - 使用 `shell` 命令进入容器
   - 使用 `logs` 命令查看实时日志

## 更新日志

- **v1.0.1**: 新增 deploy 功能
  - 支持一键部署
  - 自动编译和运行
  - 生产级容器配置
  - 完整的错误处理和日志记录
