# FANUC Data Collector v2 FIFO缓存和自动重试机制

## 📋 **问题分析**

根据您提到的错误日志：
```
[fanuc_001] warning: 推送失败，数据已缓存: 发送请求失败: Post "http://host.docker.internal:ce/data": dial tcp **************:8091: connect: connection refused
```

您关心的两个核心问题：
1. **缓存的数据是否实现了 FIFO**
2. **推送服务器恢复正常时，缓存的数据是否会自动再次 FIFO 推送**

## 🔍 **原问题发现**

### **❌ 原实现的问题**
1. **缓存存储**: 使用 `map[string]*models.CacheItem`，map 是无序的
2. **GetAll() 方法**: 直接遍历 map，**无法保证 FIFO 顺序**
3. **推送顺序**: 缓存数据推送顺序是随机的，不是按时间顺序

```go
// 原问题代码
func (m *MemoryCacheManager) GetAll(limit int) ([]*models.CacheItem, error) {
    // 直接遍历map，顺序随机！
    for _, item := range m.items {
        items = append(items, item)
    }
    return items, nil
}
```

## ✅ **FIFO 缓存修复实现**

### **1. 数据结构增强**
```go
type MemoryCacheManager struct {
    items       map[string]*models.CacheItem
    deviceIndex map[string][]string // 设备ID -> 缓存项ID列表（FIFO顺序）
    fifoQueue   []string            // 🔥 新增：全局FIFO队列
    maxSize     int
    maxAge      time.Duration
    // ...
}
```

### **2. FIFO 队列维护**
```go
// Store 方法：存储时维护 FIFO 队列
func (m *MemoryCacheManager) Store(data *models.DeviceData) error {
    // 存储缓存项
    m.items[itemID] = item
    
    // 更新设备索引（FIFO顺序）
    m.deviceIndex[data.DeviceID] = append(m.deviceIndex[data.DeviceID], itemID)
    
    // 🔥 关键：更新全局FIFO队列（确保推送顺序）
    m.fifoQueue = append(m.fifoQueue, itemID)
    
    return nil
}
```

### **3. FIFO 顺序获取**
```go
// GetAll 方法：按 FIFO 顺序返回数据
func (m *MemoryCacheManager) GetAll(limit int) ([]*models.CacheItem, error) {
    var items []*models.CacheItem
    count := 0

    // 🔥 关键：按FIFO队列顺序获取数据
    for _, itemID := range m.fifoQueue {
        if count >= limit {
            break
        }
        
        if item, exists := m.items[itemID]; exists {
            items = append(items, item)
            count++
        }
    }

    return items, nil
}
```

### **4. 队列一致性维护**
```go
// Remove 方法：删除时维护队列一致性
func (m *MemoryCacheManager) Remove(itemID string) error {
    // 删除缓存项
    delete(m.items, itemID)
    
    // 🔥 关键：从FIFO队列中删除
    for i, id := range m.fifoQueue {
        if id == itemID {
            m.fifoQueue = append(m.fifoQueue[:i], m.fifoQueue[i+1:]...)
            break
        }
    }
    
    return nil
}
```

## 🔄 **自动重试机制**

### **1. 定时刷新机制**
```go
// backgroundTasks 中的定时器
func (dm *DeviceManager) backgroundTasks() {
    // 缓存清理定时器
    cacheTicker := time.NewTicker(time.Duration(dm.config.Cache.FlushInterval) * time.Second)
    
    for {
        select {
        case <-cacheTicker.C:
            dm.flushCachedData() // 🔥 定时刷新缓存数据
        }
    }
}
```

### **2. 缓存数据推送流程**
```go
func (dm *DeviceManager) flushCachedData() {
    // 1. 获取所有缓存数据（按FIFO顺序）
    cachedItems, err := dm.cacheManager.GetAll(dm.config.DataPush.BatchSize)
    
    // 2. 转换为设备数据
    var dataList []*models.DeviceData
    var itemIDs []string
    for _, item := range cachedItems {
        dataList = append(dataList, item.Data)
        itemIDs = append(itemIDs, item.ID)
    }

    // 3. 尝试批量推送
    if err := dm.pusherManager.PushBatch(dm.ctx, dataList); err != nil {
        // 推送失败，保留缓存
        dm.publishEvent("system", "warning", "缓存数据推送失败", nil)
    } else {
        // 4. 推送成功，删除缓存项
        dm.cacheManager.RemoveBatch(itemIDs)
        dm.publishEvent("system", "info", 
            fmt.Sprintf("成功推送 %d 条缓存数据", len(dataList)), nil)
    }
}
```

### **3. 推送失败时的缓存逻辑**
```go
func (dm *DeviceManager) processDeviceData(data *models.DeviceData) {
    // 尝试推送数据
    if err := dm.pusherManager.Push(dm.ctx, data); err != nil {
        // 🔥 推送失败，缓存数据（按FIFO顺序）
        if cacheErr := dm.cacheManager.Store(data); cacheErr != nil {
            dm.publishEvent(data.DeviceID, "error", "推送失败且缓存失败", nil)
        } else {
            dm.publishEvent(data.DeviceID, "warning", "推送失败，数据已缓存", nil)
        }
    } else {
        dm.publishEvent(data.DeviceID, "data", "数据推送成功", nil)
    }
}
```

## 📊 **FIFO 保证机制**

### **1. 时间顺序保证**
- **存储顺序**: 数据按产生时间顺序添加到 `fifoQueue`
- **获取顺序**: `GetAll()` 按 `fifoQueue` 顺序返回数据
- **推送顺序**: 批量推送时保持 FIFO 顺序

### **2. 队列一致性保证**
- **添加操作**: `Store()` 时同时更新 `items`、`deviceIndex` 和 `fifoQueue`
- **删除操作**: `Remove()` 时同时从三个数据结构中删除
- **清理操作**: `cleanup()` 时维护队列一致性

### **3. 并发安全保证**
```go
type MemoryCacheManager struct {
    mu sync.RWMutex // 读写锁保护
    // ...
}

func (m *MemoryCacheManager) Store(data *models.DeviceData) error {
    m.mu.Lock()         // 写锁
    defer m.mu.Unlock()
    // ... FIFO 操作
}

func (m *MemoryCacheManager) GetAll(limit int) ([]*models.CacheItem, error) {
    m.mu.RLock()        // 读锁
    defer m.mu.RUnlock()
    // ... FIFO 读取
}
```

## ⏰ **自动重试时机**

### **1. 定时触发**
- **配置参数**: `cache.flush_interval` (默认60秒)
- **触发条件**: 定时器到期
- **执行频率**: 每60秒检查一次缓存

### **2. 健康检查触发**
- **推送器健康检查**: 检查推送服务器是否恢复
- **自动恢复**: 服务器恢复后下次定时器触发时自动推送

### **3. 批量推送优化**
- **批量大小**: `data_push.batch_size` (默认10条)
- **推送策略**: 按批次推送，提高效率
- **失败处理**: 整批失败时保留所有数据，部分失败时重试

## 🧪 **测试验证**

### **测试脚本**
- **test_fifo_cache.sh** - FIFO缓存和自动重试专项测试
- 模拟推送服务器不可达
- 验证数据缓存和FIFO顺序
- 测试服务器恢复后的自动推送

### **测试场景**
1. **推送失败阶段**: 配置不可达的推送服务器，观察数据缓存
2. **缓存积累阶段**: 让设备持续采集，积累缓存数据
3. **服务器恢复阶段**: 修复推送服务器配置
4. **自动重试阶段**: 观察缓存数据按FIFO顺序自动推送

### **验证指标**
- 缓存数据的时间顺序
- 推送数据的FIFO顺序
- 自动重试的触发频率
- 缓存清空的完整性

## 📋 **配置参数**

### **缓存配置**
```json
{
  "cache": {
    "type": "memory",
    "max_size": 10000,           // 最大缓存条数
    "max_age": 3600,             // 缓存过期时间(秒)
    "flush_interval": 60,        // 🔥 自动刷新间隔(秒)
    "storage_path": "./cache"
  }
}
```

### **推送配置**
```json
{
  "data_push": {
    "type": "restful",
    "enabled": true,
    "batch_size": 10,            // 🔥 批量推送大小
    "timeout": 30,               // 推送超时时间
    "config": {
      "url": "http://host.docker.internal:8091/api/v1/device/data",
      "timeout": 30
    }
  }
}
```

## 🎯 **使用建议**

### **生产环境配置**
```json
{
  "cache": {
    "flush_interval": 30,        // 30秒刷新，快速恢复
    "max_size": 50000,          // 增大缓存容量
    "max_age": 7200             // 2小时过期
  },
  "data_push": {
    "batch_size": 50,           // 增大批量大小
    "timeout": 60               // 增加超时时间
  }
}
```

### **监控建议**
```bash
# 监控缓存状态
curl http://localhost:9005/api/v1/system/cache

# 查看推送失败日志
grep "推送失败，数据已缓存" logs/fanuc_v2.log

# 查看自动重试日志
grep "成功推送.*条缓存数据" logs/fanuc_v2.log

# 监控缓存大小
grep "缓存使用率" logs/fanuc_v2.log
```

## 🎉 **总结**

### **FIFO 缓存实现**
1. ✅ **真正的 FIFO**: 使用 `fifoQueue` 维护数据顺序
2. ✅ **时间顺序**: 数据按产生时间顺序缓存和推送
3. ✅ **队列一致性**: 所有操作都维护队列一致性
4. ✅ **并发安全**: 使用读写锁保护数据结构

### **自动重试机制**
1. ✅ **定时触发**: 每60秒自动检查缓存数据
2. ✅ **批量推送**: 支持批量推送提高效率
3. ✅ **失败保护**: 推送失败时保留缓存数据
4. ✅ **自动恢复**: 服务器恢复后自动推送缓存

### **问题解答**
- **Q1: 缓存的数据是否有实现 FIFO?**
  - **A1: ✅ 是的**，现在使用 `fifoQueue` 确保严格的 FIFO 顺序

- **Q2: 推送服务器恢复正常时，缓存的数据是否会自动再次 FIFO 推送?**
  - **A2: ✅ 是的**，定时器每60秒自动尝试推送缓存数据，按 FIFO 顺序推送

**🎉 FIFO缓存和自动重试机制已完全实现！现在可以确保数据按时间顺序缓存，并在推送服务器恢复后自动按FIFO顺序重新推送。**

---

**修复日期**: 2025-05-30  
**版本**: v2.0.0  
**核心改进**: FIFO队列 + 自动重试机制  
**解决问题**: 缓存顺序随机 → FIFO顺序保证
