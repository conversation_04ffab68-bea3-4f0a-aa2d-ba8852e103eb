# FANUC Data Collector v2 连接失败数据推送机制

## 📋 **机制概述**

当 `cnc_allclibhndl3` 返回错误码 -16（连接失败）时，系统仍然会推送数据，但设置 `connected = false`，确保下游系统能够及时了解设备的连接状态。

## 🔄 **工作原理**

### **连接状态处理流程**
```
尝试连接设备
    ↓
cnc_allclibhndl3调用
    ↓
返回值检查
    ↓
┌─────────────────┬─────────────────┐
│   返回值 = 0    │   返回值 = -16  │
│   (连接成功)    │   (连接失败)    │
└─────────────────┴─────────────────┘
    ↓                     ↓
设置connected=true    设置connected=false
    ↓                     ↓
推送完整数据          推送失败状态数据
    ↓                     ↓
包含实际设备数据      包含错误信息和默认值
```

### **数据推送策略**
- **连接成功**: 推送包含真实设备数据的完整数据包
- **连接失败**: 推送包含错误信息和默认值的状态数据包

## 🛠️ **实现详情**

### **1. 连接失败检测**

```go
// 在 performRealDataCollection 中
if err := dc.establishConnection(&libhndl); err != nil {
    log.Printf("[%s] 建立连接失败: %v", dc.config.ID, err)
    connected = false
    dc.updateConnectionStatus(false)
    // 连接失败时也要推送数据，但标记为未连接状态
    dc.pushDisconnectedData()
    return
}
```

### **2. 错误码详细处理**

```go
// 在 establishConnection 中
switch ret {
case -16:
    log.Printf("[%s] 连接失败: 网络连接错误或设备不可达 (错误码: -16)", dc.config.ID)
    return fmt.Errorf("连接设备失败: -16 (网络连接错误)")
case -8:
    log.Printf("[%s] 连接失败: 功能不可用或参数错误 (错误码: -8)", dc.config.ID)
    return fmt.Errorf("连接设备失败: -8 (功能不可用)")
default:
    log.Printf("[%s] 连接失败: 未知错误 (错误码: %d)", dc.config.ID, ret)
    return fmt.Errorf("连接设备失败: %d", ret)
}
```

### **3. 断开连接数据推送**

```go
func (dc *DeviceCollector) pushDisconnectedData() {
    // 创建断开连接状态的设备数据
    data := models.NewDeviceData(dc.config.ID, dc.config.Name)
    data.DataType = "fanuc_cnc"
    data.Location = fmt.Sprintf("%s:%d", dc.config.IP, dc.config.Port)
    data.Status.Connected = false  // 关键：设置连接状态为false
    
    // 连接失败时的状态信息
    data.RawData["connected"] = false
    data.RawData["connection_status"] = "failed"
    data.RawData["error_message"] = "设备连接失败"
    data.RawData["last_error_code"] = -16
    
    // 设置默认值表示数据不可用
    data.RawData["machine_id"] = ""
    data.RawData["alarm_status"] = -1
    data.RawData["run_status_description"] = "disconnected"
    // ... 其他默认值
}
```

## 📊 **数据格式对比**

### **连接成功时的数据格式**
```json
{
  "device_id": "fanuc_001",
  "data_type": "fanuc_cnc",
  "timestamp": "2025-05-30T10:34:50Z",
  "status": {
    "connected": true,
    "last_seen": "2025-05-30T10:34:50Z"
  },
  "raw_data": {
    "connected": true,
    "connection_status": "connected",
    "machine_id": "3c7b5d01-5f814293-be272789-6362c43d",
    "alarm_status": 1,
    "auto_mode": 9,
    "run_status": 0,
    "run_status_description": "stop",
    "current_program_number": 1108,
    "actual_feedrate": 0,
    "actual_spindle_speed": 0,
    "workpiece_count": 218
  }
}
```

### **连接失败时的数据格式**
```json
{
  "device_id": "fanuc_001",
  "data_type": "fanuc_cnc",
  "timestamp": "2025-05-30T10:34:50Z",
  "status": {
    "connected": false,
    "last_seen": "2025-05-30T10:34:50Z"
  },
  "raw_data": {
    "connected": false,
    "connection_status": "failed",
    "error_message": "设备连接失败",
    "last_error_code": -16,
    "machine_id": "",
    "alarm_status": -1,
    "auto_mode": -1,
    "run_status": -1,
    "run_status_description": "disconnected",
    "current_program_number": -1,
    "actual_feedrate": -1,
    "actual_spindle_speed": -1,
    "workpiece_count": -1
  }
}
```

## ✅ **机制优势**

### **1. 状态透明性**
- **实时状态**: 下游系统能够实时了解设备连接状态
- **状态区分**: 明确区分连接成功和失败的数据
- **错误信息**: 提供详细的错误码和错误描述

### **2. 数据连续性**
- **持续推送**: 无论连接成功与否都推送数据
- **时间戳一致**: 保持数据推送的时间连续性
- **格式统一**: 使用相同的数据格式，便于下游处理

### **3. 故障诊断**
- **错误码记录**: 详细记录FOCAS函数返回的错误码
- **错误分类**: 区分不同类型的连接错误
- **调试信息**: 提供丰富的调试和监控信息

### **4. 系统稳定性**
- **优雅降级**: 连接失败时不影响数据推送流程
- **默认值处理**: 为不可用数据提供合理的默认值
- **异常隔离**: 连接问题不影响整体系统运行

## 🔍 **错误码说明**

### **常见FOCAS错误码**
| 错误码 | 含义 | 处理方式 |
|--------|------|----------|
| 0 | 成功 | 正常处理 |
| -8 | 功能不可用或参数错误 | 重连机制 |
| -16 | 网络连接错误或设备不可达 | 推送失败状态数据 |
| -17 | 连接超时 | 推送失败状态数据 |
| -32 | 内存不足 | 推送失败状态数据 |

### **错误处理策略**
- **-16错误**: 推送连接失败数据，设置connected=false
- **-8错误**: 触发重连机制（已实现）
- **其他错误**: 记录错误信息，推送失败状态数据

## 📋 **日志示例**

### **连接失败日志**
```
[fanuc_001] 开始独立连接采集
[fanuc_001] 开始建立FOCAS连接
[fanuc_001] cnc_startupprocess返回值: 0
[fanuc_001] cnc_allclibhndl3返回值: -16, 耗时: 114.901792ms, 句柄: 0
[fanuc_001] 连接失败: 网络连接错误或设备不可达 (错误码: -16)
[fanuc_001] 建立连接失败: 连接设备失败: -16 (网络连接错误)
[fanuc_001] 连接状态更新: 已断开
[fanuc_001] 推送连接失败状态数据
[fanuc_001] 生成断开连接数据: fanuc_001 (connected=false)
[fanuc_001] 断开连接数据已发送到处理通道
```

### **连接成功日志**
```
[fanuc_001] 开始独立连接采集
[fanuc_001] 开始建立FOCAS连接
[fanuc_001] cnc_startupprocess返回值: 0
[fanuc_001] cnc_allclibhndl3返回值: 0, 耗时: 67ms, 句柄: 32769
[fanuc_001] FOCAS连接建立成功
[fanuc_001] 连接建立成功，句柄: 32769
[fanuc_001] 连接状态更新: 已连接
[fanuc_001] 机器ID: 3c7b5d01-5f814293-be272789-6362c43d (句柄: 32769, 尝试: 1)
[fanuc_001] 数据采集成功: fanuc_001
[fanuc_001] 数据已发送到处理通道
```

## 🧪 **测试验证**

### **测试脚本**
- **test_connection_failure.sh** - 连接失败专项测试
- 模拟设备不可达的情况
- 验证失败状态数据推送
- 检查数据格式和字段完整性

### **测试方法**
1. **配置不存在的设备IP** - 模拟网络不可达
2. **监控连接尝试** - 观察错误码-16的产生
3. **验证数据推送** - 确认失败状态数据的推送
4. **检查数据格式** - 验证connected=false等字段

### **运行测试**
```bash
# 编译
export CGO_ENABLED=1
go build -o build/fanuc-collector-v2 ./cmd/main.go

# 运行测试
./test_connection_failure.sh

# 监控连接失败事件
tail -f app.log | grep -E "返回值: -16|推送连接失败"
```

## 🎯 **使用建议**

### **监控连接状态**
```bash
# 查看连接失败事件
grep "cnc_allclibhndl3返回值: -16" app.log

# 查看失败状态数据推送
grep "推送连接失败状态数据" app.log

# 统计连接成功率
total_attempts=$(grep "cnc_allclibhndl3返回值:" app.log | wc -l)
failures=$(grep "cnc_allclibhndl3返回值: -16" app.log | wc -l)
success_rate=$(((total_attempts - failures) * 100 / total_attempts))
echo "连接成功率: ${success_rate}%"
```

### **数据处理建议**
```bash
# 下游系统处理建议
if data.raw_data.connected == false:
    # 处理连接失败状态
    log_connection_failure(data.raw_data.error_message)
    update_device_status(device_id, "disconnected")
    # 使用默认值或上次已知值
else:
    # 处理正常数据
    process_device_data(data.raw_data)
    update_device_status(device_id, "connected")
```

## 🔧 **配置优化**

### **连接超时设置**
```json
{
  "timeout": 10,        // 连接超时时间（秒）
  "retry_count": 3,     // 重试次数
  "retry_delay": 15,    // 重试间隔（秒）
  "collect_interval": 5000  // 采集间隔（毫秒）
}
```

### **网络环境适配**
- **稳定网络**: timeout=10秒，retry_delay=15秒
- **不稳定网络**: timeout=15秒，retry_delay=30秒
- **测试环境**: timeout=5秒，retry_delay=10秒

## 🎉 **总结**

### **核心特性**
1. ✅ **连接失败检测** - 精确识别cnc_allclibhndl3返回-16
2. ✅ **状态数据推送** - 连接失败时推送包含错误信息的数据
3. ✅ **连接状态标记** - 正确设置connected=false
4. ✅ **默认值处理** - 为不可用数据设置合理默认值
5. ✅ **错误信息记录** - 详细记录错误码和错误原因

### **预期效果**
- 🎯 **状态透明性** - 下游系统实时了解设备连接状态
- 🎯 **数据连续性** - 保持数据推送的连续性
- 🎯 **故障诊断** - 提供丰富的错误信息用于故障排查
- 🎯 **系统稳定性** - 连接失败不影响整体数据流

**🎉 连接失败数据推送机制已完成！现在当cnc_allclibhndl3返回-16时，系统会推送包含连接状态信息的数据，确保下游系统能够及时了解设备状态。**

---

**实现日期**: 2025-05-30  
**版本**: v2.0.0  
**机制类型**: 连接失败状态推送  
**目标**: 保证数据推送连续性
