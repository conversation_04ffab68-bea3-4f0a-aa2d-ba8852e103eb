#!/bin/bash

# 启动基本服务脚本

# 设置Go环境
export GOROOT=/opt/homebrew/Cellar/go/1.24.2/libexec
export PATH=$GOROOT/bin:$PATH

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "Starting basic MDC services..."
mkdir -p "$PROJECT_ROOT/logs"

# 启动设备采集服务
echo "🚀 Starting 02_device_collect..."
cd "$PROJECT_ROOT/02_device_collect"
nohup go run main.go > "$PROJECT_ROOT/logs/02_device_collect.log" 2>&1 &
echo $! > "$PROJECT_ROOT/logs/02_device_collect.pid"
echo "   PID: $(cat $PROJECT_ROOT/logs/02_device_collect.pid)"

sleep 2

# 启动数据清洗服务
echo "🚀 Starting 03_data_cleaning..."
cd "$PROJECT_ROOT/03_data_cleaning"
nohup go run main.go > "$PROJECT_ROOT/logs/03_data_cleaning.log" 2>&1 &
echo $! > "$PROJECT_ROOT/logs/03_data_cleaning.pid"
echo "   PID: $(cat $PROJECT_ROOT/logs/03_data_cleaning.pid)"

sleep 2

# 启动数据推送服务
echo "🚀 Starting 04_data_push..."
cd "$PROJECT_ROOT/04_data_push"
nohup go run main.go > "$PROJECT_ROOT/logs/04_data_push.log" 2>&1 &
echo $! > "$PROJECT_ROOT/logs/04_data_push.pid"
echo "   PID: $(cat $PROJECT_ROOT/logs/04_data_push.pid)"

echo ""
echo "✅ Basic services started!"
echo ""
echo "To check status: ps -p \$(cat logs/*.pid)"
echo "To view logs: tail -f logs/02_device_collect.log"
