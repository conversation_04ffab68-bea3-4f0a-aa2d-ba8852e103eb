#!/bin/bash

# MDC 服务运行脚本

set -e  # 遇到错误时退出

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
echo "Project root: $PROJECT_ROOT"

# 设置Go环境
setup_go_env() {
    # 检查是否有Homebrew安装的Go
    if [ -d "/opt/homebrew/Cellar/go" ]; then
        # 找到最新版本的Go
        GO_VERSION=$(ls /opt/homebrew/Cellar/go/ | sort -V | tail -1)
        export GOROOT="/opt/homebrew/Cellar/go/$GO_VERSION/libexec"
        export PATH="$GOROOT/bin:$PATH"
        echo "Using Homebrew Go: $GOROOT"
    fi
}

# 设置Go环境
setup_go_env

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed or not in PATH"
    exit 1
fi

echo "Go version: $(go version)"

# 服务列表
SERVICES=(
    "02_device_collect"
    "03_data_cleaning"
    "04_data_push"
    "11_server_jet"
    "12_server_api"
)

# 函数：运行单个服务
run_service() {
    local service=$1
    local service_dir="$PROJECT_ROOT/$service"

    echo "=========================================="
    echo "Running service: $service"
    echo "Directory: $service_dir"
    echo "=========================================="

    if [ ! -d "$service_dir" ]; then
        echo "Error: Service directory $service_dir does not exist"
        return 1
    fi

    cd "$service_dir"

    # 检查是否有go.mod文件
    if [ ! -f "go.mod" ]; then
        echo "Error: go.mod not found in $service_dir"
        return 1
    fi

    # 检查是否有main.go文件
    if [ ! -f "main.go" ]; then
        echo "Error: main.go not found in $service_dir"
        return 1
    fi

    # 下载依赖
    echo "Downloading dependencies..."
    GOSUMDB=off go mod download

    # 整理依赖
    echo "Tidying dependencies..."
    GOSUMDB=off go mod tidy

    # 编译检查
    echo "Building service..."
    if GOSUMDB=off go build -o "${service}_binary" main.go; then
        echo "✅ Service $service built successfully"

        # 清理二进制文件
        rm -f "${service}_binary"

        echo "Service $service is ready to run"
        echo "To start: cd $service_dir && go run main.go"
    else
        echo "❌ Failed to build service $service"
        return 1
    fi

    echo ""
}

# 函数：运行所有服务（构建检查）
run_all_services() {
    echo "Checking all services..."

    for service in "${SERVICES[@]}"; do
        if ! run_service "$service"; then
            echo "❌ Failed to process service: $service"
            exit 1
        fi
    done

    echo "=========================================="
    echo "✅ All services are ready!"
    echo "=========================================="
    echo ""
    echo "To start individual services:"
    for service in "${SERVICES[@]}"; do
        echo "  $service: cd $PROJECT_ROOT/$service && go run main.go"
    done
    echo ""
    echo "To start with Docker Compose:"
    echo "  cd $PROJECT_ROOT && make up"
}

# 主函数
main() {
    echo "MDC Services Runner"
    echo "=================="

    # 如果提供了服务名参数，只运行指定服务
    if [ $# -gt 0 ]; then
        service_name=$1
        if [[ " ${SERVICES[@]} " =~ " ${service_name} " ]]; then
            run_service "$service_name"
        else
            echo "Error: Unknown service '$service_name'"
            echo "Available services: ${SERVICES[*]}"
            exit 1
        fi
    else
        # 运行所有服务检查
        run_all_services
    fi
}

# 运行主函数
main "$@"