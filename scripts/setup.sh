#!/bin/bash

# 项目设置脚本

echo "Setting up MDC Full project..."

# 创建必要的目录
echo "Creating directories..."
mkdir -p data/sqlite
mkdir -p data/influxdb
mkdir -p data/mongodb
mkdir -p data/redis
mkdir -p data/nats
mkdir -p logs

# 设置权限
echo "Setting permissions..."
chmod +x scripts/*.sh

# 检查Docker和Docker Compose
echo "Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

# 检查Docker Compose (支持新旧版本)
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
    echo "Found docker-compose command"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
    echo "Found docker compose command"
else
    echo "Docker Compose is not available. Please install Docker Compose first."
    exit 1
fi

# 拉取基础镜像
echo "Pulling base images..."
docker pull redis:7-alpine
docker pull nats:2.10-alpine
docker pull influxdb:2.7-alpine
docker pull mongo:7
docker pull golang:1.21-alpine
docker pull alpine:latest

# 创建网络
echo "Creating Docker network..."
docker network create mdc_network 2>/dev/null || true

# 构建项目
echo "Building project..."
make build

echo "Setup completed successfully!"
echo ""
echo "To start the services, run:"
echo "  make up"
echo ""
echo "To send test data, run:"
echo "  ./scripts/test-data.sh"
echo ""
echo "To view logs, run:"
echo "  make logs"
