set -e  # 遇到错误时退出

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
echo "Project root: $PROJECT_ROOT"


# 设置Go环境
setup_go_env() {
    # 检查是否有Homebrew安装的Go
    if [ -d "/opt/homebrew/Cellar/go" ]; then
        # 找到最新版本的Go
        GO_VERSION=$(ls /opt/homebrew/Cellar/go/ | sort -V | tail -1)
        export GOROOT="/opt/homebrew/Cellar/go/$GO_VERSION/libexec"
        export PATH="$GOROOT/bin:$PATH"
        echo "Using Homebrew Go: $GOROOT"
    fi
}

# 设置Go环境
setup_go_env

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed or not in PATH"
    exit 1
fi

echo "Go version: $(go version)"

# 函数：根据服务名获取端口
get_service_port() {
    case "$1" in
        "02_device_collect") echo "8081" ;;
        "03_data_cleaning") echo "8082" ;;
        "04_data_push") echo "8083" ;;
        "11_server_jet") echo "8084" ;;
        "12_server_api") echo "9005" ;;
        *) echo "" ;;
    esac
}

SERVICES=(
    "02_device_collect"
    "03_data_cleaning"
    "04_data_push"
    "11_server_jet"
    "12_server_api"
)

# 函数：检查端口是否被占用并杀死进程
check_and_kill_port() {
    local port=$1
    local service_name=$2

    echo "Checking port $port for service $service_name..."

    # 查找占用端口的进程
    local pid=$(lsof -ti:$port)

    if [ -n "$pid" ]; then
        echo "⚠️  Port $port is occupied by PID: $pid"
        echo "   Killing process $pid..."

        # 尝试优雅关闭
        kill $pid 2>/dev/null
        sleep 2

        # 检查进程是否还在运行
        if ps -p $pid > /dev/null 2>&1; then
            echo "   Force killing process $pid..."
            kill -9 $pid 2>/dev/null
        fi

        # 再次检查端口
        sleep 1
        local new_pid=$(lsof -ti:$port)
        if [ -n "$new_pid" ]; then
            echo "❌ Failed to free port $port"
            return 1
        else
            echo "✅ Port $port is now free"
        fi
    else
        echo "✅ Port $port is available"
    fi

    return 0
}

# 函数：启动所有服务
start_all_services() {
    echo "Starting all MDC services..."

    # 创建日志目录
    mkdir -p "$PROJECT_ROOT/logs"

    # 检查并清理端口
    echo ""
    echo "Checking and cleaning ports..."
    for service in "${SERVICES[@]}"; do
        port=$(get_service_port "$service")
        if ! check_and_kill_port "$port" "$service"; then
            echo "❌ Failed to free port $port for $service"
            echo "Please manually check and kill processes using port $port"
            return 1
        fi
    done

    echo ""
    echo "All ports are ready. Starting services..."

    # 启动每个服务
    for service in "${SERVICES[@]}"; do
        service_dir="$PROJECT_ROOT/$service"

        if [ ! -d "$service_dir" ]; then
            echo "❌ Service directory $service_dir does not exist"
            continue
        fi

        if [ ! -f "$service_dir/main.go" ]; then
            echo "❌ main.go not found in $service_dir"
            continue
        fi

        echo "🚀 Starting $service..."

        # 在后台启动服务，输出重定向到日志文件
        cd "$service_dir"
        nohup go run main.go > "$PROJECT_ROOT/logs/${service}.log" 2>&1 &
        service_pid=$!

        # 保存PID到文件
        echo $service_pid > "$PROJECT_ROOT/logs/${service}.pid"

        echo "   PID: $service_pid"
        echo "   Log: $PROJECT_ROOT/logs/${service}.log"

        # 等待一下确保服务启动
        sleep 2
    done

    echo ""
    echo "✅ All services started!"
    echo ""
    echo "To check service status:"
    echo "  ./run.sh status"
    echo ""
    echo "To stop all services:"
    echo "  ./run.sh stop"
    echo ""
    echo "To view logs:"
    echo "  tail -f logs/02_device_collect.log"
}

# 函数：检查服务状态
check_services_status() {
    echo "MDC Services Status"
    echo "=================="

    for service in "${SERVICES[@]}"; do
        pid_file="$PROJECT_ROOT/logs/${service}.pid"

        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            if ps -p $pid > /dev/null 2>&1; then
                echo "✅ $service (PID: $pid) - Running"
            else
                echo "❌ $service (PID: $pid) - Stopped"
                rm -f "$pid_file"
            fi
        else
            echo "⚪ $service - Not started"
        fi
    done
}

# 函数：停止所有服务
stop_all_services() {
    echo "Stopping all MDC services..."

    for service in "${SERVICES[@]}"; do
        pid_file="$PROJECT_ROOT/logs/${service}.pid"

        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            if ps -p $pid > /dev/null 2>&1; then
                echo "🛑 Stopping $service (PID: $pid)..."
                kill $pid
                sleep 1

                # 如果进程还在运行，强制杀死
                if ps -p $pid > /dev/null 2>&1; then
                    echo "   Force killing $service..."
                    kill -9 $pid
                fi
            fi
            rm -f "$pid_file"
        fi
    done

    echo "✅ All services stopped!"
}

# 主函数
main() {
    echo "MDC Services Runner"
    echo "=================="

    case "${1:-help}" in
        "start")
            start_all_services
            ;;
        "stop")
            stop_all_services
            ;;
        "status")
            check_services_status
            ;;
        "restart")
            stop_all_services
            sleep 2
            start_all_services
            ;;
        "help"|"")
            echo "Usage: $0 {start|stop|status|restart}"
            echo ""
            echo "Commands:"
            echo "  start    - Start all services"
            echo "  stop     - Stop all services"
            echo "  status   - Check services status"
            echo "  restart  - Restart all services"
            echo ""
            echo "Available services: ${SERVICES[*]}"
            ;;
        *)
            echo "Error: Unknown command '$1'"
            echo "Available commands: start, stop, status, restart"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"