#!/bin/bash

# 确保脚本在出错时停止执行
set -e

# 镜像名称和标签
IMAGE_NAME="studio_api"
TAG="2.1.3"
PLATFORM="linux/amd64"

echo "构建 ${PLATFORM} 架构的 Docker 镜像..."
echo "镜像名称: ${IMAGE_NAME}"
echo "镜像标签: ${TAG}"
echo "--------------------------------"

# 启用 Docker BuildKit
export DOCKER_BUILDKIT=1

# 使用 DOCKER_DEFAULT_PLATFORM 环境变量指定目标平台
export DOCKER_DEFAULT_PLATFORM=${PLATFORM}

# 构建镜像
docker build -t ${IMAGE_NAME}:${TAG} .

echo "${PLATFORM} Docker 镜像构建完成: ${IMAGE_NAME}:${TAG}"
echo "可以使用以下命令运行容器:"
echo "  docker run -p 8080:8080 -v $(pwd)/data:/app/data ${IMAGE_NAME}:${TAG}"

# 添加执行权限
chmod +x $0 



# 更新 ./push.sh 中的镜像版本号
# 确保 push.sh 文件存在并且路径正确
if [ -f "docker_push_api.sh" ]; then
    sed -i '' "s/IMAGE_VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/IMAGE_VERSION=\"${TAG}\"/" docker_push_api.sh
else
    echo "错误: docker_push_api.sh 文件不存在"
fi


# 更新 ./docker-compose.yml 中的镜像版本号
#services:
#  fastreport_net:
#    image: fastreport_net:0.0.1
# 确保 docker-compose.yml 文件存在并且路径正确
if [ -f "docker-compose.yml" ]; then
    sed -i '' "s/image: ${IMAGE_NAME}:[0-9]*\.[0-9]*\.[0-9]*/image: ${IMAGE_NAME}:${TAG}/" docker-compose.yml
else
    echo "错误: docker-compose.yml 文件不存在"
fi


IMAGE_ID=$(docker images -q ${IMAGE_NAME}:${TAG})

echo "镜像ID: ${IMAGE_ID}"