#!/bin/bash

IMAGE_NAME="studio_api"
IMAGE_VERSION="2.1.3"

# 通过 docker images 获取镜像ID
IMAGE_ID=$(docker images -q ${IMAGE_NAME}:${IMAGE_VERSION})

URL="crpi-miowmruvplz7jr0c.cn-shenzhen.personal.cr.aliyuncs.com"
IMAGE_URL="${URL}/c2studio/${IMAGE_NAME}"

docker tag ${IMAGE_ID} ${IMAGE_URL}:${IMAGE_VERSION}
docker push ${IMAGE_URL}:${IMAGE_VERSION}

echo "镜像推送完成"
echo "镜像版本号: ${IMAGE_VERSION}"
echo "镜像ID: ${IMAGE_ID}"
echo "拉取镜像: docker pull ${IMAGE_URL}:${IMAGE_VERSION}"
echo "镜像重命名: docker tag ${IMAGE_URL}:${IMAGE_VERSION} ${IMAGE_NAME}:${IMAGE_VERSION}"
echo "镜像重命名: docker tag ${IMAGE_ID} ${IMAGE_NAME}:${IMAGE_VERSION}"
