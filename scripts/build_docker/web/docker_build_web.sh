#!/bin/bash

# 确保脚本在出错时停止执行
set -e

# 镜像名称和标签
TAG="1.0.4"

IMAGE_NAME="studio_web"
PLATFORM="linux/amd64"

echo "构建 Docker 镜像..."

npm run build

# 启用 Docker BuildKit
export DOCKER_BUILDKIT=1

# 使用 DOCKER_DEFAULT_PLATFORM 环境变量指定目标平台
export DOCKER_DEFAULT_PLATFORM=${PLATFORM}

# 构建镜像
docker build -t ${IMAGE_NAME}:${TAG} .

echo "Docker 镜像构建完成: ${IMAGE_NAME}:${TAG}"
echo "可以使用以下命令运行容器:"
echo "  docker-compose up -d" 

# 更新 ./docker-push.sh 中的镜像版本号
# 确保 docker-push.sh 文件存在并且路径正确
if [ -f "docker-push.sh" ]; then
    sed -i '' "s/IMAGE_VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/IMAGE_VERSION=\"${TAG}\"/" docker-push.sh
else
    echo "错误: docker-push.sh 文件不存在"
fi


# 更新 ./docker-compose.yml 中的镜像版本号
#services:
#  studio_web:
#    image: studio_web:0.0.1
# 确保 docker-compose.yml 文件存在并且路径正确
if [ -f "docker-compose.yml" ]; then
    sed -i '' "s/image: studio_web:[0-9]*\.[0-9]*\.[0-9]*/image: studio_web:${TAG}/" docker-compose.yml
else
    echo "错误: docker-compose.yml 文件不存在"
fi