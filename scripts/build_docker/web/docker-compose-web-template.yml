services:
  studio_web:
    image: studio_web:1.0.4
    container_name: studio_web
    ports:
      - "8080:80"
    restart: always
    # 如果有环境变量需要配置，可以取消下面的注释
    # environment:
    #   - NODE_ENV=production
    # 如果需要运行时配置，可以挂载config.js
    volumes:
      # 挂载配置文件
      - ./public/config.js:/usr/share/nginx/html/config.js 
      # 图标
      - ./public/logo.png:/usr/share/nginx/html/logo.png
      # 收藏夹图标
      - ./public/favicon.ico:/usr/share/nginx/html/favicon.ico