#!/usr/bin/env node

/**
 * 批量添加设备记录脚本
 * 
 * 功能：通过调用 API 批量添加 test 18 到 test 47 共30个设备记录
 * 参考：test 16, test 17 的设备记录格式
 */

const https = require('https');
const http = require('http');

// API 配置
const API_BASE_URL = 'http://localhost:9005';
const API_ENDPOINT = '/api/admin/devices';
const LOGIN_ENDPOINT = '/api/auth/login';

// 默认管理员账户
const DEFAULT_ADMIN = {
    username: 'admin',
    password: 'admin123'
};

/**
 * 发送 HTTP 请求
 * @param {string} method HTTP 方法
 * @param {string} url 请求 URL
 * @param {Object} data 请求数据
 * @param {string} token JWT token (可选)
 * @returns {Promise} 请求结果
 */
function makeRequest(method, url, data = null, token = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        // 添加认证头
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }

        if (data) {
            const postData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(postData);
        }

        const req = http.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsedData = JSON.parse(responseData);
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        resolve(parsedData);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${parsedData.error || responseData}`));
                    }
                } catch (error) {
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        resolve(responseData);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
                    }
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

/**
 * 生成设备数据
 * @param {number} deviceNum 设备编号
 * @returns {Object} 设备数据对象
 */
function generateDeviceData(deviceNum) {
    return {
        device_id: `test-${deviceNum}`,
        name: `test ${deviceNum}`,
        data_type: "fanuc_30i",
        location: `A${deviceNum}`,
        status: "active",
        brand: `HF${deviceNum}`,
        model: `M${deviceNum}`,
        ip: `192.168.1.${200 + deviceNum}`,
        port: 8193,
        enabled: true,
        auto_start: true,
        collect_interval: 3000,
        timeout: 10,
        retry_count: 3,
        retry_delay: 15,
        description: `测试设备 ${deviceNum} - 用于模拟生产数据`,
        sort_order: deviceNum
    };
}

/**
 * 用户登录获取 JWT token
 * @returns {Promise<string>} JWT token
 */
async function login() {
    try {
        const result = await makeRequest('POST', `${API_BASE_URL}${LOGIN_ENDPOINT}`, DEFAULT_ADMIN);
        if (result.data && result.data.token) {
            return result.data.token;
        } else {
            throw new Error('登录响应中未找到 token');
        }
    } catch (error) {
        throw new Error(`登录失败: ${error.message}`);
    }
}

/**
 * 添加单个设备
 * @param {Object} deviceData 设备数据
 * @param {string} token JWT token
 * @returns {Promise} 添加结果
 */
async function addDevice(deviceData, token) {
    try {
        const result = await makeRequest('POST', `${API_BASE_URL}${API_ENDPOINT}`, deviceData, token);
        return { success: true, data: result, device: deviceData };
    } catch (error) {
        return { success: false, error: error.message, device: deviceData };
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 开始批量添加设备记录...');
    console.log(`📡 API 地址: ${API_BASE_URL}${API_ENDPOINT}`);

    // 第一步：登录获取 JWT token
    console.log('🔐 正在登录获取认证令牌...');
    let token;
    try {
        token = await login();
        console.log('✅ 登录成功，获取到认证令牌');
    } catch (error) {
        console.error('❌ 登录失败:', error.message);
        process.exit(1);
    }

    // 第二步：生成设备数据 (test 18 到 test 47，共30个设备)
    const devices = [];
    for (let i = 18; i <= 47; i++) {
        devices.push(generateDeviceData(i));
    }

    console.log(`📝 准备添加 ${devices.length} 个设备记录...`);

    // 第三步：批量添加设备
    const results = [];
    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < devices.length; i++) {
        const device = devices[i];
        console.log(`📱 正在添加设备 ${i + 1}/${devices.length}: ${device.name}...`);

        const result = await addDevice(device, token);
        results.push(result);

        if (result.success) {
            successCount++;
            console.log(`   ✅ 成功添加: ${device.name} (位置: ${device.location}, IP: ${device.ip})`);
        } else {
            failCount++;
            console.log(`   ❌ 添加失败: ${device.name} - ${result.error}`);
        }

        // 添加小延迟，避免请求过快
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 输出结果统计
    console.log('\n📊 添加结果统计:');
    console.log(`✅ 成功: ${successCount} 个设备`);
    console.log(`❌ 失败: ${failCount} 个设备`);
    console.log(`📱 总计: ${devices.length} 个设备`);

    if (successCount > 0) {
        console.log('\n🎉 成功添加的设备列表:');
        results.filter(r => r.success).forEach((result, index) => {
            const device = result.device;
            console.log(`   ${index + 1}. ${device.name} - 位置: ${device.location} - IP: ${device.ip}`);
        });
    }

    if (failCount > 0) {
        console.log('\n⚠️ 添加失败的设备:');
        results.filter(r => !r.success).forEach((result, index) => {
            const device = result.device;
            console.log(`   ${index + 1}. ${device.name} - 错误: ${result.error}`);
        });
    }

    console.log('\n🏁 设备添加任务完成!');
}

// 运行主函数
main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
});
