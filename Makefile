.PHONY: help build up down logs clean test

# 检测Docker Compose命令
DOCKER_COMPOSE_CMD := $(shell if command -v docker-compose >/dev/null 2>&1; then echo "docker-compose"; elif docker compose version >/dev/null 2>&1; then echo "docker compose"; else echo ""; fi)

ifeq ($(DOCKER_COMPOSE_CMD),)
$(error Docker Compose is not available. Please install Docker Compose)
endif

# 默认目标
help:
	@echo "Available commands:"
	@echo "  build    - Build all Docker images"
	@echo "  up       - Start all services"
	@echo "  down     - Stop all services"
	@echo "  logs     - Show logs for all services"
	@echo "  clean    - Clean up containers and volumes"
	@echo "  test     - Run tests"
	@echo "  dev      - Start development environment"
	@echo ""
	@echo "Using Docker Compose command: $(DOCKER_COMPOSE_CMD)"

# 构建所有镜像
build:
	@echo "Building all Docker images..."
	$(DOCKER_COMPOSE_CMD) build

# 启动所有服务
up:
	@echo "Starting all services..."
	$(DOCKER_COMPOSE_CMD) up -d
	@echo "Services started. Check status with 'make logs'"

# 停止所有服务
down:
	@echo "Stopping all services..."
	$(DOCKER_COMPOSE_CMD) down

# 查看日志
logs:
	$(DOCKER_COMPOSE_CMD) logs -f

# 查看特定服务日志
logs-%:
	$(DOCKER_COMPOSE_CMD) logs -f $*

# 清理容器和卷
clean:
	@echo "Cleaning up containers and volumes..."
	$(DOCKER_COMPOSE_CMD) down -v
	docker system prune -f

# 开发环境启动
dev:
	@echo "Starting development environment..."
	$(DOCKER_COMPOSE_CMD) up --build

# 重启特定服务
restart-%:
	$(DOCKER_COMPOSE_CMD) restart $*

# 查看服务状态
status:
	$(DOCKER_COMPOSE_CMD) ps

# 进入服务容器
shell-%:
	$(DOCKER_COMPOSE_CMD) exec $* sh

# 测试API连接
test-api:
	@echo "Testing API endpoints..."
	curl -f http://localhost:9005/health || echo "API server not ready"
	curl -f http://localhost:8081/health || echo "Device collect service not ready"

# 初始化数据库
init-db:
	@echo "Initializing databases..."
	# 这里可以添加数据库初始化脚本

# 备份数据
backup:
	@echo "Creating backup..."
	mkdir -p backups
	$(DOCKER_COMPOSE_CMD) exec mongodb mongodump --out /tmp/backup
	docker cp mdc_mongodb:/tmp/backup ./backups/mongodb-$(shell date +%Y%m%d-%H%M%S)

# 恢复数据
restore:
	@echo "Restoring from backup..."
	@read -p "Enter backup directory name: " backup_dir; \
	docker cp ./backups/$$backup_dir mdc_mongodb:/tmp/restore; \
	$(DOCKER_COMPOSE_CMD) exec mongodb mongorestore /tmp/restore
